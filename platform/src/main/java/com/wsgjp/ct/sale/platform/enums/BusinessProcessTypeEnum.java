package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * @Author: wcy
 * @Date: 2022/11/22/17:19
 * @Description:
 */
public enum BusinessProcessTypeEnum implements CodeEnum {

//【业务处理方式】，pl_eshop_config 表增加字段：business_process_type
    OFFLINE(0, "线下处理"),
    ONLINE(1, "网店处理");

    private final int code;
    private final String name;

    BusinessProcessTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
