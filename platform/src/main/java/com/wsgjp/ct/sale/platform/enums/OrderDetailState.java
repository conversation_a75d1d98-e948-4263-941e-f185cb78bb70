package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 11/6/2020 下午 3:55
 */
public enum  OrderDetailState implements CodeEnum {

	NORMAL(0,"正常"),
	MODIFIED(1,"编辑"),
	CLOSED(2,"取消"),
	/*DELETED(3,"删除"),*/
	SUCCESS(4,"交易成功");

	private final int flag;
	private final String name;

	OrderDetailState(int flag, String name) {
		this.flag = flag;
		this.name = name;
	}

	@Override
	public int getCode() {
		return flag;
	}

	@Override
	public String getName() {
		return name;
	}
}
