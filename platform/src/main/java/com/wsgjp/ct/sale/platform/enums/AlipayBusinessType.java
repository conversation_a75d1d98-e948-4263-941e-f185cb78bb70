package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum AlipayBusinessType implements CodeEnum {
    OutAmount(0x1000 | 0, "其他费用", "至尊宝余额宝过桥余额增"),
    WelfareContribute(0x1000 | 1, "公益宝贝捐赠", "公益宝贝捐赠=壹乐园计划=************* "),
    TaobaoBuyerWithholding(0x1000 | 2, "淘宝客佣金代扣款", "淘宝客佣金代扣款"),
    InsuranceFee(0x1000 | 3, "保险承保", "保险承保-无忧退货-保费收取(******************)"),
    AntCheckLaterFee(0x1000 | 4, "花呗支付服务费", "花呗支付服务费[****************************];淘宝交易号[T200P******************]"),
    CreditCardFee(0x1000 | 5, "信用卡支付服务费", "信用卡支付服务费[****************************];淘宝交易号[T200P******************]"),
    VillageTaoBaoFee(0x1000 | 6, "村淘平台服务费扣款", "村淘平台服务费扣款{******************}"),
    PointsDeducted(0x1000 | 7, "代扣返点积分", "代扣返点积分******************"),
    MallFee(0x1000 | 8, "天猫佣金", "天猫佣金（类目）{******************}扣款"),

    InAmount(0x2000 | 0, "其他收入", "其他收入"),
    TradePayment(0x2000 | 1, "交易付款", "交易付款"),
    AntCheckLaterPeriodic(0x2000 | 2, "花呗分期交易号", "花呗分期交易号[****************************]"),
    AntCheckLater(0x2000 | 3, "花呗交易号", "花呗交易号[****************************]"),
    InsuranceClaims(0x2000 | 4, "保险理赔", "保险理赔-坏单包赔－理赔（******************）"),

    Untreated(0x4000 | 0, "售后退款", "售后退款-****************************-T200P******************");

    private int index;
    private String name;
    private String memo;

    AlipayBusinessType(int index, String name, String memo) {
        this.index = index;
        this.name = name;
        this.memo = memo;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }

    public String getMemo() {
        return memo;
    }
}
