package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2020-01-02 10:29
 */
public enum InvoiceState implements CodeEnum {
    /**
     * 无需开票
     */
    NONE(2,""),
    /**
     * 未开票
     */
    NO_INVOICE(0,"未开票"),
    /**
     * 已开票
     */
    INVOICED(1,"已开票");

    private int code;
    private String name;

    InvoiceState(int code, String name){
        this.code=code;
        this.name=name;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName(){
        return name;
    }

    public static InvoiceState nameValueOf(String name) {
        for (InvoiceState invoiceState : values()) {
            if (name.equals(invoiceState.getName())) {
                return invoiceState;
            }
        }
        return null;
    }
}
