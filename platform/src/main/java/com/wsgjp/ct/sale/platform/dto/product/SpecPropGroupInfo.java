package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SpecPropGroupInfo {
    /**
     * 组id
     */
    private String id;
    /**
     * 排序
     */
    private Integer orderSort;
    /**
     * 组名称
     */
    private String name;
    /**
     * 规格参数列表
     */
    private List<SpecPropEntity> specProps;

    public SpecPropGroupInfo() {
    }

    public SpecPropGroupInfo(String id, Integer orderSort, String name) {
        this.id = id;
        this.orderSort = orderSort;
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<SpecPropEntity> getSpecProps() {
        return specProps;
    }

    public void setSpecProps(List<SpecPropEntity> specProps) {
        this.specProps = specProps;
    }
}
