package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.common.enums.publish.InputTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SaleAttrValueTemplate {
    /**
     * 是否锁死
     */
    private String isLock;
    /**
     * 是否必填
     */
    private String isRequired;
    /**
     * 属性ID
     */
    private String propId;
    /**
     * 标信息，比如 4级类目 映射的 3级类目销售属性模版id 4-3级映射关系 取 mapping 字段
     */
    private List<FeatureInfo> features;
    /**
     * 排序
     */
    private Integer orderSort;
    /**
     * 模板值
     */
    private List<SaleAttrValueTemplateValue> saleAttrValueTemplateValues;
    private List<String> valueUnits;
    /**
     * 子值模板
     * 一个销售属性值模板由多个值模板组成
     */

    private List<SaleAttrValueTemplate> childValueTemplates;
    /**
     * 是否支持跨级选择
     */
    private String exclusive;
    /**
     * 属性名称
     */
    private String propName;
    /**
     * 京东自营专用(后续可能废弃,建议用inputType字段)
     * 模板类型（0：选项，1：数值单位，2：类目属性，3：自定义，4：标准模版）
     *
     */
    private String type;
    /**
     * 输入类型
     */
    private InputTypeEnum inputType;
    /**
     * 暗纹
     */
    private String prompts;
    /**
     * 模板id
     */
    private String valueTemplateId;
    /**
     * url或rgb
     */
    private String picType;

    private String picValue;

    /**
     * 选了品牌或者产品规格后会进行替换
     */
    private Long sellPropertyId;

    public Long getSellPropertyId() {
        return sellPropertyId;
    }

    public void setSellPropertyId(Long sellPropertyId) {
        this.sellPropertyId = sellPropertyId;
    }

    public String getPicType() {
        return picType;
    }

    public void setPicType(String picType) {
        this.picType = picType;
    }

    public String getPicValue() {
        return picValue;
    }

    public void setPicValue(String picValue) {
        this.picValue = picValue;
    }

    public String getIsLock() {
        return isLock;
    }

    public void setIsLock(String isLock) {
        this.isLock = isLock;
    }

    public String getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(String isRequired) {
        this.isRequired = isRequired;
    }

    public String getPropId() {
        return propId;
    }

    public void setPropId(String propId) {
        this.propId = propId;
    }

    public List<FeatureInfo> getFeatures() {
        return features;
    }

    public void setFeatures(List<FeatureInfo> features) {
        this.features = features;
    }

    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }

    public List<SaleAttrValueTemplateValue> getSaleAttrValueTemplateValues() {
        return saleAttrValueTemplateValues;
    }

    public void setSaleAttrValueTemplateValues(List<SaleAttrValueTemplateValue> saleAttrValueTemplateValues) {
        this.saleAttrValueTemplateValues = saleAttrValueTemplateValues;
    }

    public String getExclusive() {
        return exclusive;
    }

    public void setExclusive(String exclusive) {
        this.exclusive = exclusive;
    }

    public String getPropName() {
        return propName;
    }

    public void setPropName(String propName) {
        this.propName = propName;
    }

    public InputTypeEnum getInputType() {
        return inputType;
    }

    public void setInputType(InputTypeEnum inputType) {
        this.inputType = inputType;
    }

    public String getPrompts() {
        return prompts;
    }

    public void setPrompts(String prompts) {
        this.prompts = prompts;
    }

    public String getValueTemplateId() {
        return valueTemplateId;
    }

    public void setValueTemplateId(String valueTemplateId) {
        this.valueTemplateId = valueTemplateId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<SaleAttrValueTemplate> getChildValueTemplates() {
        return childValueTemplates;
    }

    public void setChildValueTemplates(List<SaleAttrValueTemplate> childValueTemplates) {
        this.childValueTemplates = childValueTemplates;
    }

    public List<String> getValueUnits() {
        return valueUnits;
    }

    public void setValueUnits(List<String> valueUnits) {
        this.valueUnits = valueUnits;
    }
}
