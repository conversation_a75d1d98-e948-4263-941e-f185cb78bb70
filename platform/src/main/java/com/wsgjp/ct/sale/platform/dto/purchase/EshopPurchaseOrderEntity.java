package com.wsgjp.ct.sale.platform.dto.purchase;


import com.wsgjp.ct.sale.platform.dto.order.EshopOrderBaseEntity;
import com.wsgjp.ct.sale.platform.enums.CurrencyType;

import java.util.Date;
import java.util.List;

/**
 * 采购订单实体
 */
public class EshopPurchaseOrderEntity extends EshopOrderBaseEntity {

    /**
     * 往来单位名称
     */
    private String btypeName;
    /**
     * 往来单位编码
     */
    private String btypeCode;

    /**
     * 要求到货时间
     */
    private Date arrivalDate;
    /**
     * 关闭日期
     */
    private Date closeTime;
    /**
     * 币种类型
     */
    private CurrencyType currencyType;

    /**
     * 订单卖家备注
     */
    private List<EshopPurchaseOrderDetail> orderDetails;


    public String getBtypeName() {
        return btypeName;
    }

    public void setBtypeName(String btypeName) {
        this.btypeName = btypeName;
    }

    public String getBtypeCode() {
        return btypeCode;
    }

    public void setBtypeCode(String btypeCode) {
        this.btypeCode = btypeCode;
    }

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public Date getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    public CurrencyType getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(CurrencyType currencyType) {
        this.currencyType = currencyType;
    }

    public List<EshopPurchaseOrderDetail> getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(List<EshopPurchaseOrderDetail> orderDetails) {
        this.orderDetails = orderDetails;
    }
}
