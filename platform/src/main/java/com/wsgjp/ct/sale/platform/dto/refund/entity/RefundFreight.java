package com.wsgjp.ct.sale.platform.dto.refund.entity;

import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 * 售后物流信息
 */
public class RefundFreight {
    /**
     * 物流公司
     */
    private String freightName;
    /**
     * 物流公司编码
     */
    private String freightCode;
    /**
     * 物流单号
     */
    private String freightNo;

    public String getFreightName() {
        if (StringUtils.isEmpty(freightName)) {
            return "";
        }
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getFreightCode() {
        if (StringUtils.isEmpty(freightCode)) {
            return "";
        }
        return freightCode;
    }

    public void setFreightCode(String freightCode) {
        this.freightCode = freightCode;
    }

    public String getFreightNo() {
        if (StringUtils.isEmpty(freightNo)) {
            return "";
        }
        return freightNo;
    }

    public void setFreightNo(String freightNo) {
        this.freightNo = freightNo;
    }
}
