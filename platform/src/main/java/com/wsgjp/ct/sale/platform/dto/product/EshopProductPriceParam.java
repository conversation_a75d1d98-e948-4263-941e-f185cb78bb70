package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 商品价格上传请求参数
 * <AUTHOR>
 */
@ApiModel("商品价格上传请求参数")
public class EshopProductPriceParam extends ErpBaseInfo {

    @ApiModelProperty("商品全名")
    private String productName;
    @ApiModelProperty("商品id")
    private String productId;
    @ApiModelProperty("商品编码")
    private String productCode;
     @ApiModelProperty("sku和单位信息 中台/药师帮")
    private List<SkuUnitInfo> skuUnitInfoList;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public List<SkuUnitInfo> getSkuUnitInfoList() {
        return skuUnitInfoList;
    }

    public void setSkuUnitInfoList(List<SkuUnitInfo> skuUnitInfoList) {
        this.skuUnitInfoList = skuUnitInfoList;
    }
}
