package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum ProductDownloadType implements CodeEnum {
    /**
     * 创建时间
     */
    CREATE_TIME(0, "创建时间"),
    /**
     * 修改时间
     */
    MODIFY_TIME(1, "修改时间"),
    /**
     * 订单ID
     */
    ID(2, "ID");


    ProductDownloadType(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    private final int flag;
    private final String name;

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

}

