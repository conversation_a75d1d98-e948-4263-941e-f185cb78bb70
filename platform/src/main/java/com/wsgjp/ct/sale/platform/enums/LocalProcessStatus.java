package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021-03-13 15:18
 */
public enum LocalProcessStatus implements CodeEnum {
    WAIT_AUDIT(1,"未审核"),
    AUDITED(2,"已审核"),
    WAIT_PICK(3,"待拣货"),
    WAIT_CHECK(4,"待验货"),
    WAIT_SEND(5,"待发货"),
    SENDED(6,"已发货")
    ;
    LocalProcessStatus(int code,String name){
        this.code=code;
        this.name=name;
    }
    private int code;
    private String name;
    @Override
    public int getCode() {
        return 0;
    }

    @Override
    public String getName() {
        return null;
    }
}
