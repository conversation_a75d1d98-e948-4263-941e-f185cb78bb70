package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum OperationType implements CodeEnum {
    CANCEL(0, "取消操作"),
    FINISH(2, "完成操作"),
    SEND_GOODS(1, "发货操作");

    private int flag;
    private String name;

    OperationType(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }


    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
