package com.wsgjp.ct.sale.platform.dto.tmc;

import com.wsgjp.ct.sale.platform.enums.MsgDataTypeEnum;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class DataPushDataEntity {
    private String id;
    private String onlineShopId;
    private Integer shopType;
    private String tradeId;
    private String refundId;
    private String productId;
    private String uniqueMark;
    private String data;
    private MsgDataTypeEnum dataType;
    private Date pushTime;
    private Date createTime;
    private Date updateTime;
    private String topic;


    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public String getUniqueMark() {
        return uniqueMark;
    }

    public void setUniqueMark(String uniqueMark) {
        this.uniqueMark = uniqueMark;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public MsgDataTypeEnum getDataType() {
        return dataType;
    }

    public void setDataType(MsgDataTypeEnum dataType) {
        this.dataType = dataType;
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOnlineShopId() {
        return onlineShopId;
    }

    public void setOnlineShopId(String onlineShopId) {
        this.onlineShopId = onlineShopId;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }
}
