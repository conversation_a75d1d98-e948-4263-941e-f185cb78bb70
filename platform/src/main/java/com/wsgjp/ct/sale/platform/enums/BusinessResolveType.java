package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @Date 2022-07-21
 * @Description
 */
public enum BusinessResolveType implements CodeEnum {
    /**
     * 分销商操作类型
     */
    INS(1, "新增"),
    UPD(2, "更新"),
    DEL(3, "删除"),
    ;

    private final int type;
    private final String name;

    BusinessResolveType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    @Override
    public int getCode() {
        return type;
    }

    @Override
    public String getName() {
        return name;
    }
}
