package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 12/28/2020 上午 09:47
 */
public enum ServiceModuleEnum implements CodeEnum {

    System(0,"系统","00"),
    Auth (1,"授权","01"),
    Subscription (2,"订阅","02"),
    Product (3,"商品","03"),
    Warehouse (4,"仓库","04"),
    Order (5,"订单","05"),
    Refund (6,"售后","06"),
    CommonExecutor(99,"公共执行方法","99");

    private int index;
    private String code;

    private String name;

    ServiceModuleEnum(int index, String name, String code){
        this.index=index;
        this.name=name;
        this.code=code;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }

    public String getUserCode(){return code;}
}
