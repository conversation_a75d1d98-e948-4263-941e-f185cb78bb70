package com.wsgjp.ct.sale.platform.dto.order;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 送检配置模板信息
 * <AUTHOR>
 */
public class SubmitTemplate {

    /**
     * 快递公司列表
     */
    private List<DeliveryList> deliveryList;

    /**
     * 质检机构列表
     */
    private List<InspectOrgList> inspectOrgList;
    private String chargeURL;


    public List<DeliveryList> getDeliveryList() {
        return deliveryList;
    }

    public void setDeliveryList(List<DeliveryList> value) {
        this.deliveryList = value;
    }


    public List<InspectOrgList> getInspectOrgList() {
        return inspectOrgList;
    }

    public void setInspectOrgList(List<InspectOrgList> value) {
        this.inspectOrgList = value;
    }


    public String getChargeURL() {
        return chargeURL;
    }

    public void setChargeURL(String value) {
        this.chargeURL = value;
    }

    public static class DeliveryList {
        private String id;
        private String name;

        /**
         * 快递产品列表
         */
        private List<DeliveryProduct> deliveryProducts;

        public String getID() {
            return id;
        }

        public void setID(String value) {
            this.id = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String value) {
            this.name = value;
        }


        public List<DeliveryProduct> getDeliveryProducts() {
            return deliveryProducts;
        }

        public void setDeliveryProducts(List<DeliveryProduct> value) {
            this.deliveryProducts = value;
        }
    }

    public static class DeliveryProduct {
        private String id;
        private String name;
        private Long enableInsure;

        /**
         * 保价类型列表
         */
        private List<InsureTypeList> insureTypeList;

        public String getID() {
            return id;
        }

        public void setID(String value) {
            this.id = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String value) {
            this.name = value;
        }


        public Long getEnableInsure() {
            return enableInsure;
        }

        public void setEnableInsure(Long value) {
            this.enableInsure = value;
        }


        public List<InsureTypeList> getInsureTypeList() {
            return insureTypeList;
        }

        public void setInsureTypeList(List<InsureTypeList> value) {
            this.insureTypeList = value;
        }
    }

    public static class InsureTypeList {
        private String id;
        private String name;
        /**
         * 有无上限
         */
        private Long upperLimitType;
        /**
         * 上限金额(单位元)
         */
        private BigDecimal upperLimitAmount;

        public String getID() {
            return id;
        }

        public void setID(String value) {
            this.id = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String value) {
            this.name = value;
        }


        public Long getUpperLimitType() {
            return upperLimitType;
        }

        public void setUpperLimitType(Long value) {
            this.upperLimitType = value;
        }


        public BigDecimal getUpperLimitAmount() {
            return upperLimitAmount;
        }

        public void setUpperLimitAmount(BigDecimal value) {
            this.upperLimitAmount = value;
        }
    }

    public static class InspectOrgList {
        private String id;
        private String name;

        @JsonProperty("id")
        public String getID() {
            return id;
        }

        @JsonProperty("id")
        public void setID(String value) {
            this.id = value;
        }

        @JsonProperty("name")
        public String getName() {
            return name;
        }

        @JsonProperty("name")
        public void setName(String value) {
            this.name = value;
        }
    }

}
