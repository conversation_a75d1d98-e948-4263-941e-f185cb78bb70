package com.wsgjp.ct.sale.platform.dto.order.entity;


import com.wsgjp.ct.sale.platform.enums.OrderTimingType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 23/7/2020 下午 2:14
 */
public class OrderTiming {
    private int cnService;
    /**
     * 预约配送时间(预计发货时间)
     */
    private Date planSendTime;
    /**
     * 最晚发货时间
     */
    private Date sendTime;
    /**
     *  最晚揽收时间
     */
    private Date promisedCollectTime;

    /**
     * 最晚签收时间
     */
    private Date promisedSignTime;

    /**
     * 承诺最早送达时间:最早送达时间
     */
    private Date promisedSignStartTime;

    private BigDecimal systemTiming = BigDecimal.ZERO;
    private OrderTimingType timingType;

    /**
     *平台发货时间
     */
    private Date platformSendTime;
    /**
     * 预计签收时间(预计送达时间)
     */
    private Date planSignTime;

    public Date getPlatformSendTime() {
        return platformSendTime;
    }

    public void setPlatformSendTime(Date platformSendTime) {
        this.platformSendTime = platformSendTime;
    }

    public Date getPromisedCollectTime() {
        return promisedCollectTime;
    }

    public void setPromisedCollectTime(Date promisedCollectTime) {
        this.promisedCollectTime = promisedCollectTime;
    }

    public Date getPromisedSignTime() {
        return promisedSignTime;
    }

    public void setPromisedSignTime(Date promisedSignTime) {
        this.promisedSignTime = promisedSignTime;
    }

    public int getCnService() {
        return cnService;
    }

    public void setCnService(int cnService) {
        this.cnService = cnService;
    }

    public Date getPlanSendTime() {
        return planSendTime;
    }

    public void setPlanSendTime(Date planSendTime) {
        this.planSendTime = planSendTime;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public BigDecimal getSystemTiming() {
        return systemTiming;
    }

    public void setSystemTiming(BigDecimal systemTiming) {
        this.systemTiming = systemTiming;
    }

    public OrderTimingType getTimingType() {
        if (timingType == null) {
            return OrderTimingType.NORMAL;
        }
        return timingType;
    }

    public void setTimingType(OrderTimingType timingType) {
        this.timingType = timingType;
    }

    public Date getPlanSignTime() {
        return planSignTime;
    }

    public void setPlanSignTime(Date planSignTime) {
        this.planSignTime = planSignTime;
    }

    public Date getPromisedSignStartTime() {
        return promisedSignStartTime;
    }

    public void setPromisedSignStartTime(Date promisedSignStartTime) {
        this.promisedSignStartTime = promisedSignStartTime;
    }
}
