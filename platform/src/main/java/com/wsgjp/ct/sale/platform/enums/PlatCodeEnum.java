package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */
public enum PlatCodeEnum implements CodeEnum {
	/**
	 * 说明
	 */
	SUCCESS("成功",0),

	API_ERR("三方接口异常",1000400),
	API_TOKEN_EXPIRE("三方接口token过期",1000401),
	API_NOT_PERMISSION("三方接口无调用权限",1000402),
	API_PARAM_ERR("三方接口请求必要参数缺失",1000403),
	API_CONN_ERR("三方接口无法连接",1000405),
	API_TIMEOUT("三方接口返回超时",1000406),
	API_DATA_ERR("三方接口返回数据异常",1000407),
	API_CALL_LIMIT("三方接口调用超限",1000408),
	API_NOT_IN_WHITE_LIST("不在平台白名单中",1000409),

	PLAT_BUSINESS_ERR("平台业务错误",2000500),
	PLAT_API_EXECUTE_ERR("执行调用平台接口方法报错",2000501);

	private final String name;
	private final int code;

	PlatCodeEnum(String name, int code) {
		this.name = name;
		this.code = code;
	}

	@Override
	public String getName() {
		return name;
	}


	@Override
	public int getCode() {
		return code;
	}


}
