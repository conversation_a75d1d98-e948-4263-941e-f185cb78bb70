package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2020-03-16 17:55
 */
public enum InvoiceCategory implements CodeEnum {
  /** 发票种类 */
  ELECTRON_NORMAL(0, "电子普票"),
  ELECTRON_ADDED_TAX(1, "电子专票"),
  PAPER_NORMAL(2, "纸质普票"),
  PAPER_ADDED_TAX(3, "纸质专票");

  private int code;
  private String name;

  InvoiceCategory(int code, String name) {
    this.code = code;
    this.name = name;
  }

  @Override
  public int getCode() {
    return code;
  }

  @Override
  public String getName() {
    return name;
  }

  public static InvoiceCategory nameValueOf(String name) {
    for (InvoiceCategory invoiceType : values()) {
      if (name.equals(invoiceType.getName())) {
        return invoiceType;
      }
    }
    return null;
  }
}
