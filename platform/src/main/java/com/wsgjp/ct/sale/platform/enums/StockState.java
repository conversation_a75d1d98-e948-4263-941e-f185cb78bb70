package com.wsgjp.ct.sale.platform.enums;


import bf.datasource.typehandler.CodeEnum;

/**
 *
 */
public enum StockState implements CodeEnum {
    /**
     * 全部
     */
    All(0, "全部"),
    /**
     * 出售中
     */
    OnSale(1, "出售中"),

    /**
     * 仓库中
     */
    InStock(2, "仓库中");

    StockState() {

    }

    public static StockState valueOf(int code) {
        for (StockState tradeStatus : values()) {
            if (tradeStatus.getCode() == code) {
                return tradeStatus;
            }
        }
        return StockState.All;
    }

    StockState(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    private int flag;
    private String name;

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
