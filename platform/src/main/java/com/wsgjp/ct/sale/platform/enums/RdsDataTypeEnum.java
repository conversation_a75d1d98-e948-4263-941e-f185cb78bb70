package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2023/12/26 18:01
 */
public enum RdsDataTypeEnum implements CodeEnum {
    /**
     * rds数据类型
     */
    ORDER(0,"订单增量接口"),
    REFUND(1,"售后增量接口"),
    PRODUCT(2,"商品增量接口"),
    ORDER_DETAIL(0,"订单详情"),
    REFUND_DETAIL(0,"售后详情"),
    PRODUCT_DETAIL(0,"商品详情"),
    ORDER_FULL_LIST(0,"订单全量接口"),
    REFUND_FULL_LIST(0,"售后全量接口"),
    PRODUCT_FULL_LIST(0,"商品全量接口"),;

    private final int index;
    private final String name;

    RdsDataTypeEnum(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }
}
