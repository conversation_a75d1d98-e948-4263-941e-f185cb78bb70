package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum FreightDeliveryMethod implements CodeEnum {
    /**
     * 汽运
     */
    DELIVERY_BY_CAR(1, "汽运"),
    /**
     * 空运
     */
    DELIVERY_BY_AIR(2, "空运");

    private int code;
    private String name;


    FreightDeliveryMethod(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
