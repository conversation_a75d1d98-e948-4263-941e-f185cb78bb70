package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum RefundSpeedType implements CodeEnum {
    /**
     * 普通售后单类型
     */
    NORMAL(0, "普通售后单类型"),
    /**
     * 急速退款类型
     */
    QUICK_REFUND(1, "急速退款类型");

    private int flag;

    private String name;


    RefundSpeedType(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }


    public static RefundSpeedType findByFlag(int flag) {
        for (RefundSpeedType productMarkType : RefundSpeedType.values()) {
            if (flag == productMarkType.getCode()) {
                return productMarkType;
            }
        }
        return null;
    }


    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return name;
    }
}
