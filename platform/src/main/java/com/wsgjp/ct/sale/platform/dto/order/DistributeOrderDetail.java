package com.wsgjp.ct.sale.platform.dto.order;

/**
 * <AUTHOR>
 */
public class DistributeOrderDetail {

    /**
     * 子订单id
     */
    private String oid;
    /**
     * 供应商id
     */
    private String supplierId;
    /**
     * 分销商id
     */
    private String distributeId;
    /**
     * 货品id
     */
    private String productId;
    /**
     * 货品skuId
     */
    private String productSkuId;
    /**
     * 代发数量
     */
    private int productCount;
    /**
     * 货品类型，用于校验防止重发之类；当传值为0时会校验所有代发订单产品数量和不超过订单的 buyAmount 0 - 主货品 1 - 赠品
     */
    private int productType;

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getDistributeId() {
        return distributeId;
    }

    public void setDistributeId(String distributeId) {
        this.distributeId = distributeId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductSkuId() {
        return productSkuId;
    }

    public void setProductSkuId(String productSkuId) {
        this.productSkuId = productSkuId;
    }

    public int getProductCount() {
        return productCount;
    }

    public void setProductCount(int productCount) {
        this.productCount = productCount;
    }

    public int getProductType() {
        return productType;
    }

    public void setProductType(int productType) {
        this.productType = productType;
    }
}
