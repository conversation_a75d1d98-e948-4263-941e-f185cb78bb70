package com.wsgjp.ct.sale.platform.dto.purchase;

import org.apache.commons.lang3.StringUtils;

/**
 * 采购订单详情错误实体
 */
public class PurchaseOrderDetailResult {

    public PurchaseOrderDetailResult(PurchaseOrderRequestParam orderParam, String errorMsg) {
        this.orderParam = orderParam;
        this.errorMsg = errorMsg;
    }
    /**
     * 订单详情入参
     */
    private PurchaseOrderRequestParam orderParam;

    private boolean success;


    private String errorMsg;

    public PurchaseOrderRequestParam getOrderParam() {
        return orderParam;
    }

    public void setOrderParam(PurchaseOrderRequestParam orderParam) {
        this.orderParam = orderParam;
    }

    public boolean isSuccess() {
        if(StringUtils.isEmpty(errorMsg)){
            success=true;
        }
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
