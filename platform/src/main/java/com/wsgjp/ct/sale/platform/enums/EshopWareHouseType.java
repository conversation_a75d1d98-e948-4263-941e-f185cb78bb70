package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum EshopWareHouseType implements CodeEnum {
    E_COMMERCE_WAREHOUSE(1, "电商仓"),
    E_COMMERCE_STORE(0, "电商门店");

    private  int index;
    private  String name;

    EshopWareHouseType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }

    public static EshopWareHouseType findByIndex(int index){
        for (EshopWareHouseType wareHouseType : EshopWareHouseType.values()) {
            if(index == wareHouseType.getCode()){
                return wareHouseType;
            }
        }
        return null;
    }
}

