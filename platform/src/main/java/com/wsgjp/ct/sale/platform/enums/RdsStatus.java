package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum RdsStatus implements CodeEnum {
    NONE(0, "未订阅"),
    NORMAL(1, "已订阅");

    private  int index;
    private  String name;

    RdsStatus(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }
}
