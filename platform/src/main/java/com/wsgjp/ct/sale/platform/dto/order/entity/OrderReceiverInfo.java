package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.platform.enums.Sex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 */
@ApiModel("订单收货人信息")
public class OrderReceiverInfo {
    public OrderReceiverInfo() {
        customerAddress = "";
        customerShopAccount = "";
        customerAddress = "";
        receiver = "";
    }

    @ApiModelProperty(value = "收货人", notes = "姓名或者账号昵称等")
    private String receiver;
    @ApiModelProperty("客户身份证姓名")
    private String customerIdCardName;
    @ApiModelProperty("客户身份证号码")
    private String customerIdCardNumber;
    @ApiModelProperty("客户店铺账户")
    private String customerShopAccount;
    @ApiModelProperty("客户支付账户")
    private String customerPayAccount;
    @ApiModelProperty("客户手机号")
    private String customerMobile;
    @ApiModelProperty("客户固定电话号")
    private String customerTel;
    @ApiModelProperty("客户所属国家")
    private String customerCountry;
    @ApiModelProperty("客户所属省份")
    private String customerProvince;
    @ApiModelProperty("客户所属城市")
    private String customerCity;
    @ApiModelProperty("客户所属区县")
    private String customerDistrict;
    @ApiModelProperty("客户所属场镇")
    private String customerTown;
    @ApiModelProperty("客户详细地址")
    private String customerAddress;
    @ApiModelProperty("客户邮政编码")
    private String customerZipCode;
    @ApiModelProperty("客户邮箱")
    private String customerEmail;
    @ApiModelProperty("性别")
    private Sex sex;

    /**
     * 是否是平台加密的订单。传这个值时 订单加密时只会保存生成的Di ，不会进行数据模糊化，
     * 加密生成的mi pi ai 等数据也不进行覆盖
     **/
    @ApiModelProperty("是否需要平台加密")
    private boolean needPlatformDecrypt;

    /**
     * 使用订单号对订单进行加密，订单敏感数据不会再次进行模糊化。
     **/
    @ApiModelProperty(value = "订单号", notes = "使用订单号对订单进行加密，订单敏感数据不会再次进行模糊化")
    private String tradeId;


    public String getReceiver() {
        if (StringUtils.isEmpty(receiver)) {
            return "";
        }
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public Sex getSex() {
        return sex;
    }

    public void setSex(Sex sex) {
        this.sex = sex;
    }

    public String getCustomerPayAccount() {
        if (StringUtils.isEmpty(customerPayAccount)) {
            return "";
        }
        return customerPayAccount;
    }

    public void setCustomerPayAccount(String customerPayAccount) {
        this.customerPayAccount = customerPayAccount;
    }

    public String getCustomerShopAccount() {
        if (StringUtils.isEmpty(customerShopAccount)) {
            return "";
        }
        return customerShopAccount;
    }

    public void setCustomerShopAccount(String customerShopAccount) {
        this.customerShopAccount = customerShopAccount;
    }

    public String getCustomerMobile() {
        if (StringUtils.isEmpty(customerMobile)) {
            return "";
        }
        return customerMobile;
    }

    public void setCustomerMobile(String customerMobile) {
        this.customerMobile = customerMobile;
    }

    public String getCustomerProvince() {
        if (StringUtils.isEmpty(customerProvince)) {
            return "";
        }
        return customerProvince;
    }

    public void setCustomerProvince(String customerProvince) {
        this.customerProvince = customerProvince;
    }

    public String getCustomerCity() {
        if (StringUtils.isEmpty(customerCity)) {
            return "";
        }
        return customerCity;
    }

    public void setCustomerCity(String customerCity) {
        this.customerCity = customerCity;
    }

    public String getCustomerDistrict() {
        if (StringUtils.isEmpty(customerDistrict)) {
            return "";
        }
        return customerDistrict;
    }

    public void setCustomerDistrict(String customerDistrict) {
        this.customerDistrict = customerDistrict;
    }

    public String getCustomerAddress() {
        if (StringUtils.isEmpty(customerAddress)) {
            return "";
        }
        return customerAddress;
    }

    public void setCustomerAddress(String customerAddress) {
        this.customerAddress = customerAddress;
    }

    public String getCustomerZipCode() {
        if (StringUtils.isEmpty(customerZipCode)) {
            return "";
        }
        return customerZipCode;
    }

    public void setCustomerZipCode(String customerZipCode) {
        this.customerZipCode = customerZipCode;
    }

    public String getCustomerEmail() {
        if (StringUtils.isEmpty(customerEmail)) {
            return "";
        }
        return customerEmail;
    }

    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }

    public String getCustomerIdCardName() {
        if (StringUtils.isEmpty(customerIdCardName)) {
            return "";
        }
        return customerIdCardName;
    }

    public void setCustomerIdCardName(String customerIdCardName) {
        this.customerIdCardName = customerIdCardName;
    }

    public String getCustomerIdCardNumber() {
        if (StringUtils.isEmpty(customerIdCardNumber)) {
            return "";
        }
        return customerIdCardNumber;
    }

    public void setCustomerIdCardNumber(String customerIdCardNumber) {
        this.customerIdCardNumber = customerIdCardNumber;
    }

    public String getCustomerTown() {
        if (StringUtils.isEmpty(customerTown)) {
            return "";
        }
        return customerTown;
    }

    public void setCustomerTown(String customerTown) {
        this.customerTown = customerTown;
    }

    public String getCustomerTel() {
        if (StringUtils.isEmpty(customerTel)) {
            return "";
        }
        return customerTel;
    }

    public void setCustomerTel(String customerTel) {
        this.customerTel = customerTel;
    }

    public String getCustomerCountry() {
        if (customerCountry == null || "".equals(customerCountry)) {
            return "中国";
        }
        return customerCountry;
    }

    public void setCustomerCountry(String customerCountry) {
        this.customerCountry = customerCountry;
    }

    public boolean isNeedPlatformDecrypt() {
        return needPlatformDecrypt;
    }

    public void setNeedPlatformDecrypt(boolean needPlatformDecrypt) {
        this.needPlatformDecrypt = needPlatformDecrypt;
    }

    public String getTradeId() {
        if (StringUtils.isEmpty(tradeId)) {
            return "";
        }
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }
}
