package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum DecryptMiddleGroundTypeEnum implements CodeEnum {

    DIRECT(0,"直接解密"),
    URL(1,"url"),
    PAGE(2,"跳转页面");


    private int index;
    private String name;

    DecryptMiddleGroundTypeEnum(int index, String name){
        this.index=index;
        this.name=name;
    }

    @Override
    public String toString(){
        return name;
    }
    @Override
    public int getCode() {
        return index;
    }
}
