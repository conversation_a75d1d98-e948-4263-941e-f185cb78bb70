package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2023/10/24 13:36
 */
public enum StockSyncErrorType  implements CodeEnum {

    /**
     * 库存同步错误类型
     */
    NONE(0,"没有报错"),
    NORMAL(1,"普通业务报错"),
    NOT_EXIST(2,"商品不存在"),
    IN_ACTIVITY(3,"活动中的商品"),
    SYSTEM_RE_TRY(4,"平台提醒重试"),
    CONCURRENT_ERROR(5,"并发错误导致失败"),
    IGNORE_ERROR(6,"可以忽略的过滤性质报错")
    ;
    private final int code;
    private final String desc;
    StockSyncErrorType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
