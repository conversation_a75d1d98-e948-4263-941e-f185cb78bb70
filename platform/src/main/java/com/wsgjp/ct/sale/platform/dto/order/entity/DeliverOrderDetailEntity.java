package com.wsgjp.ct.sale.platform.dto.order.entity;

/**
 * <AUTHOR>
 * @date 2021/3/9 16:10
 */
public class DeliverOrderDetailEntity {
    private String productName;
    private String cooperationNo;
    private String numId;
    private String barcode;
    private int quantity;
    private String poNo;
    private String price;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCooperationNo() {
        return cooperationNo;
    }

    public void setCooperationNo(String cooperationNo) {
        this.cooperationNo = cooperationNo;
    }

    public String getNumId() {
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getPoNo() {
        return poNo;
    }

    public void setPoNo(String poNo) {
        this.poNo = poNo;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }
}