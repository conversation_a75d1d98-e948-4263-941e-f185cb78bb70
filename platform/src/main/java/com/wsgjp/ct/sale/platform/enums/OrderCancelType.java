package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * TMC中订单取消类型
 * <AUTHOR>
 */

public enum OrderCancelType implements CodeEnum {

    None(0,"无需取消"),
    Normal_Cancel(1,"普通取消"),
    Force_Cancel(2,"强制取消");

    private int code;
    private String name;

    OrderCancelType(int code, String name) {
        this.code = code;
        this.name = name;
    }
    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
