package com.wsgjp.ct.sale.platform.enums;

/**
 * <AUTHOR>
 * @date 2021-03-13 16:32
 */
public enum PlatformControllerDataEnum {
    /**
     * eshop
     */
    ESHOP(0, "Eshop表"),
    /**
     * eshopCondig
     */
    CONFIG(1, "config表");


    private int flag;

    private String name;

    PlatformControllerDataEnum(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }
}
