package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 23/7/2020 下午 2:23
 */
public enum OrderTimingType implements CodeEnum {
    NORMAL(0, ""),
    TODAY(1, "当日达"),
    TOMORROW_MORNING(2, "次晨达"),
    TOMORROW(3, "次日达"),
    FOLLOWING_DAY(4, "隔日达"),
    THREE_DAY(5, "三日达"),
    FOUR_DAY(6, "四日达"),
    THREE_KILOMETRE_TOW_HOUR(7, "3公里2小时达"),
    THREE_KILOMETRE_NIGHT_ORDER(8, "3公里深夜预约"),
    TEN_KILOMETRE_FOUR_HOUR(9, "10公里4小时达"),
    TEN_KILOMETRE_NIGHT_ORDER(10, "10公里深夜预约"),
    TIMING_SEND(11, "定时送"),
    ON_TIME_ARRIVE(12, "按时达"),
    B_24H(13, "24 小时内发货"),
    B_48H(14, " 48 小时内发货"),
    ;
    private int index;

    private String name;

    OrderTimingType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }
}
