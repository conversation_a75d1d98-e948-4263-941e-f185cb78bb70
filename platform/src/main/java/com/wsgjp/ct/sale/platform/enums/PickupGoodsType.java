package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PickupGoodsType implements CodeEnum {

    /**
     * 物品类型 1:衣服 2:母婴玩具(非食品) 3:运动健康 4:3C数码 5:汽车用品 6:其他
     */

    CLOTH(1, "衣服"),
    TOY(2, "母婴玩具(非食品)"),
    SPORTS_AND_HEALTH(3, "运动健康"),
    DIGITAL_3C(4, "3C数码"),
    CAR_ACCESSORIES(5, "汽车用品"),
    OTHER(6, "其它");


    private int code;
    private String name;

    PickupGoodsType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PickupGoodsType valueOf(int code) {
        for (PickupGoodsType goodsType : values()) {
            if (goodsType.getCode() == code) {
                return goodsType;
            }
        }
        return PickupGoodsType.OTHER;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
