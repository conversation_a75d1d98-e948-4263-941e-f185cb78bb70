package com.wsgjp.ct.sale.platform.dto.plugin;

import com.wsgjp.ct.sale.platform.dto.order.SubscribeApplication;
import com.wsgjp.ct.sale.platform.enums.AuthType;
import com.wsgjp.ct.sale.platform.enums.TagAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-27 17:05
 */
@ApiModel("店铺标签信息")
public class EshopTagInfo {
    @ApiModelProperty("是否显示授权")
    private boolean showAuth;
    @ApiModelProperty("是否显示包裹号")
    private boolean showPackageCode;
    @ApiModelProperty("是否显示AG（急速退款）")
    private boolean showAg;
    @ApiModelProperty("是否显示订购退款标志")
    private boolean showRefundLink;
    @ApiModelProperty("退款类型")
    private int refundShopType;
    @ApiModelProperty("是否展示订购")
    private boolean showOrderLink;
    @ApiModelProperty("是否有授权")
    private boolean hasAuth;
    @ApiModelProperty("授权类型")
    private AuthType authType;
    @ApiModelProperty("订购链接")
    private String orderLink;
    @ApiModelProperty("退款链接")
    private String refundLink;
    @ApiModelProperty("订阅应用")
    private List<SubscribeApplication> subscribeApplications;
    @ApiModelProperty("商城类型")
    private int mallType;
    /**
     * 是否支持设置平台扣率 - 平台佣金
     */
    @ApiModelProperty("是否支持平台扣率")
    private boolean enablePlatformCommission;
    /**
     * 是否支持选择以买家账号创建往来单位
     */
    @ApiModelProperty("是否允许用买家账号创建往来单位")
    private boolean allowBuyerAccount = true;
    /**
     * 是否展示填写appkey
     */
    @ApiModelProperty("是否展示AppKey")
    private boolean showAppKey;
    /**
     *     DO_NOT(0,"无需记账"),
     *     BY_ORG(1,"按网店记账"),
     *     BY_BUYER(2,"按买家账号记账"),
     *     BY_FXS(3,"按分销商记账"),
     *     BY_MD(4,"按门店记账");
     */
    @ApiModelProperty("指定记账方式")
    private int assignAccountType;
    @ApiModelProperty("处理方式；0=下辖，1=网店")
    private int process_type = 1;
    /**
     * 使用appKey值作为OnlineShopId
     */
    private boolean useAppKeyAsOnlineShopId;

    /**
     * 店铺开关配置，暂时存SysData, key=showOutOfStockCallback_{eshopId}
     */
    private List<TagAttribute> attributeList;

    public boolean isUseAppKeyAsOnlineShopId() {
        return useAppKeyAsOnlineShopId;
    }

    public boolean isShowAuth() {
        return showAuth;
    }

    public void setShowAuth(boolean showAuth) {
        this.showAuth = showAuth;
    }

    public boolean isShowPackageCode() {
        return showPackageCode;
    }

    public void setShowPackageCode(boolean showPackageCode) {
        this.showPackageCode = showPackageCode;
    }

    public boolean isShowAg() {
        return showAg;
    }

    public void setShowAg(boolean showAg) {
        this.showAg = showAg;
    }

    public boolean isShowRefundLink() {
        return showRefundLink;
    }

    public void setShowRefundLink(boolean showRefundLink) {
        this.showRefundLink = showRefundLink;
    }

    public boolean isShowOrderLink() {
        return showOrderLink;
    }

    public void setShowOrderLink(boolean showOrderLink) {
        this.showOrderLink = showOrderLink;
    }

    public boolean isHasAuth() {
        return hasAuth;
    }

    public void setHasAuth(boolean hasAuth) {
        this.hasAuth = hasAuth;
    }

    public AuthType getAuthType() {
        return authType;
    }

    public void setAuthType(AuthType authType) {
        this.authType = authType;
    }

    public String getOrderLink() {
        return orderLink;
    }

    public void setOrderLink(String orderLink) {
        this.orderLink = orderLink;
    }

    public String getRefundLink() {
        return refundLink;
    }

    public void setRefundLink(String refundLink) {
        this.refundLink = refundLink;
    }

    public boolean isEnablePlatformCommission() {
        return enablePlatformCommission;
    }

    public void setEnablePlatformCommission(boolean enablePlatformCommission) {
        this.enablePlatformCommission = enablePlatformCommission;
    }

    public boolean isAllowBuyerAccount() {
        return allowBuyerAccount;
    }

    public void setAllowBuyerAccount(boolean allowBuyerAccount) {
        this.allowBuyerAccount = allowBuyerAccount;
    }

    public int getMallType() {
        return mallType;
    }

    public void setMallType(int mallType) {
        this.mallType = mallType;
    }

    public int getRefundShopType() {
        return refundShopType;
    }

    public void setRefundShopType(int refundShopType) {
        this.refundShopType = refundShopType;
    }

    public boolean isShowAppKey() {
        return showAppKey;
    }

    public void setShowAppKey(boolean showAppKey) {
        this.showAppKey = showAppKey;
    }

    public List<SubscribeApplication> getSubscribeApplications() {
        return subscribeApplications;
    }

    public void setSubscribeApplications(List<SubscribeApplication> subscribeApplications) {
        this.subscribeApplications = subscribeApplications;
    }

    public int getAssignAccountType() {
        return assignAccountType;
    }

    public void setAssignAccountType(int assignAccountType) {
        this.assignAccountType = assignAccountType;
    }

    public int getProcess_type() {
        return process_type;
    }

    public void setProcess_type(int process_type) {
        this.process_type = process_type;
    }

    public boolean getUseAppKeyAsOnlineShopId() {
        return useAppKeyAsOnlineShopId;
    }

    public void setUseAppKeyAsOnlineShopId(boolean useAppKeyAsOnlineShopId) {
        this.useAppKeyAsOnlineShopId = useAppKeyAsOnlineShopId;
    }

    public List<TagAttribute> getAttributeList() {
        return attributeList;
    }

    public void setAttributeList(List<TagAttribute> attributeList) {
        this.attributeList = attributeList;
    }
}
