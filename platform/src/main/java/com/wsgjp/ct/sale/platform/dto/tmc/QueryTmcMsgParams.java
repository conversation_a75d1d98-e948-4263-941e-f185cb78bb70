package com.wsgjp.ct.sale.platform.dto.tmc;

import com.wsgjp.ct.sale.platform.enums.DateType;
import com.wsgjp.ct.sale.platform.slice.DownloadSlice;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryTmcMsgParams {
    private BigInteger profileId;
    private BigInteger eshopId;
    private List<String> tradeIds;
    private Date startTime;
    private Date endTime;
    /**
     * 查询时间类型
     * 0 按创建时间查询
     * 1 按更新时间查询
     */
    private DateType dateType;
    /**
     * 从第0页开始
     */
    private Integer pageNo;
    private Integer pageSize;

    public QueryTmcMsgParams() {

    }

    public QueryTmcMsgParams(BigInteger profileId, BigInteger eshopId) {
        this.profileId = profileId;
        this.eshopId = eshopId;
    }

    public QueryTmcMsgParams(BigInteger profileId, BigInteger eshopId, DownloadSlice request, boolean createTime) {
        this.profileId = profileId;
        this.eshopId = eshopId;
        this.pageNo = request.getPage();
        this.pageSize = request.getPageSize();
        this.startTime = request.getStartTime();
        this.endTime = request.getEndTime();
        this.setDateType(createTime ? DateType.CREATE_TIME : DateType.UPDATE_TIME);
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public DateType getDateType() {
        return dateType;
    }

    public void setDateType(DateType dateType) {
        this.dateType = dateType;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public List<String> getTradeIds() {
        return tradeIds;
    }

    public void setTradeIds(List<String> tradeIds) {
        this.tradeIds = tradeIds;
    }
}
