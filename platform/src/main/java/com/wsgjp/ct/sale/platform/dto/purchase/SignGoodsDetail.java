package com.wsgjp.ct.sale.platform.dto.purchase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 订单签收明细(入库商品详情)
 *
 * <AUTHOR>
 * @date 2022-01-13 15:18
 */
@ApiModel("订单签收明细(入库商品详情)")
public class SignGoodsDetail {

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商家编码")
    private String xcode;

    @ApiModelProperty("入库数量")
    private BigDecimal qty;

    @ApiModelProperty("商品单位名称")
    private String unitName;

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public BigDecimal getQty() {
        if (qty==null){
            qty = BigDecimal.ZERO;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}
