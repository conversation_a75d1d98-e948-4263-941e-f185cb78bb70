package com.wsgjp.ct.sale.platform.dto.sendgoods;

import java.math.BigDecimal;

public class OrderDetailInformation {

    /**
     * 明细id
     */
    private String oid;
    /**
     * 原始数量(订单明细中的原始商品数量)
     */
    private BigDecimal originalQty;
    /**
     * 套餐行数量
     */
    private BigDecimal comboQty;
    /**
     * 已经发货的数量
     */
    private BigDecimal shippedQty;
    /**
     * 商品numid
     */
    private String numId;
    /**
     * 商品skuid
     */
    private String skuId;

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public BigDecimal getOriginalQty() {
        return originalQty;
    }

    public void setOriginalQty(BigDecimal originalQty) {
        this.originalQty = originalQty;
    }

    public BigDecimal getShippedQty() {
        return shippedQty;
    }

    public void setShippedQty(BigDecimal shippedQty) {
        this.shippedQty = shippedQty;
    }

    public String getNumId() {
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getComboQty() {
        return comboQty;
    }

    public void setComboQty(BigDecimal comboQty) {
        this.comboQty = comboQty;
    }
}
