package com.wsgjp.ct.sale.platform.dto.product;

/**
 * 商品品牌
 * <AUTHOR>
 */
public class EshopBrandEntity {
    /**
     * 品牌Id
     */
    private String brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌的属性id,如:20000
     */
    private String brandPropId;

    /**
     * 品牌的属性名:如:品牌
     */
    private String brandPropName;

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getBrandPropId() {
        return brandPropId;
    }

    public void setBrandPropId(String brandPropId) {
        this.brandPropId = brandPropId;
    }

    public String getBrandPropName() {
        return brandPropName;
    }

    public void setBrandPropName(String brandPropName) {
        this.brandPropName = brandPropName;
    }
}
