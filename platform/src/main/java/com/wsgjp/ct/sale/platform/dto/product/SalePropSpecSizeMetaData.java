package com.wsgjp.ct.sale.platform.dto.product;

/**
 * 尺码需要编辑的元素信息，比如：身高，胸围，体重。。。。
 *
 * <AUTHOR>
 */
public class SalePropSpecSizeMetaData {
    //尺码表分类id
    private int sizeSpecClassId;


    //尺码表分组id
    private int sizeSpecGroupId;


    //尺码表分组名称
    private int sizeSpecGroupName;

    //尺码表元数据Id
    private int sizeSpecMetaId;

    //尺码表元数据名称
    private String sizeSpecMetaName;

    public int getSizeSpecClassId() {
        return sizeSpecClassId;
    }

    public void setSizeSpecClassId(int sizeSpecClassId) {
        this.sizeSpecClassId = sizeSpecClassId;
    }

    public int getSizeSpecGroupId() {
        return sizeSpecGroupId;
    }

    public void setSizeSpecGroupId(int sizeSpecGroupId) {
        this.sizeSpecGroupId = sizeSpecGroupId;
    }

    public int getSizeSpecGroupName() {
        return sizeSpecGroupName;
    }

    public void setSizeSpecGroupName(int sizeSpecGroupName) {
        this.sizeSpecGroupName = sizeSpecGroupName;
    }

    public int getSizeSpecMetaId() {
        return sizeSpecMetaId;
    }

    public void setSizeSpecMetaId(int sizeSpecMetaId) {
        this.sizeSpecMetaId = sizeSpecMetaId;
    }

    public String getSizeSpecMetaName() {
        return sizeSpecMetaName;
    }

    public void setSizeSpecMetaName(String sizeSpecMetaName) {
        this.sizeSpecMetaName = sizeSpecMetaName;
    }
}
