package com.wsgjp.ct.sale.platform.dto.product;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ProductBizInfo {
    /**
     * 是否otc分类
     */
    private boolean isOtc;
    /**
     * 是否处方药分类
     */
    private boolean isPrescription;
    /**
     * 是否资质敏感分类
     */
    private boolean isSensitive;
    /**
     * 是否是属性商品
     */
    private boolean isProp;

    /**
     * 是否标品
     */
    private boolean standardGoods;

    private List<ServiceRule> serviceRules;
    /**
     * 是否必须设置满2件折扣
     */
    private boolean pieceDiscountRequired;
    /**
     * 允许的最大折扣%比
     */
    private int maxDiscount;
    /**
     * 允许的最小折扣%比
     */
    private int minDiscount;

    /**
     * 推荐的折扣, %比
     */
    private int recommendDiscount;

    public boolean getOtc() {
        return isOtc;
    }

    public void setOtc(boolean otc) {
        isOtc = otc;
    }

    public boolean getIsPrescription() {
        return isPrescription;
    }

    public void setPrescription(boolean prescription) {
        isPrescription = prescription;
    }

    public boolean getIsSensitive() {
        return isSensitive;
    }

    public void setSensitive(boolean sensitive) {
        isSensitive = sensitive;
    }

    public boolean getIsProp() {
        return isProp;
    }

    public void setProp(boolean prop) {
        isProp = prop;
    }

    public boolean isOtc() {
        return isOtc;
    }

    public boolean isPrescription() {
        return isPrescription;
    }

    public boolean isSensitive() {
        return isSensitive;
    }

    public boolean isProp() {
        return isProp;
    }

    public boolean isStandardGoods() {
        return standardGoods;
    }

    public void setStandardGoods(boolean standardGoods) {
        this.standardGoods = standardGoods;
    }

    public List<ServiceRule> getServiceRules() {
        if (null == serviceRules) {
            serviceRules = new ArrayList<>();
        }
        return serviceRules;
    }

    public void setServiceRules(List<ServiceRule> serviceRules) {
        this.serviceRules = serviceRules;
    }

    public boolean isPieceDiscountRequired() {
        return pieceDiscountRequired;
    }

    public void setPieceDiscountRequired(boolean pieceDiscountRequired) {
        this.pieceDiscountRequired = pieceDiscountRequired;
    }

    public int getMaxDiscount() {
        return maxDiscount;
    }

    public void setMaxDiscount(int maxDiscount) {
        this.maxDiscount = maxDiscount;
    }

    public int getMinDiscount() {
        return minDiscount;
    }

    public void setMinDiscount(int minDiscount) {
        this.minDiscount = minDiscount;
    }

    public int getRecommendDiscount() {
        return recommendDiscount;
    }

    public void setRecommendDiscount(int recommendDiscount) {
        this.recommendDiscount = recommendDiscount;
    }

}
