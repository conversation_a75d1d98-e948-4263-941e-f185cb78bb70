package com.wsgjp.ct.sale.platform.mock.taobao;

import com.taobao.api.request.TradeFullinfoGetRequest;
import com.wsgjp.ct.sale.platform.entity.request.other.MockRequest;
import com.wsgjp.ct.sale.platform.mock.ApiMockQueryService;
import com.wsgjp.ct.sale.platform.mock.ApiMockerService;
import com.wsgjp.ct.sale.platform.utils.MockUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2024/5/28 17:14
 */
@Service
public class TaoBaoOrderGetMockImpl implements ApiMockerService {

    private final ApiMockQueryService queryService;

    public TaoBaoOrderGetMockImpl(ApiMockQueryService queryService) {
        this.queryService = queryService;
    }

    @Override
    public Object queryData(MockRequest request) {
        String json = queryService.queryTradeByTradeId(request.getShopAccount(), request.getTradeId());
        return MockUtils.doBuildTbTrade(json);
    }

    @Override
    public String methodName() {
        return "taobao.trade.fullinfo.get";
    }

    @Override
    public void buildRequest(MockRequest request, Object[] args) {
        TradeFullinfoGetRequest getRequest = (TradeFullinfoGetRequest) args[0];
        request.setTradeId(getRequest.getTid().toString());
    }
}
