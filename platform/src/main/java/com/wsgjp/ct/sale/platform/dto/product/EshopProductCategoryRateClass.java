package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-7-16 19:32
 */
@ApiModel("网店商品类目")
public class EshopProductCategoryRateClass extends ErpBaseInfo {

    public EshopProductCategoryRateClass() {
    }

    public EshopProductCategoryRateClass(EshopSystemParams systemParams) {
        setProfileId(systemParams.getProfileId());
        setEshopId(systemParams.geteShopId());
        setShopType(systemParams.getShopType());
    }

    /**
     * 商城类目id
     */
    @ApiModelProperty("商城类目id")
    private String platformClassId;
    /**
     * 网店父级目录名称
     */
    @ApiModelProperty("网店父级目录名称")
    private String platformClassName;
    /**
     * 线上父级类目id
     */
    @ApiModelProperty("线上父级类目id")
    private String parPlatformClassId;

    /**
     * 线上类目id组合：线上父级类目id+线上子级类目id
     */
    @ApiModelProperty("线上类目id组合：线上父级类目id+线上子级类目id")
    private String concatParSonPlatformClassId;
    /**
     * 线上分类id全路径（根节点一直到子节点的路径,用 _ 链接）
     */
    @ApiModelProperty("线上分类id全路径（根节点一直到子节点的路径,用 _ 链接）")
    private String fullPlatformClassId;
    /**
     * 分类id全路径（根节点一直到子节点的路径,用 _ 链接）
     */
    @ApiModelProperty("分类id全路径（根节点一直到子节点的路径,用 _ 链接）")
    private String fullClassId;
    /**
     * 是否有子节点
     */
    @ApiModelProperty("是否有子节点")
    private boolean hasSon;
    /**
     * 商城扣点
     */
    @ApiModelProperty("商城扣点")
    private BigDecimal platformComminsion = BigDecimal.ZERO;
    /**
     * 商城扣点：用于UI-》 判断扣点设置是否有变
     */
    @ApiModelProperty("商城扣点：用于UI-》 判断扣点设置是否有变")
    private BigDecimal prePlatformcomminsion;


    /**
     * 线上分类id全路径（根节点一直到子节点的路径,用 _ 链接）
     */
    @ApiModelProperty("线上分类id全路径（根节点一直到子节点的路径,用 \\ 链接）")
    private String fullPlatformClassName;

    private boolean checked;

    /**
     * 以下字段是业务使用字段，平台无需赋值
     */
    private BigInteger id;
    private BigInteger parId;
    private String crateTime;
    private String updateTime;
    /**
     * 网店目录id组合：父级id+子级id（父亲+当前节点,
     */
    private String concatParSonId;

    private int haschild;

    private List<String> childClassIds;

    public String getPlatformClassId() {
        return platformClassId;
    }

    public void setPlatformClassId(String platformClassId) {
        this.platformClassId = platformClassId;
    }

    public String getPlatformClassName() {
        return platformClassName;
    }

    public void setPlatformClassName(String platformClassName) {
        this.platformClassName = platformClassName;
    }

    public String getParPlatformClassId() {
        return parPlatformClassId;
    }

    public void setParPlatformClassId(String parPlatformClassId) {
        this.parPlatformClassId = parPlatformClassId;
    }

    public String getConcatParSonPlatformClassId() {
        return concatParSonPlatformClassId;
    }

    public void setConcatParSonPlatformClassId(String concatParSonPlatformClassId) {
        this.concatParSonPlatformClassId = concatParSonPlatformClassId;
    }

    public String getFullPlatformClassId() {
        return fullPlatformClassId;
    }

    public void setFullPlatformClassId(String fullPlatformClassId) {
        this.fullPlatformClassId = fullPlatformClassId;
    }

    public String getFullClassId() {
        return fullClassId;
    }

    public void setFullClassId(String fullClassId) {
        this.fullClassId = fullClassId;
    }

    public boolean isHasSon() {
        return hasSon;
    }

    public void setHasSon(boolean hasSon) {
        this.hasSon = hasSon;
    }

    public BigDecimal getPlatformComminsion() {
        if (platformComminsion == null) {
            return BigDecimal.ZERO;
        }
        return platformComminsion;
    }

    public void setPlatformComminsion(BigDecimal platformComminsion) {
        this.platformComminsion = platformComminsion;
    }

    public BigDecimal getPrePlatformcomminsion() {
        return prePlatformcomminsion;
    }

    public void setPrePlatformcomminsion(BigDecimal prePlatformcomminsion) {
        this.prePlatformcomminsion = prePlatformcomminsion;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getParId() {
        return parId;
    }

    public void setParId(BigInteger parId) {
        this.parId = parId;
    }

    public String getCrateTime() {
        return crateTime;
    }

    public void setCrateTime(String crateTime) {
        this.crateTime = crateTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getConcatParSonId() {
        return concatParSonId;
    }

    public void setConcatParSonId(String concatParSonId) {
        this.concatParSonId = concatParSonId;
    }

    public String getFullPlatformClassName() {
        return fullPlatformClassName;
    }

    public void setFullPlatformClassName(String fullPlatformClassName) {
        this.fullPlatformClassName = fullPlatformClassName;
    }

    public int getHaschild() {
        if (isHasSon()) {
            haschild = 1;
        } else {
            haschild = 0;
        }
        return haschild;
    }

    public void setHaschild(int haschild) {
        this.haschild = haschild;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public List<String> getChildClassIds() {
        if (CollectionUtils.isEmpty(this.childClassIds)) {
            childClassIds = new ArrayList<>();
        }
        return childClassIds;
    }

    public void setChildClassIds(List<String> childClassIds) {
        this.childClassIds = childClassIds;
    }
}
