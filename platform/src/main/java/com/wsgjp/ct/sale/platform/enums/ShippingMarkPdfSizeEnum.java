package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum ShippingMarkPdfSizeEnum implements CodeEnum {

    /**
     * 1. SIZE_100_150：100mm*150mm；
     * 2. SIZE_100_100：100mm*100mm;
     */

    SIZE_100_150(1, "SIZE_100_150"),
    SIZE_100_100(2, "SIZE_100_100");

    private int code;
    private String name;

    ShippingMarkPdfSizeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
