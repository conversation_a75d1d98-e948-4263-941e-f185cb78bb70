package com.wsgjp.ct.sale.platform.entity.entities.hipac;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class OrderItem {

    @XmlElement(name = "itemName")
    public String itemName;

    @XmlElement(name = "itemSupplyNo")
    public String itemSupplyNo;

    @XmlElement(name = "itemPrice")
    public String itemPrice;

    @XmlElement(name = "itemQuantity")
    public int itemQuantity;

    @XmlElement(name = "itemTotal")
    public String itemTotal;

    @XmlElement(name = "itemTotalTax")
    public String itemTotalTax;

    @XmlElement(name = "addTaxRate")
    public String addTaxRate;

    @XmlElement(name = "exciseRate")
    public String exciseRate;


    @XmlElement(name = "tariffRate")
    public String tariffRate;

    @XmlElement(name = "specNme")
    public String specNme;


    @XmlElement(name = "specNum")
    public int specNum;

    @XmlElement(name = "orderSpecQuantity")
    public int orderSpecQuantity;

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemSupplyNo() {
        return itemSupplyNo;
    }

    public void setItemSupplyNo(String itemSupplyNo) {
        this.itemSupplyNo = itemSupplyNo;
    }

    public String getItemPrice() {
        return itemPrice;
    }

    public void setItemPrice(String itemPrice) {
        this.itemPrice = itemPrice;
    }

    public int getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(int itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    public String getItemTotal() {
        return itemTotal;
    }

    public void setItemTotal(String itemTotal) {
        this.itemTotal = itemTotal;
    }

    public String getItemTotalTax() {
        return itemTotalTax;
    }

    public void setItemTotalTax(String itemTotalTax) {
        this.itemTotalTax = itemTotalTax;
    }

    public String getAddTaxRate() {
        return addTaxRate;
    }

    public void setAddTaxRate(String addTaxRate) {
        this.addTaxRate = addTaxRate;
    }

    public String getExciseRate() {
        return exciseRate;
    }

    public void setExciseRate(String exciseRate) {
        this.exciseRate = exciseRate;
    }

    public String getTariffRate() {
        return tariffRate;
    }

    public void setTariffRate(String tariffRate) {
        this.tariffRate = tariffRate;
    }

    public String getSpecNme() {
        return specNme;
    }

    public void setSpecNme(String specNme) {
        this.specNme = specNme;
    }

    public int getSpecNum() {
        return specNum;
    }

    public void setSpecNum(int specNum) {
        this.specNum = specNum;
    }

    public int getOrderSpecQuantity() {
        return orderSpecQuantity;
    }

    public void setOrderSpecQuantity(int orderSpecQuantity) {
        this.orderSpecQuantity = orderSpecQuantity;
    }
}
