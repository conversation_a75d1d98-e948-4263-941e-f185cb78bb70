package com.wsgjp.ct.sale.platform.dto.order;

import java.util.Objects;

/**
 * 订单明细：赠品与主商品的关联关系
 */
public class ProductGiftRelation {
    //主商品oid
    private String sourceOid;
    //赠品oid
    private String giftOid;

    public String getSourceOid() {
        return sourceOid;
    }

    public void setSourceOid(String sourceOid) {
        this.sourceOid = sourceOid;
    }

    public String getGiftOid() {
        return giftOid;
    }

    public void setGiftOid(String giftOid) {
        this.giftOid = giftOid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductGiftRelation that = (ProductGiftRelation) o;
        return Objects.equals(sourceOid, that.sourceOid) && Objects.equals(giftOid, that.giftOid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sourceOid, giftOid);
    }
}
