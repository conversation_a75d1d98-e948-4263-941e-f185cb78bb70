package com.wsgjp.ct.sale.platform.slice;

import com.wsgjp.ct.sale.platform.enums.SliceSequence;
import com.wsgjp.ct.sale.platform.enums.SliceType;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DownloadProductSlice extends BaseSlice {
    private Date startTime;
    private Date endTime;
    private int page;
    private int pageSize;
    private String cursor;
    private int businessType;
    private SliceType type;
    private SliceProductParams sliceParams;
    private int status;
    private int maxPage;
    private SliceSequence sliceSequence;

    public SliceSequence getSliceSequence() {
        return sliceSequence;
    }

    public void setSliceSequence(SliceSequence sliceSequence) {
        this.sliceSequence = sliceSequence;
    }

    public int getMaxPage() {
        return maxPage;
    }

    public void setMaxPage(int maxPage) {
        this.maxPage = maxPage;
    }

    @ApiModelProperty("需要过滤的值")
    private String filterStr;
    @ApiModelProperty("商品类目Id")
    private String sellerClassId;
    @ApiModelProperty("是否下载库存商品")
    private boolean downloadStock = true;
    @ApiModelProperty("商品NumId列表")
    private List<String> numIds;

    public String getFilterStr() {
        return filterStr;
    }

    public void setFilterStr(String filterStr) {
        this.filterStr = filterStr;
    }

    public String getSellerClassId() {
        return sellerClassId;
    }

    public void setSellerClassId(String sellerClassId) {
        this.sellerClassId = sellerClassId;
    }

    public boolean isDownloadStock() {
        return downloadStock;
    }

    public void setDownloadStock(boolean downloadStock) {
        this.downloadStock = downloadStock;
    }

    public List<String> getNumIds() {
        return numIds;
    }

    public void setNumIds(List<String> numIds) {
        this.numIds = numIds;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getCursor() {
        return cursor;
    }

    public void setCursor(String cursor) {
        this.cursor = cursor;
    }

    public int getBusinessType() {
        return businessType;
    }

    public void setBusinessType(int businessType) {
        this.businessType = businessType;
    }

    public SliceType getType() {
        return type;
    }

    public void setType(SliceType type) {
        this.type = type;
    }

    public SliceProductParams getSliceParams() {
        return sliceParams;
    }

    public void setSliceParams(SliceProductParams sliceParams) {
        this.sliceParams = sliceParams;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
