package com.wsgjp.ct.sale.platform.dto.purchase;

import ngp.utils.StringUtils;

import java.math.BigDecimal;

public class EshopPurchaseOrderDetail {
    /**
     * 商家编码
     */
    private String xcode;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 规格型号（sku属性）
     */
    private String properties;
    /**
     * 重量
     */
    private String weight;

    /**
     * 图片
     */
    private String imgUrl;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 批次号
     */
    private String batchNumber;

    /**
     * 是否是赠品
     */
    private boolean gift;

    private BigDecimal qty;

    /**
     * 商品的唯一编码
     */
    private String nunId;


    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public boolean isGift() {
        return gift;
    }

    public void setGift(boolean gift) {
        this.gift = gift;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getNunId() {
        return nunId;
    }

    public void setNunId(String nunId) {
        this.nunId = nunId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getSkuId() {
        StringBuilder skuidBuild = new StringBuilder();
        if (StringUtils.isEmpty(nunId))
        {
            return skuidBuild.toString();
        }
        skuidBuild.append(nunId);
        if (StringUtils.isEmpty(unitName))
        {
            return skuidBuild.toString();
        }
        skuidBuild.append("|");
        skuidBuild.append(unitName);
        return skuidBuild.toString();
    }

}
