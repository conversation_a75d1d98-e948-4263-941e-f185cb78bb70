package com.wsgjp.ct.sale.platform.dto.member;

import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 会员信息
 */
public class VipMemberEntity extends ErpBaseInfo {
    /**
     * 会员卡ID
     */
    public Long id;
    /**
     * 会员编号
     */
    public String number;
    /**
     * 发卡日期
     */
    public Date handOutDate;
    /**
     * 到期日期
     */
    public Date ValidDate;
    /**
     * 会员卡类型id
     */
    public Long cardTypeID;
    /**
     * 会员卡类型名称
     */
    public String cardTypeName;
    /**
     * 会员卡状态（启用 停用 挂失 ）
     */
    //public VipMemberState cardState;
    /**
     * 会员卡号
     */
    public String cardNumber;
    /**
     * 持卡者名字
     */
    public String holderName;
    /**
     * 会员证件号
     */
    public String holderID;
    /**
     * 会员证件号
     */
    public String holderTel;
    /**
     * 会员住址
     */
    public String holderAddress;
    /**
     * 备注
     */
    public String comment;
    /**
     * 有效年限
     */
    public double validYear;
    /**
     * 会员折扣
     */
    public BigDecimal holderDiscount;
    /**
     * 最后使用日期
     */
    public Date lastDate;
    /**
     * 累计消费金额
     */
    public BigDecimal useTotal;
    /**
     * 累计充值金额
     */
    public BigDecimal rechargeTotal;
    /**
     * 余额
     */
    public BigDecimal balanceTotal;
    /**
     * 账户积分
     */
    public BigDecimal integralTotal;
    /**
     * 使用次数
     */
    public int usedCount;
    /**
     * 创建时间
     */
    public Date createTime;
    /**
     * 修改时间
     */
    public Date modifyTime;
    /**
     * 初始积分
     */
    public BigDecimal initialTotal;
    /**
     * 客户端已使用余额
     */
    public BigDecimal assignedBalance;
    /**
     * 允许储值 默认允许积分
     */
    private boolean canDeposit = true;
    /**
     * 允许积分 默认允许积分
     */
    public boolean canIntegral;
    /**
     * 允许使用角色
     */
    public boolean canUseRule;
    /**
     * 会员储值消费密码
     */
    public String payPassWord;
    /**
     * 期初余额
     */
    public BigDecimal initBalanceTotal;
    /**
     * 会员生日
     */
    public Date holderBirthday;
    public boolean deleted;
    public boolean pushStatus;
    public boolean message;
}
