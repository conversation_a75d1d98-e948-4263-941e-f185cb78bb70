package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * 商品批次上传请求参数
 * <AUTHOR>
 */
@ApiModel("商品批次上传请求参数")
public class EshopProductBatchPublishEntity extends ErpBaseInfo {

    @ApiModelProperty("商品全名")
    private String productName;
    @ApiModelProperty("商品id")
    private String productId;

    @ApiModelProperty("商品编码")
    private String productCode;

    @ApiModelProperty("过期时间")
    private Date expireDate;
    @ApiModelProperty("生产日期/最新生产日期")
    private Date produceDate;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("批次ID")
    private String batchId;

    @ApiModelProperty("sku和单位信息 中台/药师帮")
    private List<SkuUnitInfo> skuUnitInfoList;

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(Date produceDate) {
        this.produceDate = produceDate;
    }

    public List<SkuUnitInfo> getSkuUnitInfoList() {
        return skuUnitInfoList;
    }

    public void setSkuUnitInfoList(List<SkuUnitInfo> skuUnitInfoList) {
        this.skuUnitInfoList = skuUnitInfoList;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}
