package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * <p>
 * 2020-01-16
 */

public enum NotifyType implements CodeEnum {

    Order(0, "订单变更消息"),
    Ptype(1, "商品消息"),
    RefundOrder(2, "售后订单"),
    InvoiceResultReturn(3, "电子发票开票结果"),
    AGRefund(4, "AG售后退款消息"),
    InvoiceApply(5, "Invoice");

    private int flag;
    private String name;

    NotifyType(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }


    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
