package com.wsgjp.ct.sale.platform.dto.plugin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 3/4/2020 下午 3:19
 */
@ApiModel("下拉选项")
public class DropDownPlugin {
    public DropDownPlugin() {
    }

    public DropDownPlugin(int value, String text) {
        this.value = value;
        this.text = text;
        this.enable = false;
    }

    public DropDownPlugin(int id, String key, Boolean enable) {
        this.value = id;
        this.text = key;
        this.enable = enable;
    }

    @ApiModelProperty("值")
    private int value;
    @ApiModelProperty("展示文本")
    private String text;
    @ApiModelProperty("是否启用")
    private Boolean enable;

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DropDownPlugin other = (DropDownPlugin) obj;
        if (this.value != other.value) {
            return false;
        }
        return true;
    }
}
