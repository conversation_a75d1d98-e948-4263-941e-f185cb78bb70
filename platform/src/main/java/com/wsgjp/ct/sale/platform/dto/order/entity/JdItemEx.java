package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.platform.factory.jdong.entity.SerialCodeType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-26 13:16
 */
public class JdItemEx {
    private String originPrice;
    private String newStoreId;
    private String skuUuid;
    /**
     * sku对应经销商店铺id
     */
    private String popSupplyShopId;
    /**
     * sku对应经销商店铺名称
     */
    private String popSupplyShopName;
    /**
     * sku对应经销商的公司id
     */
    private String popSupplyCompanyName;
    /**
     * sku对应经销商的公司名称
     */
    private String popSupplyCompanyId;

    /**
     *  3c国补订单上传4码要求（仅详情接口有值）
     */
    private List<SerialCodeType> serialCodeReqList;
    /**
     * 京东大店订单采购价
     */
    private String cgPrice;

    public String getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(String originPrice) {
        this.originPrice = originPrice;
    }

    public String getNewStoreId() {
        return newStoreId;
    }

    public void setNewStoreId(String newStoreId) {
        this.newStoreId = newStoreId;
    }

    public String getSkuUuid() {
        return skuUuid;
    }

    public void setSkuUuid(String skuUuid) {
        this.skuUuid = skuUuid;
    }

    public String getPopSupplyShopId() {
        return popSupplyShopId;
    }

    public void setPopSupplyShopId(String popSupplyShopId) {
        this.popSupplyShopId = popSupplyShopId;
    }

    public String getPopSupplyShopName() {
        return popSupplyShopName;
    }

    public void setPopSupplyShopName(String popSupplyShopName) {
        this.popSupplyShopName = popSupplyShopName;
    }

    public String getPopSupplyCompanyName() {
        return popSupplyCompanyName;
    }

    public void setPopSupplyCompanyName(String popSupplyCompanyName) {
        this.popSupplyCompanyName = popSupplyCompanyName;
    }

    public String getPopSupplyCompanyId() {
        return popSupplyCompanyId;
    }

    public void setPopSupplyCompanyId(String popSupplyCompanyId) {
        this.popSupplyCompanyId = popSupplyCompanyId;
    }

    public List<SerialCodeType> getSerialCodeReqList() {
        return serialCodeReqList;
    }

    public void setSerialCodeReqList(List<SerialCodeType> serialCodeReqList) {
        this.serialCodeReqList = serialCodeReqList;
    }

    public String getCgPrice() {
        return cgPrice;
    }

    public void setCgPrice(String cgPrice) {
        this.cgPrice = cgPrice;
    }
}
