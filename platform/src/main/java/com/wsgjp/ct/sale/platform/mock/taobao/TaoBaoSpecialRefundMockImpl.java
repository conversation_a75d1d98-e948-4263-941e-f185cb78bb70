package com.wsgjp.ct.sale.platform.mock.taobao;

import com.wsgjp.ct.sale.platform.entity.request.other.MockRequest;
import com.wsgjp.ct.sale.platform.mock.ApiMockerService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2024/5/10 9:40
 */
@Service
public class TaoBaoSpecialRefundMockImpl  implements ApiMockerService {
    @Override
    public Object queryData(MockRequest request) {
        return null;
    }

    @Override
    public String methodName() {
        return "taobao.special.refunds.receive.get";
    }

    @Override
    public void buildRequest(MockRequest request, Object[] args) {

    }
}
