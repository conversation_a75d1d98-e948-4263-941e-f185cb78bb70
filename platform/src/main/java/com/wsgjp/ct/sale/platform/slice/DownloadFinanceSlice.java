package com.wsgjp.ct.sale.platform.slice;

import com.wsgjp.ct.sale.platform.enums.SliceType;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class DownloadFinanceSlice extends BaseSlice{
    private Date startTime;
    private Date endTime;
    private int page;
    private int pageSize;
    private String cursor;
    private int businessType;
    private SliceType type;
    private SliceFinanceParams sliceParams;
    private int status;

    private int maxPage;

    @ApiModelProperty("支付类型")
    private String type1;

    public String getType1() {
        return type1;
    }

    public void setType1(String type1) {
        this.type1 = type1;
    }

    public int getMaxPage() {
        return maxPage;
    }

    public void setMaxPage(int maxPage) {
        this.maxPage = maxPage;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getCursor() {
        return cursor;
    }

    public void setCursor(String cursor) {
        this.cursor = cursor;
    }

    public int getBusinessType() {
        return businessType;
    }

    public void setBusinessType(int businessType) {
        this.businessType = businessType;
    }

    public SliceType getType() {
        return type;
    }

    public void setType(SliceType type) {
        this.type = type;
    }

    public SliceFinanceParams getSliceParams() {
        return sliceParams;
    }

    public void setSliceParams(SliceFinanceParams sliceParams) {
        this.sliceParams = sliceParams;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
