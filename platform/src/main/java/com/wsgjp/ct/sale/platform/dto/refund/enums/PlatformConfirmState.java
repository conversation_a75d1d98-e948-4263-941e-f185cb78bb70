package com.wsgjp.ct.sale.platform.dto.refund.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PlatformConfirmState implements CodeEnum {
    ALL(-1,"全部"),
    WAIT_AUDIT(0,"未审核"),
    AUDITED(1,"同意审核"),
    REJECTED(2,"拒绝审核");


    private final int flag;

    private final String name;

    PlatformConfirmState(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

}
