package com.wsgjp.ct.sale.platform.enums;


/**
 * <AUTHOR>
 * @date 2021/8/6 16:29
 */
public enum FeatureEnum {

    WAREHOUSE_SYNC(0, "分仓同步", "EshopStockWarehouseFeature"),
    STOCK_SYNC(1, "库存同步", "EshopStockGeneralFeature"),
    ORDER_INCR(2, "根据更新时间获取订单列表（自动下单）", "EshopOrderDownloadFromApiIncrementFeature"),
    MODIFY_RECEIVER(3, "修改订单收货人信息", "EshopModifyReceiverFeature"),
    ;

    FeatureEnum(int code, String desc, String feature) {
        this.code = code;
        this.desc = desc;
        this.feature = feature;
    }

    private int code;
    private String desc;
    private String feature;

    public int getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }


    public String getFeature() {
        return feature;
    }

}
