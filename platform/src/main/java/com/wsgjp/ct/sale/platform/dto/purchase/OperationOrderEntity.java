package com.wsgjp.ct.sale.platform.dto.purchase;


import com.wsgjp.ct.sale.platform.enums.OperationType;

public class OperationOrderEntity {

    /**
     * 订单号
     */
    private String OrderId;
    /**
     * 操作人名字
     */
    private String operationName;
    /**
     * 操作人手机
     */
    private String operationMobile;
    /**
     * 操作备注
     */
    private String remark;
    /**
     * 操作人id 可以不填
     */
    private String userId;

    private OperationType type;

    public String getOrderId() {
        return OrderId;
    }

    public void setOrderId(String orderId) {
        OrderId = orderId;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOperationMobile() {
        return operationMobile;
    }

    public void setOperationMobile(String operationMobile) {
        this.operationMobile = operationMobile;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public OperationType getType() {
        return type;
    }

    public void setType(OperationType type) {
        this.type = type;
    }
}
