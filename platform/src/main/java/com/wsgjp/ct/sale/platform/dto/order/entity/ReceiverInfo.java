package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.enums.Sex;
import ngp.utils.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;

import java.math.BigInteger;

/**
 * 收货人信息
 */
public class ReceiverInfo extends SecretSystem {
    public ReceiverInfo() {
        customerAddress = "";
        customerShopAccount = "";
        customerAddress = "";
        receiver = "";
    }

    public ReceiverInfo(EshopSystemParams systemParams) {
        customerAddress = "";
        customerShopAccount = "";
        customerAddress = "";
        receiver = "";
        this.profileId = systemParams.getProfileId();
        this.shopType = systemParams.getShopType();
    }

    private BigInteger profileId;
    private ShopType shopType;
    /**
     * 收货人姓名
     */
    private String receiver;
    /**
     * 身份证姓名
     */
    private String customerIdCardName;
    /**
     * 身份证号
     */
    private String customerIdCardNumber;
    /**
     * 买家账号
     */
    private String customerShopAccount;
    /**
     * 支付账号
     */
    private String customerPayAccount;
    private String customerMobile;
    private String customerTel;
    private String customerCountry;
    private String customerProvince;
    private String customerCity;
    private String customerDistrict;
    private String customerTown;
    private String customerAddress;
    private String customerZipCode;
    private String customerEmail;
    private Sex sex;
    /**
     * 是否是平台加密的订单。传这个值时 订单加密时只会保存生成的Di ，不会进行数据模糊化，
     * 加密生成的mi pi ai 等数据也不进行覆盖
     **/
    private boolean needPlatformDecrypt;
    /**
     * 使用订单号对订单进行加密，订单敏感数据不会再次进行模糊化。
     **/
    private String tradeId;

    /**
     * 买家信息的唯一标识，业务端可以通过这个字段来判断买家信息是否发生变更
     */
    private String uniqueMark;

    /**
     * 平台原始的买家唯一标识(比如oaid、caid、openAddressId) 业务端之后可以通过这个来判断订单是否可以合并、以及外接wms推送云仓使用，不用在去从di上解析获取
     * 如果没有就不填
     */
    private String platformOpenId;

    /**
     * 平台原始的买家唯一标识(比如oaid、caid、openAddressId) 业务端之后可以通过这个来判断订单是否可以合并、以及外接wms推送云仓使用，不用在去从di上解析获取
     * 如果没有就不填
     */
    private String platformOpenId2;


    /**
     * 平台省地址编码
     */
    private String provinceCode;

    /**
     * 平台市地址编码
     */
    private String cityCode;

    /**
     * 平台区地址编码
     */
    private String districtCode;

    /**
     * 平台街道地址（=》四级地址）编码
     */
    private String townCode;
    /**
     * 真正买家id(送礼订单场景)
     */
    private String realPlatformBuyerId;
    /**
     * 真正买家昵称(送礼订单场景)
     */
    private String realPlatformBuyerNick;

    public String getReceiver() {
        if (StringUtils.isEmpty(receiver)) {
            return "";
        }
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public Sex getSex() {
        return sex;
    }

    public void setSex(Sex sex) {
        this.sex = sex;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }


    public String getCustomerPayAccount() {
        if (StringUtils.isEmpty(customerPayAccount)) {
            return "";
        }
        return customerPayAccount;
    }

    public void setCustomerPayAccount(String customerPayAccount) {
        this.customerPayAccount = customerPayAccount;
    }

    public String getCustomerShopAccount() {
        if (StringUtils.isEmpty(customerShopAccount)) {
            return "";
        }
        return customerShopAccount;
    }

    public void setCustomerShopAccount(String customerShopAccount) {
        this.customerShopAccount = customerShopAccount;
    }

    public String getCustomerMobile() {
        if (StringUtils.isEmpty(customerMobile)) {
            return "";
        }
        return customerMobile;
    }

    public void setCustomerMobile(String customerMobile) {
        this.customerMobile = customerMobile;
    }

    public String getCustomerProvince() {
        if (StringUtils.isEmpty(customerProvince)) {
            return "";
        }
        return customerProvince;
    }

    public void setCustomerProvince(String customerProvince) {
        this.customerProvince = customerProvince;
    }

    public String getCustomerCity() {
        if (StringUtils.isEmpty(customerCity)) {
            return "";
        }
        return customerCity;
    }

    public void setCustomerCity(String customerCity) {
        this.customerCity = customerCity;
    }

    public String getCustomerDistrict() {
        if (StringUtils.isEmpty(customerDistrict)) {
            return "";
        }
        return customerDistrict;
    }

    public void setCustomerDistrict(String customerDistrict) {
        this.customerDistrict = customerDistrict;
    }

    public String getCustomerAddress() {
        if (StringUtils.isEmpty(customerAddress)) {
            return "";
        }
        return customerAddress;
    }

    public void setCustomerAddress(String customerAddress) {
        this.customerAddress = customerAddress;
    }

    public String getCustomerZipCode() {
        if (StringUtils.isEmpty(customerZipCode)) {
            return "";
        }
        return customerZipCode;
    }

    public void setCustomerZipCode(String customerZipCode) {
        this.customerZipCode = customerZipCode;
    }

    public String getCustomerEmail() {
        if (StringUtils.isEmpty(customerEmail)) {
            return "";
        }
        return customerEmail;
    }

    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }

    public String getCustomerIdCardName() {
        if (StringUtils.isEmpty(customerIdCardName)) {
            return "";
        }
        return customerIdCardName;
    }

    public void setCustomerIdCardName(String customerIdCardName) {
        this.customerIdCardName = customerIdCardName;
    }

    public String getCustomerIdCardNumber() {
        if (StringUtils.isEmpty(customerIdCardNumber)) {
            return "";
        }
        return customerIdCardNumber;
    }

    public void setCustomerIdCardNumber(String customerIdCardNumber) {
        this.customerIdCardNumber = customerIdCardNumber;
    }

    public String getCustomerTown() {
        if (StringUtils.isEmpty(customerTown)) {
            return "";
        }
        return customerTown;
    }

    public void setCustomerTown(String customerTown) {
        this.customerTown = customerTown;
    }

    public String getCustomerTel() {
        if (StringUtils.isEmpty(customerTel)) {
            return "";
        }
        return customerTel;
    }

    public void setCustomerTel(String customerTel) {
        this.customerTel = customerTel;
    }

    public String getCustomerCountry() {
        if (customerCountry == null || "".equals(customerCountry)) {
            return "中国";
        }
        return customerCountry;
    }

    public void setCustomerCountry(String customerCountry) {
        this.customerCountry = customerCountry;
    }

    public boolean isNeedPlatformDecrypt() {
        return needPlatformDecrypt;
    }

    public void setNeedPlatformDecrypt(boolean needPlatformDecrypt) {
        this.needPlatformDecrypt = needPlatformDecrypt;
    }

    public String getTradeId() {
        if (StringUtils.isEmpty(tradeId)) {
            return "";
        }
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getUniqueMark() {
        if (StringUtils.isEmpty(uniqueMark)) {
            uniqueMark = "";
        }
        return uniqueMark;
    }

    /**
     * 买家信息唯一标识，用于计算订单是否变更
     *
     * @return
     */
    public String getBuyerInfoUniqueId() {
        return DigestUtils.md5Hex(String.format("%s-%s-%s-%s-%s-%s",
                customerMobile, customerTel, customerAddress,
                receiver, customerIdCardNumber, customerShopAccount));
    }

    public void setUniqueMark(String uniqueMark) {
        this.uniqueMark = uniqueMark;
    }

    public String getPlatformOpenId() {
        return platformOpenId;
    }

    public void setPlatformOpenId(String platformOpenId) {
        this.platformOpenId = platformOpenId;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getTownCode() {
        return townCode;
    }

    public void setTownCode(String townCode) {
        this.townCode = townCode;
    }

    public String getRealPlatformBuyerId() {
        return realPlatformBuyerId;
    }

    public void setRealPlatformBuyerId(String realPlatformBuyerId) {
        this.realPlatformBuyerId = realPlatformBuyerId;
    }

    public String getRealPlatformBuyerNick() {
        return realPlatformBuyerNick;
    }

    public void setRealPlatformBuyerNick(String realPlatformBuyerNick) {
        this.realPlatformBuyerNick = realPlatformBuyerNick;
    }


    public String getPlatformOpenId2() {
        return platformOpenId2;
    }

    public void setPlatformOpenId2(String platformOpenId2) {
        this.platformOpenId2 = platformOpenId2;
    }
}
