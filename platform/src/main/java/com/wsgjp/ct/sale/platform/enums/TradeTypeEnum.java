package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum TradeTypeEnum implements CodeEnum {

    /**
     * 普通交易
     */
    NORMAL(0,"普通"),

    /**
     * 预售-按计划发
     *
     */
    ADVANCE_FORWARD_SALE(1,"预售-按计划发"),

    /**
     * 预售-有货就发
     *
     */
    ADVANCE_FORWARD_SALE_BY_STOCK(2,"预售-有货就发"),

    /**
     * 周期购
     */
    CYCLE_PURCHASE(3,"周期购"),

    /**
     * 团购
     */
    GROUP_PURCHASE(4,"团购"),

    /**
     * 虚拟服务
     */
    VIRTUAL_SERVICE(5,"虚拟服务"),
    /**
     *送礼物订单
     */
    VIRTUAL_GIFT_MAIN(6,"送礼物订单"),
    /**
     *收礼物订单
     */
    GIFT_SUB(7,"收礼物订单");


    private int flag;

    private String name;

    TradeTypeEnum(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
