package com.wsgjp.ct.sale.platform.dto.distributor;

import java.math.BigDecimal;
import java.math.BigInteger;

public class ProductPriceInfo {

    private BigDecimal distributorPrice;
    //必填
    private BigDecimal retailPrice;
    private String ptypeId;
    private BigInteger skuid;
    private String skuCode;
    private String properties;
    private String unitId;
    private String unitName;
    private Integer skuPrice;

    public Integer getSkuPrice() {
        return skuPrice;
    }

    public void setSkuPrice(Integer skuPrice) {
        this.skuPrice = skuPrice;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getDistributorPrice() {
        return distributorPrice;
    }

    public void setDistributorPrice(BigDecimal distributorPrice) {
        this.distributorPrice = distributorPrice;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public String getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(String ptypeId) {
        this.ptypeId = ptypeId;
    }

    public BigInteger getSkuid() {
        return skuid;
    }

    public void setSkuid(BigInteger skuid) {
        this.skuid = skuid;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }
}
