package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PickupShippingMarkSizeEnum implements CodeEnum {

    /**
     * 1. A4，打印货品明细；2. 100 * 100尺寸, 不包含货品明细。
     * */

    A4(1,"A4"),
    SIZE_100_100(2,"SIZE_100_100");


    private int code;
    private String name;

    PickupShippingMarkSizeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
