package com.wsgjp.ct.sale.platform.dto.product;

/**
 * <AUTHOR>
 */
public class SaleAttrValueTemplateValue {
    /**
     * 排序
     */
    private Integer orderSort;
    /**
     * 色卡模版标准值父ID
     */
    private String fatherTemplateId;
    /**
     * 色卡模版标准值父级名称
     */
    private String fatherTemplateName;
    /**
     * 标准值模版ID
     */
    private String templateStandardId;

    /**
     * 若为类目属性类型此处为属性名字
     */
    private String name;

    /**
     * 若为类目属性类型此处为属性值id
     */
    private String id;
    /**
     * 属性值
     */
    private String value;
    /**
     * 如果值包含颜色复制到该字段
     * 颜色 #FFFFFF
     */
    private String color;


    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }

    public String getFatherTemplateId() {
        return fatherTemplateId;
    }

    public void setFatherTemplateId(String fatherTemplateId) {
        this.fatherTemplateId = fatherTemplateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTemplateStandardId() {
        return templateStandardId;
    }

    public void setTemplateStandardId(String templateStandardId) {
        this.templateStandardId = templateStandardId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getFatherTemplateName() {
        return fatherTemplateName;
    }

    public void setFatherTemplateName(String fatherTemplateName) {
        this.fatherTemplateName = fatherTemplateName;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}
