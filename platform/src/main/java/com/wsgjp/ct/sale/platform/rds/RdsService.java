package com.wsgjp.ct.sale.platform.rds;

import com.taobao.api.domain.Item;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.product.EshopProductEntity;
import com.wsgjp.ct.sale.platform.dto.refund.EshopRefundOrderEntity;
import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.slice.DownloadSlice;

import java.util.List;
import java.util.Map;

public interface RdsService {
    ShopType[] supportedShopTypes();

    SliceDownloadResponse<EshopOrderEntity> downloadOrderByCreateTime(DownloadSlice slice, RdsParams params);

    SliceDownloadResponse<EshopOrderEntity> downloadOrderByUpdateTime(DownloadSlice slice, RdsParams params);

    /**
     * 检查RDS是否可用
     * @param rdsName rdsName
     * @param nickName nickName
     * @return 是否可用
     */
    boolean checkRdsEnabled(String rdsName, String nickName);

    /**
     * 根据单号查询订单列表
     * @param tradeIds 订单号列表
     * @param params rds参数
     * @return 订单列表
     */
    List<EshopOrderEntity> downloadOrderById(List<String> tradeIds, RdsParams params);

    SliceDownloadResponse<EshopRefundOrderEntity> downloadRefundByCreateTime(DownloadSlice slice, RdsParams params);

    SliceDownloadResponse<EshopRefundOrderEntity> downloadRefundByUpdateTime(DownloadSlice slice, RdsParams params);

    /**
     * 跟进售后单号查询售后单列表
     * @param params rds参数
     * @param refundIds 售后单号
     * @return 售后列表
     */
    List<EshopRefundOrderEntity> downloadRefundByRefundId(RdsParams params, List<String> refundIds);

    /**
     * 根据原始订单号查询售后单列表
     * @param params rds参数
     * @param tradeIds 原始订单号
     * @return 售后列表
     */
    List<EshopRefundOrderEntity> downloadRefundByTradeId(RdsParams params, List<String> tradeIds);


    SliceDownloadResponse<EshopProductEntity> downloadProductByCreateTime(DownloadSlice slice, RdsParams params);

    SliceDownloadResponse<EshopProductEntity> downloadAllProducts(DownloadSlice slice, RdsParams params);

    SliceDownloadResponse<EshopProductEntity> downloadProductByUpdateTime(DownloadSlice slice, RdsParams params);

    int queryRdsOrderCount(DownloadSlice slice, RdsParams params);

    List<EshopProductEntity> downloadProductsByNumIds(RdsParams params, List<String> numIds);

    Map<String, Item> downloadItemsByNumIds(RdsParams params, List<String> numIds);
}
