package com.wsgjp.ct.sale.platform.log;

import com.wsgjp.ct.support.log.annotation.LogEntity;
import com.wsgjp.ct.support.log.entity.BaseLog;
import com.wsgjp.ct.support.log.type.DBType;

import java.math.BigInteger;

/**
 * <AUTHOR> 2023/12/6 10:30
 */
@LogEntity(tableName = "pl_eshop_api_log", dbType = DBType.LOG)
public class PlatformApiLog extends BaseLog {
    private BigInteger eshopId;
    private int eshopType;
    private String method;
    private String request;
    private String response;
    private int status;

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public int getEshopType() {
        return eshopType;
    }

    public void setEshopType(int eshopType) {
        this.eshopType = eshopType;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
