package com.wsgjp.ct.sale.platform.enums;

/**
 * <AUTHOR>
 * @date 2019-12-17 15:29
 */
public enum Sex {
    /**
     * 男
     */
    Man(0, "男"),
    /**
     * 女
     */
    Woman(1, "女");

    private int flag;

    private String name;

    Sex(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }
}
