package com.wsgjp.ct.sale.platform.dto.plugin;

import com.wsgjp.ct.sale.platform.enums.EshopBusinessConfigTypeEnum;
import com.wsgjp.ct.sale.platform.enums.OperationTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel("店铺业务设置")
public class EshopBusinessConfig {
    @ApiModelProperty("业务设置key")
    private String key;
    @ApiModelProperty("业务设置描述")
    private String description;
    @ApiModelProperty("业务配置枚举类型")
    private EshopBusinessConfigTypeEnum businessConfigType;
    @ApiModelProperty("业务配置操作枚举类型")
    private OperationTypeEnum operationType;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public EshopBusinessConfigTypeEnum getBusinessConfigType() {
        return businessConfigType;
    }

    public void setBusinessConfigType(EshopBusinessConfigTypeEnum businessConfigType) {
        this.businessConfigType = businessConfigType;
    }

    public OperationTypeEnum getOperationType() {
        return operationType;
    }

    public void setOperationType(OperationTypeEnum operationType) {
        this.operationType = operationType;
    }
}
