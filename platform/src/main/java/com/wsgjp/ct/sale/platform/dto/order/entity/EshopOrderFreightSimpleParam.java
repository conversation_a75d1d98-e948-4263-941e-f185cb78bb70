package com.wsgjp.ct.sale.platform.dto.order.entity;


import java.math.BigInteger;
import java.util.List;

public class EshopOrderFreightSimpleParam {
    private BigInteger otypeId;
    private List<String> tradeOrderList;
    private BigInteger profileId;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public List<String> getTradeOrderList() {
        return tradeOrderList;
    }

    public void setTradeOrderList(List<String> tradeOrderList) {
        this.tradeOrderList = tradeOrderList;
    }
}
