package com.wsgjp.ct.sale.platform.dto.token;

import com.wsgjp.ct.sale.platform.enums.ControllerTypeEnum;
import com.wsgjp.ct.sale.platform.enums.PlatformControllerDataEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-27 16:58
 */
@ApiModel("店铺字段信息")
public class EshopFieldInfo {
    public EshopFieldInfo(){}
    public EshopFieldInfo(String field,ControllerTypeEnum controllerType,boolean required,String title,String description){
        this.field=field;
        this.controllerType=controllerType;
        this.required=required;
        this.title=title;
        this.description = description;
    }
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("字段名称")
    private String field;
    @ApiModelProperty("介绍")
    private String description;
    @ApiModelProperty("介绍样式")
    private String descriptionStyle;
    @ApiModelProperty("默认值")
    private String defaultValue;
    @ApiModelProperty("控件类型")
    private ControllerTypeEnum controllerType;
    @ApiModelProperty("提示文本")
    private String hintText;
    @ApiModelProperty("下拉列表值")
    private List<Dropdown> dropdownDataSource;
    @ApiModelProperty("配置数据来源")
    private PlatformControllerDataEnum dataEnum;
    @ApiModelProperty("是否必填")
    private boolean required;

    public PlatformControllerDataEnum getDataType() {
        if (dataEnum == null) {
            dataEnum = PlatformControllerDataEnum.ESHOP;
        }
        return dataEnum;
    }

    public void setDataType(PlatformControllerDataEnum dataType) {
        this.dataEnum = dataType;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public static class Dropdown {
        private String text;
        private String value;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescriptionStyle() {
        return descriptionStyle;
    }

    public void setDescriptionStyle(String descriptionStyle) {
        this.descriptionStyle = descriptionStyle;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public ControllerTypeEnum getControllerType() {
        return controllerType;
    }

    public void setControllerType(ControllerTypeEnum controllerType) {
        this.controllerType = controllerType;
    }

    public String getHintText() {
        return hintText;
    }

    public void setHintText(String hintText) {
        this.hintText = hintText;
    }

    public List<Dropdown> getDropdownDataSource() {
        return dropdownDataSource;
    }

    public void setDropdownDataSource(List<Dropdown> dropdownDataSource) {
        this.dropdownDataSource = dropdownDataSource;
    }
}
