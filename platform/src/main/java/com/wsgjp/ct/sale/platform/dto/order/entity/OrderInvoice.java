package com.wsgjp.ct.sale.platform.dto.order.entity;


import com.wsgjp.ct.sale.platform.enums.InvoiceCategory;
import com.wsgjp.ct.sale.platform.enums.InvoiceState;
import com.wsgjp.ct.sale.platform.enums.InvoiceType;
import ngp.utils.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020-01-02 10:27
 */
public class OrderInvoice {
    /**
     * 是否需要开发票
     */
    private boolean neeInvoice;
    /**
     * 发票类型：个人/企业
     */
    private InvoiceType invoiceType;
    /**
     * 发票类目
     */
    private InvoiceCategory invoiceCategory;
    /**
     * 发票金额
     */
    private BigDecimal invoiceTotal;
    /**
     * 开发票状态
     */
    private InvoiceState invoiceState;
    private String invoiceTitle;
    /**
     *
     */
    private String invoiceCode;
    private String invoiceCompany;
    private String invoiceRegisterAddr;
    private String invoiceRegisterPhone;
    /**
     * 开发票的银行
     */
    private String invoiceBank;
    /**
     * 银行账号
     */
    private String invoiceBankAccount;

    private String invoiceRemark;

    /**
     * 商品型号
     */
    private String commodityModel;

    public String getCommodityModel() {
        return commodityModel;
    }

    public void setCommodityModel(String commodityModel) {
        this.commodityModel = commodityModel;
    }

    public String getInvoiceRemark() {
        return invoiceRemark;
    }

    public void setInvoiceRemark(String invoiceRemark) {
        this.invoiceRemark = invoiceRemark;
    }

    public boolean isNeeInvoice() {
        return neeInvoice;
    }

    public void setNeeInvoice(boolean neeInvoice) {
        this.neeInvoice = neeInvoice;
    }

    public InvoiceType getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(InvoiceType invoiceType) {
        this.invoiceType = invoiceType;
    }

    public BigDecimal getInvoiceTotal() {
        return invoiceTotal;
    }

    public void setInvoiceTotal(BigDecimal invoiceTotal) {
        this.invoiceTotal = invoiceTotal;
    }

    public InvoiceState getInvoiceState() {
        return invoiceState;
    }

    public void setInvoiceState(InvoiceState invoiceState) {
        this.invoiceState = invoiceState;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public InvoiceCategory getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(InvoiceCategory invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }
    public String getInvoiceCompany() {
        if (StringUtils.isEmpty(invoiceCompany))
        {
            return "";
        }
        return invoiceCompany;
    }

    public void setInvoiceCompany(String invoiceCompany) {
        this.invoiceCompany = invoiceCompany;
    }

    public String getInvoiceRegisterAddr() {
        if (StringUtils.isEmpty(invoiceRegisterAddr))
        {
            return "";
        }
        return invoiceRegisterAddr;
    }

    public void setInvoiceRegisterAddr(String invoiceRegisteraddr) {
        this.invoiceRegisterAddr = invoiceRegisteraddr;
    }

    public String getInvoiceRegisterPhone() {
        if (StringUtils.isEmpty(invoiceRegisterPhone))
        {
            return "";
        }
        return invoiceRegisterPhone;
    }

    public void setInvoiceRegisterPhone(String invoiceRegisterPhone) {
        this.invoiceRegisterPhone = invoiceRegisterPhone;
    }

    public String getInvoiceBank() {
        if (StringUtils.isEmpty(invoiceBank))
        {
            return "";
        }
        return invoiceBank;
    }

    public void setInvoiceBank(String invoiceBank) {
        this.invoiceBank = invoiceBank;
    }

    public String getInvoiceBankAccount() {
        if (StringUtils.isEmpty(invoiceBankAccount))
        {
            return "";
        }
        return invoiceBankAccount;
    }

    public void setInvoiceBankAccount(String invoiceBankAccount) {
        this.invoiceBankAccount = invoiceBankAccount;
    }
}
