package com.wsgjp.ct.sale.platform.dto.sendgoods;

import com.wsgjp.ct.sale.platform.entity.entities.BillSerialnoSon;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.BatchInfo;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FreightBillNoSyncDetailEntity {
    private String tradeId;
    private String refundId;
    private String oid;
    private String platformPtypeId;
    private String platformSkuId;
    private String platformPtypeXcode;
    /**
     * 商品名称 药帮忙必传
     */
    private String platformPtypeName;
    private BigDecimal qty;
    private BigDecimal deliveredQty;
    /**
     * 拆套餐的话是发货单总数量
     */
    private BigDecimal totalQty;
    /**
     * 原单数量
     */
    private BigDecimal originalQty;
    /**
     * 基本单位数量
     */
    private BigDecimal unitQuantity;

    /**
     * 单位换算率
     */
    private BigDecimal unitRate;
    /**
     * 单位名称
     */
    private String unitName;
    private BigInteger deliverOrderId;
    private BigInteger deliverOrderDetailId;
    private BigInteger warehouseTaskId;
    private BigInteger warehouseTaskDetailId;
    private String platformJson;
    private boolean gift;
    private boolean lastOne;

    private boolean onlyModifyStatus;

    private boolean refund;

    //明细是否部分发货，=》快手平台内部计算使用。
    private boolean isPartialSend;

    /**
     * 计算后为可追加发货
     */
    private boolean isAppend;


    //没有使用了，请使用platformCodeList

    @Deprecated
    private List<FreightBillNoSyncSnEntity> snList;

    /**
     * 业务组/仓储组传递的原始序列号等编码实体
     * 没有使用了，请使用platformCodeList
     */
    private List<BillSerialnoSon> serialNoSonList;
    /**
     * 商品材质类型
     */
    private String productMaterialType;


    public String getProductMaterialType() {
        return productMaterialType;
    }

    public void setProductMaterialType(String productMaterialType) {
        this.productMaterialType = productMaterialType;
    }

    public BigDecimal getOriginalQty() {
        if (null == originalQty) {
            originalQty = BigDecimal.ZERO;
        }
        return originalQty;
    }

    public void setOriginalQty(BigDecimal originalQty) {
        this.originalQty = originalQty;
    }

    public List<BillSerialnoSon> getSerialNoSonList() {
        return serialNoSonList;
    }

    public void setSerialNoSonList(List<BillSerialnoSon> serialNoSonList) {
        this.serialNoSonList = serialNoSonList;
    }

    /**
     * 平台编码实体列表(包含序列号、imei码、IccId码、拆封码等)。由serialNoList在通用层转换而来。供平台接口组使用
     */
    private List<FreightBillNoSyncPlatformCodeEntity> platformCodeList;

    private boolean combo;
    private BigInteger comboRowId;
    private BigInteger comboRowParId;
    private boolean deleted;
    private boolean manual;
    /**
     * 明细里的商家编码
     */
    private String xcode;
    /**
     * 批号
     */
    private List<BatchInfo> batchInfos;

    private BigDecimal price;

    private String platformFreightId;
        /**
     * 质检信息--发货物流公司
     */
    private String platformQualityBtypeCode;
    /**
     * 质检信息--发货物流公司-物流产品
     */
    private String platformQualityBtypeProduct;
    /**
     * 质检信息--保价金额
     */
    private String platformQualityBtypeInsureTotal;

    /**
     * 69码也就是条码
     */
    private List<String> fullBarcode;

    public List<String> getFullBarcode() {
        return fullBarcode;
    }

    public void setFullBarcode(List<String> fullBarcode) {
        this.fullBarcode = fullBarcode;
    }
    /**
     * 质检信息--售后拦截
     */
    private String platformQualityRefundInterceptionCode;

    public String getPlatformQualityBtypeCode() {
        return platformQualityBtypeCode;
    }

    public void setPlatformQualityBtypeCode(String platformQualityBtypeCode) {
        this.platformQualityBtypeCode = platformQualityBtypeCode;
    }

    public String getPlatformQualityBtypeProduct() {
        return platformQualityBtypeProduct;
    }

    public void setPlatformQualityBtypeProduct(String platformQualityBtypeProduct) {
        this.platformQualityBtypeProduct = platformQualityBtypeProduct;
    }

    public String getPlatformQualityBtypeInsureTotal() {
        return platformQualityBtypeInsureTotal;
    }

    public void setPlatformQualityBtypeInsureTotal(String platformQualityBtypeInsureTotal) {
        this.platformQualityBtypeInsureTotal = platformQualityBtypeInsureTotal;
    }

    public String getPlatformQualityRefundInterceptionCode() {
        return platformQualityRefundInterceptionCode;
    }

    public void setPlatformQualityRefundInterceptionCode(String platformQualityRefundInterceptionCode) {
        this.platformQualityRefundInterceptionCode = platformQualityRefundInterceptionCode;
    }

    public BigDecimal getUnitQuantity() {
        return unitQuantity;
    }

    public void setUnitQuantity(BigDecimal unitQuantity) {
        this.unitQuantity = unitQuantity;
    }

    public BigDecimal getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(BigDecimal unitRate) {
        this.unitRate = unitRate;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public boolean isRefund() {
        return refund;
    }

    public void setRefund(boolean refund) {
        this.refund = refund;
    }

    public String getPlatformFreightId() {
        return platformFreightId;
    }

    public void setPlatformFreightId(String platformFreightId) {
        this.platformFreightId = platformFreightId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public List<BatchInfo> getBatchInfos() {
        return batchInfos;
    }

    public void setBatchInfos(List<BatchInfo> batchInfos) {
        this.batchInfos = batchInfos;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getPlatformPtypeId() {
        return platformPtypeId;
    }

    public void setPlatformPtypeId(String platformPtypeId) {
        this.platformPtypeId = platformPtypeId;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getPlatformPtypeXcode() {
        return platformPtypeXcode;
    }

    public void setPlatformPtypeXcode(String platformPtypeXcode) {
        this.platformPtypeXcode = platformPtypeXcode;
    }

    public BigInteger getDeliverOrderId() {
        return deliverOrderId;
    }

    public void setDeliverOrderId(BigInteger deliverOrderId) {
        this.deliverOrderId = deliverOrderId;
    }

    public BigInteger getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(BigInteger warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }

    public BigInteger getWarehouseTaskDetailId() {
        return warehouseTaskDetailId;
    }

    public void setWarehouseTaskDetailId(BigInteger warehouseTaskDetailId) {
        this.warehouseTaskDetailId = warehouseTaskDetailId;
    }

    public boolean isGift() {
        return gift;
    }

    public void setGift(boolean gift) {
        this.gift = gift;
    }

    //没有使用了，请使用platformCodeList
    public List<FreightBillNoSyncSnEntity> getSnList() {
        return snList;
    }

    public void setSnList(List<FreightBillNoSyncSnEntity> snList) {
        this.snList = snList;
    }

    public boolean isCombo() {
        return combo;
    }

    public void setCombo(boolean combo) {
        this.combo = combo;
    }

    public BigInteger getComboRowId() {
        return comboRowId;
    }

    public void setComboRowId(BigInteger comboRowId) {
        this.comboRowId = comboRowId;
    }

    public BigInteger getComboRowParId() {
        return comboRowParId;
    }

    public void setComboRowParId(BigInteger comboRowParId) {
        this.comboRowParId = comboRowParId;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public BigDecimal getQty() {
        if (null == qty) {
            qty = BigDecimal.ZERO;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getDeliveredQty() {
        if (null == deliveredQty) {
            deliveredQty = BigDecimal.ZERO;
        }
        return deliveredQty;
    }

    public void setDeliveredQty(BigDecimal deliveredQty) {
        this.deliveredQty = deliveredQty;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public String getPlatformJson() {
        return platformJson;
    }

    public void setPlatformJson(String platformJson) {
        this.platformJson = platformJson;
    }

    public BigInteger getDeliverOrderDetailId() {
        return deliverOrderDetailId;
    }

    public void setDeliverOrderDetailId(BigInteger deliverOrderDetailId) {
        this.deliverOrderDetailId = deliverOrderDetailId;
    }

    public boolean isManual() {
        return manual;
    }

    public void setManual(boolean manual) {
        this.manual = manual;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public List<FreightBillNoSyncPlatformCodeEntity> getPlatformCodeList() {
        return platformCodeList;
    }

    public void setPlatformCodeList(List<FreightBillNoSyncPlatformCodeEntity> platformCodeList) {
        this.platformCodeList = platformCodeList;
    }

    public boolean isLastOne() {
        return lastOne;
    }

    public void setLastOne(boolean lastOne) {
        this.lastOne = lastOne;
    }

    public String getPlatformPtypeName() {
        return platformPtypeName;
    }

    public boolean isOnlyModifyStatus() {
        return onlyModifyStatus;
    }

    public void setPlatformPtypeName(String platformPtypeName) {
        this.platformPtypeName = platformPtypeName;
    }

    public void setOnlyModifyStatus(boolean onlyModifyStatus) {
        this.onlyModifyStatus = onlyModifyStatus;
    }

    public boolean isPartialSend() {
        return isPartialSend;
    }

    public void setPartialSend(boolean partialSend) {
        isPartialSend = partialSend;
    }

    public boolean isAppend() {
        return isAppend;
    }

    public void setAppend(boolean append) {
        isAppend = append;
    }
}
