package com.wsgjp.ct.sale.platform.dto.sendgoods;

/**
 * 平台编码实体(包含序列号、imei码、IccId码、拆封码等)
 */
public class FreightBillNoSyncPlatformCodeEntity {
    /**
     * 序列号
     */
    private String serialNo;
    /**
     * IMEI码
     */
    private String imei;
    /**
     * ICCID码
     */
    private String iccId;
    /**
     * 拆封码
     */
    private String sealVerifyNo;

    /**
     * 要先判断明细是否是套餐，才用此接口判断序列号
     */
    private boolean mainPtype;


    public boolean isMainPtype() {
        return mainPtype;
    }

    public void setMainPtype(boolean mainPtype) {
        this.mainPtype = mainPtype;
    }

    public FreightBillNoSyncPlatformCodeEntity() {
    }

    public FreightBillNoSyncPlatformCodeEntity(String serialNo, String imei, String iccId, String sealVerifyNo) {
        this.serialNo = serialNo;
        this.imei = imei;
        this.iccId = iccId;
        this.sealVerifyNo = sealVerifyNo;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getIccId() {
        return iccId;
    }

    public void setIccId(String iccId) {
        this.iccId = iccId;
    }

    public String getSealVerifyNo() {
        return sealVerifyNo;
    }

    public void setSealVerifyNo(String sealVerifyNo) {
        this.sealVerifyNo = sealVerifyNo;
    }
}
