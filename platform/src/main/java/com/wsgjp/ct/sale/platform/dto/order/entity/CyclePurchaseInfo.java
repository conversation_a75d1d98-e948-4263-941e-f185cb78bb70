package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.common.enums.core.entity.MarkData;

import java.util.ArrayList;
import java.util.List;

public class CyclePurchaseInfo {
    /**
     * 总周期数
     */
    private int totalPeriodNum;

    private String tradeOrderId;

    private List<CyclePurchaseSubOrder> cyclePurchaseSubOrders;

    private List<MarkData> markDataList;

    public int getTotalPeriodNum() {
        return totalPeriodNum;
    }

    public void setTotalPeriodNum(int totalPeriodNum) {
        this.totalPeriodNum = totalPeriodNum;
    }

    public List<CyclePurchaseSubOrder> getCyclePurchaseSubOrders() {
        if (cyclePurchaseSubOrders == null) {
            cyclePurchaseSubOrders = new ArrayList<>();
        }
        return cyclePurchaseSubOrders;
    }

    public void setCyclePurchaseSubOrders(List<CyclePurchaseSubOrder> cyclePurchaseSubOrders) {
        this.cyclePurchaseSubOrders = cyclePurchaseSubOrders;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public List<MarkData> getMarkDataList() {
        if (markDataList == null) {
            markDataList = new ArrayList<>();
        }
        return markDataList;
    }

    public void setMarkDataList(List<MarkData> markDataList) {
        this.markDataList = markDataList;
    }
}
