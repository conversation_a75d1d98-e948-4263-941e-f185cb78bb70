package com.wsgjp.ct.sale.platform.dto.refund;

import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import com.wsgjp.ct.sale.platform.dto.order.entity.ReceiverInfo;
import com.wsgjp.ct.sale.platform.dto.refund.entity.RefundFreight;
import com.wsgjp.ct.sale.platform.dto.refund.enums.*;
import com.wsgjp.ct.sale.platform.enums.*;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import ngp.utils.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * 平台售后单实体
 */
@ApiModel("平台售后单实体")
public class EshopRefundOrderEntity extends ErpBaseInfo {

    public EshopRefundOrderEntity() {
    }

    public EshopRefundOrderEntity(EshopSystemParams systemParams) {
        setShopType(systemParams.getShopType());
        setProfileId(systemParams.getProfileId());
        setEshopId(systemParams.geteShopId());
    }

    public EshopRefundOrderEntity(String refundId, String tradeId, String exchangeTradeId, double refundFee, Boolean hasGoodReturn, String reason, String description, String refundVersion, String payNo, String buyerNick, String sellerNick, double totalFee, String payment, double refundFreightFee, RefundPhase refundPhase, TradeStatus tradeStatus, RefundStatus refundStatus, RefundGoodsStatus goodsStatus, RefundTypeEnum refundTypeEnum, Date created, Date modified, Date goodReturnTime, List<RefundFreight> refundFreights, List<EshopRefundDetailEntity> refundDetails, List<EshopExchangeDetailEntity> exchangeDetails, ReceiverInfo receiverInfo, RefundSpeedType refundSpeedType, boolean existLocal, BusinessType businessType, BigDecimal serviceFee, String sellerMemo, Date processLatestTime, RefundProcessStatus processStatus, boolean delivered, PlatformChangeState platformChangeState, PlatformConfirmState platformConfirmState, PlatformRefundState platformRefundState, PlatformReturnState platformReturnState, List<BaseOrderMarkEnum> refundMarks, List<MarkData> markDataList, Date refundFinishTime, Date refundSuccessTime, List<RefundInterceptEntity> refundInterceptList, String interceptInvestor, Boolean platformAutoInterceptAgree) {
        this.refundId = refundId;
        this.tradeId = tradeId;
        this.exchangeTradeId = exchangeTradeId;
        this.refundFee = refundFee;
        this.hasGoodReturn = hasGoodReturn;
        this.reason = reason;
        this.description = description;
        this.refundVersion = refundVersion;
        this.payNo = payNo;
        this.buyerNick = buyerNick;
        this.sellerNick = sellerNick;
        this.totalFee = totalFee;
        this.payment = payment;
        this.refundFreightFee = refundFreightFee;
        this.refundPhase = refundPhase;
        this.tradeStatus = tradeStatus;
        this.refundStatus = refundStatus;
        this.goodsStatus = goodsStatus;
        this.refundTypeEnum = refundTypeEnum;
        this.created = created;
        this.modified = modified;
        this.goodReturnTime = goodReturnTime;
        this.refundFreights = refundFreights;
        this.refundDetails = refundDetails;
        this.exchangeDetails = exchangeDetails;
        this.receiverInfo = receiverInfo;
        this.refundSpeedType = refundSpeedType;
        this.existLocal = existLocal;
        this.businessType = businessType;
        this.serviceFee = serviceFee;
        this.sellerMemo = sellerMemo;
        this.processLatestTime = processLatestTime;
        this.processStatus = processStatus;
        this.delivered = delivered;
        this.platformChangeState = platformChangeState;
        this.platformConfirmState = platformConfirmState;
        this.platformRefundState = platformRefundState;
        this.platformReturnState = platformReturnState;
        this.refundMarks = refundMarks;
        this.markDataList = markDataList;
        this.refundFinishTime = refundFinishTime;
        this.refundSuccessTime = refundSuccessTime;
        this.refundInterceptList = refundInterceptList;
        this.interceptInvestor = interceptInvestor;
        this.platformAutoInterceptAgree = platformAutoInterceptAgree;
    }

    /**
     * 售后单号，必填
     */
    @ApiModelProperty("售后单号")
    private String refundId;
    /**
     * 订单号，必填
     */
    @ApiModelProperty("订单号")
    private String tradeId;

    /**
     * 5.5平台业务才做目前暂时没用
     */
    @ApiModelProperty("换货订单号")
    private String exchangeTradeId;

    /**
     * 退还金额(退还给买家的金额),必填
     */
    @ApiModelProperty("买家申请退款金额")
    private double refundFee;
    /**
     * 是否退还商品
     */
    @ApiModelProperty("是否退还商品")
    private Boolean hasGoodReturn;

    /**
     * 售后原因，可选
     */
    @ApiModelProperty("售后原因")
    private String reason;
    /**
     * 售后描述，可选
     */
    @ApiModelProperty("售后描述")
    private String description;
    /**
     * 售后版本号,可选
     */
    @ApiModelProperty("售后版本号")
    private String refundVersion;
    /**
     * 支付单号
     */
    @ApiModelProperty("支付单号")
    private String payNo;
    /**
     * 买家昵称，淘宝天猫用于解密。
     */
    @ApiModelProperty("买家昵称")
    private String buyerNick;
    /**
     * 卖家昵称，可不填
     */
    @ApiModelProperty("卖家昵称")
    private String sellerNick;
    /**
     * 交易总金额
     */
    @ApiModelProperty("交易总金额")
    private double totalFee;
    /**
     * 买家支付金额
     */
    @ApiModelProperty("买家支付金额")
    private String payment;
    /**
     * 运费退款金额
     */
    @ApiModelProperty("运费退款金额")
    private double refundFreightFee;
    /**
     * 售后阶段：售中(未发货)/售后(已发货)- 不是必填字段
     */
    @ApiModelProperty("售后阶段")
    private RefundPhase refundPhase;

    /**
     * 平台售后阶段
     * 有些平台售后阶段定义与ERP不同,比如淘宝售中=未发货+已发货但未收到货
     */
    @Getter
    @ApiModelProperty("平台售后")
    private RefundPhase platformRefundPhase;

    /**
     * 交易状态
     */
    @ApiModelProperty("交易状态")
    private TradeStatus tradeStatus;
    /**
     * 售后状态
     */
    @ApiModelProperty("售后状态")
    private RefundStatus refundStatus;
    /**
     * 售后商品状态
     */
    @ApiModelProperty("售后商品状态")
    private RefundGoodsStatus goodsStatus;
    /**
     * 售后类型
     */
    @ApiModelProperty("售后类型")
    private RefundTypeEnum refundTypeEnum;

    @ApiModelProperty("支付类型")
    private RefundPayTypeEnum refundPayTypeEnum;
    /**
     * 售后单创建时间
     */
    @ApiModelProperty("售后单创建时间")
    private Date created;
    /**
     * 售后单修改时间
     */
    @ApiModelProperty("售后单修改时间")
    private Date modified;
    /**
     * 商品退回时间
     */
    @ApiModelProperty("商品退回时间")
    private Date goodReturnTime;
    /**
     * 售后物流信息
     */
    @ApiModelProperty("售后物流信息")
    private List<RefundFreight> refundFreights;
    /**
     * 售后详情信息
     */
    @ApiModelProperty("售后详情信息")
    private List<EshopRefundDetailEntity> refundDetails;
    /**
     * 换货商品信息()
     */
    @ApiModelProperty("换货商品信息")
    private List<EshopExchangeDetailEntity> exchangeDetails;

    /**
     * 售后单收货人信息
     */
    @ApiModelProperty("售后单收货人信息")
    private ReceiverInfo receiverInfo;

    /**
     * 售后单退款类型
     */
    @ApiModelProperty("售后单退款类型")
    private RefundSpeedType refundSpeedType;

    /**
     * todo 存在本地(接口这边不需要赋值)
     */
    @ApiModelProperty("是否存在本地")
    private boolean existLocal;

    /**
     * 售后单业务类型
     */
    @ApiModelProperty("售后单业务类型")
    private BusinessType businessType;
    /**
     * 退款的服务费
     */
    private BigDecimal serviceFee;


    /**
     * 卖家备注
     */
    private String sellerMemo;

    private Date processLatestTime;

    private RefundProcessStatus processStatus;

    private boolean delivered;
    //换货确认状态
    private PlatformChangeState platformChangeState;
    //审核状态
    private PlatformConfirmState platformConfirmState;
    //退款确认
    private PlatformRefundState platformRefundState;
    //退货确认
    private PlatformReturnState platformReturnState;

    /**
     * 退款的服务费
     */

    /**
     * 售后提醒,已废弃
     */
    @ApiModelProperty("售后提醒")
    private List<BaseOrderMarkEnum> refundMarks;

    /**
     * 订单提醒-携带标记数据
     */
    @ApiModelProperty("售后提醒")
    private List<MarkData> markDataList;


    /**
     * 售后完成时间，如果平台返回了，则直接用，如果平台没返回，则以第一次返回售后完成/关闭的时间作为售后完成时间
     */
    private Date refundFinishTime;

    /**
     * 售后退款成功时间
     */
    private Date refundSuccessTime;


    /**
     * 售后单拦截信息
     */
    @ApiModelProperty("售后单拦截信息")
    private List<RefundInterceptEntity> refundInterceptList;

    @ApiModelProperty("快递拦截的出资方")
    private String interceptInvestor;

    /**
     * 平台自动拦截是否同意
     */
    @ApiModelProperty("平台自动拦截是否同意")
    private Boolean platformAutoInterceptAgree;
    /**
     * 售后单标记
     * 仅接口层使用
     */
    private int refundTag;

    private PlatformSignState platformSignState;
    /**
     * 第三方平台的售后状态
     */
    private String onlineRefundStatus;
    /**
     * 第三方平台的售后类型
     */
    private String onlineRefundType;

    private List<Integer> salePeriodNums;

    // 退平台优惠金额
    private BigDecimal refundPlatformAmount;
    // 退国家补贴金额
    private BigDecimal refundNationalSubsidyTotal;

    public BigDecimal getRefundPlatformAmount() {
        if (refundPlatformAmount == null) {
            return BigDecimal.ZERO;
        }
        return refundPlatformAmount;
    }

    public void setRefundPlatformAmount(BigDecimal refundPlatformAmount) {
        this.refundPlatformAmount = refundPlatformAmount;
    }

    public BigDecimal getRefundNationalSubsidyTotal() {
        if (refundNationalSubsidyTotal == null) {
            return BigDecimal.ZERO;
        }
        return refundNationalSubsidyTotal;
    }

    public void setRefundNationalSubsidyTotal(BigDecimal refundNationalSubsidyTotal) {
        this.refundNationalSubsidyTotal = refundNationalSubsidyTotal;
    }


    public List<Integer> getSalePeriodNums() {
        return salePeriodNums;
    }

    public void setSalePeriodNums(List<Integer> salePeriodNums) {
        this.salePeriodNums = salePeriodNums;
    }
    public RefundPayTypeEnum getRefundPayTypeEnum() {
        return refundPayTypeEnum;
    }

    public void setRefundPayTypeEnum(RefundPayTypeEnum refundPayTypeEnum) {
        this.refundPayTypeEnum = refundPayTypeEnum;
    }

    public PlatformSignState getPlatformSignState() {
        if (platformSignState == null) {
            return PlatformSignState.UNKNOWN;
        }
        return platformSignState;
    }

    public void setPlatformSignState(PlatformSignState platformSignState) {
        this.platformSignState = platformSignState;
    }

    public String getExchangeTradeId() {
        return exchangeTradeId;
    }

    public void setExchangeTradeId(String exchangeTradeId) {
        this.exchangeTradeId = exchangeTradeId;
    }

    public Date getRefundSuccessTime() {
        return refundSuccessTime;
    }

    public void setRefundSuccessTime(Date refundSuccessTime) {
        this.refundSuccessTime = refundSuccessTime;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public BusinessType getBusinessType() {
        return businessType;
    }

    public void setBusinessType(BusinessType businessType) {
        this.businessType = businessType;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public double getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(double refundFee) {
        this.refundFee = refundFee;
    }

    public String getReason() {
        // 徐星叫我改的
        if (reason == null) {
            return "";
        }
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDescription() {
        if (StringUtils.isEmpty(description)) {
            description = "";
        }
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRefundVersion() {
        return refundVersion;
    }

    public void setRefundVersion(String refundVersion) {
        this.refundVersion = refundVersion;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public double getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(double totalFee) {
        this.totalFee = totalFee;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public double getRefundFreightFee() {
        return refundFreightFee;
    }

    public void setRefundFreightFee(double refundFreightFee) {
        this.refundFreightFee = refundFreightFee;
    }

    public RefundPhase getRefundPhase() {
        return refundPhase;
    }

    public void setRefundPhase(RefundPhase refundPhase) {
        this.refundPhase = refundPhase;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public RefundStatus getRefundStatus() {
        if (refundStatus == null){
            return RefundStatus.NONE;
        }
        return refundStatus;
    }

    public void setRefundStatus(RefundStatus refundStatus) {
        this.refundStatus = refundStatus;
    }

    public RefundGoodsStatus getGoodsStatus() {
        return goodsStatus;
    }

    public void setGoodsStatus(RefundGoodsStatus goodsStatus) {
        this.goodsStatus = goodsStatus;
    }

    public RefundTypeEnum getRefundTypeEnum() {
        return refundTypeEnum;
    }

    public void setRefundTypeEnum(RefundTypeEnum refundTypeEnum) {
        this.refundTypeEnum = refundTypeEnum;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Date getGoodReturnTime() {
        return goodReturnTime;
    }

    public void setGoodReturnTime(Date goodReturnTime) {
        this.goodReturnTime = goodReturnTime;
    }

    public List<RefundFreight> getRefundFreights() {
        if (Objects.isNull(refundFreights)) {
            refundFreights = new ArrayList<>();
        }
        return refundFreights;
    }

    public void setRefundFreights(List<RefundFreight> refundFreights) {
        this.refundFreights = refundFreights;
    }

    public List<EshopRefundDetailEntity> getRefundDetails() {
        if (Objects.isNull(refundDetails)) {
            refundDetails = new ArrayList<>();
        }
        return refundDetails;
    }

    public void setRefundDetails(List<EshopRefundDetailEntity> refundDetails) {
        this.refundDetails = refundDetails;
    }

    public List<EshopExchangeDetailEntity> getExchangeDetails() {
        if (CollectionUtils.isEmpty(exchangeDetails)) {
            exchangeDetails = new ArrayList<>();
        }
        return exchangeDetails;
    }

    public void setExchangeDetails(List<EshopExchangeDetailEntity> exchangeDetails) {
        this.exchangeDetails = exchangeDetails;
    }

    public ReceiverInfo getReceiverInfo() {
        return receiverInfo;
    }

    public void setReceiverInfo(ReceiverInfo receiverInfo) {
        this.receiverInfo = receiverInfo;
    }

    public RefundSpeedType getRefundSpeedType() {
        return refundSpeedType;
    }

    public void setRefundSpeedType(RefundSpeedType refundSpeedType) {
        this.refundSpeedType = refundSpeedType;
    }

    public Boolean getHasGoodReturn() {
        return hasGoodReturn;
    }

    public void setHasGoodReturn(Boolean hasGoodReturn) {
        this.hasGoodReturn = hasGoodReturn;
    }

    public boolean isExistLocal() {
        return existLocal;
    }

    public void setExistLocal(boolean existLocal) {
        this.existLocal = existLocal;
    }

    public RefundProcessStatus getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(RefundProcessStatus processStatus) {
        this.processStatus = processStatus;
    }

    public Date getProcessLatestTime() {
        return processLatestTime;
    }

    public void setProcessLatestTime(Date processLatestTime) {
        this.processLatestTime = processLatestTime;
    }

    public BigDecimal getServiceFee() {
        if (serviceFee == null) {
            serviceFee = BigDecimal.ZERO;
        }
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    public boolean isDelivered() {
        return delivered;
    }

    public void setDelivered(boolean delivered) {
        this.delivered = delivered;
    }

    public PlatformChangeState getPlatformChangeState() {
        return platformChangeState;
    }

    public void setPlatformChangeState(PlatformChangeState platformChangeState) {
        this.platformChangeState = platformChangeState;
    }

    public PlatformConfirmState getPlatformConfirmState() {
        return platformConfirmState;
    }

    public void setPlatformConfirmState(PlatformConfirmState platformConfirmState) {
        this.platformConfirmState = platformConfirmState;
    }

    public PlatformRefundState getPlatformRefundState() {
        return platformRefundState;
    }

    public void setPlatformRefundState(PlatformRefundState platformRefundState) {
        this.platformRefundState = platformRefundState;
    }

    public PlatformReturnState getPlatformReturnState() {
        return platformReturnState;
    }

    public void setPlatformReturnState(PlatformReturnState platformReturnState) {
        this.platformReturnState = platformReturnState;
    }

    public List<BaseOrderMarkEnum> getRefundMarks() {
        if (refundMarks == null) {
            refundMarks = new ArrayList<>();
        }
        return refundMarks;
    }

    public void setRefundMarks(List<BaseOrderMarkEnum> refundMarks) {
        this.refundMarks = refundMarks;
    }

    public List<MarkData> getMarkDataList() {
        if (markDataList == null) {
            markDataList = new ArrayList<>();
        }
        return markDataList;
    }

    public void setMarkDataList(List<MarkData> markDataList) {
        this.markDataList = markDataList;
    }

    public Date getRefundFinishTime() {
        return refundFinishTime;
    }

    public void setRefundFinishTime(Date refundFinishTime) {
        this.refundFinishTime = refundFinishTime;
    }

    public void setInterceptInvestor(String interceptInvestor) {
        this.interceptInvestor = interceptInvestor;
    }

    public String getInterceptInvestor() {
        return interceptInvestor;
    }

    public Boolean getPlatformAutoInterceptAgree() {
        return platformAutoInterceptAgree;
    }

    public void setPlatformAutoInterceptAgree(Boolean platformAutoInterceptAgree) {
        this.platformAutoInterceptAgree = platformAutoInterceptAgree;
    }

    public List<RefundInterceptEntity> getRefundInterceptList() {
        return refundInterceptList;
    }

    public void setRefundInterceptList(List<RefundInterceptEntity> refundInterceptList) {
        this.refundInterceptList = refundInterceptList;
    }

    public void setPlatformRefundPhase(RefundPhase platformRefundPhase) {
        this.platformRefundPhase = platformRefundPhase;
    }

    public RefundPhase getPlatformRefundPhase() {
        return platformRefundPhase;
    }

    public int getRefundTag() {
        return refundTag;
    }

    public void setRefundTag(int refundTag) {
        this.refundTag = refundTag;
    }

    public String getOnlineRefundStatus() {
        return onlineRefundStatus;
    }

    public void setOnlineRefundStatus(String onlineRefundStatus) {
        this.onlineRefundStatus = onlineRefundStatus;
    }

    public String getOnlineRefundType() {
        return onlineRefundType;
    }

    public void setOnlineRefundType(String onlineRefundType) {
        this.onlineRefundType = onlineRefundType;
    }
}
