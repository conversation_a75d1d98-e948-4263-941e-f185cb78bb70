package com.wsgjp.ct.sale.platform.mock;

import com.wsgjp.ct.sale.platform.entity.request.other.MockRequest;

/**
 * <AUTHOR> 2024/4/11 14:44
 */
public interface ApiMockerService {
    /**
     * 根据订单号查询订单
     * @param request 求情参数
     * @return JSON实体
     */
    Object queryData(MockRequest request);

    /**
     * mock方法名称
     * @return 方法名称
     */
    String methodName();

    /**
     * 构建mock参数
     * @param request mock请求参数
     * @param args 原始请求参数
     */
    void buildRequest(MockRequest request, Object[] args);
}
