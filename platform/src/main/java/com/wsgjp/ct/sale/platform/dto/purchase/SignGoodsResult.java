package com.wsgjp.ct.sale.platform.dto.purchase;

import ngp.utils.StringUtils;

/**
 * 订单签收信息结果
 */
public class SignGoodsResult {

    public SignGoodsResult(SignGoodsEntity signGoods, String errorMsg) {
        this.signGoods = signGoods;
        this.errorMsg = errorMsg;
    }

    /**
     * 签收信息
     */
    private SignGoodsEntity signGoods;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 签收结果：true=签收成功，false=签收失败
     */
    private boolean success;

    public SignGoodsEntity getSignGoods() {
        return signGoods;
    }

    public void setSignGoods(SignGoodsEntity signGoods) {
        this.signGoods = signGoods;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public boolean isSuccess() {
        if(!StringUtils.isEmpty(errorMsg)){
            success = true;
        }
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
