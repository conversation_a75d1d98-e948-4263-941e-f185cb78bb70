package com.wsgjp.ct.sale.platform.dto.order;

import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class RdsOrderEntity {
    private String tradeId;
    private String status;
    private String type;
    private String sellerNick;
    private String response;
    private Date created;
    private Date modified;
    private Date pushCreated;
    private Date pushModified;

    private TradeStatus tradeStatus;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Date getPushCreated() {
        return pushCreated;
    }

    public void setPushCreated(Date pushCreated) {
        this.pushCreated = pushCreated;
    }

    public Date getPushModified() {
        return pushModified;
    }

    public void setPushModified(Date pushModified) {
        this.pushModified = pushModified;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }
}
