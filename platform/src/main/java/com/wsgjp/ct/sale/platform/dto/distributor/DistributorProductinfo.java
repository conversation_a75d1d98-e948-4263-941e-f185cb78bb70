package com.wsgjp.ct.sale.platform.dto.distributor;

import java.util.List;

public class DistributorProductinfo {

    //必填
    private String btypeId;
    //必填
    private String distributorName;

    private String btypeCode;

    private String btypePhone;

    private int btypeStatus;

    private String btypePerson;

    private List<ProductPriceInfo> productPriceInfoList;

    //错误信息 只有数据返回 回填错误的时候才会有 入参不要给
    private String errMessage;

    public String getBtypePhone() {
        return btypePhone;
    }

    public void setBtypePhone(String btypePhone) {
        this.btypePhone = btypePhone;
    }

    public int getBtypeStatus() {
        return btypeStatus;
    }

    public void setBtypeStatus(int btypeStatus) {
        this.btypeStatus = btypeStatus;
    }

    public String getBtypePerson() {
        return btypePerson;
    }

    public void setBtypePerson(String btypePerson) {
        this.btypePerson = btypePerson;
    }

    public String getBtypeCode() {
        return btypeCode;
    }

    public void setBtypeCode(String btypeCode) {
        this.btypeCode = btypeCode;
    }


    public String getBtypeId() {
        return btypeId;
    }

    public void setBtypeId(String btypeId) {
        this.btypeId = btypeId;
    }

    public String getDistributorName() {
        return distributorName;
    }

    public void setDistributorName(String distributorName) {
        this.distributorName = distributorName;
    }

    public List<ProductPriceInfo> getProductPriceInfoList() {
        return productPriceInfoList;
    }

    public void setProductPriceInfoList(List<ProductPriceInfo> productPriceInfoList) {
        this.productPriceInfoList = productPriceInfoList;
    }

    public String getErrMessage() {
        return errMessage;
    }

    public void setErrMessage(String errMessage) {
        this.errMessage = errMessage;
    }
}
