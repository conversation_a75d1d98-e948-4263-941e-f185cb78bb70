package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 币种类型
 */
public enum CurrencyType implements CodeEnum {
    /**
     * 人民币
     */
    RMB(0,"人民币")
   ;


    CurrencyType(int flag, String name){
        this.flag=flag;
        this.name=name;
    }

    private int flag;
    private String name;

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

}
