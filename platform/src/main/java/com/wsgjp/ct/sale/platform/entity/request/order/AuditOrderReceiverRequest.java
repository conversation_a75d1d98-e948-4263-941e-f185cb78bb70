package com.wsgjp.ct.sale.platform.entity.request.order;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.enums.LocalProcessStatus;

/**
 * <AUTHOR>
 */
public class AuditOrderReceiverRequest extends BaseRequest {
    private String tradeId;

    private LocalProcessStatus status;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public LocalProcessStatus getStatus() {
        return status;
    }

    public void setStatus(LocalProcessStatus status) {
        this.status = status;
    }
}
