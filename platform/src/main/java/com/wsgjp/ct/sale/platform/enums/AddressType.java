package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum AddressType implements CodeEnum {
    PROVINCE(1, "省"),
    CITY(2, "市"),
    DISTRICT(3, "区/县"),
    TOWN(4,"镇");

    private final int index;

    private final String name;

    AddressType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}
