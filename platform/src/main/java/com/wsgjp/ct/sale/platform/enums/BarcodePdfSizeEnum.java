package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum BarcodePdfSizeEnum implements CodeEnum {

    /**
     * 1. SIZE_80_30：80mm*30mm
     * 2. SIZE_60_20：60mm*20mm
     * */

    SIZE_80_30(1,"SIZE_80_30"),
    SIZE_60_20(2,"SIZE_60_20");

    private int code;
    private String name;

    BarcodePdfSizeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
