package com.wsgjp.ct.sale.platform.dto.refund;


import com.wsgjp.ct.sale.platform.enums.RefundStatus;

/**
 * 换货明细
 */
public class EshopExchangeDetailEntity {
    /**
     * 对应订单明细中的子订单号, 没用
     */
    private String oid;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 换货商品id，必传
     */
    private String numId;
    /**
     * 换货商品skuId
     */
    private String skuId;
    /**
     * 换货主商品编码
     */
    private String outerId;
    /**
     * 换货sku商品编码
     */
    private String outerSkuId;
    /**
     * 换货商品属性，必传
     */
    private String properties;

    private String picUrl;
    /**
     * 换货商品数量
     */
    private double qty;
    /**
     * 商品单价
     */
    private double price;
    /**
     * 换货明细总金额
     */
    private double totalFee;

    /**
     * 换货运费金额，暂时未用
     */
    private double refundFreightFee;
    /**
     * 买家支付金额，暂时未用
     */
    private String payment;
    /**
     * 售后状态，暂时未用
     */
    private RefundStatus refundState;

    private boolean fillGoodsFromGoodApi;

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    @Deprecated
    public String getOid() {
        return oid;
    }

    @Deprecated
    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getNumId() {
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getOuterSkuId() {
        return outerSkuId;
    }

    public void setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public double getQty() {
        return qty;
    }

    public void setQty(double qty) {
        this.qty = qty;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(double totalFee) {
        this.totalFee = totalFee;
    }

    public double getRefundFreightFee() {
        return refundFreightFee;
    }

    public void setRefundFreightFee(double refundFreightFee) {
        this.refundFreightFee = refundFreightFee;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public RefundStatus getRefundState() {
        return refundState;
    }

    public void setRefundState(RefundStatus refundState) {
        this.refundState = refundState;
    }

    public boolean isFillGoodsFromGoodApi() {
        return fillGoodsFromGoodApi;
    }

    public void setFillGoodsFromGoodApi(boolean fillGoodsFromGoodApi) {
        this.fillGoodsFromGoodApi = fillGoodsFromGoodApi;
    }
}
