package com.wsgjp.ct.sale.platform.dto.product;


import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;

/**
 * <AUTHOR>
 * 商品类目实体
 */
public class ProductCategoryEntity extends ErpBaseInfo {
    /**
     * 网店目录id
     */
    private String classId;

    /**
     * 网店父级目录id
     */
    private String parClassId;

    /**
     * 网店父级目录名称
     */
    private String className;


    public String getClassId() {
        return classId;
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public String getParClassId() {
        return parClassId;
    }

    public void setParClassId(String parClassId) {
        this.parClassId = parClassId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

}
