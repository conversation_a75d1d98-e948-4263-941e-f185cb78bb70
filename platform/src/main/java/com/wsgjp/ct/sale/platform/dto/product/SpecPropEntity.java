package com.wsgjp.ct.sale.platform.dto.product;

import com.doudian.open.api.product_getCatePropertyV2.data.UnitsItem;
import com.doudian.open.api.product_getCatePropertyV2.data.ValidateRule;
import com.wsgjp.ct.sale.common.enums.publish.InputTypeEnum;
import ngp.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 规格属性实体
 *
 * <AUTHOR>
 */
public class SpecPropEntity {
    /**
     * 规格参数id
     */
    private String specId;
    /**
     * 规格参数排序
     */
    private Integer orderSort;
    /**
     * 规格参数名称
     */
    private String specName;
    /**
     * 是否必填
     */
    private boolean required;
    /**
     * 输入类型
     */
    private InputTypeEnum inputType;
    /**
     * 属性值别名
     */
    private String attrAlias;
    /**
     * 属性值单位
     */
    private String valUnit;
    /**
     * 维护备注/示例
     */
    private String maintainRemark;
    /**
     * 属性值别名的内容
     */
    private String aliasContent;
    /**
     * 是否使用家电选购指数1:是,0:否
     */
    private Integer choosePurchase;
    /**
     * 规格参数值列表
     */
    private List<SpecPropValue> values;

    /**
     * 级联规格值列表
     */
    private SpecPropEntity childSpecProp;

    private List<Object> showChildValues;

    private boolean hasParentSpecProp;

    private String valuesModeId;

    private ValidateRule validateRule;

    /**
     * 抖店在用，比如材质，多选后需要输入占比多少
     */
    private List<UnitsItem> units;

    private String unitsModeId;
    /**
     * 属性多选限制个数
     */
    private Integer valCount;
    /**
     * 级联组id
     */
    private Integer cascadeGroupId;
    /**
     * 级联组名称
     */
    private String cascadeGroupName;
    /**
     * 级联组级别
     */
    private Integer cascadeGroupLevel;
    /**
     * 地域等级
     */
    private String inputAreaLevel;
    /**
     * 级联属性
     */
    private List<SpecPropEntity> cascadePropList;
    /**
     * 内部映射InputType
     */
    private Integer originInputType;
    /**
     * 平台扩展字段
     */
    private String features;

    /**
     * 1:自定义属性,0=不支持自定义
     */
    private Long diyType;

    /**
     * 是否有次级级联属性，主要用于抖店产品规格影响销售属性
     */
    private Boolean hasSubProperty;


    public String getValuesModeId() {
        return valuesModeId;
    }

    public void setValuesModeId(String valuesModeId) {
        this.valuesModeId = valuesModeId;
    }

    public String getUnitsModeId() {
        return unitsModeId;
    }

    public void setUnitsModeId(String unitsModeId) {
        this.unitsModeId = unitsModeId;
    }

    public ValidateRule getValidateRule() {
        return validateRule;
    }

    public void setValidateRule(ValidateRule validateRule) {
        this.validateRule = validateRule;
    }

    public List<UnitsItem> getUnits() {
        return units;
    }

    public void setUnits(List<UnitsItem> units) {
        this.units = units;
    }

    public Boolean getHasSubProperty() {
        return hasSubProperty;
    }

    public void setHasSubProperty(Boolean hasSubProperty) {
        this.hasSubProperty = hasSubProperty;
    }

    public Long getDiyType() {
        return diyType;
    }

    public void setDiyType(Long diyType) {
        this.diyType = diyType;
    }

    public String getSpecId() {
        return specId;
    }

    public void setSpecId(String specId) {
        this.specId = specId;
    }

    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public InputTypeEnum getInputType() {
        return inputType;
    }

    public void setInputType(InputTypeEnum inputType) {
        this.inputType = inputType;
    }

    public String getAttrAlias() {
        return attrAlias;
    }

    public void setAttrAlias(String attrAlias) {
        this.attrAlias = attrAlias;
    }

    public String getValUnit() {
        return valUnit;
    }

    public void setValUnit(String valUnit) {
        this.valUnit = valUnit;
    }

    public String getMaintainRemark() {
        return maintainRemark;
    }

    public void setMaintainRemark(String maintainRemark) {
        this.maintainRemark = maintainRemark;
    }

    public String getAliasContent() {
        return aliasContent;
    }

    public void setAliasContent(String aliasContent) {
        this.aliasContent = aliasContent;
    }

    public Integer getChoosePurchase() {
        return choosePurchase;
    }

    public void setChoosePurchase(Integer choosePurchase) {
        this.choosePurchase = choosePurchase;
    }

    public List<SpecPropValue> getValues() {
        return values;
    }

    public void setValues(List<SpecPropValue> values) {
        this.values = values;
    }

    public Integer getValCount() {
        return valCount;
    }

    public void setValCount(Integer valCount) {
        this.valCount = valCount;
    }

    public Integer getCascadeGroupId() {
        return cascadeGroupId;
    }

    public void setCascadeGroupId(Integer cascadeGroupId) {
        this.cascadeGroupId = cascadeGroupId;
    }

    public String getCascadeGroupName() {
        return cascadeGroupName;
    }

    public void setCascadeGroupName(String cascadeGroupName) {
        this.cascadeGroupName = cascadeGroupName;
    }

    public Integer getCascadeGroupLevel() {
        return cascadeGroupLevel;
    }

    public void setCascadeGroupLevel(Integer cascadeGroupLevel) {
        this.cascadeGroupLevel = cascadeGroupLevel;
    }

    public String getInputAreaLevel() {
        return inputAreaLevel;
    }

    public void setInputAreaLevel(String inputAreaLevel) {
        this.inputAreaLevel = inputAreaLevel;
    }

    public List<SpecPropEntity> getCascadePropList() {
        return cascadePropList;
    }

    public void setCascadePropList(List<SpecPropEntity> cascadePropList) {
        this.cascadePropList = cascadePropList;
    }

    public Integer getOriginInputType() {
        return originInputType;
    }

    public void setOriginInputType(Integer originInputType) {
        this.originInputType = originInputType;
    }

    public String getFeatures() {
        return features;
    }

    public void setFeatures(String features) {
        this.features = features;
    }


    public SpecPropEntity getChildSpecProp() {
        return childSpecProp;
    }

    public void setChildSpecProp(SpecPropEntity childSpecProp) {
        this.childSpecProp = childSpecProp;
    }

    public List<Object> getShowChildValues() {
        if (CollectionUtils.isEmpty(showChildValues)) {
            showChildValues = new ArrayList<>();
        }
        return showChildValues;
    }

    public void setShowChildValues(List<Object> showChildValues) {
        this.showChildValues = showChildValues;
    }

    public boolean isHasParentSpecProp() {
        return hasParentSpecProp;
    }

    public void setHasParentSpecProp(boolean hasParentSpecProp) {
        this.hasParentSpecProp = hasParentSpecProp;
    }
}
