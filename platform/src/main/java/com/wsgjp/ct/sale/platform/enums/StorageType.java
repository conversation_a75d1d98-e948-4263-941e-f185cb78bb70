package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

public enum StorageType implements Serializable, CodeEnum {
    /**
     * 正品
     */
    Normal(1, "正品"),
    /**
     * 残次品
     */
    Imperfections(101, "残次品");


    StorageType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @JSONField(serialize = false)
    private String name;

    private int code;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public static StorageType typeOf(int code) {
        for (StorageType storageType : values()) {
            if (storageType.getCode() == code) {
                return storageType;
            }
        }
        throw new RuntimeException("不支持的枚举");
    }

}
