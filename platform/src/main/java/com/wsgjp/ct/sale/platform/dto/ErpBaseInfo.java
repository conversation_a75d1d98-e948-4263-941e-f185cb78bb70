package com.wsgjp.ct.sale.platform.dto;

import com.wsgjp.ct.common.enums.core.enums.ShopType;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * ERP系统基础数据
 */
public class ErpBaseInfo {
    /**
     * 账套id
     */
    private BigInteger profileId;
    /**
     * 店铺ID
     */
    private BigInteger eshopId;
    /**
     * 店铺类型
     */
    private ShopType shopType;

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }
}
