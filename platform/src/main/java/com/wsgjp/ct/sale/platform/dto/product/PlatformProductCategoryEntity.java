package com.wsgjp.ct.sale.platform.dto.product;


import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-12-09 14:32
 */
public class PlatformProductCategoryEntity extends ErpBaseInfo {
    /**
     * 网店目录id
     */
    private String platformClassId;

    /**
     * 网店父级目录id
     */
    private String parPlatformClassId;

    /**
     * 网店父级目录名称
     */
    private String platformClassName;

    private BigDecimal platformComminsion = BigDecimal.ZERO;

    public String getPlatformClassId() {
        return platformClassId;
    }

    public void setPlatformClassId(String platformClassId) {
        this.platformClassId = platformClassId;
    }

    public String getParPlatformClassId() {
        return parPlatformClassId;
    }

    public void setParPlatformClassId(String parPlatformClassId) {
        this.parPlatformClassId = parPlatformClassId;
    }

    public String getPlatformClassName() {
        return platformClassName;
    }

    public void setPlatformClassName(String platformClassName) {
        this.platformClassName = platformClassName;
    }

    public BigDecimal getPlatformComminsion() {
        return platformComminsion;
    }

    public void setPlatformComminsion(BigDecimal platformComminsion) {
        this.platformComminsion = platformComminsion;
    }
}

