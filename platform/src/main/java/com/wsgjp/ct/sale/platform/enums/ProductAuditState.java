package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 发布/修改商品审核状态
 */
public enum ProductAuditState implements CodeEnum {
    NOT_AUDIT(0,"未审核"),
    AUDIT(1,"审核中"),
    PASS_AUDIT(2,"审核通过"),
    REFUSE_AUDIT(3,"审核拒绝");

    private final Integer code;
    private final String desc;

    ProductAuditState(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return CodeEnum.super.getName();
    }


    public String getDesc() {
        return desc;
    }
}
