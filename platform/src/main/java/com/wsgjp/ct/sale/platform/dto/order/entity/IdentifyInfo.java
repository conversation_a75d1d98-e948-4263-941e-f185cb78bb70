package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.common.enums.eshoporder.IdentifyResultType;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class IdentifyInfo {
    private IdentifyResultType status;

    private String description;

    private Date identifyTime;

    public IdentifyResultType getStatus() {
        return status;
    }

    public void setStatus(IdentifyResultType status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getIdentifyTime() {
        return identifyTime;
    }

    public void setIdentifyTime(Date identifyTime) {
        this.identifyTime = identifyTime;
    }
}
