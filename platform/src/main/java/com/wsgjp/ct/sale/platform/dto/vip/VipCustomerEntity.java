package com.wsgjp.ct.sale.platform.dto.vip;

import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import com.wsgjp.ct.sale.platform.enums.Sex;

import java.util.Date;

/**
 * 会员用户信息实体
 * <AUTHOR>
 */
public class VipCustomerEntity extends ErpBaseInfo {

    public VipCustomerEntity() {

    }

    public VipCustomerEntity(EshopSystemParams systemParams){
        setProfileId(systemParams.getProfileId());
        setEshopId(systemParams.geteShopId());
        setShopType(systemParams.getShopType());
    }

    /**
     *
     * 线上会员id
     */
    private String platformCustomerId;
    /**
     * 会员头像
     */
    private String platformCustomerAvatar;
    /**
     * 会员名称
     */
    private String platformCustomerName;
    /**
     * 会员生日
     */
    private String birthday;
    /**
     * 会员电话
     */
    private String mobile;
    /**
     * 会员等级
     */
    private String level;
    /**
     * 性别
     */
    private Sex sex;
    /**
     * 身份证号
     */

    private String idCard;
    /**
     * 邮箱
     */

    private String email;

    /**
     * 微信号
     */
    private String  weiXin;
    /**
     * 微信昵称
     */
    private String wxNickname;
    /**
     * 会员注册时间
     */
    private Date registerTime;
    /**
     * 新增时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    public String getPlatformCustomerId() {
        return platformCustomerId;
    }

    public void setPlatformCustomerId(String platformCustomerId) {
        this.platformCustomerId = platformCustomerId;
    }

    public String getPlatformCustomerAvatar() {
        return platformCustomerAvatar;
    }

    public void setPlatformCustomerAvatar(String platformCustomerAvatar) {
        this.platformCustomerAvatar = platformCustomerAvatar;
    }

    public String getPlatformCustomerName() {
        return platformCustomerName;
    }

    public void setPlatformCustomerName(String platformCustomerName) {
        this.platformCustomerName = platformCustomerName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Sex getSex() {
        return sex;
    }

    public void setSex(Sex sex) {
        this.sex = sex;
    }

    public String getWeiXin() {
        return weiXin;
    }

    public void setWeiXin(String weiXin) {
        this.weiXin = weiXin;
    }

    public String getWxNickname() {
        return wxNickname;
    }

    public void setWxNickname(String wxNickname) {
        this.wxNickname = wxNickname;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
