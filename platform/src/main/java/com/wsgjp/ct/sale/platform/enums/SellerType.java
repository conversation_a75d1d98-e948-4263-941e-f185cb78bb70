package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 卖家类型
 */
public enum SellerType implements CodeEnum {
    /**
     * 商城
     */
    B(0,"商城"),
    C(1,"普通C店");

    private int flag;

    private String name;

    SellerType(int flag, String name){
        this.flag=flag;
        this.name=name;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
