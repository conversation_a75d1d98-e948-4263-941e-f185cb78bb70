package com.wsgjp.ct.sale.platform.dto.sendgoods;

import bf.datasource.typehandler.CodeEnum;
import ngp.utils.StringUtils;

/**
 * 序列号枚举
 *
 * <AUTHOR>
 */

public enum SnType implements CodeEnum {
    DELIVER_SN(0, "snno", "序列号"),
    //唯品会
    UNSEALING_CODE(1, "sn1", "拆封码"),
    //拼多多
    IMEI(2, "sn2", "IMEI码"),
    //淘宝
    ICCID(3, "sn3", "ICCID");
    private final String name;
    private final String desc;

    private final int code;

    SnType(int code, String name, String desc) {
        this.name = name;
        this.desc = desc;
        this.code=code;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static SnType getSnTypeByCode(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }

        for (SnType value : SnType.values()) {
            if (name.equals(value.getName())) {
                return value;
            }
        }
        return null;
    }
}