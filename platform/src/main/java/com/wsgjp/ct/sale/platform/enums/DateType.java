package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */
public enum DateType implements CodeEnum {
    /**
     * 创建时间
     */
    CREATE_TIME(0, "创建时间"),
    /**
     * 修改时间
     */
    UPDATE_TIME(1, "修改时间");


    DateType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private final int code;
    private final String name;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
