package com.wsgjp.ct.sale.platform.dto.product;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class ImageCategoryInfo {
    /**
     * 要更新的分类ID
     */
    private String cateId;
    /**
     * 分类名称
     */
    private String cateName;
    /**
     * 父分类ID
     */
    private String parentCateId;
    /**
     * 分类层级
     */
    private Integer cateLevel;
    /**
     * 同级分类排序值
     */
    private Integer cateOrder;
    private Date createTime;
    private Date updateTime;

    public String getCateId() {
        return cateId;
    }

    public void setCateId(String cateId) {
        this.cateId = cateId;
    }

    public String getCateName() {
        return cateName;
    }

    public void setCateName(String cateName) {
        this.cateName = cateName;
    }

    public String getParentCateId() {
        return parentCateId;
    }

    public void setParentCateId(String parentCateId) {
        this.parentCateId = parentCateId;
    }

    public Integer getCateLevel() {
        return cateLevel;
    }

    public void setCateLevel(Integer cateLevel) {
        this.cateLevel = cateLevel;
    }

    public Integer getCateOrder() {
        return cateOrder;
    }

    public void setCateOrder(Integer cateOrder) {
        this.cateOrder = cateOrder;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
