package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum MsgDataTypeEnum implements CodeEnum {
    NONE(0, "未知"),
    ORDER(1, "订单"),
    REFUND(2, "售后"),
    PRODUCT(3, "商品"),
    ORDER_CANCEL(4, "取消订单");

    private final int code;
    private final String name;

    MsgDataTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MsgDataTypeEnum valueOf(int code) {
        for (MsgDataTypeEnum s : MsgDataTypeEnum.values()) {
            if (s.code == code) {
                return s;
            }
        }
        return MsgDataTypeEnum.NONE;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
