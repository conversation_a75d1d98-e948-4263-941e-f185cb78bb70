package com.wsgjp.ct.sale.platform.dto.eshop;

/**
 * <AUTHOR>
 * @date 10/9/2020 下午 5:25
 */
public class PlatformEshopSupport {
    /**
     * 店铺类型名称(例如：有赞微商城、有赞零售)
     */
    private String name;
    /**
     * 店铺类型编码
     */
    private int type;
    /**
     * 平台类型名称(例如：有赞)
     * 注：有赞微商城、有赞零售两种店铺类型都属于有赞平台
     */
    private String platformName;
    /**
     * 平台类型编码
     */
    private int platformType;
    /**
     * 显示appkey
     */
    private int showAppKey;
    /**
     * 授权类型
     */
    private int authType;

    public PlatformEshopSupport() {
    }

    public PlatformEshopSupport(int type, String name) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getShowAppKey() {
        return showAppKey;
    }

    public void setShowAppKey(int showAppKey) {
        this.showAppKey = showAppKey;
    }

    public int getAuthType() {
        return authType;
    }

    public void setAuthType(int authType) {
        this.authType = authType;
    }

    public int getPlatformType() {
        return platformType;
    }

    public void setPlatformType(int platformType) {
        this.platformType = platformType;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }
}
