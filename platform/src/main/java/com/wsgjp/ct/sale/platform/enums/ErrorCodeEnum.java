package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 12/28/2020 上午 09:47
 */
public enum ErrorCodeEnum implements CodeEnum {

    /**
     * 错误码
     */
    ApiError(0, "apiName", "00"),
    PlatTimeOut(1, "三方接口返回超时", "01"),
    PlatDataError(2, "三方接口数据异常", "02"),
    PermissionDenied(3, "访问平台接口无权限", "03"),
    NotInWhiteList(4, "不在平台访问接口的白名单中", "04"),
    NeedPlatformParam(5, "缺少平台必要参数", "05"),
    PlatBusinessError(6, "平台业务错误", "06"),
    PlatApiExecuteError(7, "执行调用平台接口方法报错", "07"),
    ASPECT_IGNORE(8, "注入忽略", "08"),
    BUSINESS(9, "一般业务错误", "09");

    private int index;
    private String code;

    private String name;

    ErrorCodeEnum(int index, String name, String code) {
        this.index = index;
        this.name = name;
        this.code = code;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }

    public String getUserCode() {
        return code;
    }
}
