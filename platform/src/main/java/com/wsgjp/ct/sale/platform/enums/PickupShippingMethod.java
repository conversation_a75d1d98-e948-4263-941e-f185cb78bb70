package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PickupShippingMethod implements CodeEnum {

    /**
     * 物流方式 1:零担 2:整车 3:其它
     */

    LESS_THAN_TRUCKLOAD(1,"零担"),
    COMPLETE_VEHICLE(2,"整车"),
    OTHER(3,"其它");


    private int code;
    private String name;

    PickupShippingMethod(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PickupShippingMethod valueOf(int code) {
        for (PickupShippingMethod shippingMethod : values()) {
            if (shippingMethod.getCode() == code) {
                return shippingMethod;
            }
        }
        return PickupShippingMethod.OTHER;
    }
    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
