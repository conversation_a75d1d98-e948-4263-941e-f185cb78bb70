package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.platform.enums.MergeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel("订单实体")
public class SimpleOrderEntity {
    @ApiModelProperty(value = "订单号", example = "100001")
    private String tradeId;
    @ApiModelProperty(value = "用于判断订单是否能合并的数据id，例如阿里巴巴平台的收件人信息码", example = "1000000")
    private String dataId;

    @ApiModelProperty(value = "用于判断订单是否能合并的数据id2，例如阿里巴巴平台的收件人信息码", example = "1000000")
    private String dataId2;

    @ApiModelProperty(value = "是否集运订单")
    private MergeType mergeType;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getDataId2() {
        return dataId2;
    }

    public void setDataId2(String dataId2) {
        this.dataId2 = dataId2;
    }

    public MergeType getMergeType() {
        return mergeType;
    }

    public void setMergeType(MergeType mergeType) {
        this.mergeType = mergeType;
    }
}
