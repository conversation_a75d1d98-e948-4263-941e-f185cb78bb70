package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

/**
 * <AUTHOR>
 * 销售属性规则
 */
public class SalePropRule {
    //最小长度
    private Integer min;
    //最大长度
    private Integer max;
    //正则表达式
    private List<String> patternList;

    /**
     * 当属性值不满足规则时的提示信息
     */
    private String tipMessage;

    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public List<String> getPatternList() {
        return patternList;
    }

    public void setPatternList(List<String> patternList) {
        this.patternList = patternList;
    }
}
