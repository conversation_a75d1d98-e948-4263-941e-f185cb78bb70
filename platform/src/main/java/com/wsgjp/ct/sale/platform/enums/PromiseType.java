package com.wsgjp.ct.sale.platform.enums;

/**
 * <AUTHOR>
 * @date 2020-01-02 11:01
 */
public enum PromiseType {
    /**
     * 菜鸟物流服务
     */
    CAI_NIAO(0,"菜鸟物流服务"),
    /**
     * 准时达
     */
    ON_TIME(1,"准时达"),

    /**
     * 上面自提约定
     */
    FETCH(2, "自提约定");

    private int flag;

    private String name;

    PromiseType(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }
}
