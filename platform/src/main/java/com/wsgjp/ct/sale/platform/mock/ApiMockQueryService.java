package com.wsgjp.ct.sale.platform.mock;

import com.wsgjp.ct.sale.platform.entity.request.mock.QueryDataListParam;

import java.util.List;

/**
 * <AUTHOR> 2024/5/9 15:13
 */
public interface ApiMockQueryService {
    /**
     * 根据单号查询订单
     * @param shopAccount 账号
     * @param tradeId 订单号
     * @return 订单的json实体
     */
    String queryTradeByTradeId(String shopAccount, String tradeId);

    /**
     * 根据时间查询列表实体
     * @param param 参数
     * @return json实体
     */
    List<String> queryDataList(QueryDataListParam param);

    /**
     * 查询mock数据总数
     * @param param 参数
     * @return 总数
     */
    long queryDataCount(QueryDataListParam param);

    /**
     * 根据售后单号查询售后实体
     * @param shopAccount  账号
     * @param refundId 订单号
     * @return json实体
     */
    String queryRefundByRefundId(String shopAccount, String refundId);

    /**
     * 根据订单号查询售后实体
     * @param shopAccount  账号
     * @param tradeId 订单号
     * @return json实体
     */
    String queryRefundByTradeId(String shopAccount, String tradeId);
}
