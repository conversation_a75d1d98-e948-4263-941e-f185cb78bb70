package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-09 14:31
 */
@ApiModel("修改商品请求参数")
public class EshopModifyProductEntity extends ErpBaseInfo {

    @ApiModelProperty("主商品信息")
    private boolean mainProduct;
    @ApiModelProperty("新的商家编码")
    private String newXcode;
    @ApiModelProperty("平台NumId")
    private String platformNumId;
    @ApiModelProperty("平台SkuId")
    private String platformSkuId;
    @ApiModelProperty("单属性SKUid")
    private String defaultSkuId;
    @ApiModelProperty("平台商品属性")
    private String platformProperties;
    @ApiModelProperty("平台商家编码")
    private String platformXcode;
    @ApiModelProperty("平台主商家编码")
    private String platformMainXcode;
    @ApiModelProperty("完整的商品名称")
    private String fullName;
    @ApiModelProperty("商品数量")
    private int qty;

    @ApiModelProperty("skuId集合")
    private List<String> skuIds;
    /**
     * 该字段用于响应实体中返回上下架失败原因
     */
    @ApiModelProperty("上下架失败原因")
    private String failReason;

    public List<String> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<String> skuIds) {
        this.skuIds = skuIds;
    }

    public boolean isMainProduct() {
        return mainProduct;
    }

    public void setMainProduct(boolean mainProduct) {
        this.mainProduct = mainProduct;
    }

    public String getNewXcode() {
        return newXcode;
    }

    public void setNewXcode(String newXcode) {
        this.newXcode = newXcode;
    }

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getPlatformProperties() {
        return platformProperties;
    }

    public void setPlatformProperties(String platformProperties) {
        this.platformProperties = platformProperties;
    }

    public String getPlatformXcode() {
        return platformXcode;
    }

    public void setPlatformXcode(String platformXcode) {
        this.platformXcode = platformXcode;
    }

    public String getPlatformMainXcode() {
        return platformMainXcode;
    }

    public void setPlatformMainXcode(String platformMainXcode) {
        this.platformMainXcode = platformMainXcode;
    }

    public int getQty() {
        return qty;
    }

    public void setQty(int qty) {
        this.qty = qty;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getFailReason() {
        return null == failReason ? "" : failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getDefaultSkuId() {
        return defaultSkuId;
    }

    public void setDefaultSkuId(String defaultSkuId) {
        this.defaultSkuId = defaultSkuId;
    }
}
