package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum ApiInteractiveBizType implements CodeEnum {
    UPDATE_XCODE(1, "修改商家编码"),
    UPDATE_QTY(2, "修改库存"),
    UPDATE_FLAGE_MEMO(3, "修改卖家备注/旗帜"),
    MODIFY_ADDRESS(4, "修改订单地址"),
    SEND_ORDER(5, "发货"),
    AGREE_REFUND(6, "同意售后"),
    REFUSE_REFUND(7, "拒绝售后"),
    ;

    ApiInteractiveBizType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;

    @Override
    public int getCode() {
        return 0;
    }

    @Override
    public String getName() {
        return null;
    }
}
