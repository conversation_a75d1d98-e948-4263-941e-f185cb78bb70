package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ProductCategoryRule {

    /**
     * 类目退款规则列表
     */
    private List<ProductRefundRule> refundRuleList;

    /**
     * 最长预售时间
     */
    private Integer preSaleTime;

    /**
     * 过敏包退
     */
    private String allergyRefund;
    /**
     * 坏了包退
     */
    private String freshRotRefund;
    /**
     * 破损包退
     */
    private String brokenRefund;
    /**
     * 足斤足两
     */
    private String weightGuarantee;


    /**
     * 商品服务规则
     */
    private List<ServiceRule> serviceRules;

    public List<ProductRefundRule> getRefundRuleList() {
        return refundRuleList;
    }

    public void setRefundRuleList(List<ProductRefundRule> refundRuleList) {
        this.refundRuleList = refundRuleList;
    }

    public Integer getPreSaleTime() {
        return preSaleTime;
    }

    public void setPreSaleTime(Integer preSaleTime) {
        this.preSaleTime = preSaleTime;
    }

    public String getAllergyRefund() {
        return allergyRefund;
    }

    public void setAllergyRefund(String allergyRefund) {
        this.allergyRefund = allergyRefund;
    }

    public String getFreshRotRefund() {
        return freshRotRefund;
    }

    public void setFreshRotRefund(String freshRotRefund) {
        this.freshRotRefund = freshRotRefund;
    }

    public String getBrokenRefund() {
        return brokenRefund;
    }

    public void setBrokenRefund(String brokenRefund) {
        this.brokenRefund = brokenRefund;
    }

    public String getWeightGuarantee() {
        return weightGuarantee;
    }

    public void setWeightGuarantee(String weightGuarantee) {
        this.weightGuarantee = weightGuarantee;
    }

    public List<ServiceRule> getServiceRules() {
        return serviceRules;
    }

    public void setServiceRules(List<ServiceRule> serviceRules) {
        this.serviceRules = serviceRules;
    }
}
