package com.wsgjp.ct.sale.platform.dto.member;


import com.wsgjp.ct.sale.platform.dto.member.entity.CardRight;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class VipCardEntity {
    /**
     * 商家的唯一编号
     */
    public String cardVchcode;
    /**
     * 用户会员卡卡号
     */
    public String cardNo;
    /**
     * 会员卡名称
     */
    public String Name;
    /**
     * 会员卡等级
     */
    public int level;

    /**
     * 会员卡状态，1：正常；2：已禁用；3：已删除
     */
    public int status;
    /**
     * 生效开始时间
     */
    public Date startTime;
    /**
     * 生效结束时间
     */
    public Date endTime;
    public int cardDays;
    public int dayType;
    public List<CardRight> rights;
    /**
     * 发卡链接
     */
    public String CardUrl;
    /**
     * 是否可用，必须为true
     */
    public boolean isAvailable;
    /**
     * 创建时间
     */
    public Date createTime;
    /**
     * 用户手机
     */
    public String mobile;
}
