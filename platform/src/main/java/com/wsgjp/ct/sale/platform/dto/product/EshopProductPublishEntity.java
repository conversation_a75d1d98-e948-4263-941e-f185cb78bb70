package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@ApiModel("发布商品请求参数")
public class EshopProductPublishEntity extends ErpBaseInfo {

    @ApiModelProperty("商品品牌名")
    private String brandName;
    @ApiModelProperty("商品全名")
    private String productName;
    @ApiModelProperty("商品id")
    private String productId;
    @ApiModelProperty("商品简名")
    private String productShortName;
    @ApiModelProperty("商品编码")
    private String productCode;
    @ApiModelProperty("商品所在地")
    private String productArea;
    @ApiModelProperty("商品分类 0没有分类 1有分类")
    private Integer isClassed;
    @ApiModelProperty("当前商品分类id  供应链中台填层级id")
    private BigInteger classId;

    @ApiModelProperty("当前商品分类名称")
    private String className;
    @ApiModelProperty("当前商品分类的父类id  供应链中台填父层级id")
    private BigInteger parClassId;
    @ApiModelProperty("商品类型")
    private String ptypeType;
    @ApiModelProperty("保质期类型 0天 1周 2月 3年  没有保质期不用传")
    private Integer protectType;
    @ApiModelProperty("保质期 有保质期必传")
    private int protect;
    @ApiModelProperty("电话/手机")
    private String phone;
    @ApiModelProperty("规格")
    private String specs;
    @ApiModelProperty("sku信息")
    private List<SkuPublishEntity> skuPublishEntityList;
    @ApiModelProperty("支付方式 0货到付款 1在线支付")
    private Integer payPtye;
    @ApiModelProperty("单位信息 供应链中台项目/药师帮平台必传")
    private List<UnitEntity> unitEntity;
    @ApiModelProperty("商品明细介绍")
    private String description;
    @ApiModelProperty("主商品图片信息")
    private List<ProductPic> productPicList;
    @ApiModelProperty("sku和单位信息 中台/药师帮")
    private List<SkuUnitInfo> skuUnitInfoList;
    private Boolean skuPrice;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 商品型号
     */
    @ApiModelProperty("商品型号 药师帮平台必传")
    private String productType;

    public Boolean getSkuPrice() {
        return skuPrice;
    }

    public void setSkuPrice(Boolean skuPrice) {
        this.skuPrice = skuPrice;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductShortName() {
        return productShortName;
    }

    public void setProductShortName(String productShortName) {
        this.productShortName = productShortName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductArea() {
        return productArea;
    }

    public void setProductArea(String productArea) {
        this.productArea = productArea;
    }

    public Integer getIsClassed() {
        return isClassed;
    }

    public void setIsClassed(Integer isClassed) {
        this.isClassed = isClassed;
    }

    public BigInteger getClassId() {
        return classId;
    }

    public void setClassId(BigInteger classId) {
        this.classId = classId;
    }

    public BigInteger getParClassId() {
        return parClassId;
    }

    public void setParClassId(BigInteger parClassId) {
        this.parClassId = parClassId;
    }

    public String getPtypeType() {
        return ptypeType;
    }

    public void setPtypeType(String ptypeType) {
        this.ptypeType = ptypeType;
    }

    public Integer getProtectType() {
        return protectType;
    }

    public void setProtectType(Integer protectType) {
        this.protectType = protectType;
    }

    public int getProtect() {
        return protect;
    }

    public void setProtect(int protect) {
        this.protect = protect;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs;
    }

    public List<SkuPublishEntity> getSkuPublishEntityList() {
        return skuPublishEntityList;
    }

    public void setSkuPublishEntityList(List<SkuPublishEntity> skuPublishEntityList) {
        this.skuPublishEntityList = skuPublishEntityList;
    }

    public Integer getPayPtye() {
        return payPtye;
    }

    public void setPayPtye(Integer payPtye) {
        this.payPtye = payPtye;
    }

    public List<UnitEntity> getUnitEntity() {
        return unitEntity;
    }

    public void setUnitEntity(List<UnitEntity> unitEntity) {
        this.unitEntity = unitEntity;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<ProductPic> getProductPicList() {
        return productPicList;
    }

    public void setProductPicList(List<ProductPic> productPicList) {
        this.productPicList = productPicList;
    }

    public List<SkuUnitInfo> getSkuUnitInfoList() {
        return skuUnitInfoList;
    }

    public void setSkuUnitInfoList(List<SkuUnitInfo> skuUnitInfoList) {
        this.skuUnitInfoList = skuUnitInfoList;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }
}
