package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

import java.util.Arrays;
import java.util.stream.Collectors;

public enum PlatformRefundTypeEnum implements CodeEnum {
    ALL(-1, "全部"),
    REFUND(0, "仅退款"),
    REFUND_GOODS(1, "退款退货"),
    EXCHANGE(2, "换货"),
    RESEND(3, "补发"),
    REFUND_PART(4, "卖家补差价"),
    REFUND_FEE(5,"售后费用");



    private int flag;

    private String name;

    PlatformRefundTypeEnum(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }


    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public static PlatformRefundTypeEnum getEnum(int code) {
        return Arrays.stream(PlatformRefundTypeEnum.values()).filter(item -> {
            return item.getCode() == code;
        }).collect(Collectors.toList()).get(0);
    }
}
