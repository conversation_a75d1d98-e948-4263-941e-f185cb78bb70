package com.wsgjp.ct.sale.platform.dto.order.entity;

import java.util.Date;

/**
 * 自提信息
 */
public class SelfPickupInfo extends SecretSystem{

    /**
     * 自提人名称
     */
    private String selfPickupUser;
    /**
     * 自提人电话
     */
    private String selfPickupMobile;
    /**
     * 自提码
     */
    private String selfPickupCode;
    /**
     * 自提日期
     */
    private Date selfPickupTime;

    /**
     * 自提点名称
     */
    private String selfPickupSiteName;
    /**
     * 自提点id
     */
    private String selfPickupSiteId;
    /**
     * 自提点联系人名称
     */
    private String selfPickupSiteUserName;
    /**
     * 自提点/自提点联系人电话
     */
    private String selfPickupSiteMobile;
    /**
     * 自提点所在省
     */
    private String selfPickupSiteProvince;
    /**
     * 自提点所在市
     */
    private String selfPickupSiteCity;
    /**
     * 自提点所在区/县
     */
    private String selfPickupSiteDistrict;
    /**
     * 自提点所在四级地址
     */
    private String selfPickupSiteArea;
    /**
     * 自提点所在详细地址
     */
    private String selfPickupSiteAddress;

    /**
     *联系人身份证号码
     */
    private String idCard;

    /**
     *联系人身份证姓名
     */
    private String idCardName;

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIdCardName() {
        return idCardName;
    }

    public void setIdCardName(String idCardName) {
        this.idCardName = idCardName;
    }

    public String getSelfPickupUser() {
        return selfPickupUser;
    }

    public void setSelfPickupUser(String selfPickupUser) {
        this.selfPickupUser = selfPickupUser;
    }

    public String getSelfPickupMobile() {
        return selfPickupMobile;
    }

    public void setSelfPickupMobile(String selfPickupMobile) {
        this.selfPickupMobile = selfPickupMobile;
    }

    public String getSelfPickupCode() {
        return selfPickupCode;
    }

    public void setSelfPickupCode(String selfPickupCode) {
        this.selfPickupCode = selfPickupCode;
    }

    public Date getSelfPickupTime() {
        return selfPickupTime;
    }

    public void setSelfPickupTime(Date selfPickupTime) {
        this.selfPickupTime = selfPickupTime;
    }

    public String getSelfPickupSiteName() {
        return selfPickupSiteName;
    }

    public void setSelfPickupSiteName(String selfPickupSiteName) {
        this.selfPickupSiteName = selfPickupSiteName;
    }

    public String getSelfPickupSiteId() {
        return selfPickupSiteId;
    }

    public void setSelfPickupSiteId(String selfPickupSiteId) {
        this.selfPickupSiteId = selfPickupSiteId;
    }

    public String getSelfPickupSiteUserName() {
        return selfPickupSiteUserName;
    }

    public void setSelfPickupSiteUserName(String selfPickupSiteUserName) {
        this.selfPickupSiteUserName = selfPickupSiteUserName;
    }

    public String getSelfPickupSiteMobile() {
        return selfPickupSiteMobile;
    }

    public void setSelfPickupSiteMobile(String selfPickupSiteMobile) {
        this.selfPickupSiteMobile = selfPickupSiteMobile;
    }

    public String getSelfPickupSiteProvince() {
        return selfPickupSiteProvince;
    }

    public void setSelfPickupSiteProvince(String selfPickupSiteProvince) {
        this.selfPickupSiteProvince = selfPickupSiteProvince;
    }

    public String getSelfPickupSiteCity() {
        return selfPickupSiteCity;
    }

    public void setSelfPickupSiteCity(String selfPickupSiteCity) {
        this.selfPickupSiteCity = selfPickupSiteCity;
    }

    public String getSelfPickupSiteDistrict() {
        return selfPickupSiteDistrict;
    }

    public void setSelfPickupSiteDistrict(String selfPickupSiteDistrict) {
        this.selfPickupSiteDistrict = selfPickupSiteDistrict;
    }

    public String getSelfPickupSiteArea() {
        return selfPickupSiteArea;
    }

    public void setSelfPickupSiteArea(String selfPickupSiteArea) {
        this.selfPickupSiteArea = selfPickupSiteArea;
    }

    public String getSelfPickupSiteAddress() {
        return selfPickupSiteAddress;
    }

    public void setSelfPickupSiteAddress(String selfPickupSiteAddress) {
        this.selfPickupSiteAddress = selfPickupSiteAddress;
    }
}
