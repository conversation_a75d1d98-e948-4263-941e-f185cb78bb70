package com.wsgjp.ct.sale.platform.dto.product;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class ProductConfig {
    /**
     * 初始库存
     */
    private BigDecimal qty;
    /**
     * 平台skuId
     */
    private String platformSkuId;
    /**
     * 扩展字段，json对象
     */
    private String attributes;

    public BigDecimal getQty() {
        if (qty == null) {
            qty = BigDecimal.ZERO;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }
}
