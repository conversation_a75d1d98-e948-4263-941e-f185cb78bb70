package com.wsgjp.ct.sale.platform.dto.tmc;


import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import ngp.idgenerator.UId;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * pl_eshop_tms_product_ms表对应实体
 */
public class EshopTmcRefundMsgEntity extends EshopTmcMsgBaseEntity {
    private String tradeOrderId;
    private String refundOrderId;

    public EshopTmcRefundMsgEntity() {
    }

    public EshopTmcRefundMsgEntity(BigInteger profileId,
                                   BigInteger eshopId,
                                   ShopType shopType,
                                   String message,
                                   String tradeId,
                                   String refundId,
                                   RefundStatus refundStatus) {
        this.setId(UId.newId());
        this.setProfileId(profileId);
        this.setEshopId(eshopId);
        this.setShopType(shopType);
        this.setTradeOrderId(tradeId);
        this.setRefundOrderId(refundId);
        this.setMessage(message);
        this.setMsgStatus(0);
        this.setRefundStatus(refundStatus.getCode());
        this.setMsgCreateTime(new Date());
        this.setMsgUpdateTime(new Date());
        this.setCreateTime(new Date());
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public String getRefundOrderId() {
        return refundOrderId;
    }

    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId;
    }
}
