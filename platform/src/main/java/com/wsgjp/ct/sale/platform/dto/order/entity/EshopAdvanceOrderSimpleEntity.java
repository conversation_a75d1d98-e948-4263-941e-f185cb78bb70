package com.wsgjp.ct.sale.platform.dto.order.entity;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2023/02/16/9:51
 * td_orderbill_core
 */
public class EshopAdvanceOrderSimpleEntity {


    private BigInteger profileId;
    private BigInteger vchcode;
    private String tradeOrderId;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }


}
