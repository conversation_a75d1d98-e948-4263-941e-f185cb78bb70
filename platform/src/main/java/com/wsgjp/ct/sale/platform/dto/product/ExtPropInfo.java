package com.wsgjp.ct.sale.platform.dto.product;


import com.wsgjp.ct.sale.common.enums.publish.InputTypeEnum;

import java.util.List;

/**
 * 拓展属性信息
 */
public class ExtPropInfo {
    /**
     * 属性id
     */
    private String propId;
    /**
     * 属性名称
     */
    private String propName;
    /**
     * 分类id
     */
    private String cid;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 排序标记
     */
    private Integer orderSort;
    /**
     * 是否必填
     */
    private Integer isRequired;
    /**
     * 是否保护
     */
    private Integer isShield;
    /**
     * 是否被搜寻出
     */
    private Integer isSearch;
    /**
     * 是否是关键属性
     */
    private Integer isKeyProperty;
    /**
     * 是否定制
     */
    private Integer isCustom;
    /**
     * 是否默认选中
     */
    private Integer selected;
    /**
     * 数量
     */
    private Integer colNum;
    /**
     * 是否有效(1有效,0无效)
     */
    private Integer yn;
    /**
     * 分组id
     */
    private String groupId;
    /**
     * 类型（选择或者输入）
     */
    private InputTypeEnum inputType;
    /**
     * 属性值别名
     */
    private String attrAlias;
    /**
     * 属性值单位
     */
    private String valUnit;
    /**
     * 维护备注/示例
     */
    private String maintainRemark;
    /**
     * 扩展属性值
     */
    private List<ExtPropValue> extPropValues;
    /**
     * 属性多选限制个数
     */
    private Integer valCount;
    /**
     * 级联组id
     */
    private String cascadeGroupId;
    /**
     * 级联组名称
     */
    private String cascadeGroupName;
    /**
     * 级联组级别
     */
    private Integer cascadeGroupLevel;
    /**
     * 地域等级
     */
    private String inputAreaLevel;
    /**
     * 级联属性
     */
    private List<ExtPropInfo> cascadePropList;
    /**
     * 映射inputType
     */
    private Integer originInputType;

    public String getPropId() {
        return propId;
    }

    public void setPropId(String propId) {
        this.propId = propId;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getPropName() {
        return propName;
    }

    public void setPropName(String propName) {
        this.propName = propName;
    }

    public String getCid() {
        return cid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }

    public Integer getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    public Integer getIsShield() {
        return isShield;
    }

    public void setIsShield(Integer isShield) {
        this.isShield = isShield;
    }

    public Integer getIsSearch() {
        return isSearch;
    }

    public void setIsSearch(Integer isSearch) {
        this.isSearch = isSearch;
    }

    public Integer getIsKeyProperty() {
        return isKeyProperty;
    }

    public void setIsKeyProperty(Integer isKeyProperty) {
        this.isKeyProperty = isKeyProperty;
    }

    public Integer getIsCustom() {
        return isCustom;
    }

    public void setIsCustom(Integer isCustom) {
        this.isCustom = isCustom;
    }

    public Integer getSelected() {
        return selected;
    }

    public void setSelected(Integer selected) {
        this.selected = selected;
    }

    public Integer getColNum() {
        return colNum;
    }

    public void setColNum(Integer colNum) {
        this.colNum = colNum;
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public InputTypeEnum getInputType() {
        return inputType;
    }

    public void setInputType(InputTypeEnum inputType) {
        this.inputType = inputType;
    }

    public String getAttrAlias() {
        return attrAlias;
    }

    public void setAttrAlias(String attrAlias) {
        this.attrAlias = attrAlias;
    }

    public String getValUnit() {
        return valUnit;
    }

    public void setValUnit(String valUnit) {
        this.valUnit = valUnit;
    }

    public String getMaintainRemark() {
        return maintainRemark;
    }

    public void setMaintainRemark(String maintainRemark) {
        this.maintainRemark = maintainRemark;
    }

    public List<ExtPropValue> getExtPropValues() {
        return extPropValues;
    }

    public void setExtPropValues(List<ExtPropValue> extPropValues) {
        this.extPropValues = extPropValues;
    }

    public Integer getValCount() {
        return valCount;
    }

    public void setValCount(Integer valCount) {
        this.valCount = valCount;
    }

    public String getCascadeGroupId() {
        return cascadeGroupId;
    }

    public void setCascadeGroupId(String cascadeGroupId) {
        this.cascadeGroupId = cascadeGroupId;
    }

    public String getCascadeGroupName() {
        return cascadeGroupName;
    }

    public void setCascadeGroupName(String cascadeGroupName) {
        this.cascadeGroupName = cascadeGroupName;
    }

    public Integer getCascadeGroupLevel() {
        return cascadeGroupLevel;
    }

    public void setCascadeGroupLevel(Integer cascadeGroupLevel) {
        this.cascadeGroupLevel = cascadeGroupLevel;
    }

    public String getInputAreaLevel() {
        return inputAreaLevel;
    }

    public void setInputAreaLevel(String inputAreaLevel) {
        this.inputAreaLevel = inputAreaLevel;
    }

    public List<ExtPropInfo> getCascadePropList() {
        return cascadePropList;
    }

    public void setCascadePropList(List<ExtPropInfo> cascadePropList) {
        this.cascadePropList = cascadePropList;
    }

    public Integer getOriginInputType() {
        return originInputType;
    }

    public void setOriginInputType(Integer originInputType) {
        this.originInputType = originInputType;
    }
}
