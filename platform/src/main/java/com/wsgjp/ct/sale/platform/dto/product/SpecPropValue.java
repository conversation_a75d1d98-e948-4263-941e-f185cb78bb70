package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SpecPropValue {
    /**
     * 值id
     */
    private String valueId;
    /**
     * 排序
     */
    private Integer orderSort;
    /**
     * 是否选中
     */
    private boolean selected;
    /**
     * 名称
     */
    private String valueName;
    /**
     * 主要填写百分比数，多输入前缀如长，宽，高
     */
    private String vremark;
    /**
     * 模板单位
     */
    private List<String[]> units;
    /**
     * 选中单位
     */
    private String unit;
    /**
     * 父属性值Id
     */
    private Long parentValueId;

    public String getValueId() {
        return valueId;
    }

    public void setValueId(String valueId) {
        this.valueId = valueId;
    }

    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getValueName() {
        return valueName;
    }

    public void setValueName(String valueName) {
        this.valueName = valueName;
    }

    public String getVremark() {
        return vremark;
    }

    public void setVremark(String vremark) {
        this.vremark = vremark;
    }

    public List<String[]> getUnits() {
        return units;
    }

    public void setUnits(List<String[]> units) {
        this.units = units;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getParentValueId() {
        return parentValueId;
    }

    public void setParentValueId(Long parentValueId) {
        this.parentValueId = parentValueId;
    }
}
