package com.wsgjp.ct.sale.platform.dto.order.entity;

/**
 * <AUTHOR>
 */
public class SecretSystem {
    private String di;
    private String accountDicId;
    private String receiverDicId;
    private String mobileDicId;
    private String phoneDicId;
    private String payAccountDicId;
    private String addressId;
    private String idCardNameId;
    private String idCardId;
    private String freightBillNoId;

    public String getAccountDicId() {
        return accountDicId;
    }

    public void setAccountDicId(String accountDicId) {
        this.accountDicId = accountDicId;
    }

    public String getReceiverDicId() {
        return receiverDicId;
    }

    public void setReceiverDicId(String receiverDicId) {
        this.receiverDicId = receiverDicId;
    }

    public String getMobileDicId() {
        return mobileDicId;
    }

    public void setMobileDicId(String mobileDicId) {
        this.mobileDicId = mobileDicId;
    }

    public String getPhoneDicId() {
        return phoneDicId;
    }

    public void setPhoneDicId(String phoneDicId) {
        this.phoneDicId = phoneDicId;
    }

    public String getPayAccountDicId() {
        return payAccountDicId;
    }

    public void setPayAccountDicId(String payAccountDicId) {
        this.payAccountDicId = payAccountDicId;
    }

    public String getAddressId() {
        return addressId;
    }

    public void setAddressId(String addressId) {
        this.addressId = addressId;
    }

    public String getIdCardNameId() {
        return idCardNameId;
    }

    public void setIdCardNameId(String idCardNameId) {
        this.idCardNameId = idCardNameId;
    }

    public String getIdCardId() {
        return idCardId;
    }

    public void setIdCardId(String idCardId) {
        this.idCardId = idCardId;
    }

    public String getFreightBillNoId() {
        return freightBillNoId;
    }

    public void setFreightBillNoId(String freightBillNoId) {
        this.freightBillNoId = freightBillNoId;
    }

    public String getDi() {
        return di;
    }

    public void setDi(String di) {
        this.di = di;
    }
}
