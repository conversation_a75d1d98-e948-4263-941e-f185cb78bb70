package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;

/**
 * 店铺退货地址
 *
 * <AUTHOR>
 */
public class ShopRefundAddress extends ErpBaseInfo {


    /**
     * 退货地址ID
     */
    private String id;

    /**
     * 是否为默认退货地址
     */
    private String isDefault;

    /**
     * 退货收件人名字
     */
    private String receiverName;

    /**
     * 退货收件人手机号
     */
    private String receiverPhone;

    /**
     * 退货收件人固定电话
     */
    private String receiverTel;

    /**
     * 退货地址所在城市ID
     */
    private Integer cityId;

    /**
     * 退货地址所在城市名字
     */
    private String cityName;

    /**
     * 退货地址所在区ID
     */
    private Integer districtId;

    /**
     * 退货地址所在区名字
     */
    private String districtName;

    /**
     * 退货地址所在省份ID
     */
    private Integer provinceId;

    /**
     * 退货地址所在省份名字
     */
    private String provinceName;

    private String townName;
    private String townId;

    /**
     * 退货地址 详细地址
     */
    private String refundAddress;

    /**
     * 平台退货地址id
     */
    private String refundAddressId;

    /**
     * 退货邮编
     */
    private String refundZipCod;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家编码
     */
    private String countryCode;

    public String getRefundZipCod() {
        return refundZipCod;
    }

    public void setRefundZipCod(String refundZipCod) {
        this.refundZipCod = refundZipCod;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getDistrictId() {
        return districtId;
    }

    public void setDistrictId(Integer districtId) {
        this.districtId = districtId;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getRefundAddress() {
        return refundAddress;
    }

    public void setRefundAddress(String refundAddress) {
        this.refundAddress = refundAddress;
    }

    public String getRefundAddressId() {
        return refundAddressId;
    }

    public void setRefundAddressId(String refundAddressId) {
        this.refundAddressId = refundAddressId;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverTel() {
        return receiverTel;
    }

    public void setReceiverTel(String receiverTel) {
        this.receiverTel = receiverTel;
    }

    public String getTownName() {
        return townName;
    }

    public void setTownName(String townName) {
        this.townName = townName;
    }

    public String getTownId() {
        return townId;
    }

    public void setTownId(String townId) {
        this.townId = townId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
}
