package com.wsgjp.ct.sale.platform.dto.tmc;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class EshopTmcMsgBaseEntity {
    private BigInteger id;
    private BigInteger profileId;
    private ShopType shopType;
    private BigInteger eshopId;
    private String message;
    /**
     * 是否被消费：0未消费 1已消费 2消费异常.99账单（类似接口调用下载）
     */
    private Integer msgStatus;
    private Date msgCreateTime;
    private Date msgUpdateTime;
    private Date msgConsumptionTime;
    private Date createTime;
    private Date updateTime;
    // 平台售后状态(0:没有退款,1:等待卖家同意,2:等待买家处理,3:卖家同意,4:卖家拒绝,5:退款成功,6:退款取消)
    private Integer refundStatus = 0;

    public Integer getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(Integer refundStatus) {
        this.refundStatus = refundStatus;
    }

    public void setRefundStatus(RefundStatus refundStatus) {
        if (refundStatus != null) {
            this.refundStatus = refundStatus.getCode();
        }
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getMsgStatus() {
        return msgStatus;
    }

    public void setMsgStatus(Integer msgStatus) {
        this.msgStatus = msgStatus;
    }

    public Date getMsgCreateTime() {
        return msgCreateTime;
    }

    public void setMsgCreateTime(Date msgCreateTime) {
        this.msgCreateTime = msgCreateTime;
    }

    public Date getMsgUpdateTime() {
        return msgUpdateTime;
    }

    public void setMsgUpdateTime(Date msgUpdateTime) {
        this.msgUpdateTime = msgUpdateTime;
    }

    public Date getMsgConsumptionTime() {
        return msgConsumptionTime;
    }

    public void setMsgConsumptionTime(Date msgConsumptionTime) {
        this.msgConsumptionTime = msgConsumptionTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
