package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StandardProduct {
    /**
     * 标品ID
     */
    private String spuId;
    /**
     * 标品名称
     */
    private String spuName;
    /**
     * 父级标品ID
     */
    private String parentSpuId;
    /**
     * 特殊属性
     */
    private List<FeatureInfo> features;
    /**
     * 标品属性
     */
    private String saleAttr;

    public String getSpuId() {
        return spuId;
    }

    public void setSpuId(String spuId) {
        this.spuId = spuId;
    }

    public String getSpuName() {
        return spuName;
    }

    public void setSpuName(String spuName) {
        this.spuName = spuName;
    }

    public String getParentSpuId() {
        return parentSpuId;
    }

    public void setParentSpuId(String parentSpuId) {
        this.parentSpuId = parentSpuId;
    }

    public List<FeatureInfo> getFeatures() {
        return features;
    }

    public void setFeatures(List<FeatureInfo> features) {
        this.features = features;
    }

    public String getSaleAttr() {
        return saleAttr;
    }

    public void setSaleAttr(String saleAttr) {
        this.saleAttr = saleAttr;
    }
}
