package com.wsgjp.ct.sale.platform.dto.order.entity;

import java.math.BigInteger;
import java.util.*;

public class QueryAdvanceOrderParameter {
    private String tableName = "td_orderbill_core `core`";
    private List<String> tradeOrderIds;
    private BigInteger profileId;
    private List<BigInteger> otypeIds;

    public List<String> getTradeOrderIds() {
        return tradeOrderIds;
    }

    public void setTradeOrderIds(List<String> tradeOrderIds) {
        this.tradeOrderIds = tradeOrderIds;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public List<BigInteger> getOtypeIds() {
        return otypeIds;
    }

    public void setOtypeIds(List<BigInteger> otypeIds) {
        this.otypeIds = otypeIds;
    }

    public String getTableName() {
        return tableName;
    }


}
