package com.wsgjp.ct.sale.platform.dto.stock;


import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import com.wsgjp.ct.sale.platform.entity.entities.upload.Contactor;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 仓库实体基础信息
 */
public class BaseWareHouseInfo extends ErpBaseInfo {
    /**
     * 仓库编码
     */
    private String storeCode;
    /**
     * 仓库名称
     */
    private String storeName;

    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 简称
     */
    private String aliasName;
    /**
     * 仓库ID,唯一键
     */
    private BigInteger ktypeId;
    /**
     * 联系人
     */
    private List<Contactor> contactors;
    /**
     * 停用状态
     */
    private Boolean isStopped;
    /**
     * 乡镇,四级地址
     */
    private String town;

    /**
     * 邮编
     */
    private String postCode;


    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public List<Contactor> getContactors() {
        if (contactors == null) {
            contactors = new ArrayList<>();
        }
        return contactors;
    }

    public void setContactors(List<Contactor> contactors) {
        this.contactors = contactors;
    }

    public Boolean getStopped() {
        return isStopped;
    }

    public void setStopped(Boolean stopped) {
        isStopped = stopped;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }
}
