package com.wsgjp.ct.sale.platform.dto.refund.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PlatformRefundState implements CodeEnum {

    ALL(-1,"全部"),
    NONE(0,"无需退款"),
    YES(1,"确认退款"),
    NO(2,"拒绝退款");


    private final int flag;

    private final String name;

    PlatformRefundState(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
