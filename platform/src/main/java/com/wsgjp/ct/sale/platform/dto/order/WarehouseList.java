package com.wsgjp.ct.sale.platform.dto.order;


import com.wsgjp.ct.sale.platform.dto.order.entity.ReceiverInfo;

import java.util.List;

public class WarehouseList {
    /**
     * 仓库id
     */
    private String warehouseCode;
    /**
     * 仓库名字
     */
    private String warehouseName;
    /**
     * 仓库收货信息
     */
    private ReceiverInfo receiverInfo;
    private String fullAddress;
    public String getFullAddress() {
        if(this.getReceiverInfo() == null)
        {
            return "";
        }
        return String.format("%s %s %s%s%s%s%s",this.getReceiverInfo().getReceiver(),this.getReceiverInfo().getCustomerMobile(),
                this.getReceiverInfo().getCustomerProvince(),this.getReceiverInfo().getCustomerCity(),this.getReceiverInfo().getCustomerDistrict(),
                this.getReceiverInfo().getCustomerTown(),this.getReceiverInfo().getCustomerAddress());
    }
    private List<AgencyListItem> agencyListItems;

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public ReceiverInfo getReceiverInfo() {
        return receiverInfo;
    }

    public void setReceiverInfo(ReceiverInfo receiverInfo) {
        this.receiverInfo = receiverInfo;
    }

    public List<AgencyListItem> getAgencyListItems() {
        return agencyListItems;
    }

    public void setAgencyListItems(List<AgencyListItem> agencyListItems) {
        this.agencyListItems = agencyListItems;
    }
}
