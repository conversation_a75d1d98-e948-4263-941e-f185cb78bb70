package com.wsgjp.ct.sale.platform.dto.order.entity;

public class MainPlatformJsonEntity {
    private AlibabaSecrecy alibabaSecrecy;
    /**
     * 唯品会MP：是否可打印电子面单
     */
    private Byte isPrint;
    /**
     * 唯品会MP：合包识别码
     */
    private String hebaoIdentificationCode;
    /**
     * 往来单位id  如果这个字段有值，会直接使用 这个id 做为订单的btypeId
     */
    private String btypeId;

    /**
     *  发货保障时长(单位小时)
     */
    private Integer sendTimeLimit;

    private String distributionMode;

    public String getDistributionMode() {
        return distributionMode;
    }

    public void setDistributionMode(String distributionMode) {
        this.distributionMode = distributionMode;
    }

    public AlibabaSecrecy getAlibabaSecrecy() {
        return alibabaSecrecy;
    }

    public MainPlatformJsonEntity setAlibabaSecrecy(AlibabaSecrecy alibabaSecrecy) {
        this.alibabaSecrecy = alibabaSecrecy;
        return this;
    }

    public static class AlibabaSecrecy {
        private String WgSenderName = "";
        private String WgSenderPhone = "";

        public String getWgSenderName() {
            return WgSenderName;
        }

        public AlibabaSecrecy setWgSenderName(String wgSenderName) {
            WgSenderName = wgSenderName;
            return this;
        }

        public String getWgSenderPhone() {
            return WgSenderPhone;
        }

        public AlibabaSecrecy setWgSenderPhone(String wgSenderPhone) {
            WgSenderPhone = wgSenderPhone;
            return this;
        }
    }

    public Byte getIsPrint() {
        return isPrint;
    }

    public void setIsPrint(Byte isPrint) {
        this.isPrint = isPrint;
    }

    public String getHebaoIdentificationCode() {
        return hebaoIdentificationCode;
    }

    public void setHebaoIdentificationCode(String hebaoIdentificationCode) {
        this.hebaoIdentificationCode = hebaoIdentificationCode;
    }

    public String getBtypeId() {
        return btypeId;
    }

    public void setBtypeId(String btypeId) {
        this.btypeId = btypeId;
    }

    public Integer getSendTimeLimit() {
        return sendTimeLimit;
    }

    public void setSendTimeLimit(Integer sendTimeLimit) {
        this.sendTimeLimit = sendTimeLimit;
    }
}
