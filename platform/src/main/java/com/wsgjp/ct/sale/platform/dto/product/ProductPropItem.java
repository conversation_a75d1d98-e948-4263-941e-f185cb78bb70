package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.common.enums.publish.AttrDimEnum;
import com.wsgjp.ct.sale.common.enums.publish.BusinessTypeEnum;
import com.wsgjp.ct.sale.common.enums.publish.InputTypeEnum;

import java.util.List;

/**
 * 商品属性实体
 * 销售属性、规格属性、扩展属性等平台属性
 * <AUTHOR>
 */
public class ProductPropItem {
    /**
     * 属性Id
     */
    private String propId;
    /**
     * 属性名
     */
    private String propName;
    /**
     * 属性别名
     */
    private String propAlias;
    /**
     * 属性排序
     */
    private int sort;
    /**
     * 属性维度
     */
    private AttrDimEnum dim;
    /**
     * 属性类型
     */
    private BusinessTypeEnum propType;
    /**
     * 输入类型
     */
    private InputTypeEnum inputType;
    /**
     * 属性值列表
     */
    private List<ProductPropItemValue> propItemValues;
    /**
     * 属性扩展字段
     */
    private String attributes;

    public String getPropId() {
        return propId;
    }

    public void setPropId(String propId) {
        this.propId = propId;
    }

    public String getPropName() {
        return propName;
    }

    public void setPropName(String propName) {
        this.propName = propName;
    }

    public String getPropAlias() {
        return propAlias;
    }

    public void setPropAlias(String propAlias) {
        this.propAlias = propAlias;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public AttrDimEnum getDim() {
        return dim;
    }

    public void setDim(AttrDimEnum dim) {
        this.dim = dim;
    }

    public void setInputType(InputTypeEnum inputType) {
        this.inputType = inputType;
    }

    public List<ProductPropItemValue> getPropItemValues() {
        return propItemValues;
    }

    public void setPropItemValues(List<ProductPropItemValue> propItemValues) {
        this.propItemValues = propItemValues;
    }

    public BusinessTypeEnum getPropType() {
        return propType;
    }

    public void setPropType(BusinessTypeEnum propType) {
        this.propType = propType;
    }

    public InputTypeEnum getInputType() {
        return inputType;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }
}
