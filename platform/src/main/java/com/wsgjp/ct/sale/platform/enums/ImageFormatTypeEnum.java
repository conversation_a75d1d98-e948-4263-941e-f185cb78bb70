package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum ImageFormatTypeEnum implements CodeEnum {
    MAIN_IMAGE(0,"主图"),
    LONG_IMAGE(1,"长图"),
    DETAIL_IMAGE(2,"详情图"),
    ACTIVITY_IMAGE(3,"活动图"),
    IMAGE_34(4,"3:4图");

    private final int index;
    private final String name;

    ImageFormatTypeEnum(int index,String name){
        this.index=index;
        this.name=name;
    }
    public static ImageFormatTypeEnum getEnumByCode(int code) {
        for (ImageFormatTypeEnum imageFormatEnum : ImageFormatTypeEnum.values()) {
            if (code == imageFormatEnum.getCode()) {
                return imageFormatEnum;
            }
        }
        return null;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}
