package com.wsgjp.ct.sale.platform.utils;

import com.google.common.collect.Lists;
import com.wsgjp.ct.sale.platform.enums.ErrorCodeEnum;
import com.wsgjp.ct.sale.platform.enums.ErrorLevelEnum;
import com.wsgjp.ct.sale.platform.enums.ServiceModuleEnum;
import ngp.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-20 17:30
 */
public class Common {
	/**
	 * 拆分集合
	 *
	 * @param <T>           泛型对象
	 * @param resList       需要拆分的集合
	 * @param subListLength 每个子集合的元素个数
	 * @return 返回拆分后的各个集合组成的列表
	 **/
	public static <T> List<List<T>> splitList(List<T> resList, int subListLength) {
		if (CollectionUtils.isEmpty(resList) || subListLength <= 0) {
			return Lists.newArrayList();
		}
		List<List<T>> ret = Lists.newArrayList();
		int size = resList.size();
		if (size <= subListLength) {
			// 数据量不足 subListLength 指定的大小
			ret.add(resList);
		} else {
			int pre = size / subListLength;
			int last = size % subListLength;
			// 前面pre个集合，每个大小都是 subListLength 个元素
			for (int i = 0; i < pre; i++) {
				List<T> itemList = Lists.newArrayList();
				for (int j = 0; j < subListLength; j++) {
					itemList.add(resList.get(i * subListLength + j));
				}
				ret.add(itemList);
			}
			// last的进行处理
			if (last > 0) {
				List<T> itemList = Lists.newArrayList();
				for (int i = 0; i < last; i++) {
					itemList.add(resList.get(pre * subListLength + i));
				}
				ret.add(itemList);
			}
		}
		return ret;
	}

	public static String CombineErrCode(ErrorLevelEnum Sys, ServiceModuleEnum ser, ErrorCodeEnum error) {
		return String.format("%s%s%s", Sys.getUserCode(), ser.getUserCode(), error.getUserCode());
	}

	public static String trimEnd(String str, char ch) {
		try {
			if (StringUtils.isEmpty(str)) {
				return str;
			}
			int index = str.lastIndexOf(ch);
			if (index == str.length() - 1) {
				return str.substring(0,index);
			}
			return str;
		} catch (Exception ignore) {
		}
		return str;
	}


	public static String trimStart(String inStr, String prefix) {
		if(StringUtils.isEmpty(inStr) || StringUtils.isEmpty(prefix) || !inStr.contains(prefix)){
			return inStr;
		}
		return inStr.startsWith(prefix) ? inStr.substring(prefix.length()) : inStr;
	}


	public static String getMD5Str(String str) {
		try {

			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(str.getBytes());
			// digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
			// BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
			return new BigInteger(1, md.digest()).toString(16);
		} catch (Exception e) {
			throw new RuntimeException("MD5加密出现错误，" + e.toString());
		}
	}

	public static Date strToDate(String str) {
		if (StringUtils.isEmpty(str)) {
			return new Date();
		}
		//注意月份是MM
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			return simpleDateFormat.parse(str);
		} catch (Exception ex) {
			throw new RuntimeException("时间格式转换出错：" + str);
		}
	}
}
