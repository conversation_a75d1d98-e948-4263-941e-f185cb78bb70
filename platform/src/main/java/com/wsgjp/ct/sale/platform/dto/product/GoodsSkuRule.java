package com.wsgjp.ct.sale.platform.dto.product;

public class GoodsSkuRule {
    /**
     * 团购价最高差倍率
     */
    private double priceRangeRatio;

    /**
     * 同个商品下规格值的加和数量上限
     */
    private int specNumLimit;

    public double getPriceRangeRatio() {
        return priceRangeRatio;
    }

    public void setPriceRangeRatio(double priceRangeRatio) {
        this.priceRangeRatio = priceRangeRatio;
    }

    public int getSpecNumLimit() {
        return specNumLimit;
    }

    public void setSpecNumLimit(int specNumLimit) {
        this.specNumLimit = specNumLimit;
    }
}
