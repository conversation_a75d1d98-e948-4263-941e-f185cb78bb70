package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2020-01-02 10:28
 */
public enum InvoiceType implements CodeEnum {
    /**
     * 电子发票
     */
    PERSONAL(0,"个人"),
    ENTERPRISE(1,"企业");
    private int code;
    private String name;


    InvoiceType(int code, String name){
        this.code=code;
        this.name=name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName(){
        return name;
    }

    public static InvoiceType nameValueOf(String name) {
        for (InvoiceType invoiceType : values()) {
            if (name.equals(invoiceType.getName())) {
                return invoiceType;
            }
        }
        return null;
    }
}
