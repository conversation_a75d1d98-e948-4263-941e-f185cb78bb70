package com.wsgjp.ct.sale.platform.dto.notice;

import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RefundStateChangeRequest {
    private String tradeOrderId;
    private List<String> oidList;
    private RefundStatus refundStatus;
    private UpdateOrderRefundType updateOrderRefundType;
    private BigInteger shopId;


    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public List<String> getOidList() {
        return oidList;
    }

    public void setOidList(List<String> oidList) {
        this.oidList = oidList;
    }

    public RefundStatus getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(RefundStatus refundStatus) {
        this.refundStatus = refundStatus;
    }

    public UpdateOrderRefundType getUpdateOrderRefundType() {
        return updateOrderRefundType;
    }

    public void setUpdateOrderRefundType(UpdateOrderRefundType updateOrderRefundType) {
        this.updateOrderRefundType = updateOrderRefundType;
    }

    public BigInteger getShopId() {
        return shopId;
    }

    public void setShopId(BigInteger shopId) {
        this.shopId = shopId;
    }
}
