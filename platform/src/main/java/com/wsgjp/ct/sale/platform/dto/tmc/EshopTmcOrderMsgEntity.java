package com.wsgjp.ct.sale.platform.dto.tmc;


import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;

/**
 * <AUTHOR>
 * pl_eshop_tms_order_msg 表对应实体
 */
public class EshopTmcOrderMsgEntity extends EshopTmcMsgBaseEntity {
    /**
     * 用于账单接口时是账单id
     */
    private String tradeOrderId;
    private TradeStatus tradeStatus;
    private String refundOrderId;
    /**
     * 账单下载时用 账单对应的订单id，查询pl_eshop_notify_change里的content
     */
    private String statementBillByTradeId;

    public String getStatementBillByTradeId() {
        return statementBillByTradeId;
    }

    public void setStatementBillByTradeId(String statementBillByTradeId) {
        this.statementBillByTradeId = statementBillByTradeId;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public String getRefundOrderId() {
        return refundOrderId;
    }

    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId;
    }
}
