package com.wsgjp.ct.sale.platform.dto.purchase;

import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 采购订单详情请求参数信息
 * @author: lj
 * @create: 2022-03-11
 **/
public class PurchaseOrderRequestParam {

    @ApiModelProperty("订单号")
    private String tradeId;

    @ApiModelProperty("订单状态")
    private String status;

    public String getTradeId() {
        if (StringUtils.isEmpty(tradeId)) {
            tradeId = "";
        }
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getStatus() {
        if(StringUtils.isEmpty(status)){
            status="";
        }
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
