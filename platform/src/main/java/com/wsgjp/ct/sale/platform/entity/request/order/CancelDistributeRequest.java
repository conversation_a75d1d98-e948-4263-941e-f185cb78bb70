package com.wsgjp.ct.sale.platform.entity.request.order;

import com.wsgjp.ct.sale.platform.dto.order.CancelDistributeParam;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiOperation("取消分单请求类")
public class CancelDistributeRequest extends BaseRequest {

    @ApiModelProperty("取消分单请求参数")
    List<CancelDistributeParam> canceDistributeParams;

    public List<CancelDistributeParam> getCanceDistributeParams() {
        return canceDistributeParams;
    }

    public void setCanceDistributeParams(List<CancelDistributeParam> canceDistributeParams) {
        this.canceDistributeParams = canceDistributeParams;
    }
}
