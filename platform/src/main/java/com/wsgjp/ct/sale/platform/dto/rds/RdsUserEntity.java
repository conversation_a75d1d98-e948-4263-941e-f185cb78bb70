package com.wsgjp.ct.sale.platform.dto.rds;


import com.wsgjp.ct.sale.platform.enums.RdsStatus;

public class RdsUserEntity {

    /**
     * 网店账号或者线上店铺id
     */
    private String shopAccount;
    private String rdsName;
    private RdsStatus status;
    private Long UserId;

    public String getShopAccount() {
        return shopAccount;
    }

    public void setShopAccount(String shopAccount) {
        this.shopAccount = shopAccount;
    }

    public String getRdsName() {
        return rdsName;
    }

    public void setRdsName(String rdsName) {
        this.rdsName = rdsName;
    }

    public RdsStatus getStatus() {
        return status;
    }

    public void setStatus(RdsStatus status) {
        this.status = status;
    }

    public Long getUserId() {
        return UserId;
    }

    public void setUserId(Long userId) {
        UserId = userId;
    }
}
