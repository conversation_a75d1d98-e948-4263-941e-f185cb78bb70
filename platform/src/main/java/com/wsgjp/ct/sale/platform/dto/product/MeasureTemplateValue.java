package com.wsgjp.ct.sale.platform.dto.product;

import com.doudian.open.api.product_getProductUpdateRule.data.ValidateRule;
import com.wsgjp.ct.sale.common.enums.publish.InputTypeEnum;

import java.util.List;

public class MeasureTemplateValue {
    /**
     * 输入类型
     */
    private InputTypeEnum inputTypeEnum;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * 后缀
     */
    private String suffix;

    /**
     * 下拉选项时提供选择
     */
    private List<Units> values;

    /**
     * 单位
     */
    private List<Units> units;

    /**
     * 参数规则
     */
    private ValidateRule validateRule;

    private String moduleId;

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public ValidateRule getValidateRule() {
        return validateRule;
    }

    public void setValidateRule(ValidateRule validateRule) {
        this.validateRule = validateRule;
    }

    public InputTypeEnum getInputTypeEnum() {
        return inputTypeEnum;
    }

    public void setInputTypeEnum(InputTypeEnum inputTypeEnum) {
        this.inputTypeEnum = inputTypeEnum;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public List<Units> getValues() {
        return values;
    }

    public void setValues(List<Units> values) {
        this.values = values;
    }

    public List<Units> getUnits() {
        return units;
    }

    public void setUnits(List<Units> units) {
        this.units = units;
    }
}
