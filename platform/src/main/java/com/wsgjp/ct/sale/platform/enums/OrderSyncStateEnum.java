package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum OrderSyncStateEnum implements CodeEnum {
    /**
     * 自动下单任务状态
     */
    PREPARE(0,"待执行"),
    SUCCESS(1, "成功"),
    FAILURE(2, "失败");


    private int code;
    private String name;
    OrderSyncStateEnum(int code, String name){
        this.code=code;
        this.name=name;
    }
    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}

