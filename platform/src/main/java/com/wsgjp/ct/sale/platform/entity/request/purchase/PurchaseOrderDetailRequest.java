package com.wsgjp.ct.sale.platform.entity.request.purchase;


import com.wsgjp.ct.sale.platform.dto.purchase.PurchaseOrderRequestParam;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 采购订单详情请求request入参
 * @date 2022-01-13
 */
@ApiModel("获取采购单详情请求对象")
public class PurchaseOrderDetailRequest extends BaseRequest {
    /**
     * 采购订单参数
     */
    @ApiModelProperty("采购单请求参数列表")
    List<PurchaseOrderRequestParam> orderParams;

    public List<PurchaseOrderRequestParam> getOrderParams() {
        return orderParams;
    }

    public void setOrderParams(List<PurchaseOrderRequestParam> orderParams) {
        this.orderParams = orderParams;
    }
}
