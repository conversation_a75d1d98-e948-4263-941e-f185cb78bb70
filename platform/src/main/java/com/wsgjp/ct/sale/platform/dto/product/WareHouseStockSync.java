package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.platform.enums.EshopWareHouseType;

/**
 * @Description: 分仓同步库存数量
 * <AUTHOR> zxpeng
 * @Date: 2025-07-16 9:54
 */
public class WareHouseStockSync {
    /**
     * 同步数量
     */
    private Long syncQty;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    private String warehouseName;

    private EshopWareHouseType warehouseType;


    public Long getSyncQty() {
        return syncQty;
    }

    public void setSyncQty(Long syncQty) {
        this.syncQty = syncQty;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public EshopWareHouseType getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(EshopWareHouseType warehouseType) {
        this.warehouseType = warehouseType;
    }
}
