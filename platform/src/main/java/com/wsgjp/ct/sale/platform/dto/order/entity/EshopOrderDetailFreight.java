package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.Md5Utils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

public class EshopOrderDetailFreight {
    private BigInteger id;
    private String tradeOrderId;
    private BigInteger profileId;
    private BigInteger otypeId;
    private String tradeOrderDetailId;
    private String freightName;
    private String freightBillNo;
    /**
     *扩展信息_josn_-key/value_(序列号、批次、ime码、其它)
     */
    private String extendInfo;
    /**
     *生成方式（线上下载、本地新增）
     * 0--线上下载
     * 1--本地新增
     */
    private int createType;
    /**
     *发货数量(本地同步用)
     */
    private BigDecimal sendQty;
    /**
     *同步状态(本地同步用)
     *     NONE(0, "未同步"),
     *     SUCCESS(1, "同步成功"),
     *     ING(2, "同步中"),
     *     FAIL(3, "同步失败"),
     *     PART_SUCCESS(4, "部分同步成功"),
     *     NOT_NEED(5, "无需同步"),
     *     PROCESSED(6, "已核对处理"),
     *     NEED_RETRY(7, "需要重试"),
     *     IN_EXECUTE(8, "执行中");
     */
    private int syncState;
    /**
     *同步类型(本地同步用)
     */
    private int syncType;
    /**
     *是否拆分(本地同步用)
     */
    private boolean hasSplit;
    /**
     *是否第一单(本地同步用)
     */
    private boolean hasFirst;
    /**
     *是否最后一单(本地同步用)
     */
    private boolean hasLast;
    /**
     *唯一hash（profile_id,trade_order_id,otype_id,trade_order_detail_id,freight_name,freight_bill_no,freight_code）
     */
    private String hashKey;
    private String freightCode;
    private Date createTime;
    private Date updateTime;

    /**
     *
     * 以下字段:platformSkuId,platformPtypeId,platformPtypeName,platformXcode,totalQty,
     * syncOrderSequence,syncOrderDetailSequence
     * 不存库,业务需要时自行补充
     *
     * */
    private String platformSkuId;
    private String platformPtypeId;
    private String platformPtypeName;
    private String platformXcode;
    private BigDecimal totalQty;

    /**
     * 在明细的同步顺序
     */
    private Integer syncOrderSequence;
    /**
     * 在明细拆分的同步顺序
     */
    private Integer syncOrderDetailSequence;


    public BigInteger getId() {
        if (null == id){
            return BigInteger.ZERO;
        }
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTradeOrderId() {
        if (null == tradeOrderId){
            return "";
        }
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public BigInteger getProfileId() {
        if (null == profileId){
            return CurrentUser.getProfileId();
        }
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        if (null == otypeId){
            return BigInteger.ZERO;
        }
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getTradeOrderDetailId() {
        if (null == tradeOrderDetailId){
            return "";
        }
        return tradeOrderDetailId;
    }

    public void setTradeOrderDetailId(String tradeOrderDetailId) {
        this.tradeOrderDetailId = tradeOrderDetailId;
    }

    public String getFreightName() {
        if (null == freightName){
            return "";
        }
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getFreightBillNo() {
        if (null == freightBillNo){
            return "";
        }
        return freightBillNo;
    }

    public void setFreightBillNo(String freightBillNo) {
        this.freightBillNo = freightBillNo;
    }

    public String getExtendInfo() {
        if (null == extendInfo){
            return "";
        }
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    public int getCreateType() {
        return createType;
    }

    public void setCreateType(int createType) {
        this.createType = createType;
    }

    public BigDecimal getSendQty() {
        if (null == sendQty){
            return BigDecimal.ZERO;
        }
        return sendQty;
    }

    public void setSendQty(BigDecimal sendQty) {
        this.sendQty = sendQty;
    }

    public int getSyncState() {
        return syncState;
    }

    public void setSyncState(int syncState) {
        this.syncState = syncState;
    }

    public int getSyncType() {
        return syncType;
    }

    public void setSyncType(int syncType) {
        this.syncType = syncType;
    }

    public boolean isHasSplit() {
        return hasSplit;
    }

    public void setHasSplit(boolean hasSplit) {
        this.hasSplit = hasSplit;
    }

    public boolean isHasFirst() {
        return hasFirst;
    }

    public void setHasFirst(boolean hasFirst) {
        this.hasFirst = hasFirst;
    }

    public boolean isHasLast() {
        return hasLast;
    }

    public void setHasLast(boolean hasLast) {
        this.hasLast = hasLast;
    }

    public String getHashKey() {
        return Md5Utils.md5(String.format("%s%s%s%s%s%s%s",getProfileId(),getTradeOrderId(),getOtypeId(),getTradeOrderDetailId(),getFreightName(),getFreightBillNo(),getFreightCode()));
    }


    public String getFreightCode() {
        if (null == freightCode){
            return "";
        }
        return freightCode;
    }

    public void setFreightCode(String freightCode) {
        this.freightCode = freightCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setHashKey(String hashKey) {
        this.hashKey = hashKey;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getPlatformPtypeId() {
        return platformPtypeId;
    }

    public void setPlatformPtypeId(String platformPtypeId) {
        this.platformPtypeId = platformPtypeId;
    }

    public String getPlatformPtypeName() {
        return platformPtypeName;
    }

    public void setPlatformPtypeName(String platformPtypeName) {
        this.platformPtypeName = platformPtypeName;
    }

    public String getPlatformXcode() {
        return platformXcode;
    }

    public void setPlatformXcode(String platformXcode) {
        this.platformXcode = platformXcode;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public Integer getSyncOrderSequence() {
        return syncOrderSequence;
    }

    public void setSyncOrderSequence(Integer syncOrderSequence) {
        this.syncOrderSequence = syncOrderSequence;
    }

    public Integer getSyncOrderDetailSequence() {
        return syncOrderDetailSequence;
    }

    public void setSyncOrderDetailSequence(Integer syncOrderDetailSequence) {
        this.syncOrderDetailSequence = syncOrderDetailSequence;
    }
}
