package com.wsgjp.ct.sale.platform.entity.request.buyer;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.factory.middlegroundfxs.sdk.request.DecryptMiddleGroundOrderRequest;

/**
 * <AUTHOR>
 */
public class DecryptOrderRequest extends BaseRequest {
    DecryptMiddleGroundOrderRequest params;

    public DecryptMiddleGroundOrderRequest getParams() {
        return params;
    }

    public void setParams(DecryptMiddleGroundOrderRequest params) {
        this.params = params;
    }
}
