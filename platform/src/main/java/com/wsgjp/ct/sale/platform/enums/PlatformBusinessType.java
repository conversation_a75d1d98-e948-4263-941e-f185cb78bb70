package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021-03-13 17:39
 */
public enum PlatformBusinessType implements CodeEnum {
    /**
     * 平台经销的单子
     */
    JINGXIAO(1, "代发"),
    /**
     * 平台代销的单子=分销业务
     */
    DAIXIAO(2, "分销"),
    SALEPROXY(3, "代销");
    private int code;
    private String name;

    PlatformBusinessType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
