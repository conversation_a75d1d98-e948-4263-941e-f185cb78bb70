package com.wsgjp.ct.sale.platform.mock;

import java.util.List;

/**
 * <AUTHOR> 2024/4/11 14:27
 */
public class MockServiceManager {


    private static List<ApiMockerService> mockerServices;

    public MockServiceManager(List<ApiMockerService> mockerServices) {
        MockServiceManager.mockerServices = mockerServices;
    }



    public static ApiMockerService getMockerService(String method) {
        if (mockerServices == null) {
            throw new RuntimeException("未找到关于mocker的实现");
        }
        for (ApiMockerService mockerService : mockerServices) {
            if (mockerService.methodName().equals(method)) {
                return mockerService;
            }
        }
        throw new RuntimeException("未找到关于mocker的实现");
    }

}
