package com.wsgjp.ct.sale.platform.dto.btype;

import java.util.List;

public class BtypeChangeNotifyParam {
    /**
     * shopAccount
     */
    private String merchantCode;

    /**
     * profileId
     */
    private String merchantErpId;

    /**
     * 不可下单的erp网点id=》往来单位编码列表
     */
    private List<String> btypeCodeList;

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getMerchantErpId() {
        return merchantErpId;
    }

    public void setMerchantErpId(String merchantErpId) {
        this.merchantErpId = merchantErpId;
    }

    public List<String> getBtypeCodeList() {
        return btypeCodeList;
    }

    public void setBtypeCodeList(List<String> btypeCodeList) {
        this.btypeCodeList = btypeCodeList;
    }
}
