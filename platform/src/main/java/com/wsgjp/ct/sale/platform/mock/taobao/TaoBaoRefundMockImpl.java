package com.wsgjp.ct.sale.platform.mock.taobao;

import com.taobao.api.internal.util.TaobaoUtils;
import com.taobao.api.request.RefundGetRequest;
import com.taobao.api.response.RefundGetResponse;
import com.wsgjp.ct.sale.platform.entity.request.other.MockRequest;
import com.wsgjp.ct.sale.platform.mock.ApiMockQueryService;
import com.wsgjp.ct.sale.platform.mock.ApiMockerService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2024/5/9 14:32
 */
@Service
public class TaoBaoRefundMockImpl implements ApiMockerService {

    private final ApiMockQueryService queryService;

    public TaoBaoRefundMockImpl(ApiMockQueryService queryService) {
        this.queryService = queryService;
    }

    @Override
    public Object queryData(MockRequest request) {
        String json = queryService.queryRefundByRefundId(request.getShopAccount(), request.getTradeId());
        try {
            return TaobaoUtils.parseResponse(json, RefundGetResponse.class);
        }catch (Exception ex){
            throw new RuntimeException("淘宝售后单mock数据解析异常：" + json);
        }
    }

    @Override
    public String methodName() {
        return "taobao.refund.get";
    }

    @Override
    public void buildRequest(MockRequest request, Object[] args) {
        RefundGetRequest getReq = (RefundGetRequest) args[0];
        request.setTradeId(getReq.getRefundId().toString());
    }


}
