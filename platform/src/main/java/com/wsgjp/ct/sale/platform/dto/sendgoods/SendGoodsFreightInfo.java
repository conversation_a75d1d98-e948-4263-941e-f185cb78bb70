package com.wsgjp.ct.sale.platform.dto.sendgoods;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2020-01-14 15:26
 */
@ApiModel("发货物流信息对象")
public class SendGoodsFreightInfo {

    @ApiModelProperty("物流名称")
    private String freightName;
    @ApiModelProperty("物流编码")
    private String freightCode;
    @ApiModelProperty("物流单号")
    private String freightBillNo;
    @ApiModelProperty("物流电话")
    private String freightTel;
    @ApiModelProperty("国际物流名称")
    private String interFreightName;
    @ApiModelProperty("国际物流编码")
    private String interFreightCode;
    @ApiModelProperty("国际物流订单号")
    private String interFreightBillNo;

    @ApiModelProperty("发货人名称")
    private String senderName;
    @ApiModelProperty("发货人电话")
    private String senderTel;
    @ApiModelProperty("发货人备注信息")
    private String senderRemark;
    @ApiModelProperty("发货省份")
    private String senderProvince;
    @ApiModelProperty("发货城市")
    private String senderCity;
    @ApiModelProperty("发货区域")
    private String senderDistrict;
    @ApiModelProperty("详细发货地址")
    private String senderAddress;

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getFreightCode() {
        if(freightCode==null)
        {
            return "";
        }
        return freightCode;
    }

    public void setFreightCode(String freightCode) {
        this.freightCode = freightCode;
    }

    public String getFreightBillNo() {
        if(freightBillNo==null)
        {
            return "";
        }
        return freightBillNo;
    }

    public void setFreightBillNo(String freightBillNo) {
        this.freightBillNo = freightBillNo;
    }

    public String getFreightTel() {
        return freightTel;
    }

    public void setFreightTel(String freightTel) {
        this.freightTel = freightTel;
    }

    public String getInterFreightName() {
        return interFreightName;
    }

    public void setInterFreightName(String interFreightName) {
        this.interFreightName = interFreightName;
    }

    public String getInterFreightCode() {
        return interFreightCode;
    }

    public void setInterFreightCode(String interFreightCode) {
        this.interFreightCode = interFreightCode;
    }

    public String getInterFreightBillNo() {
        return interFreightBillNo;
    }

    public void setInterFreightBillNo(String interFreightBillNo) {
        this.interFreightBillNo = interFreightBillNo;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderTel() {
        return senderTel;
    }

    public void setSenderTel(String senderTel) {
        this.senderTel = senderTel;
    }

    public String getSenderRemark() {
        return senderRemark;
    }

    public void setSenderRemark(String senderRemark) {
        this.senderRemark = senderRemark;
    }

    public String getSenderProvince() {
        return senderProvince;
    }

    public void setSenderProvince(String senderProvince) {
        this.senderProvince = senderProvince;
    }

    public String getSenderCity() {
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    public String getSenderDistrict() {
        return senderDistrict;
    }

    public void setSenderDistrict(String senderDistrict) {
        this.senderDistrict = senderDistrict;
    }

    public String getSenderAddress() {
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }
}
