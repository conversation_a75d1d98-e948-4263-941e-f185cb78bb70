package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 电商仓业务类型
 */
public enum WareHouseBusinessType implements CodeEnum {
    NONE(0, "默认"),
    SELF_EXTRACT(1, "自提"),
    STORE(2, "门店"),
    SELF_EXTRACT_STORE(3, "自提+门店"),
    ONLINE_STORE(4, "线上电商仓"),
    REAL_WAREHOUSE(5, "实仓"),
    VIRTUAL_WAREHOUSE(6, "虚仓"),
    PROVINCE_WAREHOUSE(7, "省仓"),
    REFUND_WAREHOUSE(8, "退供收货仓");

    private  int index;
    private  String name;

    WareHouseBusinessType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }
}

