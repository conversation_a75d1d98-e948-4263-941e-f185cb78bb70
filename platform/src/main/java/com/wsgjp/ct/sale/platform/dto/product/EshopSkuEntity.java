package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.common.enums.core.enums.ProductMarkEnum;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * sku商品信息实体
 */
@ApiModel("网店商品SKU实体")
public class EshopSkuEntity extends ErpBaseInfo {
    public EshopSkuEntity() {
    }

    public EshopSkuEntity(EshopSystemParams systemParams) {
        this.setProfileId(systemParams.getProfileId());
        this.setEshopId(systemParams.geteShopId());
        this.setShopType(systemParams.getShopType());
    }

    /**
     * 主商品ID
     */
    @ApiModelProperty("主商品ID")
    private String numId;
    /**
     * 商家编码
     */
    @ApiModelProperty("商家编码")
    private String xcode;
    /**
     * 商品skuId
     */
    @ApiModelProperty("商品skuId")

    private String skuId;
    /**
     * 商品条码
     */
    @ApiModelProperty("商品条码")
    private String barcode;
    /**
     * 颜色id：红色id;尺码ID：XL id，这个字段一定要赋值
     */
    @ApiModelProperty("颜色id:红色id;尺码id:XL id")
    private String properties;
    /**
     * 红色_XL，这个字段一定要赋值
     */
    @ApiModelProperty("红色_XL")
    private String propertiesName;
    /**
     * 颜色：红色；尺码：XL => 页面上新增商品时会需要这个字段。
     */
    @ApiModelProperty("颜色:红色;尺码:XL")
    private String fullPropertiesName;

    @ApiModelProperty("颜色:红色{备注};尺码:XL{备注}")
    private String hasMemoFullPropertiesName;
    /**
     * 平台返回的完整属性
     */
    @ApiModelProperty("平台返回的完整属性")
    private String fullProperties;
    /**
     * sku商品图片
     */
    @ApiModelProperty("sku商品图片")
    private String picUrl;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date modifiedTime;
    /**
     * 礼物id
     */
    @ApiModelProperty("礼物id")
    private String giftId;
    /**
     * sku商品名称
     */
    @ApiModelProperty("sku商品名称")
    private String name;
    /**
     * 商品单价
     */
    @ApiModelProperty("商品单价")
    private BigDecimal price;

    /**
     * 商品sku价格
     */
    @ApiModelProperty("商品价格")
    private BigDecimal skuPrice;
    /**
     * 零售价格
     */
    @ApiModelProperty("零售价格")
    private BigDecimal retailPrice;
    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private BigDecimal qty;
    /**
     * 特殊id
     */
    @ApiModelProperty("特殊id")
    private String specId;
    /**
     * 各平台特殊字段
     */
    @ApiModelProperty("各平台特殊字段")
    private String platformJson;
    /**
     * 仓库编码
     */
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("含有备注信息的属性")
    private String hasMemoPropertiesName;
    @ApiModelProperty("网店商品单位id")
    private String platformUnitId;
    @ApiModelProperty("网店商品单位名称")
    private String platformUnitName;

    /**
     * 商品标记
     */
    private List<ProductMarkEnum> productMarkEnumList;

    private ProductBigData productBigData;

    public ProductBigData getProductBigData() {
        return productBigData;
    }

    public void setProductBigData(ProductBigData productBigData) {
        this.productBigData = productBigData;
    }

    public List<ProductMarkEnum> getProductMarkEnumList() {
        if (CollectionUtils.isNotEmpty(productMarkEnumList)){
            return productMarkEnumList;
        }
        productMarkEnumList = new ArrayList<>();
        return productMarkEnumList;
    }

    public void setProductMarkEnumList(List<ProductMarkEnum> productMarkEnumList) {
        this.productMarkEnumList = productMarkEnumList;
    }

    public String getPlatformUnitId() {
        return platformUnitId;
    }

    public void setPlatformUnitId(String platformUnitId) {
        this.platformUnitId = platformUnitId;
    }

    public String getPlatformUnitName() {
        return platformUnitName;
    }

    public void setPlatformUnitName(String platformUnitName) {
        this.platformUnitName = platformUnitName;
    }

    public String getHasMemoPropertiesName() {
        return hasMemoPropertiesName;
    }

    public void setHasMemoPropertiesName(String hasMemoPropertiesName) {
        this.hasMemoPropertiesName = hasMemoPropertiesName;
    }

    public String getHasMemoFullPropertiesName() {
        return hasMemoFullPropertiesName == null ? getFullPropertiesName() : hasMemoFullPropertiesName;
    }

    public void setHasMemoFullPropertiesName(String hasMemoFullPropertiesName) {
        this.hasMemoFullPropertiesName = hasMemoFullPropertiesName;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getBarcode() {
        if(barcode==null){
            barcode="";
        }
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public String getPicUrl() {
        if(StringUtils.isEmpty(picUrl)){
            picUrl="";
        }
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public String getPropertiesName() {
        if (StringUtils.isEmpty(propertiesName)) {
            propertiesName = "";
        }
        return propertiesName;
    }

    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    public String getFullPropertiesName() {
        if (StringUtils.isEmpty(fullPropertiesName)) {
            fullPropertiesName = "";
        }
        return fullPropertiesName;
    }

    public void setFullPropertiesName(String fullPropertiesName) {
        this.fullPropertiesName = fullPropertiesName;
    }

    public String getFullProperties() {
        if (StringUtils.isEmpty(fullProperties)) {
            fullProperties = "";
        }
        return fullProperties;
    }

    public void setFullProperties(String fullProperties) {
        this.fullProperties = fullProperties;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public String getGiftId() {
        return giftId;
    }

    public void setGiftId(String giftId) {
        this.giftId = giftId;
    }

    public String getPlatformJson() {
        if (StringUtils.isEmpty(platformJson)) {
            return "";
        }
        return platformJson;
    }

    public void setPlatformJson(String platformJson) {
        this.platformJson = platformJson;
    }

    public String getNumId() {
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getProperties() {
        if (StringUtils.isEmpty(properties)) {
            properties = "";
        }
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public BigDecimal getQty() {
        if (qty == null) {
            return BigDecimal.ZERO;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        if (qty == null) {
            qty = BigDecimal.ZERO;
        }
        this.qty = qty;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSpecId() {
        return specId;
    }

    public void setSpecId(String specId) {
        this.specId = specId;
    }

    public BigDecimal getSkuPrice() {
        if (null == skuPrice) {
            return BigDecimal.ZERO;
        }
        return skuPrice;
    }

    public void setSkuPrice(BigDecimal skuPrice) {
        this.skuPrice = skuPrice;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }
}
