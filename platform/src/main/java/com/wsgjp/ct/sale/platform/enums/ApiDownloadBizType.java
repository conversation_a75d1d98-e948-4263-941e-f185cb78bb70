package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum ApiDownloadBizType implements CodeEnum {
    DOWNLOAD_GOODS_CATEGORY(1, "下载商品类目"),
    DOWNLOAD_GOODS(2, "下载商品"),
    DOWNLOAD_ORDER(3, "下载订单"),
    UPDATE_ORDER(4, "更新订单"),
    DOWNLOAD_REFUND(5, "下载售后单"),
    UPDATE_REFUND(6, "更新售后单"),
    ;

    ApiDownloadBizType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;

    @Override
    public int getCode() {
        return 0;
    }

    @Override
    public String getName() {
        return null;
    }
}
