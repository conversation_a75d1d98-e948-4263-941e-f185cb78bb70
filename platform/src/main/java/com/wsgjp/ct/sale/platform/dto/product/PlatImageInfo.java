package com.wsgjp.ct.sale.platform.dto.product;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 平台图片库的图片信息
 * <AUTHOR>
 */
public class PlatImageInfo {
    /**
     * 图片ID
     */
    private String imageId;
    /**
     * 图片类目ID
     */
    private String imageCateId;
    /**
     * 图片URL
     */
    private String imageUrl;
    /**
     * 图片名称
     */
    private String imageName;
    /**
     * 图片是否被引用
     */
    private Integer referenced;
    /**
     * 图片大小 单位b
     */
    private BigDecimal imageSize;
    /**
     * 图片宽度，单位px
     */
    private BigDecimal imageWidth;
    /**
     * 图片高度，单位px
     */
    private BigDecimal imageHeight;
    private Date createTime;
    private Date updateTime;

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getImageCateId() {
        return imageCateId;
    }

    public void setImageCateId(String imageCateId) {
        this.imageCateId = imageCateId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public Integer getReferenced() {
        return referenced;
    }

    public void setReferenced(Integer referenced) {
        this.referenced = referenced;
    }

    public BigDecimal getImageSize() {
        return imageSize;
    }

    public void setImageSize(BigDecimal imageSize) {
        this.imageSize = imageSize;
    }

    public BigDecimal getImageWidth() {
        return imageWidth;
    }

    public void setImageWidth(BigDecimal imageWidth) {
        this.imageWidth = imageWidth;
    }

    public BigDecimal getImageHeight() {
        return imageHeight;
    }

    public void setImageHeight(BigDecimal imageHeight) {
        this.imageHeight = imageHeight;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
