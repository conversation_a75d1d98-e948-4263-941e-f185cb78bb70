package com.wsgjp.ct.sale.platform.dto.tmc;

import com.wsgjp.ct.sale.platform.enums.DateType;
import com.wsgjp.ct.sale.platform.enums.MsgDataTypeEnum;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryMsgParams {
    private String onlineShopId;
    private Integer shopType;
    private Date startTime;
    private Date endTime;
    /**
     * 查询时间类型
     * 0 按创建时间查询
     * 1 按更新时间查询
     */
    private DateType dateType;
    /**
     * 消息数据类型
     */
    private MsgDataTypeEnum dataType;
    /**
     * 从第0页开始
     */
    private Integer pageNo;
    private Integer pageSize;
    private List<String> tradeIds;

    public String getOnlineShopId() {
        return onlineShopId;
    }

    public void setOnlineShopId(String onlineShopId) {
        this.onlineShopId = onlineShopId;
    }

    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public DateType getDateType() {
        return dateType;
    }

    public void setDateType(DateType dateType) {
        this.dateType = dateType;
    }

    public MsgDataTypeEnum getDataType() {
        return dataType;
    }

    public void setDataType(MsgDataTypeEnum dataType) {
        this.dataType = dataType;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getTradeIds() {
        return tradeIds;
    }

    public void setTradeIds(List<String> tradeIds) {
        this.tradeIds = tradeIds;
    }


}
