package com.wsgjp.ct.sale.platform.enums;

public enum PlatformExceptionEnum {
    token_permission_denied(100000, ""),
    // 平台公共异常部分
    token_expired(10000, "token过期"),
    api_overrun(100001, "接口超限"),
    not_connected(100002, "无法连接"),
    not_in_whitelisted(100023, "不在平台白名单中");

    // 平台业务异常部分
    private int code;
    private String desc;

    PlatformExceptionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
