package com.wsgjp.ct.sale.platform.dto.order;

import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.order.entity.IdentifyInfo;
import com.wsgjp.ct.sale.platform.dto.order.entity.OrderTiming;
import com.wsgjp.ct.sale.platform.dto.order.entity.QualityControlInfo;
import com.wsgjp.ct.sale.platform.entity.entities.FreightInfo;
import com.wsgjp.ct.sale.platform.enums.AttributeType;
import com.wsgjp.ct.sale.platform.enums.PlatformBusinessType;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.enums.TradeTypeEnum;
import com.wsgjp.ct.sale.platform.utils.Common;
import com.wsgjp.ct.sale.platform.utils.CommonUtils;
import io.swagger.annotations.ApiModelProperty;
import ngp.utils.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单详情实体
 */
public class EshopOrderDetailEntity {
    public EshopOrderDetailEntity() {
        taxFee = BigDecimal.ZERO;
        weight = BigDecimal.ZERO;
        freightFee = BigDecimal.ZERO;
        serviceFee = BigDecimal.ZERO;
        commissionFee = BigDecimal.ZERO;
        markDataList = new ArrayList<>();
    }

    /**
     * 数量
     */
    private BigDecimal qty;
    /**
     * 原始单价
     */
    private BigDecimal price;
    /**
     * 原始金额
     */
    private BigDecimal total;
    /**
     * 成交金额 成交金额是计算获得的  成交金额=原始金额-商家单品优惠-商家整单优惠分摊
     * 可不用赋值，平台业务组会统一处理
     */
    private BigDecimal tradeTotal;
    /**
     * 税费，如果接口有返回税费，请传到这里。如果税费在主表，请分摊税费到这里
     */
    private BigDecimal taxFee;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 快递费
     */
    private BigDecimal freightFee;
    /**
     * 售后退款金额
     */
    private BigDecimal refundFee;


    /**
     * 商城扣费（平台赋值之后就已平台为准）
     */
    private BigDecimal mallFee;

    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 重量单位
     */
    private String weightUnit;
    /**
     * 商品图片
     */
    private String picUrl;
    /**
     * 子订单id（平台自己生产的话要MD5保证唯一和位数）
     */
    private String oid;

    /**
     * 目前就小红书售后单构建再用，业务组也没用。
     */
    private String packageCompanyId;
    private String numId;
    private String skuId;
    /**
     * 商家编码
     */
    private String xcode;
    private String title;
    private String classId;

    /**
     * 商品销售属性(默认空字符串为null平台业务组要报错)
     */
    private String skuPropertiesValue = "";

    private RefundStatus refundStatus;
    private String buyerMessage;
    /**
     * 是否能发货（不发货不记账）
     */
    private boolean canSendGoods = true;

    /**
     * 不发货要记账(给true就代表不发货要记账)
     */
    private boolean noShippingBookkeeping;
    /**
     * 不能发货原因
     */
    private String cantSendReason;
    /**
     *
     */
    private Boolean cancel;
    private String platformRefundId;
    private TradeTypeEnum tradeType;
    /**
     * 服务费，如果接口有返回服务费，请传到这里。如果服务费在主表，请分摊服务费到这里
     */
    private BigDecimal serviceFee;
    /**
     * 佣金
     */
    private BigDecimal commissionFee;
    /**
     * 订单明细的优惠价格
     *
     * @Deprecated 该字段平台业务组已废弃使用，请使用EshopOrderEntity实体中的preferentialDetails字段构建订单明细优惠金额
     */
    @Deprecated
    private BigDecimal preferentialPrice;
    /**
     * 分佣金额  =  折后含税金额 - 分销结算含税金额
     */
    private BigDecimal distributionCommissionTotal;
    /**
     * 分销结算含税金额
     */
    private BigDecimal distributionBalanceTaxedTotal;
    /**
     * 分销结算含税单价
     */
    private BigDecimal distributionBalanceTaxedPrice;
    /**
     * 订单明细提醒
     */
    private List<BaseOrderMarkEnum> detailMarks;
    /**
     * 订单提醒-携带标记数据
     */

    private List<MarkData> markDataList;
    /**
     * 订单明细状态 业务构建订单时 ，如果明细的交易状态为空或者为默认值 0（ABNORMAL） ，明细会取主表的交易状态
     */
    private TradeStatus tradeStatus;

    /**
     * 平台特殊字段
     */
    private String platformSpecialJson;

    /**
     * 活动单号，元气使用，目前不会落库
     */
    private String activityNo;
    /**
     * 活动名称,元气使用，目前不会落库
     */
    private String activityName;
    /**
     * 活动类型,元气使用，目前不会落库
     */
    private String activityType;

    private int platformOperateType;

    //商品属性类别
    private AttributeType attributeType;

    /**
     * 是否是赠品，业务暂使用此字段
     */
    private boolean gift;

    /**
     * 后续电商仓id  门店id 等都赋值到此字段 明细有电商仓编码，优先使用明细的id。明细没有电商仓id，就取主表的id
     */
    @ApiModelProperty("后续电商仓id")
    private String stockId;
    /**
     * 后续电商仓编码  门店编码 等都赋值到此字段 ,明细有电商仓编码，优先使用明细的编码。明细没有电商仓编码，就取主表的编码
     */
    @ApiModelProperty("后续电商仓编码")
    private String stockCode;

    /**
     * 订单承诺时间
     */
    @ApiModelProperty("订单时效")
    private OrderTiming orderTiming;

    /**
     * 达人 主播信息
     */
    private String daRen;
    /**
     * 达人 主播id
     */
    private String daRenId;
    /**
     * 达人 主播房间号
     */
    private String roomId;
    /**
     * 规格信息
     */
    private String spec;

    private String displayCustomInfo;
    /**
     * 质检信息
     */
    private QualityControlInfo qualityControlInfo;

    /**
     * 鉴定结果
     */
    private IdentifyInfo identifyInfo;

    /**
     * 得物码
     */
    private String verifyCode;

    /**
     * 流量通道
     */
    private String flowChannel;

    private String distributionBuyerTradeDetailId;

    /**
     * 主品-赠品关系列表
     */
    private List<ProductGiftRelation> giftRelations;

    //=======以下字段是供应链中台-供应商店铺类型专用

    /**
     * 是否是套餐商品
     */
    private boolean isCombo;
    /**
     * 供销商商品id 下游的本地商品
     */
    private String supplierPtypeId;
    /**
     * 供销商商品skuid
     */
    private String supplierSkuId;
    /**
     * 供销商商品单位id
     */
    private String supplierUnitId;

    /**
     * 分销商品本地商品ID
     */
    private String distributorPtypeId;
    /**
     * 分销商品本地skuId
     */
    private String distributorSkuId;
    /**
     * 分销商品本地商品单位id
     */
    private String distributorUnitId;

    /**
     * 批次号
     */
    private String batchno;
//    /**
//     * 批次生产日期
//     */
//    private Date produceDate;
//    /**
//     * 批次到期日期
//     */
//    private Date expireDate;
//    /**
//     *
//     价格
//     */
//    private BigDecimal batchPrice;

    private List<FreightInfo> freightInfoList;

    @ApiModelProperty("国家补贴金额")
    private BigDecimal nationalSubsidyTotal;

    @ApiModelProperty("平台业务类型：经销、代销")
    private PlatformBusinessType platformBusinessType;

    public PlatformBusinessType getPlatformBusinessType() {
        return platformBusinessType;
    }

    public void setPlatformBusinessType(PlatformBusinessType platformBusinessType) {
        this.platformBusinessType = platformBusinessType;
    }

    public BigDecimal getNationalSubsidyTotal() {
        return nationalSubsidyTotal;
    }

    public void setNationalSubsidyTotal(BigDecimal nationalSubsidyTotal) {
        this.nationalSubsidyTotal = nationalSubsidyTotal;
    }

    public boolean isNoShippingBookkeeping() {
        return noShippingBookkeeping;
    }

    public void setNoShippingBookkeeping(boolean noShippingBookkeeping) {
        this.noShippingBookkeeping = noShippingBookkeeping;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public IdentifyInfo getIdentifyInfo() {
        return identifyInfo;
    }

    public void setIdentifyInfo(IdentifyInfo identifyInfo) {
        this.identifyInfo = identifyInfo;
    }

    public QualityControlInfo getQualityControlInfo() {
        return qualityControlInfo;
    }

    public void setQualityControlInfo(QualityControlInfo qualityControlInfo) {
        this.qualityControlInfo = qualityControlInfo;
    }

    /**
     * 最晚发货时间
     */
    private Date promisedSendTime;
    private ReturnState refundState;

    public ReturnState getRefundState() {
        return refundState;
    }

    public void setRefundState(ReturnState refundState) {
        this.refundState = refundState;
    }

    public Date getPromisedSendTime() {
        return promisedSendTime;
    }

    public void setPromisedSendTime(Date promisedSendTime) {
        this.promisedSendTime = promisedSendTime;
    }

    public String getPackageCompanyId() {
        return packageCompanyId;
    }

    public void setPackageCompanyId(String packageCompanyId) {
        this.packageCompanyId = packageCompanyId;
    }

    public OrderTiming getOrderTiming() {
        return orderTiming;
    }

    public void setOrderTiming(OrderTiming orderTiming) {
        this.orderTiming = orderTiming;
    }

    public BigDecimal getServiceFee() {
        if (serviceFee == null) {
            serviceFee = BigDecimal.ZERO;
        }
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    public BigDecimal getQty() {
        if (qty == null) {
            return BigDecimal.ONE;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getPrice() {
        if (price == null) {
            return BigDecimal.ZERO;
        }
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotal() {
        if (total == null) {
            return BigDecimal.ZERO;
        }
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public BigDecimal getTradeTotal() {
        if (tradeTotal == null) {
            return BigDecimal.ZERO;
        }
        return tradeTotal;
    }

    public void setTradeTotal(BigDecimal tradeTotal) {
        this.tradeTotal = tradeTotal;
    }

    public BigDecimal getRefundFee() {
        if (null == refundFee) {
            return BigDecimal.ZERO;
        }
        return refundFee;
    }

    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    public String getPicUrl() {
        if (StringUtils.isEmpty(picUrl)) {
            picUrl = "";
        }
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getOid() {
        if (StringUtils.isEmpty(oid)) {
            String str = getNumId() +
                    getSkuId() +
                    getTitle();
            return Common.getMD5Str(str);
        }
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getNumId() {
        if (numId == null) {
            numId = "";
        }
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getSkuId() {
        if (skuId == null) {
            skuId = "";
        }
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getXcode() {
        if (xcode == null) {
            xcode = "";
        }
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSkuPropertiesValue() {
        return skuPropertiesValue;
    }

    public void setSkuPropertiesValue(String skuPropertiesValue) {
        this.skuPropertiesValue = skuPropertiesValue;
    }

    public String getPlatformSpecialJson() {
        if (platformSpecialJson == null) {
            return "";
        }
        return platformSpecialJson;
    }

    public void setPlatformSpecialJson(String platformSpecialJson) {
        this.platformSpecialJson = platformSpecialJson;
    }

    public RefundStatus getRefundStatus() {
        if (refundStatus == null) {
            return RefundStatus.NONE;
        }
        return refundStatus;
    }

    public void setRefundStatus(RefundStatus refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getBuyerMessage() {
        if (buyerMessage == null) {
            buyerMessage = "";
        }
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public BigDecimal getTaxFee() {
        if (taxFee == null) {
            return BigDecimal.ZERO;
        }
        return taxFee;
    }

    public void setTaxFee(BigDecimal taxFee) {
        this.taxFee = taxFee;
    }

    public BigDecimal getFreightFee() {
        return freightFee;
    }

    public void setFreightFee(BigDecimal freightFee) {
        this.freightFee = freightFee;
    }

    public String getPropertiesValue() {
        return skuPropertiesValue;
    }

    public String getPropertiesValue(int mode) {
        if (0 == mode) {
            return skuPropertiesValue;
        }
        return CommonUtils.getPlatformPropertiesNameAfterSplit(skuPropertiesValue);
    }


    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    public boolean isCanSendGoods() {
        return canSendGoods;
    }

    public void setCanSendGoods(boolean canSendGoods) {
        this.canSendGoods = canSendGoods;
    }

    public String getCantSendReason() {
        return cantSendReason;
    }

    public void setCantSendReason(String cantSendReason) {
        this.cantSendReason = cantSendReason;
    }

    public Boolean getCancel() {
        if (null == cancel) {
            cancel = false;
        }
        return cancel;
    }

    public void setCancel(Boolean cancel) {
        this.cancel = cancel;
    }


    public String getPlatformRefundId() {
        return platformRefundId;
    }

    public void setPlatformRefundId(String platformRefundId) {
        this.platformRefundId = platformRefundId;
    }


    public TradeTypeEnum getTradeType() {
        if (tradeType == null) {
            return TradeTypeEnum.NORMAL;
        }
        return tradeType;
    }

    public void setTradeType(TradeTypeEnum tradeType) {
        this.tradeType = tradeType;
    }

    public BigDecimal getCommissionFee() {
        if (commissionFee == null) {
            commissionFee = BigDecimal.ZERO;
        }
        return commissionFee;
    }

    public void setCommissionFee(BigDecimal commissionFee) {
        this.commissionFee = commissionFee;
    }

    @Deprecated
    public BigDecimal getPreferentialPrice() {
        return preferentialPrice;
    }

    @Deprecated
    public void setPreferentialPrice(BigDecimal preferentialPrice) {
        this.preferentialPrice = preferentialPrice;
    }

    public BigDecimal getDistributionCommissionTotal() {
        if (null == distributionCommissionTotal) {
            distributionCommissionTotal = BigDecimal.ZERO;
        }
        return distributionCommissionTotal;
    }

    public void setDistributionCommissionTotal(BigDecimal distributionCommissionTotal) {
        this.distributionCommissionTotal = distributionCommissionTotal;
    }

    public BigDecimal getDistributionBalanceTaxedTotal() {
        if (null == distributionBalanceTaxedTotal) {
            return BigDecimal.ZERO;
        }
        return distributionBalanceTaxedTotal;
    }

    public void setDistributionBalanceTaxedTotal(BigDecimal distributionBalanceTaxedTotal) {
        this.distributionBalanceTaxedTotal = distributionBalanceTaxedTotal;
    }

    public BigDecimal getDistributionBalanceTaxedPrice() {
        return distributionBalanceTaxedPrice;
    }

    public void setDistributionBalanceTaxedPrice(BigDecimal distributionBalanceTaxedPrice) {
        this.distributionBalanceTaxedPrice = distributionBalanceTaxedPrice;
    }

    public List<BaseOrderMarkEnum> getDetailMarks() {
        if (detailMarks == null) {
            detailMarks = new ArrayList<>();
        }
        return detailMarks;
    }

    public void setDetailMarks(List<BaseOrderMarkEnum> detailMarks) {
        this.detailMarks = detailMarks;
    }

    public TradeStatus getTradeStatus() {
        if (tradeStatus == null) {
            return TradeStatus.ABNORMAL;
        }
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public String getClassId() {
        return classId;
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public BigDecimal getTaxRate() {
        if (null == taxRate) {
            return BigDecimal.ZERO;
        }
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getActivityNo() {
        return activityNo;
    }

    public void setActivityNo(String activityNo) {
        this.activityNo = activityNo;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public boolean isGift() {
        return gift;
    }

    public void setGift(boolean gift) {
        this.gift = gift;
    }

    public int getPlatformOperateType() {
        return platformOperateType;
    }

    public void setPlatformOperateType(int platformOperateType) {
        this.platformOperateType = platformOperateType;
    }

    public AttributeType getAttributeType() {
        return attributeType;
    }

    public void setAttributeType(AttributeType attributeType) {
        this.attributeType = attributeType;
    }

    public String getStockId() {
        if (StringUtils.isEmpty(stockId)) {
            return "";
        }
        return stockId;
    }

    public void setStockId(String stockId) {
        this.stockId = stockId;
    }

    public String getStockCode() {
        if (StringUtils.isEmpty(stockCode)) {
            return "";
        }
        return stockCode;
    }

    public void setStockCode(String stockCode) {
        this.stockCode = stockCode;
    }

    public String getDaRen() {
        if (daRen == null) {
            return "";
        }
        return daRen;
    }

    public void setDaRen(String daRen) {
        this.daRen = daRen;
    }

    public List<MarkData> getMarkDataList() {
        if (markDataList == null) {
            markDataList = new ArrayList<>();
        }
        return markDataList;
    }

    public void setMarkDataList(List<MarkData> markDataList) {
        this.markDataList = markDataList;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getDaRenId() {
        return daRenId;
    }

    public void setDaRenId(String daRenId) {
        this.daRenId = daRenId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getDisplayCustomInfo() {
        if (null == displayCustomInfo) {
            return "";
        }
        return displayCustomInfo;
    }

    public void setDisplayCustomInfo(String displayCustomInfo) {
        this.displayCustomInfo = displayCustomInfo;
    }

    public String getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        if (null == flowChannel) {
            flowChannel = "";
        }
        this.flowChannel = flowChannel;
    }

    public String getDistributionBuyerTradeDetailId() {
        return distributionBuyerTradeDetailId;
    }

    public void setDistributionBuyerTradeDetailId(String distributionBuyerTradeDetailId) {
        this.distributionBuyerTradeDetailId = distributionBuyerTradeDetailId;
    }

    public BigDecimal getMallFee() {
        if (this.mallFee == null) {
            this.mallFee = BigDecimal.ZERO;
        }
        return mallFee;
    }

    public void setMallFee(BigDecimal mallFee) {
        this.mallFee = mallFee;
    }

    public List<ProductGiftRelation> getGiftRelations() {
        if (null == giftRelations) {
            giftRelations = new ArrayList<>();
        }
        return giftRelations;
    }

    public void setGiftRelations(List<ProductGiftRelation> giftRelations) {
        this.giftRelations = giftRelations;
    }

    public boolean isCombo() {
        return isCombo;
    }

    public void setCombo(boolean combo) {
        isCombo = combo;
    }

    public String getSupplierPtypeId() {
        return supplierPtypeId;
    }

    public void setSupplierPtypeId(String supplierPtypeId) {
        this.supplierPtypeId = supplierPtypeId;
    }

    public String getSupplierSkuId() {
        return supplierSkuId;
    }

    public void setSupplierSkuId(String supplierSkuId) {
        this.supplierSkuId = supplierSkuId;
    }

    public String getSupplierUnitId() {
        return supplierUnitId;
    }

    public void setSupplierUnitId(String supplierUnitId) {
        this.supplierUnitId = supplierUnitId;
    }

    public String getDistributorPtypeId() {
        return distributorPtypeId;
    }

    public void setDistributorPtypeId(String distributorPtypeId) {
        this.distributorPtypeId = distributorPtypeId;
    }

    public String getDistributorSkuId() {
        return distributorSkuId;
    }

    public void setDistributorSkuId(String distributorSkuId) {
        this.distributorSkuId = distributorSkuId;
    }

    public String getDistributorUnitId() {
        return distributorUnitId;
    }

    public void setDistributorUnitId(String distributorUnitId) {
        this.distributorUnitId = distributorUnitId;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public List<FreightInfo> getFreightInfoList() {
        if(freightInfoList==null){
            freightInfoList= new ArrayList<>();
        }
        return freightInfoList;
    }

    public void setFreightInfoList(List<FreightInfo> freightInfoList) {
        this.freightInfoList = freightInfoList;
    }
}
