package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.platform.enums.PromiseType;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-12-17 17:27
 * todo 订单承诺信息？
 */
public class OrderPromise {

    /**
     * 发货保障类型
     */
    private PromiseType promiseType;

    private Date promiseDeliverTime;

    private String promiseDeliverRange;

    private Date expectedArrivalTime;

    private String expectedArrivalRange;

    private Date expectedPushTime;

    /**
     * 上门自提时间
     */
    private String fetchTime;
    /**
     * 上门自提人
     */
    private String fetchCustomer;



    public String getFetchTime() {
        return fetchTime;
    }

    public void setFetchTime(String fetchTime) {
        this.fetchTime = fetchTime;
    }

    public String getFetchCustomer() {
        return fetchCustomer;
    }

    public void setFetchCustomer(String fetchCustomer) {
        this.fetchCustomer = fetchCustomer;
    }


    public Date getPromiseDeliverTime() {
        return promiseDeliverTime;
    }

    public void setPromiseDeliverTime(Date promiseDeliverTime) {
        this.promiseDeliverTime = promiseDeliverTime;
    }

    public String getPromiseDeliverRange() {
        return promiseDeliverRange;
    }

    public void setPromiseDeliverRange(String promiseDeliverRange) {
        this.promiseDeliverRange = promiseDeliverRange;
    }

    public Date getExpectedArrivalTime() {
        return expectedArrivalTime;
    }

    public void setExpectedArrivalTime(Date expectedArrivalTime) {
        this.expectedArrivalTime = expectedArrivalTime;
    }

    public String getExpectedArrivalRange() {
        return expectedArrivalRange;
    }

    public void setExpectedArrivalRange(String expectedArrivalRange) {
        this.expectedArrivalRange = expectedArrivalRange;
    }

    public Date getExpectedPushTime() {
        return expectedPushTime;
    }

    public void setExpectedPushTime(Date expectedPushTime) {
        this.expectedPushTime = expectedPushTime;
    }
}
