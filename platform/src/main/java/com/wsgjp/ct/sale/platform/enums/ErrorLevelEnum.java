package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 12/28/2020 上午 09:47
 */
public enum ErrorLevelEnum implements CodeEnum {

    System(0,"系统","10"),
    Module (1,"服务模块","20"),
    Safety (2,"安全","30");

    private int index;
    private String code;

    private String name;

    ErrorLevelEnum(int index, String name, String code){
        this.index=index;
        this.name=name;
        this.code=code;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }

    public String getUserCode(){return code;}
}
