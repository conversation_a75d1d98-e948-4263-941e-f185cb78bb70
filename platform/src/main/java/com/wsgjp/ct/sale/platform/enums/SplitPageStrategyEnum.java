package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum SplitPageStrategyEnum implements CodeEnum {
    TIME_LIMIT(1,"时间限制-时间切割分页"),
    STATUS_LIMIT(2,"状态限制-状态拆分分页"),
    DATE_SIZE_LIMIT(3,"数据条数限制-状态拆分分页"),
    BUSINESS_LIMIT(4,"业务限制-类型拆分分页"),
    GENERAL(5,"普通分页"),
    ;

    SplitPageStrategyEnum(int code,String name){
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;

    @Override
    public int getCode() {
        return 0;
    }

    @Override
    public String getName() {
        return null;
    }
}
