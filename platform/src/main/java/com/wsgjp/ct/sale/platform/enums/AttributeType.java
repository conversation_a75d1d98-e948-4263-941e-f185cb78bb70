package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum AttributeType implements CodeEnum {

    DEFAULT(0,""),
    REGULAR_SALES(1,"常规销售"),
    REGULAR_GIFT(2,"常规赠品"),
    ACTIVITY_GIFT(3,"活动赠货"),
    FEE_EXCHANGE(4,"费用兑换");

    private int index;
    private String name;

    AttributeType(int index,String name){
        this.index=index;
        this.name=name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }
}
