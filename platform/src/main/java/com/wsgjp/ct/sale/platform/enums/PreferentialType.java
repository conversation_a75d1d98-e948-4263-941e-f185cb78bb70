package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 16/7/2020 上午 11:00
 */
public enum PreferentialType implements CodeEnum {
	/**
	 * 对账后结算=现场现结
	 */
	SELLER(0, "商家优惠"),
	/**
	 * 货到付款
	 */
	PLATFORM(1, "平台优惠"),
	ANCHOR(2, "主播优惠"),
	PLATFORM_SUBSIDIES(3,"平台补贴优惠");

	private final int flag;

	private final String name;

	PreferentialType(int flag, String name) {
		this.flag = flag;
		this.name = name;
	}

	@Override
	public int getCode() {
		return flag;
	}

	@Override
	public String getName() {
		return name;
	}
}
