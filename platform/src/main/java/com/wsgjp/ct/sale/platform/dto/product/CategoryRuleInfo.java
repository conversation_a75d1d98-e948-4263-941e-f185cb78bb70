package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CategoryRuleInfo {
    /**
     * 类目ID
     */
    private String categoryId;
    /**
     * 标品类型1:UPC、2:自定义、3:型号、 4:货号、5:ISBN码、6:汽车标品ID类型、7:同品标品ID类型
     */
    private String type;
    private String typeName;
    /**
     * 标品维度 1:product维度;2:sku维度
     */
    private String dim;
    /**
     * 品牌信息
     */
    private List<BrandInfo> brandInfos;

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDim() {
        return dim;
    }

    public void setDim(String dim) {
        this.dim = dim;
    }

    public List<BrandInfo> getBrandInfos() {
        return brandInfos;
    }

    public void setBrandInfos(List<BrandInfo> brandInfos) {
        this.brandInfos = brandInfos;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}
