package com.wsgjp.ct.sale.platform.dto.order.entity;

import java.math.BigInteger;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-12-31 17:15
 */
public class QueryOrderParameter  {
    private List<String> tradeOrderIds;
    private List<BigInteger> otypeIds;
    private BigInteger profileId;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public List<String> getTradeOrderIds() {
        return tradeOrderIds;
    }

    public void setTradeOrderIds(List<String> tradeOrderIds) {
        this.tradeOrderIds = tradeOrderIds;
    }

    public List<BigInteger> getOtypeIds() {
        return otypeIds;
    }

    public void setOtypeIds(List<BigInteger> otypeIds) {
        this.otypeIds = otypeIds;
    }
}
