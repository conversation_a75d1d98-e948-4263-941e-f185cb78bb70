package com.wsgjp.ct.sale.platform.slice;

/**
 * <AUTHOR>
 */
public class SliceRequest {
    /**
     * 订单状态的枚举code，下载网店商品指的是上下架状态，下载原始账单目前无
     */
    private int status;

    /**
     * 是否是增量
     */
    private Boolean increment;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Boolean getIncrement() {
        return increment;
    }

    public void setIncrement(Boolean increment) {
        this.increment = increment;
    }
}
