package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PickupOrderTypeEnum implements CodeEnum {
    JIT_ORDER(1,"入库单"),
    STOCK_ORDER(2,"发货单");

    private int code;
    private String name;

    PickupOrderTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PickupOrderTypeEnum valueOf(int code) {
        for (PickupOrderTypeEnum pickupOrderType : values()) {
            if (pickupOrderType.getCode() == code) {
                return pickupOrderType;
            }
        }
        return PickupOrderTypeEnum.JIT_ORDER;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
