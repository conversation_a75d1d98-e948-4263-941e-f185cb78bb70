package com.wsgjp.ct.sale.platform.dto.order.entity;

import java.math.BigInteger;
import java.util.List;


public class QuerySimpleOrderDetailParameter {
    private BigInteger profileId;
    private BigInteger eshopId;
    private List<String> tradeOrderIdList;
    private List<String> tradeOrderDetailIds;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public List<String> getTradeOrderIdList() {
        return tradeOrderIdList;
    }

    public void setTradeOrderIdList(List<String> tradeOrderIdList) {
        this.tradeOrderIdList = tradeOrderIdList;
    }

    public List<String> getTradeOrderDetailIds() {
        return tradeOrderDetailIds;
    }

    public void setTradeOrderDetailIds(List<String> tradeOrderDetailIds) {
        this.tradeOrderDetailIds = tradeOrderDetailIds;
    }
}
