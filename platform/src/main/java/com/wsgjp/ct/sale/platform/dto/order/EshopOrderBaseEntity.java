package com.wsgjp.ct.sale.platform.dto.order;


import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import com.wsgjp.ct.sale.platform.dto.order.entity.ReceiverInfo;

import java.util.Date;

public class EshopOrderBaseEntity extends ErpBaseInfo {

    /**
     * 订单号
     */
    private String tradeId;
    /**
     * 订单状态（交易状态）
     */
    private TradeStatus tradeStatus;

    /**
     * 拍下时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifiedTime;
    /**
     * 买家信息（收货人信息+收货地址信息）
     */
    private ReceiverInfo receiverInfo;

    /**
     * 订单卖家备注
     */
    private String sellerMemo;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 发货时间
     */
    private Date deliveredTime;


    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public ReceiverInfo getReceiverInfo() {
        return receiverInfo;
    }

    public void setReceiverInfo(ReceiverInfo receiverInfo) {
        this.receiverInfo = receiverInfo;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getDeliveredTime() {
        return deliveredTime;
    }

    public void setDeliveredTime(Date deliveredTime) {
        this.deliveredTime = deliveredTime;
    }
}
