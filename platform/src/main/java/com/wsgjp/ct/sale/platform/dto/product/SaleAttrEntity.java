package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.common.enums.publish.AttrDimEnum;

/**
 * <AUTHOR>
 */
public class SaleAttrEntity {
    /**
     * valueTemplateId：模版ID
     */
    private String id;

    /**
     * 结构化组件值
     */
    private String value;

    /**
     * 数值单位
     */
    private String unit;

    /**
     * 色卡模版标准值ID
     */
    private String valueId;
    /**
     * 销售属性维度
     */
    private AttrDimEnum dim;

    private int sort;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getValueId() {
        return valueId;
    }

    public void setValueId(String valueId) {
        this.valueId = valueId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public AttrDimEnum getDim() {
        return dim;
    }

    public void setDim(AttrDimEnum dim) {
        this.dim = dim;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}
