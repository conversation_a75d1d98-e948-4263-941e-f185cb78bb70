package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.common.enums.publish.AttrDimEnum;
import com.wsgjp.ct.sale.common.enums.publish.InputTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SalePropInfo {
    /**
     * 是否结构化(属性值是否需要由多个子属性值拼接得到)
     */
    private boolean isStruct;

    /**
     * 是否支持属性备注
     */
    private boolean supportRemark;
    /**
     * 是否支持自定义
     */
    private boolean supportDiy;
    /**
     * 属性ID
     */
    private String propId;
    /**
     * 属性名称
     */
    private String name;

    /**
     * 销售属性对应的父规格id。拼多多用：拼多多仅在这个层级返回
     */
    private String parentSpecId;

    /**
     * 属性维度
     */
    private AttrDimEnum dim;
    /**
     * 销售属性模板信息
     */
    private List<SaleAttrValueTemplate> saleAttrValueTemplates;

    /**
     * 度量衡模版，g kg,也可以放在销售属性里
     */
    private List<MeasureTemplates> measureTemplates;

    /**
     * 同一个维度可能有多个属性
     */
    private int sort;
    /**
     * 是否必填
     */
    private boolean isRequired;
    /**
     * 输入类型(结构化模板可以不填),结构化模板一个属性的值由多个子属性value值拼接成
     */
    private InputTypeEnum inputType;

    /**
     * 抖店发品内部流转替换属性时用
     */
    private InputTypeEnum douDianInputType;

    private String platformCateId;

    //尺码表模板
    private List<SalePropSpecSizeTemplate> propSpecSizeTemplates;

    private String propSizeMetaName;

    public InputTypeEnum getDouDianInputType() {
        return douDianInputType;
    }

    public void setDouDianInputType(InputTypeEnum douDianInputType) {
        this.douDianInputType = douDianInputType;
    }

    /**
     * 选了品牌或者产品规格后会进行替换
     */
    private Long sellPropertyId;

    public List<MeasureTemplates> getMeasureTemplates() {
        return measureTemplates;
    }

    public void setMeasureTemplates(List<MeasureTemplates> measureTemplates) {
        this.measureTemplates = measureTemplates;
    }

    public Long getSellPropertyId() {
        return sellPropertyId;
    }

    public void setSellPropertyId(Long sellPropertyId) {
        this.sellPropertyId = sellPropertyId;
    }

    public void setStruct(boolean struct) {
        isStruct = struct;
    }

    public boolean isSupportRemark() {
        return supportRemark;
    }

    public void setSupportRemark(boolean supportRemark) {
        this.supportRemark = supportRemark;
    }

    public boolean isSupportDiy() {
        return supportDiy;
    }

    public void setSupportDiy(boolean supportDiy) {
        this.supportDiy = supportDiy;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public AttrDimEnum getDim() {
        return dim;
    }

    public void setDim(AttrDimEnum dim) {
        this.dim = dim;
    }

    public List<SaleAttrValueTemplate> getSaleAttrValueTemplates() {
        return saleAttrValueTemplates;
    }

    public void setSaleAttrValueTemplates(List<SaleAttrValueTemplate> saleAttrValueTemplates) {
        this.saleAttrValueTemplates = saleAttrValueTemplates;
    }

    public boolean getStruct() {
        return isStruct;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getPropId() {
        return propId;
    }

    public void setPropId(String propId) {
        this.propId = propId;
    }

    public boolean isRequired() {
        return isRequired;
    }

    public void setRequired(boolean required) {
        isRequired = required;
    }

    public InputTypeEnum getInputType() {
        return inputType;
    }

    public void setInputType(InputTypeEnum inputType) {
        this.inputType = inputType;
    }

    public String getPlatformCateId() {
        return platformCateId;
    }

    public void setPlatformCateId(String platformCateId) {
        this.platformCateId = platformCateId;
    }


    public List<SalePropSpecSizeTemplate> getPropSpecSizeTemplates() {
        return propSpecSizeTemplates;
    }

    public void setPropSpecSizeTemplates(List<SalePropSpecSizeTemplate> propSpecSizeTemplates) {
        this.propSpecSizeTemplates = propSpecSizeTemplates;
    }

    public String getPropSizeMetaName() {
        return propSizeMetaName;
    }

    public void setPropSizeMetaName(String propSizeMetaName) {
        this.propSizeMetaName = propSizeMetaName;
    }

    public String getParentSpecId() {
        return parentSpecId;
    }

    public void setParentSpecId(String parentSpecId) {
        this.parentSpecId = parentSpecId;
    }
}
