package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.platform.enums.PreferentialType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import ngp.utils.StringUtils;

import java.math.BigDecimal;

/**
 * 订单明细商品优惠
 */
@ApiModel("订单明细商品优惠")
public class EshopPreferentialDetail {
    public EshopPreferentialDetail() {
    }

    public EshopPreferentialDetail(String oid, BigDecimal total, PreferentialType type) {
        this.oid = oid;
        this.total = total;
        this.type = type;
    }

    /**
     * 子订单id
     */
    @ApiModelProperty("子订单id")
    private String oid;
    /**
     * 明细单品优惠金额
     */
    @ApiModelProperty("明细单品优惠金额")
    private BigDecimal total;

    /**
     * 主表优惠分摊(平台接口中可以不用赋值，构建订单时会分摊主表整单优惠过来)
     */
    @ApiModelProperty("主表优惠分摊(平台接口中可以不用赋值，构建订单时会分摊主表整单优惠过来)")
    private BigDecimal totalShare;

    /**
     * 优惠类型：商家优惠/平台优惠/主播优惠/平台补贴优惠
     */
    @ApiModelProperty("优惠类型：商家优惠/平台优惠/主播优惠/平台补贴优惠")
    private PreferentialType type;
    @ApiModelProperty("平台优惠类型名称")
    private String platformCouponTypeName;


    public String getOid() {
        if (StringUtils.isEmpty(oid)) {
            return "";
        }
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public BigDecimal getTotal() {
        if (total == null) {
            return BigDecimal.ZERO;
        }
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public PreferentialType getType() {
        if (type == null) {
            return PreferentialType.SELLER;
        }
        return type;
    }

    public void setType(PreferentialType type) {
        this.type = type;
    }

    public BigDecimal getTotalShare() {
        if (totalShare == null) {
            return BigDecimal.ZERO;
        }
        return totalShare;
    }

    public void setTotalShare(BigDecimal totalShare) {
        this.totalShare = totalShare;
    }

    public String getPlatformCouponTypeName() {
        return platformCouponTypeName;
    }

    public void setPlatformCouponTypeName(String platformCouponTypeName) {
        this.platformCouponTypeName = platformCouponTypeName;
    }

}
