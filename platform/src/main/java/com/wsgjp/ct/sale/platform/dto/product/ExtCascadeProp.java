package com.wsgjp.ct.sale.platform.dto.product;

import com.jd.open.api.sdk.domain.supplier.ProductManagementService.response.find.ExtPropDtow;
import com.jd.open.api.sdk.domain.supplier.ProductManagementService.response.find.ExtPropValueDto;

import java.util.List;

/**
 * 级联属性
 * <AUTHOR>
 */
public class ExtCascadeProp {
    /**
     * 属性ID
     */
    private Integer propId;
    /**
     * 属性名称
     */
    private String propName;
    private Integer cid;
    private Integer cataClass;
    private Integer type;
    private Integer orderSort;
    private Integer isRequired;
    private Integer isShield;
    private Integer isSearch;
    private Integer isKeyProperty;
    private Integer isCustom;
    private Integer isMultiSele;
    private Integer colNum;
    private Integer yn;
    private Integer groupId;
    private Integer inputType;
    private String attrAlias;
    private String valUnit;
    private String maintainRemark;
    private List<ExtPropValueDto> extPropValue;
    private Integer valCount;
    private Integer cascadeGroupId;
    private String cascadeGroupName;
    private Integer cascadeGroupLevel;
    private String inputAreaLevel;
    private List<ExtPropDtow> cascadePropList;
    private Integer originInputType;
}
