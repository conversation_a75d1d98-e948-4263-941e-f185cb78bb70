package com.wsgjp.ct.sale.platform.dto.stock;


import com.wsgjp.ct.sale.platform.enums.EshopWareHouseType;
import com.wsgjp.ct.sale.platform.enums.WareHouseBusinessType;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 电商仓实体
 * 电商仓用于和客户真实仓库对应，同步线上库存，商品使用
 * 电商门店用于客户指定线下发货门店，订单使用
 **/
public class EshopPlatformWareHouse extends BaseWareHouseInfo {

    /**
     * 0=电商仓；1=电商门店
     */
    private EshopWareHouseType eshopWareHouseType;

    /**
     * 仓库业务类型
     */
    private WareHouseBusinessType businessType;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 早上开始营业时间
     */
    private Date openTime;

    /**
     * 晚上关店时间
     */
    private Date closeTime;

    /**
     * 每周营业时间
     */
    private List<String> weekDays;

    public EshopWareHouseType getEshopWareHouseType() {
        return eshopWareHouseType;
    }

    public void setEshopWareHouseType(EshopWareHouseType eshopWareHouseType) {
        this.eshopWareHouseType = eshopWareHouseType;
    }

    public WareHouseBusinessType getBusinessType() {
        return businessType;
    }

    public void setBusinessType(WareHouseBusinessType businessType) {
        this.businessType = businessType;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public Date getOpenTime() {
        return openTime;
    }

    public void setOpenTime(Date openTime) {
        this.openTime = openTime;
    }

    public Date getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    public List<String> getWeekDays() {
        return weekDays;
    }

    public void setWeekDays(List<String> weekDays) {
        this.weekDays = weekDays;
    }
}
