package com.wsgjp.ct.sale.platform.dto.order.entity;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/9 16:07
 */
public class EshopDeliverOrderEntity {
    private String tradeId;
    private String status;
    private List<String> availableWarehouses;
    private String erpSelectedWarehouse;
    private List<DeliverOrderDetailEntity> orderDetails;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<String> getAvailableWarehouses() {
        return availableWarehouses;
    }

    public void setAvailableWarehouses(List<String> availableWarehouses) {
        this.availableWarehouses = availableWarehouses;
    }

    public String getErpSelectedWarehouse() {
        return erpSelectedWarehouse;
    }

    public void setErpSelectedWarehouse(String erpSelectedWarehouse) {
        this.erpSelectedWarehouse = erpSelectedWarehouse;
    }

    public List<DeliverOrderDetailEntity> getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(List<DeliverOrderDetailEntity> orderDetails) {
        this.orderDetails = orderDetails;
    }
}

