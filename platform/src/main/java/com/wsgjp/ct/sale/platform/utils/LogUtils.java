package com.wsgjp.ct.sale.platform.utils;

import cn.hutool.json.JSONUtil;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformEshopConfig;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.entity.request.product.ModifyStockParam;
import com.wsgjp.ct.sale.platform.enums.PlatformBizTypeEnum;
import com.wsgjp.ct.sale.platform.log.ApiTrade;
import com.wsgjp.ct.sale.platform.log.PlatformBizLog;
import com.wsgjp.ct.support.log.service.LogService;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.List;

/**
 * 店铺日志输出工具
 *
 * <AUTHOR>
 */
public class LogUtils {
    private static final String ERR_LOG_FORMAT1 = "账套ID:{},店铺ID:{},{}店铺,{}出错,错误信息:{}.\n";
    private static final String ERR_LOG_FORMAT2 = "账套ID:{},店铺ID:{},{}店铺,{}出错,错误信息:{}.request:{},response:{}\n";
    private static final String ERR_LOG_FORMAT3 = "账套ID:{},店铺ID:{},{}店铺,{}出错,错误信息:{}.request:{}\n";
    private static final String LOG_FORMAT = "账套ID:{},店铺ID:{},{}店铺,{}.request:{},response:{}\n";
    private final EshopSystemParams systemParams;

    private static final Logger thisLogger = LoggerFactory.getLogger(LogUtils.class);

    public LogUtils(EshopSystemParams systemParams) {
        this.systemParams = systemParams;
    }

    /**
     * @param logger  Logger
     * @param bizName 业务描述
     * @param errMsg  错误信息
     */
    public void log(Logger logger, String bizName, String errMsg) {
        logger.error(ERR_LOG_FORMAT1, systemParams.getProfileId(), systemParams.geteShopId(), shopTypeName(), bizName, errMsg);
    }

    /**
     * @param logger  Logger
     * @param bizName 业务描述
     * @param errMsg  错误信息
     * @param ex      异常对象
     */
    public void log(Logger logger, String bizName, String errMsg, Exception ex) {
        logger.error(ERR_LOG_FORMAT1, systemParams.getProfileId(), systemParams.geteShopId(), shopTypeName(), bizName, errMsg, ex);
    }

    /**
     * @param logger   Logger
     * @param bizName  业务描述
     * @param errMsg   错误信息
     * @param request  接口请求对象
     * @param response 接口响应对象
     */
    public void log(Logger logger, String bizName, String errMsg, Object request, Object response) {
        logger.error(ERR_LOG_FORMAT2, systemParams.getProfileId(), systemParams.geteShopId(), shopTypeName(),
                bizName, errMsg, JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(response));
    }

    public void log(Logger logger, String bizName, Object request, Object response) {
        logger.error(LOG_FORMAT, systemParams.getProfileId(), systemParams.geteShopId(), shopTypeName(),
                bizName, JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(response));
    }

    /**
     * @param logger   Logger
     * @param bizName  业务描述
     * @param errMsg   错误信息
     * @param request  接口请求对象
     * @param response 接口响应对象
     * @param ex       异常对象
     */
    public void log(Logger logger, String bizName, String errMsg, Object request, Object response, Exception ex) {
        logger.error(ERR_LOG_FORMAT2, systemParams.getProfileId(), systemParams.geteShopId(), shopTypeName(),
                bizName, errMsg, JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(response), ex);
    }

    /**
     * @param logger  Logger
     * @param bizName 业务描述
     * @param errMsg  错误信息
     * @param request 接口请求对象
     * @param ex      异常对象
     */
    public void log(Logger logger, String bizName, String errMsg, Object request, Exception ex) {
        logger.error(ERR_LOG_FORMAT3, systemParams.getProfileId(), systemParams.geteShopId(), shopTypeName(),
                bizName, errMsg, JSONUtil.toJsonStr(request), ex);
    }

    private String shopTypeName() {
        return systemParams.getShopType() != null ? systemParams.getShopType().getName() : "";
    }

    private static boolean checkCanLogStock(EshopSystemParams params) {
        try {
            PlatformEshopConfig eshopConfig = BeanUtils.getBean(PlatformEshopConfig.class);
            String typeName = params.getShopType().name().toLowerCase();
            String logShopTypes = eshopConfig.getNeedWriteStockBizLogShopTypes();
            if (StringUtils.isEmpty(logShopTypes)) {
                return false;
            }
            String[] split = logShopTypes.split(",");
            for (String s : split) {
                if (s.toLowerCase().equals(typeName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }

    public static void doWriteBizLog(PlatformBizLog log, EshopSystemParams params) {
        try {
            log.setEshopType(params.getShopType());
            log.setEshopId(params.geteShopId());
            if (StringUtils.isEmpty(log.getHashMark())) {
                String mark = Md5Utils.md5(log.getResponseInfo());
                log.setHashMark(mark);
            }

            if (log.getBizType().equals(PlatformBizTypeEnum.SYNC_STOCK)) {
                // 库存同步日志太多了，暂时屏蔽
                boolean canWrite = checkCanLogStock(params);
                if (!canWrite) {
                    return;
                }
            }
            if (log.getBizType().equals(PlatformBizTypeEnum.SEND_GOODS)) {
                LogService.update(log);
                return;
            }
            String logLockKey = String.format("platform_biz_log_lock_%s_%s_%s", params.getProfileId(), params.geteShopId(), log.getBizType());
            String formatDateTime = DateUtils.formatDateTime(DateUtils.getDate());
            String formatKey = String.format("%s_%s", logLockKey, formatDateTime);
            if (!CommonUtils.getConcurrencyLock(50, formatKey)) {
                return;
            }
            LogService.update(log);
        } catch (Exception ex) {
            thisLogger.error("记录日志报错:{}", ex.getMessage());
        }
    }

    public static String getSyncStockMark(ModifyStockParam params) {
        return Md5Utils.md5(JsonUtils.toJson(params));
    }

    public static PlatformBizLog buildPlatformStockBizLog(String request, String response, EshopSystemParams params) {
        return buildPlatformBizLog(request, response, PlatformBizTypeEnum.SYNC_STOCK, params);
    }

    public static PlatformBizLog buildPlatformBizLog(String request, String response, PlatformBizTypeEnum bizType, EshopSystemParams params) {
        PlatformBizLog log = new PlatformBizLog();
        log.setBizType(bizType);
        log.setEshopId(params.geteShopId());
        log.setEshopType(params.getShopType());
        log.setRequestInfo(request);
        log.setResponseInfo(response);
        return log;
    }

    public static void doBuildAndWriteBizLog(String request, String response, PlatformBizTypeEnum bizType, EshopSystemParams params) {
        PlatformBizLog log = buildPlatformBizLog(request, response, bizType, params);
        doWriteBizLog(log, params);
    }

    public static void saveTradeLog(List<ApiTrade> tradeList) {
        if (CollectionUtils.isEmpty(tradeList)) {
            return;
        }
        for (ApiTrade trade : tradeList) {
            trade.setEtypeId(BigInteger.ZERO);
            LogService.update(trade);
        }
    }

    public static void doWritTradeLog(String body, EshopOrderEntity orderEntity, String request, EshopSystemParams systemParams) {
        ApiTrade trade = new ApiTrade();
        trade.setTradeId(orderEntity.getTradeId());
        trade.setEshopId(systemParams.geteShopId());
        trade.setProfileId(systemParams.getProfileId());
        trade.setRequest(request);
        trade.setHashMark(orderEntity.getHashMark());
        trade.setBody(body);
        trade.setEtypeId(BigInteger.ZERO);
        LogService.update(trade);
    }
}
