package com.wsgjp.ct.sale.platform.dto.purchase;

import com.wsgjp.ct.sale.platform.dto.sendgoods.SendGoodsFreightInfo;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


/**
 * @description: 订单签收信息
 * @author: lj
 * @create: 2022-03-11
 */
public class SignGoodsEntity {

    @ApiModelProperty("订单号")
    private String tradeId;

    @ApiModelProperty("物流信息")
    private List<SendGoodsFreightInfo> freightInfo;

    @ApiModelProperty("erp中单据编号")
    private String erpTradeId;

    @ApiModelProperty("入库说明，备注信息等")
    private String remark;

    @ApiModelProperty("入库商品详情")
    private List<SignGoodsDetail>  goodsDetails;


    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public List<SendGoodsFreightInfo> getFreightInfo() {
        return freightInfo;
    }

    public void setFreightInfo(List<SendGoodsFreightInfo> freightInfo) {
        this.freightInfo = freightInfo;
    }

    public String getErpTradeId() {
        return erpTradeId;
    }

    public void setErpTradeId(String erpTradeId) {
        this.erpTradeId = erpTradeId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<SignGoodsDetail> getGoodsDetails() {
        return goodsDetails;
    }

    public void setGoodsDetails(List<SignGoodsDetail> goodsDetails) {
        this.goodsDetails = goodsDetails;
    }
}
