package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 邮费承担方
 */
public enum PostFeeBearRoleType implements CodeEnum {
    /**
     * 卖家承担
     */
    SELLER(0, "卖家承担运费"),
    /**
     * 货到付款
     */
    BUYER(1, "买家承担运费")
   ;

    private int flag;

    private String name;

    PostFeeBearRoleType(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

}