package com.wsgjp.ct.sale.platform.slice;

import com.wsgjp.ct.sale.platform.enums.SliceType;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class DownloadSlice extends BaseSlice {
    private Date startTime;
    private Date endTime;
    private int page;
    private int pageSize;
    private String cursor;
    private boolean rds;
    private int businessType;
    private SliceType type;
    private SliceParams sliceParams;
    private int status;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public boolean isRds() {
        return rds;
    }

    public void setRds(boolean rds) {
        this.rds = rds;
    }

    public int getBusinessType() {
        return businessType;
    }

    public void setBusinessType(int businessType) {
        this.businessType = businessType;
    }

    public String getCursor() {
        return cursor;
    }

    public void setCursor(String cursor) {
        this.cursor = cursor;
    }

    public SliceType getType() {
        return type;
    }

    public void setType(SliceType type) {
        this.type = type;
    }

    public SliceParams getSliceParams() {
        return sliceParams;
    }

    public void setSliceParams(SliceParams sliceParams) {
        this.sliceParams = sliceParams;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
