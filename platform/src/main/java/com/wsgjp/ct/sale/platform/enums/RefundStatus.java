package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 售后状态
 */
public enum RefundStatus implements CodeEnum {
    /**
     * 没有退款
     */
    NONE(0, "没有退款", "NO_REFUND"),
    /**
     * 买家申请，等待卖家同意
     */
    WAIT_SELLER_AGREE(1, "等待卖家同意", "REFUNDING"),

    /**
     * 退款退货时：需要买家处理的部分
     */
    WAIT_BUYER_SETTLE(2, "等待买家处理", "REFUNDING"),
    /**
     * 卖家同意
     */
    SELLER_AGREE(3, "卖家同意", "REFUNDING"),
    /**
     * 卖家拒绝
     */
    SELLER_REFUSE(4, "卖家拒绝", "NO_REFUND"),
    /**
     * 退款成功
     */
    SUCCESS(5, "退款成功", "FINISH"),
    /**
     * 退款取消
     */
    CANCEL(6, "退款取消", "NO_REFUND"),
    /**
     * 平台处理中
     */
    PLATFORM_HANDING(7, "平台处理中", "REFUNDING"),

    /**
     * 未知状态
     */
    UN_KNOW(8, "未知", "REFUNDING"),
    ;


    private int flag;

    private String name;

    private String deliverRefundStatus;

    RefundStatus(int flag, String name, String deliverRefundStatus) {
        this.flag = flag;
        this.name = name;
        this.deliverRefundStatus = deliverRefundStatus;
    }


    public static RefundStatus findByFlag(int flag) {
        for (RefundStatus productMarkType : RefundStatus.values()) {
            if (flag == productMarkType.getCode()) {
                return productMarkType;
            }
        }
        return null;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public String getDeliverRefundStatus() {
        return deliverRefundStatus;
    }
}
