package com.wsgjp.ct.sale.platform.mock;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformEshopConfig;
import eshopsdkbase.BaseClient;
import eshopsdkbase.BaseRequest;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;

import java.lang.reflect.Method;
import java.util.Map;

public class MockClientProxy {
    public static PlatformEshopConfig eshopConfig;
    private static final Logger logger = LoggerFactory.getLogger(MockClientProxy.class);

    public MockClientProxy(PlatformEshopConfig eshopConfig) {
        MockClientProxy.eshopConfig = eshopConfig;
    }

    public static <T extends BaseClient> T newMockClient(final T originalClient, EshopSystemParams params) {
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(originalClient.getClass());
        enhancer.setCallback(new MethodInterceptor() {
            @Override
            public Object intercept(Object object, Method method, Object[] args, MethodProxy proxy) throws Throwable {
                ShopType shopType = params.getShopType();
                String platformName = shopType.toString().toLowerCase();
                Map<String, Map<String, PlatformEshopConfig.MockData>> mock = eshopConfig.getMock();
                if (mock == null || mock.get(platformName) == null || !method.getName().contains("execute") || args.length == 0 || !(args[0] instanceof BaseRequest)) {
                    return proxy.invoke(originalClient, args);
                }
                BaseRequest request = (BaseRequest) args[0];
                String apiName = request.getApiName().replaceAll("\\.", "").replaceAll("/", "");
                PlatformEshopConfig.MockData mockData = mock.get(platformName).get(apiName.toLowerCase());
                if (mockData == null) {
                    return proxy.invoke(originalClient, args);
                }
                Class<?> responseType = null;
                try {
                    responseType = Class.forName(mockData.getType());
                } catch (Throwable e) {
                    logger.warn("Mock数据，响应类型设置错误，请检查Apollo设置", e);
                }
                return JsonUtils.toObject(mockData.getValue(), responseType != null ? responseType : method.getReturnType());
            }
        });
        return (T) enhancer.create();
    }
}
