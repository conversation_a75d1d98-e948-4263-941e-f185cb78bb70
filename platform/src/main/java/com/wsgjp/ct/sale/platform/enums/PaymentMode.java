package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PaymentMode implements CodeEnum {
    EMPTY(0, ""),
    PT_SHOP(1, "现金"),
    DYY_SHOP(2, "银行转账"),
    <PERSON>Y_SHOP(3, "支付宝"),
    LS_SHOP(4, "微信"),
    COD(5, "货到付款");

    private int index;
    private String name;

    PaymentMode(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }

    public static PaymentMode nameValueOf(String name) {
        for (PaymentMode invoiceType : values()) {
            if (name.equals(invoiceType.getName())) {
                return invoiceType;
            }
        }
        return null;
    }
}
