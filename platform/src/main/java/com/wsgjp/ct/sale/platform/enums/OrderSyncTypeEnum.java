package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum OrderSyncTypeEnum implements CodeEnum {
    /**
      * 自动下单任务状态
     */
    AUTO_INCREASE(0, "自动下单",""),
    AUTO_CONSTANT(1, " 常量下单",""),
    INCREASE_RETRY(2, "增量补单","increase"),
    CONSTANT_RETRY(3, "常量补单","full"),
    MUL_INCREASE(4, "手工下单",""),
    INCREASE_ERROR_RETRY(5, "自动下单错误重试补单","");


    private final int code;
    private final String name;
    private final String tag;

    OrderSyncTypeEnum(int code, String name, String tag) {
        this.code = code;
        this.name = name;
        this.tag = tag;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public String getTag() {
        return tag;
    }

}
