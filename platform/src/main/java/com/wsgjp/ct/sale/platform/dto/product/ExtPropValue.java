package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

public class ExtPropValue {
    /**
     * 值id
     */
    private String valueId;
    /**
     * 属性ID
     */
    private String propId;
    /**
     * 值名称
     */
    private String valueName;
    /**
     * 值类型
     */
    private Integer type;
    /**
     * 是否有效(1有效,0无效)
     */
    private Integer yn;
    /**
     * 品牌
     */
    private String brandPrx;
    /**
     * 排序标记
     */
    private Integer sort;
    /**
     * 依等级排列
     */
    private Integer gradeAvg;
    /**
     * 注释
     */
    private String remarks;
    /**
     * 是否必填
     */
    private Integer isRequired;
    /**
     * 属性值别名的内容
     */
    private String aliasContent;
    /**
     * 主要填写百分比数，多输入前缀如长，宽，高
     */
    private String vremark;
    /**
     * 单位模板值
     */
    private List<String[]> units;
    /**
     * 单位
     */
    private String unit;
    /**
     * 父属性值Id
     */
    private String parentId;

    public String getValueId() {
        return valueId;
    }

    public void setValueId(String valueId) {
        this.valueId = valueId;
    }

    public String getPropId() {
        return propId;
    }

    public void setPropId(String propId) {
        this.propId = propId;
    }

    public String getValueName() {
        return valueName;
    }

    public void setValueName(String valueName) {
        this.valueName = valueName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }

    public String getBrandPrx() {
        return brandPrx;
    }

    public void setBrandPrx(String brandPrx) {
        this.brandPrx = brandPrx;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getGradeAvg() {
        return gradeAvg;
    }

    public void setGradeAvg(Integer gradeAvg) {
        this.gradeAvg = gradeAvg;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    public String getAliasContent() {
        return aliasContent;
    }

    public void setAliasContent(String aliasContent) {
        this.aliasContent = aliasContent;
    }

    public String getVremark() {
        return vremark;
    }

    public void setVremark(String vremark) {
        this.vremark = vremark;
    }

    public List<String[]> getUnits() {
        return units;
    }

    public void setUnits(List<String[]> units) {
        this.units = units;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
}
