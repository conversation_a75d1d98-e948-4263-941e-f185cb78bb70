package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * TODO ？
 */
public enum PayType implements CodeEnum {
    /**
     * 对账后结算=现场现结
     */
    SCENE_SETTLEMENT(0, "现场现结"),
    /**
     * 货到付款
     */
    CASH_ON_DELIVERY(1, "货到付款"),
    /**
     * 月结
     */
    MONTH_SETTLEMENT(2, "月结");

    private int flag;

    private String name;

    PayType(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public static PayType nameValueOf(String name) {
        for (PayType invoiceType : values()) {
            if (name.equals(invoiceType.getName())) {
                return invoiceType;
            }
        }
        return null;
    }
}
