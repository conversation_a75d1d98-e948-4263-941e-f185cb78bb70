package com.wsgjp.ct.sale.platform.log;

import com.wsgjp.ct.support.log.annotation.LogEntity;
import com.wsgjp.ct.support.log.entity.BaseLog;
import com.wsgjp.ct.support.log.type.DBType;

import java.math.BigInteger;

/**
 * <AUTHOR> 2023/11/9 14:45
 */
@LogEntity(tableName = "pl_platform_trade_log", isReplaceInto = true, dbType = DBType.BUSSINESS)
public class ApiTrade extends BaseLog {

    private BigInteger eshopId;
    private String messageId = "";
    private String tradeId;
    private String body;
    private String request;
    private String hashMark;


    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getHashMark() {
        if(hashMark==null){

        }
        return hashMark;
    }

    public void setHashMark(String hashMark) {
        this.hashMark = hashMark;
    }
}
