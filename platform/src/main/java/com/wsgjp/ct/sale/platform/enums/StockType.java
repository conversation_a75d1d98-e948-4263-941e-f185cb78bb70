package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public enum StockType implements Serializable, CodeEnum {

    Generall(0, "普通库存"),
    <PERSON><PERSON>(1, "阶梯库存"),
    <PERSON><PERSON><PERSON>(2, "分仓库存"),
    AGING_STOCK(3,"时效库存");

    private int code;
    @JSONField(serialize = false)
    private String name;

    StockType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public static StockType typeOf(int code) {
        for (StockType stockType : values()) {
            if (stockType.getCode() == code) {
                return stockType;
            }
        }
        throw new RuntimeException("不支持的同步库存枚举");
    }

}
