package com.wsgjp.ct.sale.platform.dto.product;

/**
 * 属性值实体，存储属性的值信息
 * <AUTHOR>
 */
public class ProductPropItemValue {
    /**
     * 属性ID
     */
    private String propId;
    /**
     * 属性值ID
     */
    private String valueId;
    /**
     * 属性值
     */
    private String value;
    /**
     * 属性值排序
     */
    private int sort;
    /**
     * 属性值扩展字段
     */
    private String attributes;

    public String getPropId() {
        return propId;
    }

    public void setPropId(String propId) {
        this.propId = propId;
    }

    public String getValueId() {
        return valueId;
    }

    public void setValueId(String valueId) {
        this.valueId = valueId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }
}
