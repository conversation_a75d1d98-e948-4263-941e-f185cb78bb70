package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum CarTypeEnum implements CodeEnum {
    /**
     * 车型
     * 2: 额定载重2T，核载体积15方，长宽高4.2*1.9*1.9
     * 3: 额定载重12T，核载体积45方，长宽高7.6*2.4*2.7
     * 4: 额定载重15T，核载体积55方，长宽高9.6*2.4*2.7
     * 7: 其它
     */

    TWO_TON(2, "额定载重2T"),
    THREE_TON(3, "额定载重12T"),
    FIFTEEN_TON(4, "额定载重15T"),
    OTHER(7, "其它");


    private int code;
    private String name;

    CarTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CarTypeEnum valueOf(int code) {
        for (CarTypeEnum carType : values()) {
            if (carType.getCode() == code) {
                return carType;
            }
        }
        return CarTypeEnum.OTHER;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
