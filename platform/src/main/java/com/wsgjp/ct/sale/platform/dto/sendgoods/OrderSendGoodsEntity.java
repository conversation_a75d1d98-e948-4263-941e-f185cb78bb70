package com.wsgjp.ct.sale.platform.dto.sendgoods;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillDeliverType;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.sale.platform.enums.SendType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-01-08 17:19
 */
@ApiModel("发货订单信息对象")
public class OrderSendGoodsEntity {
    @ApiModelProperty("是否拆单发货")
    private boolean isSplit;
    /**
     * 是否合单
     */
    @ApiModelProperty("是否合单发货")
    private boolean isMergerd;
    /**
     * erp的订单id，部分平台需要
     */
    @ApiModelProperty(value = "erp的订单Id", notes = "部分平台需要")
    private String orderId;
    @ApiModelProperty(value = "订单Id", required = true)
    private String tradeId;
    @ApiModelProperty("是否需要报告")
    private boolean needReport;
    @ApiModelProperty("发货订单明细列表")
    private List<SendGoodsDetail> orderDetailList;
    @ApiModelProperty("发货物流信息")
    private SendGoodsFreightInfo freightInfo;
    @ApiModelProperty(value = "发货类型", notes = "默认：0")
    private SendType sendType = SendType.Normal;
    @ApiModelProperty("平台特殊传递信息")
    private String platformSpecialJson;
    @ApiModelProperty("店铺码")
    private String storeCode;
    @ApiModelProperty("账单交付类型")
    private BillDeliverType deliverType;
    @ApiModelProperty("备注信息")
    private String remark;
    /**
     * 出仓方式(目前仅用于抖店)
     * delivery_type 是 【0】&& ship_type 是【1】，对应一单一包裹模式；
     * delivery_type 是 【0】&& ship_type 是【3】，对应混订单
     */
    @ApiModelProperty(value = "出仓方式(目前仅用于抖店)", hidden = true)
    private int shipType;
    /**
     * 送检方式(目前仅用于抖店)
     */
    @ApiModelProperty(value = "送检方式(目前仅用于抖店)", hidden = true)
    private int deliveryType;
    @ApiModelProperty("订单标记类型列表")
    private List<BaseOrderMarkEnum> orderMarks;
    /**
     * 发货单售后类型(现用于拼多多额单发货)
     */
    @ApiModelProperty("售后类型")
    private int refundType;

    /**
     * 原始订单信息
     */
    private OrderInformation orderInformation;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public List<SendGoodsDetail> getOrderDetailList() {
        return orderDetailList;
    }

    public void setOrderDetailList(List<SendGoodsDetail> orderDetailList) {
        this.orderDetailList = orderDetailList;
    }

    public String getPlatformSpecialJson() {
        return platformSpecialJson;
    }

    public void setPlatformSpecialJson(String platformSpecialJson) {
        this.platformSpecialJson = platformSpecialJson;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public BillDeliverType getDeliverType() {
        return deliverType;
    }

    public void setDeliverType(BillDeliverType deliverType) {
        this.deliverType = deliverType;
    }

    public SendGoodsFreightInfo getFreightInfo() {
        return freightInfo;
    }

    public void setFreightInfo(SendGoodsFreightInfo freightInfo) {
        this.freightInfo = freightInfo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public boolean isSplit() {
        return isSplit;
    }

    public void setSplit(boolean split) {
        isSplit = split;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public boolean isNeedReport() {
        return needReport;
    }

    public void setNeedReport(boolean needReport) {
        this.needReport = needReport;
    }

    public SendType getSendType() {
        return sendType;
    }

    public void setSendType(SendType sendType) {
        this.sendType = sendType;
    }

    public boolean isMergerd() {
        return isMergerd;
    }

    public void setMergerd(boolean mergerd) {
        isMergerd = mergerd;
    }

    public int getShipType() {
        return shipType;
    }

    public void setShipType(int shipType) {
        this.shipType = shipType;
    }

    public int getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(int deliveryType) {
        this.deliveryType = deliveryType;
    }

    public List<BaseOrderMarkEnum> getOrderMarks() {
        return orderMarks;
    }

    public void setOrderMarks(List<BaseOrderMarkEnum> orderMarks) {
        this.orderMarks = orderMarks;
    }


    public OrderInformation getOrderInformation() {
        return orderInformation;
    }

    public void setOrderInformation(OrderInformation orderInformation) {
        this.orderInformation = orderInformation;
    }

    public int getRefundType() {
        return refundType;
    }

    public void setRefundType(int refundType) {
        this.refundType = refundType;
    }
}
