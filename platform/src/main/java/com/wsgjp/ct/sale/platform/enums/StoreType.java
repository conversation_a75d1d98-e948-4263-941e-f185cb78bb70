package com.wsgjp.ct.sale.platform.enums;

/**
 * 仓库类型(用于平台业务组构建仓库未对应标记tips时提示文字的区分)
 * <AUTHOR>
 */
public enum StoreType {
    STORE(1,"门店"),
    WAREHOUSE(2,"商家仓"),
    STORE_OR_WAREHOUSE(3,"发货仓库/门店"),
    PLATFORM_WAREHOUSE(4,"平台仓");

    private final int type;
    private final String desc;

    StoreType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
