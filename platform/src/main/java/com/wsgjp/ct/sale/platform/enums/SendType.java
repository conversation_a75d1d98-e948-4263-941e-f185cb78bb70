package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public enum SendType implements Serializable, CodeEnum {

    Normal(0, "普通发货"),
    Virtual(1, "虚拟发货"),
    ChinaIn(2, "跨境入口"),
    ChinaOut(3, "跨境出口"),
    SameCity(4, "同城配送"),
    CollectionStock(5, "集货仓发货"),
    SelfPick(6, "门店自提核销");


    SendType(int code, String name) {
        this.code = code;
        this.name = name;
    }


    @JSONField(serialize = false)
    private String name;

    private int code;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }


    public static SendType typeOf(int code) {
        for (SendType sendType : values()) {
            if (sendType.getCode() == code) {
                return sendType;
            }
        }
        throw new RuntimeException("不支持的发货枚举");
    }
}
