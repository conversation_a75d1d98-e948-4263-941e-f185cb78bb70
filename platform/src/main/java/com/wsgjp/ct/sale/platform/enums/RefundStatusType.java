package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 售后状态类型
 **/
public enum RefundStatusType implements CodeEnum {
    /**
     * 部分退款
     */
    PART(0, "部分退款"),
    /**
     * 全部退款
     */
    ALL(1, "全部退款");

    private final int flag;

    private final String name;


    RefundStatusType(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

}
