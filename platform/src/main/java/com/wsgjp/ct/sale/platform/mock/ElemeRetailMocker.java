package com.wsgjp.ct.sale.platform.mock;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class ElemeRetailMocker implements ApiMocker {

    @Override
    public Object execute(Object[] args, Method method, EshopSystemParams params) {
        return null;
    }

    @Override
    public List<ShopType> getShopTypes() {
        List<ShopType> types = new ArrayList<>(2);
        types.add(ShopType.EleMeRetail);
        return types;
    }
}
