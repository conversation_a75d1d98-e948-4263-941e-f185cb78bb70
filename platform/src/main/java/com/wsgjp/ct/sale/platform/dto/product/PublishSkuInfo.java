package com.wsgjp.ct.sale.platform.dto.product;

import java.math.BigDecimal;
import java.util.List;

/**
 * 发布商品时需要的sku商品信息
 *
 * <AUTHOR>
 */
public class PublishSkuInfo {
    /**
     * 含税价
     */
    private BigDecimal taxedPrice;
    /**
     * 不含税价
     */
    private BigDecimal price;
    /**
     * 折后含税价
     */
    private BigDecimal disedTaxedPrice;
    /**
     * 原价
     */
    private BigDecimal initialPrice;
    /**
     * 供应商价(采购价)
     */
    private BigDecimal supplyPrice;
    /**
     * 商品skuId
     */
    private String platformSkuId;
    /**
     * 属性值拼接
     * 颜色：红色 尺码：L
     * 红色_L
     */
    private String skuPropertiesName;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 短标
     */
    private String titleShort;
    /**
     * 商品编码(货号)
     */
    private String xcode;
    /**
     * 商品条码(upc编码)
     */
    private String barcode;
    /**
     * 长
     */
    private BigDecimal length;
    /**
     * 高度 单位:毫米,含包装 必须是正整数
     */
    private BigDecimal height;
    /**
     * 单位:千克,含包装
     */
    private BigDecimal weight;
    /**
     * 宽度 单位:毫米
     */
    private BigDecimal width;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 初始库存
     */
    private BigDecimal qty;
    /**
     * 存储sku商品级别的属性
     * 如销售属性
     */
    private List<ProductPropItem> propItems;

    /**
     * sku商品图片信息
     */
    private List<ProductPicInfo> picInfos;

    /**
     * 扩展字段
     */
    private String attributes;

    public String getSkuPropertiesName() {
        return skuPropertiesName;
    }

    public void setSkuPropertiesName(String skuPropertiesName) {
        this.skuPropertiesName = skuPropertiesName;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public List<ProductPropItem> getPropItems() {
        return propItems;
    }

    public void setPropItems(List<ProductPropItem> propItems) {
        this.propItems = propItems;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getTaxedPrice() {
        return taxedPrice;
    }

    public void setTaxedPrice(BigDecimal taxedPrice) {
        this.taxedPrice = taxedPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getDisedTaxedPrice() {
        if (null == disedTaxedPrice) {
            disedTaxedPrice = BigDecimal.ZERO;
        }
        return disedTaxedPrice;
    }

    public void setDisedTaxedPrice(BigDecimal disedTaxedPrice) {
        this.disedTaxedPrice = disedTaxedPrice;
    }

    public BigDecimal getInitialPrice() {
        return initialPrice;
    }

    public void setInitialPrice(BigDecimal initialPrice) {
        this.initialPrice = initialPrice;
    }

    public BigDecimal getSupplyPrice() {
        return supplyPrice;
    }

    public void setSupplyPrice(BigDecimal supplyPrice) {
        this.supplyPrice = supplyPrice;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleShort() {
        return titleShort;
    }

    public void setTitleShort(String titleShort) {
        this.titleShort = titleShort;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getQty() {
        if (null == qty) {
            qty = BigDecimal.ZERO;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public List<ProductPicInfo> getPicInfos() {
        return picInfos;
    }

    public void setPicInfos(List<ProductPicInfo> picInfos) {
        this.picInfos = picInfos;
    }
}
