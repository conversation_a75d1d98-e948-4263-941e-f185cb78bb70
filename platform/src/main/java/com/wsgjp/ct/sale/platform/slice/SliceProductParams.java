package com.wsgjp.ct.sale.platform.slice;

import com.wsgjp.ct.sale.platform.enums.SliceSequence;
import com.wsgjp.ct.sale.platform.enums.SliceType;

/**
 * <AUTHOR>
 */
public class SliceProductParams {
    /**
     * 使用最大页数必须传值(默认升序)
     */
    private SliceSequence sliceSequence = SliceSequence.ASC;

    /**
     * 针对只有创建时间或者只有更新时间的平台(不传值默认全量取创建时间增量取更新时间)
     */
    private Boolean onlyIncrement;
    private int maxPage;
    private int pageSize;
    private int pageStartIndex;
    /**
     * 下载最大时间间隔限制 单位:秒
     */
    private int downloadInterval;

    private Integer businessType;
    private SliceType type;
    private int status;
    /**
     * 只针对全量下载自己配置开始时间，结束时间为当前时间(如果是全量刷新网店商品有最大页数必须传) (默认为半年)
     * 默认的180是从当前时间往前推半年
     */
    private Integer startDay;

    public Boolean getOnlyIncrement() {
        return onlyIncrement;
    }

    public void setOnlyIncrement(Boolean onlyIncrement) {
        this.onlyIncrement = onlyIncrement;
    }

    public SliceSequence getSliceSequence() {
        return sliceSequence;
    }

    public void setSliceSequence(SliceSequence sliceSequence) {
        this.sliceSequence = sliceSequence;
    }

    public Integer getStartDay() {
        return startDay;
    }

    public void setStartDay(Integer startDay) {
        this.startDay = startDay;
    }

    /**
     * 平台自定义扩展
     */
    private String extendQueryInfo;

    public int getMaxPage() {
        return maxPage;
    }

    public void setMaxPage(int maxPage) {
        this.maxPage = maxPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageStartIndex() {
        return pageStartIndex;
    }

    public void setPageStartIndex(int pageStartIndex) {
        this.pageStartIndex = pageStartIndex;
    }

    public int getDownloadInterval() {
        return downloadInterval;
    }

    public void setDownloadInterval(int downloadInterval) {
        this.downloadInterval = downloadInterval;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public SliceType getType() {
        return type;
    }

    public void setType(SliceType type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getExtendQueryInfo() {
        return extendQueryInfo;
    }

    public void setExtendQueryInfo(String extendQueryInfo) {
        this.extendQueryInfo = extendQueryInfo;
    }


}
