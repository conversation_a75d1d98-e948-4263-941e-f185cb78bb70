package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum SendCheckCodeEnum implements CodeEnum {
    SUCCESS(0, "成功"),
    TADES_STATUS_CLOSED(1,"订单交易关闭"),
    TRADE_HAS_REFUND(2,"订单存在退款"),
    FREIGHT_UNMAPPING(3,"物流未映射"),
    DETAIL_CHANGED(5,"发货明细跟原始订单明细不一致，线上商品可能存在变更情况"),
    OTHER(4,"其他原因");

    SendCheckCodeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private final int code;
    private final String name;


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return name;
    }
}
