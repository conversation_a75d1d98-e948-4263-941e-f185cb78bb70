package com.wsgjp.ct.sale.platform.dto.sendgoods;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * 配送完成发货单确认明细实体
 * <AUTHOR>
 */
public class DeliveryEndBillNoDetailConfirmEntity {
    private String tradeId;
    private String oid;
    private String platformPtypeId;
    private String platformSkuId;
    private String platformPtypeXcode;
    /**
     * 商品名称 药帮忙必传
     */
    private String platformPtypeName;
    private BigDecimal qty;
    private BigDecimal deliveredQty;
    private BigDecimal totalQty;
    private BigInteger deliverOrderId;
    private BigInteger deliverOrderDetailId;
    private BigInteger warehouseTaskId;
    private BigInteger warehouseTaskDetailId;
    private String platformJson;
    private boolean gift;
    private boolean manual;
    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getPlatformPtypeId() {
        return platformPtypeId;
    }

    public void setPlatformPtypeId(String platformPtypeId) {
        this.platformPtypeId = platformPtypeId;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getPlatformPtypeXcode() {
        return platformPtypeXcode;
    }

    public void setPlatformPtypeXcode(String platformPtypeXcode) {
        this.platformPtypeXcode = platformPtypeXcode;
    }

    public String getPlatformPtypeName() {
        return platformPtypeName;
    }

    public void setPlatformPtypeName(String platformPtypeName) {
        this.platformPtypeName = platformPtypeName;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getDeliveredQty() {
        return deliveredQty;
    }

    public void setDeliveredQty(BigDecimal deliveredQty) {
        this.deliveredQty = deliveredQty;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigInteger getDeliverOrderId() {
        return deliverOrderId;
    }

    public void setDeliverOrderId(BigInteger deliverOrderId) {
        this.deliverOrderId = deliverOrderId;
    }

    public BigInteger getDeliverOrderDetailId() {
        return deliverOrderDetailId;
    }

    public void setDeliverOrderDetailId(BigInteger deliverOrderDetailId) {
        this.deliverOrderDetailId = deliverOrderDetailId;
    }

    public BigInteger getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(BigInteger warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }

    public BigInteger getWarehouseTaskDetailId() {
        return warehouseTaskDetailId;
    }

    public void setWarehouseTaskDetailId(BigInteger warehouseTaskDetailId) {
        this.warehouseTaskDetailId = warehouseTaskDetailId;
    }

    public String getPlatformJson() {
        return platformJson;
    }

    public void setPlatformJson(String platformJson) {
        this.platformJson = platformJson;
    }

    public boolean isGift() {
        return gift;
    }

    public void setGift(boolean gift) {
        this.gift = gift;
    }

    public boolean isManual() {
        return manual;
    }

    public void setManual(boolean manual) {
        this.manual = manual;
    }
}
