package com.wsgjp.ct.sale.platform.dto.sendgoods;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillCreateType;
import com.wsgjp.ct.bill.core.handle.entity.enums.SelfDeliveryModeEnum;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecordDetail;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.AddressInfo;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.PlatformOperator;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.SyncFreightInfo;
import com.wsgjp.ct.sale.platform.enums.SyncType;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FreightBillNoSyncEntity {
    private String tradeId;
    /**
     * 合并发货所有的订单号
     */
    private List<String> mergeTradeIds;
    private String refundId;
    private String storeCode;
    private TradeStatus tradeStatus;
    private Date payTime;
    private SyncType syncType;
    private AddressInfo sendAddress;
    private AddressInfo returnAddress;
    private List<SyncFreightInfo> freightInfoList;
    private List<SyncFreightInfo> lastFreightInfoList;
    /**
     * 针对平台特殊业务有多个物流公司
     */
    private List<SyncFreightInfo> freightInfo2List;
    private boolean last;
    private boolean first;
    private boolean spilt;
    private boolean merge;
    // 查询的原单明细的交易状态和售后状态赋值
    private boolean existRefund;
    private String remark;
    private String platformJson;
    private SelfDeliveryModeEnum deliveryMode;
    private List<MarkData> deliverMarks;

    /**
     * 只存交易单和任务单传过来的标记
     */
    private List<MarkData> deliverMarks2;
    /**
     * 线上发货地址ID
     */
    private String sendAddressId;
    /**
     * 线上发货仓库ID
     */
    private String sendWarehouseId;
    private List<FreightBillNoSyncDetailEntity> details;
    private boolean allowManual;
    private String platformStockId;
    private String customerReceiverProvince;
    private String customerReceiverCity;
    private String customerReceiverDistrict;
    private String customerReceiverTown;
    /**
     * 未提交到发货流程的明细数量
     */
    private int noSubmitCount;
    /**
     * 此订单同步成功的所有记录
     */
    private List<EshopFreightSyncRecordDetail> otherFreightInfoList;
    /**
     * 订单已发货成功发货物流包裹数量
     * 例如：京东拆分发货最多拆分10个包裹，第10个包裹需要将剩余所有商品明细数量发完
     */
    private int deliveredPackageCount;

    private PlatformOperator platformOperator;

    private String shipOrderNo;

    private BigInteger deliverOrderId;

    private String storageNo;

    private String loginUsername;

    //强制走整单发货
    private Boolean forceWholeSendEnabled;

    //拆分发货：当前订单发货：是否部分商品发货，如果当前订单已经全部发货完成需要赋值false;
    private boolean partialSend;

    private BillCreateType billCreateType;

    private String di;

    public List<MarkData> getDeliverMarks2() {
        return deliverMarks2;
    }

    public void setDeliverMarks2(List<MarkData> deliverMarks2) {
        this.deliverMarks2 = deliverMarks2;
    }

    public boolean isExistRefund() {
        return existRefund;
    }

    public void setExistRefund(boolean existRefund) {
        this.existRefund = existRefund;
    }
    public List<SyncFreightInfo> getFreightInfo2List() {
        return freightInfo2List;
    }

    public void setFreightInfo2List(List<SyncFreightInfo> freightInfo2List) {
        this.freightInfo2List = freightInfo2List;
    }

    public String getShipOrderNo() {
        return shipOrderNo;
    }

    public void setShipOrderNo(String shipOrderNo) {
        this.shipOrderNo = shipOrderNo;
    }

    public BigInteger getDeliverOrderId() {
        return deliverOrderId;
    }

    public void setDeliverOrderId(BigInteger deliverOrderId) {
        this.deliverOrderId = deliverOrderId;
    }

    public String getSendAddressId() {

        return sendAddressId;
    }

    public void setSendAddressId(String sendAddressId) {
        this.sendAddressId = sendAddressId;
    }

    public String getCustomerReceiverProvince() {
        return customerReceiverProvince;
    }

    public void setCustomerReceiverProvince(String customerReceiverProvince) {
        this.customerReceiverProvince = customerReceiverProvince;
    }

    public String getCustomerReceiverCity() {
        return customerReceiverCity;
    }

    public void setCustomerReceiverCity(String customerReceiverCity) {
        this.customerReceiverCity = customerReceiverCity;
    }

    public String getCustomerReceiverDistrict() {
        return customerReceiverDistrict;
    }

    public void setCustomerReceiverDistrict(String customerReceiverDistrict) {
        this.customerReceiverDistrict = customerReceiverDistrict;
    }

    public String getPlatformStockId() {
        return platformStockId;
    }

    public void setPlatformStockId(String platformStockId) {
        this.platformStockId = platformStockId;
    }

    public String getDi() {
        return di;
    }

    public void setDi(String di) {
        this.di = di;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public SyncType getSyncType() {
        return syncType;
    }

    public void setSyncType(SyncType syncType) {
        this.syncType = syncType;
    }

    public List<SyncFreightInfo> getFreightInfoList() {
        return freightInfoList;
    }

    public void setFreightInfoList(List<SyncFreightInfo> freightInfoList) {
        this.freightInfoList = freightInfoList;
    }

    public boolean isSpilt() {
        return spilt;
    }

    public void setSpilt(boolean spilt) {
        this.spilt = spilt;
    }

    public boolean isMerge() {
        return merge;
    }

    public void setMerge(boolean merge) {
        this.merge = merge;
    }

    public SelfDeliveryModeEnum getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(SelfDeliveryModeEnum deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public List<MarkData> getDeliverMarks() {
        if (CollectionUtils.isEmpty(deliverMarks)) {
            deliverMarks = new ArrayList<>();
        }
        return deliverMarks;
    }

    public void setDeliverMarks(List<MarkData> deliverMarks) {
        this.deliverMarks = deliverMarks;
    }

    public List<FreightBillNoSyncDetailEntity> getDetails() {
        if (CollectionUtils.isEmpty(details)) {
            details = new ArrayList<>();
        }
        return details;
    }

    public void setDetails(List<FreightBillNoSyncDetailEntity> details) {
        this.details = details;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<SyncFreightInfo> getLastFreightInfoList() {
        return lastFreightInfoList;
    }

    public void setLastFreightInfoList(List<SyncFreightInfo> lastFreightInfoList) {
        this.lastFreightInfoList = lastFreightInfoList;
    }

    public String getPlatformJson() {
        return platformJson;
    }

    public void setPlatformJson(String platformJson) {
        this.platformJson = platformJson;
    }

    public boolean isFirst() {
        return first;
    }

    public void setFirst(boolean first) {
        this.first = first;
    }

    public boolean isLast() {
        return last;
    }

    public void setLast(boolean last) {
        this.last = last;
    }

    public AddressInfo getSendAddress() {
        return sendAddress;
    }

    public void setSendAddress(AddressInfo sendAddress) {
        this.sendAddress = sendAddress;
    }

    public AddressInfo getReturnAddress() {
        if (null == returnAddress) {
            returnAddress = new AddressInfo();
        }
        return returnAddress;
    }

    public void setReturnAddress(AddressInfo returnAddress) {
        this.returnAddress = returnAddress;
    }

    public boolean isAllowManual() {
        return allowManual;
    }

    public void setAllowManual(boolean allowManual) {
        this.allowManual = allowManual;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public List<String> getMergeTradeIds() {
        return mergeTradeIds;
    }

    public void setMergeTradeIds(List<String> mergeTradeIds) {
        this.mergeTradeIds = mergeTradeIds;
    }

    public List<EshopFreightSyncRecordDetail> getOtherFreightInfoList() {
        return otherFreightInfoList;
    }

    public void setOtherFreightInfoList(List<EshopFreightSyncRecordDetail> otherFreightInfoList) {
        this.otherFreightInfoList = otherFreightInfoList;
    }

    public String getFreightInfoString() {
        if (CollectionUtils.isEmpty(getFreightInfoList())) {
            return "";
        }
        getFreightInfoList().sort(Comparator.comparing(SyncFreightInfo::getFreightBillNo));
        return JsonUtils.toJson(getFreightInfoList());
    }

    public String getCustomerReceiverTown() {
        if (customerReceiverTown == null){
            return "";
        }
        if (StringUtils.isEmpty(customerReceiverTown)){
            return "";
        }
        return customerReceiverTown;
    }

    public void setCustomerReceiverTown(String customerReceiverTown) {
        this.customerReceiverTown = customerReceiverTown;
    }

    public PlatformOperator getPlatformOperator() {
        return platformOperator;
    }

    public void setPlatformOperator(PlatformOperator platformOperator) {
        this.platformOperator = platformOperator;
    }

    public int getNoSubmitCount() {
        return noSubmitCount;
    }

    public void setNoSubmitCount(int noSubmitCount) {
        this.noSubmitCount = noSubmitCount;
    }

    public String getSendWarehouseId() {
        return sendWarehouseId;
    }

    public void setSendWarehouseId(String sendWarehouseId) {
        this.sendWarehouseId = sendWarehouseId;
    }

    public int getDeliveredPackageCount() {
        return deliveredPackageCount;
    }

    public void setDeliveredPackageCount(int deliveredPackageCount) {
        this.deliveredPackageCount = deliveredPackageCount;
    }

    public String getStorageNo() {
        return storageNo;
    }

    public void setStorageNo(String storageNo) {
        this.storageNo = storageNo;
    }

    public String getLoginUsername() {
        if (null == loginUsername) {
            loginUsername = "";
        }
        return loginUsername;
    }

    public void setLoginUsername(String loginUsername) {
        this.loginUsername = loginUsername;
    }

    public Boolean getForceWholeSendEnabled() {
        if (forceWholeSendEnabled == null) {
            forceWholeSendEnabled = false;
        }
        return forceWholeSendEnabled;
    }

    public void setForceWholeSendEnabled(Boolean forceWholeSendEnabled) {
        this.forceWholeSendEnabled = forceWholeSendEnabled;
    }

    public boolean isPartialSend() {
        return partialSend;
    }

    public void setPartialSend(boolean partialSend) {
        this.partialSend = partialSend;
    }

    public BillCreateType getBillCreateType() {
        return billCreateType;
    }

    public void setBillCreateType(BillCreateType billCreateType) {
        this.billCreateType = billCreateType;
    }
}
