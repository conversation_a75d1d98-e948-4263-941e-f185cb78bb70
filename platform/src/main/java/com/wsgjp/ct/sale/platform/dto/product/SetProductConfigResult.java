package com.wsgjp.ct.sale.platform.dto.product;

/**
 * <AUTHOR>
 */
public class SetProductConfigResult {
    /**
     * 设置时效天数失败原因
     */
    private String configPromiseFailReason;
    /**
     * 打厂直标失败原因
     */
    private String czFlagFailReason;
    /**
     * 设置库存失败原因
     */
    private String configStockFailReason;
    /**
     * 错误原因
     */
    private String errMsg;
    /**
     * 设置库存结果
     */
    private boolean configStockResult;
    /**
     * 打厂直标结果
     */
    private boolean czFlagResult;
    /**
     * 设置时效天数结果
     */
    private boolean configPromiseResult;
    /**
     * 京东skuId
     */
    private String skuId;

    public String getConfigPromiseFailReason() {
        return configPromiseFailReason;
    }

    public void setConfigPromiseFailReason(String configPromiseFailReason) {
        this.configPromiseFailReason = configPromiseFailReason;
    }

    public String getCzFlagFailReason() {
        return czFlagFailReason;
    }

    public void setCzFlagFailReason(String czFlagFailReason) {
        this.czFlagFailReason = czFlagFailReason;
    }

    public String getConfigStockFailReason() {
        return configStockFailReason;
    }

    public void setConfigStockFailReason(String configStockFailReason) {
        this.configStockFailReason = configStockFailReason;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public boolean isConfigStockResult() {
        return configStockResult;
    }

    public void setConfigStockResult(boolean configStockResult) {
        this.configStockResult = configStockResult;
    }

    public boolean isCzFlagResult() {
        return czFlagResult;
    }

    public void setCzFlagResult(boolean czFlagResult) {
        this.czFlagResult = czFlagResult;
    }

    public boolean isConfigPromiseResult() {
        return configPromiseResult;
    }

    public void setConfigPromiseResult(boolean configPromiseResult) {
        this.configPromiseResult = configPromiseResult;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }
}
