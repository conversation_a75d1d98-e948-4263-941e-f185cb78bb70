package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 9/4/2020 下午 1:38
 */
public enum SupportMappingType implements CodeEnum {
    NORMAL(0, "手工绑定"),
    XCODE(1, "按商家编码对应");

    private final int index;
    private final String name;

    SupportMappingType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }
}
