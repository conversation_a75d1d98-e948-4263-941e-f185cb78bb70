package com.wsgjp.ct.sale.platform.dto.token;


import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.MallType;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("授权信息")
public class EshopAuthInfo {

    public EshopAuthInfo() {
    }

    public EshopAuthInfo(EshopSystemParams systemParams) {
        this.eshopId = systemParams.geteShopId();
        this.profileId = systemParams.getProfileId();
    }

    @ApiModelProperty("店铺id")
    private BigInteger eshopId;
    @ApiModelProperty("账套id")
    private BigInteger profileId;
    @ApiModelProperty("令牌")
    private String token = "";
    @ApiModelProperty("刷新令牌")
    private String refreshToken = "";
    @ApiModelProperty("刷新令牌过期时间")
    private Date reExpiresIn;
    @ApiModelProperty("令牌过期时间")
    private Date expiresIn;
    /**
     * 此字段授权时有token和授权过期时间的情况下此字段必须要赋值。
     */
    @ApiModelProperty("长令牌")
    private Date r1ExpireIn;
    @ApiModelProperty("应用标识")
    private String appKey = "";
    @ApiModelProperty("应用密钥")
    private String appSecret = "";
    @ApiModelProperty("线上店铺id")
    private String onlineShopId;
    @ApiModelProperty("是否有令牌过期时间")
    private boolean hasTokenExpired = false;
    @ApiModelProperty("线上店铺账户")
    private String onlineShopAccount = "";
    @ApiModelProperty("是否开启Tmc")
    private boolean tmcEnabled = false;
    @ApiModelProperty("是否开启Rds")
    private boolean rdsEnabled = false;
    @ApiModelProperty("Rds订阅时间")
    private Date rdsApplyTime;
    @ApiModelProperty("Rds订阅检查时间")
    private Date rdsCheckTime;
    @ApiModelProperty("Rds可以使用时间")
    private Date rdsReadyTime;
    @ApiModelProperty(hidden = true)
    private String errorMessage;
    @ApiModelProperty(hidden = true)
    /**
     * 是否已授权
     */
    private int isAuth = 0;
    private int expireNotice = 0;

    /**
     * 是否共享授权（不同店铺类型使用相同的授权场景。比如：拼多多和拼多多厂家，快团团和快团团供应商）
     */
    @ApiModelProperty(hidden = true)
    private boolean shareToken;

    /**
     * 共享授权的店铺类型范围
     */
    @ApiModelProperty(hidden = true)
    private List<ShopType> shareShopTypes;

    /**
     * 子账号id,非必填
     */
    private String subUserId;

    /**
     * 子账号昵称,非必填
     */
    private String subUserAccount;

    private MallType mallType;
    //AuthCheckResult
    private Integer authCheckResult;
    //是否是连锁总店
    private boolean isMainShop;

    public boolean isMainShop() {
        return isMainShop;
    }

    public void setMainShop(boolean mainShop) {
        isMainShop = mainShop;
    }

    public Integer getAuthCheckResult() {
        return authCheckResult;
    }

    public void setAuthCheckResult(Integer authCheckResult) {
        this.authCheckResult = authCheckResult;
    }

    public String getSubUserId() {
        return subUserId;
    }

    public void setSubUserId(String subUserId) {
        this.subUserId = subUserId;
    }

    public String getSubUserAccount() {
        return subUserAccount;
    }

    public void setSubUserAccount(String subUserAccount) {
        this.subUserAccount = subUserAccount;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getReExpiresIn() {
        return reExpiresIn;
    }

    public void setReExpiresIn(Date reExpiresIn) {
        this.reExpiresIn = reExpiresIn;
    }

    public Date getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Date expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getOnlineShopId() {
        return onlineShopId;
    }

    public void setOnlineShopId(String onlineShopId) {
        this.onlineShopId = onlineShopId;
    }

    public Date getR1ExpireIn() {
        return r1ExpireIn;
    }

    public void setR1ExpireIn(Date r1ExpireIn) {
        this.r1ExpireIn = r1ExpireIn;
    }

    public boolean isHasTokenExpired() {
        return hasTokenExpired;
    }

    public void setHasTokenExpired(boolean hasTokenExpired) {
        this.hasTokenExpired = hasTokenExpired;
    }

    public String getOnlineShopAccount() {
        return onlineShopAccount;
    }

    public void setOnlineShopAccount(String onlineShopAccount) {
        this.onlineShopAccount = onlineShopAccount;
    }

    public boolean isTmcEnabled() {
        return tmcEnabled;
    }

    public void setTmcEnabled(boolean tmcEnabled) {
        this.tmcEnabled = tmcEnabled;
    }

    public boolean isRdsEnabled() {
        return rdsEnabled;
    }

    public void setRdsEnabled(boolean rdsEnabled) {
        this.rdsEnabled = rdsEnabled;
    }

    public Date getRdsApplyTime() {
        return rdsApplyTime;
    }

    public void setRdsApplyTime(Date rdsApplyTime) {
        this.rdsApplyTime = rdsApplyTime;
    }

    public int getIsAuth() {
        return isAuth;
    }

    public void setIsAuth(int isAuth) {
        this.isAuth = isAuth;
    }

    public boolean isShareToken() {
        return shareToken;
    }

    public void setShareToken(boolean shareToken) {
        this.shareToken = shareToken;
    }

    public List<ShopType> getShareShopTypes() {
        return shareShopTypes;
    }

    public void setShareShopTypes(List<ShopType> shareShopTypes) {
        this.shareShopTypes = shareShopTypes;
    }

    public Date getRdsCheckTime() {
        return rdsCheckTime;
    }

    public void setRdsCheckTime(Date rdsCheckTime) {
        this.rdsCheckTime = rdsCheckTime;
    }

    public Date getRdsReadyTime() {
        return rdsReadyTime;
    }

    public void setRdsReadyTime(Date rdsReadyTime) {
        this.rdsReadyTime = rdsReadyTime;
    }

    public MallType getMallType() {
        return mallType;
    }

    public void setMallType(MallType mallType) {
        this.mallType = mallType;
    }
}
