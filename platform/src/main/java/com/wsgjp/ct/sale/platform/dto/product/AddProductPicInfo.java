package com.wsgjp.ct.sale.platform.dto.product;


import java.util.List;

/**
 * <AUTHOR>
 */
public class AddProductPicInfo {
    /**
     * 平台商品ID
     */
    private String platformNumId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 图片信息
     */
    private List<ProductPicInfo> picInfos;
    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public List<ProductPicInfo> getPicInfos() {
        return picInfos;
    }

    public void setPicInfos(List<ProductPicInfo> picInfos) {
        this.picInfos = picInfos;
    }
}
