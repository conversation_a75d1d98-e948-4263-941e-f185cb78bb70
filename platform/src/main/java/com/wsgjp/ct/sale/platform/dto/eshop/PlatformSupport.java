package com.wsgjp.ct.sale.platform.dto.eshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 10/9/2020 下午 5:24
 */
@ApiModel("平台信息")
public class PlatformSupport {
    /**
     * 平台类型名称(例如：有赞)
     */
    @ApiModelProperty("平台名")
    private String name;
    /**
     * 平台类型编码
     */
    @ApiModelProperty("平台编号")
    private int platformType;

    public PlatformSupport() {

    }

    public PlatformSupport(String name, int platformType) {
        this.name = name;
        this.platformType = platformType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPlatformType() {
        return platformType;
    }

    public void setPlatformType(int platformType) {
        this.platformType = platformType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, platformType);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PlatformSupport that = (PlatformSupport) o;
        return platformType == that.platformType && Objects.equals(name, that.name);
    }

    @Override
    public String toString() {
        return "PlatformSupport{" +
                "name='" + name + '\'' +
                ", platformType=" + platformType +
                '}';
    }
}
