package com.wsgjp.ct.sale.platform.dto.btype;

import ngp.utils.StringUtils;

public class BtypeChangeNotifyResult {
    public BtypeChangeNotifyResult(BtypeChangeNotifyParam notify, String errMsg) {
        this.notify = notify;
        this.errorMsg = errMsg;
    }

    /**
     * 往来单位变更通知信息
     */
    private BtypeChangeNotifyParam notify;
    /**
     * 通知失败时的错误信息；
     */
    private String errorMsg;

    private boolean success;

    public BtypeChangeNotifyParam getNotify() {
        return notify;
    }

    public void setNotify(BtypeChangeNotifyParam notify) {
        this.notify = notify;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public boolean isSuccess() {
        if (!StringUtils.isEmpty(errorMsg)) {
            success = false;
        }
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
