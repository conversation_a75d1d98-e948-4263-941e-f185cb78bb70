package com.wsgjp.ct.sale.platform.dto.refund;


import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.enums.RefundTypeEnum;

import java.io.*;
import java.math.BigDecimal;
import java.util.Date;

public class EshopRefundDetailEntity implements Serializable {
    /**
     * 对应订单明细中的子订单号
     */
    private String oid;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 主商品id
     */
    private String numIid;
    /**
     * 商品skuId
     */
    private String skuId;
    /**
     * 主商品编码
     */
    private String outerId;
    /**
     * sku商品编码
     */
    private String outerSkuId;
    /**
     * 商品属性(必填)
     */
    private String properties;
    /**
     * 退货商品数量
     */
    private double qty;
    /**
     * 商品单价
     */
    private double price;
    /**
     * 售后明细总金额
     */
    private double totalFee;
    /**
     * 退款金额
     */
    private double refundFee;
    /**
     * 退换运费金额
     */
    private double refundFreightFee;
    /**
     * 买家支付金额
     */
    private String payment;
    /**
     * 商品退回时间
     */
    private Date goodReturnTime;
    /**
     * 售后状态
     */
    private RefundStatus refundState;
    /**
     * 构建售后单用
     * todo 删除
     */
    private BigDecimal restRefundFee;

    /**
     * 退款的服务费
     */
    private BigDecimal serviceFee;
    /**
     * 京东网店用
     */

    private String skuUuid;

    /**
     * 是否赠品
     */
    private boolean isGift;
    /**
     * 美团用，可不赋值
     * 售后类型
     */
    private RefundTypeEnum refundTypeEnum;

    private String TempTradeOrderId;

    // 退平台优惠金额
    private BigDecimal refundPlatformAmount;
    // 退国家补贴金额
    private BigDecimal refundNationalSubsidyTotal;

    public BigDecimal getRefundPlatformAmount() {
        if (refundPlatformAmount == null) {
            return BigDecimal.ZERO;
        }
        return refundPlatformAmount;
    }

    public void setRefundPlatformAmount(BigDecimal refundPlatformAmount) {
        this.refundPlatformAmount = refundPlatformAmount;
    }

    public BigDecimal getRefundNationalSubsidyTotal() {
        if (refundNationalSubsidyTotal == null) {
            return BigDecimal.ZERO;
        }
        return refundNationalSubsidyTotal;
    }

    public void setRefundNationalSubsidyTotal(BigDecimal refundNationalSubsidyTotal) {
        this.refundNationalSubsidyTotal = refundNationalSubsidyTotal;
    }


    public String getTempTradeOrderId() {
        return TempTradeOrderId;
    }

    public void setTempTradeOrderId(String tempTradeOrderId) {
        TempTradeOrderId = tempTradeOrderId;
    }

    public boolean isGift() {
        return isGift;
    }

    public void setGift(boolean gift) {
        isGift = gift;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getOid() {
        if (oid == null) {
            oid = "";
        }
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getNumIid() {
        if (numIid == null) {
            numIid = "";
        }
        return numIid;
    }

    public void setNumIid(String numIid) {
        this.numIid = numIid;
    }

    public String getSkuId() {
        if (skuId == null) {
            skuId = "";
        }
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getOuterSkuId() {
        if (outerSkuId == null) {
            return "";
        }
        return outerSkuId;
    }

    public void setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public double getQty() {
        return qty;
    }

    public void setQty(double qty) {
        this.qty = qty;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public double getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(double totalFee) {
        this.totalFee = totalFee;
    }

    public double getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(double refundFee) {
        this.refundFee = refundFee;
    }

    public double getRefundFreightFee() {
        return refundFreightFee;
    }

    public void setRefundFreightFee(double refundFreightFee) {
        this.refundFreightFee = refundFreightFee;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public Date getGoodReturnTime() {
        return goodReturnTime;
    }

    public void setGoodReturnTime(Date goodReturnTime) {
        this.goodReturnTime = goodReturnTime;
    }

    public String getProperties() {
        if (properties == null) {
            return "";
        }
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public RefundStatus getRefundState() {
        return refundState;
    }

    public void setRefundState(RefundStatus refundState) {
        this.refundState = refundState;
    }

    public BigDecimal getRestRefundFee() {
        if (restRefundFee == null) {
            restRefundFee = BigDecimal.valueOf(refundFee).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return restRefundFee;
    }
    public void setRestRefundFee(BigDecimal restRefundFee) {
        this.restRefundFee = restRefundFee;
    }

    public BigDecimal getServiceFee() {
        if(serviceFee == null){
            serviceFee = BigDecimal.ZERO;
        }
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    public String getSkuUuid() {
        return skuUuid;
    }

    public void setSkuUuid(String skuUuid) {
        this.skuUuid = skuUuid;
    }

    public RefundTypeEnum getRefundTypeEnum() {
        return refundTypeEnum;
    }

    public void setRefundTypeEnum(RefundTypeEnum refundTypeEnum) {
        this.refundTypeEnum = refundTypeEnum;
    }

    public EshopRefundDetailEntity deepClone() {
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(this);

            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);
            return (EshopRefundDetailEntity) in.readObject();
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
            return null;
        }
    }

}
