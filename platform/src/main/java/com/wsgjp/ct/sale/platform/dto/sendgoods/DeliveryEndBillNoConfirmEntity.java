package com.wsgjp.ct.sale.platform.dto.sendgoods;

import com.wsgjp.ct.bill.core.handle.entity.enums.SelfDeliveryModeEnum;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.PlatformOperator;

import java.util.Date;
import java.util.List;

/**
 * 配送完成发货单确认实体
 * <AUTHOR>
 */
public class DeliveryEndBillNoConfirmEntity {
    private String tradeId;
    private String storeCode;
    private TradeStatus tradeStatus;
    private Date payTime;
    private boolean spilt;
    private String platformJson;
    private SelfDeliveryModeEnum deliveryMode;
    private List<MarkData> deliverMarks;
    private List<DeliveryEndBillNoDetailConfirmEntity> details;
    private boolean allowManual;
    private String platformStockId;
    private String customerReceiverProvince;
    private String customerReceiverCity;
    private String customerReceiverDistrict;
    private String customerReceiverTown;
    private PlatformOperator platformOperator;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public boolean isSpilt() {
        return spilt;
    }

    public void setSpilt(boolean spilt) {
        this.spilt = spilt;
    }

    public String getPlatformJson() {
        return platformJson;
    }

    public void setPlatformJson(String platformJson) {
        this.platformJson = platformJson;
    }

    public SelfDeliveryModeEnum getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(SelfDeliveryModeEnum deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public List<MarkData> getDeliverMarks() {
        return deliverMarks;
    }

    public void setDeliverMarks(List<MarkData> deliverMarks) {
        this.deliverMarks = deliverMarks;
    }

    public List<DeliveryEndBillNoDetailConfirmEntity> getDetails() {
        return details;
    }

    public void setDetails(List<DeliveryEndBillNoDetailConfirmEntity> details) {
        this.details = details;
    }

    public boolean isAllowManual() {
        return allowManual;
    }

    public void setAllowManual(boolean allowManual) {
        this.allowManual = allowManual;
    }

    public String getPlatformStockId() {
        return platformStockId;
    }

    public void setPlatformStockId(String platformStockId) {
        this.platformStockId = platformStockId;
    }

    public String getCustomerReceiverProvince() {
        return customerReceiverProvince;
    }

    public void setCustomerReceiverProvince(String customerReceiverProvince) {
        this.customerReceiverProvince = customerReceiverProvince;
    }

    public String getCustomerReceiverCity() {
        return customerReceiverCity;
    }

    public void setCustomerReceiverCity(String customerReceiverCity) {
        this.customerReceiverCity = customerReceiverCity;
    }

    public String getCustomerReceiverDistrict() {
        return customerReceiverDistrict;
    }

    public void setCustomerReceiverDistrict(String customerReceiverDistrict) {
        this.customerReceiverDistrict = customerReceiverDistrict;
    }

    public String getCustomerReceiverTown() {
        return customerReceiverTown;
    }

    public void setCustomerReceiverTown(String customerReceiverTown) {
        this.customerReceiverTown = customerReceiverTown;
    }

    public PlatformOperator getPlatformOperator() {
        return platformOperator;
    }

    public void setPlatformOperator(PlatformOperator platformOperator) {
        this.platformOperator = platformOperator;
    }
}
