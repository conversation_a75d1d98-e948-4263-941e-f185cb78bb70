package com.wsgjp.ct.sale.platform.dto.product;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class SkuUnitInfo {

    private BigInteger skuId;
    private String skuName;
    private BigInteger unitId;
    private String unitName;
    @ApiModelProperty("单位sku商家编码，药师帮必传")
    private String xcode;
    private BigDecimal distributionPrice;
    @ApiModelProperty("sku单位价格")
    private BigDecimal price;
    @ApiModelProperty("过期时间")
    private Date expireDate;
    @ApiModelProperty("生产日期")
    private Date produceDate;
    @ApiModelProperty("批次号")
    private String batchNo;


    @ApiModelProperty("生产厂家")
    private String factoryName;


    @ApiModelProperty("批准文号")
    private String approval;

    public String getFactoryName() {
        return factoryName;
    }

    public String getApproval() {
        return approval;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public void setApproval(String approval) {
        this.approval = approval;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(Date produceDate) {
        this.produceDate = produceDate;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * 单位条码
     */
    private String barCode;

    private String uid;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public BigInteger getUnitId() {
        return unitId;
    }

    public void setUnitId(BigInteger unitId) {
        this.unitId = unitId;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public BigDecimal getDistributionPrice() {
        return distributionPrice;
    }

    public void setDistributionPrice(BigDecimal distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    public BigDecimal getPrice() {
        if(null==price){
            price=BigDecimal.ZERO;
        }
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }


}
