package com.wsgjp.ct.sale.platform.dto.order;

import io.swagger.annotations.ApiModelProperty;

public class ModifyItemParam {

    @ApiModelProperty("商品数量")
    private int qty;

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品价格")
    private double price;

    @ApiModelProperty("0不是赠品 1是赠品")
    private int type;

    public int getQty() {
        return qty;
    }

    public void setQty(int qty) {
        this.qty = qty;
    }

    public String getCommodityCode() {
        return commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
