package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum EShopApiExceptionType implements CodeEnum
{

    Network (0x1000000,"网络错误"),
    NetworkNotReach ( Network.getCode() | 0x001 ,"网络不可达"),
    API (0x2000000,"API异常"),
    ApiCallLimit ( API.getCode() | 0x1000,"API超限"),
    ApiCallLimitAppKey (ApiCallLimit.getCode() | 0x001,"Appkey当日调用超限"),
    ApiCallLimitSession ( ApiCallLimit.getCode() | 0x002,"Session调用次数超限"),
    ApiCallLimitTokenSolete (ApiCallLimit.getCode() | 0x003,"token过期"),
    Application (0x4000000,"应用异常");

    private int index;

    private String name;

    EShopApiExceptionType(int index, String name){
        this.index=index;
        this.name=name;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}