package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2024/7/19 16:39
 */
public enum SendOrderFilterTypeEnum  implements CodeEnum {

    /**
     * 同步单号接口根据平台最新订单信息过滤明细的结果
     */
    SUCCESS(0, ""),
    NO_ORDER(1, "通过平台接口未能查询到该订单，请尝试重试一次！"),
    NO_ORDER_DETAIL(2, "通过平台接口发现该订单没有明细，请尝试重试一次！"),
    ORDER_IS_SEND(3, "订单已经是已发货状态，平台不支持已发货订单修改物流单号，请更新一下订单！"),
    FILTER_ALL(4, "所有发货明细的状态都是已发货，无需再次同步，本次同步被过滤！");


    SendOrderFilterTypeEnum(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    private final int flag;
    private final String name;


    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
