package com.wsgjp.ct.sale.platform.rds;

import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;

/**
 * <AUTHOR>
 */
public class RdsParams {
    private EshopSystemParams params;
    private PlatformBaseConfig platformBaseConfig;

    private String sellerCid;

    public String getSellerCid() {
        return sellerCid;
    }

    public void setSellerCid(String sellerCid) {
        this.sellerCid = sellerCid;
    }

    public EshopSystemParams getParams() {
        return params;
    }

    public void setParams(EshopSystemParams params) {
        this.params = params;
    }

    public PlatformBaseConfig getPlatformBaseConfig() {
        return platformBaseConfig;
    }

    public void setPlatformBaseConfig(PlatformBaseConfig platformBaseConfig) {
        this.platformBaseConfig = platformBaseConfig;
    }
}
