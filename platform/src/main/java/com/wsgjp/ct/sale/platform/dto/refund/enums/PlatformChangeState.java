package com.wsgjp.ct.sale.platform.dto.refund.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PlatformChangeState implements CodeEnum {

    ALL(-1,"全部"),
    NONE(0,"无需换货"),
    YES(1,"确认换货"),
    NO(2,"拒绝换货"),
    NO_BUT_REFUND_ONLY(2,"确认不换只退款");


    private final int flag;

    private final String name;

    PlatformChangeState(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
