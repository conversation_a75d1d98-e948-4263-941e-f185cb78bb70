package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2023/5/23 14:11
 */
public enum TagAttribute implements CodeEnum {
    /**
     * 店铺开关配置，暂时存SysData, key=  showOutOfStockCallback_{eshopId}
     */
    SHOW_STOCK_CALLBACK(0,"缺货回告");


    private final int code;

    private final String name;

    TagAttribute(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return name();
    }
}
