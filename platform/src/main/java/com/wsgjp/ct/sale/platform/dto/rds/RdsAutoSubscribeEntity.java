package com.wsgjp.ct.sale.platform.dto.rds;


/**
 * 自动订阅实体类
 */
public class RdsAutoSubscribeEntity {

    /**
     * 是否开启自动订阅
     */
    private Boolean autoSubscribeRdsEnabled;

    /**
     * 距上次订阅成功后多久开始自动订阅
     */
    private Long rdsCheckDuration;

    public Boolean getAutoSubscribeRdsEnabled() {
        return autoSubscribeRdsEnabled;
    }

    public void setAutoSubscribeRdsEnabled(Boolean autoSubscribeRdsEnabled) {
        this.autoSubscribeRdsEnabled = autoSubscribeRdsEnabled;
    }

    public Long getRdsCheckDuration() {
        return rdsCheckDuration;
    }

    public void setRdsCheckDuration(Long rdsCheckDuration) {
        this.rdsCheckDuration = rdsCheckDuration;
    }
}
