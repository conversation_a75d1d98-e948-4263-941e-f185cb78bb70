package com.wsgjp.ct.sale.platform.dto.product;


import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2019-12-09 14:31
 */
public class EshopProductErrorEntity extends ErpBaseInfo {
    private String platformNumId;
    /**
     * 同一条sku UnitId不一样，会有两条，如果后续改造，根据unitId来区分同一条sku失败的是那一条
     * 如果没有unitId代表整条失败
     */
    private BigInteger unitId;
    private String platformSkuId;
    private String batchId;
    private String productName;
    //货号
    private String produtCode;
    private String platformXcode;
    private String errorMessage;

    public String getProdutCode() {
        return produtCode;
    }

    public void setProdutCode(String produtCode) {
        this.produtCode = produtCode;
    }

    public BigInteger getUnitId() {
        return unitId;
    }

    public void setUnitId(BigInteger unitId) {
        this.unitId = unitId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPlatformXcode() {
        return platformXcode;
    }

    public void setPlatformXcode(String platformXcode) {
        this.platformXcode = platformXcode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }
}
