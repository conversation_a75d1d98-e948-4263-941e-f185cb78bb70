package com.wsgjp.ct.sale.platform.dto.product;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

public class SkuPublishEntity {

    @ApiModelProperty("skuid")
    private String skuId;
    @ApiModelProperty("属性照片地址")
    private String picUrl;
    @ApiModelProperty("属性照片名字")
    private String picName;
    @ApiModelProperty("属性商家编码")
    private String xcode;
    @ApiModelProperty("价格")
    private BigDecimal price;
    @ApiModelProperty("单位id")
    private String unitId;
    @ApiModelProperty("属性名id")
    private String propId1;
    private String propId2;
    private String propId3;
    private String propId4;
    @ApiModelProperty("属性名")
    private String propName1;
    private String propName2;
    private String propName3;
    private String propName4;
    @ApiModelProperty("属性值id")
    private String propvalueId1;
    private String propvalueId2;
    private String propvalueId3;
    private String propvalueId4;
    @ApiModelProperty("属性值")
    private String propvalueName1;
    private String propvalueName2;
    private String propvalueName3;
    private String propvalueName4;

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getPicName() {
        return picName;
    }

    public void setPicName(String picName) {
        this.picName = picName;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getPropId1() {
        return propId1;
    }

    public void setPropId1(String propId1) {
        this.propId1 = propId1;
    }

    public String getPropId2() {
        return propId2;
    }

    public void setPropId2(String propId2) {
        this.propId2 = propId2;
    }

    public String getPropId3() {
        return propId3;
    }

    public void setPropId3(String propId3) {
        this.propId3 = propId3;
    }

    public String getPropId4() {
        return propId4;
    }

    public void setPropId4(String propId4) {
        this.propId4 = propId4;
    }

    public String getPropName1() {
        return propName1;
    }

    public void setPropName1(String propName1) {
        this.propName1 = propName1;
    }

    public String getPropName2() {
        return propName2;
    }

    public void setPropName2(String propName2) {
        this.propName2 = propName2;
    }

    public String getPropName3() {
        return propName3;
    }

    public void setPropName3(String propName3) {
        this.propName3 = propName3;
    }

    public String getPropName4() {
        return propName4;
    }

    public void setPropName4(String propName4) {
        this.propName4 = propName4;
    }

    public String getPropvalueId1() {
        return propvalueId1;
    }

    public void setPropvalueId1(String propvalueId1) {
        this.propvalueId1 = propvalueId1;
    }

    public String getPropvalueId2() {
        return propvalueId2;
    }

    public void setPropvalueId2(String propvalueId2) {
        this.propvalueId2 = propvalueId2;
    }

    public String getPropvalueId3() {
        return propvalueId3;
    }

    public void setPropvalueId3(String propvalueId3) {
        this.propvalueId3 = propvalueId3;
    }

    public String getPropvalueId4() {
        return propvalueId4;
    }

    public void setPropvalueId4(String propvalueId4) {
        this.propvalueId4 = propvalueId4;
    }

    public String getPropvalueName1() {
        return propvalueName1;
    }

    public void setPropvalueName1(String propvalueName1) {
        this.propvalueName1 = propvalueName1;
    }

    public String getPropvalueName2() {
        return propvalueName2;
    }

    public void setPropvalueName2(String propvalueName2) {
        this.propvalueName2 = propvalueName2;
    }

    public String getPropvalueName3() {
        return propvalueName3;
    }

    public void setPropvalueName3(String propvalueName3) {
        this.propvalueName3 = propvalueName3;
    }

    public String getPropvalueName4() {
        return propvalueName4;
    }

    public void setPropvalueName4(String propvalueName4) {
        this.propvalueName4 = propvalueName4;
    }
}
