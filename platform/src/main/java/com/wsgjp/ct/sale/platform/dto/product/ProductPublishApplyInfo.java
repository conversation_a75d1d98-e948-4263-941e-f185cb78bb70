package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.sale.platform.enums.ProductAuditState;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class ProductPublishApplyInfo {
    /**
     * 申请ID
     */
    private String applyId;
    /**
     * 创建者
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 修改者
     */
    private String modifiedBy;
    private String platformNumId;
    /**
     * 修改时间
     */
    private Date modifiedTime;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 审核状态
     */
    private ProductAuditState auditState;
    /**
     * 归档状态 1：归档
     */
    private Integer archiveStatus;
    /**
     * 新老品状态 0：新品 1：老品
     */
    private Integer productType;
    /**
     * 审核人员编码
     */
    private String approverCode;
    /**
     * 审核人员名称
     */
    private String approverName;
    /**
     * 审核建议
     */
    private String opinion;

    /**
     * 审核时间
     */
    private Date approveTime;

    private PublishProductInfo productInfo;

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public String getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public ProductAuditState getAuditState() {
        return auditState;
    }

    public void setAuditState(ProductAuditState auditState) {
        this.auditState = auditState;
    }

    public Integer getArchiveStatus() {
        return archiveStatus;
    }

    public void setArchiveStatus(Integer archiveStatus) {
        this.archiveStatus = archiveStatus;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getApproverCode() {
        return approverCode;
    }

    public void setApproverCode(String approverCode) {
        this.approverCode = approverCode;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public PublishProductInfo getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(PublishProductInfo productInfo) {
        this.productInfo = productInfo;
    }
}
