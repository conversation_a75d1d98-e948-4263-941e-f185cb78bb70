package com.wsgjp.ct.sale.platform.slice;

import com.wsgjp.ct.sale.platform.enums.SliceType;

/**
 * <AUTHOR>
 */
public class SliceFinanceParams {
    private int maxPage;
    private int pageSize;
    private int pageStartIndex;
    /**
     * 下载最大时间间隔限制 单位:秒
     */
    private int downloadInterval;

    private Integer businessType;
    private SliceType type;
    private int status;

    public int getMaxPage() {
        return maxPage;
    }

    public void setMaxPage(int maxPage) {
        this.maxPage = maxPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageStartIndex() {
        return pageStartIndex;
    }

    public void setPageStartIndex(int pageStartIndex) {
        this.pageStartIndex = pageStartIndex;
    }

    public int getDownloadInterval() {
        return downloadInterval;
    }

    public void setDownloadInterval(int downloadInterval) {
        this.downloadInterval = downloadInterval;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public SliceType getType() {
        return type;
    }

    public void setType(SliceType type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
