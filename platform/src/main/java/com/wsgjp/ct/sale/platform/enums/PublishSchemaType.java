package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PublishSchemaType implements CodeEnum {
    MAIN(0,"主要schema结构"),
    PROPS(1,"额外属性");


    private String name;
    private int code;

    PublishSchemaType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
