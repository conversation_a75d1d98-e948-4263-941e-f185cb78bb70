package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 旗帜（备注标记）
 */
public enum SellerFlag implements CodeEnum {
    /**
     * 旗帜（备注标记）
     */
    WHITE(0, "", ""),
    RED(1, "#FF0000", "红色"),
    YELLOW(2, "#FFFF00", "黄色"),
    GREEN(3, "#339900", "绿色"),
    BLUE(4, "#0099CC", "蓝色"),
    VIOLET(5, "#8B3DFF", "紫色"),
    GRAY(6, "#DBDBDB", "灰色"),
    ORANGE(7, "#FFA500", "橙色"),
    CHING(8, "#00FFFF", "青色"),
    ROSERED(9, "#E4248E", "玫红"),
    TENDERGREEN(10, "#9BA217", "秋香绿"),
    VIOLETPURPLE(11, "#41B4FA", "紫罗兰");

    private int code;
    private String color;
    private String name;

    SellerFlag(int code, String color, String name) {
        this.code = code;
        this.name = name;
        this.color = color;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }


    public static SellerFlag findByIndex(int index) {
        for (SellerFlag flag : SellerFlag.values()) {
            if (index == flag.getCode()) {
                return flag;
            }
        }
        return null;
    }

    public String getColor() {
        return color;
    }

}
