package com.wsgjp.ct.sale.platform.dto.refund.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PlatformReturnState implements CodeEnum {

    ALL(-1,"全部"),
    NONE(0,"无需退货"),
    YES(1,"确认收货"),
    NO(2,"拒绝收货"),
    NO_BUT_REFUND_ONLY(2,"确认不退只退款");


    private final int flag;

    private final String name;

    PlatformReturnState(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
