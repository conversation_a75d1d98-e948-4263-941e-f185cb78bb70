package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum LogBizType implements CodeEnum {
    API_LOG(1, "API接口日志"),
    TMC_LOG(2, "tmc日志"),
    DOWNLOAD_BIZ_LOG(3, "下载相关业务日志"),
    INTERACTIVE_BIZ_LOG(4, "交互业务相关日志"),
    ;

    LogBizType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;

    @Override
    public int getCode() {
        return 0;
    }

    @Override
    public String getName() {
        return null;
    }
}