package com.wsgjp.ct.sale.platform.dto.member;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class VipCardTypeEntity {
    /**
     * 会员卡类型id
     */
    public Long id;
    /**
     * 类型名称
     */
    public String cardTypeName;
    /**
     * 折扣
     */
    public BigDecimal discount;
    /**
     * 创建时间
     */
    public Date createTime;
    /**
     * 修改
     */
    public Date modifyTime;
    /**
     * 备注
     */
    public String comment;
    /**
     * 公司账套id
     */
    public long profileId;
    /**
     * 允许使用积分
     */
    public boolean canIntegral;
    /**
     * 允许储值
     */
    public boolean canDeposit;
    /**
     * 允许使用规则
     */
    public boolean canUseRule;
    public boolean deleted;
    public boolean pushStatus;
    public String message;
}
