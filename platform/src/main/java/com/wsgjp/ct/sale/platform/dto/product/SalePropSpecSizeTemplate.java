package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

/**
 * 尺码需要编辑的元素信息，比如：身高，胸围，体重。。。。
 *
 * <AUTHOR>
 */
public class SalePropSpecSizeTemplate {
    //尺码表分类id
    private int sizeSpecClassId;


    //尺码表分组id
    private int sizeSpecGroupId;


    //尺码表分组名称
    private int sizeSpecGroupName;

    //尺码元素列表
    private List<SalePropSpecSizeMetaData> specSizeMetaDatas;

    public int getSizeSpecClassId() {
        return sizeSpecClassId;
    }

    public void setSizeSpecClassId(int sizeSpecClassId) {
        this.sizeSpecClassId = sizeSpecClassId;
    }

    public int getSizeSpecGroupId() {
        return sizeSpecGroupId;
    }

    public void setSizeSpecGroupId(int sizeSpecGroupId) {
        this.sizeSpecGroupId = sizeSpecGroupId;
    }

    public int getSizeSpecGroupName() {
        return sizeSpecGroupName;
    }

    public void setSizeSpecGroupName(int sizeSpecGroupName) {
        this.sizeSpecGroupName = sizeSpecGroupName;
    }

    public List<SalePropSpecSizeMetaData> getSpecSizeMetaDatas() {
        return specSizeMetaDatas;
    }

    public void setSpecSizeMetaDatas(List<SalePropSpecSizeMetaData> specSizeMetaDatas) {
        this.specSizeMetaDatas = specSizeMetaDatas;
    }
}
