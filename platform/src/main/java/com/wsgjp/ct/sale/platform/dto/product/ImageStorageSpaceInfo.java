package com.wsgjp.ct.sale.platform.dto.product;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class ImageStorageSpaceInfo {
    /**
     * 空间名称
     */
    private String name;
    /**
     * 已使用容量,单位kb
     */
    private BigDecimal usedSize;
    /**
     * 总容量,单位kb
     */
    private BigDecimal totalSize;
    /**
     * 图片空间创建时间
     */
    private Date createTime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getUsedSize() {
        return usedSize;
    }

    public void setUsedSize(BigDecimal usedSize) {
        this.usedSize = usedSize;
    }

    public BigDecimal getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(BigDecimal totalSize) {
        this.totalSize = totalSize;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
