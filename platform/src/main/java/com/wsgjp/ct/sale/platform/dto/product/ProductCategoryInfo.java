package com.wsgjp.ct.sale.platform.dto.product;


import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import lombok.Getter;

import java.math.BigInteger;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class ProductCategoryInfo {
    public ProductCategoryInfo() {
    }

    public ProductCategoryInfo(EshopSystemParams param) {
        if (null == param) {
            return;
        }
        if (null != param.geteShopId()) {
            getEshopIds().add(param.geteShopId());
        }
        if (StringUtils.isNotEmpty(param.getFullName())) {
            getEshopNames().add(param.getFullName());
        }
    }

    /**
     * 类目全路径名称
     * 一级类目/二级类目/三级类目
     * 数码/手机/华为手机
     */
    private String fullPathCatName;

    /**
     * 一级类目id,二级类目id,三级类目id
     */
    @Getter
    private String fullPathCatId;


    /**
     * 类目ID
     */
    private String catId;
    /**
     * 类目名称
     */
    private String catName;
    /**
     * 类目级别
     */
    private Integer depth;
    /**
     * 父类目ID
     */
    private String parentCatId;
    /**
     * 是否还有子类目
     */
    private boolean hasSon;
    /**
     * 网店ID列表
     */
    private Set<BigInteger> eshopIds;
    /**
     * 网店名称
     */
    private Set<String> eshopNames;

    /**
     * 阿里巴巴
     * 类目的类型，1为1688大市场类目，2为1688工业品专业化类目，3为1688主流商品类目
     */
    private String categoryType;

    public String getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(String categoryType) {
        this.categoryType = categoryType;
    }

    public String getFullPathCatName() {
        return fullPathCatName;
    }

    public void setFullPathCatName(String fullPathCatName) {
        this.fullPathCatName = fullPathCatName;
    }

    public String getCatId() {
        return catId;
    }

    public void setCatId(String catId) {
        this.catId = catId;
    }

    public String getCatName() {
        return catName;
    }

    public void setCatName(String catName) {
        this.catName = catName;
    }

    public Integer getDepth() {
        return depth;
    }

    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    public String getParentCatId() {
        return parentCatId;
    }

    public void setParentCatId(String parentCatId) {
        this.parentCatId = parentCatId;
    }

    public boolean isHasSon() {
        return hasSon;
    }

    public void setHasSon(boolean hasSon) {
        this.hasSon = hasSon;
    }

    public Set<BigInteger> getEshopIds() {
        if (eshopIds == null) {
            eshopIds = new HashSet<>();
        }
        return eshopIds;
    }

    public void setEshopIds(Set<BigInteger> eshopIds) {
        this.eshopIds = eshopIds;
    }

    public Set<String> getEshopNames() {
        if (eshopNames == null) {
            eshopNames = new HashSet<>();
        }
        return eshopNames;
    }

    public void setEshopNames(Set<String> eshopNames) {
        this.eshopNames = eshopNames;
    }

    public void setFullPathCatId(String fullPathCatId) {
        this.fullPathCatId = fullPathCatId;
    }

    public String getFullPathCatId() {
        return fullPathCatId;
    }
}
