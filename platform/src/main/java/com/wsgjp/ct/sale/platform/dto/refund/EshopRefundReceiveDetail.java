package com.wsgjp.ct.sale.platform.dto.refund;

import com.wsgjp.ct.sale.platform.dto.refund.entity.BatchInfo;
import com.wsgjp.ct.sale.platform.dto.refund.entity.SnInfo;
import com.wsgjp.ct.sale.platform.enums.StorageType;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class EshopRefundReceiveDetail {
    @ApiModelProperty("售后子单号")
    private String oid;
    @ApiModelProperty("货品id")
    private String platformNumId;
    @ApiModelProperty("商品编码")
    private String platformXcode;
    @ApiModelProperty("实际收货数量")
    private int actualReceiveQty;
    @ApiModelProperty("售后单退款数量")
    private int refundQty;
    @ApiModelProperty("库存类型")
    private StorageType storageType;

    @ApiModelProperty("是否是套餐行")
    private boolean comboRowId;
    @ApiModelProperty("是否是套餐里的商品")
    private boolean comboRowProduct;
    @ApiModelProperty("批次信息")
    private List<BatchInfo> batchInfos;
    @ApiModelProperty("序列号信息")

    private List<SnInfo> snInfos;


    public boolean isComboRowId() {
        return comboRowId;
    }

    public void setComboRowId(boolean comboRowId) {
        this.comboRowId = comboRowId;
    }

    public boolean isComboRowProduct() {
        return comboRowProduct;
    }

    public void setComboRowProduct(boolean comboRowProduct) {
        this.comboRowProduct = comboRowProduct;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public int getActualReceiveQty() {
        return actualReceiveQty;
    }

    public void setActualReceiveQty(int actualReceiveQty) {
        this.actualReceiveQty = actualReceiveQty;
    }

    public int getRefundQty() {
        return refundQty;
    }

    public void setRefundQty(int refundQty) {
        this.refundQty = refundQty;
    }

    public StorageType getStorageType() {
        return storageType;
    }

    public void setStorageType(StorageType storageType) {
        this.storageType = storageType;
    }

    public String getPlatformXcode() {
        return platformXcode;
    }

    public void setPlatformXcode(String platformXcode) {
        this.platformXcode = platformXcode;
    }

    public List<BatchInfo> getBatchInfos() {
        return batchInfos;
    }

    public void setBatchInfos(List<BatchInfo> batchInfos) {
        this.batchInfos = batchInfos;
    }

    public List<SnInfo> getSnInfos() {
        return snInfos;
    }

    public void setSnInfos(List<SnInfo> snInfos) {
        this.snInfos = snInfos;
    }
}
