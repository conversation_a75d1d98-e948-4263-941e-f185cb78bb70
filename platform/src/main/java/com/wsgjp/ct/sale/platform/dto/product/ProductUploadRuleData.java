package com.wsgjp.ct.sale.platform.dto.product;

import java.util.List;

public class ProductUploadRuleData {

    /**
     * sku规则
     */
    private GoodsSkuRule goodsSkuRule;

    /**
     * 商品服务规则
     */
    private List<ServiceRule> serviceRules;

    /**
     * 标品规则
     */
    private GoodsSpuRule spuRule;

    /**
     * 满2件折扣相关规则
     */
    private GoodsDiscountRule discountRule;


    public GoodsSkuRule getGoodsSkuRule() {
        return goodsSkuRule;
    }

    public void setGoodsSkuRule(GoodsSkuRule goodsSkuRule) {
        this.goodsSkuRule = goodsSkuRule;
    }

    public List<ServiceRule> getServiceRules() {
        return serviceRules;
    }

    public void setServiceRules(List<ServiceRule> serviceRules) {
        this.serviceRules = serviceRules;
    }

    public GoodsSpuRule getSpuRule() {
        return spuRule;
    }

    public void setSpuRule(GoodsSpuRule spuRule) {
        this.spuRule = spuRule;
    }

    public GoodsDiscountRule getDiscountRule() {
        return discountRule;
    }

    public void setDiscountRule(GoodsDiscountRule discountRule) {
        this.discountRule = discountRule;
    }

}
