package com.wsgjp.ct.sale.platform.dto.product;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.BigInteger;

public class UnitEntity {

    @ApiModelProperty("单位id ")
    private BigInteger unitId;
    @ApiModelProperty("单位序号")
    private int unitCode;
    @ApiModelProperty("单位名称")
    private String unitName;
    @ApiModelProperty("单位换算关系")
    private BigDecimal unitRate;

    public BigInteger getUnitId() {
        return unitId;
    }

    public void setUnitId(BigInteger unitId) {
        this.unitId = unitId;
    }

    public int getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(int unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(BigDecimal unitRate) {
        this.unitRate = unitRate;
    }
}
