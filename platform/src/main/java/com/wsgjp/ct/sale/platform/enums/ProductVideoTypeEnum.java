package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 商品的视频类型
 * <AUTHOR>
 */

public enum ProductVideoTypeEnum implements CodeEnum {
    MAIN_PIC(1,"主图"),
    PANORAMIC_PIC(2,"全景图");
    private final Integer code;
    private final String desc;

    ProductVideoTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return CodeEnum.super.getName();
    }


    public String getDesc() {
        return desc;
    }
}
