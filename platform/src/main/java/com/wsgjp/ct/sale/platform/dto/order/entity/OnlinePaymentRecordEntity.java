package com.wsgjp.ct.sale.platform.dto.order.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel("线上支付记录（账单）")
public class OnlinePaymentRecordEntity {
    /**
     * 经手人
     */
    private BigInteger etypeId;
    /**
     * 交易流水号
     */
    @ApiModelProperty("交易流水号")
    private BigInteger id;

    /**
     * 支付单号(交易流水号)
     */
    @ApiModelProperty("交易流水号")
    private String paymentNumber;

    /**
     * 商户订单号
     */
    @ApiModelProperty("交易流水号")
    private String merchantOrderNumber;

    /**
     * 商户支付账号
     */
    @ApiModelProperty("交易流水号")
    private String merchantPaymentAccount;

    /**
     * 对方支付账号
     */
    @ApiModelProperty("交易流水号")
    private String oppositePaymentAccount;

    /**
     * 平台业务编号
     */
    @ApiModelProperty("交易流水号")
    private String platformBusinessType;

    /**
     * 平台子业务编号
     */
    @ApiModelProperty("交易流水号")
    private String platformBusinessSubType;

    /**
     * 交易项目编号
     */
    @ApiModelProperty("交易流水号")
    private int businessType;

    /**
     * 交易项目名称
     */
    @ApiModelProperty("交易流水号")
    private String businessName;

    /**
     * 原始订单编号
     */
    @ApiModelProperty("交易流水号")
    private String tradeOrderId;

    /**
     * 收入金额
     */
    @ApiModelProperty("交易流水号")
    private BigDecimal inAmount;

    /**
     * 支出金额
     */
    @ApiModelProperty("交易流水号")
    private BigDecimal outAmount;

    /**
     * 账户余额
     */
    @ApiModelProperty("交易流水号")
    private BigDecimal balanceTotal;

    /**
     * 账单备注
     */
    @ApiModelProperty("交易流水号")
    private String paymentRemark;

    /**
     * 本地科目
     */
    @ApiModelProperty("交易流水号")
    private BigInteger atypeId;

    /**
     * 创建时间
     */
    @ApiModelProperty("交易流水号")
    private Date createTime;

    /**
     * 动账时间
     */
    @ApiModelProperty("动账时间")
    private Date billTime;

    /**
     * 下载时间
     */
    @ApiModelProperty("交易流水号")
    private Date downloadTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("交易流水号")
    private Date updateTime;

    /**
     * 本地单据vchcode
     */
    @ApiModelProperty("交易流水号")
    private BigInteger billVchcode;

    /**
     * 本地单据vchcode
     */
    @ApiModelProperty("交易流水号")
    private BigInteger eshopOrderId;

    /**
     * 本地单据类型
     */
    @ApiModelProperty("交易流水号")
    private int billVchtype;

    /**
     * 本地单据类型
     */
    @ApiModelProperty("0=网店订单到账的流水,1=线下交易单收款流水")
    private int sourceType;

    /**
     * 网店id
     */
    @ApiModelProperty("网店id")
    private BigInteger otypeId;

    /**
     * 账套id
     */
    @ApiModelProperty("账套id")
    private BigInteger profileId;

    /**
     * 原始订单id
     */
    @ApiModelProperty("原始订单id")
    private BigInteger eshopSaleorderVchcode;

    /**
     * 账单索引
     */
    @ApiModelProperty("账单索引")
    private String uniqueMark;
    @ApiModelProperty("交易单单号")
    private String billNumber;
    @ApiModelProperty("交易单id")
    private BigInteger vchcode;
    @ApiModelProperty("审核状态")
    private int processState;
    @ApiModelProperty("支付方式")
    private String paymentMode;

    public Date getBillTime() {
        return billTime;
    }

    public void setBillTime(Date billTime) {
        this.billTime = billTime;
    }

    public BigInteger getEtypeId() {
        return etypeId;
    }

    public void setEtypeId(BigInteger etypeId) {
        this.etypeId = etypeId;
    }

    public String getBillNumber() {
        return billNumber;
    }

    public void setBillNumber(String billNumber) {
        this.billNumber = billNumber;
    }

    public BigInteger getEshopOrderId() {
        return eshopOrderId == null ? BigInteger.ZERO : eshopOrderId;
    }

    public void setEshopOrderId(BigInteger eshopOrderId) {
        this.eshopOrderId = eshopOrderId;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public int getProcessState() {
        return processState;
    }

    public void setProcessState(int processState) {
        this.processState = processState;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getPaymentNumber() {
        return paymentNumber;
    }

    public void setPaymentNumber(String paymentNumber) {
        this.paymentNumber = paymentNumber;
    }

    public String getMerchantOrderNumber() {
        return merchantOrderNumber;
    }

    public void setMerchantOrderNumber(String merchantOrderNumber) {
        this.merchantOrderNumber = merchantOrderNumber;
    }

    public String getMerchantPaymentAccount() {
        return merchantPaymentAccount;
    }

    public void setMerchantPaymentAccount(String merchantPaymentAccount) {
        this.merchantPaymentAccount = merchantPaymentAccount;
    }

    public String getOppositePaymentAccount() {
        return oppositePaymentAccount;
    }

    public void setOppositePaymentAccount(String oppositePaymentAccount) {
        this.oppositePaymentAccount = oppositePaymentAccount;
    }

    public String getPlatformBusinessType() {
        return platformBusinessType == null ? "" : platformBusinessType;
    }

    public void setPlatformBusinessType(String platformBusinessType) {
        this.platformBusinessType = platformBusinessType;
    }

    public String getPlatformBusinessSubType() {
        return platformBusinessSubType == null ? "" : platformBusinessSubType;
    }

    public void setPlatformBusinessSubType(String platformBusinessSubType) {
        this.platformBusinessSubType = platformBusinessSubType;
    }

    public int getBusinessType() {
        return businessType;
    }

    public void setBusinessType(int businessType) {
        this.businessType = businessType;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getTradeOrderId() {
        return tradeOrderId == null ? "" : tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public BigDecimal getInAmount() {
        return inAmount;
    }

    public void setInAmount(BigDecimal inAmount) {
        this.inAmount = inAmount;
    }

    public BigDecimal getOutAmount() {
        return outAmount;
    }

    public void setOutAmount(BigDecimal outAmount) {
        this.outAmount = outAmount;
    }

    public BigDecimal getBalanceTotal() {
        return balanceTotal;
    }

    public void setBalanceTotal(BigDecimal balanceTotal) {
        this.balanceTotal = balanceTotal;
    }

    public String getPaymentRemark() {
        return paymentRemark;
    }

    public void setPaymentRemark(String paymentRemark) {
        this.paymentRemark = paymentRemark;
    }

    public BigInteger getAtypeId() {
        return atypeId == null ? BigInteger.ZERO : atypeId;
    }

    public void setAtypeId(BigInteger atypeId) {
        this.atypeId = atypeId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getDownloadTime() {
        return downloadTime;
    }

    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public BigInteger getBillVchcode() {
        return billVchcode;
    }

    public void setBillVchcode(BigInteger billVchcode) {
        this.billVchcode = billVchcode;
    }

    public int getBillVchtype() {
        return billVchtype;
    }

    public void setBillVchtype(int billVchtype) {
        this.billVchtype = billVchtype;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopSaleorderVchcode() {
        return eshopSaleorderVchcode;
    }

    public void setEshopSaleorderVchcode(BigInteger eshopSaleorderVchcode) {
        this.eshopSaleorderVchcode = eshopSaleorderVchcode;
    }

    public String getUniqueMark() {
        return uniqueMark;
    }

    public void setUniqueMark(String uniqueMark) {
        this.uniqueMark = uniqueMark;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }
}
