package com.wsgjp.ct.sale.platform.entity.request.logistics;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR> 获取运单号请求实体
 */
@ApiModel("获取运单号请求对象")
public class GetTransportNoRequest extends BaseRequest {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true)
    private String tradeId;
    /**
     *  承运商编码
     */
    @ApiModelProperty(value = "承运商编码", required = true)
    private String carrierCode;
    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型", required = true)
    private OrderType orderType;
    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }

    public enum OrderType {
        NORMAL_ORDER(0,"普通订单"),
        EXCHANGE_ORDER(1,"换货订单"),
        BU_JI_ORDER(2,"补寄订单");

        private final int code;

        private final String desc;

        OrderType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
