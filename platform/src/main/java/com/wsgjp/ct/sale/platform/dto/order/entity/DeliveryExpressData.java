package com.wsgjp.ct.sale.platform.dto.order.entity;

import ngp.utils.StringUtils;

import java.util.List;

public class DeliveryExpressData {

    public DeliveryExpressData() {
    }

    public DeliveryExpressData(String tradeId, String message) {
        this.tradeId = tradeId;
        this.success = !StringUtils.isNotEmpty(message);
        this.message = message;
    }

    private String tradeId;

    private List<DeliveryExpressInfo> deliveryExpressInfoList;

    private boolean success = true;

    private String message;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<DeliveryExpressInfo> getDeliveryExpressInfoList() {
        return deliveryExpressInfoList;
    }

    public void setDeliveryExpressInfoList(List<DeliveryExpressInfo> deliveryExpressInfoList) {
        this.deliveryExpressInfoList = deliveryExpressInfoList;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }
}
