package com.wsgjp.ct.sale.platform.dto.product;

public class ServiceRule {
    public ServiceRule() {
    }

    ;

    public ServiceRule(String serviceKey, boolean visible, boolean required) {
        this.serviceKey = serviceKey;
        this.visible = visible;
        this.required = required;
    }

    private String serviceKey;
    private boolean visible;
    private boolean required;

    public String getServiceKey() {
        return serviceKey;
    }

    public void setServiceKey(String serviceKey) {
        this.serviceKey = serviceKey;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }
}
