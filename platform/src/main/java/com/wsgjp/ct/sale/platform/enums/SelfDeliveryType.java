package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum SelfDeliveryType implements CodeEnum {

    /**
     *
     * */
    Express(1,"快递"),
    SentByTruck(2,"卡车配送"),
    Other(3,"其它");

    private int code;
    private String name;

    SelfDeliveryType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
