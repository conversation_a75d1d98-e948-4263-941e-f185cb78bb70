package com.wsgjp.ct.sale.platform.dto.refund;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class EshopTmcRefundMsgDto {
    private BigInteger profileId;
    private BigInteger eshopId;
    private ShopType shopType;
    private String tradeId;
    private String refundId;
    /**
     * 消息体
     */
    private String message;
    private RefundStatus refundStatus;

    public EshopTmcRefundMsgDto(BigInteger profileId, BigInteger eshopId, ShopType shopType, String tradeId, String refundId, String message, RefundStatus refundStatus) {
        this.profileId = profileId;
        this.eshopId = eshopId;
        this.shopType = shopType;
        this.tradeId = tradeId;
        this.refundId = refundId;
        this.message = message;
        this.refundStatus = refundStatus;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public RefundStatus getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(RefundStatus refundStatus) {
        this.refundStatus = refundStatus;
    }
}
