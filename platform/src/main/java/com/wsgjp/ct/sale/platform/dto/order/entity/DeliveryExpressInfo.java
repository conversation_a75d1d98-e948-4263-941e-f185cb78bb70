package com.wsgjp.ct.sale.platform.dto.order.entity;

import java.math.BigDecimal;
import java.util.List;

public class DeliveryExpressInfo {
    /**
     * 物流code
     */
    private String logisticsCode;
    /**
     * 物流名称
     */
    private String logisticsName;
    /**
     * 是否可达
     */
    private boolean deliverable;
    /**
     * 是否推荐
     */
    private boolean recommended;

    /**
     * 权重 (抖店是超过其他物流公司百分比)
     */
    private String levelPercent;

    /**
     * 所需运费
     */
    private BigDecimal freightFree;
    /**
     * 物流方案id temu使用
     */
    private String expressCompanyId;

    /**
     * 线上物流公司名称
     */
    private String expressCompanyName;

    /**
     * 可预约揽收时间
     */
    private List<ChannelScheduleTime> channelScheduleTimeList;

    private Boolean standbyExpress;

    public Boolean getStandbyExpress() {
        return standbyExpress;
    }

    public void setStandbyExpress(Boolean standbyExpress) {
        this.standbyExpress = standbyExpress;
    }

    public List<ChannelScheduleTime> getChannelScheduleTimeList() {
        return channelScheduleTimeList;
    }

    public void setChannelScheduleTimeList(List<ChannelScheduleTime> channelScheduleTimeList) {
        this.channelScheduleTimeList = channelScheduleTimeList;
    }

    public static class ChannelScheduleTime{

        /**
         * 可预约日期 北京时间 格式
         * yyyy-MM-dd
         */
        private String date;

        /**
         * 可预约日期的时间起点 北
         * 京时间 格式HH:mm
         */
        private String StartTime;

        /**
         * 可预约日期的时间终点 北
         * 京时间 格式HH:mm
         */
        private String EndTime;

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getStartTime() {
            return StartTime;
        }

        public void setStartTime(String startTime) {
            StartTime = startTime;
        }

        public String getEndTime() {
            return EndTime;
        }

        public void setEndTime(String endTime) {
            EndTime = endTime;
        }
    }

    public String getExpressCompanyId() {
        return expressCompanyId;
    }

    public void setExpressCompanyId(String expressCompanyId) {
        this.expressCompanyId = expressCompanyId;
    }

    public String getExpressCompanyName() {
        return expressCompanyName;
    }

    public void setExpressCompanyName(String expressCompanyName) {
        this.expressCompanyName = expressCompanyName;
    }

    public BigDecimal getFreightFree() {
        return freightFree;
    }

    public void setFreightFree(BigDecimal freightFree) {
        this.freightFree = freightFree;
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public String getLogisticsName() {
        return logisticsName;
    }

    public void setLogisticsName(String logisticsName) {
        this.logisticsName = logisticsName;
    }

    public boolean isDeliverable() {
        return deliverable;
    }

    public void setDeliverable(boolean deliverable) {
        this.deliverable = deliverable;
    }

    public boolean isRecommended() {
        return recommended;
    }

    public void setRecommended(boolean recommended) {
        this.recommended = recommended;
    }

    public String getLevelPercent() {
        return levelPercent;
    }

    public void setLevelPercent(String levelPercent) {
        this.levelPercent = levelPercent;
    }
}
