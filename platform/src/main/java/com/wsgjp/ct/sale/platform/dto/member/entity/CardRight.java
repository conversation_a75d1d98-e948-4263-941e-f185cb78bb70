package com.wsgjp.ct.sale.platform.dto.member.entity;

/**
 * <AUTHOR>
 * 会员卡权益
 */
public class CardRight {
    /**
     * 折扣权益字段，折数 ，1表示0.01折范围选择：1-100（type为2时必传）
     */
    public int discount;
    /**
     * 是否可用，必须为true
     */
    public boolean isAvailable;

    /**
     * 积分权益字段，积分数量（type为4时必传）
     */
    public int points;
    /**
     * 权益类型，1:包邮；2:折扣；3:送现金券；4：送积分
     */
    public int cardType;

    public int getDiscount() {
        return discount;
    }

    public void setDiscount(int discount) {
        this.discount = discount;
    }

    public boolean isAvailable() {
        return isAvailable;
    }

    public void setAvailable(boolean available) {
        isAvailable = available;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public int getCardType() {
        return cardType;
    }

    public void setCardType(int cardType) {
        this.cardType = cardType;
    }
}
