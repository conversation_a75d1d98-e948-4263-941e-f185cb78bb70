package com.wsgjp.ct.sale.platform.rds;

import com.wsgjp.ct.common.enums.core.enums.ShopType;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RdsServiceManager {
    private static List<RdsService> rdsServiceList;

    public RdsServiceManager(List<RdsService> rdsServiceList) {
        RdsServiceManager.rdsServiceList = rdsServiceList;
    }

    public static RdsService getRdsService(ShopType shopType) {
        for (RdsService service : rdsServiceList) {
            if (Arrays.stream(service.supportedShopTypes()).anyMatch(type -> type == shopType)) {
                return service;
            }
        }
        throw new RuntimeException("未找到：" + shopType.getName() + "平台的Rds实现，请检查是否有实现RdsService，且放入Spring boot依赖管理（@Service、@Component、@Configuration、getBean等等）");
    }
}
