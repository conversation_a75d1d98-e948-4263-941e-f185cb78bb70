package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum AuthType implements CodeEnum {

    NO_AUTH(0, "无需授权"),
    DirectAuth(1, "需要弹出授权页面"),
    IndirectAuth(2, "不需要弹出授权页面"),
    LoginInfoAuth(3, "需要登录信息的授权"),
    DirectAuth_Choose_(4, "需要弹出授权页面并且需要选择销售机构"),
    IndirectAuth_Step(5, "用户登录平台开平授权，erp操作获取授权");

    private int index;

    private String name;

    AuthType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}
