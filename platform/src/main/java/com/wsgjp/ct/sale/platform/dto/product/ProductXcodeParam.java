package com.wsgjp.ct.sale.platform.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 按商家编码获取商品信息参数
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@ApiModel("按商家编码获取商品信息参数")
public class ProductXcodeParam {
    @ApiModelProperty("商家编码")
    private String xcode;

    @ApiModelProperty("往来单位名称、销售单位名称")
    private String btypeName;

    @ApiModelProperty("商品条码")
    private String barCode;

    @ApiModelProperty("销售单位名称")
    private String saleUnitName;

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public String getBtypeName() {
        return btypeName;
    }

    public void setBtypeName(String btypeName) {
        this.btypeName = btypeName;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getSaleUnitName() {
        return saleUnitName;
    }

    public void setSaleUnitName(String saleUnitName) {
        this.saleUnitName = saleUnitName;
    }
}
