package com.wsgjp.ct.sale.platform.dto.warehouse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @desc 平台仓实体：平台仓适用于供应商业务，需要客户从电商仓发货到平台仓，平台再统一发货。
 * @type add
 * @date 2020-12-31 15:05:00
 **/
@ApiModel("平台仓库信息")
public class EshopOnlineStoreInfo extends BaseWareHouseInfo {

    /**
     * 仓库类型
     * 1=平台自流转仓库,2=平台采购仓,3=商家云仓
     */
    @ApiModelProperty("仓库类型")
    private int storeType;
    /**
     * 标签 例如：JTX
     */
    @ApiModelProperty("仓库标签")
    private String label;

    public int getStoreType() {
        return storeType;
    }

    public void setStoreType(int storeType) {
        this.storeType = storeType;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
