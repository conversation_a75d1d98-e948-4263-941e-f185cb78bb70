package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 商品包装类型
 *
 * <AUTHOR>
 */

public enum ProductPackTypeEnum implements CodeEnum {
    NORMAL(1, "普通商品"),
    FRAGILE(2, "易碎品"),
    LIQUID(3, "裸装液体"),
    PACKAGE_LIQUID(4, "带包装液体"),
    ORIGINAL_PACKAGE(5, "原包装出库");
    private final Integer code;
    private final String desc;

    ProductPackTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return CodeEnum.super.getName();
    }


    public String getDesc() {
        return desc;
    }
}

