package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum OperationTypeEnum implements CodeEnum {

    OpenUrl(1,"打开链接"),
    OpenOrCloseConfig(2,"开启或关闭配置");

    private int code;
    private String name;

    OperationTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }
    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
