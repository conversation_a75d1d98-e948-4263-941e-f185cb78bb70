package com.wsgjp.ct.sale.platform.dto.product;

public class GoodsDiscountRule {
    /**
     * 是否标品=》标品仅展示关键属性，其他属性不展示
     */
    private boolean mustTwoPiecesDiscount;

    /**
     * 允许的最大折扣
     */
    private int maxTwoPiecesDiscount;

    /**
     * 允许的最大折扣允许的最小折扣
     */
    private int minTwoPiecesDiscount;

    /**
     * 推荐的折扣
     */
    private int recommendTwoPiecesDiscount;

    public boolean isMustTwoPiecesDiscount() {
        return mustTwoPiecesDiscount;
    }

    public void setMustTwoPiecesDiscount(boolean mustTwoPiecesDiscount) {
        this.mustTwoPiecesDiscount = mustTwoPiecesDiscount;
    }

    public int getMaxTwoPiecesDiscount() {
        return maxTwoPiecesDiscount;
    }

    public void setMaxTwoPiecesDiscount(int maxTwoPiecesDiscount) {
        this.maxTwoPiecesDiscount = maxTwoPiecesDiscount;
    }

    public int getMinTwoPiecesDiscount() {
        return minTwoPiecesDiscount;
    }

    public void setMinTwoPiecesDiscount(int minTwoPiecesDiscount) {
        this.minTwoPiecesDiscount = minTwoPiecesDiscount;
    }

    public int getRecommendTwoPiecesDiscount() {
        return recommendTwoPiecesDiscount;
    }

    public void setRecommendTwoPiecesDiscount(int recommendTwoPiecesDiscount) {
        this.recommendTwoPiecesDiscount = recommendTwoPiecesDiscount;
    }
}
