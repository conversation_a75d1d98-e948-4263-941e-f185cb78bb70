package com.wsgjp.ct.sale.platform.entity.request.order;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import io.swagger.annotations.ApiModel;

import java.util.Date;

@ApiModel("修改地址申请查询请求")
public class QueryModifyAddressRequest extends BaseRequest {

    private Boolean isIncrement;
    private Date beginTime;
    private Date endTime;
    private AuditStatus auditStatus;
    /**
     * 是否仅发送修改地址通知
     */
    private boolean onlyNotifyAddressModify;
    /**
     * 是否强制回告平台修改地址成功
     */
    private boolean forceCallBackSuccess;

    public Boolean getIncrement() {
        return isIncrement;
    }

    public void setIncrement(Boolean increment) {
        isIncrement = increment;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public AuditStatus getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(AuditStatus auditStatus) {
        this.auditStatus = auditStatus;
    }

    public boolean isOnlyNotifyAddressModify() {
        return onlyNotifyAddressModify;
    }

    public void setOnlyNotifyAddressModify(boolean onlyNotifyAddressModify) {
        this.onlyNotifyAddressModify = onlyNotifyAddressModify;
    }

    public boolean isForceCallBackSuccess() {
        return forceCallBackSuccess;
    }

    public void setForceCallBackSuccess(boolean forceCallBackSuccess) {
        this.forceCallBackSuccess = forceCallBackSuccess;
    }
}
