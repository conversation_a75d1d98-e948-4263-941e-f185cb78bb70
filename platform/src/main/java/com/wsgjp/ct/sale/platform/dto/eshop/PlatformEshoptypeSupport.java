package com.wsgjp.ct.sale.platform.dto.eshop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 09/04/2024 下午 12:24
 */
@ApiModel("平台信息")
public class PlatformEshoptypeSupport {
    /**
     * 平台类型名称(例如：有赞)
     */
    @ApiModelProperty("平台名")
    private String name;
    /**
     * 平台类型编码
     */
    @ApiModelProperty("平台编号")
    private int platformType;
    private int shoptype;

    @ApiModelProperty("标签")
    private boolean sign;
    @ApiModelProperty("地址")
    private String url;
    private int type;
    private int AuthType;
    private int showAppKey;


    public int getShoptype() {
        return shoptype;
    }

    public void setShoptype(int shoptype) {
        this.shoptype = shoptype;
    }

    public int getShowAppKey() {
        return showAppKey;
    }

    public void setShowAppKey(int showAppKey) {
        this.showAppKey = showAppKey;
    }

    public int getAuthType() {
        return AuthType;
    }

    public void setAuthType(int authType) {
        AuthType = authType;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public PlatformEshoptypeSupport() {
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isSign() {
        return sign;
    }

    public void setSign(boolean sign) {
        this.sign = sign;
    }

    public PlatformEshoptypeSupport(String name, int platformType) {
        this.name = name;
        this.platformType = platformType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPlatformType() {
        return platformType;
    }

    public void setPlatformType(int platformType) {
        this.platformType = platformType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, platformType);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PlatformEshoptypeSupport that = (PlatformEshoptypeSupport) o;
        return platformType == that.platformType && Objects.equals(name, that.name);
    }

    @Override
    public String toString() {
        return "PlatformSupport{" +
                "name='" + name + '\'' +
                ", platformType=" + platformType +
                '}';
    }
}
