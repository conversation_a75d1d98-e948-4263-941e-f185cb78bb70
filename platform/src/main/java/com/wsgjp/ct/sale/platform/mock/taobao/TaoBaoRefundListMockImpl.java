package com.wsgjp.ct.sale.platform.mock.taobao;

import com.taobao.api.domain.Refund;
import com.taobao.api.internal.util.TaobaoUtils;
import com.taobao.api.request.RefundsReceiveGetRequest;
import com.taobao.api.response.RefundGetResponse;
import com.taobao.api.response.RefundsReceiveGetResponse;
import com.wsgjp.ct.sale.platform.entity.request.mock.QueryDataListParam;
import com.wsgjp.ct.sale.platform.entity.request.other.MockRequest;
import com.wsgjp.ct.sale.platform.enums.MockQueryType;
import com.wsgjp.ct.sale.platform.mock.ApiMockQueryService;
import com.wsgjp.ct.sale.platform.mock.ApiMockerService;
import com.wsgjp.ct.sale.platform.utils.MockUtils;
import ngp.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2024/5/10 9:39
 */
@Service
public class TaoBaoRefundListMockImpl implements ApiMockerService {

    private final ApiMockQueryService queryService;

    public TaoBaoRefundListMockImpl(ApiMockQueryService queryService) {
        this.queryService = queryService;
    }

    @Override
    public Object queryData(MockRequest request) {
        RefundsReceiveGetResponse response = new RefundsReceiveGetResponse();
        QueryDataListParam param = MockUtils.initParam(request);
        param.setType(MockQueryType.Refund);
        long dataCount = queryService.queryDataCount(param);
        if (dataCount == 0) {
            return response;
        }
        List<String> list = queryService.queryDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return response;
        }
        List<Refund> refundList = new ArrayList<>();
        for (String item : list) {
            try {
                RefundGetResponse refundGetResponse = TaobaoUtils.parseResponse(item, RefundGetResponse.class);
                refundList.add(refundGetResponse.getRefund());
            } catch (Exception ex) {
                throw new RuntimeException("淘宝售后单mock数据解析异常：" + item);
            }
        }
        response.setRefunds(refundList);
        return response;
    }

    @Override
    public String methodName() {
        return "taobao.refunds.receive.get";
    }

    @Override
    public void buildRequest(MockRequest request, Object[] args) {
        try {
            RefundsReceiveGetRequest receiveGetRequest = (RefundsReceiveGetRequest) args[0];
            if (receiveGetRequest.getStartModified() != null) {
                request.setEndTime(receiveGetRequest.getEndModified());
                request.setBeginTime(receiveGetRequest.getStartModified());
                request.setPageSize(receiveGetRequest.getPageSize().intValue());
                request.setPageIndex(receiveGetRequest.getPageNo().intValue());
                request.setIncrease(true);
            }
        } catch (Exception ex) {
            throw new RuntimeException("售后列表下载，请求参数类型不正确！");
        }
    }

}
