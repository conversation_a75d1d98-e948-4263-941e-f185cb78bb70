package com.wsgjp.ct.sale.platform.dto.product;

import java.math.BigInteger;
import java.util.List;

public class ProductBigData {
    private BigInteger profileId;
    private BigInteger etypeId;
    private List<MultiTimeStock> multiTimeStocks;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEtypeId() {
        return etypeId;
    }

    public void setEtypeId(BigInteger etypeId) {
        this.etypeId = etypeId;
    }

    public List<MultiTimeStock> getMultiTimeStocks() {
        return multiTimeStocks;
    }

    public void setMultiTimeStocks(List<MultiTimeStock> multiTimeStocks) {
        this.multiTimeStocks = multiTimeStocks;
    }
}
