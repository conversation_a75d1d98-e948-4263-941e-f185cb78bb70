package com.wsgjp.ct.sale.platform.dto.sendgoods;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2020-01-14 15:18
 */
@ApiModel("发货订单明细对象")
public class SendGoodsDetail {
    @ApiModelProperty("订单Id")
    private String tradeId;
    @ApiModelProperty("子订单id")
    private String oid;
    @ApiModelProperty("订单skuId")
    private String skuId;
    @ApiModelProperty("本地skuId")
    private String localSkuId;
    @ApiModelProperty("订单numId")
    private String numId;
    @ApiModelProperty("订单商家编码")
    private String xcode;
    @ApiModelProperty("本地商家编码")
    private String localXcode;
    @ApiModelProperty("重量")
    private BigDecimal weight;
    @ApiModelProperty("数量")
    private BigDecimal qty;
    @ApiModelProperty("平台特殊传递信息")
    private String platformSpecialJson;
    private String packageOrderId;
    @ApiModelProperty(notes = "根据平台订单确认是否使用")
    private String snNo;
    @ApiModelProperty("物流名称")
    private String freightName;
    @ApiModelProperty("物流单号")
    private String freightBillNo;
    //业务组使用字段，接口不需要传值
    @ApiModelProperty(value = "批次Id", hidden = true)
    private BigInteger batchId;
    @ApiModelProperty("是否为赠品")
    private boolean gift;
    /**
     * 套餐行有值
     * 套餐明细有值
     * 普通明细=0
     */
    @ApiModelProperty("套餐明细ID")
    private BigInteger comboRowId;
    /**
     * 套餐行=true
     * 套餐明细=false
     * 普通明细=false
     */
    @ApiModelProperty("是否为套餐行")
    private Boolean combo = false;

    public BigInteger getComboRowId() {
        return comboRowId;
    }

    public void setComboRowId(BigInteger comboRowId) {
        this.comboRowId = comboRowId;
    }

    public Boolean getCombo() {
        return combo;
    }

    public void setCombo(Boolean combo) {
        this.combo = combo;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getNumId() {
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public String getLocalXcode() {
        return localXcode;
    }

    public void setLocalXcode(String localXcode) {
        this.localXcode = localXcode;
    }

    public String getLocalSkuId() {
        return localSkuId;
    }

    public void setLocalSkuId(String localSkuId) {
        this.localSkuId = localSkuId;
    }

    public String getPlatformSpecialJson() {
        return platformSpecialJson;
    }

    public void setPlatformSpecialJson(String platformSpecialJson) {
        this.platformSpecialJson = platformSpecialJson;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getPackageOrderId() {
        return packageOrderId;
    }

    public void setPackageOrderId(String packageOrderId) {
        this.packageOrderId = packageOrderId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getFreightBillNo() {
        return freightBillNo;
    }

    public void setFreightBillNo(String freightBillNo) {
        this.freightBillNo = freightBillNo;
    }

    public BigInteger getBatchId() {
        return batchId;
    }

    public void setBatchId(BigInteger batchId) {
        this.batchId = batchId;
    }

    public boolean isGift() {
        return gift;
    }

    public void setGift(boolean gift) {
        this.gift = gift;
    }
}
