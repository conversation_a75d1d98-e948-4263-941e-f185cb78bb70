package com.wsgjp.ct.sale.platform.log;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.enums.PlatformBizTypeEnum;
import com.wsgjp.ct.support.log.annotation.LogEntity;
import com.wsgjp.ct.support.log.entity.BaseLog;
import com.wsgjp.ct.support.log.type.DBType;

import java.math.BigInteger;

/**
 * <AUTHOR> 2023/11/17 10:56
 */
@LogEntity(tableName = "pl_eshop_api_biz_log", isReplaceInto = true, dbType = DBType.LOG)
public class PlatformBizLog extends BaseLog {

    private BigInteger eshopId;
    private PlatformBizTypeEnum bizType;
    private ShopType eshopType;
    private String requestInfo;
    private String responseInfo;
    private String hashMark;

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public PlatformBizTypeEnum getBizType() {
        return bizType;
    }

    public void setBizType(PlatformBizTypeEnum bizType) {
        this.bizType = bizType;
    }

    public ShopType getEshopType() {
        return eshopType;
    }

    public void setEshopType(ShopType eshopType) {
        this.eshopType = eshopType;
    }

    public String getRequestInfo() {
        return requestInfo;
    }

    public void setRequestInfo(String requestInfo) {
        this.requestInfo = requestInfo;
    }

    public String getResponseInfo() {
        return responseInfo;
    }

    public void setResponseInfo(String responseInfo) {
        this.responseInfo = responseInfo;
    }

    public String getHashMark() {
        return hashMark;
    }

    public void setHashMark(String hashMark) {
        this.hashMark = hashMark;
    }
}
