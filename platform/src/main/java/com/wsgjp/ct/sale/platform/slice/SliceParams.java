package com.wsgjp.ct.sale.platform.slice;

import com.wsgjp.ct.sale.platform.enums.SliceType;

/**
 * <AUTHOR>
 */
public class SliceParams {
    private int maxPage;
    private int pageSize;
    private int pageStartIndex;
    /**
     * 下载最大时间间隔限制 单位:秒
     */
    private int downloadInterval;
    /**
     * 下载开始时间偏移量 单位:分钟
     * 例如：京东自营下载售后单只能根据创建时间下载,为了不漏单将下载售后单的开始时间向前便宜3天
     */
    private int downloadOffsetTime;
    private int rdsAliveTime;
    private int rdsHoldTime;
    private Integer businessType;
    private SliceType type;
    private int status;
    /**
     * 不支持RDS
     */
    private boolean noSupportRds;

    public int getMaxPage() {
        return maxPage;
    }

    public void setMaxPage(int maxPage) {
        this.maxPage = maxPage;
    }

    public int getPageStartIndex() {
        return pageStartIndex;
    }

    public void setPageStartIndex(int pageStartIndex) {
        this.pageStartIndex = pageStartIndex;
    }

    public int getDownloadInterval() {
        return downloadInterval;
    }

    public void setDownloadInterval(int downloadInterval) {
        this.downloadInterval = downloadInterval;
    }

    public int getDownloadOffsetTime() {
        return downloadOffsetTime;
    }

    public void setDownloadOffsetTime(int downloadOffsetTime) {
        this.downloadOffsetTime = downloadOffsetTime;
    }

    public int getRdsAliveTime() {
        return rdsAliveTime;
    }

    public void setRdsAliveTime(int rdsAliveTime) {
        this.rdsAliveTime = rdsAliveTime;
    }

    public int getRdsHoldTime() {
        return rdsHoldTime;
    }

    public void setRdsHoldTime(int rdsHoldTime) {
        this.rdsHoldTime = rdsHoldTime;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public SliceType getType() {
        return type;
    }

    public void setType(SliceType type) {
        this.type = type;
    }

    public Integer getBusinessType() {
        if (null == businessType) {
            businessType = 0;
        }
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public boolean hasPageLimit() {
        return maxPage > 0;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isNoSupportRds() {
        return noSupportRds;
    }

    public void setNoSupportRds(boolean noSupportRds) {
        this.noSupportRds = noSupportRds;
    }
}
