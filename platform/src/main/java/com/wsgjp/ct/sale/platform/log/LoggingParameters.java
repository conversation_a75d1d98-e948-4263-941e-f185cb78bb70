package com.wsgjp.ct.sale.platform.log;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LoggingParameters {
    @Value("${app.id}")
    private String appId;
    @Value("${apollo.cluster}")
    private String cluster;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }
}
