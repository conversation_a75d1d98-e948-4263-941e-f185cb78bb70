package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.platform.enums.RefundFreightInterceptStatus;
import ngp.utils.Md5Utils;

import java.math.BigInteger;
import java.util.Date;

public class EshopAdvanceOrderFreight {
    private BigInteger id;
    private BigInteger profileId;
    private BigInteger vchcode;
    private BigInteger freightBtypeId;
    private String freightBtypeName;
    private String freightCode;
    private String freightNo;
    private String freightName;
    private Date createTime;
    private Date updateTime;
    /**
     *物流拦截状态 0 = 无需拦截  1 = 拦截中  2 = 拦截成功  3 = 拦截失败
     */
    private RefundFreightInterceptStatus freightInterceptStatus;

    private String tradeId;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public BigInteger getFreightBtypeId() {
        return freightBtypeId;
    }

    public void setFreightBtypeId(BigInteger freightBtypeId) {
        this.freightBtypeId = freightBtypeId;
    }

    public String getFreightBtypeName() {
        return freightBtypeName;
    }

    public void setFreightBtypeName(String freightBtypeName) {
        this.freightBtypeName = freightBtypeName;
    }

    public String getFreightCode() {
        return freightCode;
    }

    public void setFreightCode(String freightCode) {
        this.freightCode = freightCode;
    }

    public String getFreightNo() {
        return freightNo;
    }

    public void setFreightNo(String freightNo) {
        this.freightNo = freightNo;
    }

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }


    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public RefundFreightInterceptStatus getFreightInterceptStatus() {
        return freightInterceptStatus;
    }

    public void setFreightInterceptStatus(RefundFreightInterceptStatus freightInterceptStatus) {
        this.freightInterceptStatus = freightInterceptStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    /**
     * 0=退回，1=寄出
     */

    public String getCompareKey(){
        return Md5Utils.md5(String.format("%s%s%s%s",this.getFreightName(),this.getFreightCode(),this.getFreightNo(),this.getFreightInterceptStatus()));
    }
}
