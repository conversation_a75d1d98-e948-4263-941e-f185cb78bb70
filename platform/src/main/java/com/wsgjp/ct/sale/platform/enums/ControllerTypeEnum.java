package com.wsgjp.ct.sale.platform.enums;

/**
 * <AUTHOR>
 * @date 2019-12-27 17:00
 */
public enum ControllerTypeEnum {
    /**
     * 文本控件
     */
    TEXT(0,"文本控件"),
    /**
     * 数字控件
     */
    NUMBER(1,"数字控件"),
    /**
     * 选择控件
     */
    SELECTOR(2,"选择控件");

    private int flag;

    private String name;

    ControllerTypeEnum(int flag,String name){
        this.flag=flag;
        this.name=name;
    }

    @Override
    public String toString(){
        return name;
    }
}
