package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 售后类型
 *
 * <AUTHOR>
 * @date 2020-05-30
 */
public enum RefundTypeEnum implements CodeEnum {
    ALL(-1, "全部"),
    MONEY_ONLY(0, "已发-仅退款"),
    MONEY_GOODS(1, "已发-退款退货"),
    EXCHANGE_GOODS(2, "已发-换货"),
    RESEND_GOODS(3, "已发-破损补发"),
    OMIT_SEND_GOODS(4, "已发-漏发补发"),
    REFUND_ONLY_ON_SALE(5, "未发-仅退款"),
    REFUND_ONLY_PART_MONEY(6, "卖家补差价"),
    REFUND_FEE_AFTER_SALE(7,"已发-售后费用");



    private int flag;

    private String name;

    RefundTypeEnum(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }


    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public static RefundTypeEnum getEnum(int code) {
        return Arrays.stream(RefundTypeEnum.values()).filter(item -> {
            return item.getCode() == code;
        }).collect(Collectors.toList()).get(0);
    }
}