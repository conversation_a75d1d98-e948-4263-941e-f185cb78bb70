package com.wsgjp.ct.sale.platform.dto.refund;

import com.wsgjp.ct.common.enums.core.enums.LogisticInterceptEnum;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR> 2024/3/11 14:30
 */
public class RefundInterceptEntity {
    @ApiModelProperty("快递拦截的状态")
    private LogisticInterceptEnum interceptStatus;

    @ApiModelProperty("拦截的快递公司")
    private String interceptFreightName;

    @ApiModelProperty("拦截的快递公司编码")
    private String interceptFreightCode;

    @ApiModelProperty("拦截的快递单号")
    private String interceptFreightBillNo;

    @ApiModelProperty("拦截的时间")
    private Date interceptDate;

    @ApiModelProperty("业务子单号")
    private String subBizOrderId;

    @ApiModelProperty("拦截失败原因")
    private String interceptFailCode;

    public LogisticInterceptEnum getInterceptStatus() {
        return interceptStatus;
    }

    public void setInterceptStatus(LogisticInterceptEnum interceptStatus) {
        this.interceptStatus = interceptStatus;
    }

    public String getInterceptFreightName() {
        return interceptFreightName;
    }

    public void setInterceptFreightName(String interceptFreightName) {
        this.interceptFreightName = interceptFreightName;
    }

    public String getInterceptFreightCode() {
        return interceptFreightCode;
    }

    public void setInterceptFreightCode(String interceptFreightCode) {
        this.interceptFreightCode = interceptFreightCode;
    }

    public String getInterceptFreightBillNo() {
        return interceptFreightBillNo;
    }

    public void setInterceptFreightBillNo(String interceptFreightBillNo) {
        this.interceptFreightBillNo = interceptFreightBillNo;
    }

    public Date getInterceptDate() {
        return interceptDate;
    }

    public void setInterceptDate(Date interceptDate) {
        this.interceptDate = interceptDate;
    }

    public String getSubBizOrderId() {
        return subBizOrderId;
    }

    public void setSubBizOrderId(String subBizOrderId) {
        this.subBizOrderId = subBizOrderId;
    }

    public String getInterceptFailCode() {
        return interceptFailCode;
    }

    public void setInterceptFailCode(String interceptFailCode) {
        this.interceptFailCode = interceptFailCode;
    }
}
