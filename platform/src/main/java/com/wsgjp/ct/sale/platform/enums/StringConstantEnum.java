package com.wsgjp.ct.sale.platform.enums;

/**
 * 字符串统一枚举类
 *
 * <AUTHOR>
 * @date 2020-10-10 16:37
 */
public enum StringConstantEnum {
    COMMA(","),
    COLON(":"),
    BEGIN_SQUARE_BRACKETS("["),
    END_SQUARE_BRACKETS("]"),
    PAYMENT_ATYPE("000040000300006"),
    SERVICE_FEE_EXPENSE_ATYPE("000040000300199"),
    ZERO("0"),
    NULL_LOWER_CASE("null"),
    SUCCESS_CODE("200"),
    ;
    private String symbol;

    StringConstantEnum(String symbol) {
        this.symbol = symbol;
    }


    public String getSymbol() {
        return symbol;
    }
}
