package com.wsgjp.ct.sale.platform.dto.order;

/**
 * <AUTHOR>
 * 订购的应用实体
 */
public class SubscribeApplication {
    private int key;
    /**
     * 应用名
     */
    private String appName;
    /**
     * 应用描述
     */
    private String description;
    /**
     * 应用订购链接
     */
    private String orderLinkUrl;

    public SubscribeApplication() {
    }

    public SubscribeApplication(int key, String appName, String description) {
        this.key = key;
        this.appName = appName;
        this.description = description;
    }

    public SubscribeApplication(int key, String appName, String description, String orderLinkUrl) {
        this.key = key;
        this.appName = appName;
        this.description = description;
        this.orderLinkUrl = orderLinkUrl;
    }

    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOrderLinkUrl() {
        return orderLinkUrl;
    }

    public void setOrderLinkUrl(String orderLinkUrl) {
        this.orderLinkUrl = orderLinkUrl;
    }
}
