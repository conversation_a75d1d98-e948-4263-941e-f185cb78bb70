package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CyclePurchaseSubOrder {
    /**
     * 原单明细对应oid,找关系
     */
    private String parentOid;
    private int currentPeriodNum;

    /**
     * 周期购子单真实oid,发货等流程需要
     */
    private String oid;

    private TradeStatus subTradeStatus;

    private ReturnState subRefundStatus;

    private Date planSendTime;
    // 预计签收/送达时间
    private Date planSignTime;
    private Date actualSendTime;
    private Date planPrepareTime;
    private Date latestSendTime;

    private ReceiverInfo receiverInfo;
    private BigDecimal planSendQty;

    private List<MarkData> markDataList;
    /**
     * 物流公司名称
     */
    private String freightName;
    /**
     * 物流编码
     */
    private String freightCode;
    /**
     * 多物流公司
     */
    private List<EshopSaleOrderFreight> freightList;

    /**
     * 物流单号
     */
    private String freightBillNo;

    public Date getPlanSignTime() {
        return planSignTime;
    }

    public void setPlanSignTime(Date planSignTime) {
        this.planSignTime = planSignTime;
    }

    public String getParentOid() {
        return parentOid;
    }

    public void setParentOid(String parentOid) {
        this.parentOid = parentOid;
    }

    public int getCurrentPeriodNum() {
        return currentPeriodNum;
    }

    public void setCurrentPeriodNum(int currentPeriodNum) {
        this.currentPeriodNum = currentPeriodNum;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public TradeStatus getSubTradeStatus() {
        return subTradeStatus;
    }

    public void setSubTradeStatus(TradeStatus subTradeStatus) {
        this.subTradeStatus = subTradeStatus;
    }

    public ReturnState getSubRefundStatus() {
        return subRefundStatus;
    }

    public void setSubRefundStatus(ReturnState subRefundStatus) {
        this.subRefundStatus = subRefundStatus;
    }

    public Date getPlanSendTime() {
        return planSendTime;
    }

    public void setPlanSendTime(Date planSendTime) {
        this.planSendTime = planSendTime;
    }

    public Date getPlanPrepareTime() {
        return planPrepareTime;
    }

    public void setPlanPrepareTime(Date planPrepareTime) {
        this.planPrepareTime = planPrepareTime;
    }

    public ReceiverInfo getReceiverInfo() {
        return receiverInfo;
    }

    public void setReceiverInfo(ReceiverInfo receiverInfo) {
        this.receiverInfo = receiverInfo;
    }

    public BigDecimal getPlanSendQty() {
        if (planSendQty == null) {
            planSendQty = BigDecimal.ONE;
        }
        return planSendQty;
    }

    public void setPlanSendQty(BigDecimal planSendQty) {
        this.planSendQty = planSendQty;
    }

    public Date getActualSendTime() {
        return actualSendTime;
    }

    public void setActualSendTime(Date actualSendTime) {
        this.actualSendTime = actualSendTime;
    }

    public List<MarkData> getMarkDataList() {
        if (markDataList == null) {
            markDataList = new ArrayList<>();
        }
        return markDataList;
    }

    public void setMarkDataList(List<MarkData> markDataList) {
        this.markDataList = markDataList;
    }

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getFreightCode() {
        return freightCode;
    }

    public void setFreightCode(String freightCode) {
        this.freightCode = freightCode;
    }

    public String getFreightBillNo() {
        return freightBillNo;
    }

    public void setFreightBillNo(String freightBillNo) {
        this.freightBillNo = freightBillNo;
    }

    public List<EshopSaleOrderFreight> getFreightList() {
        return freightList;
    }

    public void setFreightList(List<EshopSaleOrderFreight> freightList) {
        this.freightList = freightList;
    }

    public Date getLatestSendTime() {
        return latestSendTime;
    }

    public void setLatestSendTime(Date latestSendTime) {
        this.latestSendTime = latestSendTime;
    }
}
