package com.wsgjp.ct.sale.platform.dto.refund.enums;

import bf.datasource.typehandler.CodeEnum;

public enum PlatformSignState implements CodeEnum {
    UNKNOWN(0,"未知"),
    BUYER_NOT_RECEIVED(1,"未收货"),
    BUYER_RECEIVED(2,"已收货")
    ;

    private final int flag;

    private final String name;

    PlatformSignState(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }
    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }
}
