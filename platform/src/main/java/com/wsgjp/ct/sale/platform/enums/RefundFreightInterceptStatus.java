package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum RefundFreightInterceptStatus implements CodeEnum{
    NONE(0,"无"),
    INTERCEPT_ING(1,"拦截中"),
    INTERCEPT_SUCCEED(2,"拦截成功"),
    INTERCEPT_FAIL(3,"拦截失败");
    private int code;
    private String name;

    RefundFreightInterceptStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
