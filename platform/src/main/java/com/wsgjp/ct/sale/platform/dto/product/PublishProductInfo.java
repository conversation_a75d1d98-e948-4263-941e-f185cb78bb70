package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.publish.ProtectDaysUnitEnum;
import com.wsgjp.ct.sale.platform.enums.PublishSchemaType;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * 发布商品时需要的商品信息
 *
 * <AUTHOR>
 */
public class PublishProductInfo {
    /**
     * 申请编号
     */
    private String applyId;
    /**
     * 商品编号（不能为空）
     * 修改商品不能为空
     */
    private String platformNumId;
    /**
     * 标题
     */
    private String title;
    /**
     * 短标题
     */
    private String titleShort;
    /**
     * 产地
     */
    private String area;
    /**
     * 型号
     */
    private String ptypeType;

    /**
     * 第一分类ID（三级分类）
     */
    private String categoryId;
    /**
     * 类目名称
     */
    private String categoryName;
    /**
     * 商品条码(UPC编码)
     */
    private String barcode;
    /**
     * 品牌ID
     */
    private String brandId;

    private String brandName;
    /**
     * 商品编码(itemNum)
     */
    private String xcode;

    /**
     * PC端代码录入的商品介绍（敏感标签将会被自动过滤，比如：a、script）
     */
    private String descriptionPc;

    /**
     * Mobile端代码录入的商品介绍
     */
    private String descriptionApp;
    /**
     * 保质期天
     */
    private Integer protectDays;

    /**
     * 保质期单位
     */
    private ProtectDaysUnitEnum protectDaysUnit;
    /**
     * 初始库存
     */
    private BigDecimal initStock;
    /**
     * 是否是有属性商品
     */
    private boolean hasProperties;
    /**
     * 含税价
     */
    private BigDecimal taxedPrice;
    /**
     * 不含税价
     */
    private BigDecimal price;
    /**
     * 折后含税价
     */
    private BigDecimal disedTaxedPrice;
    /**
     * 原价
     */
    private BigDecimal initialPrice;
    /**
     * 供应商价(采购价)
     */
    private BigDecimal supplyPrice;
    /**
     * 长度(单位：毫米)
     */
    private BigDecimal length;

    /**
     * 宽度(单位：毫米，含包装)
     */
    private BigDecimal width;
    /**
     * 高度(单位：毫米，含包装)
     */
    private BigDecimal height;
    /**
     * 商品重量(单位：千克，含包装)
     */
    private BigDecimal weight;
    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 存储主商品级别的属性
     * 如规格参数、扩展属性
     */
    private List<ProductPropItem> propItems;
    /**
     * 商品sku信息
     */
    private List<PublishSkuInfo> skuInfos;

    /**
     * 主商品图片信息
     */
    private List<ProductPicInfo> picInfos;
    /**
     * 扩展字段
     */
    private String attributes;
    private List<EshopProductPublishSchema> eshopProductPublishSchemas;

    /**
     * 是否需要检查审核状态
     */
    private boolean needAudit;

    // ==============以下为平台特殊字段===============

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public List<ProductPropItem> getPropItems() {
        if (propItems == null) {
            propItems = new ArrayList<>();
        }
        return propItems;
    }

    public void setPropItems(List<ProductPropItem> propItems) {
        this.propItems = propItems;
    }

    public List<PublishSkuInfo> getSkuInfos() {
        return skuInfos;
    }

    public void setSkuInfos(List<PublishSkuInfo> skuInfos) {
        this.skuInfos = skuInfos;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleShort() {
        return titleShort;
    }

    public void setTitleShort(String titleShort) {
        this.titleShort = titleShort;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getPtypeType() {
        return ptypeType;
    }

    public void setPtypeType(String ptypeType) {
        this.ptypeType = ptypeType;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getDescriptionPc() {
        return descriptionPc;
    }

    public void setDescriptionPc(String descriptionPc) {
        this.descriptionPc = descriptionPc;
    }

    public String getDescriptionApp() {
        return descriptionApp;
    }

    public void setDescriptionApp(String descriptionApp) {
        this.descriptionApp = descriptionApp;
    }

    public Integer getProtectDays() {
        return protectDays;
    }

    public void setProtectDays(Integer protectDays) {
        this.protectDays = protectDays;
    }

    public ProtectDaysUnitEnum getProtectDaysUnit() {
        return protectDaysUnit;
    }

    public void setProtectDaysUnit(ProtectDaysUnitEnum protectDaysUnit) {
        this.protectDaysUnit = protectDaysUnit;
    }

    public BigDecimal getInitStock() {
        if (null == initStock) {
            initStock = BigDecimal.ZERO;
        }
        return initStock;
    }

    public void setInitStock(BigDecimal initStock) {
        this.initStock = initStock;
    }

    public boolean isHasProperties() {
        return hasProperties;
    }

    public void setHasProperties(boolean hasProperties) {
        this.hasProperties = hasProperties;
    }

    public BigDecimal getTaxedPrice() {
        return taxedPrice;
    }

    public void setTaxedPrice(BigDecimal taxedPrice) {
        this.taxedPrice = taxedPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getDisedTaxedPrice() {
        return disedTaxedPrice;
    }

    public void setDisedTaxedPrice(BigDecimal disedTaxedPrice) {
        this.disedTaxedPrice = disedTaxedPrice;
    }

    public BigDecimal getInitialPrice() {
        if (null == initialPrice) {
            initialPrice = BigDecimal.ZERO;
        }
        return initialPrice;
    }

    public void setInitialPrice(BigDecimal initialPrice) {
        this.initialPrice = initialPrice;
    }

    public BigDecimal getSupplyPrice() {
        return supplyPrice;
    }

    public void setSupplyPrice(BigDecimal supplyPrice) {
        this.supplyPrice = supplyPrice;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public List<ProductPicInfo> getPicInfos() {
        return picInfos;
    }

    public void setPicInfos(List<ProductPicInfo> picInfos) {
        this.picInfos = picInfos;
    }

    public List<EshopProductPublishSchema> getEshopProductPublishSchemas() {
        return eshopProductPublishSchemas;
    }

    public void setEshopProductPublishSchemas(List<EshopProductPublishSchema> eshopProductPublishSchemas) {
        this.eshopProductPublishSchemas = eshopProductPublishSchemas;
    }

    public boolean isNeedAudit() {
        return needAudit;
    }

    public void setNeedAudit(boolean needAudit) {
        this.needAudit = needAudit;
    }

    public static class EshopProductPublishSchema {
        private BigInteger id;
        private BigInteger profileId;
        private BigInteger basePublishId;
        private BigInteger publishId;
        private ShopType shopType;
        private PublishSchemaType schemaType;
        private String categoryId;
        private String schemaData;

        public BigInteger getId() {
            return id;
        }

        public void setId(BigInteger id) {
            this.id = id;
        }

        public BigInteger getProfileId() {
            return profileId;
        }

        public void setProfileId(BigInteger profileId) {
            this.profileId = profileId;
        }

        public BigInteger getBasePublishId() {
            return basePublishId;
        }

        public void setBasePublishId(BigInteger basePublishId) {
            this.basePublishId = basePublishId;
        }

        public BigInteger getPublishId() {
            return publishId;
        }

        public void setPublishId(BigInteger publishId) {
            this.publishId = publishId;
        }

        public ShopType getShopType() {
            return shopType;
        }

        public void setShopType(ShopType shopType) {
            this.shopType = shopType;
        }

        public PublishSchemaType getSchemaType() {
            return schemaType;
        }

        public void setSchemaType(PublishSchemaType schemaType) {
            this.schemaType = schemaType;
        }

        public String getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(String categoryId) {
            this.categoryId = categoryId;
        }

        public String getSchemaData() {
            return schemaData;
        }

        public void setSchemaData(String schemaData) {
            this.schemaData = schemaData;
        }


    }
}
