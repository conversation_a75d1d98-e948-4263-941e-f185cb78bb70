package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

import java.util.Arrays;
import java.util.stream.Collectors;


public enum RefundPayTypeEnum implements CodeEnum {
    ALL(-1, "全部"),
    WX_PAY(0, "微信支付"),
    OFFLINE_PAY(1, "线下支付"),
    OTHER_ACCOUNT_PAY(2, "其它账户支付"),
    @Deprecated
    BALANCE_PAY(3, "余额支付"),
    /**
     * 这个支付方式比较特殊，暂时由小程序自行判断
     **/
    WEIGH_PAY(4, "称重支付"),
    PAY_ON_DELIVERY(5, "货到付款"),
    MONTH_PAY(6, "月结"),
    //将余额支付 拆分称 预付款余额支付 和 会员储值支付，这样就不用下单的时候再去判断了
    BTYPE_BALANCE_PAY(7, "预付款余额支付"),
    VIP_SCORE_PAY(8, "会员储值支付"),
    ;

    private int flag;

    private String name;

    RefundPayTypeEnum(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }


    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public static RefundPayTypeEnum getEnum(int code) {
        return Arrays.stream(RefundPayTypeEnum.values()).filter(item -> {
            return item.getCode() == code;
        }).collect(Collectors.toList()).get(0);
    }
}