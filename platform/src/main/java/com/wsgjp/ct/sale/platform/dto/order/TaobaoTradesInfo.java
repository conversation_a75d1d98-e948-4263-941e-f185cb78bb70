package com.wsgjp.ct.sale.platform.dto.order;

import com.taobao.api.domain.Trade;

import java.util.ArrayList;
import java.util.List;

public class TaobaoTradesInfo {
    private List<Trade> tradeList;
    private int totalRecords;

    public List<Trade> getTradeList() {
        if (tradeList == null) {
            tradeList = new ArrayList<>();
        }
        return tradeList;
    }

    public void setTradeList(List<Trade> tradeList) {
        this.tradeList = tradeList;
    }

    public int getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(int totalRecords) {
        this.totalRecords = totalRecords;
    }
}
