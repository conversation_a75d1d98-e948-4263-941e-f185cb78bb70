package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum SyncType implements CodeEnum {
    /**
     * 整单同步
     */
    WHOLE(0, "整单同步"),
    SPLIT(1, "拆分同步"),
    GIFT(2, "赠品同步"),
    REFUND(3, "售后同步"),
    SPLIT_MERGE(4, "拆合同步"),
    DOT_SYNC(5, "无需同步"),
    RESEND(6, "修改同步"),
    SAME_CITY(7,"同城配送"),
    SELF_PICK(8,"线下自提"),
    VIRTUAL_SEND(9,"虚拟发货"),
    APPEND_SEND(10,"追加包裹发货");
    private final int code;
    private final String desc;

    SyncType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
