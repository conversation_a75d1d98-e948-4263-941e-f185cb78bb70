package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2023/11/17 10:57
 */
public enum PlatformBizTypeEnum implements CodeEnum {
    /**
     * 平台操作业务类型，用于记录日志
     */
    SYNC_STOCK(1, "同步库存"),
    SEND_GOODS(2, "同步物流单号"),
    ERROR(3, "错误排查记录"),
    REFUND_AUDIT(4,"售后审核");


    private final String name;
    private final int code;

    PlatformBizTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String toString(){
        return name();
    }
}
