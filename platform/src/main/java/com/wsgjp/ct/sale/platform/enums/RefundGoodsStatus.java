package com.wsgjp.ct.sale.platform.enums;
/**
 * <AUTHOR>
 * @date 20120-01-11 17:02
 */
public enum RefundGoodsStatus {

    /**
     * 错误状态
     */
    ERROR_STATUS(-1,"错误状态"),

    /**
     * 买家未收到货
     */
    BUYER_NOT_RECEIVED(0,"买家未收到货"),

    /**
     * 买家已收到货
     */
    BUYER_RECEIVED(1,"买家已收到货"),
    /**
     * 买家已退货
     */
    BUYER_RETURNED_GOODS(2, "买家已退货"),
    /**
     * 卖家已收货
     */
    SELLER_RECEIVED(3, "卖家已收货");

    private int flag;

    private String name;

    RefundGoodsStatus(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }
}
