package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2019-12-18 18:12
 */
public enum RefundPhase implements CodeEnum {
    /**
     * 未发货-售中
     */
    ON_SALE(0, "未发货-售中"),
    /**
     * 已发货-售后
     */
    AFTER_SALE(1, "已发货-售后");

    private int flag;

    private String name;

    RefundPhase(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return this.flag;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
