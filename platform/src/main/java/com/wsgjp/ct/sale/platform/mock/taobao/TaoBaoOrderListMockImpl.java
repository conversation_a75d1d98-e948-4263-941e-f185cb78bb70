package com.wsgjp.ct.sale.platform.mock.taobao;

import com.taobao.api.domain.Trade;
import com.taobao.api.request.TradesSoldGetRequest;
import com.taobao.api.request.TradesSoldIncrementGetRequest;
import com.taobao.api.response.TradeFullinfoGetResponse;
import com.taobao.api.response.TradesSoldGetResponse;
import com.wsgjp.ct.sale.platform.entity.request.mock.QueryDataListParam;
import com.wsgjp.ct.sale.platform.entity.request.other.MockRequest;
import com.wsgjp.ct.sale.platform.enums.MockQueryType;
import com.wsgjp.ct.sale.platform.mock.ApiMockQueryService;
import com.wsgjp.ct.sale.platform.mock.ApiMockerService;
import com.wsgjp.ct.sale.platform.utils.MockUtils;
import ngp.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 2024/5/29 11:17
 */
@Service
public class TaoBaoOrderListMockImpl implements ApiMockerService {

    private final ApiMockQueryService queryService;

    public TaoBaoOrderListMockImpl(ApiMockQueryService queryService) {
        this.queryService = queryService;
    }

    @Override
    public Object queryData(MockRequest request) {
        TradesSoldGetResponse response=new TradesSoldGetResponse();
        QueryDataListParam param = MockUtils.initParam(request);
        param.setStateList(request.getStateList());
        param.setType(MockQueryType.Order);
        long dataCount = queryService.queryDataCount(param);
        response.setTotalResults(dataCount);
        if(dataCount>0){
            List<Trade> trades=new ArrayList<>();
            List<String> list = queryService.queryDataList(param);
            for(String item: list){
                TradeFullinfoGetResponse tradeResponse = MockUtils.doBuildTbTrade(item);
                trades.add(tradeResponse.getTrade());
            }
            response.setTrades(trades);
        }
        return response;
    }

    @Override
    public String methodName() {
        return "taobao.trades.sold.get";
    }

    @Override
    public void buildRequest(MockRequest request, Object[] args) {
        try {
            if(args[0] instanceof TradesSoldGetRequest){
                TradesSoldGetRequest tradesRequest= (TradesSoldGetRequest) args[0];
                request.setIncrease(false);
                request.setEndTime(tradesRequest.getEndCreated());
                request.setBeginTime(tradesRequest.getStartCreated());
                if(StringUtils.isNotEmpty(tradesRequest.getStatus())){
                    request.setStateList(Collections.singletonList(tradesRequest.getStatus()));
                }
                request.setPageSize(tradesRequest.getPageSize().intValue());
                request.setPageIndex(tradesRequest.getPageNo().intValue());
                return;
            }
            if(args[0] instanceof TradesSoldIncrementGetRequest){
                TradesSoldIncrementGetRequest increaseRequest= (TradesSoldIncrementGetRequest) args[0];
                request.setIncrease(true);
                request.setEndTime(increaseRequest.getEndModified());
                request.setBeginTime(increaseRequest.getStartModified());
                request.setPageSize(increaseRequest.getPageSize().intValue());
                request.setPageIndex(increaseRequest.getPageNo().intValue());
            }

            throw new RuntimeException("淘宝mock下载订单请求参数类型不对");
        }catch (Exception ex){
            throw new RuntimeException("售后列表下载，请求参数类型不正确！");
        }
    }
}
