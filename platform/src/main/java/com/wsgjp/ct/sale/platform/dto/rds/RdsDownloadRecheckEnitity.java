package com.wsgjp.ct.sale.platform.dto.rds;

public class RdsDownloadRecheckEnitity {
    /**
     * 是否开启自动订阅
     */
    private Boolean rdsDownloadRecheckEnabled;
    /**
     * 设置距离上次下单成功后多久调用接口下单
     */
    private Long rdsRecheckDuration;

    /**
     * 设置排除所处时间检查漏单下载
     * @return
     */
    private ExcludeTime rdsRecheckExcludeTimeSection;

    public ExcludeTime getRdsRecheckExcludeTimeSection() {
        return rdsRecheckExcludeTimeSection;
    }

    public void setRdsRecheckExcludeTimeSection(ExcludeTime rdsRecheckExcludeTimeSection) {
        this.rdsRecheckExcludeTimeSection = rdsRecheckExcludeTimeSection;
    }

    public Boolean getRdsDownloadRecheckEnabled() {
        return rdsDownloadRecheckEnabled;
    }

    public void setRdsDownloadRecheckEnabled(Boolean rdsDownloadRecheckEnabled) {
        this.rdsDownloadRecheckEnabled = rdsDownloadRecheckEnabled;
    }

    public Long getRdsRecheckDuration() {
        return rdsRecheckDuration;
    }

    public void setRdsRecheckDuration(Long rdsRecheckDuration) {
        this.rdsRecheckDuration = rdsRecheckDuration;
    }

    public static class ExcludeTime{
        private String startTime;
        private String endTime;

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }
    }


}
