package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum RefundProcessStatus implements CodeEnum {

    APPROVE(0,"同意售后"),
    REJECT(1,"拒绝售后");

    RefundProcessStatus(int code,String name){
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
