package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum EshopPlatformConfigType implements CodeEnum {
    /**
     * 按照1,2,4,8,16按位递增的顺序赋值
     */
    CustomerAddressModify(1, "自助改地址"),
    AG(2, "AG");

    private int code;
    private String name;

    EshopPlatformConfigType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
