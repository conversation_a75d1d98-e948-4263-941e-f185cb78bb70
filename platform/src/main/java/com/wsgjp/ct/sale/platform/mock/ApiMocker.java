package com.wsgjp.ct.sale.platform.mock;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;

import java.lang.reflect.Method;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ApiMocker {
    /**
     * 执行mocker
     * @param args 参数
     * @param method 请求方法
     * @return 返回mocker数据
     */
    Object execute(Object[] args, Method method, EshopSystemParams params);

    /**
     *  返回 mocker支持的店铺类型
     * @return mocker支持的店铺类型
     */
    List<ShopType> getShopTypes();
}
