package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;

public enum OrderDownloadResultEnum implements CodeEnum {
    SUCCESS(0, "成功", 0, 0),
    NEED_RETRY_FAST_ERROR(1, "平台返回失败、希望业务尽快重试", 2, 3),
    API_LIMIT_ERROR(3, "平台限流", 10, 1),
    AUTH_EXPIRED_ERROR(4, "授权过期", 60, 3),
    SYSTEM_ERROR(5, "系统内部异常", 10, 1),
    ABNORMAL_RESPONSE_ERROR(6, "接口响应异常", 60, 3),
    //（默认）
    OTHER_ERROR(9, "其他异常", 60, 1),
    //不需要重试的错误信息：比如参数不正确，验签不通过等
    NOT_RETRY_ERROR(10, "其他异常", 0, 0);

    private final int code;
    private final String desc;
    //重试时间间隔，单位分钟
    private final int retryInterval;

    //重试次数
    private final int retryTimes;

    private OrderDownloadResultEnum(int code, String name, int retryInterval, int retryTimes) {
        this.code = code;
        this.desc = name;
        this.retryInterval = 0;
        this.retryTimes = 0;
    }

    public String getDesc() {
        return desc;
    }

    public int getRetryInterval() {
        return retryInterval;
    }

    public int getRetryTimes() {
        return retryTimes;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return CodeEnum.super.getName();
    }
}
