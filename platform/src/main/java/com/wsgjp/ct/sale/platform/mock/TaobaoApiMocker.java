package com.wsgjp.ct.sale.platform.mock;

import com.taobao.api.TaobaoRequest;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.entity.request.other.MockRequest;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class <PERSON><PERSON>ApiMocker implements ApiMocker {

    @Override
    public Object execute(Object[] args, Method method, EshopSystemParams params) {
        TaobaoRequest request = (TaobaoRequest) args[0];
        String apiMethodName = request.getApiMethodName();
        ApiMockerService service = MockServiceManager.getMockerService(apiMethodName);
        MockRequest mockRequest=new MockRequest();
        mockRequest.setShopAccount(params.getShopAccount());
        service.buildRequest(mockRequest, args);
        return service.queryData(mockRequest);
    }

    @Override
    public List<ShopType> getShopTypes() {
        List<ShopType> types = new ArrayList<>(2);
        types.add(ShopType.TaoBao);
        types.add(ShopType.Tmall);
        return types;
    }

}
