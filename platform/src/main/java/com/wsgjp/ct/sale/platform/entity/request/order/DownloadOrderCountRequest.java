package com.wsgjp.ct.sale.platform.entity.request.order;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import io.swagger.annotations.ApiModel;

import java.util.Date;

/**
 * <AUTHOR> 2023/11/22 9:18
 */
@ApiModel("订单数量下载实体")
public class DownloadOrderCountRequest extends BaseRequest {
    public Date begin;
    public Date end;
    //检查类型，0常态检查过去几天数据，1检查当天固定小时数据
    public int checkOrderType;

    public int getCheckOrderType() {
        return checkOrderType;
    }

    public void setCheckOrderType(int checkOrderType) {
        this.checkOrderType = checkOrderType;
    }

    public Date getBegin() {
        return begin;
    }

    public void setBegin(Date begin) {
        this.begin = begin;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }
}
