package com.wsgjp.ct.sale.platform.dto.order.entity;

import com.wsgjp.ct.sale.common.enums.eshoporder.QcResultType;

/**
 * <AUTHOR>
 */
public class QualityControlInfo {
    private QcResultType status;

    private String description;

    private String pictures;

    private Integer confirmStatus;

    public Integer getConfirmStatus() {
        return confirmStatus;
    }

    public void setConfirmStatus(Integer confirmStatus) {
        this.confirmStatus = confirmStatus;
    }

    public QcResultType getStatus() {
        return status;
    }

    public void setStatus(QcResultType status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPictures() {
        return pictures;
    }

    public void setPictures(String pictures) {
        this.pictures = pictures;
    }
}
