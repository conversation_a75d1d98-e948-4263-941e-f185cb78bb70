package com.wsgjp.ct.sale.platform.enums;

import bf.datasource.typehandler.CodeEnum;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillBusinessType;

public enum BusinessType implements CodeEnum {
    /**
     * 业务类型：0直营1分销2代运营3加盟
     */
    ALL(-1,"全部"),
    ZHIYING(0,"自营业务"),
    DAIXIAO(1,"代销业务"),
    DAIFA(2,"分销业务"),
    SHUADAN(3,"刷单业务"),
    DAIYUNYIN(4,"代运营业务");

    private int code;
    private String name;

    BusinessType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public BillBusinessType businessTypeToBill(){
        switch (this){
            case ALL:
                return BillBusinessType.None;
            case ZHIYING:
                return BillBusinessType.SaleNormal;
            case DAIXIAO:
                return BillBusinessType.SaleProxy;
            case DAIFA:
                return BillBusinessType.SaleDistribution;
            case SHUADAN:
                return BillBusinessType.EshopClickFarm;
            case DAIYUNYIN:
                return BillBusinessType.None;
            default:
                return BillBusinessType.None;
        }
    }
}
