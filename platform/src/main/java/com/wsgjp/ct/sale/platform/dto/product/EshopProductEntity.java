package com.wsgjp.ct.sale.platform.dto.product;

import com.wsgjp.ct.common.enums.core.enums.ProductMarkEnum;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.ErpBaseInfo;
import com.wsgjp.ct.sale.platform.enums.StockState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import ngp.utils.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 主商品实体
 */
@ApiModel("网店商品实体")
public class EshopProductEntity extends ErpBaseInfo {
    private Date modifyTime;
    private Date createTime;

    public EshopProductEntity() {
    }

    public EshopProductEntity(EshopSystemParams systemParams) {
        this.setProfileId(systemParams.getProfileId());
        this.setEshopId(systemParams.geteShopId());
        this.setShopType(systemParams.getShopType());
    }

    /**
     * 主商品ID
     */
    @ApiModelProperty("主商品ID")
    private String numId;
    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;
    /**
     * 商品默认skuId
     */
    @ApiModelProperty("商品默认skuId")
    private String defaultSkuId;
    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String xcode;
    /**
     * 商品条码
     */
    @ApiModelProperty("商品条码")
    private String barcode;
    /**
     * 商品单价
     */
    @ApiModelProperty("商品单价")
    private BigDecimal price;
    /**
     * 零售单价
     */
    @ApiModelProperty("零售单价")
    private BigDecimal retailPrice;
    /**
     * 商品上下架状态
     */
    @ApiModelProperty("商品上下架状态")
    private StockState status;
    /**
     * 类目id TODO 区别
     */
    @ApiModelProperty("类目id")
    private String classId;
    @ApiModelProperty("卖家分类ID")
    private String sellerClassId;
    /**
     * 类目名称 TODO 区别
     */
    @ApiModelProperty("类目名称")
    private String className;

    //商家自定义分类
    @ApiModelProperty("商家自定义分类")
    private String sellerClassName;
    /**
     * 商品图片
     */
    @ApiModelProperty("商品图片")
    private String picUrl;
    /**
     * 商品仓库编码
     */
    @ApiModelProperty("商品仓库编码")
    private String storeCode;
    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private BigDecimal qty;
    /**
     * 属性别名
     */
    @ApiModelProperty("属性别名")
    private String propAlias;
    /**
     * 往来单位名称
     */
    @ApiModelProperty("往来单位名称")
    private String btypeName;
    /**
     * 往来单位编码
     */
    @ApiModelProperty("往来单位编码")
    private String btypeCode;
    /**
     * 商品sku信息
     */
    @ApiModelProperty("商品sku信息")
    private List<EshopSkuEntity> skuList;

    /**
     * 商品对应的门店
     */
    private String warehouseCode;

    @ApiModelProperty("网店商品单位id")
    private String defaultPlatformUnitId;
    @ApiModelProperty("网店商品单位名称")
    private String defaultPlatformUnitName;

    public List<EshopSkuEntity> getSkuList() {
        if (CollectionUtils.isEmpty(skuList)) {
            skuList = new ArrayList<>();
        }
        return skuList;
    }

    /**
     * 商品标记
     */
    private List<ProductMarkEnum> productMarkEnumList;

    public void setSkuList(List<EshopSkuEntity> skuList) {
        this.skuList = skuList;
    }

    public String getNumId() {
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getProductName() {
        if(productName==null){
            productName = "";
        }
        if(productName.length() > 500){
            productName = productName.substring(0, 499);
        }
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDefaultSkuId() {
        if(defaultSkuId == null){
            defaultSkuId = "";
        }
        return defaultSkuId;
    }

    public void setDefaultSkuId(String defaultSkuId) {
        this.defaultSkuId = defaultSkuId;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public BigDecimal getPrice() {
        if (price == null) {
            return BigDecimal.ZERO;
        }
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getRetailPrice() {
        if (retailPrice == null) {
            return BigDecimal.ZERO;
        }
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public String getClassId() {
        return classId;
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getSellerClassId() {
        if(sellerClassId==null){
            sellerClassId="";
        }
        return sellerClassId;
    }

    public void setSellerClassId(String sellerClassId) {
        this.sellerClassId = sellerClassId;
    }

    public String getSellerClassName() {
        return sellerClassName;
    }

    public void setSellerClassName(String sellerClassName) {
        this.sellerClassName = sellerClassName;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public StockState getStatus() {
        return status;
    }

    public void setStatus(StockState status) {
        this.status = status;
    }

    public BigDecimal getQty() {
        if (qty == null) {
            return BigDecimal.ZERO;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getPropAlias() {
        if(propAlias==null){
            return "";
        }
        if(propAlias.length()>400){
            return propAlias.substring(0, 399);
        }
        return propAlias;
    }

    public void setPropAlias(String propAlias) {
        this.propAlias = propAlias;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getBtypeName() {
        return btypeName;
    }

    public void setBtypeName(String btypeName) {
        this.btypeName = btypeName;
    }

    public String getBtypeCode() {
        return btypeCode;
    }

    public void setBtypeCode(String btypeCode) {
        this.btypeCode = btypeCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public List<ProductMarkEnum> getProductMarkEnumList() {
        if (productMarkEnumList == null) {
            productMarkEnumList = new ArrayList<>();
        }
        return productMarkEnumList;
    }

    public void setProductMarkEnumList(List<ProductMarkEnum> productMarkEnumList) {
        this.productMarkEnumList = productMarkEnumList;
    }

    public String getDefaultPlatformUnitId() {
        return defaultPlatformUnitId;
    }

    public void setDefaultPlatformUnitId(String defaultPlatformUnitId) {
        this.defaultPlatformUnitId = defaultPlatformUnitId;
    }

    public String getDefaultPlatformUnitName() {
        return defaultPlatformUnitName;
    }

    public void setDefaultPlatformUnitName(String defaultPlatformUnitName) {
        this.defaultPlatformUnitName = defaultPlatformUnitName;
    }
}
