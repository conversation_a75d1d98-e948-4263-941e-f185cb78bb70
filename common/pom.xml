<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sale</artifactId>
        <groupId>com.wsgjp.ct</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wsgjp.ct</groupId>
    <artifactId>common</artifactId>
    <version>${common.version}</version>
    <dependencies>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>common-enum-core</artifactId>
            <version>${common.enum.core.version}</version>
        </dependency>

        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.4</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mailapi</artifactId>
            <version>1.4.3</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>bill-core-handle</artifactId>
            <version>${bill.core.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>business-datasource</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>sale-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct.sis</groupId>
            <artifactId>sis-client</artifactId>
            <version>${sis.client.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
