package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 3/8/2020 下午 5:15
 */
public class EshopSaleOrder {
    private BigInteger profileId;
    private BigInteger id;
    private int localTradeState;
    private int localRefundState;
    private int deleted;
    private int selfDeliveryMode;
    private BigInteger otypeId;
    private String tradeId;
    private String platformParentOrderId;
    private BigInteger vchcode;
    private BigInteger submitBatchId;

    public BigInteger getVchcode() {
        return vchcode;
    }

    public String getPlatformParentOrderId() {
        return platformParentOrderId;
    }

    public void setPlatformParentOrderId(String platformParentOrderId) {
        this.platformParentOrderId = platformParentOrderId;
    }
    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public BigInteger getSubmitBatchId() {
        return submitBatchId;
    }

    public void setSubmitBatchId(BigInteger submitBatchId) {
        this.submitBatchId = submitBatchId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public int getLocalTradeState() {
        return localTradeState;
    }

    public void setLocalTradeState(int localTradeState) {
        this.localTradeState = localTradeState;
    }

    public int getDeleted() {
        return deleted;
    }

    public void setDeleted(int deleted) {
        this.deleted = deleted;
    }

    public int getLocalRefundState() {
        return localRefundState;
    }

    public void setLocalRefundState(int localRefundState) {
        this.localRefundState = localRefundState;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public int getSelfDeliveryMode() {
        return selfDeliveryMode;
    }

    public void setSelfDeliveryMode(int selfDeliveryMode) {
        this.selfDeliveryMode = selfDeliveryMode;
    }
}
