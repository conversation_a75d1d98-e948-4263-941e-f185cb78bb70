package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @description 商城类型
 */

public enum MallType implements CodeEnum {
    ALL(-1, "全部"),
    NOMARL(0, "普通"),
    SHOPMALL(1, "商城"),
    CROSS_BORDER_MALL(2,"跨境商城");


    private int index;
    private String name;

    MallType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public String toString() {
        return this.name;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public int getCode() {
        return this.index;
    }


    public static MallType getMallType( int index)
    {
        MallType[] types = MallType.values();
        for (MallType type:types) {
            if(index == type.getCode())
            {
                return type;
            }
        }
        return null;
    }
}
