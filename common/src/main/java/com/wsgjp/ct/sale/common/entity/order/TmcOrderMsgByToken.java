package com.wsgjp.ct.sale.common.entity.order;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR> 2023/11/27 11:00
 */
public class TmcOrderMsgByToken {
    private BigInteger profileId;
    /**
     * 云订货授权的token，用于查找网店
     */
    private String token;
    /**
     * 订单号
     */
    private String tradeId;
    /**
     * 售后单号（如果是售后单变更则必填）
     */
    private String refundId;

    /**
     * 消息类型，0=订单，1=售后单
     */
    private int type;
    private Date modifyTime;

    private String messageId;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getTradeId() {
        if (tradeId == null) {
            tradeId = "";
        }
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getRefundId() {
        if (refundId == null) {
            refundId = "";
        }
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
}
