package com.wsgjp.ct.sale.common.entity.eshop;

import com.wsgjp.ct.common.enums.core.enums.ShopType;

import java.math.BigInteger;

/**
 * <AUTHOR> 2023/7/24 9:40
 */
public class EshopSelectorData {
    private BigInteger otypeId;
    private BigInteger profileId;
    private ShopType eshopType;
    private String fullname;

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public ShopType getEshopType() {
        return eshopType;
    }

    public void setEshopType(ShopType eshopType) {
        this.eshopType = eshopType;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }
}
