package com.wsgjp.ct.sale.common.entity.order;

import ngp.utils.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/9/6 9:56
 */
public class TmcOrderMessage extends TmcBaseMessage {

	//是否重试
	private int retryTime;
	private String tradeId;
	private String tradeStatus;
	private String tag;

	private String refundId;

	/**
	 *希望订单变更过的时间节点
	 * 可以为空，传值则表示本次获取订单详情旨在获取最新订单信息。当从RDS获取到的订单更新时间早于该时间时，要重新从API接口获取订单详情返回
	 */
	private Date desiredTradeModifiedTime;

	private String onlineShopId;
	/**
	 * 平台消息类型
	 */
	private String platformMsgType;

	/**
	 * 保存一份带有备注(买家，卖家备注。旗帜)，订单状态，售后状态
	 */
	private String platformUpdateOrderJson;

	private int shopCode;

	public int getShopCode() {
		return shopCode;
	}

	public void setShopCode(int shopCode) {
		this.shopCode = shopCode;
	}

	public String getPlatformUpdateOrderJson() {
		return platformUpdateOrderJson;
	}

	public void setPlatformUpdateOrderJson(String platformUpdateOrderJson) {
		this.platformUpdateOrderJson = platformUpdateOrderJson;
	}

	public Date getDesiredTradeModifiedTime() {
		return desiredTradeModifiedTime;
	}

	public void setDesiredTradeModifiedTime(Date desiredTradeModifiedTime) {
		this.desiredTradeModifiedTime = desiredTradeModifiedTime;
	}

	public int getRetryTime() {
		return retryTime;
	}

	public void setRetryTime(int retryTime) {
		this.retryTime = retryTime;
	}

	public String getTradeId() {
		return tradeId;
	}

	public void setTradeId(String tradeId) {
		this.tradeId = tradeId;
	}

	public String getTradeStatus() {
		if(StringUtils.isEmpty(tradeStatus))
		{
			return "";
		}
		return tradeStatus;
	}

	public void setTradeStatus(String tradeStatus) {
		this.tradeStatus = tradeStatus;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getRefundId() {
		return refundId;
	}

	public void setRefundId(String refundId) {
		this.refundId = refundId;
	}

	public String getOnlineShopId() {
		return onlineShopId;
	}

	public void setOnlineShopId(String onlineShopId) {
		this.onlineShopId = onlineShopId;
	}

	public String getPlatformMsgType() {
		return platformMsgType;
	}

	public void setPlatformMsgType(String platformMsgType) {
		this.platformMsgType = platformMsgType;
	}
}
