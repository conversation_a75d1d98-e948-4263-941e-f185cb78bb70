package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 13/4/2021 下午 1:55
 */
public class SkuMapping {
	private String platformNumId;
	private String platformSkuId;
	private String platformXcode;
	private BigInteger skuId;
	private BigInteger ptypeId;
	private int pcategory;
	private String xcode;
	private String warehouseCode;

	public String getPlatformNumId() {
		return platformNumId;
	}

	public void setPlatformNumId(String platformNumId) {
		this.platformNumId = platformNumId;
	}

	public String getPlatformSkuId() {
		return platformSkuId;
	}

	public void setPlatformSkuId(String platformSkuId) {
		this.platformSkuId = platformSkuId;
	}

	public String getPlatformXcode() {
		return platformXcode;
	}

	public void setPlatformXcode(String platformXcode) {
		this.platformXcode = platformXcode;
	}

	public BigInteger getSkuId() {
		return skuId;
	}

	public void setSkuId(BigInteger skuId) {
		this.skuId = skuId;
	}

	public BigInteger getPtypeId() {
		return ptypeId;
	}

	public void setPtypeId(BigInteger ptypeId) {
		this.ptypeId = ptypeId;
	}

	public int getPcategory() {
		return pcategory;
	}

	public void setPcategory(int pcategory) {
		this.pcategory = pcategory;
	}

	public String getXcode() {
		return xcode;
	}

	public void setXcode(String xcode) {
		this.xcode = xcode;
	}

	public String getWarehouseCode() {
		return warehouseCode;
	}

	public void setWarehouseCode(String warehouseCode) {
		this.warehouseCode = warehouseCode;
	}
}
