package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;

public enum FileUseTypeEnum implements CodeEnum {
    MAIN_IMAGE(0,"主图"),
    LONG_IMAGE(1,"长图"),
    DETAIL_IMAGE(2,"详情图"),
    ACTIVITY_IMAGE(3,"活动图"),
    IMAGE_34(4,"3:4图");

    private int index;
    private String name;

    FileUseTypeEnum(int index,String name){
        this.index=index;
        this.name=name;
    }
    public static FileUseTypeEnum getEnumBycode(int code) {
        for (FileUseTypeEnum fileUseTypeEnum : FileUseTypeEnum.values()) {
            if (code == fileUseTypeEnum.getCode()) {
                return fileUseTypeEnum;
            }
        }
        return null;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}
