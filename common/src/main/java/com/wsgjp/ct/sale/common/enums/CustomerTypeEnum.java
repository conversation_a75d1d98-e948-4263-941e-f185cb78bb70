package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021-05-19
 **/
public enum CustomerTypeEnum implements CodeEnum {

    PRE_SALE_ORDER(1, "预售单"),
    DELIVER_BILL(0, "交易单");

    private String desc;
    private int code;

    CustomerTypeEnum(int code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public int getCode() {
        return code;
    }
}
