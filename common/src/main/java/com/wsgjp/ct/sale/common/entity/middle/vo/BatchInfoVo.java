package com.wsgjp.ct.sale.common.entity.middle.vo;

import com.wsgjp.ct.sale.common.entity.middle.pojo.BatchInfo;

import java.math.BigInteger;

public class BatchInfoVo extends BatchInfo {

    private String ktypeName;
    private Integer protectDays;
    private String propNames;
    private String propvalueNames;
    private String ptypeName;
    private String usercode;
    private Integer stockSendQty;
    private BigInteger ptypeId;

    @Override
    public BigInteger getPtypeId() {
        return ptypeId;
    }

    @Override
    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public Integer getStockSendQty() {
        return stockSendQty;
    }

    public void setStockSendQty(Integer stockSendQty) {
        this.stockSendQty = stockSendQty;
    }

    public String getPropNames() {
        return propNames;
    }

    public void setPropNames(String propNames) {
        this.propNames = propNames;
    }

    public String getPropvalueNames() {
        return propvalueNames;
    }

    public void setPropvalueNames(String propvalueNames) {
        this.propvalueNames = propvalueNames;
    }

    public String getPtypeName() {
        return ptypeName;
    }

    public void setPtypeName(String ptypeName) {
        this.ptypeName = ptypeName;
    }

    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    public String getKtypeName() {
        return ktypeName;
    }

    public void setKtypeName(String ktypeName) {
        this.ktypeName = ktypeName;
    }

    public Integer getProtectDays() {
        return protectDays;
    }

    public void setProtectDays(Integer protectDays) {
        this.protectDays = protectDays;
    }

}
