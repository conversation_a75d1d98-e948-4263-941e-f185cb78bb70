package com.wsgjp.ct.sale.common.processlogger;

/**
 * <AUTHOR>
 * @date 2019-12-10 18:22
 */
public interface ProcessLogger {
    /**
     * 追加日志信息
     * @param msg 日志信息
     * @return 日志行id
     */
    int appendMsg(String msg);

    /**
     * 修改最后一条日志信息
     * @param msg 日志信息
     */
    void modifyMsg(String msg);

    /**
     * 修改指定行号的日志信息
     * @param msg 日志
     * @param msgId 行号
     */
    void modifyMsg(String msg,int msgId);

    /**
     * 进程完毕标记
     */
    void doFinish();
}
