package com.wsgjp.ct.sale.common.entity.middle.pojo;


import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * 同步批次信息
 */
public class BatchInfo {
    private BigInteger id;
    private BigInteger profileId;
    private BigInteger ktypeId;
    private BigInteger ptypeId;
    private BigInteger skuId;
    // 次品状态
    private Integer qualityState;
    private String batchno;
    private Date produceDate;
    private Date expireDate;
    private BigDecimal qty;
    private BigDecimal subQty;
    private String batchHash;
    // 仓库存货点id（0-仓内库存  非0-仓外库存）
    private BigInteger ktypePointId;
    // 仓库存货点类型（0-仓内 非0-仓外）
    private Integer ktypePointType;
    private BigDecimal batchPrice;
    private Date createTime;
    private Date updateTime;
    private Date batchTime;
    private String picUrl;


    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public Date getBatchTime() {
        return batchTime;
    }

    public void setBatchTime(Date batchTime) {
        this.batchTime = batchTime;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public BigInteger getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public Integer getQualityState() {
        return qualityState;
    }

    public void setQualityState(Integer qualityState) {
        this.qualityState = qualityState;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public Date getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(Date produceDate) {
        this.produceDate = produceDate;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public BigDecimal getQty() {
        if (qty == null) {
            return BigDecimal.ZERO;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getSubQty() {
        if (subQty == null) {
            return BigDecimal.ZERO;
        }
        return subQty;
    }

    public void setSubQty(BigDecimal subQty) {
        this.subQty = subQty;
    }

    public String getBatchHash() {
        return batchHash;
    }

    public void setBatchHash(String batchHash) {
        this.batchHash = batchHash;
    }

    public BigInteger getKtypePointId() {
        return ktypePointId;
    }

    public void setKtypePointId(BigInteger ktypePointId) {
        this.ktypePointId = ktypePointId;
    }

    public Integer getKtypePointType() {
        return ktypePointType;
    }

    public void setKtypePointType(Integer ktypePointType) {
        this.ktypePointType = ktypePointType;
    }

    public BigDecimal getBatchPrice() {
        if (batchPrice == null) {
            return BigDecimal.ZERO;
        }
        return batchPrice;
    }

    public void setBatchPrice(BigDecimal batchPrice) {
        this.batchPrice = batchPrice;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
