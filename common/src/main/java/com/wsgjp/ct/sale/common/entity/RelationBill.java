package com.wsgjp.ct.sale.common.entity;


import com.wsgjp.ct.sale.common.enums.BillCreateType;
import com.wsgjp.ct.sale.common.enums.VchtypeEnum;

import java.math.BigInteger;
import java.util.Date;

/**
 * @Author: lailai
 * @Date: 2020-07-04 11:35
 */
public class RelationBill {
    private BigInteger ruleId;
    private String number;
    private Integer vchtype;
    private Integer customType;
    private Integer status;
    private BigInteger detailId;
    private BigInteger vchcode;
    private String fields;
    private Integer businessType;
    private String deleErrorMsg;
    private String modErrorMsg;
    private String vchtypeName;
    private VchtypeEnum vchtypeEnum;
    private Integer processType;
    private Date date;
    private String createTypeName;
    private BillCreateType createType;
    private Integer postState;
    private BigInteger sourceId;
    private Boolean showDetail = true;
    private Integer intPostState;
    private Boolean eshopBill = false;

    public String getModErrorMsg() {
        return modErrorMsg;
    }

    public void setModErrorMsg(String modErrorMsg) {
        this.modErrorMsg = modErrorMsg;
    }

    public String getDeleErrorMsg() {
        return deleErrorMsg;
    }

    public void setDeleErrorMsg(String deleErrorMsg) {
        this.deleErrorMsg = deleErrorMsg;
    }

    public BigInteger getRuleId() {
        return ruleId;
    }

    public void setRuleId(BigInteger ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getVchtype() {
        return vchtype;
    }

    public void setVchtype(Integer vchtype) {
        this.vchtype = vchtype;
    }

    public String getNumber() {
        return number;
    }

    public BigInteger getSourceId() {
        return sourceId;
    }

    public Boolean getShowDetail() {
        return showDetail;
    }

    public void setShowDetail(Boolean showDetail) {
        this.showDetail = showDetail;
    }

    public void setSourceId(BigInteger sourceId) {
        this.sourceId = sourceId;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public BigInteger getDetailId() {
        return detailId;
    }

    public void setDetailId(BigInteger detailId) {
        this.detailId = detailId;
    }

    public Integer getProcessType() {
        return processType;
    }

    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getPostState() {
        return postState;
    }

    public void setPostState(Integer postState) {
        this.postState = postState;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public String getFields() {
        return fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }

    public VchtypeEnum getVchtypeEnum() {
        return vchtypeEnum;
    }

    public void setVchtypeEnum(VchtypeEnum vchtypeEnum) {
        this.vchtypeEnum = vchtypeEnum;
    }

    public String getVchtypeName() {
        return vchtypeName;
    }

    public BillCreateType getCreateType() {
        return createType;
    }

    public void setCreateType(BillCreateType createType) {
        this.createType = createType;
    }

    public String getCreateTypeName() {
        return createTypeName;
    }

    public void setCreateTypeName(String createTypeName) {
        this.createTypeName = createTypeName;
    }

    public void setVchtypeName(String vchtypeName) {
        this.vchtypeName = vchtypeName;
    }

    public RelationBill() {
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public RelationBill(Integer vchtype, Integer businessType, BigInteger vchcode, BigInteger ruleId) {
        this.vchtype = vchtype;
        this.vchcode = vchcode;
        this.businessType = businessType;
        this.ruleId = ruleId;

    }

    @Override
    public String toString() {
        return String.format("RelationBill{number='%s', vchtype=%d, status=%d, vchcode=%s, fields='%s', " +
                "deleErrorMsg='%s', modErrorMsg='%s', vchtypeName='%s', vchtypeEnum=%s, processType=%d, date=%s, " +
                "createTypeName='%s', createType=%s, postState=%d, sourceId=%s, showDetail=%s}", number, vchtype,
                status, vchcode, fields, deleErrorMsg, modErrorMsg, vchtypeName, vchtypeEnum, processType, date,
                createTypeName, createType, postState, sourceId, showDetail);
    }

    public Integer getCustomType() {
        return customType;
    }

    public void setCustomType(Integer customType) {
        this.customType = customType;
    }

    public Integer getIntPostState() {
        return intPostState;
    }

    public void setIntPostState(Integer intPostState) {
        this.intPostState = intPostState;
    }

    public Boolean getEshopBill() {
        return eshopBill;
    }

    public void setEshopBill(Boolean eshopBill) {
        this.eshopBill = eshopBill;
    }
}
