package com.wsgjp.ct.sale.common.config;

import com.wsgjp.ct.support.global.BaseConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> 2024/6/7 14:16
 */
@Configuration
@ConfigurationProperties(prefix = "sale.common")
public class SaleCommonBizConfig implements BaseConfig {

    private String freightCacheVersion = "V1";

    private String platformAddressCacheVersion = "V1";

    private String freightCacheWithNameShopList = "139";

    private Boolean freightMapUseRedisCacheEnabled = false;

    private String platformCateCacheVersion = "V1";

    public String getFreightCacheVersion() {
        return freightCacheVersion;
    }

    public void setFreightCacheVersion(String freightCacheVersion) {
        this.freightCacheVersion = freightCacheVersion;
    }

    public Boolean getFreightMapUseRedisCacheEnabled() {
        return freightMapUseRedisCacheEnabled;
    }

    public void setFreightMapUseRedisCacheEnabled(Boolean freightMapUseRedisCacheEnabled) {
        this.freightMapUseRedisCacheEnabled = freightMapUseRedisCacheEnabled;
    }

    public String getFreightCacheWithNameShopList() {
        return freightCacheWithNameShopList;
    }

    public void setFreightCacheWithNameShopList(String freightCacheWithNameShopList) {
        this.freightCacheWithNameShopList = freightCacheWithNameShopList;
    }

    public String getPlatformAddressCacheVersion() {
        return platformAddressCacheVersion;
    }

    public void setPlatformAddressCacheVersion(String platformAddressCacheVersion) {
        this.platformAddressCacheVersion = platformAddressCacheVersion;
    }

    public String getPlatformCateCacheVersion() {
        return platformCateCacheVersion;
    }

    public void setPlatformCateCacheVersion(String platformCateCacheVersion) {
        this.platformCateCacheVersion = platformCateCacheVersion;
    }
}
