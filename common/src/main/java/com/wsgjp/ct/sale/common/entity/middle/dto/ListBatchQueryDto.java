package com.wsgjp.ct.sale.common.entity.middle.dto;

import cn.hutool.core.date.DateTime;
import com.wsgjp.ct.sale.common.entity.middle.pojo.BatchInfo;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * 查询同步批次的DTO
 */
public class ListBatchQueryDto extends BatchInfo {

    private List<BigInteger> ktypeIdList;
    private List<BigInteger> ptypeIdList;
    private DateTime startTime;
    private DateTime endTime;
    private String batchnoStr;
    private String ptypeStr;

    public String getBatchnoStr() {
        return batchnoStr;
    }

    public void setBatchnoStr(String batchnoStr) {
        this.batchnoStr = batchnoStr;
    }

    public String getPtypeStr() {
        return ptypeStr;
    }

    public void setPtypeStr(String ptypeStr) {
        this.ptypeStr = ptypeStr;
    }

    public List<BigInteger> getKtypeIdList() {
        return ktypeIdList;
    }

    public void setKtypeIdList(List<BigInteger> ktypeIdList) {
        this.ktypeIdList = ktypeIdList;
    }

    public List<BigInteger> getPtypeIdList() {
        return ptypeIdList;
    }

    public void setPtypeIdList(List<BigInteger> ptypeIdList) {
        this.ptypeIdList = ptypeIdList;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(DateTime startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(DateTime endTime) {
        this.endTime = endTime;
    }


}
