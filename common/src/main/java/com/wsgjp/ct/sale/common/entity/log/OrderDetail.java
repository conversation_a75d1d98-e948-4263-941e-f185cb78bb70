package com.wsgjp.ct.sale.common.entity.log;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class OrderDetail {
    /**
     * 明细里的商家编码
     */
    private String xcode;
    /**
     * 数量qty
     */
    private Long qty;
    /**
     * 单价
     */
    private Double price;
    /**
     * 批号
     */
    private String batchNum;
    /**
     * 有效期
     */
    private String validity;
    /**
     * 生产日期
     */
    private String prodDate;

    private String oid;

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public Long getQty() {
        return qty;
    }

    public void setQty(Long qty) {
        this.qty = qty;
    }

    @JsonProperty("price")
    public Double getPrice() { return price; }
    @JsonProperty("price")
    public void setPrice(Double value) { this.price = value; }

    @JsonProperty("batchNum")
    public String getBatchNum() { return batchNum; }
    @JsonProperty("batchNum")
    public void setBatchNum(String value) { this.batchNum = value; }

    @JsonProperty("validity")
    public String getValidity() { return validity; }
    @JsonProperty("validity")
    public void setValidity(String value) { this.validity = value; }

    @JsonProperty("prodDate")
    public String getProdDate() { return prodDate; }
    @JsonProperty("prodDate")
    public void setProdDate(String value) { this.prodDate = value; }

    @JsonProperty("oid")
    public String getOid() {
        return oid;
    }

    @JsonProperty("oid")
    public void setOid(String oid) {
        this.oid = oid;
    }
}
