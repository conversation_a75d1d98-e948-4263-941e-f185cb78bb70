package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;

public enum BusinessTypeEnum implements CodeEnum {
    SALE_PROPERTIES(0, "销售属性"),
    DIY_PROPERTIES(1, "自定义属性"),
    SPEC_PROPERTIES(2, "规格属性"),
    EXT_PROPERTIES(3, "扩展属性"),
    SKU_PROPERTIES(4, "SKU属性比如颜色，尺码");
    private final int index;
    private final String name;

    BusinessTypeEnum(int index, String name) {
        this.index = index;
        this.name = name;
    }

    public static BusinessTypeEnum businessTypeEnum(int code) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (code == businessTypeEnum.getCode()) {
                return businessTypeEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}
