package com.wsgjp.ct.sale.common.notify.entity;

import java.util.Date;

public class TimingInfoDto {
    /**
     *预计发货时间
     */
    private Date planSendTime;
    /**
     *预计签收/送达时间
     */
    private Date planSignTime;
    /**
     *最晚发货时间
     */
    private Date sendTime;
    /**
     * 最晚揽收时间
     */
    private Date promisedCollectTime;
    /**
     * 最晚签收时间
     */
    private Date promisedSignTime;
    /**
     *签收时间
     */
    private Date signTime;
    /**
     *明细最晚发货时间
     */
    private Date promisedSendTime;
    private Date tradeFinishTime;

    public Date getTradeFinishTime() {
        return tradeFinishTime;
    }

    public void setTradeFinishTime(Date tradeFinishTime) {
        this.tradeFinishTime = tradeFinishTime;
    }

    public Date getPlanSendTime() {
        return planSendTime;
    }

    public void setPlanSendTime(Date planSendTime) {
        this.planSendTime = planSendTime;
    }

    public Date getPlanSignTime() {
        return planSignTime;
    }

    public void setPlanSignTime(Date planSignTime) {
        this.planSignTime = planSignTime;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }
    /**
     *承诺最早送达时间
     */
    private Date promisedSignStartTime;

    public Date getPromisedSignStartTime() {
        return promisedSignStartTime;
    }

    public void setPromisedSignStartTime(Date promisedSignStartTime) {
        this.promisedSignStartTime = promisedSignStartTime;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Date getPromisedCollectTime() {
        return promisedCollectTime;
    }

    public void setPromisedCollectTime(Date promisedCollectTime) {
        this.promisedCollectTime = promisedCollectTime;
    }

    public Date getPromisedSignTime() {
        return promisedSignTime;
    }

    public void setPromisedSignTime(Date promisedSignTime) {
        this.promisedSignTime = promisedSignTime;
    }

    public Date getPromisedSendTime() {
        return promisedSendTime;
    }

    public void setPromisedSendTime(Date promisedSendTime) {
        this.promisedSendTime = promisedSendTime;
    }
}
