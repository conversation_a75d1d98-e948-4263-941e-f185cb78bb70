package com.wsgjp.ct.sale.common.notify.entity;

import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.sale.common.enums.RefundStatusEnum;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-23
 **/
public class RefundOrderDetailDAO extends NotifyRefundOrderDetail {
    private BigInteger vchcode;
    private BigInteger id;
    private BigInteger profileId;
    private RefundStatusEnum refundState;
    private String oid;
    private String tradeOrderId;
    private BigInteger deliverDetailId;
    private BigInteger batchId;
    private String fullName;
    //原始订单明细id
    private BigInteger orderDetailId;
    private BigInteger eshopOrderId;

    private int billCreateType;

    private List<BaseOrderMarkEnum> marks;

    public List<BaseOrderMarkEnum> getMarks() {
        return marks;
    }

    public void setMarks(List<BaseOrderMarkEnum> marks) {
        this.marks = marks;
    }

    public int getBillCreateType() {
        return billCreateType;
    }

    public void setBillCreateType(int billCreateType) {
        this.billCreateType = billCreateType;
    }

    public BigInteger getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(BigInteger orderDetailId) {
        this.orderDetailId = orderDetailId;
    }


    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public RefundStatusEnum getRefundState() {
        return refundState;
    }

    public void setRefundState(RefundStatusEnum refundState) {
        this.refundState = refundState;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public BigInteger getDeliverDetailId() {
        return deliverDetailId;
    }

    public void setDeliverDetailId(BigInteger deliverDetailId) {
        this.deliverDetailId = deliverDetailId;
    }

    public BigInteger getBatchId() {
        return batchId;
    }

    public void setBatchId(BigInteger batchId) {
        this.batchId = batchId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public BigInteger getEshopOrderId() {
        return eshopOrderId;
    }

    public void setEshopOrderId(BigInteger eshopOrderId) {
        this.eshopOrderId = eshopOrderId;
    }
}
