package com.wsgjp.ct.sale.common.notify.entity;

import com.wsgjp.ct.common.enums.core.entity.DeliverNewMark;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className DeliverBillDetailPlatformMarkUpdateDao
 */
public class DeliverBillDetailPlatformMarkUpdateDao implements Serializable {
    private List<DeliverNewMark> newPlatformMarks;
    private List<DeliverNewMark> deletedPlatformMarks;
    private String oid;
    private String tradeOrderId;

    private String comment;

    public List<DeliverNewMark> getNewPlatformMarks() {
        return newPlatformMarks;
    }

    public void setNewPlatformMarks(List<DeliverNewMark> newPlatformMarks) {
        this.newPlatformMarks = newPlatformMarks;
    }

    public List<DeliverNewMark> getDeletedPlatformMarks() {
        return deletedPlatformMarks;
    }

    public void setDeletedPlatformMarks(List<DeliverNewMark> deletedPlatformMarks) {
        this.deletedPlatformMarks = deletedPlatformMarks;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
