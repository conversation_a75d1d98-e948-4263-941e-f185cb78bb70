package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;

public enum UpdateOrderRefundType implements CodeEnum {
    REFUND_CREATE_BY_TMC(0, "提前通知售后拦截", OrderOpreateType.TMC_AUTO_DOWNLOAD, AdvanceOrderOperateTypeEnum.TMC_AUTO_UPDATE),
    REFUND_CANCEL_BY_TMC(1, "提前取消售后拦截", OrderOpreateType.TMC_AUTO_DOWNLOAD,AdvanceOrderOperateTypeEnum.TMC_AUTO_UPDATE),
    REFUND_CREATE_BY_SYNC_ORDER(2, "提前通知售后拦截", OrderOpreateType.AUTO_DOWNLOAD,AdvanceOrderOperateTypeEnum.ADVANCE_UPDATE),
    REFUND_CANCEL_BY_SYNC_ORDER(3, "提前取消售后拦截", OrderOpreateType.AUTO_DOWNLOAD,AdvanceOrderOperateTypeEnum.ADVANCE_UPDATE);


    /**
     * code
     */
    private int code;

    /**
     * name
     */
    private String name;
    private OrderOpreateType orderOpreateType;
    private AdvanceOrderOperateTypeEnum advanceOrderOperateType;

    UpdateOrderRefundType(int code, String name,OrderOpreateType orderOpreateType,AdvanceOrderOperateTypeEnum advanceOrderOperateType) {
        this.code = code;
        this.name = name;
        this.orderOpreateType=orderOpreateType;
        this.advanceOrderOperateType=advanceOrderOperateType;
    }

    public AdvanceOrderOperateTypeEnum getAdvanceOrderOperateType() {
        return advanceOrderOperateType;
    }

    public void setAdvanceOrderOperateType(AdvanceOrderOperateTypeEnum advanceOrderOperateType) {
        this.advanceOrderOperateType = advanceOrderOperateType;
    }

    public OrderOpreateType getOrderOpreateType() {
        return orderOpreateType;
    }

    public void setOrderOpreateType(OrderOpreateType orderOpreateType) {
        this.orderOpreateType = orderOpreateType;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }


}
