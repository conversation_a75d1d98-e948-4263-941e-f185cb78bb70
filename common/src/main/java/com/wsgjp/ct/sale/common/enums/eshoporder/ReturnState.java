package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum ReturnState implements CodeEnum {
    NONE(0, "无需退款"),
    REFUNDING(1, "退款中"),
    SECTION_REFUNDING(2, "部分退款中"),
    SECTION_REFUND_SUCCESS(3, "部分退款成功"),
    SUCCESS(4, "退款成功");

    private int flag;
    private String name;

    ReturnState(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public static ReturnState getTopReturnState(List<ReturnState> returnStateList) {
        if (returnStateList == null || returnStateList.isEmpty()) {
            return NONE;
        }
        if (returnStateList.contains(REFUNDING)) {
            return REFUNDING;
        }
        if (returnStateList.contains(SECTION_REFUNDING)) {
            return SECTION_REFUNDING;
        }
        if (returnStateList.contains(SECTION_REFUND_SUCCESS)) {
            return SECTION_REFUND_SUCCESS;
        }
        if (returnStateList.contains(SUCCESS)) {
            return SUCCESS;
        }
        return NONE;
    }

    public static ReturnState codeToEnum(int flag) {
        for (ReturnState item : ReturnState.values()) {
            if (Objects.equals(item.getCode(), flag)) {
                return item;
            }
        }
        return null;
    }


}
