package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * @author: zengguangwu on 2021-08-20 14:39
 */
public class BillSaveExceptionDetailDTO {
    /**
     * 商品ID
     */
    private BigInteger ptypeId;
    /**
     * 商品全名
     */
    private String pFullName;
    /**
     * 商品编号
     */
    private String ptypeCode;
    /**
     * 商品单位
     */
    private String ptypeUnit;
    /**
     * 仓库ID
     */
    private BigInteger ktypeId;
    /**
     * 仓库全名
     */
    private String kFullName;
    /**
     * 仓库2ID
     */
    private BigInteger ktypeId2;
    /**
     * 仓库2全名
     */
    private String kFullName2;
    /**
     * 属性1名称
     */
    private String prop1Name;
    /**
     * 属性2名称
     */
    private String prop2Name;
    /**
     * 最低售价
     */
    private BigDecimal minPrice;
    /**
     * 最近进价
     */
    private BigDecimal recPrice;
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * 单据需求数量
     */
    private BigDecimal qty;
    /**
     * 实际数量
     */
    private BigDecimal realQty;
    /**
     * 序列号
     */
    private String snNo;
    /**
     * 序列号备注
     */
    private String snNoMemo;
    /**
     * 保质期
     */
    private BigInteger protectDays;
    /**
     * 生产日期
     */
    private String produceDate;
    /**
     * 到期日期
     */
    private String expirationDate;
    /**
     * 到期日期
     */
    private String expireDate;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 异常消息
     */
    private String message;

    /**
     * 单据编号
     */
    private String billNumber;

    /**
     * 商品编号
     */
    private String pUserCode;
    /**
     * 商品编号
     */
    private String ptypeUserCode;
    /**
     * 规格
     */
    private String standard;
    /**
     * 型号
     */
    private String ptypetype;

    /**
     * 属性组合
     */
    private String propValues;

    /**
     * 条码
     */
    private String fullbarcode;

    /**
     * 条码
     */
    private String skuBarcode;

    /**
     * 商品条码
     */
    private String pFullBarCode;

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }

    public String getPtypeUserCode() {
        return ptypeUserCode;
    }

    public void setPtypeUserCode(String ptypeUserCode) {
        this.ptypeUserCode = ptypeUserCode;
    }

    public String getFullbarcode() {
        return fullbarcode;
    }

    public void setFullbarcode(String fullbarcode) {
        this.fullbarcode = fullbarcode;
    }

    public String getSkuBarcode() {
        return skuBarcode;
    }

    public void setSkuBarcode(String skuBarcode) {
        this.skuBarcode = skuBarcode;
    }

    public String getpFullBarCode() {
        return pFullBarCode;
    }

    public void setpFullBarCode(String pFullBarCode) {
        this.pFullBarCode = pFullBarCode;
    }

    public String getpUserCode() {
        return pUserCode;
    }

    public void setpUserCode(String pUserCode) {
        this.pUserCode = pUserCode;
    }

    public String getStandard() {
        return standard;
    }

    public void setStandard(String standard) {
        this.standard = standard;
    }

    public String getPtypetype() {
        return ptypetype;
    }

    public void setPtypetype(String ptypetype) {
        this.ptypetype = ptypetype;
    }

    public String getPropValues() {
        return propValues;
    }

    public void setPropValues(String propValues) {
        this.propValues = propValues;
    }

    public String getBillNumber() {
        return billNumber;
    }

    public void setBillNumber(String billNumber) {
        this.billNumber = billNumber;
    }

    public BigInteger getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public String getpFullName() {
        return pFullName;
    }

    public void setpFullName(String pFullName) {
        this.pFullName = pFullName;
    }

    public String getPtypeCode() {
        return ptypeCode;
    }

    public void setPtypeCode(String ptypeCode) {
        this.ptypeCode = ptypeCode;
    }

    public String getPtypeUnit() {
        return ptypeUnit;
    }

    public void setPtypeUnit(String ptypeUnit) {
        this.ptypeUnit = ptypeUnit;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public String getkFullName() {
        return kFullName;
    }

    public void setkFullName(String kFullName) {
        this.kFullName = kFullName;
    }

    public BigInteger getKtypeId2() {
        return ktypeId2;
    }

    public void setKtypeId2(BigInteger ktypeId2) {
        this.ktypeId2 = ktypeId2;
    }

    public String getkFullName2() {
        return kFullName2;
    }

    public void setkFullName2(String kFullName2) {
        this.kFullName2 = kFullName2;
    }

    public String getProp1Name() {
        return prop1Name;
    }

    public void setProp1Name(String prop1Name) {
        this.prop1Name = prop1Name;
    }

    public String getProp2Name() {
        return prop2Name;
    }

    public void setProp2Name(String prop2Name) {
        this.prop2Name = prop2Name;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public BigDecimal getRecPrice() {
        return recPrice;
    }

    public void setRecPrice(BigDecimal recPrice) {
        this.recPrice = recPrice;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getRealQty() {
        return realQty;
    }

    public void setRealQty(BigDecimal realQty) {
        this.realQty = realQty;
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public String getSnNoMemo() {
        return snNoMemo;
    }

    public void setSnNoMemo(String snNoMemo) {
        this.snNoMemo = snNoMemo;
    }

    public BigInteger getProtectDays() {
        return protectDays;
    }

    public void setProtectDays(BigInteger protectDays) {
        this.protectDays = protectDays;
    }

    public String getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(String produceDate) {
        this.produceDate = produceDate;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return String.format("BillSaveExceptionDetailDTO{ptypeId=%s, pFullName='%s', ptypeCode='%s', ptypeUnit='%s', ktypeId=%s, kFullName='%s', ktypeId2=%s, kFullName2='%s', prop1Name='%s', prop2Name='%s', minPrice=%s, recPrice=%s, costPrice=%s, qty=%s, realQty=%s, snNo='%s', snNoMemo='%s', protectDays=%s, produceDate='%s', expirationDate='%s', batchNo='%s', message='%s'}", ptypeId, pFullName, ptypeCode, ptypeUnit, ktypeId, kFullName, ktypeId2, kFullName2, prop1Name, prop2Name, minPrice, recPrice, costPrice, qty, realQty, snNo, snNoMemo, protectDays, produceDate, expirationDate, batchNo, message);
    }
}
