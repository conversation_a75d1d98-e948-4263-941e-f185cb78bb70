package com.wsgjp.ct.sale.common.entity.dto;


import com.wsgjp.ct.common.enums.core.enums.QtyChangeSourceType;
import com.wsgjp.ct.common.enums.core.enums.QtyChangeType;
import ngp.utils.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 25/2/2021 下午 5:13
 */
public class StockChangeDTO implements Cloneable {
	private BigInteger profileId;
	private BigInteger ktypeId;
	private BigInteger skuId;
	private BigDecimal qty;
	private QtyChangeType changeType;
	private String sourceTypeStr;
	private QtyChangeSourceType sourceType;
	private BigInteger sourceId;
	private BigInteger sourceDetailId;
	private BigInteger ruleId;

    public StockChangeDTO doClone() throws CloneNotSupportedException {
        StockChangeDTO clone = new StockChangeDTO();
        BeanUtils.copyProperties(this, clone);
        return clone;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

	public void setProfileId(BigInteger profileId) {
		this.profileId = profileId;
	}

	public BigInteger getKtypeId() {
		return ktypeId;
	}

	public void setKtypeId(BigInteger ktypeId) {
		this.ktypeId = ktypeId;
	}

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public QtyChangeType getChangeType() {
        if (changeType == null) {
            return QtyChangeType.INCREASE;
        }
        return changeType;
    }

    public void setChangeType(QtyChangeType changeType) {
        this.changeType = changeType;
    }

    public QtyChangeSourceType getSourceType() {
        if (sourceType == null) {
            return QtyChangeSourceType.NONE;
        }
        return sourceType;
    }

    public void setSourceType(QtyChangeSourceType sourceType) {
        this.sourceType = sourceType;
    }

    public BigInteger getSourceId() {
        return sourceId;
    }

    public void setSourceId(BigInteger sourceId) {
        this.sourceId = sourceId;
    }

    public BigInteger getSourceDetailId() {
        return sourceDetailId;
    }

    public void setSourceDetailId(BigInteger sourceDetailId) {
        this.sourceDetailId = sourceDetailId;
    }

    public BigInteger getRuleId() {
        return ruleId;
    }

    public void setRuleId(BigInteger ruleId) {
        this.ruleId = ruleId;
    }

    public String getSourceTypeStr() {
        if (sourceTypeStr == null || StringUtils.isNoneEmpty(sourceTypeStr)) {
            return String.format("%s:%s", getSourceType().getCode(), getSourceType().getName());
        }
        return sourceTypeStr;
    }

    public void setSourceTypeStr(String sourceTypeStr) {
        this.sourceTypeStr = sourceTypeStr;
    }
}
