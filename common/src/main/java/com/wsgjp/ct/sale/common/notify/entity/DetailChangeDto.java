package com.wsgjp.ct.sale.common.notify.entity;



import com.wsgjp.ct.sale.common.enums.eshoporder.OrderDetailChangeType;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 23/6/2020 下午 3:14
 */
public class DetailChangeDto {
	private List<String> onlineDetailIds;
	private OrderDetailChangeType detailChangeType;
	/**
	 *平台金额
	 */
	private BigDecimal total;
	//折后金额
	private BigDecimal disedInitialTotal;
	//折惠后含税金额
	private BigDecimal disedTaxedTotal;
	//折惠后金额
	private BigDecimal disedTotal;
	//税额
	private BigDecimal taxTotal;
	//商家单品优惠
	private BigDecimal ptypePreferentialTotal;
	//平台单品优惠
	private BigDecimal platformPtypePreferentialTotal;
	//商家整单优惠分摊
	private BigDecimal orderPreferentialAllotTotal;
	//平台整单优惠分摊
	private BigDecimal platformOrderPreferentialTotal;
	//主播整单优惠分摊
	private BigDecimal anchorOrderPreferentialTotal;
	//主播单品优惠金额
	private BigDecimal anchorPtypePreferentialTotal;
	//平台补贴商家整单金额分摊
	private BigDecimal platformOrderSubsidyTotal;
	//平台补贴商家单品金额
	private BigDecimal platformPtypeSubsidyTotal;
	//国补金额
	private BigDecimal nationalSubsidyTotal;
	//服务费
	private BigDecimal ptypeServiceFee;

	public BigDecimal getAnchorOrderPreferentialTotal() {
		return anchorOrderPreferentialTotal;
	}

	public void setAnchorOrderPreferentialTotal(BigDecimal anchorOrderPreferentialTotal) {
		this.anchorOrderPreferentialTotal = anchorOrderPreferentialTotal;
	}

	public BigDecimal getAnchorPtypePreferentialTotal() {
		return anchorPtypePreferentialTotal;
	}

	public void setAnchorPtypePreferentialTotal(BigDecimal anchorPtypePreferentialTotal) {
		this.anchorPtypePreferentialTotal = anchorPtypePreferentialTotal;
	}

	public BigDecimal getPlatformOrderSubsidyTotal() {
		return platformOrderSubsidyTotal;
	}

	public void setPlatformOrderSubsidyTotal(BigDecimal platformOrderSubsidyTotal) {
		this.platformOrderSubsidyTotal = platformOrderSubsidyTotal;
	}

	public BigDecimal getPlatformPtypeSubsidyTotal() {
		return platformPtypeSubsidyTotal;
	}

	public void setPlatformPtypeSubsidyTotal(BigDecimal platformPtypeSubsidyTotal) {
		this.platformPtypeSubsidyTotal = platformPtypeSubsidyTotal;
	}

	public BigDecimal getNationalSubsidyTotal() {
		return nationalSubsidyTotal;
	}

	public void setNationalSubsidyTotal(BigDecimal nationalSubsidyTotal) {
		this.nationalSubsidyTotal = nationalSubsidyTotal;
	}

	public BigDecimal getPtypeServiceFee() {
		return ptypeServiceFee;
	}

	public void setPtypeServiceFee(BigDecimal ptypeServiceFee) {
		this.ptypeServiceFee = ptypeServiceFee;
	}

	public BigDecimal getTotal() {
		return total;
	}

	public void setTotal(BigDecimal total) {
		this.total = total;
	}

	public BigDecimal getDisedInitialTotal() {
		return disedInitialTotal;
	}

	public void setDisedInitialTotal(BigDecimal disedInitialTotal) {
		this.disedInitialTotal = disedInitialTotal;
	}

	public BigDecimal getDisedTaxedTotal() {
		return disedTaxedTotal;
	}

	public void setDisedTaxedTotal(BigDecimal disedTaxedTotal) {
		this.disedTaxedTotal = disedTaxedTotal;
	}

	public BigDecimal getDisedTotal() {
		return disedTotal;
	}

	public void setDisedTotal(BigDecimal disedTotal) {
		this.disedTotal = disedTotal;
	}

	public BigDecimal getTaxTotal() {
		return taxTotal;
	}

	public void setTaxTotal(BigDecimal taxTotal) {
		this.taxTotal = taxTotal;
	}

	public BigDecimal getPtypePreferentialTotal() {
		return ptypePreferentialTotal;
	}

	public void setPtypePreferentialTotal(BigDecimal ptypePreferentialTotal) {
		this.ptypePreferentialTotal = ptypePreferentialTotal;
	}

	public BigDecimal getPlatformPtypePreferentialTotal() {
		return platformPtypePreferentialTotal;
	}

	public void setPlatformPtypePreferentialTotal(BigDecimal platformPtypePreferentialTotal) {
		this.platformPtypePreferentialTotal = platformPtypePreferentialTotal;
	}

	public BigDecimal getOrderPreferentialAllotTotal() {
		return orderPreferentialAllotTotal;
	}

	public void setOrderPreferentialAllotTotal(BigDecimal orderPreferentialAllotTotal) {
		this.orderPreferentialAllotTotal = orderPreferentialAllotTotal;
	}

	public BigDecimal getPlatformOrderPreferentialTotal() {
		return platformOrderPreferentialTotal;
	}

	public void setPlatformOrderPreferentialTotal(BigDecimal platformOrderPreferentialTotal) {
		this.platformOrderPreferentialTotal = platformOrderPreferentialTotal;
	}

	public List<String> getOnlineDetailIds() {
		return onlineDetailIds;
	}

	public void setOnlineDetailIds(List<String> onlineDetailIds) {
		this.onlineDetailIds = onlineDetailIds;
	}

	public OrderDetailChangeType getDetailChangeType() {
		return detailChangeType;
	}

	public void setDetailChangeType(OrderDetailChangeType detailChangeType) {
		this.detailChangeType = detailChangeType;
	}
}
