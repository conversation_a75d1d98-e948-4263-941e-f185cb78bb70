package com.wsgjp.ct.sale.common.enums;

public enum BillCreateType {
    INPUT(0, "手工新增","INPUT"),
    FROM_DOWNLOAD_ORDER(1, "平台下载","FROM_DOWNLOAD_ORDER"),
    FROM_ORDER_REFUND(2, "售后生成","FROM_ORDER_REFUND"),
    FROM_SALE_ORDER_BILL(4, "销售订单生成","FROM_SALE_ORDER_BILL"),
    FROM_Buy_ORDER_BILL(5, "采购订单生成","FROM_Buy_ORDER_BILL"),
    FROM_CLOSE_PERIOD(10, "月末结账","FROM_CLOSE_PERIOD"),
    FROM_ORDER_RECONCILIATION(12, "按单对账生成","FROM_ORDER_RECONCILIATION"),
    FROM_ORDER_PAYMENT_FLOW(13, "原始账单生成","FROM_ORDER_PAYMENT_FLOW"),
    FROM_FREIGHT_SETTLEMENT(14, "运费结算生成","FROM_FREIGHT_SETTLEMENT"),
    FROM_ORDER_REFUND_CHECKIN(15, "收货登记生成","FROM_ORDER_REFUND_CHECKIN"),
    IMPORT(16, "导入新增","IMPORT"),
    SAFETY_STOCK_WARN(17, "安全库存预警生成","SAFETY_STOCK_WARN"),
    NEGATIVE_STOCK_WARN(18, "负库存预警生成","NEGATIVE_STOCK_WARN"),
    REPLENISHMENT(19, "缺货补货生成","REPLENISHMENT"),
    STOCK_CHECK(20, "盘点生成","STOCK_CHECK");

    private int type;
    private String desc;
    private String name;

    private BillCreateType(int type, String desc,String name) {
        this.type = type;
        this.desc = desc;
        this.name = name;
    }

    public static BillCreateType convertBillCreateType(int value){
        for (BillCreateType item:values()
        ) {
            if(item.getType()==value){
                return  item;
            }
        }
        return  null;
    }

    public int getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    public int getCode() {
        return this.getType();
    }

    public String getName() {
        return this.name;
    }
}

