package com.wsgjp.ct.sale.common.enums;

import com.wsgjp.ct.common.enums.core.config.BillNameConst;

/**
 * 修改单据的场景汇总
 */
public enum ModifyScene {
    /**
     * 平台通知更新
     */
    SaleOrder_Notify("原单通知",true),
    Bill_Modify("修改单据",true),
    Detail_Modify("修改明细",true),
    Import_Modify("导入修改",true),
    BillOtherModify("言，址，票，留言、备注，回告平台 等附属内容",true),
    Freight_Modify("改物流信息",true),
    Audit_Modify("审核" + BillNameConst.BILL_NAME,false),
    Process_Modify("流程变化：审核、发货、核算、驳回、截停 等工作",true),

    ;
    private String msg;

    /**
     * 单据主表时间应该比纳湖
     */
    private boolean billMoved;

    ModifyScene(String msg, boolean billMoved) {
        this.msg = msg;
        this.billMoved = billMoved;
    }

    ModifyScene(String msg) {
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public boolean isBillMoved() {
        return billMoved;
    }
}
