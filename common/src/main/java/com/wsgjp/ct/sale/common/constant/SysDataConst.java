package com.wsgjp.ct.sale.common.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/22 15:48
 */
public class SysDataConst {
	public static final String SALE_QTY_NON_PAID_ORDER = "sysGlobalSaleQtyNonPaidOrder";
	public static final String SALE_QTY_ERR_BIZ_ORDER = "sysGlobalSaleQtyErrBusinessOrder";
	public static final String ORDER_AVAILABLE_STOCK = "recordsheetSaleOrderAvailableStock";
	public static final String ONE_STR = "1";
	public static final String ZERO_STR = "0";
	public static final String COLON = "：";

    public static final String EMPTY_STR = "";

	public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

	public static final String DATE_PATTERN = "yyyy-MM-dd";

    public static final String PLATFORM_1688_UPLOAD_CHANEL_DATE = "AlibabaUploadChanelDate";

    public static final String ALIBABA_CHANEL_UPLOAD_DD = "AlibabaUploadChanel_DD";

    public static final String ALIBABA_CHANEL_UPLOAD_DD_CODE = "douyin";
    public static final String ALIBABA_CHANEL_UPLOAD_KS = "AlibabaUploadChanel_ks";

    public static final String ALIBABA_CHANEL_UPLOAD_KS_CODE = "kuaishou";

    public static final List<String> ALIBABA_CHANEL_UPLOAD = new ArrayList<>();

    static {
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_tb");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_c2m");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_jingdong");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_pinduoduo");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_weixin");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_kuajing");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_youzan");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_douyin");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_siku");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_meituan");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_xiaohongshu");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_dangdang");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_suning");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_davdian");
        ALIBABA_CHANEL_UPLOAD.add("AlibabaUploadChanel_xingyun");
    }
}
