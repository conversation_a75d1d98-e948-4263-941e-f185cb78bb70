package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;

public enum AdvanceOrderOperateTypeEnum implements CodeEnum {
    SUBMIT(0, "确认提交"),
    MODIFY_PLAN_SEND_TIME(1, "修改预计发货时间"),
    MODIFY_SELLER_MESSAGE(2, "修改卖家备注"),
    MODIFY_TRADE_TYPE(3, "修改预售交易类型"),
    GATHER_FRONT(4, "收定金"),
    GATHER_TAIL(5, "收尾款"),
    EXPORT_ORDER(6, "导出订单"),
    CREATE_ADVANCE(7, "创建预售订单"),
    EDIT_ADVANCE(8, " 编辑预售订单"),
    RESUBMIT_ORDER(13, "还原订单"),
    UPDATE_ADVANCE(15, "更新预售订单"),
    UPDATE_ADVANCE_REFUND(16, "更新退款状态"),
    UPDATE_CUSTOM_TRADE(17, "更新线上交易状态"),
    REFUND_NOTIFY(14, "售后通知"),
    SEND_ONLINE(15, "系统发货"),
    MODIFY_SELLER_MESSAGE_NOTIFY(16, "修改卖家备注通知"),
    DO_RELATION(17, "网店商品对应"),
    CUSTOM_MARK(18, "自定义标记"),
    UPDATE_PROCESS_STATE(19, "修改提交状态"),
    TMC_AUTO_UPDATE(20, "TMC自动更新订单"),
    ADVANCE_UPDATE(21, "自动更新预售订单"),
    ;
    private int code;
    private String name;

    AdvanceOrderOperateTypeEnum(int index, String name) {
        this.code = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

}
