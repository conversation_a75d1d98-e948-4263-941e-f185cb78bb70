package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

public enum RefundPlatformAuditStatus implements CodeEnum {
    ALL(-1, "全部"),
    WAIT_AUDIT(0, "未审核"),
    AUDITED(1, "已审核"),
    REJECTED(2, "拒绝");
    private int code;
    private String name;

    RefundPlatformAuditStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
