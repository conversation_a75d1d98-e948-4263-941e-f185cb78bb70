package com.wsgjp.ct.sale.common.entity.stock;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR> 2023/6/14 11:16
 */
public class NotifyMessageStockSyncParam {
    private BigInteger eshopId;
    private String warehouseCode;
    private int targetType;
    private String taskId;
    private List<StockSyncPubMessage> pubMessageList;

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public int getTargetType() {
        return targetType;
    }

    public void setTargetType(int targetType) {
        this.targetType = targetType;
    }

    public List<StockSyncPubMessage> getPubMessageList() {
        return pubMessageList;
    }

    public void setPubMessageList(List<StockSyncPubMessage> pubMessageList) {
        this.pubMessageList = pubMessageList;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
}
