package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

public enum ProducerTypeEnum implements CodeEnum {

    PRE_SALE_ORDER(0, "预售"),
    SALE_ORDER(1, "交易单"),
    REFUND_ORDER(2,"售后订单"),
    OFFLINE_ORDER(3,"线下订单"),
    OFFLINE_SALE_ORDER(4,"线下销售订单");

    private String desc;
    private int code;

    ProducerTypeEnum(int code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public int getCode() {
        return code;
    }
}
