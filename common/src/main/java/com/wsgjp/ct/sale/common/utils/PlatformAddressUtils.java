package com.wsgjp.ct.sale.common.utils;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.api.ProfileApiForPlatform;
import com.wsgjp.ct.sale.common.config.SaleCommonBizConfig;
import com.wsgjp.ct.sale.common.entity.request.QueryConfigDataRequest;
import com.wsgjp.ct.sale.common.entity.response.QueryConfigDataResponse;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> 2024/6/20 20:22
 */
public class PlatformAddressUtils {

    private static final Logger logger = LoggerFactory.getLogger(PlatformAddressUtils.class);
    private static final ConcurrentHashMap<String, String> ADDRESS_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, String> Cate_MAP = new ConcurrentHashMap<>();

    private static String VERSION = "";
    private static String CATE_VERSION = "";

    private static ProfileApiForPlatform profileApi;
    private static SaleCommonBizConfig config;


    public PlatformAddressUtils(SaleCommonBizConfig config, ProfileApiForPlatform profileApi) {
        PlatformAddressUtils.profileApi = profileApi;
        PlatformAddressUtils.config = config;
        PlatformAddressUtils.VERSION = config.getPlatformAddressCacheVersion();
    }


    public static String getAddress(ShopType shopType) {
        try {
            if (!config.getPlatformAddressCacheVersion().equals(VERSION)) {
                ADDRESS_MAP.clear();
                PlatformAddressUtils.VERSION = config.getPlatformAddressCacheVersion();
            }
            String key = String.format("platform-address-config-%s", shopType.name());
            if (ADDRESS_MAP.containsKey(key)) {
                return ADDRESS_MAP.get(key);
            }
            initAddressMap(key);
            if (ADDRESS_MAP.containsKey(key)) {
                return ADDRESS_MAP.get(key);
            }
        } catch (Exception ex) {
            logger.error("获取平台地址配置失败", ex);
        }
        return "";
    }

    private static void initAddressMap(String key) {
        try {
            String configVal = getPlatformConfigData(key);
            if (StringUtils.isNotEmpty(configVal)) {
                ADDRESS_MAP.put(key, configVal);
            }
        } catch (Exception ex) {
            logger.error("获取平台地址配置失败", ex);
        }
    }


    public static String getPlatformCatList(ShopType shopType) {
        try {
            if (!config.getPlatformCateCacheVersion().equals(CATE_VERSION)) {
                Cate_MAP.clear();
                PlatformAddressUtils.CATE_VERSION = config.getPlatformCateCacheVersion();
            }
            String key = String.format("platform-category-%s", shopType.getCode());
            if (Cate_MAP.containsKey(key)) {
                return Cate_MAP.get(key);
            }
            initPlatformCate(key);
            if (Cate_MAP.containsKey(key)) {
                return Cate_MAP.get(key);
            }
        } catch (Exception ex) {
            logger.error("获取平台类目配置失败", ex);
        }
        return "";
    }


    private static void initPlatformCate(String key) {
        try {
            String configVal = getPlatformConfigData(key);
            if (StringUtils.isNotEmpty(configVal)) {
                Cate_MAP.put(key, configVal);
            }
        } catch (Exception ex) {
            logger.error("获取平台类目配置失败", ex);
        }
    }

    private static String getPlatformConfigData(String key) {
        QueryConfigDataRequest request = new QueryConfigDataRequest();
        request.setKey(key);
        QueryConfigDataResponse response = profileApi.queryConfigData(request);
        if (!response.getSuccess()) {
            throw new RuntimeException(response.getMsg());
        }
        if (response.getData() == null) {
            return null;
        }
        return response.getData().getValue();
    }
}
