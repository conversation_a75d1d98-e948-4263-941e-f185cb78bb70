package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

public enum BillBusinessTypeEnum implements CodeEnum {
    None(0, "普通业务"),
    Buy(100, "采购业务"),
    WholeSaleOnline(200, "线上批发业务"),
    WholeSaleOffline(201, "线下批发业务"),
    EshopRetail(202, "网店直营业务"),
    EshopDistribution(203, "网店分销业务"),
    EshopRetailManufacturer(204, "网店代销业务"),
    EshopClickFarm(205, "网店刷单业务"),
    StoreRetail(206, "门店零售业务"),
    FranchiseeRetail(207, "加盟店业务"),
    Leakage(208, "漏发补发"),
    GoodsTrans(300, "调货业务"),
    Consign(301, "委托业务"),
    Outsource(302, "委外业务"),
    StockPriceAdjust(303, "库存调价"),
    StockAssembly(304, "组装拆卸"),
    StockLoss(305, "报损业务"),
    StockOverflow(306, "报溢业务"),
    OtherInoutStock(307, "其他库存业务"),
    InternalRequisition(308, "内部领用业务"),
    BuyFee(401, "采购费用"),
    SaleFee(402, "销售费用"),
    StockFee(403, "库存费用"),
    GeneralFee(404, "管理费用"),
    OnlyRefundMoneyFee(405, "售后仅退款");

    private int businessType;
    private String businessName;


    BillBusinessTypeEnum(int type, String name) {
        this.businessType = type;
        this.businessName = name;
    }

    public int getBusinessType() {
        return businessType;
    }

    public String getBusinessName() {
        return businessName;
    }

    @Override
    public int getCode() {
        return this.getBusinessType();
    }

    @Override
    public String getName() {
        return this.getBusinessName();
    }

    public static BillBusinessTypeEnum typeOf(int type) {
        for (BillBusinessTypeEnum businessType : values()) {
            if (businessType.getBusinessType() == type) {
                return businessType;
            }
        }
        throw new RuntimeException("没有找到对应的枚举");
    }
}
