package com.wsgjp.ct.sale.common.enums;


/**
 * <AUTHOR>
 * @date 2020-06-23
 **/
public enum RefundStatusEnum implements PriorityEnum {

    REFUNDING(1, "退款中", 4),
    FINISH(2, "退款成功", 3),
    NO_REFUND(4, "无需退款", 1),
    CLOSE(4, "无需退款", 1);

    private String desc;
    private Integer value;
    private Integer priority;

    public String getDesc() {
        return desc;
    }

    public Integer getValue() {
        return value;
    }

    RefundStatusEnum(Integer value, String desc, Integer priority) {
        this.value = value;
        this.desc = desc;
        this.priority = priority;
    }

    @Override
    public int getPriority() {
        return priority;
    }
}
