package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum SaleOrderQueryType implements CodeEnum {

    /**
     * 门店单据查询类型
     */
    DirectSales(0, "直营门店单据查询"),
    Distributors(1, "渠道门店单据查询"),
    FXS(2, "分销商销售单据查询"),
    AllSales(3, "全渠道单据查询");

    private int index;
    private String name;

    SaleOrderQueryType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }

}
