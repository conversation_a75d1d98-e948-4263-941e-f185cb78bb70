package com.wsgjp.ct.sale.common.entity.tmc;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import ngp.utils.StringUtils;

import java.math.BigInteger;
import java.util.Date;

/**
 * tmc 授权tmc消息
 * <AUTHOR>
 */
public class TmcAuthMessage {

    private String token;
    private String refreshToken;
    /**
     * 授权过期时间
     */
    private Date expiresIn;
    /**
     * 刷新授权码过期时间
     */
    private Date reExpiresIn;

    /**
     * 获取token的code
     */
    private String authCode;

    private BigInteger profileId;
    private BigInteger eshopId;
    private ShopType shopType;

    private String shopAccount;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getRefreshToken() {
        if(StringUtils.isEmpty(refreshToken)){
            refreshToken="";
        }
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Date expiresIn) {
        this.expiresIn = expiresIn;
    }

    public Date getReExpiresIn() {
        return reExpiresIn;
    }

    public void setReExpiresIn(Date reExpiresIn) {
        this.reExpiresIn = reExpiresIn;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public String getShopAccount() {
        return shopAccount;
    }

    public void setShopAccount(String shopAccount) {
        this.shopAccount = shopAccount;
    }
}
