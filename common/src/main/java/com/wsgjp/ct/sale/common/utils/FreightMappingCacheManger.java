package com.wsgjp.ct.sale.common.utils;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.api.ProfileApiForPlatform;
import com.wsgjp.ct.sale.common.config.SaleCommonBizConfig;
import com.wsgjp.ct.sale.common.entity.BizBigConfigData;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.common.entity.freight.QueryFreightMappingParam;
import com.wsgjp.ct.sale.common.entity.request.QueryConfigDataRequest;
import com.wsgjp.ct.sale.common.entity.response.QueryConfigDataResponse;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/6/7 13:50
 */
public class FreightMappingCacheManger {

    private static ProfileApiForPlatform profileApi;
    private static SaleCommonBizConfig config;

    private static String version = "";

    private static final Logger logger = LoggerFactory.getLogger(FreightMappingCacheManger.class);

    private static final ConcurrentMap<String, List<FreightMapping>> FREIGHT_CONFIG_DATA = new ConcurrentHashMap<>();

    public FreightMappingCacheManger(ProfileApiForPlatform profileApi, SaleCommonBizConfig config) {
        FreightMappingCacheManger.profileApi = profileApi;
        FreightMappingCacheManger.config = config;
        FreightMappingCacheManger.version = config.getFreightCacheVersion();
    }


    public static List<FreightMapping> getFreightMappingByCache(ShopType shopType) {
        try {
            if (!config.getFreightCacheVersion().equals(version)) {
                FREIGHT_CONFIG_DATA.clear();
                FreightMappingCacheManger.version = config.getFreightCacheVersion();
            }
            int shopTypeCode = shopType.getCode();
            String key = String.format("freight_map_%s", shopTypeCode);
            if (!FREIGHT_CONFIG_DATA.containsKey(key)) {
                doBuildCacheWithProfileConfig(shopType, key);
            }
            if (!FREIGHT_CONFIG_DATA.containsKey(key)) {
                return null;
            }
            return FREIGHT_CONFIG_DATA.get(key);
        } catch (Exception ex) {
            logger.error("FreightMappingUtil执行getFreightMappingByCache构建物流公司映射报错：{}", ex.getMessage(), ex);
            return null;
        }
    }

    private static void doAppendExtraCache(ShopType shopType, List<FreightMapping> freightMappings) {
        if (CollectionUtils.isEmpty(freightMappings)) {
            return;
        }
        String key = String.format("freight_map_%s", shopType.getCode());
        if (!FREIGHT_CONFIG_DATA.containsKey(key)) {
            FREIGHT_CONFIG_DATA.put(key, freightMappings);
            return;
        }
        List<FreightMapping> existMapping = FREIGHT_CONFIG_DATA.get(key);
        if (CollectionUtils.isEmpty(existMapping)) {
            FREIGHT_CONFIG_DATA.put(key, freightMappings);
            return;
        }
        Map<String, List<FreightMapping>> listMap = existMapping.stream().collect(Collectors.groupingBy(FreightMapping::getOnlineCode));
        for (FreightMapping mapping : freightMappings) {
            if (listMap.containsKey(mapping.getOnlineCode())) {
                continue;
            }
            existMapping.add(mapping);
        }
        FREIGHT_CONFIG_DATA.put(key, existMapping);
    }

    private static FreightMapping doGetFreightMappingByOnlineCode(List<FreightMapping> mappingList, String code) {
        if (CollectionUtils.isEmpty(mappingList)) {
            return null;
        }
        List<FreightMapping> collect = mappingList.stream().filter(x -> x.getOnlineCode().equalsIgnoreCase(code)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        if (collect.size() > 1) {
            Optional<FreightMapping> first = collect.stream().filter(x -> x.getOnlineName().equalsIgnoreCase(x.getLocalName())).findFirst();
            if (first.isPresent()) {
                return first.get();
            }
        }
        return collect.get(0);
    }

    private static FreightMapping doGetFreightMappingByLocalCode(List<FreightMapping> mappingList, String code) {
        if (CollectionUtils.isEmpty(mappingList)) {
            return null;
        }
        List<FreightMapping> collect = mappingList.stream().filter(x -> x.getLocalCode().equalsIgnoreCase(code)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        if (collect.size() > 1) {
            Optional<FreightMapping> first = collect.stream().filter(x -> x.getLocalName().equals(x.getOnlineName())).findFirst();
            if (first.isPresent()) {
                return first.get();
            }
        }
        return collect.get(0);
    }

    public static FreightMapping getFreightMapping(ShopType shopType, String code) {
        try {
            List<FreightMapping> mappingList = getFreightMappingByCache(shopType);
            FreightMapping mapping = doGetFreightMappingByOnlineCode(mappingList, code);
            if (mapping != null) {
                return mapping;
            }
            QueryFreightMappingParam mappingParam = new QueryFreightMappingParam();
            mappingParam.setShopType(shopType.getCode());
            List<FreightMapping> freightMappings = profileApi.queryFreightMapping(mappingParam);
            doAppendExtraCache(shopType, freightMappings);
            return doGetFreightMappingByOnlineCode(freightMappings, code);
        } catch (Exception ex) {
            logger.error("FreightMappingUtil执行getFreightMapping查询物流公司映射报错：{}", ex.getMessage(), ex);
        }
        return null;
    }

    public static FreightMapping getFreightMappingByLocalCode(ShopType shopType, String code) {
        try {
            List<FreightMapping> mappingByCache = getFreightMappingByCache(shopType);
            FreightMapping mapping = doGetFreightMappingByLocalCode(mappingByCache, code);
            if (mapping != null) {
                return mapping;
            }
            QueryFreightMappingParam mappingParam = new QueryFreightMappingParam();
            mappingParam.setShopType(shopType.getCode());
            List<FreightMapping> freightMappings = profileApi.queryFreightMapping(mappingParam);
            doAppendExtraCache(shopType, freightMappings);
            return doGetFreightMappingByLocalCode(freightMappings, code);
        } catch (Exception ex) {
            logger.error("FreightMappingUtil执行getLocalFreight查询物流公司映射报错：{}", ex.getMessage(), ex);
        }
        return null;
    }

    public static FreightMapping getFreightMappingByOnlineName(ShopType shopType, String name) {
        try {
            List<FreightMapping> mappingList = getFreightMappingByCache(shopType);
            if (CollectionUtils.isEmpty(mappingList)) {
                return null;
            }
            Optional<FreightMapping> first = mappingList.stream().filter(x -> x.getOnlineName().equalsIgnoreCase(name)).findFirst();
            return first.orElse(null);
        } catch (Exception ex) {
            logger.error("FreightMappingUtil执行getFreightMapping查询物流公司映射报错：{}", ex.getMessage(), ex);
        }
        return null;
    }

    private static void doBuildCacheWithProfileConfig(ShopType shopType, String cacheKey) {
        try {
            String configKey = String.format("platform-freight-%s", shopType.getCode());
            QueryConfigDataRequest request = new QueryConfigDataRequest();
            request.setKey(configKey);
            QueryConfigDataResponse response = profileApi.queryConfigData(request);
            if (!response.getSuccess()) {
                return;
            }
            BizBigConfigData data = response.getData();
            if (data == null) {
                return;
            }
            String value = data.getValue();
            if (StringUtils.isEmpty(value)) {
                return;
            }
            ArrayList<FreightMapping> freightMappings = JsonUtils.toList(value, FreightMapping.class);
            FREIGHT_CONFIG_DATA.put(cacheKey, freightMappings);
        } catch (Exception ex) {
            logger.error("FreightMappingUtil执行doBuildCacheWithProfileConfig查询物流公司映射报错：{}", ex.getMessage(), ex);
        }
    }
}
