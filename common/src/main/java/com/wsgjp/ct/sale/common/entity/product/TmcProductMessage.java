package com.wsgjp.ct.sale.common.entity.product;

import com.wsgjp.ct.sale.common.entity.order.TmcBaseMessage;

/**
 * <AUTHOR>
 * @date 2022-01-19
 */
public class TmcProductMessage extends TmcBaseMessage {
    private int delaySeconds;
    /**
     * 商品id
     */
    private String numId;

    /**
	 * 网店商品/图片发版的申请id，平台返回的
	 */
	private String applyId;

	/**
	 * 网店商品推送的关键信息实体
	 */
	private String notifyJson;

	/**
     * 商品发布唯一标识
     */
    private String platformPublishId;
    /**
     * 单位名称
     */
    private String unitName;
    /**
     * 商品编码
     */
    private String barCode;
    private String type;
    /**
     * 商品在ERP系统的唯一ID
     */
    private String productUniqueId;
    /**
     * 商品在ERP系统的单位编码
     */
    private String productUnitCode;
    private String mappingType;


    public int getDelaySeconds() {
        return delaySeconds;
    }

    public void setDelaySeconds(int delaySeconds) {
        this.delaySeconds = delaySeconds;
    }

    public String getNumId() {
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getProductUniqueId() {
        return productUniqueId;
    }

    public void setProductUniqueId(String productUniqueId) {
        this.productUniqueId = productUniqueId;
    }

    public String getProductUnitCode() {
        return productUnitCode;
    }

    public void setProductUnitCode(String productUnitCode) {
        this.productUnitCode = productUnitCode;
    }

    public String getMappingType() {
        return mappingType;
    }

    public void setMappingType(String mappingType) {
        this.mappingType = mappingType;
	}

	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}

	public String getNotifyJson() {
		return notifyJson;
	}

	public void setNotifyJson(String notifyJson) {
		this.notifyJson = notifyJson;
    }

    public String getPlatformPublishId() {
        return platformPublishId;
    }

    public void setPlatformPublishId(String platformPublishId) {
        this.platformPublishId = platformPublishId;
    }
}
