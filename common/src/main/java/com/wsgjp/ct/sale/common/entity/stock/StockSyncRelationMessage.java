package com.wsgjp.ct.sale.common.entity.stock;

import java.math.BigInteger;

/**
 * <AUTHOR> 2023/5/30 13:51
 */
public class StockSyncRelationMessage {
    private BigInteger profileId;
    private BigInteger otypeId;
    private BigInteger skuId;
    private BigInteger ptypeId;
    private BigInteger unitId;
    private String ptypeName;
    private String xcode;
    private int pcategory;
    private Boolean batchEnabled;
    private int mappingType;
    private String platformNumId;
    private String platformSkuId;

    private String platformXcode;
    private String platformProperties;
    private String defaultSkuId;


    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public BigInteger getPtypeId() {
        if (ptypeId == null) {
            ptypeId = BigInteger.ZERO;
        }
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public BigInteger getUnitId() {
        return unitId;
    }

    public void setUnitId(BigInteger unitId) {
        this.unitId = unitId;
    }

    public int getPcategory() {
        return pcategory;
    }

    public void setPcategory(int pcategory) {
        this.pcategory = pcategory;
    }

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public String getPlatformXcode() {
        return platformXcode;
    }

    public void setPlatformXcode(String platformXcode) {
        this.platformXcode = platformXcode;
    }

    public String getPlatformProperties() {
        return platformProperties;
    }

    public void setPlatformProperties(String platformProperties) {
        this.platformProperties = platformProperties;
    }

    public String getPtypeName() {
        return ptypeName;
    }

    public void setPtypeName(String ptypeName) {
        this.ptypeName = ptypeName;
    }

    public int getMappingType() {
        return mappingType;
    }

    public void setMappingType(int mappingType) {
        this.mappingType = mappingType;
    }

    public String getDefaultSkuId() {
        return defaultSkuId;
    }

    public void setDefaultSkuId(String defaultSkuId) {
        this.defaultSkuId = defaultSkuId;
    }


    public Boolean getBatchEnabled() {
        if (batchEnabled == null) {
            batchEnabled = false;
        }
        return batchEnabled;
    }

    public void setBatchEnabled(Boolean batchEnabled) {
        this.batchEnabled = batchEnabled;
    }
}
