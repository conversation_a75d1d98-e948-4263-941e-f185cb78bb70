package com.wsgjp.ct.sale.common.entity.dto;


import bf.datasource.typehandler.CodeEnum;

public enum OrderOpreateType implements CodeEnum {
    DELETE_ORDER(13, "订单发货流程删除原单"),
    DELIVER_UPDATE(14, "订单发货流程更新原单");
    private int code;
    private String name;

    OrderOpreateType(int index, String name) {
        this.code = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

}
