package com.wsgjp.ct.sale.common.entity.tmc;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.TmcOrderChangeTypeEnum;

import java.math.BigInteger;
import java.util.Map;

/**
 * <AUTHOR> 2024/9/2 17:19
 */
public class TmcOrderChangeMessage {
    private String tradeId;
    private String refundId;
    private BigInteger profileId;
    private BigInteger eshopId;
    private ShopType shopType;
    private TmcOrderChangeTypeEnum changeType;
    private String sellerMemo;
    private String buyerMessage;
    private String sellerFlag;

    private Map<String, String> extraChangeInfo;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public TmcOrderChangeTypeEnum getChangeType() {
        return changeType;
    }

    public void setChangeType(TmcOrderChangeTypeEnum changeType) {
        this.changeType = changeType;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getSellerFlag() {
        return sellerFlag;
    }

    public void setSellerFlag(String sellerFlag) {
        this.sellerFlag = sellerFlag;
    }

    public Map<String, String> getExtraChangeInfo() {
        return extraChangeInfo;
    }

    public void setExtraChangeInfo(Map<String, String> extraChangeInfo) {
        this.extraChangeInfo = extraChangeInfo;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }
}
