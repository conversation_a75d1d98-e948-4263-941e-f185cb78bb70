package com.wsgjp.ct.sale.common.enums;


/**
 * 业务错误码
 * <AUTHOR>
 */
public enum BizErrorCodeEnum {

    /**
     * 未知异常
     */
    UNSPECIFIED("500", "未知异常"),


    /**
     *可发货库存不足异常
     */
    AVAILABLESTOCK_ERROR("10001","可发货库存不足"),

    //账套异常
    PROFILE_YEARPROC_ERROR("1001","账套正在年结存"),
    PROFILE_REBUILD_ERROR("1002","账套正在重建"),
    PROFILE_STOP_ERROR("1003","账套停用"),
    PROFILE_INVALID_ERROR("1004","账套无效"),
    SHARE_DETAIL_MONTHPROC_ERROR("10002","明细月结存异常"),
    //开账异常
    IniOver_STATE_ERROR("2001","账套未开账"),
    IniOver_DATE_ERROR("2002","不允许添加开账月份前的数据，请修改您的单据日期"),

    //会计区间异常
    PERIOD_BALANCE_ERROR("3001","会计区间已结算"),
    PERIOD_RANGE_ERROR("3002","单据日期不在会计区间内"),

    //业务异常
    BIZ_UNALLOW_ERROR("4001","保存异常，不允许过账！"),
    BIZ_CONFIRM_ERROR("4002","保存异常，请确认！"),
    NEG_STOCK_ERROR("4003","负库存异常！"),
    SERIANO_ERROR("4004","序列号异常！"),
    SERIANO_BACK_ERROR("4005","序列号退回异常！"),
    PRICE_ERROR("4006","商品价格异常！"),
    QTY_BACK_ERROR("4007","退回数量异常！"),
    SOURCE_BILL_MODIFY_ERROR("4008","源单据验证异常！"),
    PREFERENTION_SHARE_ERROR("4009","优惠分摊金额异常"),

    ORDER_CREATE_ERROR("4010","订单生单的异常"),

    ORDER_OPERATE_ERROR("4011","订单操作异常"),

    ORDER_GET_ERROR("9014","订单获取异常"),

    PTYPE_PERMISSIONS_ERROR("4012", "商品权限异常"),


    //单据编号异常
    BILLNUMBER_RESPEAT_ERROR("5001","单据编号重复"),

    // 修改删除验证异常
    MODIFY_AND_DELETE_ERROR("9001","修改和删除验证异常！"),

    // 存在超期应收款
    PAYMENT_OVERDUE_RECEIVING("4012","存在超期应收款！"),

    // 存在超期应收款
    PAYMENT_OVERDUE_PAY("4013","存在超期应付款！"),

    OVER_QTY("4014","明细存在超订！"),

    NEG_COST_ERROR("4015","批次负库存异常！！"),

    ORDER_AVAILABLE_STOCK_ERROR("4016","商品可销售库存数量不足"),

    BIZ_INOUT_ERROR("4017","验证出入库异常确认！"),

    SALE_COST_ERROR("4018","出库商品价格异常！"),
    COST_BATCH_ERROR("4019","成本批次异常确认！"),
    INOUT_QTY_ERROR("4020","实物数量异常！"),
    SERIALNO_COST_BATCH_ERROR("4021","序列号成本异常！");
    /**
     * 错误码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    private BizErrorCodeEnum(final String code, final String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码查询枚举。     *     * @param code 编码。     * @return 枚举。
     */
    public static BizErrorCodeEnum getByCode(String code) {
        for (BizErrorCodeEnum value : BizErrorCodeEnum.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return UNSPECIFIED;
    }

    /**
     * 枚举是否包含此code
     *  @param code 枚举code
     *  @return 结果
     */
    public static Boolean contains(String code) {
        for (BizErrorCodeEnum value : BizErrorCodeEnum.values()) {
            if (code.equals(value.getCode())) {
                return true;
            }
        }
        return false;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}

