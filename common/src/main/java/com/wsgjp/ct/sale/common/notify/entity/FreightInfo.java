package com.wsgjp.ct.sale.common.notify.entity;

import java.math.BigInteger;

/**
 * @Author: wcy
 * @Date: 2022/07/26/10:07
 * @Description:
 */
public class FreightInfo {

    private String freightName;
    private String freightCode;
    private String freightBillNo;
    private String freightBtypeId;
    private String tradeOrderId;
    //NONE(0,"无"),
    //    INTERCEPT_ING(1,"拦截中"),
    //    INTERCEPT_PART(2,"部分拦截成功"),
    //    INTERCEPT_SUCCEED(3,"拦截成功"),
    //    INTERCEPT_FAILED(5,"拦截失败"),
    private int interceptStatus;
    private BigInteger otypeId;
    private BigInteger eshopOrderId;
    private String tradeOrderDetailId;

    public String getTradeOrderDetailId() {
        return tradeOrderDetailId;
    }

    public void setTradeOrderDetailId(String tradeOrderDetailId) {
        this.tradeOrderDetailId = tradeOrderDetailId;
    }

    public BigInteger getEshopOrderId() {
        if (eshopOrderId == null) {
            return BigInteger.ZERO;
        }
        return eshopOrderId;
    }

    public void setEshopOrderId(BigInteger eshopOrderId) {
        this.eshopOrderId = eshopOrderId;
    }

    public BigInteger getOtypeId() {
        if (otypeId == null) {
            return BigInteger.ZERO;
        }
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public String getFreightBtypeId() {
        return freightBtypeId;
    }

    public void setFreightBtypeId(String freightBtypeId) {
        this.freightBtypeId = freightBtypeId;
    }

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getFreightCode() {
        return freightCode;
    }

    public void setFreightCode(String freightCode) {
        this.freightCode = freightCode;
    }

    public String getFreightBillNo() {
        return freightBillNo;
    }

    public void setFreightBillNo(String freightBillNo) {
        this.freightBillNo = freightBillNo;
    }

    public int getInterceptStatus() {
        return interceptStatus;
    }

    public void setInterceptStatus(int interceptStatus) {
        this.interceptStatus = interceptStatus;
    }
}
