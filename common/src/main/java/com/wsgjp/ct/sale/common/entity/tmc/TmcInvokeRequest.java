package com.wsgjp.ct.sale.common.entity.tmc;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.ConsumerType;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2021/10/13 16:44
 */
public class TmcInvokeRequest {
	private BigInteger profileId;
	private BigInteger eshopId;
	private String tradeId;
	private TmcNotifyMethodEnum method;
	private ShopType shopType;
	private String message;
	private String approveId;

	private String markBubble;

	/**
	 * 订单取消时当为true时，调用平台接口组EshopOrderCloseFeature接口异步回调，取消结果
	 * false时，直接调用erp改订单状态，同步回告平台取消结果
	 */
	private boolean asynCallback = true;

	private ConsumerType consumerType;

	private TmcNotifyChangeContent notifyChangeContent;
	/**
	 * 自定义订单操作日志
	 */
	private CustomOrderLogMessage customOrderLogMessage;



	public boolean isAsynCallback() {
		return asynCallback;
	}

	public void setAsynCallback(boolean asynCallback) {
		this.asynCallback = asynCallback;
	}

	public String getTradeId() {
		return tradeId;
	}

	public void setTradeId(String tradeId) {
		this.tradeId = tradeId;
	}

	public ShopType getShopType() {
		return shopType;
	}

	public void setShopType(ShopType shopType) {
		this.shopType = shopType;
	}

	public BigInteger getProfileId() {
		return profileId;
	}

	public void setProfileId(BigInteger profileId) {
		this.profileId = profileId;
	}

	public BigInteger getEshopId() {
		return eshopId;
	}

	public void setEshopId(BigInteger eshopId) {
		this.eshopId = eshopId;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public TmcNotifyMethodEnum getMethod() {
		return method;
	}

	public void setMethod(TmcNotifyMethodEnum method) {
		this.method = method;
	}

	public String getMarkBubble() {
		return markBubble;
	}

	public void setMarkBubble(String markBubble) {
		this.markBubble = markBubble;
	}

	public String getApproveId() {
		return approveId;
	}

	public void setApproveId(String approveId) {
		this.approveId = approveId;
	}

	public ConsumerType getConsumerType() {
		return consumerType;
	}

	public void setConsumerType(ConsumerType consumerType) {
		this.consumerType = consumerType;
	}

	public TmcNotifyChangeContent getNotifyChangeContent() {
		return notifyChangeContent;
	}

	public void setNotifyChangeContent(TmcNotifyChangeContent notifyChangeContent) {
		this.notifyChangeContent = notifyChangeContent;
	}

	public CustomOrderLogMessage getCustomOrderLogMessage() {
		return customOrderLogMessage;
	}

	public void setCustomOrderLogMessage(CustomOrderLogMessage customOrderLogMessage) {
		this.customOrderLogMessage = customOrderLogMessage;
	}
}
