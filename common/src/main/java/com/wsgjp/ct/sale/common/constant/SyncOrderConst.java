package com.wsgjp.ct.sale.common.constant;

public class SyncOrderConst {
    public static final String toolTopicName = "eshoporder-syncorder";
    public static final String EshopAuthtoolTopicName = "eshoporder-eshopauthexpirenotice";
    public static final String YuanQiOrderRetry = "eshoporder-yuanqiorderretry";
    public static final String EshopOrderSingle = "eshoporder-sync-single";
    public static final String compensateTopicName = "eshoporder-sync-compensate";
    public static final String TmcTopIcName = "eshoporder-sync-tmc";
    public static final String ESHOP_ORDER_SYNC_TMC_SPECIAL = "eshoporder-sync-tmc-special";

    public static final String TMC_MQ_PRODUCE_TOPIC = "eshoporder-tmc-mq-notify";
    //元气销售订单
    public static final String TmcSalesOrderTag = "sales";
    //元气采购订单
    public static final String TmcPurchaseOrdersTag = "purchase";
    public static final String TMC_REFUND_TOPIC = "tmcRefund";

    public static final String TMC_ORDER_STATUS_CHANGE="tmcForModifyStatus";

    public static final String profileOrderSingleSyncDisabled = "profileOrderSingleSyncDisabled";
    public static final String profileOrderSingleSyncShopTypes = "profileOrderSingleSyncShopTypes";
    public static final String ALL = "all";

    public static final String CHECK_ORDER_LAST_DATE = "plCheckOrderLastTime";

    public static final String REFUND_STATE="refund_state";
    public static final String TRADE_STATE="trade_state";

    public static final String DIRECT_UPDATE_ORDER = "direct_update_order";
}

