package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 13/4/2021 下午 1:54
 */
public class QueryStockRuleDO {

	private BigInteger profileId;
	private BigInteger eshopId;
	private List<SkuMapping> skuMappingList;

	public BigInteger getProfileId() {
		if (profileId == null) {
			return BigInteger.ZERO;
		}
		return profileId;
	}

	public void setProfileId(BigInteger profileId) {
		this.profileId = profileId;
	}

	public BigInteger getEshopId() {
		if (eshopId == null) {
			return BigInteger.ZERO;
		}
		return eshopId;
	}

	public void setEshopId(BigInteger eshopId) {
		this.eshopId = eshopId;
	}

	public List<SkuMapping> getSkuMappingList() {
		return skuMappingList;
	}

	public void setSkuMappingList(List<SkuMapping> skuMappingList) {
		this.skuMappingList = skuMappingList;
	}
}
