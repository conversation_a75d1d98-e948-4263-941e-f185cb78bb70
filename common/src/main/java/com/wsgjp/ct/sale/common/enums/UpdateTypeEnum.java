package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 此枚举需要和平台组保持一致，code是平台组存在changeinfo表里的
 * @date 2020-06-04
 **/
public enum UpdateTypeEnum implements CodeEnum {
    BUYER_MESSAGE(0, "买家留言", "言", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    SELLER_MEMO(1, "线上的卖家备注信息有更新，可点击图标覆盖本地卖家备注", "言", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    RECEIVER(2, "线上的买家地址信息有更新，可点击图标覆盖本地买家地址", "址", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    GOODS(3, "此单据的商品被修改或删除过", "货变", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    INVOICE(4, "发票变更", "票", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    TRADE_STATE(5, "线上交易状态", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    REFUND(6,"售后变更（无用）","", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    BUYER_DESIGNATED_LOGISTICS(7,"买家指定物流公司","", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    BUYER_ADDRESS(8,"买家自助修改地址","址", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    QUICK_SEND(9,"消费者催促尽快发货","催发", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    FREIGHT_PRICE_CHANGE(10,"此单据运费发生变化","费", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    MARK_CHANGE(11,"标记发生变更","", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    FREIGHT_INFO_CHANGE(12,"物流信息发生变化","", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    PAY_BTYPE_ID_CHANGE(13,"往来单位、结算单位发生变化","", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    UPDATE_SYSTEM_MEMO(14,"追加系统备注","", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    MODIFY_SKU(15,"自助修改sku","", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    MODIFY_TIMING(16, "时效时间", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    CONFIRM_INFO(17, "接单信息发生变化", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    GROUP_HEADER_NAME_INFO(18, "团长名称发生变化", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    FREIGHT_INFO_INTERCEPT(19,"物流信息拦截","", Arrays.asList(ModifyScene.SaleOrder_Notify,ModifyScene.Freight_Modify)),
    PLATFORM_QC_AND_IDENTIFY_RESULT(19, "质检信息或鉴定信息发生变化", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    MODIFY_MAIN_OR_DETAIL_TIMING(20, "主表或明细的时效时间发生变更", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    KTYPE_ID_CHANGE(21, "主表或明细的仓库发生变更", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    SIGN_TIME(22, "签收时间发生变更", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    QIC_QUALITY(23, "qic质检信息发生变化", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    PRICE(24, "此单据的金额被修改过", "", Arrays.asList(ModifyScene.SaleOrder_Notify)),
    ;


    private String desc;
    private int code;
    private String name;
    private List<ModifyScene> modifyScene;

    UpdateTypeEnum(int code, String desc, String name, List<ModifyScene> modifyScene) {
        this.desc = desc;
        this.code = code;
        this.name = name;
        this.modifyScene = modifyScene;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return null;
    }

    public List<ModifyScene> getModifyScene() {
        return modifyScene;
    }

    public void setModifyScene(List<ModifyScene> modifyScene) {
        this.modifyScene = modifyScene;
    }
}
