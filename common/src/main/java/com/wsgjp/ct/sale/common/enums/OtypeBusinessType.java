package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * @program: sale
 * @author: tanglan
 * @create: 2022/11/1
 * @description: 店铺类型
 **/
public enum OtypeBusinessType implements CodeEnum {
    ZY(0, "直营"),
    FX(1, "分销"),
    DYY(2, "代运营"),
    JM(3, "加盟");

    private int code;
    private String codeName;

    OtypeBusinessType(int code, String name) {
        this.code = code;
        this.codeName = name;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.codeName;
    }
}
