package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

public class OrderSubmitBatch {
    private BigInteger submitBatchId;
    private BigInteger profileId;
    private BigInteger eshopOrderId;
    private BigInteger otypeId;
    private int detailCount;
    private BigInteger etypeId;
    private String submitBatchno;
    private boolean deleted;
    private Date createTime;
    private Date updateTime;
    private int submitSourceType;

    private List<OrderSubmitBatchDetail> details;

    public BigInteger getSubmitBatchId() {
        return submitBatchId;
    }

    public void setSubmitBatchId(BigInteger submitBatchId) {
        this.submitBatchId = submitBatchId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopOrderId() {
        return eshopOrderId;
    }

    public void setEshopOrderId(BigInteger eshopOrderId) {
        this.eshopOrderId = eshopOrderId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public int getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(int detailCount) {
        this.detailCount = detailCount;
    }

    public BigInteger getEtypeId() {
        return etypeId;
    }

    public void setEtypeId(BigInteger etypeId) {
        this.etypeId = etypeId;
    }

    public String getSubmitBatchno() {
        return submitBatchno;
    }

    public void setSubmitBatchno(String submitBatchno) {
        this.submitBatchno = submitBatchno;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public int getSubmitSourceType() {
        return submitSourceType;
    }

    public void setSubmitSourceType(int submitSourceType) {
        this.submitSourceType = submitSourceType;
    }

    public List<OrderSubmitBatchDetail> getDetails() {
        return details;
    }

    public void setDetails(List<OrderSubmitBatchDetail> details) {
        this.details = details;
    }
}
