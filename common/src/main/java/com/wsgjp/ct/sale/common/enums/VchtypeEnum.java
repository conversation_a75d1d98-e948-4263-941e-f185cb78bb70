package com.wsgjp.ct.sale.common.enums;

/**
 * <AUTHOR>
 * @date 2020-02-20 0:48
 */
public enum VchtypeEnum {
    /**
     * 进货入库单
     */
    Buy(1000, "Buy", "采购入库单","BUY"),
    /**
     * 采购退货单
     */
    BuyBack(1100, "BuyBack", "采购退货单", "BUY"),
    /**
     * 进货订单
     */
    BuyOrder(9000,"BuyOrder","采购订单","BUY"),
    /**
     * 销售订单
     */
    SaleOrder(9001,"SaleOrder","销售订单","SALE"),
    /**
     * 调拨单
     */
    GoodsTrans(3000, "GoodsTrans","内部调拨单","STOCK"),
    /**
     * 报损单
     */
    StockLoss(3302,"StockLoss", "报损单","COST"),
    /**
     * 报溢单
     */
    StockOverflow(3303, "StockOverflow","报溢单","COST");

    private final int value;
    private final String name;
    private final String desc;
    private final String priceType;

    VchtypeEnum(int value, String name, String desc,String priceType) {
        this.value = value;
        this.name = name;
        this.desc = desc;
        this.priceType=priceType;
    }

    public String getName() {
        return name;
    }
    public int getValue() {
        return value;
    }
    public  String getDesc(){return desc;}
    public  String getPriceType(){return priceType;}

    public static VchtypeEnum convertVchtype(int value){
        for (VchtypeEnum item:values()
             ) {
            if(item.getValue()==value){
                return  item;
            }
        }
        return  null;
    }
}
