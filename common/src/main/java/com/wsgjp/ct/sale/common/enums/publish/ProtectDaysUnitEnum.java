package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2023/6/30 11:29
 */
public enum ProtectDaysUnitEnum implements CodeEnum {

    /**
     *  保质期单位类型
     */
    DAY(0,"天"),
    WEE<PERSON>(1,"周"),
    <PERSON>ON<PERSON>(2,"月"),
    YEAR(3,"年");

    private final int code;
    private final String name;

    ProtectDaysUnitEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String toString(){
        return name();
    }
}
