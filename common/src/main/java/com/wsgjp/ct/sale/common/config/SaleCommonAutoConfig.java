package com.wsgjp.ct.sale.common.config;

import com.wsgjp.ct.sale.common.api.ProfileApiForPlatform;
import com.wsgjp.ct.sale.common.syssecretinfo.SysSecretInfoService;
import com.wsgjp.ct.sale.common.utils.BizBigConfigUtil;
import com.wsgjp.ct.sale.common.utils.FreightMappingCacheManger;
import com.wsgjp.ct.sale.common.utils.PlatformAddressUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2022/5/18 17:33
 */
@Configuration
@EnableFeignClients(clients = {ProfileApiForPlatform.class})
@Import({SystemGlobalConfig.class,SaleCommonBizConfig.class,EshopOrderCommonConfig.class, SysSecretInfoService.class})
@MapperScan("com.wsgjp.ct.sale.common.mapper")
public class SaleCommonAutoConfig {

    @Bean
    @ConditionalOnMissingBean
    public FreightMappingCacheManger getFreightCompanyRegister(ProfileApiForPlatform profileApi, SaleCommonBizConfig config) {
        return new FreightMappingCacheManger(profileApi, config);
    }

    @Bean
    @ConditionalOnMissingBean
    public PlatformAddressUtils getPlatformAddressUtils(ProfileApiForPlatform profileApi, SaleCommonBizConfig config) {
        return new PlatformAddressUtils(config, profileApi);
    }

    @Bean
    @ConditionalOnMissingBean
    public BizBigConfigUtil getBizBigConfigUtil(ProfileApiForPlatform profileApi) {
        return new BizBigConfigUtil(profileApi);
    }
}
