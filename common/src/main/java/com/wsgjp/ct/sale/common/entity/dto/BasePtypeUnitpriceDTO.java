package com.wsgjp.ct.sale.common.entity.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/1/8
 */
public class BasePtypeUnitpriceDTO implements Serializable {
    private BigInteger id;

    private BigInteger profileId;

    private BigInteger ptypeId;

    private Integer unitCode;

    private String unitName;

    private BigDecimal unitRate;

    private String barcode;

    private BigDecimal retailPrice;

    private BigDecimal preprice1;

    private BigDecimal preprice2;

    private BigDecimal preprice3;

    private BigDecimal preprice4;

    private BigDecimal preprice5;

    private BigDecimal preprice6;

    private BigDecimal preprice7;

    private BigDecimal preprice8;

    private BigDecimal preprice9;

    private BigDecimal preprice10;

    private BigDecimal buyPrice;

    private BigDecimal minSalePrice;

    private Date createTime;

    private Date updateTime;

    private BigDecimal lastSalePrice;

    private BigDecimal lastBuyPrice;

    private Date lastSaleTime;

    private Date lastBuyTime;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public Integer getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(Integer unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public BigDecimal getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(BigDecimal unitRate) {
        this.unitRate = unitRate;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode == null ? null : barcode.trim();
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getPreprice1() {
        return preprice1;
    }

    public void setPreprice1(BigDecimal preprice1) {
        this.preprice1 = preprice1;
    }

    public BigDecimal getPreprice2() {
        return preprice2;
    }

    public void setPreprice2(BigDecimal preprice2) {
        this.preprice2 = preprice2;
    }

    public BigDecimal getPreprice3() {
        return preprice3;
    }

    public void setPreprice3(BigDecimal preprice3) {
        this.preprice3 = preprice3;
    }

    public BigDecimal getPreprice4() {
        return preprice4;
    }

    public void setPreprice4(BigDecimal preprice4) {
        this.preprice4 = preprice4;
    }

    public BigDecimal getPreprice5() {
        return preprice5;
    }

    public void setPreprice5(BigDecimal preprice5) {
        this.preprice5 = preprice5;
    }

    public BigDecimal getPreprice6() {
        return preprice6;
    }

    public void setPreprice6(BigDecimal preprice6) {
        this.preprice6 = preprice6;
    }

    public BigDecimal getPreprice7() {
        return preprice7;
    }

    public void setPreprice7(BigDecimal preprice7) {
        this.preprice7 = preprice7;
    }

    public BigDecimal getPreprice8() {
        return preprice8;
    }

    public void setPreprice8(BigDecimal preprice8) {
        this.preprice8 = preprice8;
    }

    public BigDecimal getPreprice9() {
        return preprice9;
    }

    public void setPreprice9(BigDecimal preprice9) {
        this.preprice9 = preprice9;
    }

    public BigDecimal getPreprice10() {
        return preprice10;
    }

    public void setPreprice10(BigDecimal preprice10) {
        this.preprice10 = preprice10;
    }

    public BigDecimal getBuyPrice() {
        return buyPrice;
    }

    public void setBuyPrice(BigDecimal buyPrice) {
        this.buyPrice = buyPrice;
    }

    public BigDecimal getMinSalePrice() {
        return minSalePrice;
    }

    public void setMinSalePrice(BigDecimal minSalePrice) {
        this.minSalePrice = minSalePrice;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getLastSalePrice() {
        return lastSalePrice;
    }

    public void setLastSalePrice(BigDecimal lastSalePrice) {
        this.lastSalePrice = lastSalePrice;
    }

    public BigDecimal getLastBuyPrice() {
        return lastBuyPrice;
    }

    public void setLastBuyPrice(BigDecimal lastBuyPrice) {
        this.lastBuyPrice = lastBuyPrice;
    }

    public Date getLastSaleTime() {
        return lastSaleTime;
    }

    public void setLastSaleTime(Date lastSaleTime) {
        this.lastSaleTime = lastSaleTime;
    }

    public Date getLastBuyTime() {
        return lastBuyTime;
    }

    public void setLastBuyTime(Date lastBuyTime) {
        this.lastBuyTime = lastBuyTime;
    }

    @Override
    public String toString() {
        return String.format("BasePtypeUnitpriceDTO{id=%s, profileId=%s, ptypeId=%s, unitCode=%d, unitName='%s', unitRate=%s, barcode='%s', retailPrice=%s, preprice1=%s, preprice2=%s, preprice3=%s, preprice4=%s, preprice5=%s, preprice6=%s, preprice7=%s, preprice8=%s, preprice9=%s, preprice10=%s, buyPrice=%s, minSalePrice=%s, createTime=%s, updateTime=%s, lastSalePrice=%s, lastBuyPrice=%s, lastSaleTime=%s, lastBuyTime=%s}", id, profileId, ptypeId, unitCode, unitName, unitRate, barcode, retailPrice, preprice1, preprice2, preprice3, preprice4, preprice5, preprice6, preprice7, preprice8, preprice9, preprice10, buyPrice, minSalePrice, createTime, updateTime, lastSalePrice, lastBuyPrice, lastSaleTime, lastBuyTime);
    }
}
