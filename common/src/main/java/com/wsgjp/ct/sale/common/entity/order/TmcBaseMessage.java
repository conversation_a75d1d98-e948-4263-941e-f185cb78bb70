package com.wsgjp.ct.sale.common.entity.order;

import ngp.idgenerator.UId;

import java.math.BigInteger;
import java.util.Date;

public class TmcBaseMessage {
    private Date createTime;
    private BigInteger messageId;
    private BigInteger profileId;
    private BigInteger eshopId;
    private BigInteger notifyChangeId;

    public BigInteger getMessageId() {
        if(messageId==null){
            messageId = UId.newId();
        }
        return messageId;
    }

    public void setMessageId(BigInteger messageId) {
        this.messageId = messageId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public BigInteger getNotifyChangeId() {
        return notifyChangeId;
    }
    public void setNotifyChangeId(BigInteger notifyChangeId) {
        this.notifyChangeId = notifyChangeId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
