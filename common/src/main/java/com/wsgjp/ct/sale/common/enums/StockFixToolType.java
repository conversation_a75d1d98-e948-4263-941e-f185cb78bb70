package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2023/6/8 13:41
 */
public enum StockFixToolType implements CodeEnum {
    /**
     * 库存修复工具类型
     */
    AUTO_RE_CALCULATE(0, "自动重算"),
    TOOL_AUTO_FIX(1, "自动修复"),
    API_AUTO_FIX(2,"接口指定重算");


    StockFixToolType(int code, String name){
        this.code = code;
        this.name = name;
    }

    private final int code;
    private final String name;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return name();
    }
}
