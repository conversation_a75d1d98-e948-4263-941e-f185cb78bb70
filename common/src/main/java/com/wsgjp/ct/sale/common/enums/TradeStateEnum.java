package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 功能描述 线上交易状态
 *
 * <AUTHOR>
 * @create 2019-12-23
 * @since 1.0.0
 */
public enum TradeStateEnum implements CodeEnum,PriorityEnum {
    ABNORMAL(0, "未知",0),
    WAIT_BUYER_PAY(1, "未付款",7),
    WAIT_SELLER_SEND_GOODS(2, "已付款",6),
    WAIT_BUYER_CONFIRM_GOODS(3, "已发货",4),
    TRADE_FINISHED(4, "交易成功",2),
    ALL_CLOSED(5, "交易关闭",1),
    SELLER_CONSIGNED_PART(6, "部分发货",3),
    SELLER_PAY_PART(7, "部分付款",5);

    private int code;
    private String desc;
    private int priority;

    TradeStateEnum(int code, String desc,int priority) {
        this.code = code;
        this.desc = desc;
        this.priority = priority;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return desc;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public int getPriority() {
        return priority;
    }
}
