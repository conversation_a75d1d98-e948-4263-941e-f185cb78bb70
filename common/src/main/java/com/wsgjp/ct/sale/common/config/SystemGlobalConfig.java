package com.wsgjp.ct.sale.common.config;

import com.wsgjp.ct.support.annotation.ConfigPrefix;
import com.wsgjp.ct.support.global.BaseConfig;
import com.wsgjp.ct.support.global.annotation.RetailValue;
import com.wsgjp.ct.support.global.annotation.WholesaleValue;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 3/8/2020 下午 5:20
 */
@Component
@ConfigPrefix(prefix = "sysGlobal")
public class SystemGlobalConfig implements BaseConfig {
	@WholesaleValue(value = "false")
	@RetailValue(value = "true")
	private boolean isRetailSaleQtyRule = false;

	@WholesaleValue(value = "false")
	@RetailValue(value = "true")
	private boolean isRetailEshopOrderConfig;

	private String appName = "NGP零售";
	private boolean saleQtyNonPaidOrder;
	private boolean saleQtyErrBusinessOrder;
	private boolean saleQtyConfirmSaleOrder;
	private Integer saleQtyConfirmSaleOrderDays;
	private boolean saleQtyConfirmBuyOrder;
	private Integer saleQtyConfirmBuyOrderDays;
	private boolean saleQtyAuditedBuyOrder;
	private Integer saleQtyAuditedBuyOrderDays;

	public boolean isRetailSaleQtyRule() {
		return isRetailSaleQtyRule;
	}

	public void setRetailSaleQtyRule(boolean retailSaleQtyRule) {
		isRetailSaleQtyRule = retailSaleQtyRule;
	}

	public boolean isRetailEshopOrderConfig() {
		return isRetailEshopOrderConfig;
	}

	public void setRetailEshopOrderConfig(boolean retailEshopOrderConfig) {
		isRetailEshopOrderConfig = retailEshopOrderConfig;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public boolean isSaleQtyNonPaidOrder() {
		return saleQtyNonPaidOrder;
	}

	public void setSaleQtyNonPaidOrder(boolean saleQtyNonPaidOrder) {
		this.saleQtyNonPaidOrder = saleQtyNonPaidOrder;
	}

	public boolean isSaleQtyErrBusinessOrder() {
		return saleQtyErrBusinessOrder;
	}

	public void setSaleQtyErrBusinessOrder(boolean saleQtyErrBusinessOrder) {
		this.saleQtyErrBusinessOrder = saleQtyErrBusinessOrder;
	}

	public boolean isSaleQtyConfirmSaleOrder() {
		return saleQtyConfirmSaleOrder;
	}

	public void setSaleQtyConfirmSaleOrder(boolean saleQtyConfirmSaleOrder) {
		this.saleQtyConfirmSaleOrder = saleQtyConfirmSaleOrder;
	}

	public Integer getSaleQtyConfirmSaleOrderDays() {
		return saleQtyConfirmSaleOrderDays;
	}

	public void setSaleQtyConfirmSaleOrderDays(Integer saleQtyConfirmSaleOrderDays) {
		this.saleQtyConfirmSaleOrderDays = saleQtyConfirmSaleOrderDays;
	}

	public boolean isSaleQtyConfirmBuyOrder() {
		return saleQtyConfirmBuyOrder;
	}

	public void setSaleQtyConfirmBuyOrder(boolean saleQtyConfirmBuyOrder) {
		this.saleQtyConfirmBuyOrder = saleQtyConfirmBuyOrder;
	}

	public Integer getSaleQtyConfirmBuyOrderDays() {
		return saleQtyConfirmBuyOrderDays;
	}

	public void setSaleQtyConfirmBuyOrderDays(Integer saleQtyConfirmBuyOrderDays) {
		this.saleQtyConfirmBuyOrderDays = saleQtyConfirmBuyOrderDays;
	}

	public boolean isSaleQtyAuditedBuyOrder() {
		return saleQtyAuditedBuyOrder;
	}

	public void setSaleQtyAuditedBuyOrder(boolean saleQtyAuditedBuyOrder) {
		this.saleQtyAuditedBuyOrder = saleQtyAuditedBuyOrder;
	}

	public Integer getSaleQtyAuditedBuyOrderDays() {
		return saleQtyAuditedBuyOrderDays;
	}

	public void setSaleQtyAuditedBuyOrderDays(Integer saleQtyAuditedBuyOrderDays) {
		this.saleQtyAuditedBuyOrderDays = saleQtyAuditedBuyOrderDays;
	}
}
