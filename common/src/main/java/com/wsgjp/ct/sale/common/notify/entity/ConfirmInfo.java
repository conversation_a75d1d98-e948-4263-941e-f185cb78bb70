package com.wsgjp.ct.sale.common.notify.entity;

import com.wsgjp.ct.common.enums.core.enums.jarvis.LogisticsStateEnum;
import com.wsgjp.ct.common.enums.core.enums.jarvis.PlatformConfirmStateEnum;

/**
 * @Author: wcy
 * @Date: 2022/07/26/10:07
 * @Description:
 */
public class ConfirmInfo {

    private String pickupCode;
    private String platformDispatcherName;
    private String platformDispatherMobile;
    private LogisticsStateEnum logisticsStatus;
    private PlatformConfirmStateEnum confirmStatus;
    private String tradeOrderId;

    public String getPickupCode() {
        return pickupCode;
    }

    public void setPickupCode(String pickupCode) {
        this.pickupCode = pickupCode;
    }

    public String getPlatformDispatcherName() {
        return platformDispatcherName;
    }

    public void setPlatformDispatcherName(String platformDispatcherName) {
        this.platformDispatcherName = platformDispatcherName;
    }

    public String getPlatformDispatherMobile() {
        return platformDispatherMobile;
    }

    public void setPlatformDispatherMobile(String platformDispatherMobile) {
        this.platformDispatherMobile = platformDispatherMobile;
    }

    public LogisticsStateEnum getLogisticsStatus() {
        return logisticsStatus;
    }

    public void setLogisticsStatus(LogisticsStateEnum logisticsStatus) {
        this.logisticsStatus = logisticsStatus;
    }

    public PlatformConfirmStateEnum getConfirmStatus() {
        return confirmStatus;
    }

    public void setConfirmStatus(PlatformConfirmStateEnum confirmStatus) {
        this.confirmStatus = confirmStatus;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }
}
