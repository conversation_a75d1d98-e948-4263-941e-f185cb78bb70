package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;

public enum InputTypeEnum implements CodeEnum {
    NOT_LIMIT(-1, "不限"),
    SINGLE(0, "单选"),
    MULTI(1, "多选"),
    SWITCH(2, "开关"),
    NUM(3, "数字"),

    TEXT(4, "文本"),
    IMAGE(5, "图片"),
    DATE(6, "日期"),
    SPECIAL_STRUCTURE(7, "特殊结构（另一个多维表笛卡尔积组合）"),
    DATE_YEAR(8, "时间年"),
    DATE_MONTH(9, "时间月"),
    DATE_DAY(10, "时间日"),
    SINGLE_AND_TEXT(11, "单选+文本"),
    MULTI_INPUT(12, "多输入"),
    AREA_CASCADE(13, "地域级联"),
    CASCADE(14, "级联"),
    PERCENT(15, "百分比"),
    TIME_RANGE(16,"时间段"),
    METROLOGICAL_VALUE(17, "度量衡值"),
    Single_METROLOGICAL_VALUE(18, "度量衡值(文本+单选)"),
    DATE_MONTH_RANGE(19, "时间月-范围"),
    CONNECTOR(21,"连接符"),
    CheckBoxList(20,"多选列表")
    ;
    private int index;
    private String name;

    InputTypeEnum(int index, String name) {
        this.index = index;
        this.name = name;
    }

    public static InputTypeEnum getEnumBycode(int code) {
        for (InputTypeEnum inputTypeEnum : InputTypeEnum.values()) {
            if (code == inputTypeEnum.getCode()) {
                return inputTypeEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}
