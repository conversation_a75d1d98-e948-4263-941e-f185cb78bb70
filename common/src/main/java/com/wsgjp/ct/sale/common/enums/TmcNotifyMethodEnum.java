package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021/10/13 16:57
 */
public enum TmcNotifyMethodEnum implements CodeEnum {
    MODIFY_ADDRESS_NOTIFY(0, "仅通知地址变更"),
    MODIFY_ADDRESS_UPDATE(1, "通知并修改地址"),
    AG(2, "极速退款"),
    SPECIFY_LOGISTICS(3, "买家指定物流"),
    EXPEDITE_DELIVERY(4, "催发货"),
    INVOICE(5, "开发票"),
    LOCK_ORDER(6, "锁单"),
    ADVANCE_SALE_SINK(7, "预售下沉"),
    MODIFY_SKU(8, "自助改商品SKU"),

    ORDER_CANCEL(9,"订单取消回告"),
    ORDER_STATUS_CHANGE_BY_SEND_ORDER(10,"同步单号回告订单修改交易状态"),
    ORDER_MODIFY(11,"TMC通知订单变更消息");

    private final int code;
    private final String desc;

    TmcNotifyMethodEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return desc;
    }

    @Override
    public String toString() {
        return name();
    }
}
