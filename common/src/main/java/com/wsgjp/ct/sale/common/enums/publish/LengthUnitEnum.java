package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;

import java.math.BigDecimal;

public enum LengthUnitEnum implements CodeEnum {

    UNIT_MM(0, "mm", BigDecimal.valueOf(0.001)),
    UNIT_CM(1, "cm", BigDecimal.ONE),
    UNIT_M(2, "m", BigDecimal.ONE);

    private final int code;
    private final String name;
    private final BigDecimal desc;

    LengthUnitEnum(int code, String name, BigDecimal desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String toString(){
        return name();
    }
}
