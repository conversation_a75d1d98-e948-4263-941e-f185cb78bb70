package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

public class EshopOrderSubmitDetailPrimaryKey {
    private BigInteger profileId;
    private BigInteger otypeId;
    private String tradeOrderId;
    private String oid;
    private BigInteger batchId;
    private BigDecimal qty;
    private String md5Key;
    private Date createTime;
    private Date updateTime;
    private BigInteger id;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public BigInteger getBatchId() {
        if (null == batchId){
            return BigInteger.ZERO;
        }
        return batchId;
    }

    public void setBatchId(BigInteger batchId) {
        this.batchId = batchId;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getMd5Key() {
        return md5Key;
    }

    public void setMd5Key(String md5Key) {
        this.md5Key = md5Key;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EshopOrderSubmitDetailPrimaryKey that = (EshopOrderSubmitDetailPrimaryKey) o;
        return Objects.equals(md5Key, that.md5Key);
    }

    @Override
    public int hashCode() {
        return Objects.hash(md5Key);
    }
}
