package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

public class OrderSubmitBatchDetail {
    private BigInteger id;
    private BigInteger submitBatchId;
    private BigInteger profileId;
    private BigInteger eshopOrderId;
    private BigInteger otypeId;
    private BigInteger eshopOrderDetailId;
    private String tradeOrderDetailId;
    private BigDecimal qty;
    private Date createTime;
    private Date updateTime;
    private boolean deleted;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getSubmitBatchId() {
        return submitBatchId;
    }

    public void setSubmitBatchId(BigInteger submitBatchId) {
        this.submitBatchId = submitBatchId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopOrderId() {
        return eshopOrderId;
    }

    public void setEshopOrderId(BigInteger eshopOrderId) {
        this.eshopOrderId = eshopOrderId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getEshopOrderDetailId() {
        return eshopOrderDetailId;
    }

    public void setEshopOrderDetailId(BigInteger eshopOrderDetailId) {
        this.eshopOrderDetailId = eshopOrderDetailId;
    }

    public String getTradeOrderDetailId() {
        return tradeOrderDetailId;
    }

    public void setTradeOrderDetailId(String tradeOrderDetailId) {
        this.tradeOrderDetailId = tradeOrderDetailId;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }
}
