package com.wsgjp.ct.sale.common.entity.tmc;


import com.wsgjp.ct.common.enums.core.enums.tmc.TmcNotifyResponseEnum;

/**
 * <AUTHOR>
 * @date 2021/10/13 16:42
 */
public class TmcInvokeResponse {
    private String code = "400";
    private boolean error = false;
    private String message = "";

    public static TmcInvokeResponse error(String code, String message) {
        return new TmcInvokeResponse(code, true, message);
    }

    public static TmcInvokeResponse error(TmcNotifyResponseEnum responseEnum) {
        return new TmcInvokeResponse(responseEnum.getCode(), true, responseEnum.getMessage());
    }

    public static TmcInvokeResponse error(String message) {
        return new TmcInvokeResponse("400", true, message);
    }

    public static TmcInvokeResponse success(String code) {
        return new TmcInvokeResponse(code, false, "success");
    }

    public static TmcInvokeResponse success(TmcNotifyResponseEnum responseEnum) {
        return new TmcInvokeResponse(responseEnum.getCode(), false, responseEnum.getMessage());
    }

    public static TmcInvokeResponse success(String code, String message) {
        return new TmcInvokeResponse(code, false, message);
    }

    public static TmcInvokeResponse success() {
        return new TmcInvokeResponse("200", false, "");
    }

    public TmcInvokeResponse() {
    }

    public TmcInvokeResponse(String code, boolean error, String message) {
        this.code = code;
        this.error = error;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public boolean isError() {
        return error;
    }

    public void setError(boolean error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
