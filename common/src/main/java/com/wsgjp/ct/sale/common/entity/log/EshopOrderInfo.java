package com.wsgjp.ct.sale.common.entity.log;

import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.SelfDeliveryModeEnum;

import java.util.ArrayList;
import java.util.List;

public class EshopOrderInfo {
    /**
     * 订单号
     */
    private String tradeId;
    /**
     * 平台业务标记
     */
    private List<BaseOrderMarkEnum> orderMarks;

    /**
     * 仓库id
     */
    private String stockId;

    private SelfDeliveryModeEnum selfDeliveryMode;

    public String getStockId() {
        return stockId;
    }

    public void setStockId(String stockId) {
        this.stockId = stockId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public List<BaseOrderMarkEnum> getOrderMarks() {
        if (orderMarks == null) {
            orderMarks = new ArrayList<>();
        }
        return orderMarks;
    }

    public void setOrderMarks(List<BaseOrderMarkEnum> orderMarks) {
        this.orderMarks = orderMarks;
    }

    public SelfDeliveryModeEnum getSelfDeliveryMode() {
        return selfDeliveryMode;
    }

    public void setSelfDeliveryMode(SelfDeliveryModeEnum selfDeliveryMode) {
        this.selfDeliveryMode = selfDeliveryMode;
    }
}
