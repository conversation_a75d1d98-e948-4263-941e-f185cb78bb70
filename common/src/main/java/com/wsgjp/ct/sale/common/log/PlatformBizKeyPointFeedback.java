package com.wsgjp.ct.sale.common.log;

import com.wsgjp.ct.sale.common.entity.log.LogOrder;
import com.wsgjp.ct.sale.common.entity.log.OperationStatus;
import com.wsgjp.ct.sale.common.enums.OperationEnum;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * 平台业务关键点回告(平台上传日志等)
 */
public interface PlatformBizKeyPointFeedback {
    /**
     * 批量订单操作日志上报、比如查询订单、打印订单
     * @param orders 订单列表
     * @param operation 操作
     */
    void feedback(List<LogOrder> orders, OperationEnum operation);
    /**
     * 单个订单操作日志上报
     * @param eshopId 店铺id
     * @param tradeId 订单id
     * @param operation 操作
     */
    void feedback(BigInteger eshopId, String tradeId, OperationEnum operation);

    /**
     * SQL操作上报
     * @param eshopId 店铺id
     * @param sql 具体SQL
     */
    void sql(BigInteger eshopId, String sql);

    /**
     * 批量转出订单，例如传输到物流公司、外接WMS、导出报表等等
     * @param orders 订单列表
     * @param url 转出的目标url 如果是物流公司填写接口地址、如果是导出则是报表的url，实在不清楚的先留""
     * @param fileMd5 如果是导出的话传递该字段 其他一律传null
     */
    void transfer(List<LogOrder> orders, String url, String fileMd5);


    /**
     * 同步操作，如果失败则回告接口调用失败
     * @param orders
     * @param operation
     */
    List<OperationStatus> feedBackStatus(List<LogOrder> orders, OperationEnum operation);
}
