package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

public enum TMCType implements CodeEnum {
    Order(0, "订单变更消息"),
    Ptype(1, "商品消息"),
    RefundOrder(2, "售后订单"),
    InvoiceResultReturn(3, "电子发票开票结果"),
    MemberPointAmountChange(4, " 会员积分、余额变更"),
    Test(5, "测试"),
    AGRefund(6, "AG售后退款消息"),
    HiPaC(7, " 海拍客"),
    Invoice_Apply(8, "发票申请"),
    LstRefund(9, "零售通退款消息"),
    YzVipMember(10, "有赞会员"),
    LstSelfOrderShipChange(11, "零售通线下自有订单发货状态变更消息"),
    TMallSuperMarket(12, "天猫超市"),
    TMallSuperMarketRefund(13, "天猫超市销退单"),
    TMallSuperMarketCancel(14, "天猫超市取消交易单"),
    CHANGE_ADDRESS(15, "修改地址"),
    FACILITATE_SEND(16, "催发货"),
    ChANG_INVOICE(17, "发票修改"),
    INTERCEPT_ORDER(18, "自助锁单"),
    PDD_Promise(19, "拼多多承诺信息消息"),
    TradeSuccess(20, "交易成功消息"),
    TradeSellerShip(21, "卖家发货消息"),
    //拼多多：普通订单创建成功后，会触发此消息。定金订单付完尾款后，会产生此消息。
    TradeConfirmed(22, "交易确认消息"),
    TradeMemoModified(23, "交易备注修改消息"),
    SELFHELPMODIFLYADDRESS(24, "自助修改地址"),
    BUYER_DESIGNATED_LOGISTICS(25, "买家指定物流公司"),
    SAVE_TOKEN(26, "保存授权"),
    MODIFY_SKU(27, "自助改商品SKU"),
    YOUXIAN_SEND(28, "优先发货"),
    AUTHORIZATION_CANCEL(29, "授权取消"),
    /**
     * 包含订单卖家备注和旗帜
     */
    MODIFY_SELLER_MEMO(30, "修改卖家备注信息"),
    CREATE_SHIP_ORDER(31,"平台创建发货单"),

    /**
     * 用SupportUtil.doUpdateOrderStatusNotifyByDelayWay，生产者消费者去更改订单状态时，请TmcType赋值33或者34或者35,
     */
    DIRECT_UPDATE_ORDER(32,"直接更新订单"),

    /**
     * 给33 34 35
     */
    DIRECT_UPDATE_MEMO(33, "直接更新卖家旗帜和备注"),
    DIRECT_UPDATE_TRADE_STATUS(34, "直接更新交易状态"),
    DIRECT_UPDATE_REFUND_STATUS(35, "直接更新售后状态"),

    ORDER_RECEIPT(66, "已接单"),

    ALLOW_SHIPMENT(95,"视频号允许发货"),

    PAID_ORDER_PREPROCESSING(96,"微信小店「已支付订单预处理」状态的订单，SKU信息、收件地址等信息存在改变的可能，且可能为空，暂不允许发货，处理完成后才进入待发货。"),


    CUSTOMER_INFORMATION(97,"tmc消息存pl_eshop_notify_change,药九九拿来存放首次开户的信息"),
    /**
     * 针对类似天猫超市，既有卖家备注也有时效，甚至后续会有新的字段推送，需要在构建好实体后，填充一下
     */
    SERIES_FIELD_CHANGE(98,"tmc消息存pl_eshop_notify_change实体字段变更"),

    /**
     * 由于5.6已经有33了改为
     */
    SUBMIT_CREATE_TMC(99,"自动提交生产的tmc消息"),
    ORDER_DISPATCHER(100,"订单改派"),
    ORDER_SUBSIDY_REVIEW_STATUS(101, "订单国补审核状态变更"),
    REFUND_STOP(102,"售后截停"),
    REFUND_STOP_CANCEL(103,"售后截停取消");

    private final int code;
    private final String name;

    TMCType(int code, String name) {
        this.code = code;
        this.name = name;
    }


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
