package com.wsgjp.ct.sale.common.api;

import com.wsgjp.ct.sale.common.entity.BizBigConfigData;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.eshop.NotifyRegisterRequest;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.common.entity.freight.QueryFreightMappingParam;
import com.wsgjp.ct.sale.common.entity.request.QueryConfigDataRequest;
import com.wsgjp.ct.sale.common.entity.response.QueryConfigDataResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR> 2023/12/5 14:35
 */
@Component
@FeignClient(name = "${sys.server.profile:profile-main}", contextId = "common-profile-main")
//@FeignClient(name = "${sys.server.profile:profile-main}", url = "${profile.url:http://localhost:8004}", contextId = "platform-profile-main")
public interface ProfileApiForPlatform {
    /**
     * 保存平台物流公司映射
     * @param mappingList 平台物流公司映射
     */
    @PostMapping("/config/batchSaveFreightMapping")
    void batchSavePlatformFreightMapping(@RequestBody List<FreightMapping> mappingList);

    /**
     * 查询物流公司映射列表
     * @param param 查询参数
     * @return 物流公司映射列表
     */
    @PostMapping("/config/queryFreightMappingList")
    List<FreightMapping> queryFreightMapping(@RequestBody QueryFreightMappingParam param);

    /**
     * 查询平台网店账套映射列表
     * @param request 参数
     * @return 平台网店账套映射
     */
    @PostMapping("/platformProfile/getUnionMappingList")
    List<EshopRegisterNotify> getUnionMappingList(@RequestBody NotifyRegisterRequest request);

    /**
     * 查询大数据配置项
     * @param request 请求参数
     * @return 配置响应
     */
    @PostMapping("/config/queryConfigData")
    QueryConfigDataResponse queryConfigData(@RequestBody QueryConfigDataRequest request);


    @PostMapping("/config/insertConfigData")
    void saveConfigData(@RequestBody BizBigConfigData configData);
}
