package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2024/12/10 15:53
 */
public enum PlatformMainBizType implements CodeEnum {
    /**
     * 平台接口组核心业务场景
     */
    NONE(0,"未知业务"),
    ORDER_DOWNLOAD(1,"订单下载"),
    SEND_ORDER(2,"同步单号（订单发货）"),
    STOCK_SYNC(3,"库存同步"),
    REFUND_DOWNLOAD(4,"售后下载"),;


    private final int code;
    private final String desc;

    PlatformMainBizType(int index,String desc){
        this.code = index;
        this.desc = desc;
    }


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return desc;
    }
}
