package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;
import ngp.utils.StringUtils;

/**
 * <AUTHOR> 2024/7/10 10:33
 */
public enum QmMessageType  implements CodeEnum {
    /**
     * 奇门消息类型
     */
    ORDER(1,"订单"),
    REFUND(2,"售后"),
    GOODS(3,"商品"),
    ORDER_CANCEL(4,"订单取消");

    private final int code;
    private final String name;
    QmMessageType(int code, String name){
        this.code = code;
        this.name = name;
    }
    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }


    public static QmMessageType getByName(String name) {
        if(StringUtils.isEmpty(name)){
            return null;
        }
        for (QmMessageType type : QmMessageType.values()) {
            if (type.name().equals(name)) {
                return type;
            }
            if (type.name().equals(name.toUpperCase())) {
                return type;
            }
        }
        return null;
    }
}
