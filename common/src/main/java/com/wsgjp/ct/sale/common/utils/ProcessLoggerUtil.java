package com.wsgjp.ct.sale.common.utils;

import com.wsgjp.ct.sale.common.enums.ProcessLoggerType;
import com.wsgjp.ct.sale.common.processlogger.ProcessLogger;

/**
 * <AUTHOR>
 */
public class ProcessLoggerUtil {
    /**
     * type:0-新增,1-修改
     */
    public static void appendProgressMsg(ProcessLogger processLogger, String message, ProcessLoggerType loggerType) {
        try {
            if (processLogger != null) {
                if (ProcessLoggerType.APPEND.equals(loggerType)) {
                    processLogger.appendMsg(message);
                } else if (ProcessLoggerType.MODIFY.equals(loggerType)) {
                    processLogger.modifyMsg(message);
                }
            }
        } catch (Exception ex) {

        }
    }

    public static void appendProcess(ProcessLogger processLogger, String message) {
        if (processLogger == null) {
            return;
        }
        processLogger.appendMsg(message);
    }

    public static void modifyProcess(ProcessLogger processLogger, String message) {
        if (processLogger == null) {
            return;
        }
        processLogger.modifyMsg(message);
    }

}
