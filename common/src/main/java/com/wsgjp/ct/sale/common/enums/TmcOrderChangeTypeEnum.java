package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2024/9/2 17:30
 */
public enum TmcOrderChangeTypeEnum implements CodeEnum {
    /**
     * tmc通知订单变更消息类型
     */
    ORDER_MEMO(1,"订单备注"),
    ORDER_STATE(2,"订单状态变更"),
    REFUND_STATE(3,"退款状态变更");

    private final int code;
    private final String name;
    TmcOrderChangeTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }
    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
