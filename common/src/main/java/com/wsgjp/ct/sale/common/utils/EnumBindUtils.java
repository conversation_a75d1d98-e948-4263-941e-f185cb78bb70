package com.wsgjp.ct.sale.common.utils;

import com.wsgjp.ct.sale.common.base.BindParam;

public class EnumBindUtils {
    public static Integer getBindData(Enum t) {
        BindParam annotation = null;
        try {
            annotation = t.getClass().getField(t.name()).getAnnotation(BindParam.class);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
        if (null == annotation) {
            return null;
        }
        return annotation.value();
    }
}
