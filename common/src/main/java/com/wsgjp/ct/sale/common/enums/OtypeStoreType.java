package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * @program: sale
 * @author: tanglan
 * @create: 2022/11/1
 * @description: 业务类型
 **/
public enum OtypeStoreType implements CodeEnum {
    ESHOP(0, "网店"),
    LS_STORE(1, "零售门店"),
    XN_SHOP(2, "虚拟店铺"),
    WholeSale(3, "批发部"),
    OTYPE(4, "分支机构");


    private int code;
    private String codeName;

    OtypeStoreType(int code, String name) {
        this.code = code;
        this.codeName = name;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.codeName;
    }
}
