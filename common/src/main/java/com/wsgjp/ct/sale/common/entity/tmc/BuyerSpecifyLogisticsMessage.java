package com.wsgjp.ct.sale.common.entity.tmc;

import com.wsgjp.ct.common.enums.core.enums.ShopType;

/**
 * tmc 买家指定物流消息类
 *
 * <AUTHOR>
 * @Date 2021/10/21 14:14
 */
public class BuyerSpecifyLogisticsMessage {

    private ShopType shopType;
    /**
     * 物流名称
     */
    private String freightName;
    /**
     * 物流编码
     */
    private String freightCode;
    /**
     * 主订单id
     */
    private String orderId;
    /**
     * 卖家账号
     */
    private String sellerId;
    /**
     * 卖家名称
     */
    private String sellerNick;

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public String getFreightCode() {
        return freightCode;
    }

    public void setFreightCode(String freightCode) {
        this.freightCode = freightCode;
    }

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }
}
