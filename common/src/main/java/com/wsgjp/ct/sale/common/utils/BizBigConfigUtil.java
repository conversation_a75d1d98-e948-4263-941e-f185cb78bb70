package com.wsgjp.ct.sale.common.utils;

import com.wsgjp.ct.sale.common.api.ProfileApiForPlatform;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> 2024/9/25 9:46
 */
public class BizBigConfigUtil {

    private static final Logger logger = LoggerFactory.getLogger(BizBigConfigUtil.class);
    private static final ConcurrentHashMap<String, String> MAP = new ConcurrentHashMap<>();

    public BizBigConfigUtil(ProfileApiForPlatform profileApi) {

    }
}
