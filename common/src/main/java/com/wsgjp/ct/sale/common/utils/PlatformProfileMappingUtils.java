package com.wsgjp.ct.sale.common.utils;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.api.ProfileApiForPlatform;
import com.wsgjp.ct.sale.common.config.EshopOrderCommonConfig;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.eshop.NotifyRegisterRequest;
import ngp.service.component.job.support.BeanUtils;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> 2024/6/17 10:50
 */
public class PlatformProfileMappingUtils {

    private static final Logger logger = LoggerFactory.getLogger(PlatformProfileMappingUtils.class);
    private static final ConcurrentHashMap<String, PlatformProfileCache> PROFILE_CACHE_DATA = new ConcurrentHashMap<>();

    public static List<EshopRegisterNotify> getRegisterNotifyList(String shopAccount, ShopType shopType) {
        String key = String.format("%s_%s", shopAccount, shopType.getCode());
        EshopOrderCommonConfig config = BeanUtils.getContext().getBean(EshopOrderCommonConfig.class);
        if (PROFILE_CACHE_DATA.containsKey(key)) {
            PlatformProfileCache profileCache = PROFILE_CACHE_DATA.get(key);
            Date expireTime = profileCache.getExpireTime();
            if (StringUtils.equals(config.getTmcCacheVersion(),profileCache.getVersion())
                    && DateUtils.getDate().before(expireTime)) {
                return profileCache.getRegisterNotifyList();
            } else {
                PROFILE_CACHE_DATA.remove(key);
            }
        }
        initCache(shopAccount, shopType, config);
        if (PROFILE_CACHE_DATA.containsKey(key)) {
            return PROFILE_CACHE_DATA.get(key).getRegisterNotifyList();
        }
        return new ArrayList<>();
    }

    private static void initCache(String shopAccount, ShopType shopType, EshopOrderCommonConfig config) {
        try {
            ProfileApiForPlatform api = BeanUtils.getContext().getBean(ProfileApiForPlatform.class);
            NotifyRegisterRequest req = new NotifyRegisterRequest();
            req.setProductId(config.getProductId());
            req.setCode(shopAccount);
            req.setType(shopType.getCode());
            List<EshopRegisterNotify> notifyList = api.getUnionMappingList(req);
            if (CollectionUtils.isEmpty(notifyList)) {
                return;
            }
            PlatformProfileCache platformProfileCache = new PlatformProfileCache();
            Date nowDate = DateUtils.getDate();
            Date cacheDate = DateUtils.addSeconds(nowDate, config.getSaleBizProfileCacheSeconds());
            platformProfileCache.setVersion(config.getTmcCacheVersion());
            platformProfileCache.setExpireTime(cacheDate);
            platformProfileCache.setRegisterNotifyList(notifyList);
            String key = String.format("%s_%s", shopAccount, shopType.getCode());
            PROFILE_CACHE_DATA.put(key, platformProfileCache);
        } catch (Exception ex) {
            logger.error("店铺类型：{}，shopAccount：{}查询对应账套时报错:{}", shopType.getName(), shopAccount, ex.getMessage(), ex);
        }
    }

    static class PlatformProfileCache {
        private String version;
        private Date expireTime;
        private List<EshopRegisterNotify> registerNotifyList;

        public Date getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(Date expireTime) {
            this.expireTime = expireTime;
        }

        public List<EshopRegisterNotify> getRegisterNotifyList() {
            return registerNotifyList;
        }

        public void setRegisterNotifyList(List<EshopRegisterNotify> registerNotifyList) {
            this.registerNotifyList = registerNotifyList;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }
}
