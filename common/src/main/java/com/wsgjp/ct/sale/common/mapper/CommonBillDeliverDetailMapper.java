package com.wsgjp.ct.sale.common.mapper;

import com.wsgjp.ct.sale.common.entity.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.common.entity.dto.DeliverFreightSimpleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * @Description TODO
 * @Date 2023-09-12 14:53
 * @Created by lingxue
 */
@Repository
@Mapper
public interface CommonBillDeliverDetailMapper {
    List<BillDeliverDetailDTO> getSendDetailsByOrderId(@Param("profileId") BigInteger profileId, @Param("orderIds") List<BigInteger> orderIds);

    List<DeliverFreightSimpleEntity> getDeliverFreight(@Param("profileId") BigInteger profileId, @Param("deliverVchcodeList") List<BigInteger> deliverVchcodeList);
}
