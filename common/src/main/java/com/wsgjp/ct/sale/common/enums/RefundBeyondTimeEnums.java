package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021/10/13 16:57
 */
public enum RefundBeyondTimeEnums implements CodeEnum {
    SYS_PROMISED_CONFIRM_TIME_BEYOND(0, "系统审核超时"),
    PROMISED_CONFIRM_TIME_BEYOND(1, "售后申请审核超时"),
    PROMISED_AGREE_TIME_BEYOND(2, "退款超时"),
    PROMISED_DELIVER_TIME_BEYOND(3, "补发超时"),
    PROMISED_RECEIVE_TIME(4, "收货超时");

    private final int code;
    private final String desc;

    RefundBeyondTimeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return desc;
    }

    @Override
    public String toString() {
        return name();
    }
}
