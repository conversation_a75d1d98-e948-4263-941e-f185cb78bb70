package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum QcResultType implements CodeEnum {
    NONE(0,""),
    NoQualityInspection(5,"未质检"),
    QualityInspectionPassed(1,"通过"),
    FailsToPassQualityInspection(2,"未通过"),
    ThereAreFlaws(3,"通过，但存在瑕疵"),

    StartQualityInspection(4,"开始质检");

    private int index;

    private String name;

    QcResultType(int index, String name) {
        this.index = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return index;
    }

    public String getName() {
        return name;
    }
}
