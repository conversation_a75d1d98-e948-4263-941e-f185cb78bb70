package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * 日志上传操作枚举
 */
public enum OperationEnum implements CodeEnum {
    /**
     * 订单操作枚举
     */
    Select_Order(0,"订单查询操作"),
    Modify_Order(1,"修改订单操作"),
    Delete_Order(2,"删除订单操作"),
    Import_Order(3,"导出订单操作"),
    Print_Order(4,"打印订单操作"),
    Push_Third(5,"推送第三方操作"),
    Login(6,"登陆操作"),
    Database(7,"数据库操作"),
    IDAAS(8,"IdaaS日志回传"),
    AUTHENTICATION_LOG(8,"二次认证日志回传"),
    TRANSFER_SAVE(9,"订单保存"),
    TRANSFER_AUDIT(10,"订单审核"),
    TRANSFER_PRINT(11,"订单配货"),
    TRANSFER_CHECKED(12,"订单验货"),
    TRANSFER_WRIGHT(13,"订单称重"),
    TRANSFER_SENDED(14,"订单发货"),
    DECRYPT(15,"订单解密"),
    DOWNLOAD_ORDER(16,"下载订单"),
    MODIFY_ORDER_KTYPE(17,"订单审核修改发货仓库"),
    PERSISTENCE_ORDER(18,"订单入库持久化")
    ;
    private int index;
    private String name;

    OperationEnum(int index,String name){
        this.index = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }

}
