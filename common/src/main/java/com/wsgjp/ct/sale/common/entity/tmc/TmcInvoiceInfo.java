package com.wsgjp.ct.sale.common.entity.tmc;

import java.math.BigInteger;

public class TmcInvoiceInfo {
    private BigInteger vchcode;

    private BigInteger profileId;
    /**
     * 发票id
     */
    private BigInteger id;
    /**
     * 发票类型  0,电子普通 1,电子增值,2.纸质普通,3.纸质增值
     */
    private int invoiceType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 税号
     */
    private String invoiceTax;
    /**
     * 开票单位 0,个人 1企业
     */
    private int invoiceUserType;
    /**
     * 是否需要开票
     */
    private boolean needInvoice;
    /**
     * 开票状态
     */
    private int invoiceState;
    /**
     * 发票备注
     */
    private String memo;

    /**
     * 开户银行
     */
    private String bank;


    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 单位地址
     */
    private String address;

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public int getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(int invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(String invoiceTax) {
        this.invoiceTax = invoiceTax;
    }

    public int getInvoiceUserType() {
        return invoiceUserType;
    }

    public void setInvoiceUserType(int invoiceUserType) {
        this.invoiceUserType = invoiceUserType;
    }

    public boolean isNeedInvoice() {
        return needInvoice;
    }

    public void setNeedInvoice(boolean needInvoice) {
        this.needInvoice = needInvoice;
    }

    public int getInvoiceState() {
        return invoiceState;
    }

    public void setInvoiceState(int invoiceState) {
        this.invoiceState = invoiceState;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
