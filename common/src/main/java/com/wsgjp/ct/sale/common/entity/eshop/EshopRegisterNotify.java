package com.wsgjp.ct.sale.common.entity.eshop;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class EshopRegisterNotify {
    /**
     * 线上店铺ID(shopAccount)
     */
    private String code;
    /**
     * 账套ID
     */
    private BigInteger profileId;
    /**
     * 店铺类型
     */
    private int type;
    /**
     * 店铺ID（管家婆）
     */
    private BigInteger id;
    /**
     * 产品ID(NGP零售：66、NGP批发：88)
     */
    private int productId;
    private String createTime;
    private String updateTime;
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
