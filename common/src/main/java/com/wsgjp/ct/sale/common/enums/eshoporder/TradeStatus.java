package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;


/**
 * 交易状态
 */
public enum TradeStatus implements CodeEnum {

    /**
     * 全部
     */
    ABNORMAL(0, "未知"),
    WAIT_BUYER_PAY(1, "未付款"),
    WAIT_SELLER_SEND_GOODS(2, "已付款"),
    WAIT_BUYER_CONFIRM_GOODS(3, "已发货"),
    TRADE_FINISHED(4, "交易成功"),
    ALL_CLOSED(5, "交易关闭"),
    SELLER_CONSIGNED_PART(6, "部分发货"),
    SELLER_PAY_PART(7, "部分付款"),
    All(8, "全部");


    private int flag;

    private String name;

    TradeStatus(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public static TradeStatus valueOf(int code) {
        for (TradeStatus tradeStatus : values()) {
            if (tradeStatus.getCode() == code) {
                return tradeStatus;
            }
        }
        return TradeStatus.ABNORMAL;
    }

    public static TradeStatus nameValueOf(String name) {
        for (TradeStatus tradeStatus : values()) {
            if (name.equals(tradeStatus.getName())) {
                return tradeStatus;
            }
        }
        return null;
    }
}
