package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;
/**
 * 销售属性维度
 * 或者说销售属性等级
 *
 * <AUTHOR>
 */
public enum AttrDimEnum implements CodeEnum {
    ANY(-1,"不限"),
    ONE_DIM(1, "一维消息属性"),
    TWO_DIM(2, "二维消息属性"),
    THREE_DIM(3, "三维消息属性"),
    FOUR_DIM(4, "四维消息属性"),
    FIVE_DIM(5, "五维消息属性"),
    SIX_DIM(6, "六维消息属性"),
    SEVEN_DIM(7, "七维消息属性"),
    EIGHT_DIM(8, "八维消息属性"),
    NINE_DIM(9, "九维消息属性"),
    TEN_DIM(10, "十维消息属性");
    private final Integer code;
    private final String desc;

    AttrDimEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String getName() {
        return CodeEnum.super.getName();
    }

    public static AttrDimEnum valueOf(int code) {
        AttrDimEnum[] var1 = values();
        int var2 = var1.length;
        for (int var3 = 0; var3 < var2; ++var3) {
            AttrDimEnum s = var1[var3];
            if (s.code == code) {
                return s;
            }
        }
        return AttrDimEnum.ONE_DIM;
    }
}
