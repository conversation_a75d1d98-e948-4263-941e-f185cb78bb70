package com.wsgjp.ct.sale.common.notify.entity;


import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 7/7/2020 下午 3:58
 */
public class NotifyMainAndDetailChangeDto {
	private TradeStatus tradeState;
	private Date payTime;
	private Date finishTime;
	private BigInteger vchcode;
	private String tradeId;
	private List<DeliverBillDetailTradeStatusDTO> details;
	//买家已付金额
	private BigDecimal buyerPaidTotal = BigDecimal.ZERO;
	//买家未付金额
	private BigDecimal buyerUnpaidTotal = BigDecimal.ZERO;
	//最晚发货时间
	private Date promisedSendTime;
	private BigInteger ktypeId;

	/**
	 *质检机构id
	 */
	private String platformQualityOrgId;
	/**
	 *质检机构名称
	 */
	private String platformQualityOrgName;
	/**
	 *质检仓库编码
	 */
	private String platformQualityWarehouseCode;
	/**
	 *质检仓库名称
	 */
	private String platformQualityWarehouseName;


	public String getPlatformQualityOrgId() {
		if (null == platformQualityOrgId){
			return "";
		}
		return platformQualityOrgId;
	}

	public void setPlatformQualityOrgId(String platformQualityOrgId) {
		this.platformQualityOrgId = platformQualityOrgId;
	}

	public String getPlatformQualityOrgName() {
		if (null == platformQualityOrgName){
			return "";
		}
		return platformQualityOrgName;
	}

	public void setPlatformQualityOrgName(String platformQualityOrgName) {
		this.platformQualityOrgName = platformQualityOrgName;
	}

	public String getPlatformQualityWarehouseCode() {
		if (null == platformQualityWarehouseCode){
			return "";
		}
		return platformQualityWarehouseCode;
	}

	public void setPlatformQualityWarehouseCode(String platformQualityWarehouseCode) {
		this.platformQualityWarehouseCode = platformQualityWarehouseCode;
	}

	public String getPlatformQualityWarehouseName() {
		if (null == platformQualityWarehouseName){
			return "";
		}
		return platformQualityWarehouseName;
	}

	public void setPlatformQualityWarehouseName(String platformQualityWarehouseName) {
		this.platformQualityWarehouseName = platformQualityWarehouseName;
	}

	public BigInteger getKtypeId() {
		return ktypeId;
	}

	public void setKtypeId(BigInteger ktypeId) {
		this.ktypeId = ktypeId;
	}

	public Date getPromisedSendTime() {
		return promisedSendTime;
	}

	public void setPromisedSendTime(Date promisedSendTime) {
		this.promisedSendTime = promisedSendTime;
	}

	public TradeStatus getTradeState() {
		return tradeState;
	}

	public void setTradeState(TradeStatus tradeState) {
		this.tradeState = tradeState;
	}

	public Date getPayTime() {
		return payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	public BigInteger getVchcode() {
		return vchcode;
	}

	public void setVchcode(BigInteger vchcode) {
		this.vchcode = vchcode;
	}

	public List<DeliverBillDetailTradeStatusDTO> getDetails() {
		return details;
	}

	public void setDetails(List<DeliverBillDetailTradeStatusDTO> details) {
		this.details = details;
	}

	public String getTradeId() {
		return tradeId;
	}

	public void setTradeId(String tradeId) {
		this.tradeId = tradeId;
	}

	public Date getFinishTime() {
		return finishTime;
	}

	public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}

	public BigDecimal getBuyerPaidTotal() {
		if(buyerPaidTotal == null)
		{
			return BigDecimal.ZERO;
		}
		return buyerPaidTotal;
	}

	public void setBuyerPaidTotal(BigDecimal buyerPaidTotal) {
		this.buyerPaidTotal = buyerPaidTotal;
	}

	public BigDecimal getBuyerUnpaidTotal() {
		if(buyerUnpaidTotal == null)
		{
			return BigDecimal.ZERO;
		}
		return buyerUnpaidTotal;
	}

	public void setBuyerUnpaidTotal(BigDecimal buyerUnpaidTotal) {
		this.buyerUnpaidTotal = buyerUnpaidTotal;
	}
}
