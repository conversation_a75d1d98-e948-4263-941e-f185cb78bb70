package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;
public enum ReturnSyncStateEnum implements CodeEnum {
    NO_SYNC(0, "未回传"),
    SUCCESS_SYNC(1, "回传成功"),
    FAIL_SYNC(2, "回传失败");

    private final int code;
    private final String desc;

    ReturnSyncStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return desc;
    }

    @Override
    public String toString() {
        return name();
    }
}
