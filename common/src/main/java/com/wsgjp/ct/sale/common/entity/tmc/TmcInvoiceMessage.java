package com.wsgjp.ct.sale.common.entity.tmc;

public class TmcInvoiceMessage {

    private String orderId;
    private String sellerId;
    private String sellerNick;

    /**
     * 发票属性 0：公司，1：个人
     */
    private Integer invoiceUserType;

    /**
     * 发票类型
     */
    private Integer invoiceType;
    private String invoiceTitle;
    private String invoiceTax;
    private String bank;
    private String banAccount;
    private String phone;
    private String address;

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public Integer getInvoiceUserType() {
        return invoiceUserType;
    }

    public void setInvoiceUserType(Integer invoiceUserType) {
        this.invoiceUserType = invoiceUserType;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(String invoiceTax) {
        this.invoiceTax = invoiceTax;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBanAccount() {
        return banAccount;
    }

    public void setBanAccount(String banAccount) {
        this.banAccount = banAccount;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
