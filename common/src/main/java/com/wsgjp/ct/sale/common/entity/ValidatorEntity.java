package com.wsgjp.ct.sale.common.entity;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;


/**
 * 验证器实体
 *
 * <AUTHOR>
 */
@XmlRootElement(name = "validator")
@XmlAccessorType(XmlAccessType.FIELD)
public class ValidatorEntity {

    @XmlAttribute(name = "key")
    private String key;

    @XmlAttribute(name = "name")
    private String name;

    @XmlAttribute(name = "action")
    private String action;

    @XmlAttribute(name = "issys")
    private Boolean isSys;

    @XmlAttribute(name = "validationtype")
    private String validationType;

    @XmlAttribute(name = "priority")
    private int priority;

    @XmlAttribute(name = "detailtype")
    private String detailtype;

    @XmlAttribute(name = "isNull")
    private boolean isNull;

    @XmlAttribute(name = "isPerm")
    private boolean isPerm;

    @XmlAttribute(name = "validationPropertyName")
    private String validationPropertyName;

    /**
     * 是否是单据设置项
     */
    private boolean isBillSetting;

    /**
     * 单据设置项key
     */
    private String billSettingKey;

    public String getValidationPropertyName() {
        if (validationPropertyName == null) {
            validationPropertyName = "";
        }
        return validationPropertyName;
    }

    public boolean getPerm() {
        return isPerm;
    }

    public void setPerm(boolean perm) {
        isPerm = perm;
    }

    public void setValidationPropertyName(String validationPropertyName) {
        this.validationPropertyName = validationPropertyName;
    }

    public boolean getNull() {
        return isNull;
    }

    public void setNull(boolean aNull) {
        isNull = aNull;
    }

    public String getDetailtype() {
        return detailtype;
    }

    public void setDetailtype(String detailtype) {
        this.detailtype = detailtype;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Boolean getSys() {
        if (isSys == null) {
            isSys = false;
        }
        return isSys;
    }

    public void setSys(Boolean sys) {
        isSys = sys;
    }

    public String getValidationType() {
        return validationType;
    }

    public void setValidationType(String validationType) {
        this.validationType = validationType;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean isBillSetting() {
        return isBillSetting;
    }

    public void setBillSetting(boolean billSetting) {
        isBillSetting = billSetting;
    }

    public String getBillSettingKey() {
        return billSettingKey;
    }

    public void setBillSettingKey(String billSettingKey) {
        this.billSettingKey = billSettingKey;
    }
}
