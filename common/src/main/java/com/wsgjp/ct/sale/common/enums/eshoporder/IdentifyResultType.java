package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;

public enum IdentifyResultType implements CodeEnum {
    NONE(0,""),
    NotIdentified(10,"未鉴别"),
    Really(1,"鉴别为真"),
    False(2,"鉴别为假"),
    ThereAreFlaws(3,"鉴定存在瑕疵"),
    StartIdentification(4,"开始鉴别");

    IdentifyResultType(Integer code,String name) {
        this.code = code;
        this.name = name;
    }
    private Integer code;
    private String name;


    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
