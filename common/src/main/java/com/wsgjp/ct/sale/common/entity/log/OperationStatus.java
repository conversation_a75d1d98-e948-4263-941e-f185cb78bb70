package com.wsgjp.ct.sale.common.entity.log;

import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.ShopType;

import java.math.BigInteger;

public class OperationStatus {

    private String tradeId;
    private Boolean status;
    private String message;
    private BigInteger profileId;
    private BigInteger eShopId;
    private ShopType shopType;
    private BaseOrderMarkEnum baseOrderMarkEnum;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger geteShopId() {
        return eShopId;
    }

    public void seteShopId(BigInteger eShopId) {
        this.eShopId = eShopId;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BaseOrderMarkEnum getBaseOrderMarkEnum() {
        return baseOrderMarkEnum;
    }

    public void setBaseOrderMarkEnum(BaseOrderMarkEnum baseOrderMarkEnum) {
        this.baseOrderMarkEnum = baseOrderMarkEnum;
    }
}
