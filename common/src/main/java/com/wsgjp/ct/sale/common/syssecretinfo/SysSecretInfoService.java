package com.wsgjp.ct.sale.common.syssecretinfo;

import com.wsgjp.ct.sale.common.mapper.SysSecretInfoMapper;
import com.wsgjp.ct.sis.client.SisClient;
import com.wsgjp.ct.sis.client.entity.EncryptFullAdapter;
import com.wsgjp.ct.support.context.CurrentUser;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.Collections;
import java.util.List;
@Service
public class SysSecretInfoService {
    private SysSecretInfoMapper sysSecretInfoMapper;

    public SysSecretInfoService(SysSecretInfoMapper sysSecretInfoMapper) {
        this.sysSecretInfoMapper = sysSecretInfoMapper;
    }

    /**
     * 加密并保存数据
     * @param secretInfo
     */
    public void encryptAndSaveSysSecretInfo(BaseSysSecretInfo secretInfo) {
        if (secretInfo.getProfileId() == null || secretInfo.getProfileId().compareTo(BigInteger.ZERO) == 0) {
            secretInfo.setProfileId(CurrentUser.getProfileId());
        }
        doEncryptSysSecretInfo(Collections.singletonList(secretInfo));
        BaseSysSecretInfo sysSecretInfo = sysSecretInfoMapper.querySecretInfoByHash(secretInfo.getProfileId(),secretInfo.getHashMark());
        if (null == sysSecretInfo) {
            sysSecretInfoMapper.insertSecretInfo(secretInfo);
        }else {
            secretInfo.setId(sysSecretInfo.getId());
        }
    }

    /**
     * 仅加密数据
     * @param secretInfos
     */
    public void doEncryptSysSecretInfo(List<EncryptFullAdapter> secretInfos)
    {
        try {
            if (secretInfos != null && !secretInfos.isEmpty()) {
                SisClient.batchEncrypt(secretInfos);
            }
        } catch (RuntimeException ex) {
            throw new RuntimeException(String.format("系统信息加密失败:%s", ex.getMessage()), ex);
        }
    }

    /**
     * 批量解密数据
     * @param secretInfos
     */
    public  <T extends EncryptFullAdapter> void decryptSysSecretInfo(List<T> secretInfos) {
        try {
            if (secretInfos != null && !secretInfos.isEmpty()) {
                List<EncryptFullAdapter> encryptFullAdapters = (List<EncryptFullAdapter>) secretInfos;
                SisClient.batchDecrypt(encryptFullAdapters);
            }
        } catch (RuntimeException ex) {
            throw new RuntimeException(String.format("系统信息解密失败:%s", ex.getMessage()), ex);
        }
    }

//    /**
//     * 批量解密数据
//     * @param secretInfos
//     */
//    public void decryptSysSecretInfo(List<EncryptFullAdapter> secretInfos) {
//        try {
//            if (secretInfos != null && !secretInfos.isEmpty()) {
//                SisClient.batchDecrypt(secretInfos);
//            }
//        } catch (RuntimeException ex) {
//            throw new RuntimeException(String.format("系统信息解密失败:%s", ex.getMessage()), ex);
//        }
//    }
    /**
     * 单个解密数据
     * @param secretInfo
     */
    public void decryptSysSecretInfoSingle(EncryptFullAdapter secretInfo) {
        try {
            if (secretInfo != null ) {
                SisClient.batchDecrypt(Collections.singletonList(secretInfo));
            }
        } catch (RuntimeException ex) {
            throw new RuntimeException(String.format("系统信息解密失败:%s", ex.getMessage()), ex);
        }
    }


    public List<BaseSysSecretInfo> queryBaseSysSecretInfoList(BigInteger profileId,List<BigInteger> ids)
    {
        return sysSecretInfoMapper.querySecretInfoByIds(profileId,ids);
    }
    public BaseSysSecretInfo querySecretInfoById(BigInteger profileId, BigInteger id){
        return sysSecretInfoMapper.querySecretInfoById(profileId,id);
    }

    public BaseSysSecretInfo querySecretInfoBySecretDi(BigInteger profileId, String di){
        return sysSecretInfoMapper.querySecretInfoBySecretDi(profileId,di);
    }

    public BaseSysSecretInfo querySecretInfoByParam(SecretInfoHandleParam queryParam){
        return sysSecretInfoMapper.querySecretInfoByParam(queryParam);
    }
    public void deleteSecretInfoByParam(SecretInfoHandleParam queryParam){
        sysSecretInfoMapper.deleteSecretInfoByParam(queryParam);

    }
    public void deleteSecretInfoByIds(BigInteger profileId,List<String> ids){
        sysSecretInfoMapper.deleteSecretInfoByIds(profileId,ids);
    }
}
