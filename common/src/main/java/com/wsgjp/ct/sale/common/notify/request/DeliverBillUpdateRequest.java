package com.wsgjp.ct.sale.common.notify.request;

import com.wsgjp.ct.common.enums.core.enums.ShopType;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-06-04
 **/
public class DeliverBillUpdateRequest extends SaleOrderChangeDAO implements Serializable  {
    private ShopType shopType;
    //买家自助修改地址时，是否需要变更buyerId
    private Boolean needModifyBuyerAddressDb = true;

    public Boolean getNeedModifyBuyerAddressDb() {
        return needModifyBuyerAddressDb;
    }

    public void setNeedModifyBuyerAddressDb(Boolean needModifyBuyerAddressDb) {
        this.needModifyBuyerAddressDb = needModifyBuyerAddressDb;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }
}
