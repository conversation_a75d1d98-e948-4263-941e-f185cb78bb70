package com.wsgjp.ct.sale.common.utils;

import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;

public class RedisProcessMessageUtils {

    public static void appendProcess(RedisProcessMessage redisLogger, String message) {
        if (redisLogger == null) {
            return;
        }
        redisLogger.doAppendMsg(message);
    }

    public static void modifyProcess(RedisProcessMessage redisLogger, String message) {
        if (redisLogger == null) {
            return;
        }
        try {
            redisLogger.doModifyMsg(message);
        } catch (RuntimeException ex) {
        }
    }
}
