package com.wsgjp.ct.sale.common.mapper;

import com.wsgjp.ct.sale.common.syssecretinfo.BaseSysSecretInfo;
import com.wsgjp.ct.sale.common.syssecretinfo.SecretInfoHandleParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * @Description 123
 * @Date 2024-11-06 14:53
 * @Created by kangyu
 */
@Repository
@Mapper
public interface SysSecretInfoMapper {
    /*
    增，删，改，查
     */

    void insertSecretInfo(BaseSysSecretInfo secretInfo);
    void batchInsertSecretInfo(@Param("secretInfoList") List<BaseSysSecretInfo> secretInfoList);

    /**
     *
     * @param profileId
     * @param id
     */
    BaseSysSecretInfo querySecretInfoById(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);
    BaseSysSecretInfo querySecretInfoByHash(@Param("profileId") BigInteger profileId, @Param("hashMark") String hashMark);
    List<BaseSysSecretInfo> querySecretInfoByIds(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);

    BaseSysSecretInfo querySecretInfoBySecretDi(BigInteger profileId, String di);

    BaseSysSecretInfo querySecretInfoByParam(@Param("queryParam") SecretInfoHandleParam queryParam);
    void deleteSecretInfoByParam(@Param("queryParam") SecretInfoHandleParam queryParam);
    void deleteSecretInfoByIds(BigInteger profileId,List<String> ids);
}
