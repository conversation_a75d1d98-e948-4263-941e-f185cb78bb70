package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2024/12/10 15:50
 */
public enum MonitorSourceEnum  implements CodeEnum {
    /**
     * 操作来源
     */
    NONE(0,"未知操作",PlatformApiEnum.NONE),
    MANUAL_DOWNLOAD_ORDER(1,"手工下单",PlatformApiEnum.DOWNLOAD_ORDER_FULL),
    MANUAL_DOWNLOAD_ORDER_BY_ID(2,"手工下单-根据单号",PlatformApiEnum.DOWNLOAD_ORDER_DETAIL),
    MANUAL_MODIFY_ORDER(3,"手工更新订单",PlatformApiEnum.DOWNLOAD_ORDER_DETAIL),
    MANUAL_MODIFY_ORDER_BY_REFUND(4,"手工更新售后单",PlatformApiEnum.DOWNLOAD_ORDER_DETAIL),
    DOWNLOAD_ORDER_BY_REFUND(5,"下载售后过程中补单",PlatformApiEnum.DOWNLOAD_ORDER_DETAIL),
    MANUAL_DOWNLOAD_ORDER_RE_TRY(6,"手工执行补单重试",PlatformApiEnum.DOWNLOAD_ORDER_INCREASE),
    AUTO_DOWNLOAD(7,"自动下载订单",PlatformApiEnum.DOWNLOAD_ORDER_INCREASE),
    AUTO_DOWNLOAD_RE_TRY(8,"自动下载订单-重试",PlatformApiEnum.DOWNLOAD_ORDER_INCREASE),
    COMPENSATE_ORDER_INCREASE(9,"增量补单",PlatformApiEnum.DOWNLOAD_ORDER_INCREASE),
    COMPENSATE_ORDER_FULL(10,"全量补单",PlatformApiEnum.DOWNLOAD_ORDER_FULL),
    TMC_ORDER(11,"TMC下单",PlatformApiEnum.DOWNLOAD_ORDER_DETAIL),
    TMC_REFUND(101,"TMC下载售后单",PlatformApiEnum.DOWNLOAD_REFUND_DETAIL),
    MANUAL_DOWNLOAD_REFUND_WHEN_ORDER_DOWNLOAD(103,"手工下单-补充下载售后",PlatformApiEnum.DOWNLOAD_REFUND_FULL),
    AUTO_DOWNLOAD_REFUND_WHEN_ORDER_DOWNLOAD(104,"自动下单-补充下载售后",PlatformApiEnum.DOWNLOAD_REFUND_INCREASE),
    MANUAL_DOWNLOAD_REFUND(102,"手工下载售后单",PlatformApiEnum.DOWNLOAD_REFUND_FULL),
    WMS_REFUND_CHECK(105,"售后检查",PlatformApiEnum.DOWNLOAD_ORDER_DETAIL);


    private final int code;
    private final String name;

    private final PlatformApiEnum api;

    MonitorSourceEnum(int index, String name, PlatformApiEnum api){
        this.code = index;
        this.name = name;
        this.api = api;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public PlatformMainBizType getBizType() {
        return api.getBizType();
    }

    public String getApiName() {
        return api.getName();
    }

    public PlatformApiEnum getApi() {
        return api;
    }
}
