package com.wsgjp.ct.sale.common.entity.middle.pojo;


import java.math.BigInteger;
import java.util.Date;

/**
 * 仓库信息实体
 */
public class KtypeInfo {

    private BigInteger id;
    private BigInteger profileId;
    private String typeid;
    private String partypeid;
    private String usercode;
    private String fullname;
    private String shortname;
    private String namepy;
    private Integer scategory;
    private Integer classed;
    private Integer stoped;
    private Integer deleted;
    private BigInteger rowindex;
    private String memo;
    private Integer sysrow;
    private Integer customsType;
    private Integer bondedType;
    private Date createTime;
    private Date updateTime;
    private Integer stockType;
    private Integer stockState;
    private BigInteger btypeId;
    private Integer blongType;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getTypeid() {
        return typeid;
    }

    public void setTypeid(String typeid) {
        this.typeid = typeid;
    }

    public String getPartypeid() {
        return partypeid;
    }

    public void setPartypeid(String partypeid) {
        this.partypeid = partypeid;
    }

    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getShortname() {
        return shortname;
    }

    public void setShortname(String shortname) {
        this.shortname = shortname;
    }

    public String getNamepy() {
        return namepy;
    }

    public void setNamepy(String namepy) {
        this.namepy = namepy;
    }

    public Integer getScategory() {
        return scategory;
    }

    public void setScategory(Integer scategory) {
        this.scategory = scategory;
    }

    public Integer getClassed() {
        return classed;
    }

    public void setClassed(Integer classed) {
        this.classed = classed;
    }

    public Integer getStoped() {
        return stoped;
    }

    public void setStoped(Integer stoped) {
        this.stoped = stoped;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public BigInteger getRowindex() {
        return rowindex;
    }

    public void setRowindex(BigInteger rowindex) {
        this.rowindex = rowindex;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getSysrow() {
        return sysrow;
    }

    public void setSysrow(Integer sysrow) {
        this.sysrow = sysrow;
    }

    public Integer getCustomsType() {
        return customsType;
    }

    public void setCustomsType(Integer customsType) {
        this.customsType = customsType;
    }

    public Integer getBondedType() {
        return bondedType;
    }

    public void setBondedType(Integer bondedType) {
        this.bondedType = bondedType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getStockType() {
        return stockType;
    }

    public void setStockType(Integer stockType) {
        this.stockType = stockType;
    }

    public Integer getStockState() {
        return stockState;
    }

    public void setStockState(Integer stockState) {
        this.stockState = stockState;
    }

    public BigInteger getBtypeId() {
        return btypeId;
    }

    public void setBtypeId(BigInteger btypeId) {
        this.btypeId = btypeId;
    }

    public Integer getBlongType() {
        return blongType;
    }

    public void setBlongType(Integer blongType) {
        this.blongType = blongType;
    }
}
