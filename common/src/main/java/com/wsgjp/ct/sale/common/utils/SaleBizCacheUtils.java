package com.wsgjp.ct.sale.common.utils;


import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> 2024/6/25 15:37
 */
public class SaleBizCacheUtils {

    private static final ConcurrentMap<String, BizCacheData> CACHE_MAP = new ConcurrentHashMap<>();
    private static final Logger logger = LoggerFactory.getLogger(SaleBizCacheUtils.class);

    public static Object get(String key) {
        try {
            if (!CACHE_MAP.containsKey(key)) {
                return null;
            }
            BizCacheData data = CACHE_MAP.get(key);
            if (data.getExpireTime().before(DateUtils.getDate())) {
                CACHE_MAP.remove(key);
                return null;
            }
            return data.getData();
        } catch (Exception ex) {
            logger.error("SaleBizCacheUtils get cache error", ex);
            return null;
        }
    }

    public static void put(String key, Object data, int expireSeconds) {
        try {
            BizCacheData bizCacheData = new BizCacheData();
            bizCacheData.expireTime = DateUtils.addSeconds(DateUtils.getDate(), expireSeconds);
            bizCacheData.data = data;
            CACHE_MAP.put(key, bizCacheData);
        } catch (Exception ex) {
            logger.error("SaleBizCacheUtils put cache error,key:{}, data:{}, ex:{}", key, JsonUtils.toJson(data), ex.getMessage(), ex);
        }
    }

    static class BizCacheData {
        private Date expireTime;
        private Object data;

        public Date getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(Date expireTime) {
            this.expireTime = expireTime;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }
    }
}
