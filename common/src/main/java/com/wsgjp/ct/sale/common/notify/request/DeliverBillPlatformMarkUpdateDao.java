package com.wsgjp.ct.sale.common.notify.request;

import com.wsgjp.ct.common.enums.core.entity.DeliverNewMark;
import com.wsgjp.ct.sale.common.enums.MarkUpdateType;
import com.wsgjp.ct.sale.common.notify.entity.DeliverBillDetailPlatformMarkUpdateDao;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className DeliverBillPlatformMarkUpdateDao
 */
public class DeliverBillPlatformMarkUpdateDao implements Serializable {
    private BigInteger profileId;
    private String tradeOrderId;
    private List<DeliverNewMark> newPlatformMarks;
    private List<DeliverNewMark> deletedPlatformMarks;
    private List<DeliverBillDetailPlatformMarkUpdateDao> details;
    private List<BigInteger> deletedCustomMarkIds;
    private List<BigInteger> newCustomMarkIds;
    private MarkUpdateType markUpdateType;
    private BigInteger eshopOrderId;
    private BigInteger vchcode;
    /**
     * 交易类型
     *0--普通
     *1--预售-按计划发
     *2--预售-有货就发
     *3--周期购
     *4--团购
     *5--虚拟服务
     */
    private int tradeType;

    public int getTradeType() {
        return tradeType;
    }

    public void setTradeType(int tradeType) {
        this.tradeType = tradeType;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public MarkUpdateType getMarkUpdateType() {
        return markUpdateType;
    }

    public void setMarkUpdateType(MarkUpdateType markUpdateType) {
        this.markUpdateType = markUpdateType;
    }

    public List<BigInteger> getDeletedCustomMarkIds() {
        return deletedCustomMarkIds;
    }

    public void setDeletedCustomMarkIds(List<BigInteger> deletedCustomMarkIds) {
        this.deletedCustomMarkIds = deletedCustomMarkIds;
    }

    public List<BigInteger> getNewCustomMarkIds() {
        return newCustomMarkIds;
    }

    public void setNewCustomMarkIds(List<BigInteger> newCustomMarkIds) {
        this.newCustomMarkIds = newCustomMarkIds;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public List<DeliverNewMark> getNewPlatformMarks() {
        return newPlatformMarks;
    }

    public void setNewPlatformMarks(List<DeliverNewMark> newPlatformMarks) {
        this.newPlatformMarks = newPlatformMarks;
    }

    public List<DeliverNewMark> getDeletedPlatformMarks() {
        return deletedPlatformMarks;
    }

    public void setDeletedPlatformMarks(List<DeliverNewMark> deletedPlatformMarks) {
        this.deletedPlatformMarks = deletedPlatformMarks;
    }

    public List<DeliverBillDetailPlatformMarkUpdateDao> getDetails() {
        return details;
    }

    public void setDetails(List<DeliverBillDetailPlatformMarkUpdateDao> details) {
        this.details = details;
    }

    public BigInteger getEshopOrderId() {
        return eshopOrderId;
    }

    public void setEshopOrderId(BigInteger eshopOrderId) {
        this.eshopOrderId = eshopOrderId;
    }
}
