package com.wsgjp.ct.sale.common.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wsgjp.ct.sale.common.base.BaseExcelVo;
import com.wsgjp.ct.sale.common.base.ExcelModel;
import com.wsgjp.ct.sale.common.enums.MessageTypeEnum;
import com.wsgjp.ct.support.utils.RedisBizUtils;
import ngp.tracing.config.TracerLocal;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.util.*;

public class ProcessUtil {
    private static ThreadLocal<String> localProcessKey = new ThreadLocal<>();
    private static ThreadLocal<Integer> localPercent = new ThreadLocal<>();
    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessUtil.class);
    // 30分钟
    private final static int EXRP_MINUT = 30 * 60;

    public static void setLocalProcessKey(String key) {
        localProcessKey.set(key);
    }

    public static boolean canComplete() {
        return !StringUtils.isEmpty(localProcessKey.get());
    }

    public static String getLocalProcessKey() {
        return getLocalProcessKey(false);
    }

    public static String getLocalProcessKey(boolean reset) {
        String key = localProcessKey.get();
        if (StringUtils.isEmpty(key) || reset) {
            String requestId = RedisBizUtils.getRequestId();
            localProcessKey.set(requestId);
            key = requestId;
            LOGGER.debug("ProcessUtil.setLocalProcessKey(String key)未调用,系统自动生成localProcessKey=" + requestId);
        }
        return key;
    }

    public static void appendInfo(String message) {
        appendInfo(message, null, null);
    }

    public static void appendInfo(String message, Integer percent) {
        appendInfo(message, percent, null);
    }

    public static void appendInfo(String message, Integer percent, Object data) {
        append(message, percent, data, MessageTypeEnum.INFO);
    }

    public static void appendError(String message) {
        appendError(message, null, null);
    }

    public static void appendError(String message, Integer percent) {
        appendError(message, percent, null);
    }

    public static void appendError(String message, Integer percent, Object data) {
        append(message, percent, data, MessageTypeEnum.ERROR);
    }

    public static void appendWarn(String message) {
        appendWarn(message, null, null);
    }

    public static void appendWarn(String message, Integer percent) {
        appendWarn(message, percent, null);
    }

    public static void appendWarn(String message, Integer percent, Object data) {
        append(message, percent, data, MessageTypeEnum.WARN);
    }

    public static void appendSuccess(String message) {
        appendSuccess(message, null, null);
    }

    public static void appendSuccess(String message, Integer percent) {
        appendSuccess(message, percent, null);
    }

    public static void appendSuccess(String message, Integer percent, Object data) {
        append(message, percent, data, MessageTypeEnum.SUCCESS);
    }

    public static void appendDebug(String message) {
        appendDebug(message, null, null);
    }

    public static void appendDebug(String message, Integer percent) {
        appendDebug(message, percent, null);
    }

    public static void appendDebug(String message, Integer percent, Object data) {
        append(message, percent, data, MessageTypeEnum.DEBUG);
    }

    /**
     * 存储进度信息列表信息的key
     *
     * @return
     */
    private static String getProcessMessageListKey() {
        String key = getLocalProcessKey();
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("localProcessKey不能为空");
        }
        return RedisBizUtils.getCacheKeyContainsProfileId(ProcessUtil.class, "processlist", key);
    }

    /**
     * 清空进度
     *
     * @param key
     */
    public static void clearProcess(String key) {
        setLocalProcessKey(key);
        String cachekey = getProcessMessageListKey();
        RedisBizUtils.lclear(cachekey);
    }

    /**
     * 存储当前进度信息
     *
     * @return
     */
    private static String getCurrentContextKey() {
        String key = getLocalProcessKey();
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("localProcessKey不能为空");
        }
        return RedisBizUtils.getCacheKeyContainsProfileId(ProcessUtil.class, "processCorrent", key);
    }

    private static String getListContextKey() {
        String key = getLocalProcessKey();
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("localProcessKey不能为空");
        }
        return RedisBizUtils.getCacheKeyContainsProfileId(ProcessUtil.class, "listContext", key);
    }

    public synchronized static void append(String message, Integer percent, Object data, MessageTypeEnum messageTypeEnum) {
        CurrentProcess currentProcess = new CurrentProcess();
        currentProcess.setCompleted(false);
        if (percent == null) {
            percent = localPercent.get();
            if (percent == null) {
                percent = Integer.valueOf(0);
            }
        }
        currentProcess.setPercent(percent);
        currentProcess.setData(data);
        currentProcess.setMessage(message);
        currentProcess.setMessageType(messageTypeEnum);
        currentProcess.setTraceId(TracerLocal.getTracerId());

        ProcessMessage processMessage = new ProcessMessage();
        processMessage.setMessage(message);
        processMessage.setMessageType(messageTypeEnum);

        if (percent != null) {
            localPercent.set(percent);
        }

        RedisBizUtils.setObject(getCurrentContextKey(), currentProcess);
        RedisBizUtils.lpush(getProcessMessageListKey(), JsonUtils.toJson(processMessage));
        //超时设置
        cacheExpire();
    }

    public synchronized static void complete(Object data) {
        complete(data, MessageTypeEnum.INFO);
    }

    public synchronized static void complete(Object data, MessageTypeEnum messageTypeEnum) {
        CurrentProcess currentProcess = new CurrentProcess();
        currentProcess.setCompleted(true);
        currentProcess.setPercent(100);
        currentProcess.setData(data);
        currentProcess.setMessage("完成");
        currentProcess.setMessageType(messageTypeEnum);
        currentProcess.setTraceId(TracerLocal.getTracerId());
        //存储完成进度信息
        //超时设置
        cacheExpire();
        RedisBizUtils.setObject(getCurrentContextKey(), currentProcess);
        removeThreadLocal();
    }

    private static void removeThreadLocal() {
        localProcessKey.remove();
        localPercent.remove();
    }

    /**
     * 收集错误结果
     *
     * @param data
     * @param message
     */
    public static <T extends BaseExcelVo> void addResultError(T data, String message) {
        addResult(data, message, MessageTypeEnum.ERROR);
    }

    public final static String EXCEL_ERROR_MESSAGE = "errorMessage";

    public static <T extends ExcelModel> void putResultError(T data, String message) {
        ResultContext context = new ResultContext();
        data.getCustomProperties().put(EXCEL_ERROR_MESSAGE, message);
        context.setData(data);
        context.setMessage(message);
        context.setMessageType(MessageTypeEnum.ERROR);
        context.setDataClassName(data.getClass().getName());
        RedisBizUtils.lpush(getListContextKey(), JsonUtils.toJson(context));
    }

    /**
     * 收集数据结果
     *
     * @param data
     * @param message
     */
    public static <T extends BaseExcelVo> void addResult(T data, String message, MessageTypeEnum messageTypeEnum) {
        ResultContext context = new ResultContext();
        data.setMessage(message);
        context.setData(data);
        context.setMessage(message);
        context.setMessageType(messageTypeEnum);
        context.setDataClassName(data.getClass().getName());
        RedisBizUtils.lpush(getListContextKey(), JsonUtils.toJson(context));
    }

    /**
     * 数据结果集合
     *
     * @param processKey
     * @return
     */
    public static List<ResultContext> getListContext(String processKey) {
        setLocalProcessKey(processKey);
        //todo 设置过期时间
        List<String> list = RedisBizUtils.lrange(getListContextKey(), 0, -1);
        Collections.reverse(list);
        List<ResultContext> reslist = new ArrayList<>();
        for (String item : list) {
            ResultContext obj = JsonUtils.toObject(item, ResultContext.class);
            reslist.add(obj);
        }
        return reslist;
    }

    /**
     * 数据结果集合，first:对象类型，second:列表数据实体
     *
     * @param processKey
     * @return
     * @throws ClassNotFoundException
     */
    public static Pair<Class, List<Object>> getListResult(String processKey) throws ClassNotFoundException {
        setLocalProcessKey(processKey);
        //todo 设置过期时间
        List<String> list = RedisBizUtils.lrange(getListContextKey(), 0, -1);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Collections.reverse(list);
        List<Object> reslist = new ArrayList<>();

        Class clazz = null;
        for (String item : list) {
            ResultContext obj = JsonUtils.toObject(item, ResultContext.class);
            if (clazz == null) {
                String className = obj.getDataClassName();
                clazz = Class.forName(className);
            }
            reslist.add(obj.getData(clazz));
        }
        if (clazz == null) {
            return null;
        }
        return Pair.of(clazz, reslist);
    }

    /**
     * 设置缓存超时
     */
    private static void cacheExpire() {
        RedisBizUtils.expire(getCurrentContextKey(), EXRP_MINUT);
        RedisBizUtils.expire(getProcessMessageListKey(), EXRP_MINUT);
    }

    /**
     * 获取当前全部进程状况
     *
     * @param processKey
     * @return
     */
    public synchronized static CurrentProcess getAllProcess(String processKey) {
        return getProcess(processKey, 0);
    }

    /**
     * 获取当前进程状况
     *
     * @param processKey
     * @param index
     * @return
     */
    public synchronized static CurrentProcess getProcess(String processKey, long index) {
        //设置当前processKey
        setLocalProcessKey(processKey);

        try {
            CurrentProcess currentProcess = RedisBizUtils.getObjectEx(getCurrentContextKey(), CurrentProcess.class);
            if (currentProcess == null) {
                return null;
            }
            List<ProcessMessage> messages = new ArrayList<>();
            List<String> list = RedisBizUtils.lrangeEx(getProcessMessageListKey(), 0, -(index + 1));
            Collections.reverse(list);
            for (String item : list) {
                ProcessMessage message = JsonUtils.toObject(item, ProcessMessage.class);
                messages.add(message);
            }
            currentProcess.setList(messages);
            return currentProcess;
        } catch (Exception ex) {
            LOGGER.warn("ProcessUtil.getProcess(String processKey,long index) err:" + ex.getMessage(), ex);
            String message = "缓存服务器异常，无法查看执行结果";
            CurrentProcess currentProcess = new CurrentProcess();
            currentProcess.setCompleted(true);
            currentProcess.setPercent(100);
            currentProcess.setMessage(message);
            currentProcess.setData("CacheError");
            currentProcess.setMessageType(MessageTypeEnum.WARN);
            ProcessMessage processMessage = new ProcessMessage();
            processMessage.setMessage(message);
            processMessage.setMessageType(MessageTypeEnum.WARN);
            currentProcess.setList(Arrays.asList(processMessage));
            return currentProcess;
        }

    }

    public static <T extends BaseExcelVo> void editExcelPropertyValue(Class<T> clazz, String fieldName, String columnName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            ExcelProperty excel = field.getAnnotation(ExcelProperty.class);
            InvocationHandler excelH = Proxy.getInvocationHandler(excel);
            Field excelF = excelH.getClass().getDeclaredField("memberValues");
            excelF.setAccessible(true);
            Map excelValues = (Map) excelF.get(excelH);
            excelValues.put("value", new String[]{columnName});
            LOGGER.info("反射修改列名注解成功：");
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            LOGGER.error("反射修改列名注解异常：" + e.getMessage(), e);
        }
    }

    public static <T extends BaseExcelVo> void setNecessaryExportInformation(T excelVo, String fieldName, String columnName, int index) {
        //设置必要导出列
        List<String> list = excelVo.getIncludeColumn();
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        list.add(fieldName);
        excelVo.setIncludeColumn(list);
        //设置表头的名称
        Map<Integer, String> headMap = excelVo.getHeadMap();
        if (headMap == null) {
            headMap = new HashMap<>();
        }
        headMap.put(index, columnName);
        excelVo.setHeadMap(headMap);
    }

    public static <T extends BaseExcelVo> void setExcelVoExcludeColumns(T excelVo, String fieldName) {
        List<String> list = excelVo.getExcludeColumns();
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(fieldName);
        excelVo.setExcludeColumns(list);
    }

    public static class ResultContext {
        private MessageTypeEnum messageType;
        private String message;
        private String dataClassName;
        private Object data;

        public MessageTypeEnum getMessageType() {
            return messageType;
        }

        public void setMessageType(MessageTypeEnum messageType) {
            this.messageType = messageType;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }

        public String getDataClassName() {
            return dataClassName;
        }

        public void setDataClassName(String dataClassName) {
            this.dataClassName = dataClassName;
        }

        public <T> T getData(Class<T> clazz) {
            return JsonUtils.toObject(JsonUtils.toJson(this.data), clazz);
        }
    }

    public static class ProcessMessage {
        private MessageTypeEnum messageType;
        private String message;

        public MessageTypeEnum getMessageType() {
            return messageType;
        }

        public void setMessageType(MessageTypeEnum messageType) {
            this.messageType = messageType;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    public static class CurrentProcess {
        //0-100
        private Integer percent;
        private Boolean completed;
        private String className;
        private Object data;
        private String message;
        private MessageTypeEnum messageType;
        private String traceId;
        private List<ProcessMessage> list;

        public Integer getPercent() {
            return percent;
        }

        public void setPercent(Integer percent) {
            this.percent = percent;
        }

        public Boolean getCompleted() {
            return completed;
        }

        public <T> T getData(Class<T> clazz) {
            return JsonUtils.toObject(JsonUtils.toJson(this.data), clazz);
        }

        public void setCompleted(Boolean completed) {
            this.completed = completed;
        }

        public List<ProcessMessage> getList() {
            return list;
        }

        public void setList(List<ProcessMessage> list) {
            this.list = list;
        }


        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }

        public String getClassName() {
            return className;
        }

        public void setClassName(String className) {
            this.className = className;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public MessageTypeEnum getMessageType() {
            return messageType;
        }

        public void setMessageType(MessageTypeEnum messageType) {
            this.messageType = messageType;
        }

        public String getTraceId() {
            return traceId;
        }

        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }
    }
}
