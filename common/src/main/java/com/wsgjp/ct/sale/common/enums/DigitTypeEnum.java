package com.wsgjp.ct.sale.common.enums;

/**
 * Create by liuyu
 */
public enum DigitTypeEnum {

    NONE(0, "无"),
    PRICE(1,"单价"),
    TOTAL(2,"金额"),
    QTY(3,"数量"),
    DISCOUNT(4,"折扣");

    private int code;
    private String name;

    DigitTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code + "";
    }

//    public void setCode(int code) {
//        this.code = code;
//    }

    public String getName() {
        return name;
    }

//    public void setName(String name) {
//        this.name = name;
//    }
}
