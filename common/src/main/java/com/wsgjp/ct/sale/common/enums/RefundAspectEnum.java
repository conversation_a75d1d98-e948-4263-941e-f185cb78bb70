package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

public enum RefundAspectEnum implements CodeEnum {
    WAIT_AUDIT(1, "待审核"),
    RETURN_GOODS_WAIT_USER_RETURN(2, "退款退货待用户退货"),
    RETURN_GOODS_WAIT_CONFIRM_GOODS (3, "退款退货待确认收货"),
    EXCHANGE_GOODS_WAIT_USER_RETURN(4, "换货待用户退货"),
    EXCHANGE_GOODS_WAI_CONFIRM_GOODS(5, "换货待确认收货"),
    EXCHANGE_GOODS_WAIT_SENDER_SEND(6, "换货待卖家发货"),
    ;
    private int code;
    private String desc;
    RefundAspectEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public int getCode() {
        return 0;
    }

    @Override
    public String getName() {
        return CodeEnum.super.getName();
    }
}
