package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

public enum RefundPlatformOperation implements CodeEnum {
    doRefundAgree(0, "同意仅退款"),
    doRefundRefuse(1, "拒绝仅退款"),
    doReturnAgree(2, "同意退货"),
    doReturnRefuse(3, "拒绝退货"),
    doReturnRefundAgree(4, "已退货并同意退款"),
    doReturnRefundRefuse(5, "已退货并拒绝退款"),
    doExchangeAgree(6, "同意换货"),
    doExchangeRefuse(7, "拒绝换货"),
    doReturnExchangeAgree(8, "已退货并完成换货"),
    doReturnExchangeRefuse(9, "已退货并拒绝换货"),
    doProcessAgree(10, "同意补发"),
    doProcessRefuse(11, "拒绝补发"),
    ;
    private int code;
    private String name;

    RefundPlatformOperation(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
