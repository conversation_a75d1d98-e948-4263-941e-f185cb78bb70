package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 13/4/2021 下午 1:50
 */
public class StockRuleDTO {

	private BigInteger profileId;
	private BigInteger ruleId;
	private String ruleName;
	private String xcode;
	private BigInteger skuId;
	private BigInteger ptypeId;
	private int pcategory;

	public BigInteger getProfileId() {
		return profileId;
	}

	public void setProfileId(BigInteger profileId) {
		this.profileId = profileId;
	}

	public BigInteger getRuleId() {
		return ruleId;
	}

	public void setRuleId(BigInteger ruleId) {
		this.ruleId = ruleId;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getXcode() {
		return xcode;
	}

	public void setXcode(String xcode) {
		this.xcode = xcode;
	}

	public int getPcategory() {
		return pcategory;
	}

	public void setPcategory(int pcategory) {
		this.pcategory = pcategory;
	}

	public BigInteger getSkuId() {
		return skuId;
	}

	public void setSkuId(BigInteger skuId) {
		this.skuId = skuId;
	}

	public BigInteger getPtypeId() {
		return ptypeId;
	}

	public void setPtypeId(BigInteger ptypeId) {
		this.ptypeId = ptypeId;
	}
}
