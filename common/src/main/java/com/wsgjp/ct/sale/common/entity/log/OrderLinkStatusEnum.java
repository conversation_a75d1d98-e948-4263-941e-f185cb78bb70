package com.wsgjp.ct.sale.common.entity.log;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum OrderLinkStatusEnum implements CodeEnum {
    /**
     * ERP转单
     * ERP将推送库订单转到业务库后回流该订单状态
     */
    QIMEN_ERP_TRANSFER(1,"ERP转单"),
    /**
     * ERP审单
     * ERP完成订单审单后回流该订单状态
     */
    QIMEN_ERP_CHECK(2,"ERP审单"),
    /**
     * 	ERP通知配货
     * 商家订单若走奇门创建出库单，无须回流该状态，若未对接奇门需要回传该状态
     */
    QIMEN_CP_NOTIFY(3,"ERP通知配货"),
    /**
     * 仓库通知ERP出库
     * 商家订单若走奇门创建出库单，无须回流该状态，若未对接奇门需要回传该状态
     */
    QIMEN_CP_OUT(4,"仓库通知ERP出库");

    private final int code;
    private final String name;

    OrderLinkStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
