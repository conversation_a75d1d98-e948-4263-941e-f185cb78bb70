package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;

public enum FileTypeEnum implements CodeEnum {
    IMAGE(0,"图片"),
    VIDEO(1,"视频"),
    AUDIO_FREQUENCY(2,"音频");

    private int index;
    private String name;

    FileTypeEnum(int index,String name){
        this.index=index;
        this.name=name;
    }
    public static FileTypeEnum getEnumBycode(int code) {
        for (FileTypeEnum fileTypeEnum : FileTypeEnum.values()) {
            if (code == fileTypeEnum.getCode()) {
                return fileTypeEnum;
            }
        }
        return null;
    }
    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}
