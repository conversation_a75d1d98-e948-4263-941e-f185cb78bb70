package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;

/**
 * FileName:OrderDetailChangeType
 * Author: zhuxiupeng
 * Date:  2021-06-01 15:51
 */
public enum OrderDetailChangeType  implements CodeEnum {
    NORMAL(0,"正常"),
    ADD_DETAIL(1,"新增明细"),
    TRADE_TYPE_CHANGE(2,"交易类型变更"),
    PRICE_CHANGE(3,"商品价格变更"),
    PTYPE_CHANGE(4,"商品信息变更"),
    DELETE_DETAIL(5,"商品被删除"),
    MAPPING_CHANGE(6,"对应关系变更"),
    PTYPE_CANCEL(7,"商品被取消"),
    DELIVER_REQUIRE(8,"无需发货状态变更"),
    KTYPE_CHANGE(9,"仓库变更"),
    GIFT_CHANGE(10,"赠品变更"),
    ;
    private final int code;

    private final String name;

    OrderDetailChangeType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
