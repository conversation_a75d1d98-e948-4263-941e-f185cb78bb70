package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;
import com.wsgjp.ct.sale.common.enums.PriorityEnum;

/**
 * <AUTHOR> @description 换/补状态
 */

public enum ReSendStateEnum implements CodeEnum, PriorityEnum {
    NOMARL(0,"无需换/补",0),
    EXCHANGING_REPLENISHING(1,"换/补中",2),
    EXCHANGED_REPLENISHED(2,"换/补完成",1);


    private int code;
    private String name;
    private int priority;

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    ReSendStateEnum(int code, String name,int priority){
        this.code=code;
        this.name=name;
        this.priority = priority;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString()
    {
        return this.name;
    }

    @Override
    public int getCode() {
        return code;
    }}