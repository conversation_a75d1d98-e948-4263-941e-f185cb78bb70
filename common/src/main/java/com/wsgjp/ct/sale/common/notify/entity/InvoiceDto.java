package com.wsgjp.ct.sale.common.notify.entity;

/**
 * <AUTHOR>
 * @date 23/6/2020 上午 11:08
 */
public class InvoiceDto {
	//发票类型  0,电子普通 1,电子增值,2.纸质普通,3.纸质增值
	private int invoiceType;
	//发票抬头
	private String invoiceTitle;
	//税号
	private String invoiceTax;
	//开票单位 0,个人 1企业
	private int invoiceUserType;
	//是否需要开票
	private boolean needInvoice;
	//开票状态 未开票，已开票
	private int invoiceState;
	//发票备注
	private String memo;
	//注册地址
	private String address;
	//注册电话
	private String phone;
	//开户银行
	private String bank;
	//开户账号
	private String bankAccount;

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getBank() {
		return bank;
	}

	public void setBank(String bank) {
		this.bank = bank;
	}

	public String getBankAccount() {
		return bankAccount;
	}

	public void setBankAccount(String bankAccount) {
		this.bankAccount = bankAccount;
	}

	public int getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(int invoiceType) {
		this.invoiceType = invoiceType;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getInvoiceTax() {
		return invoiceTax;
	}

	public void setInvoiceTax(String invoiceTax) {
		this.invoiceTax = invoiceTax;
	}

	public int getInvoiceUserType() {
		return invoiceUserType;
	}

	public void setInvoiceUserType(int invoiceUserType) {
		this.invoiceUserType = invoiceUserType;
	}

	public boolean isNeedInvoice() {
		return needInvoice;
	}

	public void setNeedInvoice(boolean needInvoice) {
		this.needInvoice = needInvoice;
	}

	public int getInvoiceState() {
		return invoiceState;
	}

	public void setInvoiceState(int invoiceState) {
		this.invoiceState = invoiceState;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
}
