package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR> 2024/12/10 16:26
 */
public enum PlatformApiEnum implements CodeEnum {
    /**
     * 平台接口组主要API名称
     */
    NONE(0,"增量下单接口",PlatformMainBizType.ORDER_DOWNLOAD),
    DOWNLOAD_ORDER_INCREASE(1,"增量下单接口",PlatformMainBizType.ORDER_DOWNLOAD),
    DOWNLOAD_ORDER_FULL(2,"全量下单接口",PlatformMainBizType.ORDER_DOWNLOAD),
    DOWNLOAD_ORDER_DETAIL(3,"订单详情接口",PlatformMainBizType.ORDER_DOWNLOAD),
    DOWNLOAD_REFUND_DETAIL(4,"售后详情接口",PlatformMainBizType.REFUND_DOWNLOAD),
    DOWNLOAD_REFUND_INCREASE(5,"增量下载售后接口",PlatformMainBizType.REFUND_DOWNLOAD),
    DOWNLOAD_REFUND_FULL(6,"全量下载售后接口",PlatformMainBizType.REFUND_DOWNLOAD),
    STOCK_SYNC(7,"库存同步接口",PlatformMainBizType.STOCK_SYNC),
    FREIGHT_SYNC(8,"同步物流单号接口",PlatformMainBizType.SEND_ORDER),
    ;


    private final int code;
    private final String name;

    private final PlatformMainBizType bizType;

    PlatformApiEnum(int index, String name, PlatformMainBizType bizType){
        this.code = index;
        this.name = name;
        this.bizType = bizType;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public PlatformMainBizType getBizType() {
        return bizType;
    }
}
