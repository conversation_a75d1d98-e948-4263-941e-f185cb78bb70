package com.wsgjp.ct.sale.common.enums;

/**
 * 出入库类型
 * <AUTHOR>
 */

public enum InOutTypeEnum {

    /**
     * 出库
     */
    OUT((short)0),
    /**
     * 入库
     */
    IN((short)1),

    /**
     * 调拨(出入库)
     */
    GOODSTRANSTWO((short)2);

    private final short value;

    InOutTypeEnum(short value) {
        this.value = value;
    }

    /**
     * 获取枚举值
     *
     * @return
     */
    public short getValue() {
        return value;
    }

}
