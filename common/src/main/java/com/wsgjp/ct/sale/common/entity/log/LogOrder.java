package com.wsgjp.ct.sale.common.entity.log;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LogOrder {
    private String tradeId;

    /**
     * 合并订单号
     */
    private String mergeTradeId;
    private BigInteger eshopId;
    /**
     * 目前得物使用
     */
    private BigInteger ktypeId;
    /**
     * 收货人
     */
    private BuyerInfo buyerInfo;

    /**
     * 目前药师帮使用需要订单明细回传状态
     */
    private List<OrderDetail> orderDetails;

    public String getMergeTradeId() {
        return mergeTradeId;
    }

    public void setMergeTradeId(String mergeTradeId) {
        this.mergeTradeId = mergeTradeId;
    }

    public List<OrderDetail> getOrderDetails() { return orderDetails; }
    public void setOrderDetails(List<OrderDetail> value) { this.orderDetails = value; }


    public LogOrder() {
    }

    public LogOrder(String tradeId, BigInteger eshopId) {
        this.tradeId = tradeId;
        this.eshopId = eshopId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public BuyerInfo getBuyerInfo() {
        return buyerInfo;
    }

    public void setBuyerInfo(BuyerInfo buyerInfo) {
        this.buyerInfo = buyerInfo;
    }
}
