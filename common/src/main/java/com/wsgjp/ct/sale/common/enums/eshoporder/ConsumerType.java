package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;

public enum ConsumerType implements CodeEnum {
    NORMAL(0, "常规下单流程"),
    DIRECT_UPDATE_MEMO(1, "直接更新卖家旗帜和备注"),
    DIRECT_UPDATE_TRADE_STATUS(2, "直接更新交易状态"),
    DIRECT_UPDATE_REFUND_STATUS(3, "直接更新售后状态");


    /**
     * code
     */
    private int code;


    /**
     * name
     */
    private String name;

    ConsumerType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
