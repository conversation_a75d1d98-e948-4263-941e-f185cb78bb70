package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * @Description TODO
 * @Date 2023-09-12 14:59
 * @Created by lingxue
 */
public class BillDeliverDetailDTO {
    private BigInteger vchcode;
    private BigInteger detailId;
    private BigInteger orderDetailId;
    private BigInteger orderId;
    /**
     * 线上订单明细id
     */
    private String onlineDetailId;
    /**
     * 平台商品id
     */
    private String onlinePtypeId;
    /**
     * 平台商品skuid
     */
    private String onlineSkuId;
    /**
     * 线上订单id
     */
    private String tradeOrderId;
    private int deleted;
    /**
     * 辅助单位数量
     */
    private BigDecimal unitQty;

    private boolean combo;

    private BigInteger warehouseTaskId;
    /**
     * 质检信息--发货物流公司
     */
    private String platformQualityBtypeCode;
    /**
     * 质检信息--发货物流公司-物流产品
     */
    private String platformQualityBtypeProduct;
    /**
     * 质检信息--保价金额
     */
    private String platformQualityBtypeInsureTotal;
    /**
     * 质检信息--售后拦截
     */
    private String platformQualityRefundInterceptionCode;


    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public BigInteger getDetailId() {
        return detailId;
    }

    public void setDetailId(BigInteger detailId) {
        this.detailId = detailId;
    }

    public BigInteger getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(BigInteger orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public BigInteger getOrderId() {
        return orderId;
    }

    public void setOrderId(BigInteger orderId) {
        this.orderId = orderId;
    }

    public String getOnlineDetailId() {
        return onlineDetailId;
    }

    public void setOnlineDetailId(String onlineDetailId) {
        this.onlineDetailId = onlineDetailId;
    }

    public String getOnlinePtypeId() {
        return onlinePtypeId;
    }

    public void setOnlinePtypeId(String onlinePtypeId) {
        this.onlinePtypeId = onlinePtypeId;
    }

    public String getOnlineSkuId() {
        return onlineSkuId;
    }

    public void setOnlineSkuId(String onlineSkuId) {
        this.onlineSkuId = onlineSkuId;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public int getDeleted() {
        return deleted;
    }

    public void setDeleted(int deleted) {
        this.deleted = deleted;
    }

    public BigDecimal getUnitQty() {
        if (null == unitQty) {
            unitQty = BigDecimal.ZERO;
        }
        return unitQty;
    }

    public void setUnitQty(BigDecimal unitQty) {
        this.unitQty = unitQty;
    }

    public boolean isCombo() {
        return combo;
    }

    public void setCombo(boolean combo) {
        this.combo = combo;
    }

    public BigInteger getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(BigInteger warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }

    public String getPlatformQualityBtypeCode() {
        return platformQualityBtypeCode;
    }

    public void setPlatformQualityBtypeCode(String platformQualityBtypeCode) {
        this.platformQualityBtypeCode = platformQualityBtypeCode;
    }

    public String getPlatformQualityBtypeProduct() {
        return platformQualityBtypeProduct;
    }

    public void setPlatformQualityBtypeProduct(String platformQualityBtypeProduct) {
        this.platformQualityBtypeProduct = platformQualityBtypeProduct;
    }

    public String getPlatformQualityBtypeInsureTotal() {
        return platformQualityBtypeInsureTotal;
    }

    public void setPlatformQualityBtypeInsureTotal(String platformQualityBtypeInsureTotal) {
        this.platformQualityBtypeInsureTotal = platformQualityBtypeInsureTotal;
    }

    public String getPlatformQualityRefundInterceptionCode() {
        return platformQualityRefundInterceptionCode;
    }

    public void setPlatformQualityRefundInterceptionCode(String platformQualityRefundInterceptionCode) {
        this.platformQualityRefundInterceptionCode = platformQualityRefundInterceptionCode;
    }
}
