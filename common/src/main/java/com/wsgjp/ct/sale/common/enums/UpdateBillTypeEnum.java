package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021-05-19
 **/
public enum  UpdateBillTypeEnum implements CodeEnum {
    BILL(0, "主表"),
    DETAIL(1, "明细");

    private String desc;
    private int code;

    UpdateBillTypeEnum(int code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public int getCode() {
        return code;
    }
}
