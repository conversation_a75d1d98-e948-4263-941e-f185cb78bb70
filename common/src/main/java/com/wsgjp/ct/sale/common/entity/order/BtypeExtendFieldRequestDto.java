package com.wsgjp.ct.sale.common.entity.order;


import java.math.BigInteger;

/**
 * <AUTHOR>
 * 往来单位扩展附件信息
 * 2025-03-19 09:39
 */
public class BtypeExtendFieldRequestDto {

    /**
     * 往来单位Id
     */
    private BigInteger btypeId;

    /**
     * 附件名称
     */
    private String fieldName;

    /**
     * 附件内容
     */
    private String fieldValue;

    /**
     * 附件类型:0=文本，1=图片，2=文件
     */
    private Integer dataType;

    /**
     * 文件大小
     */
    private BigInteger fileSize;

    public BigInteger getBtypeId() {
        return btypeId;
    }

    public void setBtypeId(BigInteger btypeId) {
        this.btypeId = btypeId;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public BigInteger getFileSize() {
        return fileSize;
    }

    public void setFileSize(BigInteger fileSize) {
        this.fileSize = fileSize;
    }
}
