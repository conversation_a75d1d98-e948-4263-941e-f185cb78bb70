package com.wsgjp.ct.sale.common.entity.dto;

import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class EshopOrderBillChange {

	private String billNumber;
	@NotNull
	private BigInteger profileId;
	@NotNull
	private BigInteger eshopOrderId;
	private Boolean needRecordChange;
	@NotNull
	private List<EshopOrderBillChangeDetail> details;

	private List<BigInteger> detailIds;

	private BigInteger submitBatchId;

	//0:发货单 1:预售订单
	private int sourceType;
	// 0:普通 1:以采定销预售 2:以销定采预售 3 :周期购 4:团购 5:虚拟服务
	private int tradeType;

	private BigInteger otypeId;

	private BigInteger etypeId;
	private String platformParentOrderId;
	private String tradeOrderId;

	public String getTradeOrderId() {
		return tradeOrderId;
	}

	public void setTradeOrderId(String tradeOrderId) {
		this.tradeOrderId = tradeOrderId;
	}

	public String getPlatformParentOrderId() {
		return platformParentOrderId;
	}

	public void setPlatformParentOrderId(String platformParentOrderId) {
		this.platformParentOrderId = platformParentOrderId;
	}
	private int processStatus;
	public int getProcessStatus() {
		return processStatus;
	}

	public void setProcessStatus(int processStatus) {
		this.processStatus = processStatus;
	}

	public EshopOrderBillChange() {
	}

	public BigInteger getProfileId() {
		return profileId;
	}

	public void setProfileId(BigInteger profileId) {
		this.profileId = profileId;
	}

	public BigInteger getEshopOrderId() {
		return eshopOrderId;
	}

	public void setEshopOrderId(BigInteger eshopOrderId) {
		this.eshopOrderId = eshopOrderId;
	}

	public List<EshopOrderBillChangeDetail> getDetails() {
		return details;
	}

	public void setDetails(List<EshopOrderBillChangeDetail> details) {
		this.details = details;
	}

	public List<BigInteger> getDetailIds() {
		if (details == null || details.size() == 0) {
			return new ArrayList<>();
		}
		return details.stream().map(EshopOrderBillChangeDetail::getDetailId).collect(Collectors.toList());
	}

	public Boolean getNeedRecordChange() {
		if (needRecordChange == null) {
			return true;
		}
		return needRecordChange;
	}

	public void setNeedRecordChange(Boolean needRecordChange) {
		this.needRecordChange = needRecordChange;
	}

	public BigInteger getSubmitBatchId() {
		return submitBatchId;
	}

	public void setSubmitBatchId(BigInteger submitBatchId) {
		this.submitBatchId = submitBatchId;
	}

	public int getTradeType() {
		return tradeType;
	}

	public void setTradeType(int tradeType) {
		this.tradeType = tradeType;
	}

	public int getSourceType() {
		return sourceType;
	}

	public void setSourceType(int sourceType) {
		this.sourceType = sourceType;
	}

	public BigInteger getOtypeId() {
		return otypeId;
	}

	public void setOtypeId(BigInteger otypeId) {
		this.otypeId = otypeId;
	}

	public BigInteger getEtypeId() {
		return etypeId;
	}

	public void setEtypeId(BigInteger etypeId) {
		this.etypeId = etypeId;
	}

	public String getBillNumber() {
		return billNumber;
	}

	public void setBillNumber(String billNumber) {
		this.billNumber = billNumber;
	}
}
