package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 3/8/2020 下午 5:28
 */
public class QueryOrderParameter {
	private BigInteger profileId;
	private BigInteger id;
	private BigInteger submitBatchId;

	private List<BigInteger> idList;
	private List<BigInteger> submitBatchIdList;

	public List<BigInteger> getIdList() {
		return idList;
	}

	public List<BigInteger> getSubmitBatchIdList() {
		return submitBatchIdList;
	}


	public void setIdList(List<BigInteger> idList) {
		this.idList = idList;
	}

	public void setSubmitBatchIdList(List<BigInteger> submitBatchIdList) {
		this.submitBatchIdList = submitBatchIdList;
	}

	public BigInteger getProfileId() {
		return profileId;
	}

	public void setProfileId(BigInteger profileId) {
		this.profileId = profileId;
	}

	public BigInteger getId() {
		return id;
	}

	public void setId(BigInteger id) {
		this.id = id;
	}

	public BigInteger getSubmitBatchId() {
		if (submitBatchId == null)
		{
			return BigInteger.ZERO;
		}
		return submitBatchId;
	}

	public void setSubmitBatchId(BigInteger submitBatchId) {
		this.submitBatchId = submitBatchId;
	}
}
