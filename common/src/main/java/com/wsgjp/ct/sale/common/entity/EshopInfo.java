package com.wsgjp.ct.sale.common.entity;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.support.kms.KmsClient;
import ngp.utils.StringUtils;

import java.math.BigInteger;
import java.util.Date;


/**
 * @author: lyz
 * @description:
 */
public class EshopInfo {
    private BigInteger id;
    private BigInteger otypeId;
    private BigInteger mainOtypeId;
    private BigInteger profileId;
    private ShopType eshopType;
    private int eshopTypeInt;
    private int mappingType;
    private int isAuth;
    private int eshopSalePlatform;
    private String fullname="";
    private String eshopAccount="";
    private String onlineEshopId="";
    private String token="";
    private String appKey="";
    private String appSecret="";
    private String refreshToken="";
    private Date tokenExpireIn;
    private Date tokenR1ExpireIn;
    private Date refreshTokenExpireIn;
    private Date refreshTime;
    private Date createTime;
    private Date modifyTime;
    private Date updateTime;
    private boolean hasTokenExpired;
    private boolean hasException;
    private boolean deleted;
    private boolean stoped;
    private boolean enabled;
    private boolean newShop;
    private boolean autoSyncOrderEnabled;
    private boolean autoSyncStockEnabled;
    private boolean soldOutSyncEnabled = true;
    private String userName;
    private String password;
    private boolean rdsEnabled;
    private String rdsName;
    private Date rdsApplyTime;
    private Date rdsReadyTime;
    private Date rdsCheckTime;
    private Integer ocategory;
    private String ktypeIds;
    private String ruleCron;
    private String vendorId="";
    private String platformEshopId="";
    private boolean agEnabled;
    private String platformEshopSnType="";
    private Integer mutiSelectAppkey;
    private boolean isOpenXcode;
    private Integer mallType;
    private Integer classed;
    // 最后一次同步至中台时间
    private Date lastUploadBusinessTime;
    private boolean test;

    private int btypeGenerateType;
    private Integer deliverDuration;
    private Integer promisedSignDuration;
    private Integer promisedSyncFreightDuration;
    private Integer promisedCollectDuration;
    private Integer refundSysPromisedConfirmDuration;
    private Integer refundPromisedConfirmDuration;
    private Integer refundPromisedAgreeDuration;
    private Integer refundPromisedDeliverDuration;
    private Integer refundPromisedReceiveDuration;
    private int mentionDeliverDuration;
    private boolean subscribeLogistics;
    private boolean ptypeAutoUploadEnabled;
    private boolean btypeAutoUploadEnabled;
    private boolean ktypeAutoUploadEnabled;
    private boolean invoiceUploadEnabled;
    private boolean isSkuMemoDesired;
    private boolean skuMemoModeState;

    private BigInteger platfromConfig;
    private boolean usePlatformSkuidAsXcode;

    private Boolean tmallSpecialSale;
    private boolean mainEshop;
    private String groupId;


    private boolean synFreightVisible;

    public boolean isSynFreightVisible() {
        return synFreightVisible;
    }

    public void setSynFreightVisible(boolean synFreightVisible) {
        this.synFreightVisible = synFreightVisible;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public BigInteger getMainOtypeId() {
        return mainOtypeId;
    }

    public void setMainOtypeId(BigInteger mainOtypeId) {
        this.mainOtypeId = mainOtypeId;
    }

    public boolean isMainEshop() {
        return mainEshop;
    }

    public void setMainEshop(boolean mainEshop) {
        this.mainEshop = mainEshop;
    }

    public Boolean getTmallSpecialSale() {
        return tmallSpecialSale;
    }

    public void setTmallSpecialSale(Boolean tmallSpecialSale) {
        this.tmallSpecialSale = tmallSpecialSale;
    }

    private boolean usePriceStrategy = false;

    public boolean isUsePriceStrategy() {
        return usePriceStrategy;
    }

    public void setUsePriceStrategy(boolean usePriceStrategy) {
        this.usePriceStrategy = usePriceStrategy;
    }
    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }
    private boolean tmcEnabled;

    public boolean isTmcEnabled() {
        return tmcEnabled;
    }

    public void setTmcEnabled(boolean tmcEnabled) {
        this.tmcEnabled = tmcEnabled;
    }

    public boolean isSkuMemoDesired() {
        return isSkuMemoDesired;
    }

    public void setSkuMemoDesired(boolean skuMemoDesired) {
        isSkuMemoDesired = skuMemoDesired;
    }

    public boolean isSkuMemoModeState() {
        return skuMemoModeState;
    }

    public void setSkuMemoModeState(boolean skuMemoModeState) {
        this.skuMemoModeState = skuMemoModeState;
    }

    public Integer getPromisedSyncFreightDuration() {
        return promisedSyncFreightDuration;
    }

    public void setPromisedSyncFreightDuration(Integer promisedSyncFreightDuration) {
        this.promisedSyncFreightDuration = promisedSyncFreightDuration;
    }

    public Integer getClassed() {
        return classed;
    }

    public void setClassed(Integer classed) {
        this.classed = classed;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public ShopType getEshopType() {
        return eshopType;
    }

    public void setEshopType(ShopType eshopType) {
        this.eshopType = eshopType;
    }

    public int getEshopTypeInt() {
        return eshopTypeInt;
    }

    public void setEshopTypeInt(int eshopTypeInt) {
        this.eshopTypeInt = eshopTypeInt;
    }

    public int getMappingType() {
        return mappingType;
    }

    public void setMappingType(int mappingType) {
        this.mappingType = mappingType;
    }

    public int getEshopSalePlatform() {
        return eshopSalePlatform;
    }

    public void setEshopSalePlatform(int eshopSalePlatform) {
        this.eshopSalePlatform = eshopSalePlatform;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getEshopAccount() {
        return eshopAccount;
    }

    public void setEshopAccount(String eshopAccount) {
        this.eshopAccount = eshopAccount;
    }

    public String getOnlineEshopId() {
        return onlineEshopId;
    }

    public void setOnlineEshopId(String onlineEshopId) {
        this.onlineEshopId = onlineEshopId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getTokenExpireIn() {
        return tokenExpireIn;
    }

    public void setTokenExpireIn(Date tokenExpireIn) {
        this.tokenExpireIn = tokenExpireIn;
    }

    public Date getTokenR1ExpireIn() {
        return tokenR1ExpireIn;
    }

    public void setTokenR1ExpireIn(Date tokenR1ExpireIn) {
        this.tokenR1ExpireIn = tokenR1ExpireIn;
    }

    public Date getRefreshTokenExpireIn() {
        return refreshTokenExpireIn;
    }

    public void setRefreshTokenExpireIn(Date refreshTokenExpireIn) {
        this.refreshTokenExpireIn = refreshTokenExpireIn;
    }

    public Date getRefreshTime() {
        return refreshTime;
    }

    public void setRefreshTime(Date refreshTime) {
        this.refreshTime = refreshTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public boolean isHasTokenExpired() {
        return hasTokenExpired;
    }

    public void setHasTokenExpired(boolean hasTokenExpired) {
        this.hasTokenExpired = hasTokenExpired;
    }

    public boolean isHasException() {
        return hasException;
    }

    public void setHasException(boolean hasException) {
        this.hasException = hasException;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public boolean isStoped() {
        return stoped;
    }

    public void setStoped(boolean stoped) {
        this.stoped = stoped;
    }

    public boolean isNewShop() {
        return newShop;
    }

    public void setNewShop(boolean newShop) {
        this.newShop = newShop;
    }

    public boolean isAutoSyncOrderEnabled() {
        return autoSyncOrderEnabled;
    }

    public void setAutoSyncOrderEnabled(boolean autoSyncOrderEnabled) {
        this.autoSyncOrderEnabled = autoSyncOrderEnabled;
    }

    public boolean isAutoSyncStockEnabled() {
        return autoSyncStockEnabled;
    }

    public void setAutoSyncStockEnabled(boolean autoSyncStockEnabled) {
        this.autoSyncStockEnabled = autoSyncStockEnabled;
    }

    public boolean isSoldOutSyncEnabled() {
        return soldOutSyncEnabled;
    }

    public void setSoldOutSyncEnabled(boolean soldOutSyncEnabled) {
        this.soldOutSyncEnabled = soldOutSyncEnabled;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isRdsEnabled() {
        return rdsEnabled;
    }

    public void setRdsEnabled(boolean rdsEnabled) {
        this.rdsEnabled = rdsEnabled;
    }

    public Date getRdsApplyTime() {
        return rdsApplyTime;
    }

    public void setRdsApplyTime(Date rdsApplyTime) {
        this.rdsApplyTime = rdsApplyTime;
    }

    public Integer getOcategory() {
        return ocategory;
    }

    public void setOcategory(Integer ocategory) {
        this.ocategory = ocategory;
    }

    public String getKtypeIds() {
        return ktypeIds;
    }

    public void setKtypeIds(String ktypeIds) {
        this.ktypeIds = ktypeIds;
    }

    public String getRuleCron() {
        return ruleCron;
    }

    public void setRuleCron(String ruleCron) {
        this.ruleCron = ruleCron;
    }

    public String getVendorId() {
        return vendorId;
    }

    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    public String getPlatformEshopId() {
        return platformEshopId;
    }

    public void setPlatformEshopId(String platformEshopId) {
        this.platformEshopId = platformEshopId;
    }

    public boolean isAgEnabled() {
        return agEnabled;
    }

    public void setAgEnabled(boolean agEnabled) {
        this.agEnabled = agEnabled;
    }

    public String getPlatformEshopSnType() {
        return platformEshopSnType;
    }

    public void setPlatformEshopSnType(String platformEshopSnType) {
        this.platformEshopSnType = platformEshopSnType;
    }

    public Integer getMutiSelectAppkey() {
        return mutiSelectAppkey;
    }

    public void setMutiSelectAppkey(Integer mutiSelectAppkey) {
        this.mutiSelectAppkey = mutiSelectAppkey;
    }
    @Override
    public String toString() {
        return "EshopInfo{" +
                "otypeId=" + otypeId +
                ", profileId=" + profileId +
                ", eshopType=" + eshopType +
                ", eshopTypeInt=" + eshopTypeInt +
                ", mappingType=" + mappingType +
                ", eshopSalePlatform=" + eshopSalePlatform +
                ", fullname='" + fullname + '\'' +
                ", eshopAccount='" + eshopAccount + '\'' +
                ", onlineEshopId='" + onlineEshopId + '\'' +
                ", token='" + token + '\'' +
                ", appKey='" + appKey + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", refreshToken='" + refreshToken + '\'' +
                ", tokenExpireIn=" + tokenExpireIn +
                ", tokenR1ExpireIn=" + tokenR1ExpireIn +
                ", refreshTokenExpireIn=" + refreshTokenExpireIn +
                ", refreshTime=" + refreshTime +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                ", hasTokenExpired=" + hasTokenExpired +
                ", hasException=" + hasException +
                ", deleted=" + deleted +
                ", stoped=" + stoped +
                ", newShop=" + newShop +
                ", autoSyncOrderEnabled=" + autoSyncOrderEnabled +
                ", autoSyncStockEnabled=" + autoSyncStockEnabled +
                ", soldOutSyncEnabled=" + soldOutSyncEnabled +
                ", userName='" + userName + '\'' +
                ", password='" + password + '\'' +
                ", rdsEnabled=" + rdsEnabled +
                ", rdsApplyTime=" + rdsApplyTime +
                ", ocategory=" + ocategory +
                ", ktypeIds='" + ktypeIds + '\'' +
                ", ruleCron='" + ruleCron + '\'' +
                ", vendorId='" + vendorId + '\'' +
                ", platformEshopId='" + platformEshopId + '\'' +
                ", agEnabled=" + agEnabled +
                ", platformEshopSnType='" + platformEshopSnType + '\'' +
                '}';
    }

    public boolean isOpenXcode() {
        return isOpenXcode;
    }

    public void setOpenXcode(boolean openXcode) {
        isOpenXcode = openXcode;
    }

    public Integer getMallType() {
        return mallType;
    }

    public void setMallType(Integer mallType) {
        this.mallType = mallType;
    }

    public Date getLastUploadBusinessTime() {
        return lastUploadBusinessTime;
    }

    public void setLastUploadBusinessTime(Date lastUploadBusinessTime) {
        this.lastUploadBusinessTime = lastUploadBusinessTime;
    }

    public int getIsAuth() {
        return isAuth;
    }

    public void setIsAuth(int isAuth) {
        this.isAuth = isAuth;
    }

    public boolean isTest() {
        return test;
    }

    public void setTest(boolean test) {
        this.test = test;
    }

    public int getBtypeGenerateType() {
        return btypeGenerateType;
    }

    public void setBtypeGenerateType(int btypeGenerateType) {
        this.btypeGenerateType = btypeGenerateType;
    }

    public Integer getDeliverDuration() {
        return deliverDuration;
    }

    public void setDeliverDuration(Integer deliverDuration) {
        this.deliverDuration = deliverDuration;
    }

    public Integer getPromisedSignDuration() {
        return promisedSignDuration;
    }

    public void setPromisedSignDuration(Integer promisedSignDuration) {
        this.promisedSignDuration = promisedSignDuration;
    }


    public boolean isSubscribeLogistics() {
        return subscribeLogistics;
    }

    public void setSubscribeLogistics(boolean subscribeLogistics) {
        this.subscribeLogistics = subscribeLogistics;
    }

    public Integer getPromisedCollectDuration() {
        return promisedCollectDuration;
    }

    public void setPromisedCollectDuration(Integer promisedCollectDuration) {
        this.promisedCollectDuration = promisedCollectDuration;
    }

    public Integer getRefundSysPromisedConfirmDuration() {
        return refundSysPromisedConfirmDuration;
    }

    public void setRefundSysPromisedConfirmDuration(Integer refundSysPromisedConfirmDuration) {
        this.refundSysPromisedConfirmDuration = refundSysPromisedConfirmDuration;
    }

    public Integer getRefundPromisedConfirmDuration() {
        return refundPromisedConfirmDuration;
    }

    public void setRefundPromisedConfirmDuration(Integer refundPromisedConfirmDuration) {
        this.refundPromisedConfirmDuration = refundPromisedConfirmDuration;
    }

    public Integer getRefundPromisedAgreeDuration() {
        return refundPromisedAgreeDuration;
    }

    public void setRefundPromisedAgreeDuration(Integer refundPromisedAgreeDuration) {
        this.refundPromisedAgreeDuration = refundPromisedAgreeDuration;
    }

    public Integer getRefundPromisedDeliverDuration() {
        return refundPromisedDeliverDuration;
    }

    public void setRefundPromisedDeliverDuration(Integer refundPromisedDeliverDuration) {
        this.refundPromisedDeliverDuration = refundPromisedDeliverDuration;
    }

    public Integer getRefundPromisedReceiveDuration() {
        return refundPromisedReceiveDuration;
    }

    public void setRefundPromisedReceiveDuration(Integer refundPromisedReceiveDuration) {
        this.refundPromisedReceiveDuration = refundPromisedReceiveDuration;
    }

    public boolean isPtypeAutoUploadEnabled() {
        return ptypeAutoUploadEnabled;
    }

    public void setPtypeAutoUploadEnabled(boolean ptypeAutoUploadEnabled) {
        this.ptypeAutoUploadEnabled = ptypeAutoUploadEnabled;
    }

    public boolean isBtypeAutoUploadEnabled() {
        return btypeAutoUploadEnabled;
    }

    public void setBtypeAutoUploadEnabled(boolean btypeAutoUploadEnabled) {
        this.btypeAutoUploadEnabled = btypeAutoUploadEnabled;
    }

    public boolean isKtypeAutoUploadEnabled() {
        return ktypeAutoUploadEnabled;
    }

    public void setKtypeAutoUploadEnabled(boolean ktypeAutoUploadEnabled) {
        this.ktypeAutoUploadEnabled = ktypeAutoUploadEnabled;
    }

    public boolean isInvoiceUploadEnabled() {
        return invoiceUploadEnabled;
    }

    public void setInvoiceUploadEnabled(boolean invoiceUploadEnabled) {
        this.invoiceUploadEnabled = invoiceUploadEnabled;
    }

    public int getMentionDeliverDuration() {
        return mentionDeliverDuration;
    }

    public void setMentionDeliverDuration(int mentionDeliverDuration) {
        this.mentionDeliverDuration = mentionDeliverDuration;
    }

    public Date getRdsReadyTime() {
        return rdsReadyTime;
    }

    public void setRdsReadyTime(Date rdsReadyTime) {
        this.rdsReadyTime = rdsReadyTime;
    }

    public Date getRdsCheckTime() {
        return rdsCheckTime;
    }

    public void setRdsCheckTime(Date rdsCheckTime) {
        this.rdsCheckTime = rdsCheckTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRdsName() {
        return rdsName;
    }

    public void setRdsName(String rdsName) {
        this.rdsName = rdsName;
    }

    public BigInteger getPlatfromConfig() {
        return platfromConfig;
    }

    public void setPlatfromConfig(BigInteger platfromConfig) {
        this.platfromConfig = platfromConfig;
    }

    public boolean isUsePlatformSkuidAsXcode() {
        return usePlatformSkuidAsXcode;
    }

    public void setUsePlatformSkuidAsXcode(boolean usePlatformSkuidAsXcode) {
        this.usePlatformSkuidAsXcode = usePlatformSkuidAsXcode;
    }

    public String getDecryptToken() {
        String decryptToken = "";
        if (StringUtils.isNotEmpty(getToken())) {
            String decrypt = KmsClient.decrypt(getToken());
            decryptToken = decrypt;
        }
        return decryptToken;
    }
    public String getDecryptinfo(String key) {
        String decryptToken = "";
        if (StringUtils.isNotEmpty(getToken())) {
            String decrypt = KmsClient.decrypt(key);
            decryptToken = decrypt;
        }
        return decryptToken;
    }
}
