package com.wsgjp.ct.sale.common.entity.dto;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class EshopOrderDetailDTO {
    private int deleted;
    private BigInteger skuId;
    private BigInteger stockSyncRuleId;
    private BigDecimal qty;
    private BigInteger ktypeId;
    private int tradeState;
    private int localRefundState;
    private int processState;
    private BigInteger id;
    private String oid;
    private boolean mappingState;
    private boolean deliverRequired;
    private BigInteger profileId;
    private BigInteger vchcode;
    private int tradeType;

    public int getDeleted() {
        return deleted;
    }

    public void setDeleted(int deleted) {
        this.deleted = deleted;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public BigInteger getStockSyncRuleId() {
        return stockSyncRuleId;
    }

    public void setStockSyncRuleId(BigInteger stockSyncRuleId) {
        this.stockSyncRuleId = stockSyncRuleId;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public int getTradeState() {
        return tradeState;
    }

    public void setTradeState(int tradeState) {
        this.tradeState = tradeState;
    }

    public int getLocalRefundState() {
        return localRefundState;
    }

    public void setLocalRefundState(int localRefundState) {
        this.localRefundState = localRefundState;
    }

    public int getProcessState() {
        return processState;
    }

    public void setProcessState(int processState) {
        this.processState = processState;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public boolean isMappingState() {
        return mappingState;
    }

    public void setMappingState(boolean mappingState) {
        this.mappingState = mappingState;
    }

    public boolean isDeliverRequired() {
        return deliverRequired;
    }

    public void setDeliverRequired(boolean deliverRequired) {
        this.deliverRequired = deliverRequired;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public int getTradeType() {
        return tradeType;
    }

    public void setTradeType(int tradeType) {
        this.tradeType = tradeType;
    }
}
