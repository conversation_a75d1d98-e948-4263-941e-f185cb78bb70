package com.wsgjp.ct.sale.common.error;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证器异常
 *
 * <AUTHOR>
 */
public class ValidatorError<T> {

    /**
     * 异常列表
     */
    private List<T> errorList;

    public List<T> getErrorList() {
        if (errorList == null) {
            errorList = new ArrayList<>();
        }
        return errorList;
    }

    public void writeErrorInfo(T errorInfo) {
        if (errorList == null) {
            errorList = new ArrayList<>();
        }
        this.errorList.add(errorInfo);
    }

    public void setErrorList(List<T> errorList) {
        this.errorList = errorList;
    }
}
