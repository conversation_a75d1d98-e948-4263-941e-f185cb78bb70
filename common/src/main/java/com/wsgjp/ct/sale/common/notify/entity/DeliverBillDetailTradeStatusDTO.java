package com.wsgjp.ct.sale.common.notify.entity;


import com.wsgjp.ct.sale.common.enums.eshoporder.IdentifyResultType;
import com.wsgjp.ct.sale.common.enums.eshoporder.QcResultType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import ngp.utils.StringUtils;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 30/7/2020 上午 9:27
 */
public class DeliverBillDetailTradeStatusDTO {
	private TradeStatus tradeState;
	private String oid;
	private BigInteger orderDetailId;

	/**
	 *平台质检结果
	 */
	private QcResultType platformQcResult;

	/**
	 *平台质检结果描述
	 */
	private String platformQcResultDesc;

	/**
	 *平台鉴定结果
	 */
	private IdentifyResultType platformIdentifyResult;

	/**
	 *平台鉴定结果描述
	 */
	private String platformIdentifyResultDesc;

	//最晚发货时间
	private Date promisedSendTime;
	private BigInteger ktypeId;

	public BigInteger getKtypeId() {
		return ktypeId;
	}

	public void setKtypeId(BigInteger ktypeId) {
		this.ktypeId = ktypeId;
	}

	public Date getPromisedSendTime() {
		return promisedSendTime;
	}

	public void setPromisedSendTime(Date promisedSendTime) {
		this.promisedSendTime = promisedSendTime;
	}

	public QcResultType getPlatformQcResult() {
		if (null == platformQcResult){
			return QcResultType.NONE;
		}
		return platformQcResult;
	}

	public void setPlatformQcResult(QcResultType platformQcResult) {
		this.platformQcResult = platformQcResult;
	}

	public String getPlatformQcResultDesc() {
		if (StringUtils.isEmpty(platformQcResultDesc)){
			return "";
		}
		return platformQcResultDesc;
	}

	public void setPlatformQcResultDesc(String platformQcResultDesc) {
		this.platformQcResultDesc = platformQcResultDesc;
	}

	public IdentifyResultType getPlatformIdentifyResult() {
		if (null == platformIdentifyResult){
			return IdentifyResultType.NONE;
		}
		return platformIdentifyResult;
	}

	public void setPlatformIdentifyResult(IdentifyResultType platformIdentifyResult) {
		this.platformIdentifyResult = platformIdentifyResult;
	}

	public String getPlatformIdentifyResultDesc() {
		if (StringUtils.isEmpty(platformIdentifyResultDesc)){
			return "";
		}
		return platformIdentifyResultDesc;
	}

	public void setPlatformIdentifyResultDesc(String platformIdentifyResultDesc) {
		this.platformIdentifyResultDesc = platformIdentifyResultDesc;
	}

	public TradeStatus getTradeState() {
		return tradeState;
	}

	public void setTradeState(TradeStatus tradeState) {
		this.tradeState = tradeState;
	}

	public BigInteger getOrderDetailId() {
		return orderDetailId;
	}

	public void setOrderDetailId(BigInteger orderDetailId) {
		this.orderDetailId = orderDetailId;
	}

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}
}
