package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;

/**
 * 售后单收货状态
 *
 * <AUTHOR>
 * @date 2020-05-30
 */
public enum CheckinState implements CodeEnum {
    ALL(-1,"全部"),
    UN_OPEN(0, "未验货"),
    OPEN(1, "已验货"),
    ;

    private int code;

    private String name;

    CheckinState(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
