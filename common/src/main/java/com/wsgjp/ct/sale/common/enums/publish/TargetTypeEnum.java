package com.wsgjp.ct.sale.common.enums.publish;

import bf.datasource.typehandler.CodeEnum;

public enum TargetTypeEnum implements CodeEnum {
    PUBLISH_COMMON(0,"商品发布基础资料"),
    PUBLISH(1,"个性化平台商品发布资料");

    private int index;
    private String name;

    TargetTypeEnum(int index,String name){
        this.index=index;
        this.name=name;
    }

    public static TargetTypeEnum getEnumBycode(int code) {
        for (TargetTypeEnum targetTypeEnum : TargetTypeEnum.values()) {
            if (code == targetTypeEnum.getCode()) {
                return targetTypeEnum;
            }
        }
        return null;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }
}
