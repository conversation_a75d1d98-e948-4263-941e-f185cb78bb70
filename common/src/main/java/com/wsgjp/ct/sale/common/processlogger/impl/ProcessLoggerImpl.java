package com.wsgjp.ct.sale.common.processlogger.impl;

import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.common.processlogger.ProcessLogger;
import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class ProcessLoggerImpl implements ProcessLogger {
    private RedisProcessMessage message;
    private String loggerKey;

    private final int RETARY_COUNT = 3;

    public ProcessLoggerImpl(String loggerKey) {
        message = new RedisProcessMessage(loggerKey);
        this.loggerKey = loggerKey;
    }

    public ProcessLoggerImpl(String loggerKey, RedisProcessMessage message) {
        this.loggerKey = loggerKey;
        this.message = message;
    }

    @Override
    public int appendMsg(String msg) {
        int i = 1;
        while (i < RETARY_COUNT) {
            try {
                return message.doAppendMsg(msg);
            } catch (RuntimeException ex) {

            }
            i++;
        }
        return message.doAppendMsg(msg);
    }

    @Override
    public void modifyMsg(String msg) {
        int i = 1;
        while (i < RETARY_COUNT) {
            try {
                message.doModifyMsg(msg);
                return;
            } catch (RuntimeException ex) {
            }
            i++;
        }
        message.doModifyMsg(msg);
    }

    @Override
    public void modifyMsg(String msg, int msgId) {
        message.doModifyMsg(msg, msgId);
    }

    @Override
    public void doFinish() {
        message.setFinish();
    }

    public String getLoggerKey() {
        if (StringUtils.isEmpty(loggerKey)) {
            return "";
        }
        return loggerKey;
    }

    public void setLoggerKey(String loggerKey) {
        this.loggerKey = loggerKey;
    }
}

