package com.wsgjp.ct.sale.common.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum ProcessLoggerType implements CodeEnum {
    /**
     * 新增日志
     */
    APPEND(0, "新增"),
    /**
     * 修改日志
     */
    MODIFY(1, "修改");
    private int code;

    private String name;

    ProcessLoggerType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
