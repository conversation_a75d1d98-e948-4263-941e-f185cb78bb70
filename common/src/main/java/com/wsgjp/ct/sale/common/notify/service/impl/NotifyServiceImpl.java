package com.wsgjp.ct.sale.common.notify.service.impl;

import com.wsgjp.ct.sale.common.mapper.NotifyMapper;
import com.wsgjp.ct.sale.common.notify.request.DeliverBillUpdateRequest;
import com.wsgjp.ct.sale.common.notify.service.NotifyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: wcy
 * @Date: 2024/12/25/16:57
 * @Description:
 */

@Service
public class NotifyServiceImpl implements NotifyService {

    private final NotifyMapper mapper;
    private final Logger logger = LoggerFactory.getLogger(NotifyServiceImpl.class);


    public NotifyServiceImpl(NotifyMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public void insertNotifyChange(List<DeliverBillUpdateRequest> changeInfoList) {
        for (DeliverBillUpdateRequest changeInfo : changeInfoList) {
            try {
                mapper.insertChangeInfo(changeInfo);
            } catch (Exception ex) {
                logger.error(String.format("保存订单【%s】变更数据报错：%s", changeInfo.getTradeOrderId(), ex.getMessage()), ex);
            }
        }
    }
}
