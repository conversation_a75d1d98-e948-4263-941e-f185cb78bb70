package com.wsgjp.ct.sale.common.enums.eshoporder;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum OrderOpreateType implements CodeEnum {
    MANUAL(0, "手工创建订单"),
    DOWNLOAD(1, "手工下载订单"),
    MODIFY_ORDER(2, "编辑订单"),
    AUTO_DOWNLOAD(3, "自动下载订单"),
    FROM_ORDER_BILL_SUBMIT(4, "确认提交"),
    IMPORT_ORDER(5, "导入订单"),
    REPORT_ORDER(6, "导出成功"),
    MANUAL_UPDATE(7, "手工下载更新订单"),
    AUTO_UPDATE(8, "自动更新订单"),
    MODIFY_BUYER_MESSAGE(9, "修改卖家备注"),
    MODIFY_REAMRK(10, "修改系统备注"),
    CLOSE_ORDER(11, "关闭交易"),
    RELETAION(12, "网店商品临时对应"),
    DELETE_ORDER(13, "删除/撤销删除订单"),
    SUBMIT_ADVANCE(14, "提交预售订单"),
    RESUBMIT_ORDER(15, "还原订单"),
    UPDATE_ALL_DATA(16, "更新订单(全部)"),
    UPDATE_ORDER(17, "更新订单(线上)"),
    PTYPE_RELETAION(18, "网店商品对应"),
    REFUND_NOTIFY(19, "售后通知"),
    UPDATE_ADVANCE(20, "更新预售订单"),
    DELIVER_UPDATE_ADDRESS(21, "修改地址"),
    TMC_BUYER_MODIFY_ADDRESS(22, "买家自主改地址"),
    TMC_BUYER_AG(23, "急速退款"),
    TMC_BUYER_SPECIFY_LOGISTICS(24, "买家指定物流"),
    TMC_EXPEDITE_DELIVERY(25, "买家指定物流"),
    INVOICE(26, "开发票"),
    LOCK_ORDER(27, "锁单"),
    SEND_ONLINE(28, "系统发货"),
    EXPEDITE_DELIVERY(29,"催发货"),
    GATHER_FRONT(30, "收定金"),
    GATHER_TAIL(31,"收尾款"),
    OFFLINE_GATHER(32,"线下收款"),
    PLATFORM_BTYPE_MAPPING(33,"客户/供应商对应"),
    MODIFY_SKU(34,"自助改商品SKU"),
    TMC_AUTO_DOWNLOAD(35,"TMC自动下载订单"),

    ORDER_CANCEL(36,"订单取消"),
    REFUND_MANUAL(37, "售后生成订单"),
    AUDIT_BUYER_MODIFY_ADDRESS(38, "用户修改地址申请"),
    API_UPLOAD(40, "自建商城上载"),
    UPDATE_PROCESS_STATE(41, "修改提交状态"),
    TMC_AUTO_UPDATE(42, "TMC自动更新订单"),
    STORE_MAPPING(43, "全渠道门店/平台仓对应"),
    SYNC_FREIGHT(43, "同步单号"),
    DELIVER_NOTIFY(44, "发货通知"),
    DELIVER_RELETAION(45, "发货临时对应"),
    ORDER_GATHER(46, "订单收款"),
    IMPORT_GATHER(47, "导入收款"),
    DELETE_GATHER_ORDER(48, "删除收款单");
    private int code;
    private String name;

    OrderOpreateType(int index, String name) {
        this.code = index;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

}
