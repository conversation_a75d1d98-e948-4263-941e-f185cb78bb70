package com.wsgjp.ct.sale.common.notify.request;

import com.wsgjp.ct.sale.common.enums.CustomerTypeEnum;
import com.wsgjp.ct.sale.common.enums.ProducerTypeEnum;
import com.wsgjp.ct.sale.common.enums.UpdateBillTypeEnum;
import com.wsgjp.ct.sale.common.enums.UpdateTypeEnum;
import ngp.utils.Md5Utils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-05
 **/
public class SaleOrderChangeDAO {

    private BigInteger id;
    private BigInteger profileId;
    private String tradeOrderId;
    private UpdateTypeEnum subChangeType;
    private UpdateBillTypeEnum changeType;
    private String content;
    private BigInteger otypeId;
    private BigInteger vchcode;
    private String oid;
    private BigInteger batchId;
    private ProducerTypeEnum producer;
    private CustomerTypeEnum customer;

    /**
     * 结算单位-结算单位变更通知时使用
    **/
    private BigInteger payBtypeId;

    /**
     * 往来单位
     **/
    private BigInteger btypeId;

    /**
     * 发货单的vchcodes
    **/
    private List<BigInteger> deliverVchcodes;
    private List<BigInteger> warehouseTaskId;

    /**
     * 系统备注
     **/
    private String memo;


    /**
     * 买家运费
     */
    private BigDecimal orderBuyerFreightFee;

    private BigInteger eshopOrderId;

    private Date modifyTime;

    private String uniqueKey;

    /**
     * 交易类型
     *0--普通
     *1--预售-按计划发
     *2--预售-有货就发
     *3--周期购
     *4--团购
     *5--虚拟服务
     */
    private int tradeType;

    public int getTradeType() {
        return tradeType;
    }

    public void setTradeType(int tradeType) {
        this.tradeType = tradeType;
    }

    public String getUniqueKey() {
        return Md5Utils.md5(String.format("%s%s%s%s%s",getProfileId(),getEshopOrderId(),getTradeOrderId(),getOid(),getSubChangeType()));
    }

    public void setUniqueKey(String uniqueKey) {
        this.uniqueKey = uniqueKey;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public BigInteger getEshopOrderId() {
        return eshopOrderId;
    }

    public void setEshopOrderId(BigInteger eshopOrderId) {
        this.eshopOrderId = eshopOrderId;
    }

    public BigDecimal getOrderBuyerFreightFee() {
        return orderBuyerFreightFee;
    }

    public void setOrderBuyerFreightFee(BigDecimal orderBuyerFreightFee) {
        this.orderBuyerFreightFee = orderBuyerFreightFee;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public BigInteger getBtypeId() {
        return btypeId;
    }

    public void setBtypeId(BigInteger btypeId) {
        this.btypeId = btypeId;
    }

    public BigInteger getPayBtypeId() {
        return payBtypeId;
    }

    public void setPayBtypeId(BigInteger payBtypeId) {
        this.payBtypeId = payBtypeId;
    }

    // 任务单改造 移除该字段
    public List<BigInteger> getDeliverVchcodes() {
        return deliverVchcodes;
    }

    // 任务单改造 移除该字段
    @Deprecated()
    public void setDeliverVchcodes(List<BigInteger> deliverVchcodes) {
        this.deliverVchcodes = deliverVchcodes;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public UpdateTypeEnum getSubChangeType() {
        return subChangeType;
    }

    public void setSubChangeType(UpdateTypeEnum subChangeType) {
        this.subChangeType = subChangeType;
    }

    public UpdateBillTypeEnum getChangeType() {
        return changeType;
    }

    public void setChangeType(UpdateBillTypeEnum changeType) {
        this.changeType = changeType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public ProducerTypeEnum getProducer() {
        return producer;
    }

    public void setProducer(ProducerTypeEnum producer) {
        this.producer = producer;
    }

    public CustomerTypeEnum getCustomer() {
        return customer;
    }

    public void setCustomer(CustomerTypeEnum customer) {
        this.customer = customer;
    }

    public BigInteger getBatchId() {
        return batchId;
    }

    public void setBatchId(BigInteger batchId) {
        this.batchId = batchId;
    }

    public List<BigInteger> getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(List<BigInteger> warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }
}
