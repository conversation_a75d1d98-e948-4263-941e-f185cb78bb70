package com.wsgjp.ct.sale.common.entity.freight;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel("物流映射")
public class FreightMapping {
    @ApiModelProperty("线上物流公司编码")
    private String onlineCode;
    @ApiModelProperty("线上物流公司名称")
    private String onlineName;
    @ApiModelProperty("本地物流公司名称")
    private String localName;
    @ApiModelProperty("本地物流公司编码")
    private String localCode;
    @ApiModelProperty("网店类型编码")
    private int shopType;
    @ApiModelProperty("产品ID")
    private int productId;
    @ApiModelProperty("唯一键")
    private String hashCode;
    @ApiModelProperty("平台额外名称")
    private String onlineExtraCode;

    public String getOnlineCode() {
        return onlineCode;
    }

    public void setOnlineCode(String onlineCode) {
        this.onlineCode = onlineCode;
    }

    public String getOnlineName() {
        return onlineName;
    }

    public void setOnlineName(String onlineName) {
        this.onlineName = onlineName;
    }

    public String getLocalName() {
        return localName;
    }

    public void setLocalName(String localName) {
        this.localName = localName;
    }

    public String getLocalCode() {
        return localCode;
    }

    public void setLocalCode(String localCode) {
        this.localCode = localCode;
    }

    public int getShopType() {
        return shopType;
    }

    public void setShopType(int shopType) {
        this.shopType = shopType;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    public String getOnlineExtraCode() {
        return onlineExtraCode;
    }

    public void setOnlineExtraCode(String onlineExtraCode) {
        this.onlineExtraCode = onlineExtraCode;
    }
}
