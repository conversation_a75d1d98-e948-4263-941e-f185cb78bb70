package com.wsgjp.ct.sale.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: wcy
 * @Date: 2022/05/05/14:21
 * @Description:
 */
@Configuration
@ConfigurationProperties(prefix = "sys")
public class EshopOrderCommonConfig {
    private Boolean speedMode = false;
    //分钟单位,操作的时间频率
    private Integer expireTime = 1;
    //批量操作的数量限制
    private Integer operateNum = 50;
    //下载订单走分页接口新方式
    private Boolean splitPageSaveOrder = true;
    //刷新网店商品走分页接口新方式
    private Boolean splitPageRefreshPtype = true;
    //下载订单时，按照【_】拆分订单网店商品属性最大次数
    private Integer splitPropertiesNum = 5;
    private boolean manualDownloadUseUpdateTime = false;
    private Integer productId;

    private boolean needReDownloadRefundForTb = true;

    /**
     * 提交不检查重复明细的平台
     */
    private String noCheckSubmitByShopType = "85";

    private int saleBizProfileCacheSeconds = 3600;
    /**
     * tmc店铺映射关系缓存版本号
     */
    private String tmcCacheVersion = "V1";
    private String notSupportReissueEshopTypes;
    /**
     * 是否需要检查网店商品的所有订单是否被删除
     */
    private boolean needCheckProductHasDeletedOrder = false;

    private boolean refundNotifyFilter = false;

    private boolean noNeedBuildAndSave = false;

    private int downloadOrderMaxSize = 200;
    /**
     * 单位分钟
     */
    private int downloadRefundByTimeOffsetTime = 2 * 24 * 60;

    private boolean useBusAutoCheckin = false;

    private boolean deleteSaleBackBill = true;

    private String needAddAmountPlatform = "2,20";

    public String getNeedAddAmountPlatform() {
        return needAddAmountPlatform;
    }

    public void setNeedAddAmountPlatform(String needAddAmountPlatform) {
        this.needAddAmountPlatform = needAddAmountPlatform;
    }

    public void setDeleteSaleBackBill(boolean deleteSaleBackBill) {
        this.deleteSaleBackBill = deleteSaleBackBill;
    }

    public boolean isDeleteSaleBackBill() {
        return deleteSaleBackBill;
    }

    public boolean isUseBusAutoCheckin() {
        return useBusAutoCheckin;
    }

    public void setUseBusAutoCheckin(boolean useBusAutoCheckin) {
        this.useBusAutoCheckin = useBusAutoCheckin;
    }

    public boolean isNoNeedBuildAndSave() {
        return noNeedBuildAndSave;
    }

    public void setNoNeedBuildAndSave(boolean noNeedBuildAndSave) {
        this.noNeedBuildAndSave = noNeedBuildAndSave;
    }

    public boolean isRefundNotifyFilter() {
        return refundNotifyFilter;
    }

    public void setRefundNotifyFilter(boolean refundNotifyFilter) {
        this.refundNotifyFilter = refundNotifyFilter;
    }

    public boolean isNeedCheckProductHasDeletedOrder() {
        return needCheckProductHasDeletedOrder;
    }

    public void setNeedCheckProductHasDeletedOrder(boolean needCheckProductHasDeletedOrder) {
        this.needCheckProductHasDeletedOrder = needCheckProductHasDeletedOrder;
    }

    public String getNotSupportReissueEshopTypes() {
        return notSupportReissueEshopTypes;
    }

    public void setNotSupportReissueEshopTypes(String notSupportReissueEshopTypes) {
        this.notSupportReissueEshopTypes = notSupportReissueEshopTypes;
    }

    private String discountEshopType;

    public String getDiscountEshopType() {
        if (discountEshopType == null) {
            return "";
        }
        return discountEshopType;
    }

    public void setDiscountEshopType(String discountEshopType) {
        this.discountEshopType = discountEshopType;
    }

    public String getNoCheckSubmitByShopType() {
        return noCheckSubmitByShopType;
    }

    public void setNoCheckSubmitByShopType(String noCheckSubmitByShopType) {
        this.noCheckSubmitByShopType = noCheckSubmitByShopType;
    }

    public Boolean getSplitPageSaveOrder() {
        return splitPageSaveOrder;
    }

    public void setSplitPageSaveOrder(Boolean splitPageSaveOrder) {
        this.splitPageSaveOrder = splitPageSaveOrder;
    }

    public Boolean getSplitPageRefreshPtype() {
        return splitPageRefreshPtype;
    }

    public void setSplitPageRefreshPtype(Boolean splitPageRefreshPtype) {
        this.splitPageRefreshPtype = splitPageRefreshPtype;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSplitPropertiesNum() {
        return splitPropertiesNum;
    }

    public void setSplitPropertiesNum(Integer splitPropertiesNum) {
        this.splitPropertiesNum = splitPropertiesNum;
    }

    private boolean isOpenDDGXNewFrontDecrypt = true;

    public Boolean getSpeedMode() {
        return speedMode;
    }

    public Integer getOperateNum() {
        return operateNum;
    }

    public void setOperateNum(Integer operateNum) {
        this.operateNum = operateNum;
    }

    public Integer getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Integer expireTime) {
        this.expireTime = expireTime;
    }

    public Boolean isSpeedMode() {
        return speedMode;
    }

    public void setSpeedMode(Boolean speedMode) {
        this.speedMode = speedMode;
    }

    public boolean isManualDownloadUseUpdateTime() {
        return manualDownloadUseUpdateTime;
    }

    public void setManualDownloadUseUpdateTime(boolean manualDownloadUseUpdateTime) {
        this.manualDownloadUseUpdateTime = manualDownloadUseUpdateTime;
    }

    public boolean isOpenDDGXNewFrontDecrypt() {
        return isOpenDDGXNewFrontDecrypt;
    }

    public void setOpenDDGXNewFrontDecrypt(boolean openDDGXNewFrontDecrypt) {
        isOpenDDGXNewFrontDecrypt = openDDGXNewFrontDecrypt;
    }

    public boolean isNeedReDownloadRefundForTb() {
        return needReDownloadRefundForTb;
    }

    public void setNeedReDownloadRefundForTb(boolean needReDownloadRefundForTb) {
        this.needReDownloadRefundForTb = needReDownloadRefundForTb;
    }

    public int getSaleBizProfileCacheSeconds() {
        return saleBizProfileCacheSeconds;
    }

    public void setSaleBizProfileCacheSeconds(int saleBizProfileCacheSeconds) {
        this.saleBizProfileCacheSeconds = saleBizProfileCacheSeconds;
    }

    public String getTmcCacheVersion() {
        return tmcCacheVersion;
    }

    public void setTmcCacheVersion(String tmcCacheVersion) {
        this.tmcCacheVersion = tmcCacheVersion;
    }

    public int getDownloadOrderMaxSize() {
        return downloadOrderMaxSize;
    }

    public int getDownloadRefundByTimeOffsetTime() {
        return downloadRefundByTimeOffsetTime;
    }

    public void setDownloadRefundByTimeOffsetTime(int downloadRefundByTimeOffsetTime) {
        this.downloadRefundByTimeOffsetTime = downloadRefundByTimeOffsetTime;
    }

    public void setDownloadOrderMaxSize(int downloadOrderMaxSize) {
        this.downloadOrderMaxSize = downloadOrderMaxSize;
    }
}