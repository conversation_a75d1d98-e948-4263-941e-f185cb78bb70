package com.wsgjp.ct.sale.common.entity.dto;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2/7/2020 上午 10:37
 */
public class EshopOrderBillChangeDetail {
    @NotNull
    private BigInteger vchcode;
    @NotNull
    private BigInteger detailId;
    @NotNull
    private BigInteger skuId;
    private BigInteger ktypeId;
    private BigInteger stockSyncRuleId;
    @NotNull
    private BigDecimal unitQty;
    @NotNull
    private BigDecimal qty;
    private String oid;
    private boolean needRecordQtyChange = true;
    //是否为套餐
    private Boolean combo = false;
    /**
     * 套餐行ID
     */
    private BigInteger comboDetailId;

    public Boolean getCombo() {
        return combo;
    }

    public void setCombo(Boolean combo) {
        this.combo = combo;
    }

    public BigInteger getComboDetailId() {
        return comboDetailId;
    }

    public void setComboDetailId(BigInteger comboDetailId) {
        this.comboDetailId = comboDetailId;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public BigInteger getDetailId() {
        return detailId;
    }

    public void setDetailId(BigInteger detailId) {
        this.detailId = detailId;
    }

    public BigInteger getStockSyncRuleId() {
        return stockSyncRuleId;
    }

    public void setStockSyncRuleId(BigInteger stockSyncRuleId) {
        this.stockSyncRuleId = stockSyncRuleId;
    }

    public BigDecimal getUnitQty() {
        return unitQty;
    }

    public void setUnitQty(BigDecimal unitQty) {
        this.unitQty = unitQty;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public boolean isNeedRecordQtyChange() {
        return needRecordQtyChange;
    }

    public void setNeedRecordQtyChange(boolean needRecordQtyChange) {
        this.needRecordQtyChange = needRecordQtyChange;
    }
}
