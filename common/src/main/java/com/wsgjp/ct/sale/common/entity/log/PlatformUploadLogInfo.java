package com.wsgjp.ct.sale.common.entity.log;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.OperationEnum;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * 推送平台日志实体
 *
 * <AUTHOR>
 */
public class PlatformUploadLogInfo {

    /**
     * 仅用于需要明细的网店，直接调用此使用
     */
    List<LogOrder> orderList;
    /**
     * 店铺类型
     */
    private ShopType shopType;

    /**
     * 账套id
     */
    private BigInteger profileId;

    /**
     * 店铺id(系统中的店铺id)
     */
    private BigInteger otypeId;
    /**
     * 日志上传操作枚举
     */
    private OperationEnum operation;

    /**
     * 客户端请求url
     */
    private String url;

    /**
     * 请求网关的url
     */
    private String gatewayUrl;

    /**
     * 当isv将订单推送至第三方时必须填写(如wms)
     * 订单推送目的地url
     */
    private String sendToUrl;

    /**
     * 用户IP地址(不是isv服务商的IP地址)
     */
    private String userIp;

    /**
     * 用户在isv账号体系中的唯一标识
     */
    private String userId;

    /**
     * 用户设备的唯一标识
     */
    private String deviceId;

    /**
     * pdd 设置的浏览器指纹
     */
    private String pati;

    /**
     * 用户要操作的订单id
     */
    private List<String> orderId;

    /**
     * 如果是订单操作 所操作的订单总量
     */
    private long orderTotal;

    /**
     * 针对导出等其它操作。如京东导出订单 请填写导出文件的md5值
     */
    private String data;

    /**
     * 针对对当前操作的结果(如登陆操作结果，订单操作结果)
     */
    private boolean result;

    /**
     * 数据库操作时记录
     */
    private String sql;
    /**
     * 页面url
     *
     * @return
     */
    private String referer;
    /**
     * 抖店日志特殊字段
     *
     * @return
     */
    private String eventId;

    /**
     * 平台接口组通用层补充订单数据字段
     * 平台接口组通用层需要补充数据才会有值
     *
     * @return
     */
    private List<EshopOrderInfo> orderInfos;

    /**
     * 只有枚举类为MODIFY_ORDER_KTYPE(17,"订单审核修改发货仓库")，才会构建
     */
    private List<EshopOrderAndStore> stores;

    public List<EshopOrderAndStore> getStores() {
        if (stores == null) {
            return new ArrayList<>();
        }
        return stores;
    }

    public void setStores(List<EshopOrderAndStore> stores) {
        this.stores = stores;
    }

    public List<LogOrder> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<LogOrder> orderList) {
        this.orderList = orderList;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getGatewayUrl() {
        return gatewayUrl;
    }

    public void setGatewayUrl(String gatewayUrl) {
        this.gatewayUrl = gatewayUrl;
    }

    public String getSendToUrl() {
        return sendToUrl;
    }

    public void setSendToUrl(String sendToUrl) {
        this.sendToUrl = sendToUrl;
    }

    public String getUserIp() {
        return userIp;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public List<String> getOrderId() {
        if (orderId == null) {
            orderId = new ArrayList<>();
        }
        return orderId;
    }

    public void setOrderId(List<String> orderId) {
        this.orderId = orderId;
    }

    public long getOrderTotal() {
        return orderTotal;
    }

    public void setOrderTotal(long orderTotal) {
        this.orderTotal = orderTotal;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public boolean isResult() {
        return result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public OperationEnum getOperation() {
        return operation;
    }

    public void setOperation(OperationEnum operation) {
        this.operation = operation;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getReferer() {
        return referer;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public List<EshopOrderInfo> getOrderInfos() {
        return orderInfos;
    }

    public void setOrderInfos(List<EshopOrderInfo> orderInfos) {
        this.orderInfos = orderInfos;
    }

    public String getPati() {
        return pati;
    }

    public void setPati(String pati) {
        this.pati = pati;
    }
}
