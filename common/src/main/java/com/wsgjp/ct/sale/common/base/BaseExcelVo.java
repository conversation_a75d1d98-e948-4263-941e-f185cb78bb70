package com.wsgjp.ct.sale.common.base;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import java.util.List;
import java.util.Map;

public class BaseExcelVo {
    @ExcelProperty(value = "错误信息")
    private String message;

    /**
     * easyexcel过滤字段
     */
    @ExcelIgnore
    private List<String> excludeColumns;

    /**
     * easyexcel不过滤字段
     */
    @ExcelIgnore
    private List<String> includeColumn;

    /**
     * 自定义标题头部字段(类对应属性名， 表对应列名)
     */
    private Map<Integer, String> headMap;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 获取过滤字段
     * @return
     */
    public List<String> getExcludeColumns() {
        return excludeColumns;
    }

    /**
     * 设置过滤字段
     * @param excludeColumns
     */
    public void setExcludeColumns(List<String> excludeColumns) {
        this.excludeColumns = excludeColumns;
    }

    /**
     * 获取不够率字段
     * @return
     */
    public List<String> getIncludeColumn() {
        return includeColumn;
    }

    /**
     * 设置不过滤字段
     * @param includeColumn
     */
    public void setIncludeColumn(List<String> includeColumn) {
        this.includeColumn = includeColumn;
    }


    public Map<Integer, String> getHeadMap() {
        return headMap;
    }

    public void setHeadMap(Map<Integer, String> headMap) {
        this.headMap = headMap;
    }

}
