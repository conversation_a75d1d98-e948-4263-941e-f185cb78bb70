package com.wsgjp.ct.sale.common.syssecretinfo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wsgjp.ct.sis.client.entity.EncryptFullAdapter;
import com.wsgjp.ct.sis.client.entity.EncryptInfo;
import com.wsgjp.ct.sis.client.entity.SisInfo;
import ngp.idgenerator.UId;
import ngp.utils.Md5Utils;
import org.springframework.beans.BeanUtils;

import java.math.BigInteger;
import java.util.Date;

/**
 * 功能描述 系统加密信息
 *
 * <AUTHOR>
 * @create 2024-11-06
 * @since 1.0.0
 */
public class BaseSysSecretInfo  implements EncryptFullAdapter {

    private BigInteger profileId;
    private BigInteger id;
    /**
     * 可以是收件人，送货人什么的名字信息
     */
    private String fullname;
    /**
     * 收集
     */

    private String mobile;
    /**
     * 电话
     */
    private String phone;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 街道
     */
    private String town;
    /**
     * 详细地址
     */
    private String address;

    /**
     * 全地址
     */
    private String fullAddress;
    /**
     * 加密系统加密id
     */
    private String di;
    /**
     * 姓名的加密id
     */
    private String ri;
    /**
     * 电话的加密id
     */
    private String pi;
    /**
     * 收集的加密id
     */
    private String mi;
    /**
     * 详细地址的加密id
     */
    private String addri;
    /**
     * 来源表
     */
    private String sourceTable;
    private Date createTime;
    private Date updateTime;
    /**
     * 唯一键
     */

    private String hashMark;

    public String getHashMark() {
        String keyStr = String.format("%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s_%s",
                getProfileId(),
                getOrgId(),
                getPi(),
                getRi(),
                getMi(),
                getAddri(),
                getProvince(),
                getCity(),
                getDistrict(),
                getTown(),
                getSourceTable(),
                getFullname(),
                getTradeId());
        return Md5Utils.md5(keyStr);
    }


    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getId() {
        if (id==null) {
            id= UId.newId();
        }
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getFullname() {
        if (null == fullname){
            return "";
        }
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getMobile() {
        if (null == mobile){
            return "";
        }
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPhone() {
        if (null == phone){
            return "";
        }
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProvince() {
        if (null == province){
            return "";
        }
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        if (null == city){
            return "";
        }
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        if (null == district){
            return "";
        }
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getTown() {
        if (null == town){
            return "";
        }
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getAddress() {
        if (null == address){
            return "";
        }
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getFullAddress() {
        if (null == fullAddress){
            return "";
        }
        return fullAddress;
    }

    public void setFullAddress(String fullAddress) {
        this.fullAddress = fullAddress;
    }

    public String getDi() {
        if (null == di){
            return "";
        }
        return di;
    }

    public void setDi(String di) {
        this.di = di;
    }

    public String getRi() {
        if (null == ri){
            return "";
        }
        return ri;
    }

    public void setRi(String ri) {
        this.ri = ri;
    }

    public String getPi() {
        if (null == pi){
            return "";
        }
        return pi;
    }

    public void setPi(String pi) {
        this.pi = pi;
    }

    public String getMi() {
        if (null == mi){
            return "";
        }
        return mi;
    }

    public void setMi(String mi) {
        this.mi = mi;
    }

    public String getAddri() {
        if (null == addri){
            return "";
        }
        return addri;
    }

    public void setAddri(String addri) {
        this.addri = addri;
    }

    public String getSourceTable() {
        if (null == sourceTable){
            return "";
        }
        return sourceTable;
    }

    public void setSourceTable(String sourceTable) {
        this.sourceTable = sourceTable;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    @JsonIgnore
    public EncryptInfo getEncryptSource() {
        EncryptInfo encryptInfo = new EncryptInfo();
        encryptInfo.setId(this.getId());
        encryptInfo.setContactor(this.getFullname());
        encryptInfo.setAddress(this.getAddress());
        encryptInfo.setMobile(this.getMobile());
        encryptInfo.setPhone(this.getPhone());
        encryptInfo.setSisId(this.getDi());
        encryptInfo.setEshopId(BigInteger.ONE);
        return encryptInfo;
    }

    @Override
    public void fillEncryptInfo(SisInfo sisInfo) {
        BeanUtils.copyProperties(sisInfo, this);
        setAddress(sisInfo.getAddress());
        setFullname(sisInfo.getContactor());
        setFullAddress(String.format("%s%s%s%s%s", getProvince(), getCity(), getDistrict(), getTown(), getAddress()));
        setMobile(sisInfo.getMobile());
        setPhone(sisInfo.getPhone());
    }


    /**
     * 真实编码时需要像BillDeliver中那样复杂的实现
     *
     * @return
     */
    @Override
    public boolean needEncrypt() {
        return true;
    }

    @Override
    public void fillDecryptInfo(SisInfo sisInfo) {
        if (sisInfo.getContactor() != null) {
            setFullname(sisInfo.getContactor());
        }

        if (sisInfo.getMobile() != null) {
            setMobile(sisInfo.getMobile());
        }
        if (sisInfo.getPhone() != null) {
            setPhone(sisInfo.getPhone());
        }

        if (sisInfo.getAddress() != null) {
            setAddress(sisInfo.getAddress());
        }

        setFullAddress(String.format("%s%s%s%s%s", getProvince(), getCity(), getDistrict(), getTown(), getAddress()));
    }

    @Override
    public BigInteger getOrgId() {
        return BigInteger.ONE;
    }

    @Override
    public BigInteger getEncryptInfoId() {
        return this.getId();
    }


    @Override
    public String getTradeId() {
        return null;
    }


}
