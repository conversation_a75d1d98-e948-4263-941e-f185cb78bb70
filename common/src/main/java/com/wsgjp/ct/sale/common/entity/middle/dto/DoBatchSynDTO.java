package com.wsgjp.ct.sale.common.entity.middle.dto;

import com.wsgjp.ct.sale.common.entity.middle.vo.BatchInfoVo;

import java.math.BigInteger;
import java.util.List;

public class DoBatchSynDTO {

    private BigInteger eshopId;

    private Integer priceType;

    private List<BatchInfoVo> list;

    public Integer getPriceType() {
        return priceType;
    }

    public void setPriceType(Integer priceType) {
        this.priceType = priceType;
    }

    public List<BatchInfoVo> getList() {
        return list;
    }

    public void setList(List<BatchInfoVo> list) {
        this.list = list;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }
}
