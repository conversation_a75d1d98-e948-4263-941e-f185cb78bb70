<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.common.mapper.NotifyMapper">

    <insert id="insertChangeInfo">
        insert into pl_eshop_sale_order_change_info
        (id, profile_id, trade_order_id, change_type, content, modify_time, eshop_order_id, otype_id,trade_order_detail_id,sub_change_type,producer,customer,unique_key)
        values (#{id}, #{profileId}, #{tradeOrderId}, #{changeType}, #{content}, #{modifyTime}, #{eshopOrderId}, #{otypeId}, #{oid}
               , #{subChangeType}, #{producer}, #{customer}, #{uniqueKey})
    </insert>
</mapper>
