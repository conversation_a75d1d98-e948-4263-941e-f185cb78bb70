<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.common.mapper.SysSecretInfoMapper">
    <insert id="insertSecretInfo">
        insert into sys_secret_info (id,profile_id,fullname,mobile,phone,province,city,district,town,address,full_address,di,ri,pi,mi,addri,source_table,create_time,hash_mark)
        values(#{id},#{profileId},#{fullname},#{mobile},#{phone},#{province},#{city},#{district},#{town},#{address},#{fullAddress},#{di},#{ri},#{pi},#{mi},#{addri},#{sourceTable},#{createTime},#{hashMark})
    </insert>
    <insert id="batchInsertSecretInfo">
        insert into sys_secret_info (id,profile_id,fullname,mobile,phone,province,city,district,town,address,full_address,di,ri,pi,mi,addri,source_table,create_time,hash_mark)
        values
        <foreach collection="secretInfoList" item="item" separator=",">
            (#{item.id},#{item.profileId},#{item.fullname},#{item.mobile},#{item.phone},#{item.province},#{item.city},#{item.district},#{item.town},#{item.address},#{item.fullAddress},#{item.di},#{item.ri},#{item.pi},#{item.mi},#{item.addri},#{item.sourceTable},#{item.createTime},#{item.hashMark})
        </foreach>
    </insert>
    <delete id="deleteSecretInfoByParam">
        delete from sys_secret_info where profile_id=#{queryParam.profileId} and id=#{queryParam.secretId}
    </delete>
    <delete id="deleteSecretInfoByIds">
        delete from sys_secret_info where profile_id=#{profileId} and id in
        <foreach collection="ids" separator="," open="(" close=")"  item="item">
            #{item}
        </foreach>
    </delete>
    <select id="querySecretInfoById" resultType="com.wsgjp.ct.sale.common.syssecretinfo.BaseSysSecretInfo">
        select * from sys_secret_info where profile_id=#{profileId} and id=#{id}
    </select>
    <select id="querySecretInfoByHash" resultType="com.wsgjp.ct.sale.common.syssecretinfo.BaseSysSecretInfo">
        select * from sys_secret_info where profile_id=#{profileId} and hash_mark=#{hashMark} limit 1
    </select>
    <select id="querySecretInfoBySecretDi" resultType="com.wsgjp.ct.sale.common.syssecretinfo.BaseSysSecretInfo">
        select * from sys_secret_info where profile_id=#{profileId} and di=#{di} limit 1
    </select>
    <select id="querySecretInfoByParam" resultType="com.wsgjp.ct.sale.common.syssecretinfo.BaseSysSecretInfo">
        select * from sys_secret_info where profile_id=#{queryParam.profileId}
        <if test="queryParam.secretId!=null">
            and id=#{queryParam.secretId}
        </if>
        <if test="queryParam.di!=null">
            and di=#{queryParam.di}
        </if>
    </select>
    <select id="querySecretInfoByIds" resultType="com.wsgjp.ct.sale.common.syssecretinfo.BaseSysSecretInfo">
        select * from sys_secret_info where profile_id=#{profileId} and id in
        <foreach collection="ids" separator="," open="(" close=")"  item="item">
            #{item}
        </foreach>
    </select>

</mapper>
