package com.wsgjp.ct.sale.platform.sdk.log;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */

public enum BillType implements CodeEnum {
    ORDER(0, "订单"),
    REFUND(1, "售后单");
    private final int code;
    private final String name;

    BillType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }


}
