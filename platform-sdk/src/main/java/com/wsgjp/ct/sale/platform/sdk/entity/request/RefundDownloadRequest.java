package com.wsgjp.ct.sale.platform.sdk.entity.request;

import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.refund.EshopRefundOrderEntity;

/**
 * <AUTHOR>
 */
public class RefundDownloadRequest extends TimeDownloadRequest<EshopRefundOrderEntity> {
    private TradeStatus tradeStatus;

    private boolean increment;

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public boolean isIncrement() {
        return increment;
    }

    public void setIncrement(boolean increment) {
        this.increment = increment;
    }
}
