package com.wsgjp.ct.sale.platform.sdk.entity.eshop;

import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class EshopSaleOrderDetailSimpleEntity {
    private BigInteger id;
    private BigInteger profileId;
    private BigInteger orderId;
    private BigInteger otypeId;
    private BigInteger ptypeId;
    private String platformPtypeId;
    private String platformSkuId;
    private String platformPtypeXcode;
    private String platformPtypeName;
    private String platformPropertiesName;
    private BigDecimal qty;
    private BigInteger comboRowId;
    private BigInteger comboId;
    private boolean combo;
    private String platformJson;
    private String oid;

    private BigDecimal price;
    /**
     * 订单明细交易状态
     */
    private TradeStatus tradeStatus;

    private int processState;

    private RefundStatus refundStatus;

    /**
     * 商品材质类型
     */
    private String productMaterialType;
    public String getProductMaterialType() {
        return productMaterialType;
    }

    public void setProductMaterialType(String productMaterialType) {
        this.productMaterialType = productMaterialType;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getPlatformPtypeId() {
        return platformPtypeId;
    }

    public void setPlatformPtypeId(String platformPtypeId) {
        this.platformPtypeId = platformPtypeId;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getPlatformPtypeXcode() {
        return platformPtypeXcode;
    }

    public void setPlatformPtypeXcode(String platformPtypeXcode) {
        this.platformPtypeXcode = platformPtypeXcode;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigInteger getComboRowId() {
        return comboRowId;
    }

    public void setComboRowId(BigInteger comboRowId) {
        this.comboRowId = comboRowId;
    }

    public BigInteger getComboId() {
        return comboId;
    }

    public void setComboId(BigInteger comboId) {
        this.comboId = comboId;
    }

    public BigInteger getOrderId() {
        return orderId;
    }

    public void setOrderId(BigInteger orderId) {
        this.orderId = orderId;
    }

    public boolean isCombo() {
        return combo;
    }

    public void setCombo(boolean combo) {
        this.combo = combo;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getPlatformJson() {
        return platformJson;
    }

    public void setPlatformJson(String platformJson) {
        this.platformJson = platformJson;
    }

    public String getPlatformPtypeName() {
        return platformPtypeName;
    }

    public void setPlatformPtypeName(String platformPtypeName) {
        this.platformPtypeName = platformPtypeName;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public int getProcessState() {
        return processState;
    }

    public void setProcessState(int processState) {
        this.processState = processState;
    }

    public RefundStatus getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(RefundStatus refundStatus) {
        this.refundStatus = refundStatus;
    }

    public BigInteger getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }


    public String getPlatformPropertiesName() {
        return platformPropertiesName;
    }

    public void setPlatformPropertiesName(String platformPropertiesName) {
        this.platformPropertiesName = platformPropertiesName;
    }
}
