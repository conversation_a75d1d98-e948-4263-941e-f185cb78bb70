package com.wsgjp.ct.sale.platform.sdk.exception;


import com.wsgjp.ct.common.enums.core.enums.SyncFreightStatus;

public class SyncCheckException extends RuntimeException {
    private SyncFreightStatus status;

    public SyncCheckException(SyncFreightStatus status, String message) {
        super(message);
        this.status = status;
    }

    public SyncFreightStatus getStatus() {
        return status;
    }

    public void setStatus(SyncFreightStatus status) {
        this.status = status;
    }
}
