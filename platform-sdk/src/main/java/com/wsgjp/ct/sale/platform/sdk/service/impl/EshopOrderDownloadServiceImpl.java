package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.common.enums.MonitorSourceEnum;
import com.wsgjp.ct.sale.common.enums.PlatformApiEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.bifrost.entity.BifrostApiMonitorTypeEnum;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.constraint.PlatformConstants;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderDetailEntity;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.refund.EshopRefundDetailEntity;
import com.wsgjp.ct.sale.platform.dto.refund.EshopRefundOrderEntity;
import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderByIdRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderCountRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderIncreaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundByTradesRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.OrderCountResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.OrderDownloadResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.entity.response.refund.RefundOrderDownloadResponse;
import com.wsgjp.ct.sale.platform.enums.OrderDownloadType;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.feature.order.*;
import com.wsgjp.ct.sale.platform.feature.plugin.EshopPluginFeature;
import com.wsgjp.ct.sale.platform.feature.refund.EshopRefundByTradeFeature;
import com.wsgjp.ct.sale.platform.sdk.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.sdk.entity.callback.RefundCheckResult;
import com.wsgjp.ct.sale.platform.sdk.entity.request.OrderDownloadRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.RefundCheckRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.TimeDownloadRequest;
import com.wsgjp.ct.sale.platform.sdk.log.BillType;
import com.wsgjp.ct.sale.platform.sdk.log.EshopSaleOrderSyncTaskLog;
import com.wsgjp.ct.sale.platform.sdk.log.EshopSaleOrderSyncTaskLogDetail;
import com.wsgjp.ct.sale.platform.sdk.mapper.EshopNotifyMapper;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopMapper;
import com.wsgjp.ct.sale.platform.sdk.service.EshopOrderDownloadService;
import com.wsgjp.ct.sale.platform.sdk.slice.SliceDownloadContext;
import com.wsgjp.ct.sale.platform.sdk.util.OrderDownloadUtil;
import com.wsgjp.ct.sale.platform.sdk.util.PlatformCommonUtil;
import com.wsgjp.ct.sale.platform.slice.DownloadSlice;
import com.wsgjp.ct.sale.platform.slice.SliceRequest;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class EshopOrderDownloadServiceImpl extends SliceDownloadServiceBase<EshopOrderEntity> implements EshopOrderDownloadService {
    private final EshopNotifyMapper mapper;
    private final MonitorService monitor;

    private final PlatformSdkEshopMapper eshopMapper;

    private static final Logger logger = LoggerFactory.getLogger(EshopOrderDownloadServiceImpl.class);

    public EshopOrderDownloadServiceImpl(EshopNotifyMapper mapper, MonitorService monitor, PlatformSdkEshopMapper eshopMapper) {
        this.mapper = mapper;
        this.monitor = monitor;
        this.eshopMapper = eshopMapper;
    }

    @Override
    public void downloadOrdersByCreateTime(OrderDownloadRequest request) {
        EshopFactory factory = getFactory(request);
        EshopOrderDownloadByCreateTimeFeature feature = factory.getFeature(EshopOrderDownloadByCreateTimeFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持下载订单", request.getSystemParams().getShopType().getPlatformName()));
        }
        boolean checkRdsEnabled = checkRdsEnabled(factory, request);
        EshopPluginFeature pluginFeature = getPlugin(factory);
        SliceDownloadContext<EshopOrderEntity> context = new SliceDownloadContext<>();
        context.setFactory(factory);
        context.setSliceDownloader((feature::downloadByCreateTime));
        context.setTimeExtractor(EshopOrderEntity::getCreateTime);
        int status = request.getTradeStatus().getCode();
        SliceRequest sliceRequest = new SliceRequest();
        sliceRequest.setStatus(status);
        sliceRequest.setIncrement(false);
        context.setSliceParamsList(pluginFeature.getOrderSliceParams(sliceRequest));
        context.setThreadName(PlatformConstants.MANUAL_DOWNLOAD_ORDER_THREAD_NAME);
        if (StringUtils.isNotEmpty(request.getThreadName())) {
            context.setThreadName(request.getThreadName());
        }
        PlatformBaseConfig platformConfig = factory.getConfig();
        context.setSingleThreadCount(platformConfig.getSingleThreadCount());
        context.setForceByApi(!checkRdsEnabled);
        context.setSourceEnum(request.getSourceEnum());
        context.setMonitor(monitor);
        download(request, context);
    }

    private boolean checkRdsEnabled(EshopFactory factory, OrderDownloadRequest request) {
        if (!request.getCheckRdsWithApiEnabled()) {
            return true;
        }
        EshopOrderDownloadCheckCountFeature countFeature = factory.getFeature(EshopOrderDownloadCheckCountFeature.class);
        if (countFeature == null) {
            return true;
        }
        DownloadSlice slice = new DownloadSlice();
        slice.setPage(1);
        slice.setStartTime(request.getStartTime());
        slice.setEndTime(request.getEndTime());
        slice.setStatus(request.getTradeStatus().getCode());
        return countFeature.rdsCountEqualsApiCount(slice);
    }

    @Override
    protected void writeOrderSyncTaskLog(TimeDownloadRequest<EshopOrderEntity> request, SliceDownloadResponse<EshopOrderEntity> response, Throwable exception) {
        EshopSystemParams systemParams = request.getSystemParams();
        EshopSaleOrderSyncTaskLog taskLog = new EshopSaleOrderSyncTaskLog(systemParams.getProfileId(), systemParams.geteShopId(), request.getTaskId(), request.getDownloadType(), DateUtils.getDate());
        if (response != null) {
            taskLog.setResponseTotal(response.getTotal());
            taskLog.setResponseId(response.getResponseId() != null ? response.getResponseId() : "");
            if (CollectionUtils.isNotEmpty(response.getList())) {
                List<EshopSaleOrderSyncTaskLogDetail> taskDetailLogs = new ArrayList<>();
                response.getList().forEach(order -> {
                    EshopSaleOrderSyncTaskLogDetail taskLogDetail = new EshopSaleOrderSyncTaskLogDetail();
                    taskLogDetail.setParentId(taskLog.getId());
                    taskLogDetail.setTradeOrderId(order.getTradeId());
                    taskLogDetail.setRefundId("");
                    taskLogDetail.setDetailType(BillType.ORDER.getCode());
                    taskLogDetail.setPlatformRefundState(order.getRefundStatus() != null ? order.getRefundStatus() : RefundStatus.NONE);
                    taskLogDetail.setPlatformTradeState(order.getTradeStatus() != null ? order.getTradeStatus() : TradeStatus.ABNORMAL);
                    taskDetailLogs.add(taskLogDetail);
                });
                LogService.addRange(taskDetailLogs);
            }
        }
        LogService.add(taskLog);
    }


    @Override
    protected void fillMissData(EshopFactory factory, SliceDownloadResponse<EshopOrderEntity> response, MonitorSourceEnum source) {
        if (response == null) {
            return;
        }
        EshopOrderDownloadRequirementsFeature downloadDetailFeature = factory.getFeature(EshopOrderDownloadRequirementsFeature.class);
        if (downloadDetailFeature != null && downloadDetailFeature.needDownloadDetails(response.getSlice())) {
            downloadOrderDetail(factory, response, source);
        }
        boolean rds = response.getSlice().isRds();
        OrderDownloadUtil.downloadOrderMissingData(factory, response.getList(), rds, OrderDownloadType.CREATE_TIME);
        OrderDownloadUtil.downloadOrderFiledMissingData(factory, response.getList(), mapper);
    }


    private void downloadOrderDetail(EshopFactory factory, SliceDownloadResponse<EshopOrderEntity> response, MonitorSourceEnum source) {
        if (CollectionUtils.isEmpty(response.getList())) {
            return;
        }
        DownloadOrderByIdRequest request = new DownloadOrderByIdRequest();
        request.setShopId(factory.getParams().geteShopId());
        request.setSystemParams(factory.getParams());
        request.setTradeIdList(response.getList().stream().map(EshopOrderEntity::getTradeId).collect(Collectors.toList()));
        request.setSourceEnum(source);
        OrderDownloadResponse orderDownloadResponse = doDownloadOrderByTradeIds(request);
        response.setList(orderDownloadResponse.getOrderList());
    }


    @Override
    public void downloadOrdersByModifyTime(OrderDownloadRequest request) {
        EshopFactory factory = getFactory(request);
        EshopOrderDownloadByUpdateTimeFeature feature = factory.getFeature(EshopOrderDownloadByUpdateTimeFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持自动下载订单", request.getSystemParams().getShopType().getPlatformName()));
        }
        EshopPluginFeature pluginFeature = getPlugin(factory);
        SliceDownloadContext<EshopOrderEntity> context = new SliceDownloadContext<>();
        context.setThreadName(PlatformConstants.INCREASE_DOWNLOAD_ORDER_THREAD_NAME);
        if (StringUtils.isNotEmpty(request.getThreadName())) {
            context.setThreadName(request.getThreadName());
        }
        context.setFactory(factory);
        context.setSliceDownloader((feature::downloadByUpdateTime));
        context.setTimeExtractor(EshopOrderEntity::getModifiedTime);
        SliceRequest sliceRequest = new SliceRequest();
        sliceRequest.setStatus(request.getTradeStatus().getCode());
        sliceRequest.setIncrement(true);
        context.setSliceParamsList(pluginFeature.getOrderSliceParams(sliceRequest));
        context.setSourceEnum(request.getSourceEnum());
        context.setMonitor(monitor);
        download(request, context);
    }

    @Override
    public OrderDownloadResponse downloadOrderOld(DownloadOrderRequest request) {
        OrderDownloadResponse response = new OrderDownloadResponse();
        EshopFactory factory = getFactory(request);
        Boolean rdsEnabled = request.getSystemParams().getRdsEnabled();
        try {
            if (Boolean.TRUE.equals(request.getUseIncrease())) {
                EshopOrderDownloadFromApiIncrementFeature incrFeature = factory.getFeature(EshopOrderDownloadFromApiIncrementFeature.class);
                if (incrFeature == null) {
                    response.setSuccess(false);
                    response.setErrorMessage("平台不支持通过接口下载订单");
                    return response;
                }
                DownloadOrderIncreaseRequest incrRequest = new DownloadOrderIncreaseRequest();
                BeanUtils.copyProperties(request, incrRequest);
                response = incrFeature.downloadOrdersFromApiByModifiedTime(incrRequest);
                downloadOrderDetails(request, response, factory, rdsEnabled, OrderDownloadType.MODIFY_TIME);
                doFillOrderOtherData(factory, response.getOrderList(), rdsEnabled);
                return response;
            }
            EshopOrderDownloadFromApiFeature feature = factory.getFeature(EshopOrderDownloadFromApiFeature.class);
            if (feature == null) {
                response.setSuccess(false);
                response.setErrorMessage("平台不支持通过接口下载订单");
                return response;
            }
            response = feature.downloadOrdersFromApiByCreateTime(request);
            downloadOrderDetails(request, response, factory, rdsEnabled, OrderDownloadType.CREATE_TIME);
            doFillOrderOtherData(factory, response.getOrderList(), rdsEnabled);
            return response;
        } catch (Exception ex) {
            String errMsg = String.format("账套:%s,店铺id:%s,网店：%s通过接口下载订单出现异常了：%s", request.getSystemParams().getProfileId(),request.getSystemParams().geteShopId(), request.getSystemParams().getFullName(), ex.getMessage());
            logger.error(errMsg, ex);
            response.setSuccess(false);
            response.setErrorMessage(errMsg);
            return response;
        }
    }

    @Override
    public OrderDownloadResponse downloadOrderByTradeIds(DownloadOrderByIdRequest request) {
        if (CollectionUtils.isEmpty(request.getTradeIdList())) {
            return new OrderDownloadResponse();
        }
        OrderDownloadResponse response = doDownloadOrderByTradeIds(request);
        EshopFactory factory = getFactory(request);
        Boolean rdsEnabled = request.getSystemParams().getRdsEnabled();
        if (request.isForceApi()) {
            rdsEnabled = false;
        }
        doFillOrderOtherData(factory, response.getOrderList(), rdsEnabled);
        return response;
    }

    public void downloadOrderDetails(DownloadOrderRequest request, OrderDownloadResponse response, EshopFactory factory, boolean isFromRds, OrderDownloadType orderDownloadType) {
        //2.补充订单详情
        EshopOrderDownloadDetailFeature downloadDetailFeature = factory.getFeature(EshopOrderDownloadDetailFeature.class);
        //根据订单是否从rds获取或是全量/增量接口获取综合判断是否需要获取详情
        if (Objects.nonNull(downloadDetailFeature) && downloadDetailFeature.needDownloadDetails(isFromRds, orderDownloadType)) {
            //获取详情通过api的方式,不走rds.如果rds数据完整 也不需要再调用获取详情方法.
            List<String> tradeIds = response.getOrderList().stream().map(EshopOrderEntity::getTradeId).collect(Collectors.toList());
            DownloadOrderByIdRequest idRequest = new DownloadOrderByIdRequest();
            idRequest.setTradeIdList(tradeIds);
            idRequest.setShopId(request.getShopId());
            idRequest.setShopType(request.getShopType());
            idRequest.setSystemParams(request.getSystemParams());
            idRequest.setProcessLogger(request.getProcessLogger());
            EshopOrderDownloadByIdFeature apiFeature = factory.getFeature(EshopOrderDownloadByIdFeature.class);
            OrderDownloadResponse tempResponse = apiFeature.downloadOrdersFromApiByTradeIds(idRequest);
            if (tempResponse != null) {
                response.setErrorMessage(tempResponse.getErrorMessage());
                response.setSuccess(tempResponse.getSuccess());
                response.setOrderList(tempResponse.getOrderList());
                response.setCode(tempResponse.getCode());
                response.setMessage(tempResponse.getMessage());
            }
        }
    }


    private void doFillOrderOtherData(EshopFactory factory, List<EshopOrderEntity> eshopOrders, boolean isRds) {
        if (CollectionUtils.isEmpty(eshopOrders)) {
            return;
        }
        OrderDownloadUtil.downloadOrderMissingData(factory, eshopOrders, isRds, OrderDownloadType.CREATE_TIME);
        OrderDownloadUtil.downloadOrderFiledMissingData(factory, eshopOrders, mapper);
    }

    public OrderDownloadResponse doDownloadOrderByTradeIds(DownloadOrderByIdRequest request) {
        if (CollectionUtils.isEmpty(request.getTradeIdList())) {
            return new OrderDownloadResponse();
        }
        if (request.isForceApi()) {
            request.getSystemParams().setRdsEnabled(false);
        }
        EshopFactory factory = getFactory(request);
        String apiName = PlatformApiEnum.DOWNLOAD_ORDER_DETAIL.getName();
        String sourceName = request.getSourceEnum().getName();
        Tags tags = Tags.of(Tag.of("shopType", factory.getShopType().getName()))
                .and(Tag.of("apiName", apiName))
                .and(Tag.of("sourceName", sourceName));
        monitor.recordSum(BifrostApiMonitorTypeEnum.PL_INTERFACE_API_QPS.getTopic(), tags, request.getTradeIdList().size());
        //todo 监控报错的次数
        EshopOrderDownloadByIdFeature feature = factory.getFeature(EshopOrderDownloadByIdFeature.class);
        if (feature == null) {
            throw new RuntimeException("不支持按单号下载订单");
        }
        return feature.downloadOrdersFromApiByTradeIds(request);

    }

    @Override
    public OrderCountResponse getOrderCount(DownloadOrderCountRequest request) {
        EshopFactory factory = getFactory(request);
        EshopOrderCountFeature feature = factory.getFeature(EshopOrderCountFeature.class);
        if (feature == null) {
            throw new RuntimeException("暂时未实现查询订单数量的接口");
        }
        return feature.getOrderCount(request.getBegin(), request.getEnd());
    }

    @Override
    public RefundCheckResult checkRefund(List<RefundCheckRequest> requestList) {
        RefundCheckResult result = new RefundCheckResult();
        if (CollectionUtils.isEmpty(requestList)) {
            result.setCode(1);
            result.setSuccess(false);
            result.setMessage("入参为空");
            return result;
        }
        //先按网店分组
        try {
            Map<BigInteger, List<RefundCheckRequest>> listMap = requestList.stream().collect(Collectors.groupingBy(RefundCheckRequest::getEshopId));
            for (BigInteger eshopId : listMap.keySet()) {
                List<RefundCheckRequest> eshopRequestList = listMap.get(eshopId);
                if (CollectionUtils.isEmpty(eshopRequestList)) {
                    continue;
                }
                if (null == eshopId || eshopId.compareTo(BigInteger.ZERO) <= 0) {
                    doBuildNoEshopIdResult(result, eshopRequestList);
                    continue;
                }
                doCheckRefundByEshop(result, eshopId, eshopRequestList);
            }
        } catch (Throwable ex) {
            result.setCode(1);
            result.setSuccess(false);
            result.setMessage("退款检查逻辑报错");
            logger.error("退款检查逻辑报错:{}", ex.getMessage(), ex);
        }

        return result;
    }

    private void doBuildNoEshopIdResult(RefundCheckResult result, List<RefundCheckRequest> eshopRequestList) {
        for (RefundCheckRequest request : eshopRequestList) {
            RefundCheckResult.RefundCheckResultData resultData = new RefundCheckResult.RefundCheckResultData();
            resultData.setTradeId(request.getTradeId());
            resultData.setOid(resultData.getOid());
            resultData.setHasRefund(false);
            result.getDataList().add(resultData);
        }
    }

    private void doBuildRefundCheckResult(RefundCheckResult result, OrderDownloadResponse response, List<RefundCheckRequest> eshopRequestList) {
        //todo 如果没有返回售后单则使用订单进行构建结果
        List<EshopRefundOrderEntity> refundOrderList = response.getRefundOrderList();
        List<EshopOrderEntity> orderList = response.getOrderList();
        Map<String, List<EshopRefundOrderEntity>> refundMap = new HashMap<>();
        Map<String, EshopOrderEntity> orderMap = buildOrderMap(orderList);
        if (CollectionUtils.isNotEmpty(refundOrderList)) {
            refundMap = refundOrderList.stream().collect(Collectors.groupingBy(EshopRefundOrderEntity::getTradeId));
        }
        for (RefundCheckRequest request : eshopRequestList) {
            String tradeId = request.getTradeId();
            List<EshopRefundOrderEntity> refundList = refundMap.get(tradeId);
            if (CollectionUtils.isNotEmpty(refundList)) {
                buildRefundCheckResultByRefund(result, request, refundList);
                continue;
            }
            EshopOrderEntity entity = orderMap.get(tradeId);
            if (entity == null) {
                continue;
            }
            buildRefundCheckResultByOrder(result, request, entity);
        }
    }

    private Map<String, EshopOrderEntity> buildOrderMap(List<EshopOrderEntity> orderList) {
        Map<String, EshopOrderEntity> orderMap = new HashMap<>();
        if (CollectionUtils.isEmpty(orderList)) {
            return orderMap;
        }
        for (EshopOrderEntity orderEntity : orderList) {
            String tradeId = orderEntity.getTradeId();
            if (orderMap.containsKey(tradeId)) {
                continue;
            }
            orderMap.put(tradeId, orderEntity);
        }
        return orderMap;
    }

    private void buildRefundCheckResultByRefund(RefundCheckResult result, RefundCheckRequest request, List<EshopRefundOrderEntity> refundList) {
        RefundCheckResult.RefundCheckResultData resultData = new RefundCheckResult.RefundCheckResultData();
        resultData.setTradeId(request.getTradeId());
        resultData.setOid(request.getOid());
        RefundStatus refundStatus = RefundStatus.NONE;
        String oid = request.getOid();
        for (EshopRefundOrderEntity refundOrder : refundList) {
            refundStatus = refundOrder.getRefundStatus();
            List<EshopRefundDetailEntity> refundDetails = refundOrder.getRefundDetails();
            if (CollectionUtils.isEmpty(refundDetails)) {
                continue;
            }
            for (EshopRefundDetailEntity detail : refundDetails) {
                if (detail.getOid().equalsIgnoreCase(oid)) {
                    refundStatus = detail.getRefundState();
                    break;
                }
            }
        }
        resultData.setRefundStatus(refundStatus);
        if (!refundStatus.equals(RefundStatus.NONE) && !refundStatus.equals(RefundStatus.CANCEL)) {
            resultData.setHasRefund(true);
        }
        result.getDataList().add(resultData);
    }

    private void buildRefundCheckResultByOrder(RefundCheckResult result, RefundCheckRequest request, EshopOrderEntity orderEntity) {
        RefundCheckResult.RefundCheckResultData resultData = new RefundCheckResult.RefundCheckResultData();
        resultData.setTradeId(request.getTradeId());
        String oid = request.getOid();
        resultData.setOid(oid);
        RefundStatus refundStatus = orderEntity.getRefundStatus();
        List<EshopOrderDetailEntity> orderDetails = orderEntity.getOrderDetails();
        for (EshopOrderDetailEntity orderDetail : orderDetails) {
            if (!orderDetail.getOid().equals(oid)) {
                continue;
            }
            refundStatus = orderDetail.getRefundStatus();
        }
        resultData.setRefundStatus(refundStatus);
        if (!refundStatus.equals(RefundStatus.NONE)) {
            resultData.setHasRefund(true);
        }
        result.getDataList().add(resultData);
    }

    private void doCheckRefundByEshop(RefundCheckResult result, BigInteger eshopId, List<RefundCheckRequest> eshopRequestList) {
        List<String> tradeIds = eshopRequestList.stream().map(RefundCheckRequest::getTradeId).distinct().collect(Collectors.toList());
        BigInteger profileId = CurrentUser.getProfileId();
        EshopInfo eshopInfo = eshopMapper.getEshopInfoByShopId(profileId, eshopId);
        if (eshopInfo == null) {
            throw new RuntimeException(String.format("账套：%s,网店：%s 查询网店信息返回为空", CurrentUser.getProfileId(), eshopId));
        }
        EshopSystemParams params = PlatformCommonUtil.toSystemParams(eshopInfo);
        DownloadOrderByIdRequest request = new DownloadOrderByIdRequest();
        request.setTradeIdList(tradeIds);
        request.setSystemParams(params);
        request.setShopId(eshopId);
        request.setSourceEnum(MonitorSourceEnum.WMS_REFUND_CHECK);
        OrderDownloadResponse response = doDownloadOrderByTradeIds(request);
        List<EshopOrderEntity> orderList = response.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<EshopRefundOrderEntity> refundOrderEntityList = checkToDownloadRefund(params, orderList);
        response.setRefundOrderList(refundOrderEntityList);
        doBuildRefundCheckResult(result, response, eshopRequestList);
    }

    private List<EshopRefundOrderEntity> checkToDownloadRefund(EshopSystemParams params, List<EshopOrderEntity> orderList) {
        DownloadRefundByTradesRequest request = new DownloadRefundByTradesRequest();
        request.setOrderList(orderList);
        request.setShopType(params.getShopType());
        request.setShopId(params.geteShopId());
        request.setSystemParams(params);
        EshopFactory factory = getFactory(request);
        EshopRefundByTradeFeature refundFeature = factory.getFeature(EshopRefundByTradeFeature.class);
        if (refundFeature == null) {
            return new ArrayList<>();
        }
        try {
            RefundOrderDownloadResponse response = refundFeature.downloadRefundOrderByTrade(request);
            return response.getRefundList();
        } catch (Exception ex) {
            logger.error("账套{}网店{}按照原始订单下载售后单报错{}", CurrentUser.getProfileId(), factory.getParams().getFullName(), ex.getMessage(), ex);
            return new ArrayList<>();
        }
    }
}
