package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.common.enums.MonitorSourceEnum;
import com.wsgjp.ct.sale.platform.constraint.PlatformConstants;
import com.wsgjp.ct.sale.platform.dto.vip.VipCustomerEntity;
import com.wsgjp.ct.sale.platform.entity.request.vip.DownloadVipCustomerByIdRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.entity.response.vip.DownloadVipCustomersResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.feature.plugin.EshopPluginFeature;
import com.wsgjp.ct.sale.platform.feature.vip.EshopVipCustomerByIdFeature;
import com.wsgjp.ct.sale.platform.feature.vip.EshopVipCustomerDownloadByCreateTimeFeature;
import com.wsgjp.ct.sale.platform.feature.vip.EshopVipCustomerDownloadByUpdateTimeFeature;
import com.wsgjp.ct.sale.platform.feature.vip.EshopVipCustomerDownloadRequirementsFeature;
import com.wsgjp.ct.sale.platform.sdk.entity.request.VipCustomerDownloadRequest;
import com.wsgjp.ct.sale.platform.sdk.service.EshopVipDownloadService;
import com.wsgjp.ct.sale.platform.sdk.slice.SliceDownloadContext;
import ngp.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 会员信息下载
 *
 * <AUTHOR>
 */
@Service
public class EshopVipDownloadServiceImpl extends SliceDownloadServiceBase<VipCustomerEntity> implements EshopVipDownloadService {

    @Override
    public void downloadVipCustomersByCreateTime(VipCustomerDownloadRequest request) {
        EshopFactory factory = getFactory(request);
        EshopVipCustomerDownloadByCreateTimeFeature feature = factory.getFeature(EshopVipCustomerDownloadByCreateTimeFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持下会员信息", request.getSystemParams().getShopType().getPlatformName()));
        }
        if (request.getEndTime() == null) {
            request.setEndTime(new Date());
        }
        if (request.getStartTime() == null) {
            request.setStartTime(DateUtils.addDays(request.getEndTime(), -1));
        }
        EshopPluginFeature pluginFeature = getPlugin(factory);
        SliceDownloadContext<VipCustomerEntity> context = new SliceDownloadContext<>();
        context.setFactory(factory);
        context.setThreadName(PlatformConstants.DOWNLOAD_VIP_THREAD_NAME);
        context.setSliceDownloader((feature::downloadByCreateTime));
        context.setTimeExtractor(VipCustomerEntity::getCreateTime);
        context.setSliceParamsList(pluginFeature.getVipCustomerSliceParams());
        download(request, context);
    }

    @Override
    public void downloadVipCustomersByModifyTime(VipCustomerDownloadRequest request) {
        EshopFactory factory = getFactory(request);
        EshopVipCustomerDownloadByUpdateTimeFeature feature = factory.getFeature(EshopVipCustomerDownloadByUpdateTimeFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持自动下会员信息", request.getSystemParams().getShopType().getPlatformName()));
        }
        EshopPluginFeature pluginFeature = getPlugin(factory);
        SliceDownloadContext<VipCustomerEntity> context = new SliceDownloadContext<>();
        context.setFactory(factory);
        context.setThreadName(PlatformConstants.DOWNLOAD_VIP_THREAD_NAME);
        context.setSliceDownloader(feature::downloadByUpdateTime);
        context.setTimeExtractor(VipCustomerEntity::getUpdateTime);
        context.setSliceParamsList(pluginFeature.getVipCustomerSliceParams());
        download(request, context);
    }

    @Override
    protected void fillMissData(EshopFactory factory, SliceDownloadResponse<VipCustomerEntity> response, MonitorSourceEnum source) {
        EshopVipCustomerDownloadRequirementsFeature downloadDetailFeature = factory.getFeature(EshopVipCustomerDownloadRequirementsFeature.class);
        if (downloadDetailFeature == null) {
            return;
        }
        if (downloadDetailFeature.needDownloadVipCustomerDetails(response.getSlice())) {
            downloadDetailFeature.fillVipCustomerInfoByDetail(response.getList());
        }
    }

    @Override
    public DownloadVipCustomersResponse downloadVipCustomersByCustomerIds(DownloadVipCustomerByIdRequest request) {
        EshopFactory factory = getFactory(request);
        EshopVipCustomerByIdFeature feature = factory.getFeature(EshopVipCustomerByIdFeature.class);
        if (feature == null) {
            throw new RuntimeException("不支持按会员ID下载会员信息");
        }
        return feature.downloadVipCustomersByCustomerIds(request);
    }
}
