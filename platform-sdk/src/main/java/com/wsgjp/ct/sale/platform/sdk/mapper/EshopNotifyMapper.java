package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface EshopNotifyMapper {

    /**
     * 获取存入数据库的tmc消息
     *
     * @param profileId
     * @param otypeId
     * @param type
     * @return
     */
    List<EshopNotifyChange> queryMessageChangeSorted(@Param("profileId") BigInteger profileId, @Param("tradeOrderIds") List<String> tradeOrderIds, @Param("otypeId") BigInteger otypeId, @Param("type") int type);
    int queryMessageChangeCount(@Param("profileId") BigInteger profileId, @Param("tradeOrderId") String tradeOrderId, @Param("otypeId") BigInteger otypeId, @Param("type") int type);

    List<EshopNotifyChange> queryMessageChange(@Param("profileId") BigInteger profileId, @Param("tradeOrderIds") List<String> tradeIds, @Param("otypeId") BigInteger otypeId);

    /**
     * 更新拼多多授权同步到云内的状态标志
     * @param appSecret
     * @param profileId
     * @param eshopId
     */
    void updatePddEshopTokenRemark(@Param("appSecret") String appSecret, @Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId);

    /**
     *
     */
    /**
     * 修改淘宝天猫RDS状态
     * @param profileId
     * @param eshopId
     * @param rdsEnabled
     * @param rdsApplyTime
     */
    void updateTBEshopRDS(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("rdsEnabled") Boolean rdsEnabled, @Param("rdsApplyTime") Date rdsApplyTime);

    void updateWxVideoShopToken(@Param("profileId")BigInteger profileId, @Param("eshopId")BigInteger eshopId, @Param("token")String token,@Param("tokenExpireIn") Date tokenExpireIn);

    /**
     * 插入notify消息
     * @param change
     */
    void insertEshopNotifyChange(EshopNotifyChange change);

    void insertEshopNotifyChanges( @Param("notifyChanges") List<EshopNotifyChange> notifyChanges);

    void insertEshopNotifyChangeBySubmit(EshopNotifyChange change);

    void updateDouDianHasTokenExpire(@Param("profileId") BigInteger profileId, @Param("eshopId")BigInteger eshopId, @Param("hasTokenExpireIn") boolean hasTokenExpireIn);
}
