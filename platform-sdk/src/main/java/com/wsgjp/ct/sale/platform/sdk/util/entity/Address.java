package com.wsgjp.ct.sale.platform.sdk.util.entity;

public class Address {
    /// <summary>
    /// 国家（没填默认表示中国）
    /// </summary>
    private String Country;
    /// <summary>
    /// 省份
    /// </summary>
    private String Province;
    /// <summary>
    /// 城市
    /// </summary>
    private String City;
    /// <summary>
    /// 区
    /// </summary>
    private String Area;
    /// <summary>
    /// 街道
    /// </summary>
    private String Town;
    /// <summary>
    /// 详细地址
    /// </summary>
    private String AddressDetail;
    /// <summary>
    /// 邮编
    /// </summary>
    private String PostCode;
    /// <summary>
    /// 收货人姓名
    /// </summary>
    private String Name;
    /// <summary>
    /// 收货人手机号
    /// </summary>
    private String Phone;

    public String getCountry() {
        return Country;
    }

    public void setCountry(String country) {
        Country = country;
    }

    public String getProvince() {
        return Province;
    }

    public void setProvince(String province) {
        Province = province;
    }

    public String getCity() {
        return City;
    }

    public void setCity(String city) {
        City = city;
    }

    public String getArea() {
        return Area;
    }

    public void setArea(String area) {
        Area = area;
    }

    public String getTown() {
        return Town;
    }

    public void setTown(String town) {
        Town = town;
    }

    public String getAddressDetail() {
        return AddressDetail;
    }

    public void setAddressDetail(String addressDetail) {
        AddressDetail = addressDetail;
    }

    public String getPostCode() {
        return PostCode;
    }

    public void setPostCode(String postCode) {
        PostCode = postCode;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getPhone() {
        return Phone;
    }

    public void setPhone(String phone) {
        Phone = phone;
    }
}