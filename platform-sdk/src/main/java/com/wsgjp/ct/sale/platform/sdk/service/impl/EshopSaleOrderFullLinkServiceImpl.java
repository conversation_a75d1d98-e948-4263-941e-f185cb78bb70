package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.entity.log.LogOrder;
import com.wsgjp.ct.sale.common.entity.log.OrderLinkStatusEnum;
import com.wsgjp.ct.sale.common.enums.OperationEnum;
import com.wsgjp.ct.sale.common.log.PlatformBizKeyPointFeedback;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.log.EshopOrderLinkStatus;
import com.wsgjp.ct.sale.platform.sdk.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopMapper;
import com.wsgjp.ct.sale.platform.sdk.service.EshopSaleOrderFullLinkService;
import com.wsgjp.ct.sale.platform.sdk.util.PlatformCommonUtil;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;


/**
 * @Author: wcy
 * @Date: 2023/02/01/9:42
 * @Description:
 */

@Service
public class EshopSaleOrderFullLinkServiceImpl implements EshopSaleOrderFullLinkService {

    private static final Logger logger = LoggerFactory.getLogger(EshopSaleOrderFullLinkServiceImpl.class);
    private final PlatformSdkEshopMapper eshopMapper;


    public EshopSaleOrderFullLinkServiceImpl(PlatformSdkEshopMapper eshopMapper) {
        this.eshopMapper = eshopMapper;

    }


    @Override
    public void fullLinkCollect(BigInteger profileId, BigInteger eshopId, List<String> tradeOrderIds, OrderLinkStatusEnum optionStatus) {
        logger.debug("全链路状态回传:已经调用");
        if (!checkParams(profileId, eshopId, tradeOrderIds, optionStatus)) {
            return;
        }
        //判断传入的账套id是否为压测账套，如果是非压测账套时，直接返回，不做任何处理。
        if (!CurrentUser.isYcFlag()) {
            logger.debug("全链路状态回传判断:此账套不是压测账号");
            return;
        }
        //整体异步调用
        try{
            ThreadPool pool = ThreadPoolFactory.build("order-relate");
            pool.executeAsync(invoker -> {
                //根据eshopid实时查询数据库获取平台类型，判断该平台是否需要做数据回传
                //需要回传的情况，将传入的数据回传给第三方平台
                EshopInfo eshopInfo = eshopMapper.getEshopInfoByShopId(profileId, eshopId);
                EshopSystemParams systemParams = PlatformCommonUtil.toSystemParams(eshopInfo);
                ShopType shopType = systemParams.getShopType();
                EshopFactory factory = EshopFactoryManager.create(shopType, systemParams);
                EshopOrderLinkStatus fullLinkFeature = factory.getFeature(EshopOrderLinkStatus.class);
                if (null == fullLinkFeature){
                    logger.debug(String.format("全链路埋点回传，账套：%s，网店：%s，平台不支持全链路回传状态",profileId,eshopId));
                    return;
                }
                fullLinkFeature.uploadOrderLinkStatus(optionStatus,tradeOrderIds);
                logger.debug("全链路状态回传:上传成功");
            }, "异步执行全链路埋点回传");
        }catch (Exception e){
            logger.error(String.format("全链路埋点回传，账套：%s，网店：%s，失败原因：%s",profileId,eshopId,e.getMessage()));
        }
    }

    @Override
    public void feedback(List<LogOrder> orders, OperationEnum operation) {
        // todo 此接口的实现类在sale-biz，由于仓储打包是没有给sale-biz所以会加载失败
        PlatformBizKeyPointFeedback feedback = null;
        try {
            feedback = BeanUtils.getBean(PlatformBizKeyPointFeedback.class);
        }catch (NoSuchBeanDefinitionException e){
            logger.error("未找到PlatformBizKeyPointFeedback:" + e.getMessage(),e);
        }
        if (feedback == null){
            logger.error("PlatformBizKeyPointFeedback为空!");
            return;
        }
        feedback.feedback(orders, operation);

    }

    private Boolean checkParams(BigInteger profileId, BigInteger eshopId, List<String> tradeOrderIds, OrderLinkStatusEnum optionStatus) {
        if (null == profileId || BigInteger.ZERO.compareTo(profileId) == 0){
            logger.info("全链路埋点回传，账套id为空");
            return false;
        }
        if (null == eshopId || BigInteger.ZERO.compareTo(eshopId) == 0){
            logger.info(String.format("全链路埋点回传，账套：%s，网店id为空",profileId));
            return false;
        }
        if (null == tradeOrderIds || tradeOrderIds.size() == 0){
            logger.info(String.format("全链路埋点回传，账套：%s，网店：%s，订单编号为空",profileId,eshopId));
            return false;
        }
        if (null == optionStatus){
            logger.error(String.format("全链路埋点回传，账套：%s，网店：%s，回传状态为空",profileId,eshopId));
            return false;
        }
        return true;
    }


}
