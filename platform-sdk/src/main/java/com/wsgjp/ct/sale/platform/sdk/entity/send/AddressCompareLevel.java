package com.wsgjp.ct.sale.platform.sdk.entity.send;

public enum AddressCompareLevel {

    //详细地址
    DETAIL_ADDRESS(0,"DETAIL_ADDRESS"),
    //四级地址
    TOWN(1,"TOWN"),
    //区
    DISTRICT(2,"DISTRICT"),
    //市
    CITY(3,"CITY"),
    //省
    PROVINCE(4,"PROVINCE");


    private final Integer code;
    private final String desc;

    AddressCompareLevel(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
}
