package com.wsgjp.ct.sale.platform.sdk.entity.send;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillCreateType;
import com.wsgjp.ct.common.enums.core.enums.SyncFreightStatus;
import com.wsgjp.ct.sale.platform.entity.entities.BillSerialnoSon;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.BatchInfo;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.SyncFreightInfo;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR> 2024/1/12 10:16
 */
public class DeliverSendDetail {
    @ApiModelProperty(value = "交易单Id", required = true)
    private BigInteger deliverOrderId;
    @ApiModelProperty(value = "交易单明细Id", required = true)
    private BigInteger deliverDetailId;
    @ApiModelProperty(value = "原始订单明细Id", required = true)
    private BigInteger orderDetailId;
    @ApiModelProperty(value = "原始订单Id", required = true)
    private BigInteger orderId;
    @ApiModelProperty(value = "线上订单id", required = true)
    private String tradeId;
    @ApiModelProperty(value = "线上售后单号", required = true)
    private String refundId;
    @ApiModelProperty(value = "线上订单明细id", required = true)
    private String oid;
    @ApiModelProperty(value = "平台商品id", required = true)
    private String onlinePtypeId;
    @ApiModelProperty(value = "平台商品skuId", required = true)
    private String onlineSkuId;
    @ApiModelProperty(value = "平台商品商家编码", required = true)
    private String xcode;
    @ApiModelProperty(value = "删除状态", required = true)
    private int deleted;
    @ApiModelProperty(value = "辅助单位数量", required = true)
    private BigDecimal unitQty;
    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal qty;
    @ApiModelProperty(value = "物流信息")
    private List<SyncFreightInfo> freightInfoList;
    @ApiModelProperty(value = "同步单号状态", required = true)
    private SyncFreightStatus status;
    @ApiModelProperty(value = "批次信息")
    private List<BatchInfo> batchInfos;
    @ApiModelProperty(value = "序列号")
    private List<BillSerialnoSon> serialNoSonList;
    @ApiModelProperty("是否套餐")
    private boolean combo;
    @ApiModelProperty("套餐行的ID")
    private BigInteger comboRowId;
    @ApiModelProperty(value = "订单创建方式,直接取发货单的", required = true)
    private BillCreateType createType;
    @ApiModelProperty(value = "是否赠品", required = true)
    private boolean gift;

    public BigInteger getDeliverOrderId() {
        return deliverOrderId;
    }

    public void setDeliverOrderId(BigInteger deliverOrderId) {
        this.deliverOrderId = deliverOrderId;
    }

    public BigInteger getDeliverDetailId() {
        return deliverDetailId;
    }

    public void setDeliverDetailId(BigInteger deliverDetailId) {
        this.deliverDetailId = deliverDetailId;
    }

    public BigInteger getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(BigInteger orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public BigInteger getOrderId() {
        return orderId;
    }

    public void setOrderId(BigInteger orderId) {
        this.orderId = orderId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getOnlinePtypeId() {
        return onlinePtypeId;
    }

    public void setOnlinePtypeId(String onlinePtypeId) {
        this.onlinePtypeId = onlinePtypeId;
    }

    public String getOnlineSkuId() {
        return onlineSkuId;
    }

    public void setOnlineSkuId(String onlineSkuId) {
        this.onlineSkuId = onlineSkuId;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public int getDeleted() {
        return deleted;
    }

    public void setDeleted(int deleted) {
        this.deleted = deleted;
    }

    public BigDecimal getUnitQty() {
        return unitQty;
    }

    public void setUnitQty(BigDecimal unitQty) {
        this.unitQty = unitQty;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public List<SyncFreightInfo> getFreightInfoList() {
        return freightInfoList;
    }

    public void setFreightInfoList(List<SyncFreightInfo> freightInfoList) {
        this.freightInfoList = freightInfoList;
    }

    public SyncFreightStatus getStatus() {
        return status;
    }

    public void setStatus(SyncFreightStatus status) {
        this.status = status;
    }

    public List<BatchInfo> getBatchInfos() {
        return batchInfos;
    }

    public void setBatchInfos(List<BatchInfo> batchInfos) {
        this.batchInfos = batchInfos;
    }

    public List<BillSerialnoSon> getSerialNoSonList() {
        return serialNoSonList;
    }

    public void setSerialNoSonList(List<BillSerialnoSon> serialNoSonList) {
        this.serialNoSonList = serialNoSonList;
    }

    public boolean isCombo() {
        return combo;
    }

    public void setCombo(boolean combo) {
        this.combo = combo;
    }

    public BigInteger getComboRowId() {
        return comboRowId;
    }

    public void setComboRowId(BigInteger comboRowId) {
        this.comboRowId = comboRowId;
    }

    public BillCreateType getCreateType() {
        return createType;
    }

    public void setCreateType(BillCreateType createType) {
        this.createType = createType;
    }

    public boolean isGift() {
        return gift;
    }

    public void setGift(boolean gift) {
        this.gift = gift;
    }
}
