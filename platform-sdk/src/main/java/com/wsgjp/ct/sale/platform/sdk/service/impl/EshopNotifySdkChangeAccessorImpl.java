package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.dto.order.PromiseMessage;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.sdk.mapper.EshopNotifyMapper;
import com.wsgjp.ct.sale.platform.support.EshopNotifyChangeAccessor;
import ngp.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class EshopNotifySdkChangeAccessorImpl implements EshopNotifyChangeAccessor {
    private final EshopNotifyMapper mapper;

    public EshopNotifySdkChangeAccessorImpl(EshopNotifyMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<PromiseMessage> getPromiseMessage(BigInteger profileId, BigInteger eshopId, List<String> tradeIds, int type) {
        List<EshopNotifyChange> changeList = mapper.queryMessageChangeSorted(profileId, tradeIds, eshopId, type);
        if (changeList == null || changeList.size() == 0) {
            return null;
        }
        List<PromiseMessage> promiseList = new ArrayList<>();
        for (EshopNotifyChange eshopNotifyChange : changeList) {
            if (StringUtils.isBlank(eshopNotifyChange.getContent())) {
                continue;
            }
            PromiseMessage promise = JsonUtils.toObject(eshopNotifyChange.getContent(), PromiseMessage.class);
            promiseList.add(promise);
        }
        return promiseList;
    }

    @Override
    public List<EshopNotifyChange> getEshopNotifyChangeMessage(BigInteger profileId, BigInteger eshopId, List<String> tradeIds, TMCType tmcType) {
        return mapper.queryMessageChangeSorted(profileId, tradeIds, eshopId, tmcType.getCode());
    }


    @Override
    public void updatePddEshopTokenRemark(String appSecret, BigInteger profileId, BigInteger eshopId) {
        mapper.updatePddEshopTokenRemark(appSecret,profileId,eshopId);
    }

    @Override
    public void updateTBEshopRDS(BigInteger profileId, BigInteger eshopId, Boolean rdsEnabled, Date rdsApplyTime) {
        mapper.updateTBEshopRDS(profileId,eshopId,rdsEnabled,rdsApplyTime);
    }

    @Override
    public void updateWxVideoShopToken(BigInteger profileId, BigInteger eshopId, String token) {
        mapper.updateWxVideoShopToken(profileId,eshopId,token,null);
    }

    @Override
    public void updateWxVideoShopToken(BigInteger profileId, BigInteger eshopId, String token, Date tokenExpireIn) {
        mapper.updateWxVideoShopToken(profileId,eshopId,token,tokenExpireIn);
    }

    @Override
    public void updateDouDianHasTokenExpire(@Param("profileId")BigInteger profileId, @Param("eshopId")BigInteger eshopId, @Param("hasTokenExpireIn")boolean hasTokenExpireIn) {
        mapper.updateDouDianHasTokenExpire(profileId,eshopId,hasTokenExpireIn);
    }
}
