package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.platform.entity.request.delivery.*;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.delivery.*;

/**
 * 揽收单处理
 */
public interface EshopPickupService {

    CreatePickupOrderResponse createPickupOrder(CreatePickupRequest request);

    /**
     * 查询揽收单
     */
    QueryPickupOrderResponse queryPickupOrder(QueryPickupOrderRequest request);

    /**
     * 打印箱唛
     */
    CreatePdfResponse createShippingMarkPdf(CreateShippingMarkPdfRequest request);


    /**
     * 打印打印货品标签
     */
    CreatePdfResponse createScItemBarcodePdf(CreateScItemBarcodeRequest request);

    /**
     * 打印揽收面单
     */
    CreatePdfResponse createPickupShippingMarkPdf(CreatePickupShippingMarkPdfRequet request);

    /**
     * 批量填写自行发货信息生成自送面单
     */
    CreateFullCustodySelfDeliveryPdfResponse createFullCustodySelfDeliveryPdf(CreateFullCustodySelfDeliveryPdfRequest request);

    /**
     * 打印自送面单
     */
    CreatePdfResponse printFullCustodySelfDeliveryPdf(PrintFullCustodySelfDeliveryPdfRequest request);

    /**
     * 取消揽收单
     */
    BaseResponse cancelPickupOrder(CancelPickupOrderRequest request);

    /**
     * 批量修改自行发货信息生成自送面单
     */
    BaseResponse updateFullCustodySelfDeliveryPdf(CreateFullCustodySelfDeliveryPdfRequest request);

    /**
     * 取消自送单
     */
    BaseResponse cancelSelfDelivery(CancelSelfDeliveryRequest request);

    /**
     * 创建发货单
     * @param request
     * @return
     */
    CreateCoErpDeliveryResponse createCoErpDelivery(CreateCoErpDeliveryRequest request);

    /**
     * 取消发货单
     * @param request
     * @return
     */
    BaseResponse cancelCoErpDelivery(CancelCoErpDeliveryRequest request);

    /**
     *查询揽收时间
     * @param request
     * @return
     */
    QueryPickupAvailableDateResponse queryPickupAvailableDate(QueryPickupAvailableDateRequest request);


    /**
     * 通过发货单号查询自寄单号
     * @param request
     * @return
     */
    QuerySelfDeliveryOrderResponse querySelfDeliveryNoByConsignOrderNo(QuerySelfDeliveryOrderRequest request);
}
