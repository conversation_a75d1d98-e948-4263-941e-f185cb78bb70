package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecord;
import com.wsgjp.ct.sale.platform.sdk.entity.send.EshopFreightSyncRecordQueryParams;
import com.wsgjp.ct.sale.platform.sdk.entity.send.FreightBillNoSyncRecordUpdateInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PlatformSdkEshopFreightSyncRecordMapper {
    int add(@Param("addList") List<EshopFreightSyncRecord> addList);

    List<EshopFreightSyncRecord> list(EshopFreightSyncRecordQueryParams queryParams);

    void update(@Param("profileId") BigInteger profileId, @Param("updateInfos") List<FreightBillNoSyncRecordUpdateInfo> updateList);

    List<EshopFreightSyncRecord> queryById(@Param("profileId") BigInteger profileId,@Param("id") BigInteger id);
}
