package com.wsgjp.ct.sale.platform.sdk.entity.send;

import com.wsgjp.ct.sale.platform.sdk.entity.BaseQuery;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class EshopFreightSyncRecordQueryParams extends BaseQuery {
    private List<BigInteger> deliverOrderIds;
    private List<BigInteger> warehouseTaskIds;

    public List<BigInteger> getDeliverOrderIds() {
        return deliverOrderIds;
    }

    public void setDeliverOrderIds(List<BigInteger> deliverOrderIds) {
        this.deliverOrderIds = deliverOrderIds;
    }

    public List<BigInteger> getWarehouseTaskIds() {
        return warehouseTaskIds;
    }

    public void setWarehouseTaskIds(List<BigInteger> warehouseTaskIds) {
        this.warehouseTaskIds = warehouseTaskIds;
    }
}
