package com.wsgjp.ct.sale.platform.sdk.entity.send;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillCreateType;
import com.wsgjp.ct.bill.core.handle.entity.enums.SelfDeliveryModeEnum;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.AddressInfo;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.PlatformOperator;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.SyncFreightInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "同步物流单号请求体")
public class SyncFreightBillNoRequest {
    @ApiModelProperty(value = "发货单的主键", required = true)
    private BigInteger deliverOrderId;
    @ApiModelProperty(value = "任务单主键", required = true, notes = "如果没有仓储环节，请传0")
    private BigInteger warehouseTaskId;
    @ApiModelProperty(value = "店铺Id", required = true)
    private BigInteger otypeId;
    @ApiModelProperty(value = "线上订单id", required = true)
    private String tradeId;
    @ApiModelProperty(value = "线上售后单号", required = true)
    private String refundId;
    @ApiModelProperty(value = "是否拆分", required = true)
    private boolean spilt;
    @ApiModelProperty(value = "是否合并", required = true)
    private boolean merge;
    @ApiModelProperty(value = "订单创建方式,直接取发货单的", required = true)
    private BillCreateType createType;
    @ApiModelProperty(value = "发货方式", required = true)
    private SelfDeliveryModeEnum deliveryMode;
    @ApiModelProperty(value = "订单提醒标记", required = true)
    private List<MarkData> deliverMarks;
    @ApiModelProperty(value = "发货地址信息")
    private AddressInfo sendAddress;
    @ApiModelProperty(value = "退件地址信息")
    private AddressInfo returnAddress;
    @ApiModelProperty(value = "物流信息")
    private List<SyncFreightInfo> freightInfoList = new ArrayList<>();
    @ApiModelProperty(value = "平台操作员信息")
    private PlatformOperator platformOperator;

    @ApiModelProperty(value = "订单发货明细")
    private List<SyncFreightBillNoDetail> details;
    @ApiModelProperty(value = "发货单di值,部分特殊业务需要解密后发货")
    private String di;

    @ApiModelProperty(value = "平台仓库编码")
    private String platformStoreCode;

    @ApiModelProperty(value = "卖家备注")
    private String remark;
    @ApiModelProperty(value = "交易状态", required = true)
    private TradeStatus tradeStatus;

    /**
     * 线上发货地址ID
     */
    @ApiModelProperty(value = "线上发货地址ID")
    private String sendAddressId;
    /**
     * 线上发货仓库ID
     */
    @ApiModelProperty(value = "线上发货仓库ID")
    private String sendWarehouseId;

    @ApiModelProperty(value = "已创建的发货单单号")
    private String shipOrderNo;

    @ApiModelProperty(value = "出仓单号")
    private String storageNo;

    private String billNo;

    public String getShipOrderNo() {
        return shipOrderNo;
    }

    public void setShipOrderNo(String shipOrderNo) {
        this.shipOrderNo = shipOrderNo;
    }

    public String getDi() {
        return di;
    }

    public void setDi(String di) {
        this.di = di;
    }

    public BigInteger getDeliverOrderId() {
        return deliverOrderId;
    }

    public void setDeliverOrderId(BigInteger deliverOrderId) {
        this.deliverOrderId = deliverOrderId;
    }

    public BigInteger getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(BigInteger warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public boolean isSpilt() {
        return spilt;
    }

    public void setSpilt(boolean spilt) {
        this.spilt = spilt;
    }

    public boolean isMerge() {
        return merge;
    }

    public void setMerge(boolean merge) {
        this.merge = merge;
    }

    public BillCreateType getCreateType() {
        return createType;
    }

    public void setCreateType(BillCreateType createType) {
        this.createType = createType;
    }

    public SelfDeliveryModeEnum getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(SelfDeliveryModeEnum deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public List<MarkData> getDeliverMarks() {
        return deliverMarks;
    }

    public void setDeliverMarks(List<MarkData> deliverMarks) {
        this.deliverMarks = deliverMarks;
    }

    public AddressInfo getSendAddress() {
        return sendAddress;
    }

    public void setSendAddress(AddressInfo sendAddress) {
        this.sendAddress = sendAddress;
    }

    public AddressInfo getReturnAddress() {
        return returnAddress;
    }

    public void setReturnAddress(AddressInfo returnAddress) {
        this.returnAddress = returnAddress;
    }

    public List<SyncFreightInfo> getFreightInfoList() {
        return freightInfoList;
    }

    public void setFreightInfoList(List<SyncFreightInfo> freightInfoList) {
        this.freightInfoList = freightInfoList;
    }

    public PlatformOperator getPlatformOperator() {
        return platformOperator;
    }

    public void setPlatformOperator(PlatformOperator platformOperator) {
        this.platformOperator = platformOperator;
    }

    public List<SyncFreightBillNoDetail> getDetails() {
        if (details == null) {
            details = new ArrayList<>();
        }
        return details;
    }

    public void setDetails(List<SyncFreightBillNoDetail> details) {
        this.details = details;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getFreightInfoString() {
        if (CollectionUtils.isEmpty(getFreightInfoList())) {
            return "";
        }
        getFreightInfoList().sort(Comparator.comparing(SyncFreightInfo::getFreightBillNo));
        return JsonUtils.toJson(getFreightInfoList());
    }

    public String getPlatformStoreCode() {
        return platformStoreCode;
    }

    public void setPlatformStoreCode(String platformStoreCode) {
        this.platformStoreCode = platformStoreCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public String getSendAddressId() {
        return sendAddressId;
    }

    public void setSendAddressId(String sendAddressId) {
        this.sendAddressId = sendAddressId;
    }

    public String getSendWarehouseId() {
        return sendWarehouseId;
    }

    public void setSendWarehouseId(String sendWarehouseId) {
        this.sendWarehouseId = sendWarehouseId;
    }

    public String getStorageNo() {
        return storageNo;
    }

    public void setStorageNo(String storageNo) {
        this.storageNo = storageNo;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }
}
