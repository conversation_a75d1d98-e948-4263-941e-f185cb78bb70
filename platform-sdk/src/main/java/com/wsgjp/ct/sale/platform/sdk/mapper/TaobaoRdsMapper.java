package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.dto.order.RdsOrderEntity;
import com.wsgjp.ct.sale.platform.dto.product.RdsProductEntity;
import com.wsgjp.ct.sale.platform.dto.refund.RdsRefundEntity;
import com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderByIdsRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest;
import com.wsgjp.ct.sale.platform.entity.request.product.QueryRdsProductByIdRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface TaobaoRdsMapper {
    /**
     * 根据创建时间查询订单
     *
     * @param param
     * @return
     */
    List<RdsOrderEntity> queryRdsOrdersByCreateTime(QueryRdsOrderRequest param);

    /**
     *
     * 获取创建时间范围内订单条数
     *
     * @param param
     * @return
     */
    Integer countRdsOrdersByCreateTime(QueryRdsOrderRequest param);

    /**
     * 根据修改时间查询订单
     *
     * @param param
     * @return
     */
    List<RdsOrderEntity> queryRdsOrdersByModifyTime(QueryRdsOrderRequest param);

    /**
     *
     * 获取修改时间范围内订单条数
     *
     * @param param
     * @return
     */
    Integer countRdsOrdersByModifyTime(QueryRdsOrderRequest param);

    /**
     * 根据查询淘宝RDS单据信息，
     *
     * @param request selleId+订单Id列表
     * @return 订单列表
     */
    List<RdsOrderEntity> queryRdsOrdersByTradeIds(QueryRdsOrderByIdsRequest request);

    /**
     * 通过售后单号查询售后单列表
     * @param refundIds 售后单号
     * @param sellerNick 卖家昵称
     * @return 售后单列表
     */
    List<RdsRefundEntity> queryRdsRefundByRefundIds(@Param("sellerNick") String sellerNick, @Param("refundIds") List<String> refundIds);

    /**
     * 通过订单号查询售后单列表
     * @param tradeIds 订单号
     * @param sellerNick 卖家昵称
     * @return 售后单列表
     */
    List<RdsRefundEntity> queryRdsRefundByTradeIds(@Param("sellerNick") String sellerNick, @Param("tradeIds")  List<String> tradeIds);

    /**
     *
     * 根据创建时间查询RDS售后信息，
     *
     * @param request
     * @return
     */
    List<RdsRefundEntity> queryRdsRefundsByCreateTime(QueryRdsOrderRequest request);

    /**
     *
     * 根据创建时间查询RDS售后条数，
     *
     * @param request
     * @return
     */
    Integer countRdsRefundsByCreateTime(QueryRdsOrderRequest request);

    /**
     *
     * 根据修改时间查询RDS售后信息，
     *
     * @param request
     * @return
     */
    List<RdsRefundEntity> queryRdsRefundsByModifyTime(QueryRdsOrderRequest request);

    /**
     *
     * 根据修改时间查询RDS售后条数，
     *
     * @param request
     * @return
     */
    Integer countRdsRefundsByModifyTime(QueryRdsOrderRequest request);


    /**
     * 获取商品下载条数
     * @param request
     * @return
     */
    Integer countRdsProducts(QueryRdsOrderRequest request);

    /**
     * 获取商品
     * @param request
     * @return
     */
    List<RdsProductEntity> queryRdsProducts(QueryRdsOrderRequest request);

    /**
     * 获取商品
     * @param request
     * @return
     */
    List<RdsProductEntity> queryRdsProductsByNumIds(QueryRdsProductByIdRequest request);
}
