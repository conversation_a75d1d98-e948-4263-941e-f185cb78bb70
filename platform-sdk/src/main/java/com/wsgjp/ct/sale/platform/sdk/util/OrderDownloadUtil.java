package com.wsgjp.ct.sale.platform.sdk.util;

import cn.hutool.core.collection.ListUtil;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.order.entity.OrderInvoice;
import com.wsgjp.ct.sale.platform.dto.order.entity.ReceiverInfo;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.enums.InvoiceCategory;
import com.wsgjp.ct.sale.platform.enums.InvoiceType;
import com.wsgjp.ct.sale.platform.enums.OrderDownloadType;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.feature.order.*;
import com.wsgjp.ct.sale.platform.sdk.mapper.EshopNotifyMapper;
import com.wsgjp.ct.sale.platform.sdk.util.entity.Address;
import com.wsgjp.ct.sale.platform.sdk.util.entity.AddressNotify;
import com.wsgjp.ct.sale.platform.sdk.util.entity.ExpressNotify;
import com.wsgjp.ct.sale.platform.sdk.util.entity.InvoiceNotify;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2023/10/7 15:31
 */
public class OrderDownloadUtil {
    private static final Logger logger = LoggerFactory.getLogger(OrderDownloadUtil.class);

    public static void downloadOrderMissingData(EshopFactory factory, List<EshopOrderEntity> orderList, boolean isFromRds, OrderDownloadType orderDownloadType) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        // 是否需要调用接口进行其他数据填充
        try {
            FillMissDataByApiRdsFeature fillMissDataByApiRdsFeature =  factory.getFeature(FillMissDataByApiRdsFeature.class);
            if (fillMissDataByApiRdsFeature != null) {
                fillMissDataByApiRdsFeature.fillMissDataByApiRdsFeature(orderList,isFromRds,orderDownloadType);
            }
        } catch (Exception e) {
            logger.error("调用接口填充数据失败,错误信息:{},账套id:{},店铺id：{}", e.getMessage(), factory.getParams().getProfileId(), factory.getParams().geteShopId(), e);
        }

        // 是否需要调用数据库进行其他数据填充
        try {
            FillMissDataByLocalRdsFeature fillMissDataByLocalRdsFeature =  factory.getFeature(FillMissDataByLocalRdsFeature.class);
            if (fillMissDataByLocalRdsFeature != null) {
                fillMissDataByLocalRdsFeature.fillMissDataByLocalRdsFeature(orderList,isFromRds,orderDownloadType);
            }
        } catch (Exception e) {
            logger.error("调用数据库填充数据失败,错误信息:{},账套id:{},店铺id：{}", e.getMessage(), factory.getParams().getProfileId(), factory.getParams().geteShopId(), e);
        }
    }

    public static void downloadOrderFiledMissingData(EshopFactory factory, List<EshopOrderEntity> orderList, EshopNotifyMapper mapper) {

        // 通用逻辑处理tmc改地址等消息
        try {
            buildOrderChange(orderList, mapper);
        } catch (Exception e) {
            logger.error("通用逻辑处理tmc改地址等消息失败,错误信息:{},账套id:{},店铺id：{}", e.getMessage(), factory.getParams().getProfileId(), factory.getParams().geteShopId(), e);
        }

        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        // 是否需要调用接口进行其他数据填充
        try {
            FillMissDataByApiFeature fillMissDataByApiFeature =  factory.getFeature(FillMissDataByApiFeature.class);
            if (fillMissDataByApiFeature != null) {
                fillMissDataByApiFeature.fillMissDataByApiFeature(orderList);
            }
        } catch (Exception e) {
            logger.error("调用接口填充数据失败,错误信息:{},账套id:{},店铺id：{}", e.getMessage(), factory.getParams().getProfileId(), factory.getParams().geteShopId(), e);
        }

        // 是否需要调用数据库进行其他数据填充
        try {
            FillMissDataByLocalFeature fillMissDataByLocalFeature =  factory.getFeature(FillMissDataByLocalFeature.class);
            if (fillMissDataByLocalFeature != null) {
                fillMissDataByLocalFeature.fillMissDataByLocalFeature(orderList);
            }
        } catch (Exception e) {
            logger.error("调用数据库填充数据失败,错误信息:{},账套id:{},店铺id：{}", e.getMessage(), factory.getParams().getProfileId(), factory.getParams().geteShopId(), e);
        }
    }

    public static void buildOrderChange(List<EshopOrderEntity> orderList,EshopNotifyMapper mapper) {
        if (orderList == null || orderList.size() == 0) {
            return;
        }
        Map<BigInteger, List<EshopOrderEntity>> map = orderList.stream().collect(Collectors.groupingBy(EshopOrderEntity::getEshopId));
        if (map == null || map.size() == 0) {
            return;
        }
        for (BigInteger key : map.keySet()
        ) {
            List<EshopOrderEntity> itemEshopOrder = map.get(key);
            buildItemOrderChange(itemEshopOrder,mapper);
        }
    }

    private static void buildItemOrderChange(List<EshopOrderEntity> itemEshopOrder,EshopNotifyMapper mapper) {
        if (CollectionUtils.isEmpty(itemEshopOrder)) {
            return;
        }
        List<String> tradeIds = itemEshopOrder.stream().map(EshopOrderEntity::getTradeId).collect(Collectors.toList());
        BigInteger otypeId = itemEshopOrder.stream().findFirst().get().getEshopId();
        List<EshopNotifyChange> changes = queryEshopNotityChange(CurrentUser.getProfileId(), tradeIds, otypeId,mapper);
        if (CollectionUtils.isEmpty(changes)) {
            return;
        }
        changes = changes.stream().filter(c -> null != c.getType()).collect(Collectors.toList());
        for (EshopOrderEntity itemOrder : itemEshopOrder) {
            Optional<EshopNotifyChange> changeMap = changes.stream().filter(x -> x.getTradeOrderId().equals(itemOrder.getTradeId())).findAny();
            if (!changeMap.isPresent()) {
                continue;
            }
            Map<TMCType, List<EshopNotifyChange>> itemOrderChangeMap = changes.stream().filter(x -> x.getTradeOrderId().equals(itemOrder.getTradeId()))
                    .collect(Collectors.groupingBy(EshopNotifyChange::getType));
            buildItemChange(itemOrderChangeMap, itemOrder);
        }
    }

    private static void buildItemChange(Map<TMCType, List<EshopNotifyChange>> itemOrderChangeMap, EshopOrderEntity order) {
        for (TMCType type : itemOrderChangeMap.keySet()
        ) {
            EshopNotifyChange change = itemOrderChangeMap.get(type).get(0);
            switch (type) {
                case CHANGE_ADDRESS:
                    buildOrderAddressChange(change, order);
                    break;
                case FACILITATE_SEND:
                    buildOrderFacilitateSendChange(change, order);
                    break;
                case ChANG_INVOICE:
                    buildOrderInvoiceChange(change, order);
                    break;
                case INTERCEPT_ORDER:
                    buildOrderInterceptChange(order);
                    break;
                case YOUXIAN_SEND:
                    buildYouxianSendChange(order);
                    break;
                default:
            }
        }
    }

    private static void buildYouxianSendChange(EshopOrderEntity order) {
        order.getOrderMarks().add(BaseOrderMarkEnum.YOUXIAN_SEND);
    }

    private static void buildOrderInterceptChange(EshopOrderEntity order) {
        order.getOrderMarks().add(BaseOrderMarkEnum.TB_INTERCEPT_ORDER);
    }
    private static void buildOrderInvoiceChange(EshopNotifyChange change, EshopOrderEntity order) {
        InvoiceNotify invoice = JsonUtils.toObject(change.getContent(), InvoiceNotify.class);
        if (null == invoice) {
            logger.info(String.format("下载订单，接收tmc消息--发票修改报错，profileId%s，tradeOrderId%s", order.getProfileId(), order.getTradeId()));
            return;
        }
        OrderInvoice buyer = order.getOrderInvoice();
        buyer.setInvoiceType(invoice.getInvoice_attr() == 1 ? InvoiceType.PERSONAL : InvoiceType.ENTERPRISE);
        buyer.setInvoiceCategory(buildInvoiceCategory(invoice.getInvoice_kind(), invoice.getInvoice_type()));
        buyer.setInvoiceCompany(invoice.getCompany_title());
        buyer.setInvoiceCode(invoice.getTax_no());
        buyer.setInvoiceRegisterAddr(invoice.getExtend_arg().getRegistered_address());
        buyer.setInvoiceRegisterPhone(invoice.getExtend_arg().getRegistered_phone());
        buyer.setInvoiceBank(invoice.getExtend_arg().getBank());
        buyer.setInvoiceBankAccount(invoice.getExtend_arg().getBank_account());
    }
    private static InvoiceCategory buildInvoiceCategory(int invoiceKind, int invoiceType) {
        if (invoiceKind == 1 && invoiceType == 1) {
            return InvoiceCategory.ELECTRON_NORMAL;
        }
        if (invoiceKind == 2 && invoiceType == 1) {
            return InvoiceCategory.PAPER_NORMAL;
        }
        if (invoiceKind == 1 && invoiceType == 2) {
            return InvoiceCategory.ELECTRON_ADDED_TAX;
        }
        if (invoiceKind == 2 && invoiceType == 2) {
            return InvoiceCategory.PAPER_ADDED_TAX;
        }
        return InvoiceCategory.ELECTRON_NORMAL;
    }

    private static void buildOrderFacilitateSendChange(EshopNotifyChange change, EshopOrderEntity order) {
        ExpressNotify express = JsonUtils.toObject(change.getContent(), ExpressNotify.class);
        if (null == express) {
            logger.info(String.format("下载订单，接收tmc消息--催发货报错，profileId%s，tradeOrderId%s", order.getProfileId(), order.getTradeId()));
            return;
        }
        order.setFreightName(express.getCap());
        order.getOrderMarks().add(BaseOrderMarkEnum.TB_QUICK_SEND);
    }

    private static void buildOrderAddressChange(EshopNotifyChange change, EshopOrderEntity order) {
        Address address = JsonUtils.toObject(change.getContent(), AddressNotify.class).getModifiedAddress();
        if (null == address) {
            logger.info(String.format("下载订单，接收tmc消息--改地址报错，profileId%s，tradeOrderId%s", order.getProfileId(), order.getTradeId()));
            return;
        }
        ReceiverInfo buyer = order.getReceiverInfo();
        buyer.setCustomerProvince(address.getProvince());
        buyer.setCustomerCity(address.getCity());
        buyer.setCustomerDistrict(address.getArea());
        buyer.setCustomerTown(address.getTown());
        buyer.setCustomerAddress(address.getAddressDetail());
        buyer.setCustomerZipCode(address.getPostCode());
        buyer.setReceiver(address.getName());
        buyer.setCustomerMobile(address.getPhone());
    }
    public static List<EshopNotifyChange> queryEshopNotityChange(BigInteger profileId, List<String> tradeIds, BigInteger otypeId,EshopNotifyMapper mapper) {
        try {
            if (ngp.utils.CollectionUtils.isEmpty(tradeIds)) {
                return null;
            }
            List<List<String>> listList = ListUtil.split(tradeIds, 2000);
            List<EshopNotifyChange> result = new ArrayList<>();
            for (List<String> list : listList) {
                List<EshopNotifyChange> pageData = mapper.queryMessageChange(profileId, list, otypeId);
                if (ngp.utils.CollectionUtils.isNotEmpty(pageData)) {
                    result.addAll(pageData);
                }
            }
            return result;
        } catch (RuntimeException ex) {
            logger.error(String.format("查询原始订单变更信息报错：%s", ex.getMessage()), ex);
        }
        return null;
    }
}
