package com.wsgjp.ct.sale.platform.sdk.log;

import com.wsgjp.ct.support.log.annotation.Ignore;
import com.wsgjp.ct.support.log.annotation.LogEntity;
import com.wsgjp.ct.support.log.entity.BaseLog;
import com.wsgjp.ct.support.log.type.DBType;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 */
@LogEntity(tableName = "pl_eshop_sale_order_sync_task_log", dbType = DBType.LOG)
public class EshopSaleOrderSyncTaskLog extends BaseLog {
    private BigInteger eshopId;
    private String taskId = "";
    private Integer downloadType;
    private String responseId = "";
    private Integer responseTotal = 0;
    private Integer responseRefundTotal = 0;
    private Date downloadTime;
    @Ignore
    private BigInteger etypeId;

    public EshopSaleOrderSyncTaskLog() {
    }

    public EshopSaleOrderSyncTaskLog(BigI<PERSON>ger profileId, BigInteger eshopId, String taskId, Integer downloadType, Date downloadTime) {
        this.setProfileId(profileId);
        this.eshopId = eshopId;
        this.taskId = taskId;
        this.downloadType = downloadType;
        this.downloadTime = downloadTime;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getDownloadType() {
        return downloadType;
    }

    public void setDownloadType(Integer downloadType) {
        this.downloadType = downloadType;
    }

    public String getResponseId() {
        if (responseId == null) {
            responseId = "";
        }
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public Integer getResponseTotal() {
        return responseTotal;
    }

    public void setResponseTotal(Integer responseTotal) {
        this.responseTotal = responseTotal;
    }

    public Date getDownloadTime() {
        return downloadTime;
    }

    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }

    @Override
    public BigInteger getEtypeId() {
        return etypeId;
    }

    @Override
    public void setEtypeId(BigInteger etypeId) {
        this.etypeId = etypeId;
    }

    public Integer getResponseRefundTotal() {
        return responseRefundTotal;
    }

    public void setResponseRefundTotal(Integer responseRefundTotal) {
        this.responseRefundTotal = responseRefundTotal;
    }
}
