package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.common.enums.core.entity.BaseMarkBigDataEntity;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.order.entity.DeliveryExpressData;
import com.wsgjp.ct.sale.platform.dto.order.entity.DeliveryExpressInfo;
import com.wsgjp.ct.sale.platform.dto.order.entity.OrderInvoice;
import com.wsgjp.ct.sale.platform.entity.PrintShippingMarksResult;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.entities.PrintProductBarcodeResult;
import com.wsgjp.ct.sale.platform.entity.entities.SelfDeliveryInfo;
import com.wsgjp.ct.sale.platform.entity.request.download.DownloadFileRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.*;
import com.wsgjp.ct.sale.platform.entity.response.download.DownloadFileResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.*;
import com.wsgjp.ct.sale.platform.exception.BusinessSupportException;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.download.EshopDownloadFileFeature;
import com.wsgjp.ct.sale.platform.feature.order.*;
import com.wsgjp.ct.sale.platform.sdk.entity.Employee;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopOrderSimpleMarkEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderSimpleEntity;
import com.wsgjp.ct.sale.platform.sdk.mapper.EshopNotifyMapper;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEmployeeMapper;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopOrderMapper;
import com.wsgjp.ct.sale.platform.sdk.service.EshopOrderService;
import com.wsgjp.ct.sale.platform.utils.CodeConvertUtil;
import com.wsgjp.ct.sale.platform.utils.FreightMappingUtils;
import com.wsgjp.ct.sale.platform.utils.PlatformApiException;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EshopOrderServiceImpl implements EshopOrderService {
    private final Logger logger = LoggerFactory.getLogger(EshopOrderServiceImpl.class);
    private final EshopNotifyMapper notifyMapper;
    private final PlatformSdkEmployeeMapper employeeMapper;
    private final PlatformSdkEshopOrderMapper eshopOrderMapper;

    public EshopOrderServiceImpl(EshopNotifyMapper notifyMapper, PlatformSdkEmployeeMapper employeeMapper, PlatformSdkEshopOrderMapper eshopOrderMapper) {
        this.notifyMapper = notifyMapper;
        this.employeeMapper = employeeMapper;
        this.eshopOrderMapper = eshopOrderMapper;
    }

    @Override
    public QueryOrderBySelfFetchCodeResponse queryOrderBySelfFetchCode(QueryOrderBySelfFetchCodeRequest request) {
        QueryOrderBySelfFetchCodeResponse response = new QueryOrderBySelfFetchCodeResponse();
        EshopSystemParams systemParams = request.getSystemParams();
        response.setSelfFetchCode(request.getSelfFetchCode());
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), systemParams);
            EshopOrderQueryBySelfFetchCodeFeature feature = factory.getFeature(EshopOrderQueryBySelfFetchCodeFeature.class);
            if (feature == null) {
                response.setMessage("平台不支持根据核销码查询订单");
                response.setSuccess(false);
                return response;
            }
            return feature.queryOrderBySelfFetchCode(request);
        } catch (Exception ex) {
            logger.error("根据核销码查询订单信息失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            response.setMessage(ex.getMessage());
            response.setSuccess(false);
        }
        return response;
    }


    @Override
    public DeliveryExpressResponse getDeliveryExpress(DeliveryExpressRequset deliveryExpressRequset) {
        EshopFactory factory = EshopFactoryManager.create(deliveryExpressRequset.getShopType(), deliveryExpressRequset.getSystemParams());
        EshopOrderDeliveryExpressFeature deliveryExpressFeature = factory.getFeature(EshopOrderDeliveryExpressFeature.class);
        if (deliveryExpressFeature == null) {
            String msg = String.format("%s不支持获取物流探查信息！", deliveryExpressRequset.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }

//        if (deliveryExpressRequset.getShopType() == ShopType.TeMu){
//            PlatformSdkAddressMapper platformSdkAddressMapper = BeanUtils.getBean(PlatformSdkAddressMapper.class);
//            List<AddressInfo> addressList = platformSdkAddressMapper.getOnlineReturnAddressList(deliveryExpressRequset.getSystemParams().getProfileId(), deliveryExpressRequset.getSystemParams().geteShopId());
//            if (CollectionUtils.isEmpty(addressList)){
//                throw new RuntimeException("获取平台寄件人地址为空,请在订单审核退件地址操作后再试!");
//            }
//            String address2 = deliveryExpressRequset.getProviceName() + deliveryExpressRequset.getCityName() + deliveryExpressRequset.getDistrictName() + deliveryExpressRequset.getDeatilAddress();
//            for (AddressInfo addressInfo : addressList) {
//                String province = addressInfo.getProvince();
//                if (StringUtils.isNotEmpty(province)){
//                    province = addressInfo.getProvince().replace("市", "");
//                }
//                String address = province + addressInfo.getCity() + addressInfo.getDistrict() +  addressInfo.getAddress();
//                if (address.equals(address2)){
//                    deliveryExpressRequset.setDeliveryAddressId(addressInfo.getPlatformId());
//                }
//            }
//        }
        DeliveryExpressResponse deliveryExpressResponse = deliveryExpressFeature.getDeliveryExpress(deliveryExpressRequset);
        if (!deliveryExpressResponse.getSuccess()) {
            return deliveryExpressResponse;
        }
        if (!CollectionUtils.isEmpty(deliveryExpressResponse.getDeliveryExpressData()) && factory.getShopType() != ShopType.TeMu) {
            List<DeliveryExpressData> deliveryExpressData = deliveryExpressResponse.getDeliveryExpressData();
            deliveryExpressData.forEach(data -> data.getDeliveryExpressInfoList().forEach(deliveryExpressInfo -> doFreightMapping(deliveryExpressInfo, factory)));
        } else {
            List<DeliveryExpressData> deliveryExpressData = deliveryExpressResponse.getDeliveryExpressData();
            deliveryExpressData.forEach(data -> {
                if (data.isSuccess()) {
                    data.getDeliveryExpressInfoList().forEach(deliveryExpressInfo -> doFreightNameMapping(deliveryExpressInfo, factory));
                }
            });
        }
        return deliveryExpressResponse;
    }

    private void doFreightNameMapping(DeliveryExpressInfo deliveryExpressInfo, EshopFactory factory) {
        if (deliveryExpressInfo == null) {
            return;
        }
        ShopType shopType = factory.getShopType();
        String logisticsName = deliveryExpressInfo.getLogisticsName();
        FreightMapping freightMapping = FreightMappingUtils.getFreightMappingByOnlineName(shopType, logisticsName);
        if (freightMapping != null) {
            deliveryExpressInfo.setLogisticsCode(freightMapping.getLocalCode());
            deliveryExpressInfo.setLogisticsName(freightMapping.getLocalName());
        }
    }

    @Override
    public DeliveryExpressResponse getDeliveryExpressBlacklist(DeliveryExpressRequset deliveryExpressRequset) {
        EshopFactory factory = EshopFactoryManager.create(deliveryExpressRequset.getShopType(), deliveryExpressRequset.getSystemParams());
        EshopOrderDeliveryExpressFeature deliveryExpressFeature = factory.getFeature(EshopOrderDeliveryExpressFeature.class);
        if (deliveryExpressFeature == null) {
            String msg = String.format("%s不支持获取物流探查信息！", deliveryExpressRequset.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        DeliveryExpressResponse deliveryExpressResponse = deliveryExpressFeature.getDeliveryExpressBlacklist(deliveryExpressRequset);
        if (deliveryExpressResponse != null && !CollectionUtils.isEmpty(deliveryExpressResponse.getDeliveryExpressData())) {
            List<DeliveryExpressData> deliveryExpressData = deliveryExpressResponse.getDeliveryExpressData();
            deliveryExpressData.forEach(data -> data.getDeliveryExpressInfoList().forEach(deliveryExpressInfo -> doFreightMapping(deliveryExpressInfo, factory)));
        }
        return deliveryExpressResponse;
    }

    @Override
    public PrintOrderShippingMarksResponse batchPrintOrderShippingMarks(PrintOrderShippingMarksRequest request) {
        PrintOrderShippingMarksResponse response = new PrintOrderShippingMarksResponse();
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopOrderPrintShippingMarks feature = factory.getFeature(EshopOrderPrintShippingMarks.class);
            if (feature == null) {
                String msg = String.format("%s不支持打印订单箱唛！", request.getSystemParams().getShopType().getName());
                throw new BusinessSupportException(msg);
            }
            return feature.batchPrintOrderShippingMarks(request);
        } catch (Exception ex) {
            logger.error("打印订单箱唛失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            response.setResults(
                    request.getTradeIds().stream()
                            .map(tradeId -> new PrintShippingMarksResult(tradeId, ex.getMessage()))
                            .collect(Collectors.toList())
            );
        }
        return response;
    }

    @Override
    public PrintProductBarcodeResponse batchPrintOrderProductBarcode(PrintProductBarcodeRequest request) {
        PrintProductBarcodeResponse response = new PrintProductBarcodeResponse();
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopOrderPrintProductBarcode feature = factory.getFeature(EshopOrderPrintProductBarcode.class);
            if (feature == null) {
                String msg = String.format("%s不支持打印商品条码！", request.getSystemParams().getShopType().getName());
                throw new BusinessSupportException(msg);
            }
            return feature.batchPrintOrderProductBarcode(request);
        } catch (Exception ex) {
            logger.error("打印商品条码失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            response.setResults(
                    request.getPrintBarcodeParams().stream()
                            .map(param -> new PrintProductBarcodeResult(param, ex.getMessage()))
                            .collect(Collectors.toList())
            );
        }
        return response;
    }

    @Override
    public QueryExpressCompanyListResponse queryExpressCompanyList(QueryExpressCompanyListRequest request) {
        QueryExpressCompanyListResponse response = new QueryExpressCompanyListResponse();
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopQueryExpressCompanyListFeature feature = factory.getFeature(EshopQueryExpressCompanyListFeature.class);
            if (feature == null) {
                String msg = String.format("%s查询订单可以使用的物流公司列表！", request.getSystemParams().getShopType().getName());
                throw new BusinessSupportException(msg);
            }
            return feature.queryExpressCompanyList(request);
        } catch (Exception ex) {
            logger.error("查询订单可以使用的物流公司列表失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            response.setMessage(ex.getMessage());
            response.setSuccess(false);
        }
        return response;
    }

    /**
     * 创建发货单
     *
     * @param request 创建发货单实体
     * @return 是否成功
     */
    @Override

    public CreateShipOrderResponse createShipOrder(CreateShipOrderRequest request) {
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopCreateShipOrderFeature feature = factory.getFeature(EshopCreateShipOrderFeature.class);
            if (feature == null) {
                String msg = String.format("%s不支持创建发货单！", request.getSystemParams().getShopType().getName());
                throw new BusinessSupportException(msg);
            }
            ShopType shopType = factory.getShopType();
            FreightMapping mappingByLocalCode = FreightMappingUtils.getFreightMappingByLocalCode(shopType, request.getFreightInfo().getFreightCode());
            if (mappingByLocalCode != null) {
                request.getFreightInfo().setFreightCode(mappingByLocalCode.getOnlineCode());
                request.getFreightInfo().setFreightName(mappingByLocalCode.getOnlineName());
            }
            CreateShipOrderResponse response = feature.createShipOrder(request);
            //发货单创建报文存入待用
            if (response != null && response.getSuccess()) {
                EshopNotifyChange notifyChange = new EshopNotifyChange();
                notifyChange.setEshopId(request.getShopId());
                notifyChange.setId(UId.newId());
                notifyChange.setProfileId(CurrentUser.getProfileId());
                notifyChange.setContent(JsonUtils.toJson(response));
                notifyChange.setTradeOrderId(request.getTradeId());
                notifyChange.setType(TMCType.CREATE_SHIP_ORDER);
                notifyMapper.insertEshopNotifyChange(notifyChange);
            }
            return response;
        } catch (Exception ex) {
            logger.error("创建发货单失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            CreateShipOrderResponse response = new CreateShipOrderResponse();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
            return response;
        }
    }

    @Override
    public UpdateWeightResponse updateWeight(UpdateWeightRequest request) {
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            UpdateWeightFeature feature = factory.getFeature(UpdateWeightFeature.class);
            if (feature == null) {
                String msg = String.format("%s不支持修改称重重量！", request.getSystemParams().getShopType().getName());
                throw new BusinessSupportException(msg);
            }
            return feature.updateWeight(request);
        } catch (Exception ex) {
            logger.error("修改称重失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            UpdateWeightResponse response = new UpdateWeightResponse();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
            return response;
        }
    }

    @Override
    public UploadSnResponse uploadSnToOrders(UploadSnInfo request) {
        UploadSnResponse response = new UploadSnResponse();
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopUploadSnFeature feature = factory.getFeature(EshopUploadSnFeature.class);
            if (feature == null) {
                response.setMessage("平台不支持通过接口上传序列号");
                response.setSuccess(false);
                return response;
            }
            return feature.uploadSnToOrders(request);
        } catch (Exception ex) {
            logger.error("通过接口上传序列号失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            response.setMessage(ex.getMessage());
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public LocationSyncResponse locationSync(LocationSyncRequest request) {
        LocationSyncResponse response = new LocationSyncResponse();
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopOrderLocationSyncFeature feature = factory.getFeature(EshopOrderLocationSyncFeature.class);
            if (feature == null) {
                response.setMessage("平台不支持通过接口上传骑手轨迹");
                response.setSuccess(false);
                response.setCode(501);
                return response;
            }
            return feature.locationSync(request);
        } catch (Exception ex) {
            logger.error("通过接口上传骑手轨迹失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            response.setMessage(ex.getMessage());
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public SelfDeliveryStateSyncResponse selfDeliveryStateSync(SelfDeliveryStateSyncRequest request) {
        SelfDeliveryStateSyncResponse response = new SelfDeliveryStateSyncResponse();
        try {
            boolean featureSupported = EshopFactoryManager.isFeatureSupported(EshopOrderSelfDeliveryStateSyncFeature.class, request.getShopType());
            if (!featureSupported) {
                response.setMessage("平台不支持通过接口上传送骑手状态");
                response.setSuccess(false);
                response.setCode(501);
                return response;
            }
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopOrderSelfDeliveryStateSyncFeature feature = factory.getFeature(EshopOrderSelfDeliveryStateSyncFeature.class);
            if (feature == null) {
                response.setMessage("平台不支持通过接口上传送骑手状态");
                response.setSuccess(false);
                response.setCode(501);
                return response;
            }
            List<EshopSaleOrderSimpleEntity> orderInfos = eshopOrderMapper.listSaleOrders(CurrentUser.getProfileId(), request.getShopId(), Collections.singletonList(request.getSelfDeliveryInfo().getTradeId()));
            if (CollectionUtils.isNotEmpty(orderInfos) && orderInfos.get(0).getCreateType() == 0) {
                response.setMessage("手工开单不能上传骑手状态");
                response.setSuccess(false);
                response.setCode(501);
                return response;
            }
            DeliveryExpressInfo expressInfo = new DeliveryExpressInfo();
            SelfDeliveryInfo selfDeliveryInfo = request.getSelfDeliveryInfo();
            expressInfo.setLogisticsCode(selfDeliveryInfo.getDistributorTypeId());
            expressInfo.setLogisticsName(selfDeliveryInfo.getDistributorName());
            doFreightMapping(expressInfo, factory);
            selfDeliveryInfo.setDistributorTypeId(expressInfo.getLogisticsCode());
            selfDeliveryInfo.setDistributorName(expressInfo.getLogisticsName());
            Employee employee = employeeMapper.getEmployee(CurrentUser.getEmployeeId());
            List<EshopOrderSimpleMarkEntity> eshopOrderMarks = eshopOrderMapper.listEshopOrderMark(CurrentUser.getProfileId(), request.getShopId(), Collections.singletonList(selfDeliveryInfo.getTradeId()));
            if (CollectionUtils.isNotEmpty(eshopOrderMarks)) {
                selfDeliveryInfo.setMarkDataList(eshopOrderMarks.stream().filter(markEntity -> markEntity.getMarkCode() != null).map(markEntity -> {
                    MarkData markData = new MarkData();
                    markData.setOrderMarkEnum(BaseOrderMarkEnum.getMarkEnumByCode(markEntity.getMarkCode()));
                    BaseMarkBigDataEntity bigData = JsonUtils.toObject(markEntity.getBigData(), BaseMarkBigDataEntity.class);
                    markData.setBigData(bigData);
                    return markData;
                }).collect(Collectors.toList()));
            }
            if (employee != null) {
                selfDeliveryInfo.setOperatorName(employee.getFullname());
            }
            request.setSelfDeliveryInfo(selfDeliveryInfo);
            return feature.selfDeliveryStateSync(request);
        } catch (Exception ex) {
            logger.error("通过接口上传自配送骑手状态失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            response.setMessage(ex.getMessage());
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public OrderPickCompleteResponse orderPickComplete(OrderPickCompleteRequest request) {
        OrderPickCompleteResponse response = new OrderPickCompleteResponse();
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopOrderPickCompleteFeature feature = factory.getFeature(EshopOrderPickCompleteFeature.class);
            if (feature == null) {
                response.setMessage("平台不支持订单拣货完成回传");
                response.setSuccess(false);
                return response;
            }
            response = feature.orderPickComplete(request);
        } catch (PlatformApiException ex) {
            return CodeConvertUtil.convertToPlatCode(ex, response);
        }
        return response;
    }

    @Override
    public DownloadFileResponse downloadFile(DownloadFileRequest request) {
        DownloadFileResponse response = new DownloadFileResponse();
        try {
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopDownloadFileFeature feature = factory.getFeature(EshopDownloadFileFeature.class);
            if (feature == null) {
                response.setMessage("平台不支持下载文件!");
                response.setSuccess(false);
                response.setCode(500);
                return response;
            }
            return feature.downloadFile(request);
        } catch (Exception ex) {
            logger.error("下载文件失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            response.setMessage(ex.getMessage());
            response.setSuccess(false);
        }
        return response;
    }

    @Override
    public OrderInvoice getInvoice(GetInvoiceRequest invoiceRequest) {
        EshopFactory factory = EshopFactoryManager.create(invoiceRequest.getShopType(), invoiceRequest.getSystemParams());
        EshopGetInvoiceFeature feature = factory.getFeature(EshopGetInvoiceFeature.class);
        if (feature != null){
            return feature.getInvoice(invoiceRequest);
        }else {
            return new OrderInvoice();
        }
    }

    /**
     * 将返回的线上物流转为本地物流编码
     *
     * @param deliveryExpressInfo
     * @param factory
     */
    private void doFreightMapping(DeliveryExpressInfo deliveryExpressInfo, EshopFactory factory) {
        if (deliveryExpressInfo == null) {
            return;
        }
        ShopType shopType = factory.getShopType();
        String logisticsCode = deliveryExpressInfo.getLogisticsCode();
        FreightMapping freightMappingByOnlineCode = FreightMappingUtils.getFreightMappingByOnlineCode(shopType, logisticsCode);
        if (freightMappingByOnlineCode != null) {
            deliveryExpressInfo.setLogisticsCode(freightMappingByOnlineCode.getLocalCode());
            deliveryExpressInfo.setLogisticsName(freightMappingByOnlineCode.getLocalName());
        }
    }
}
