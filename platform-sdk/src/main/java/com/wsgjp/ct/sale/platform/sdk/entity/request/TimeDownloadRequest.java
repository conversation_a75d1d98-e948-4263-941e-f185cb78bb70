package com.wsgjp.ct.sale.platform.sdk.entity.request;

import com.wsgjp.ct.sale.common.enums.MonitorSourceEnum;
import com.wsgjp.ct.sale.platform.callback.DownloadCallback;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.callback.SliceDownloadCallbackResult;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class TimeDownloadRequest<T> extends BaseRequest {
    private DownloadCallback<SliceDownloadCallbackResult<T>> callback;
    private Date startTime;
    private Date endTime;

    private MonitorSourceEnum sourceEnum;
    /**
     * 参考枚举
     */
    private int downloadType;

    public DownloadCallback<SliceDownloadCallbackResult<T>> getCallback() {
        return callback;
    }

    public void setCallback(DownloadCallback<SliceDownloadCallbackResult<T>> callback) {
        this.callback = callback;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public MonitorSourceEnum getSourceEnum() {
        if (sourceEnum == null) {
            sourceEnum = MonitorSourceEnum.NONE;
        }
        return sourceEnum;
    }

    public void setSourceEnum(MonitorSourceEnum sourceEnum) {
        this.sourceEnum = sourceEnum;
    }

    public void setDownloadType(int downloadType) {
        this.downloadType = downloadType;
    }

    public int getDownloadType() {
        return downloadType;
    }
}
