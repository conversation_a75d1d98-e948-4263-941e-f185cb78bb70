package com.wsgjp.ct.sale.platform.sdk.entity.callback;

import com.wsgjp.ct.sale.platform.enums.RefundStatus;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2025/4/29 11:30
 */
public class RefundCheckResult {

    private boolean success = true;
    private int code = 0;
    private String message;
    private List<RefundCheckResultData> dataList;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<RefundCheckResultData> getDataList() {
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        return dataList;
    }

    public void setDataList(List<RefundCheckResultData> dataList) {
        this.dataList = dataList;
    }

    public static class RefundCheckResultData {
        private String tradeId;
        private String oid;
        private Boolean hasRefund = false;
        private RefundStatus refundStatus = RefundStatus.NONE;

        public String getTradeId() {
            return tradeId;
        }

        public void setTradeId(String tradeId) {
            this.tradeId = tradeId;
        }

        public String getOid() {
            return oid;
        }

        public void setOid(String oid) {
            this.oid = oid;
        }

        public Boolean getHasRefund() {
            return hasRefund;
        }

        public void setHasRefund(Boolean hasRefund) {
            this.hasRefund = hasRefund;
        }

        public RefundStatus getRefundStatus() {
            return refundStatus;
        }

        public void setRefundStatus(RefundStatus refundStatus) {
            this.refundStatus = refundStatus;
        }
    }
}
