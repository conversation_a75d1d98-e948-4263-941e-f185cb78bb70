package com.wsgjp.ct.sale.platform.sdk.entity.callback;

/**
 * <AUTHOR>
 */
public class CallbackResult {
    private boolean success;
    private String message;
    private Throwable exception;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Throwable getException() {
        return exception;
    }

    public void setException(Throwable exception) {
        this.exception = exception;
    }
}
