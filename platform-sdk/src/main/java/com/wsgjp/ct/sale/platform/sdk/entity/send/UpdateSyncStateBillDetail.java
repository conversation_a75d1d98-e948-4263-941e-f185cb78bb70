package com.wsgjp.ct.sale.platform.sdk.entity.send;


import com.wsgjp.ct.common.enums.core.enums.SyncFreightStatus;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class UpdateSyncStateBillDetail {
    private BigInteger detailId;
    private BigInteger taskId;
    private BigInteger vchcode;
    private BigInteger taskDetailId;
    private SyncFreightStatus syncState;
    private boolean combo;
    private String message;

    public BigInteger getDetailId() {
        return detailId;
    }

    public void setDetailId(BigInteger detailId) {
        this.detailId = detailId;
    }

    public BigInteger getTaskId() {
        return taskId;
    }

    public void setTaskId(BigInteger taskId) {
        this.taskId = taskId;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public BigInteger getTaskDetailId() {
        return taskDetailId;
    }

    public void setTaskDetailId(BigInteger taskDetailId) {
        this.taskDetailId = taskDetailId;
    }

    public boolean isCombo() {
        return combo;
    }

    public void setCombo(boolean combo) {
        this.combo = combo;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public SyncFreightStatus getSyncState() {
        return syncState;
    }

    public void setSyncState(SyncFreightStatus syncState) {
        this.syncState = syncState;
    }
}
