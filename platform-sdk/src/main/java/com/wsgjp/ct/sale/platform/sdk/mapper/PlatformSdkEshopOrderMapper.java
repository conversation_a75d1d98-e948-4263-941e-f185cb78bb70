package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.sdk.entity.eshop.*;
import com.wsgjp.ct.sale.platform.dto.order.entity.EshopOrderDetailFreight;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopAdvanceSaleOrderSimpleEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopOrderSimpleMarkEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderSimpleEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderDetailSimpleEntity;
import com.wsgjp.ct.sale.platform.entity.refundEntity.OrderDetailOnlineSkuInfo;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.*;
import com.wsgjp.ct.sale.platform.sdk.entity.send.UpdateSaleOrderSendStatusParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PlatformSdkEshopOrderMapper {
    /**
     * 根据订单编号读取简单原始订单信息（主表）
     *
     * @param profileId 账套id
     * @param tradeIds  订单编号列表
     * @param eshopId   网店id
     * @return 简单原始订单列表
     */
    List<EshopSaleOrderSimpleEntity> listSaleOrders(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("tradeIds") List<String> tradeIds);


    List<EshopSaleOrderDetailSimpleEntity> listSaleOrderDetails(@Param("profileId") BigInteger profileId, @Param("saleOrderIds") List<BigInteger> saleOrderIds);

    /**
     * 查询订单标记信息
     *
     * @param profileId 账套id
     * @param tradeIds  订单编号列表
     * @return 订单标记信息
     */
    List<EshopOrderSimpleMarkEntity> listEshopOrderMark(@Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId, @Param("tradeIds") List<String> tradeIds);

    /**
     * 查询售后单标记信息
     */
    List<EshopRefundSimpleMarkEntity> listEshopRefundMark(@Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId, @Param("refundIds") List<String> refundIds);

    /**
     * 更新原单的发货状态和物流信息
     *
     * @param list 订单列表
     */
    void doUpdateSaleOrderSendStatus(@Param("list") List<UpdateSaleOrderSendStatusParam> list);


    /**
     * 根据订单编号读取简单预售订单信息（主表）
     *
     * @param profileId
     * @param tradeIds
     * @return
     */
    List<EshopAdvanceSaleOrderSimpleEntity> listAdvanceOrderVchcodes(@Param("profileId") BigInteger profileId, @Param("tradeIds") List<String> tradeIds);

    /**
     * 修改订单的交易状态
     *
     * @param profileId 账套ID
     * @param otypeId   网店id
     * @param tradeId   订单号
     * @param state     state
     */
    void modifyOrderStatus(@Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId, @Param("tradeId") String tradeId, @Param("state") int state);

    /**
     * 修改订单明细的交易状态
     *
     * @param profileId 账套ID
     * @param otypeId   网店id
     * @param orderId   单据ID
     * @param oid       明细id
     */
    void modifyDetailStatus(@Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId, @Param("orderId") BigInteger orderId, @Param("oid") String oid);

    /**
     * 未发货的明细数量
     *
     * @param orderSimpleEntity 订单
     * @return 数量
     */
    int queryOrderNotSendDetailCount(EshopSaleOrderSimpleEntity orderSimpleEntity);

    /**
     * 查询预售订单明细
     * @param profileId 账套id
     * @param eshopId 网店id
     * @param tradeIds 订单号
     * @return 订单明细
     */
    List<EshopSaleOrderDetailSimpleEntity> listAdvanceOrderDetails(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("tradeIds") List<String> tradeIds);

    List<OrderDetailOnlineSkuInfo> queryDetailSkuInfo(@Param("profileId")BigInteger profileId, @Param("eshopId")BigInteger eshopId, @Param("tradeOrderIds") List<String> tradeOrderIds);

    List<EshopOrderDetailFreight> querySaleOrderDetailFreightList(@Param("profileId")BigInteger profileId, @Param("otypeId")BigInteger otypeId, @Param("tradeOrderIds") List<String> tradeOrderIds);
}
