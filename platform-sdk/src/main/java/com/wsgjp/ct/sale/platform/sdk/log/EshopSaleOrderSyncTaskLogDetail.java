package com.wsgjp.ct.sale.platform.sdk.log;

import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.support.log.annotation.Ignore;
import com.wsgjp.ct.support.log.annotation.LogEntity;
import com.wsgjp.ct.support.log.entity.BaseLog;
import com.wsgjp.ct.support.log.type.DBType;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@LogEntity(tableName = "pl_eshop_sale_order_sync_task_log_detail", dbType = DBType.LOG)
public class EshopSaleOrderSyncTaskLogDetail extends BaseLog {
    private BigInteger parentId;
    private String tradeOrderId;
    private String refundId;
    /**
     * 明细类型，0：订单，1：售后
     */
    private Integer detailType = 0;
    private TradeStatus platformTradeState;
    private RefundStatus platformRefundState;
    @Ignore
    private BigInteger etypeId;

    public BigInteger getParentId() {
        return parentId;
    }

    public void setParentId(BigInteger parentId) {
        this.parentId = parentId;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public TradeStatus getPlatformTradeState() {
        return platformTradeState;
    }

    public void setPlatformTradeState(TradeStatus platformTradeState) {
        this.platformTradeState = platformTradeState;
    }

    public RefundStatus getPlatformRefundState() {
        return platformRefundState;
    }

    public void setPlatformRefundState(RefundStatus platformRefundState) {
        this.platformRefundState = platformRefundState;
    }

    @Override
    public BigInteger getEtypeId() {
        return etypeId;
    }

    @Override
    public void setEtypeId(BigInteger etypeId) {
        this.etypeId = etypeId;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public Integer getDetailType() {
        return detailType;
    }

    public void setDetailType(Integer detailType) {
        this.detailType = detailType;
    }
}
