package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.platform.entity.request.auth.TmcRegisterRequest;
import com.wsgjp.ct.sale.platform.entity.request.baseinfo.EshopBaseInfoRequest;
import com.wsgjp.ct.sale.platform.entity.request.baseinfo.QueryPlatformShopInfoRequest;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.baseinfo.EshopBaseInfoResponse;
import com.wsgjp.ct.sale.platform.entity.response.baseinfo.QueryPlatformShopInfoResponse;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.CheckTmcRegisterResponse;

/**
 * 获取店铺基础信息
 *
 * <AUTHOR>
 */
public interface EshopBaseInfoService {
    /**
     * 查询店铺基础信息
     */
    EshopBaseInfoResponse queryEshopBaseInfo(EshopBaseInfoRequest request);

    /**
     * 查询平台店铺信息
     */
    QueryPlatformShopInfoResponse queryPlatformShopInfo(QueryPlatformShopInfoRequest request);

    /**
     * 注册tmc
     * @param request 请求参数
     * @return 注册tmc结果
     */
    CheckTmcRegisterResponse checkAndRegisterTmc(TmcRegisterRequest request);

    BaseResponse checkAuthBaseInfo(EshopBaseInfoRequest request);


}
