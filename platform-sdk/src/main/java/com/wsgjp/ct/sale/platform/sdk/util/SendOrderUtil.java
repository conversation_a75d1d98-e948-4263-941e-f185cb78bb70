package com.wsgjp.ct.sale.platform.sdk.util;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.common.enums.core.enums.SyncFreightStatus;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.platform.config.PlatformEshopConfig;
import com.wsgjp.ct.sale.platform.dto.sendgoods.FreightBillNoSyncEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecord;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.SyncFreightInfo;
import com.wsgjp.ct.sale.platform.entity.response.sendgoods.SyncFreightBillNoResultDetail;
import com.wsgjp.ct.sale.platform.enums.SyncType;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderDetailSimpleEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.request.VirtualSendParam;
import com.wsgjp.ct.sale.platform.sdk.entity.request.VirtualSendRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.send.ConfirmDeliveryEndBillNoRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.send.SyncFreightBillNoDetail;
import com.wsgjp.ct.sale.platform.sdk.entity.send.SyncFreightBillNoRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.send.VirtualSendResponse;
import com.wsgjp.ct.sale.platform.utils.FreightMappingUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2023/10/9 15:28
 */
public class SendOrderUtil {

    private static SendOrderUtil instance;


    public static SendOrderUtil getInstance() {
        if (instance == null) {
            instance = new SendOrderUtil();
        }
        return instance;
    }

    private static final Logger logger = LoggerFactory.getLogger(PlatformCommonUtil.class);

    public static List<String> getAllTradeIds(List<SyncFreightBillNoRequest> sendList) {
        if (CollectionUtils.isEmpty(sendList)) {
            return new ArrayList<>();
        }
        Set<String> tradeIds = new HashSet<>();
        for (SyncFreightBillNoRequest request : sendList) {
            List<SyncFreightBillNoDetail> details = request.getDetails();
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            for (SyncFreightBillNoDetail detail : details) {
                tradeIds.add(detail.getTradeId());
            }
        }
        return tradeIds.stream().distinct().collect(Collectors.toList());
    }

    public static List<String> getTradeIds(List<ConfirmDeliveryEndBillNoRequest> sendList) {
        if (CollectionUtils.isEmpty(sendList)) {
            return new ArrayList<>();
        }
        Set<String> tradeIds = new HashSet<>();
        for (ConfirmDeliveryEndBillNoRequest request : sendList) {
            List<SyncFreightBillNoDetail> details = request.getDetails();
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            for (SyncFreightBillNoDetail detail : details) {
                tradeIds.add(detail.getTradeId());
            }
        }
        return new ArrayList<>(tradeIds);
    }


    public void doMatchFreightMapping(SyncFreightInfo freightInfo, PlatformEshopConfig platformEshopConfig, EshopFactory factory) {
        if (freightInfo == null) {
            return;
        }
        ShopType shopType = factory.getShopType();
        FreightMapping mappingByLocalCode = FreightMappingUtils.getFreightMappingByLocalCode(shopType, freightInfo.getFreightCode());
        if (mappingByLocalCode != null) {
            freightInfo.setFreightCode(mappingByLocalCode.getOnlineCode());
            freightInfo.setFreightName(mappingByLocalCode.getOnlineName());
        }
    }

    public static List<FreightBillNoSyncEntity> buildVirtualSendParam(VirtualSendRequest request){
        List<VirtualSendParam> params = request.getParams();
        if(CollectionUtils.isEmpty(params)){
            throw new RuntimeException("虚拟发货参数不能为空");
        }
        List<FreightBillNoSyncEntity> list=new ArrayList<>();
        for (VirtualSendParam param : params){
            FreightBillNoSyncEntity entity=new FreightBillNoSyncEntity();
            entity.setTradeId(param.getTradeId());
            entity.setPlatformJson(param.getFeature());
            list.add(entity);
        }
        return list;
    }

    public static List<VirtualSendResponse> buildVirtualSendResponse(List<SyncFreightBillNoResultDetail> results){
        if(CollectionUtils.isEmpty(results)){
            return new ArrayList<>();
        }
        List<VirtualSendResponse> responseList=new ArrayList<>();
        for (SyncFreightBillNoResultDetail result : results){
            VirtualSendResponse response=new VirtualSendResponse();
            response.setTradeId(response.getTradeId());
            response.setSuccess(result.getStatus()== SyncFreightStatus.SUCCESS);
            response.setMsg(result.getMessage());
            responseList.add(response);
        }
        return responseList;
    }

    public static List<ShopType> getNeedChangeTradeStatusShopTypes(){
        List<ShopType> list=new ArrayList<>();
        list.add(ShopType.Hipac);
        list.add(ShopType.TmallInter);
        list.add(ShopType.TmallSuperMarket);
        list.add(ShopType.KaoLaZhiYing);
        list.add(ShopType.YaoShiBang);
        list.add(ShopType.YaoJiuJiu);
        return list;
        }
    public static List<EshopFreightSyncRecord> initVirtualSendSyncRecord(List<SyncFreightBillNoResultDetail> results){
        if(CollectionUtils.isEmpty(results)){
            return new ArrayList<>();
        }
        List<EshopFreightSyncRecord> recordList=new ArrayList<>();
        for (SyncFreightBillNoResultDetail result : results){
            EshopFreightSyncRecord record=new EshopFreightSyncRecord();
            record.setId(UId.newId());
            record.setCallStatus(true);
            record.setDeliverOrderId(BigInteger.ZERO);
            record.setSyncCount(0);
            record.setFreightBillNo("");
            record.setFreightCode("虚拟发货");
            record.setLastOne(true);
            record.setSaleOrderId(BigInteger.ZERO);
            record.setProfileId(CurrentUser.getProfileId());
            record.setSyncStatus(result.getStatus());
            record.setTradeId(result.getTradeId());
            record.setSyncTime(new Date());
            record.setWarehouseTaskId(BigInteger.ZERO);
            record.setSyncType(SyncType.VIRTUAL_SEND);
            record.setSyncMessage(result.getMessage());
            recordList.add(record);
        }
        return recordList;
    }


    public static List<SyncFreightBillNoResultDetail> buildNoNeedSendResult(SyncFreightBillNoRequest request){
        List<SyncFreightBillNoResultDetail> list =new ArrayList<>();
        List<SyncFreightBillNoDetail> details = request.getDetails();
        for (SyncFreightBillNoDetail detail : details){
            SyncFreightBillNoResultDetail result=new SyncFreightBillNoResultDetail();
            org.springframework.beans.BeanUtils.copyProperties(detail, result);
            result.setStatus(SyncFreightStatus.NOT_NEED);
            result.setTradeId(request.getTradeId());
            result.setDeliverDetailId(detail.getDeliverDetailId());
            result.setWarehouseTaskDetailId(detail.getWarehouseTaskDetailId());
            result.setDeliverOrderId(request.getDeliverOrderId());
            result.setWarehouseTaskId(request.getWarehouseTaskId());
            result.setMessage("计算之后发现没有需要同步的订单明细，请检查一下该订单已发明细的数量是否已经大于原单数量！");
            list.add(result);
        }
        return list;
    }

    public static boolean checkSendSkuIsSame(SyncFreightBillNoDetail sendDetail, EshopSaleOrderDetailSimpleEntity localDetail){
        if(!localDetail.getPlatformSkuId().equals(sendDetail.getOnlineSkuId())){
            return false;
        }

        if(StringUtils.isEmpty(sendDetail.getOnlinePtypeProperties())){
            return true;
        }
        String[] split = sendDetail.getOnlinePtypeProperties().split("_");
        Arrays.sort(split);
        String online = Arrays.toString(split);
        if(StringUtils.isEmpty(localDetail.getPlatformPropertiesName())){
            return false;
        }
        String[] localSplit = localDetail.getPlatformPropertiesName().split("_");
        Arrays.sort(localSplit);
        String local = Arrays.toString(localSplit);
        return online.equals(local);
    }
}
