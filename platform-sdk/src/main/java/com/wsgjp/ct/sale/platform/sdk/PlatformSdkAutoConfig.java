package com.wsgjp.ct.sale.platform.sdk;

import com.wsgjp.ct.sale.platform.sdk.mapper.*;
import com.wsgjp.ct.sale.platform.sdk.mocker.ApiMockerServiceImpl;
import com.wsgjp.ct.sale.platform.sdk.service.impl.AuthServiceSdkImpl;
import com.wsgjp.ct.sale.platform.sdk.service.impl.EshopNotifySdkChangeAccessorImpl;
import com.wsgjp.ct.sale.platform.sdk.service.impl.EshopPlatformSdkImpl;
import com.wsgjp.ct.sale.platform.support.AuthService;
import com.wsgjp.ct.sale.platform.support.EshopNotifyChangeAccessor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan(basePackageClasses = PlatformSdkAutoConfig.class)
@MapperScan("com.wsgjp.ct.sale.platform.sdk.mapper")
public class PlatformSdkAutoConfig {

    @Bean
    @ConditionalOnMissingBean
    public EshopNotifyChangeAccessor getEshopNotifyChangeAccessor(EshopNotifyMapper mapper) {
        return new EshopNotifySdkChangeAccessorImpl(mapper);
    }

    @Bean
    @ConditionalOnMissingBean
    public AuthService getAuthService(PlatformSdkEshopMapper eshopMapper) {
        return new AuthServiceSdkImpl(eshopMapper);
    }

    @Bean
    @ConditionalOnMissingBean
    public EshopPlatformSdkImpl getEshopPlatformSdk(PlatformSdkEshopProductMapper platformSdkEshopProductMapper, PlatformSdkEshopOrderMapper platformSdkEshopOrderMapper) {
        return new EshopPlatformSdkImpl(platformSdkEshopProductMapper, platformSdkEshopOrderMapper);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApiMockerServiceImpl getApiMockerService(MockMapper mapper) {
        return new ApiMockerServiceImpl(mapper);
    }
}
