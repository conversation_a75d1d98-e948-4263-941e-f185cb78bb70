package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecordDetail;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.BatchInfo;
import com.wsgjp.ct.sale.platform.sdk.entity.send.FreightBillNoSyncRecordUpdateInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PlatformSdkEshopFreightSyncRecordDetailMapper {

    List<EshopFreightSyncRecordDetail> list(@Param("profileId") BigInteger profileId, @Param("recordIds") List<BigInteger> recordIds);

    void add(@Param("details") List<EshopFreightSyncRecordDetail> details);

    List<EshopFreightSyncRecordDetail> listByTradeIds(@Param("profileId") BigInteger profileId, @Param("tradeIds") List<String> tradeIds);


    void update(@Param("profileId") BigInteger profileId, @Param("updateInfos") List<FreightBillNoSyncRecordUpdateInfo> updateInfos);

    void addBatch(@Param("batchInfoList") List<BatchInfo> batchInfoList);

    List<BatchInfo> listByRecordDetailsId(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);
}
