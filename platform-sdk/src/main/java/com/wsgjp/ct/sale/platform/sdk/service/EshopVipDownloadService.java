package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.platform.entity.request.vip.DownloadVipCustomerByIdRequest;
import com.wsgjp.ct.sale.platform.entity.response.vip.DownloadVipCustomersResponse;
import com.wsgjp.ct.sale.platform.sdk.entity.request.VipCustomerDownloadRequest;

/**
 * 会员信息下载
 * <AUTHOR>
 */
public interface EshopVipDownloadService {
    /**
     * 根据创建时间下载会员用户列表
     *
     * @param request
     * @return
     */
    void downloadVipCustomersByCreateTime(VipCustomerDownloadRequest request);
    /**
     * 根据修改时间下载会员用户列表
     *
     * @param request
     * @return
     */
    void downloadVipCustomersByModifyTime(VipCustomerDownloadRequest request);

    /**
     * 根据会员ID下载会员用户信息
     */
    DownloadVipCustomersResponse downloadVipCustomersByCustomerIds(DownloadVipCustomerByIdRequest request);
}
