package com.wsgjp.ct.sale.platform.sdk.entity.send;

import com.wsgjp.ct.bill.core.handle.entity.BillSerialno;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillCreateType;
import com.wsgjp.ct.sale.platform.dto.sendgoods.FreightBillNoSyncPlatformCodeEntity;
import com.wsgjp.ct.sale.platform.entity.entities.BillSerialnoSon;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.BatchInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("发货明细")
public class SyncFreightBillNoDetail {
    @ApiModelProperty(value = "交易单主键", required = true)
    private BigInteger deliverOrderId;
    @ApiModelProperty(value = "交易单明细Id", required = true)
    private BigInteger deliverDetailId;
    @ApiModelProperty("任务单Id")
    private BigInteger warehouseTaskId;
    @ApiModelProperty("任务单明细id")
    private BigInteger warehouseTaskDetailId;
    @ApiModelProperty(value = "订单Id", required = true)
    private String tradeId;
    @ApiModelProperty(value = "线上售后单号")
    private String refundId;
    @ApiModelProperty(value = "是否赠品", required = true)
    private boolean gift;
    @ApiModelProperty(value = "发货数量", required = true)
    private BigDecimal qty;

    @ApiModelProperty(value = "基本单位数量")
    private BigDecimal unitQuantity;

    /**
     * 单位换算率
     */
    @ApiModelProperty(value = "单位换算率")
    private BigDecimal unitRate;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "子订单id", required = true)
    private String oid;
    @ApiModelProperty(value = "商家编码")
    private String xcode;
    @ApiModelProperty(value = "批次号")
    private List<BatchInfo> batchInfos;
    @ApiModelProperty(value = "平台商品id", required = true)
    private String onlinePtypeId;
    @ApiModelProperty(value = "平台商品skuId", required = true)
    private String onlineSkuId;
    @ApiModelProperty(value = "平台商品名称", required = true)
    private String onlinePtypeName;

    @ApiModelProperty(value = "平台商品属性名称", required = true)
    private String onlinePtypeProperties;

    /**
     * 69码也就是条码
     */
    private List<String> fullBarcode;

    public List<String> getFullBarcode() {
        return fullBarcode;
    }

    public void setFullBarcode(List<String> fullBarcode) {
        this.fullBarcode = fullBarcode;
    }

    public BigDecimal getUnitQuantity() {
        return unitQuantity;
    }

    public void setUnitQuantity(BigDecimal unitQuantity) {
        this.unitQuantity = unitQuantity;
    }

    public BigDecimal getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(BigDecimal unitRate) {
        this.unitRate = unitRate;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public List<BatchInfo> getBatchInfos() {
        return batchInfos;
    }

    public void setBatchInfos(List<BatchInfo> batchInfos) {
        this.batchInfos = batchInfos;
    }

    private List<BillSerialno> serialNoList;

    /**
     * 业务组/仓储组传递的原始序列号等编码实体
     */
    @ApiModelProperty("务组/仓储组传递的原始序列号等编码实体")
    private List<BillSerialnoSon> serialNoSonList;

    public List<BillSerialnoSon> getSerialNoSonList() {
        return serialNoSonList;
    }

    public void setSerialNoSonList(List<BillSerialnoSon> serialNoSonList) {
        this.serialNoSonList = serialNoSonList;
    }

    /**
     * 平台编码实体列表(包含序列号、imei码、IccId码、拆封码等)。由serialNoList在通用层转换而来。供平台接口组使用
     */
    private List<FreightBillNoSyncPlatformCodeEntity> platformCodeList;
    @ApiModelProperty("序列号")
    private List<String> snList;
    @ApiModelProperty(value = "是否套餐", required = true)
    private boolean combo;
    @ApiModelProperty(value = "套餐行ID")
    private BigInteger comboRowId;
    private BigInteger comboRowParId;
    @ApiModelProperty(value = "是否删除", required = true)
    private boolean deleted;
    @ApiModelProperty(value = "订单创建方式,直接取发货单的", required = true)
    private BillCreateType createType;

    public BigInteger getComboRowId() {
        return comboRowId;
    }

    public void setComboRowId(BigInteger comboRowId) {
        this.comboRowId = comboRowId;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public BigInteger getDeliverOrderId() {
        return deliverOrderId;
    }

    public void setDeliverOrderId(BigInteger deliverOrderId) {
        this.deliverOrderId = deliverOrderId;
    }

    public BigInteger getDeliverDetailId() {
        return deliverDetailId;
    }

    public void setDeliverDetailId(BigInteger deliverDetailId) {
        this.deliverDetailId = deliverDetailId;
    }

    public BigInteger getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(BigInteger warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }

    public BigInteger getWarehouseTaskDetailId() {
        return warehouseTaskDetailId;
    }

    public void setWarehouseTaskDetailId(BigInteger warehouseTaskDetailId) {
        this.warehouseTaskDetailId = warehouseTaskDetailId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public boolean isGift() {
        return gift;
    }

    public void setGift(boolean gift) {
        this.gift = gift;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public boolean isCombo() {
        return combo;
    }

    public void setCombo(boolean combo) {
        this.combo = combo;
    }

    public BigInteger getComboRowParId() {
        return comboRowParId;
    }

    public void setComboRowParId(BigInteger comboRowParId) {
        this.comboRowParId = comboRowParId;
    }

    public BillCreateType getCreateType() {
        return createType;
    }

    public void setCreateType(BillCreateType createType) {
        this.createType = createType;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public List<FreightBillNoSyncPlatformCodeEntity> getPlatformCodeList() {
        return platformCodeList;
    }

    public void setPlatformCodeList(List<FreightBillNoSyncPlatformCodeEntity> platformCodeList) {
        this.platformCodeList = platformCodeList;
    }

    public List<String> getSnList() {
        return snList;
    }

    public void setSnList(List<String> snList) {
        this.snList = snList;
    }

    public List<BillSerialno> getSerialNoList() {
        return serialNoList;
    }

    public void setSerialNoList(List<BillSerialno> serialNoList) {
        this.serialNoList = serialNoList;
    }

    public String getOnlinePtypeId() {
        return onlinePtypeId;
    }

    public void setOnlinePtypeId(String onlinePtypeId) {
        this.onlinePtypeId = onlinePtypeId;
    }

    public String getOnlineSkuId() {
        return onlineSkuId;
    }

    public void setOnlineSkuId(String onlineSkuId) {
        this.onlineSkuId = onlineSkuId;
    }

    public String getOnlinePtypeName() {
        return onlinePtypeName;
    }

    public void setOnlinePtypeName(String onlinePtypeName) {
        this.onlinePtypeName = onlinePtypeName;
    }

    public String getOnlinePtypeProperties() {
        return onlinePtypeProperties;
    }

    public void setOnlinePtypeProperties(String onlinePtypeProperties) {
        this.onlinePtypeProperties = onlinePtypeProperties;
    }
}
