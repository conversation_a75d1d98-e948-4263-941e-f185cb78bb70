package com.wsgjp.ct.sale.platform.sdk.callback;

import com.wsgjp.ct.sale.platform.entity.response.sendgoods.SyncFreightBillNoResponse;
import com.wsgjp.ct.sale.platform.sdk.entity.send.UpdateSyncStateBill;

import java.util.List;

/**
 * 发货需回调接口，同步发货状态
 */
public interface SendCallback {
    /**
     * 更新物流单同步状态
     *
     * @param results     原始结果
     * @param taskResults 任务维度的发货结果
     */
    void update(List<SyncFreightBillNoResponse> results, List<UpdateSyncStateBill> taskResults);
}