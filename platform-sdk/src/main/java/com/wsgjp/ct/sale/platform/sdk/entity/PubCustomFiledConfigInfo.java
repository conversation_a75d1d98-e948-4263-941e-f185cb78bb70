package com.wsgjp.ct.sale.platform.sdk.entity;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class PubCustomFiledConfigInfo {
    private BigInteger profileId;
    private BigInteger subType;
    private String displayName;
    private String dataField;
    private String fieldId;
    private Integer businessType;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getSubType() {
        return subType;
    }

    public void setSubType(BigInteger subType) {
        this.subType = subType;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDataField() {
        return dataField;
    }

    public void setDataField(String dataField) {
        this.dataField = dataField;
    }

    public String getFieldId() {
        return fieldId;
    }

    public void setFieldId(String fieldId) {
        this.fieldId = fieldId;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }
}
