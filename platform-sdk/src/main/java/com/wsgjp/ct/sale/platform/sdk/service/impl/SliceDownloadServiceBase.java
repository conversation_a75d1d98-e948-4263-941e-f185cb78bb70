package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.MonitorSourceEnum;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.bifrost.entity.BifrostApiMonitorTypeEnum;
import com.wsgjp.ct.sale.platform.RetryableExecutor;
import com.wsgjp.ct.sale.platform.callback.DownloadCallback;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopOrderSyncTaskLog;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.enums.OrderDownloadResultEnum;
import com.wsgjp.ct.sale.platform.enums.OrderSyncStateEnum;
import com.wsgjp.ct.sale.platform.enums.OrderSyncTypeEnum;
import com.wsgjp.ct.sale.platform.enums.SliceType;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.plugin.EshopPluginFeature;
import com.wsgjp.ct.sale.platform.sdk.entity.callback.SliceDownloadCallbackResult;
import com.wsgjp.ct.sale.platform.sdk.entity.request.TimeDownloadRequest;
import com.wsgjp.ct.sale.platform.sdk.slice.SliceBuilder;
import com.wsgjp.ct.sale.platform.sdk.slice.SliceBundle;
import com.wsgjp.ct.sale.platform.sdk.slice.SliceDownloadContext;
import com.wsgjp.ct.sale.platform.sdk.slice.SliceDownloader;
import com.wsgjp.ct.sale.platform.slice.DownloadSlice;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SliceDownloadServiceBase<T> {

    private final Logger logger = LoggerFactory.getLogger(SliceDownloadServiceBase.class);

    protected EshopFactory getFactory(BaseRequest request) {
        EshopSystemParams params = request.getSystemParams();
        return EshopFactoryManager.create(params.getShopType(), params);
    }

    protected void download(TimeDownloadRequest<T> request, SliceDownloadContext<T> context) {
        try {
            EshopFactory factory = context.getFactory();
            PlatformBaseConfig config = factory.getConfig();
            SliceDownloader<DownloadSlice, SliceDownloadResponse<T>> retryDownloader = slice ->
                    RetryableExecutor.execute(() -> context.getSliceDownloader().download(slice),
                            config.getMaxRetryCount(),
                            config.getRetryInterval());
            SliceBuilder<T> builder = new SliceBuilder<>(request,
                    factory,
                    retryDownloader,
                    context.getTimeExtractor(),
                    context.getSliceParamsList(),
                    context.isForceByApi()
            );
            SliceBundle<T> sliceBundle = builder.buildSliceBundle(config.isMultiThreadedDownloadOrderEnable());
            List<DownloadSlice> pageSliceList = sliceBundle.getSliceList().stream().filter(slice -> slice.getType() == SliceType.PAGE).collect(Collectors.toList());
            List<DownloadSlice> cursorSliceList = sliceBundle.getSliceList().stream().filter(slice -> slice.getType() == SliceType.CURSOR).collect(Collectors.toList());
            ThreadPool threadPool = ThreadPoolFactory.build(context.getThreadName());
            for (SliceDownloadResponse<T> response : sliceBundle.getResponses()) {
                try {
                    dealResponse(context, request, sliceBundle, response, null, false);
                } catch (Throwable e) {
                    dealResponse(context, request, sliceBundle, null, e, false);
                }
            }
            //下载已经精确到页数的切片
            int count = context.getSingleThreadCount();
            List<List<DownloadSlice>> split = CollectionUtils.split(pageSliceList, count);
            if (config.isMultiThreadedDownloadOrderEnable()) {
                threadPool.executeAsyncTaskList(partSlice -> {
                    DownLoadsliceResponse((TimeDownloadRequest<T>) request, (SliceDownloadContext<T>) context, (SliceDownloader<DownloadSlice, SliceDownloadResponse<T>>) retryDownloader, (SliceBundle<T>) sliceBundle, partSlice);
                }, split);
            }else {
                for (List<DownloadSlice> partSlice : split) {
                    DownLoadsliceResponse((TimeDownloadRequest<T>) request, (SliceDownloadContext<T>) context, (SliceDownloader<DownloadSlice, SliceDownloadResponse<T>>) retryDownloader, (SliceBundle<T>) sliceBundle, partSlice);
                }
            }

            //下载游标类的切片
            List<List<DownloadSlice>> cursorSplitList = CollectionUtils.split(cursorSliceList, count);
            threadPool.executeAsyncTaskList(partSlice -> {
                for (DownloadSlice slice : partSlice) {
                    SliceDownloadResponse<T> response = null;
                    do {
                        try {
                            response = retryDownloader.download(slice);
                            if (response == null) {
                                throw new RuntimeException("无效的响应");
                            }
                            slice.setCursor(response.getCursor());
                            slice.setPage(slice.getPage() + 1);
                            dealResponse(context, request, sliceBundle, response, null, true);
                        } catch (Throwable e) {
                            logger.error("切片：" + JsonUtils.toJson(slice) + "下载失败,异常信息", e);
                            dealResponse(context, request, sliceBundle, null, e, true);
                            break;
                        }

                    } while (StringUtils.isNotEmpty(slice.getCursor()));
                }
            }, cursorSplitList);
            try {
                if (config.isMultiThreadedDownloadOrderEnable()) {
                    sliceBundle.await();
                }
            } catch (InterruptedException e) {
                logger.error("订单下载线程等待终止:" + e.getMessage(), e);
            }
        } catch (Exception ex) {
            //处理子线程中抛出的异常message带有消息异常类名透彻到界面问题。
            String errMsg = ex.getMessage();
            if (StringUtils.isNotBlank(errMsg)) {
                errMsg = ex.getMessage().replace("java.util.concurrent.ExecutionException:", "")
                        .replace("java.lang.RuntimeException: ", "");
            }
            throw new RuntimeException(errMsg, ex);
        }
    }

    private void DownLoadsliceResponse(TimeDownloadRequest<T> request, SliceDownloadContext<T> context, SliceDownloader<DownloadSlice, SliceDownloadResponse<T>> retryDownloader, SliceBundle<T> sliceBundle, List<DownloadSlice> partSlice) {
        for (DownloadSlice slice : partSlice) {
            try {
                SliceDownloadResponse<T> response = retryDownloader.download(slice);
                dealResponse(context, request, sliceBundle, response, null, false);
            } catch (Throwable e) {
                dealResponse(context, request, sliceBundle, null, e, false);
            }
        }
    }

    protected EshopPluginFeature getPlugin(EshopFactory factory) {
        EshopPluginFeature pluginFeature = factory.getFeature(EshopPluginFeature.class);
        if (pluginFeature == null) {
            throw new RuntimeException("请指定订单下载的切片参数");
        }
        return pluginFeature;
    }

    protected void fillMissData(EshopFactory factory, SliceDownloadResponse<T> response, MonitorSourceEnum source) {

    }

    protected void dealResponse(SliceDownloadContext<T> context,
                                TimeDownloadRequest<T> request,
                                SliceBundle<T> sliceBundle,
                                SliceDownloadResponse<T> response,
                                Throwable exception,
                                boolean cursor) {

        EshopFactory factory = context.getFactory();
        DownloadCallback<SliceDownloadCallbackResult<T>> callback = request.getCallback();
        SliceDownloadCallbackResult<T> callbackResult = new SliceDownloadCallbackResult<>();
        callbackResult.setMore(sliceBundle.isMore());
        callbackResult.setSuccess(true);
        callbackResult.setTotal(sliceBundle.getTotal());
        if (response == null) {
            doMonitor(context, true);
            callbackResult.setMore(false);
            callbackResult.setSuccess(false);
            callbackResult.setMessage("订单下载出现异常：" + (exception == null ? "未知错误" : exception.getMessage()));
        } else {
            doMonitor(context, false);
            if (StringUtils.isEmpty(response.getCursor())) {
                callbackResult.setMore(false);
            }
            sliceBundle.addDownloaded(response.getList().size());
            fillMissData(factory, response, context.getSourceEnum());
            callbackResult.setList(response.getList());
            if (null != response.getResultCode() && !OrderDownloadResultEnum.SUCCESS.equals(response.getResultCode())) {
                String message = StringUtils.isNotEmpty(response.getErrorMessage()) ? response.getErrorMessage() : response.getResultCode().getDesc();
                callbackResult.setMessage("订单下载出现异常：" + message);
                callbackResult.setSuccess(false);
            }
        }
        //失败的情况记录子task
        writeRetryTaskWhileError(context, request, response, exception);
        writeOrderSyncTaskLog(request, response, exception);
        sliceBundle.addDownloadedSliceCount();
        callbackResult.setPage(sliceBundle.getDownloadedSliceCount());
        callbackResult.setDownloaded(sliceBundle.getDownloaded());
        callbackResult.setPercent(BigDecimal.valueOf(sliceBundle.getDownloadPercent()));
        callback.complete(callbackResult);
        if (!cursor || response == null) {
            sliceBundle.count();
            return;
        }
        if (StringUtils.isEmpty(response.getCursor())) {
            sliceBundle.count();
        }
    }

    protected void writeOrderSyncTaskLog(TimeDownloadRequest<T> request, SliceDownloadResponse<T> response, Throwable exception) {
    }

    private void doMonitor(SliceDownloadContext<T> context, boolean isError) {
        MonitorService monitor = context.getMonitor();
        if (monitor == null) {
            return;
        }
        ShopType shopType = context.getFactory().getShopType();
        MonitorSourceEnum sourceEnum = context.getSourceEnum();
        if (sourceEnum.equals(MonitorSourceEnum.NONE)) {
            return;
        }
        String apiName = sourceEnum.getApiName();
        String sourceName = sourceEnum.getName();
        Tags tags = Tags.of(Tag.of("shopType", shopType.getName()))
                .and(Tag.of("apiName", apiName))
                .and(Tag.of("sourceName", sourceName));
        if (isError) {
            monitor.recordSum(BifrostApiMonitorTypeEnum.PL_INTERFACE_API_ERROR_QPS.getTopic(), tags, 1);
        }
        monitor.recordSum(BifrostApiMonitorTypeEnum.PL_INTERFACE_API_QPS.getTopic(), tags, 1);
    }

    private void writeRetryTaskWhileError(SliceDownloadContext<T> context,
                                          TimeDownloadRequest<T> request,
                                          SliceDownloadResponse<T> responses,
                                          Throwable exception) {
        if (!isNeedRetryExecute(context, request, responses)) {
            return;
        }
        try {
            EshopOrderSyncTaskLog taskLog = buildRetryTaskInfo(request, responses, exception);
            LogService.update(taskLog);
            String errMsg = StringUtils.isNotEmpty(responses.getErrorMessage()) ? responses.getErrorMessage() : responses.getResultCode().getDesc();
            logger.error(String.format("profileId: %s, eshopId: %s 下载订单出现异常：%s", getProfileIdStr(request), getShopIdStr(request), errMsg));
        } catch (Exception ex) {
            logger.error(String.format("profileId: %s, eshopId: %s 写入自动下单失败的重试任务失败：%s", getProfileIdStr(request), getShopIdStr(request), ex.getMessage()), ex);
        }
    }

    private boolean isNeedRetryExecute(SliceDownloadContext<T> context,
                                       TimeDownloadRequest<T> request,
                                       SliceDownloadResponse<T> responses) {
        try {
            if (!MonitorSourceEnum.AUTO_DOWNLOAD.equals(request.getSourceEnum())) {
                //非自动下单任务不用处理
                return false;
            }
            if (null != responses && OrderDownloadResultEnum.SUCCESS.equals(responses.getResultCode())) {
                //返回成功不写入重试任务
                return false;
            }

            EshopFactory factory = context.getFactory();
            PlatformBaseConfig config = factory.getConfig();
            if (!config.isSupportApiErrorRetryExecute()) {
                //是否支持写入自动下单重试任务
                return false;
            }

            if (null == request.getSystemParams()) {
                return false;
            }
            BigInteger eshopId = request.getSystemParams().geteShopId();
            if (null == eshopId) {
                eshopId = request.getShopId();
                request.getSystemParams().seteShopId(eshopId);
            }
            return null != eshopId;
        } catch (Exception ex) {
            logger.error(String.format("profileId: %s, eshopId: %s 写入自动下单(方法：isNeedRetryExecute)失败的重试任务失败：%s", getProfileIdStr(request), getShopIdStr(request), ex.getMessage()), ex);
        }
        return false;
    }

    private EshopOrderSyncTaskLog buildRetryTaskInfo(TimeDownloadRequest<T> request,
                                                     SliceDownloadResponse<T> responses, Throwable exception) {


        EshopSystemParams systemParams = request.getSystemParams();
        BigInteger eshopId = systemParams.geteShopId();
        OrderDownloadResultEnum rspResult;
        String errMsg = "";
        if (null == responses) {
            rspResult = OrderDownloadResultEnum.NOT_RETRY_ERROR;
        } else {
            rspResult = responses.getResultCode();
            errMsg = responses.getErrorMessage();
        }
        if (!Objects.isNull(exception)) {
            String message = null == exception.getMessage() ? "" : exception.getMessage();
            errMsg += message;
        }
        EshopOrderSyncTaskLog taskLog = new EshopOrderSyncTaskLog();
        taskLog.setProfileId(systemParams.getProfileId());
        taskLog.setEshopId(eshopId);
        taskLog.setEtypeName("系统");
        taskLog.setEshopName(systemParams.getFullName());
        taskLog.setEtypeId(BigInteger.ZERO);
        taskLog.setRetryCount(rspResult.getRetryTimes());
        taskLog.setErrorCode(rspResult);
        taskLog.setSyncState(OrderSyncStateEnum.PREPARE);
        taskLog.setSyncType(OrderSyncTypeEnum.INCREASE_ERROR_RETRY);
        taskLog.setSyncTaskBeginTime(request.getStartTime());
        taskLog.setSyncTaskEndTime(request.getEndTime());
        taskLog.setParentId(new BigInteger(request.getTaskId()));
        taskLog.setSyncTaskTimeDelay(BigInteger.ZERO);
        taskLog.setSyncExecTimeConsuming(BigInteger.ZERO);
        taskLog.setErrorMessage(errMsg);
        taskLog.setId(UId.newId());
        taskLog.setMessageId(taskLog.getId().toString());
        return taskLog;

    }

    private BigInteger getShopId(TimeDownloadRequest<T> request) {
        if (null == request.getSystemParams()) {
            return BigInteger.ZERO;
        }
        BigInteger eshopId = request.getSystemParams().geteShopId();
        if (null == eshopId && null != request.getShopId()) {
            eshopId = request.getShopId();
        }
        eshopId = null == eshopId ? BigInteger.ZERO : eshopId;
        return eshopId;
    }

    private BigInteger getProfileId(TimeDownloadRequest<T> request) {
        if (null == request.getSystemParams() || null == request.getSystemParams().getProfileId()) {
            return BigInteger.ZERO;
        }
        return request.getSystemParams().getProfileId();
    }


    private String getShopIdStr(TimeDownloadRequest<T> request) {
        return getShopId(request).toString();
    }

    private String getProfileIdStr(TimeDownloadRequest<T> request) {
        return getProfileId(request).toString();
    }
}
