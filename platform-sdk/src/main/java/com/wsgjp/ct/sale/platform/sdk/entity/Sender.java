package com.wsgjp.ct.sale.platform.sdk.entity;

import ngp.utils.Md5Utils;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

public class Sender implements Serializable {
    /**
     * 唯一标识
     */
    private BigInteger id;
    /**
     * 帐套id
     */
    private BigInteger profileId;
    /**
     * 寄件人姓名
     */
    private String senderName;
    /**
     * 寄件人手机
     */
    private String senderMobile;
    /**
     * 寄件人电话
     */
    private String senderPhone;
    /**
     * 寄件人国家
     */
    private String senderCountry;
    /**
     * 寄件人省
     */
    private String senderProvince;
    /**
     * 寄件人市
     */
    private String senderCity;
    /**
     * 寄件人区
     */
    private String senderDistrict;
    /**
     * 寄件人街道
     */
    private String senderTown;
    /**
     * 详细地址
     */
    private String senderAddress;
    /**
     * 寄件人完整地址
     */
    private String senderFullAddress;
    /**
     * 寄件人邮编
     */
    private String senderZipCode;

    /**
     * hash值
     */
    private String hashKey = "";
    /**
     * 修改时间
     */
    private Date updateTime;

    private String pi;

    private String mi;
    /**
     * 创建时间
     */
    private Date createTime;


    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderMobile() {
        return senderMobile;
    }

    public void setSenderMobile(String senderMobile) {
        this.senderMobile = senderMobile;
    }

    public String getSenderPhone() {
        return senderPhone;
    }

    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }

    public String getSenderCountry() {
        return senderCountry;
    }

    public void setSenderCountry(String senderCountry) {
        this.senderCountry = senderCountry;
    }

    public String getSenderProvince() {
        return senderProvince;
    }

    public void setSenderProvince(String senderProvince) {
        this.senderProvince = senderProvince;
    }

    public String getSenderCity() {
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    public String getSenderDistrict() {
        return senderDistrict;
    }

    public void setSenderDistrict(String senderDistrict) {
        this.senderDistrict = senderDistrict;
    }

    public String getSenderZipCode() {
        return senderZipCode;
    }

    public void setSenderZipCode(String senderZipCode) {
        this.senderZipCode = senderZipCode;
    }

    public String getSenderAddress() {
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    public String getSenderFullAddress() {
        return String.format("%s%s%s%s%s", this.getSenderProvince() == null ? "" : this.getSenderProvince(),
                this.getSenderCity() == null ? "" : this.getSenderCity(),
                this.getSenderDistrict() == null ? "" : this.getSenderDistrict(),
                this.getSenderTown() == null ? "" : this.getSenderTown(),
                this.getSenderAddress() == null ? "" : this.getSenderAddress());
    }

    public void setSenderFullAddress(String senderFullAddress) {
        this.senderFullAddress = senderFullAddress;
    }

    public String getHashKey() {
        return Md5Utils.md5(String.format("%d%s%s%s", profileId, getSenderName(), getSenderPhone(), getSenderFullAddress()));
    }

    public void setHashKey(String hashKey) {
        this.hashKey = hashKey;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getPi() {
        return pi;
    }

    public void setPi(String pi) {
        this.pi = pi;
    }

    public String getMi() {
        return mi;
    }

    public void setMi(String mi) {
        this.mi = mi;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSenderTown() {
        return senderTown;
    }

    public void setSenderTown(String senderTown) {
        this.senderTown = senderTown;
    }
}