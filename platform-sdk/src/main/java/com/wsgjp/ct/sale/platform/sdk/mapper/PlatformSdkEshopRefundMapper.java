package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.entity.entities.SimpleRefundEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

@Mapper
@Repository
public interface PlatformSdkEshopRefundMapper {
    List<SimpleRefundEntity> getRefundByRefundId(@Param("profileId") BigInteger profileId,
                                           @Param("otypeId") BigInteger otypeId,
                                           @Param("refundIds") List<String> refundIds);
}
