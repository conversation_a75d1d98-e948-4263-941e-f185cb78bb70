package com.wsgjp.ct.sale.platform.sdk.slice;

import com.wsgjp.ct.sale.platform.entity.response.product.SliceProductDownloadResponse;
import com.wsgjp.ct.sale.platform.slice.DownloadProductSlice;
import com.wsgjp.ct.sale.platform.slice.SliceProductParams;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SliceProductDownConText<T> {
    private SliceDownloader<DownloadProductSlice, SliceProductDownloadResponse<T>> sliceDownloader;
    private List<SliceProductParams> sliceProductParams;
    private SliceTimeExtractor<T> timeExtractor;

    public SliceTimeExtractor<T> getTimeExtractor() {
        return timeExtractor;
    }

    public void setTimeExtractor(SliceTimeExtractor<T> timeExtractor) {
        this.timeExtractor = timeExtractor;
    }
    public SliceDownloader<DownloadProductSlice, SliceProductDownloadResponse<T>> getSliceDownloader() {
        return sliceDownloader;
    }

    public void setSliceDownloader(SliceDownloader<DownloadProductSlice, SliceProductDownloadResponse<T>> sliceDownloader) {
        this.sliceDownloader = sliceDownloader;
    }

    public List<SliceProductParams> getSliceProductParams() {
        return sliceProductParams;
    }

    public void setSliceProductParams(List<SliceProductParams> sliceProductParams) {
        this.sliceProductParams = sliceProductParams;
    }
}
