package com.wsgjp.ct.sale.platform.sdk.config;

import com.wsgjp.ct.sale.platform.utils.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "platform.biz")
public class PlatformBizConfig {
    private boolean checkFreightEnabled;

    private Boolean sendUseMultiThreadEnabled = false;

    private int sendSleepMs = 500;

    private int sendConcurrencySeconds = 2;
    private String frightCacheVersion = "V1";

    /**
     * 单个账套下载订单的并发线程数
     */
    private int singleThreadCount = 4;

    private String notIgnoreGiftSendShopTypes = "20";

    private String forceCheckSendQtyShopTypes = "2,40,52,90,91,108,115";

    private String notSendShopTypes = "892";
    private String freightMappingDealPage = "sale/eshoporder/eshop/EShopOnlineFreightMapping.gspx";

    private Boolean needCheckNotifyRefund = true;
    private Boolean needCheckFreightMapping = true;

    private String notCheckFreightMappingShopTypes = "888,889,890,891,892,900,999,1000,9999";

    private Boolean needCheckSendRecords = true;

    /**
     * 十分钟
     */
    private int redisTime = 10;

    /**
     * 本地订单也强制使用售后发货的店铺类型
     */
    private String forceLocalRefundUseRefundSendShopTypes = "20";


    public int getRedisTime() {
        return redisTime;
    }

    public void setRedisTime(int redisTime) {
        this.redisTime = redisTime;
    }

    public boolean isCheckFreightEnabled() {
        return checkFreightEnabled;
    }

    public void setCheckFreightEnabled(boolean checkFreightEnabled) {
        this.checkFreightEnabled = checkFreightEnabled;
    }

    public Boolean getSendUseMultiThreadEnabled() {
        return sendUseMultiThreadEnabled;
    }

    public void setSendUseMultiThreadEnabled(Boolean sendUseMultiThreadEnabled) {
        this.sendUseMultiThreadEnabled = sendUseMultiThreadEnabled;
    }

    public int getSendSleepMs() {
        return sendSleepMs;
    }

    public void setSendSleepMs(int sendSleepMs) {
        this.sendSleepMs = sendSleepMs;
    }

    public String getFrightCacheVersion() {
        return frightCacheVersion;
    }

    public void setFrightCacheVersion(String frightCacheVersion) {
        this.frightCacheVersion = frightCacheVersion;
    }

    public int getSendConcurrencySeconds() {
        return sendConcurrencySeconds;
    }

    public void setSendConcurrencySeconds(int sendConcurrencySeconds) {
        this.sendConcurrencySeconds = sendConcurrencySeconds;
    }

    public int getSingleThreadCount() {
        return singleThreadCount;
    }

    public void setSingleThreadCount(int singleThreadCount) {
        this.singleThreadCount = singleThreadCount;
    }

    public String getNotIgnoreGiftSendShopTypes() {
        return notIgnoreGiftSendShopTypes;
    }

    public void setNotIgnoreGiftSendShopTypes(String notIgnoreGiftSendShopTypes) {
        this.notIgnoreGiftSendShopTypes = notIgnoreGiftSendShopTypes;
    }

    public String getForceCheckSendQtyShopTypes() {
        return forceCheckSendQtyShopTypes;
    }

    public List<Integer> getForceCheckSendQtyShopTypeList() {
        if (StringUtils.isEmpty(forceCheckSendQtyShopTypes)) {
            return new ArrayList<>();
        }
        String[] shopTypes = forceCheckSendQtyShopTypes.split(",");
        return Arrays.stream(shopTypes).map(Integer::valueOf).collect(Collectors.toList());
    }

    public void setForceCheckSendQtyShopTypes(String forceCheckSendQtyShopTypes) {
        this.forceCheckSendQtyShopTypes = forceCheckSendQtyShopTypes;
    }

    public String getNotSendShopTypes() {
        return notSendShopTypes;
    }

    public void setNotSendShopTypes(String notSendShopTypes) {
        this.notSendShopTypes = notSendShopTypes;
    }

    public List<Integer> getNotSendShopTypeList() {
        if (StringUtils.isEmpty(notSendShopTypes)) {
            return new ArrayList<>();
        }
        String[] shopTypes = notSendShopTypes.split(",");
        return Arrays.stream(shopTypes).map(Integer::valueOf).collect(Collectors.toList());
    }

    public String getFreightMappingDealPage() {
        return freightMappingDealPage;
    }

    public void setFreightMappingDealPage(String freightMappingDealPage) {
        this.freightMappingDealPage = freightMappingDealPage;
    }

    public Boolean getNeedCheckNotifyRefund() {
        return needCheckNotifyRefund;
    }

    public void setNeedCheckNotifyRefund(Boolean needCheckNotifyRefund) {
        this.needCheckNotifyRefund = needCheckNotifyRefund;
    }

    public Boolean getNeedCheckFreightMapping() {
        return needCheckFreightMapping;
    }

    public void setNeedCheckFreightMapping(Boolean needCheckFreightMapping) {
        this.needCheckFreightMapping = needCheckFreightMapping;
    }

    public String getNotCheckFreightMappingShopTypes() {
        return notCheckFreightMappingShopTypes;
    }

    public void setNotCheckFreightMappingShopTypes(String notCheckFreightMappingShopTypes) {
        this.notCheckFreightMappingShopTypes = notCheckFreightMappingShopTypes;
    }

    public List<Integer> getNotCheckFreightMappingShopTypeList() {
        if (StringUtils.isEmpty(notCheckFreightMappingShopTypes)) {
            return new ArrayList<>();
        }
        String[] shopTypes = notCheckFreightMappingShopTypes.split(",");
        return Arrays.stream(shopTypes).map(Integer::valueOf).collect(Collectors.toList());
    }

    public String getForceLocalRefundUseRefundSendShopTypes() {
        return forceLocalRefundUseRefundSendShopTypes;
    }

    public void setForceLocalRefundUseRefundSendShopTypes(String forceLocalRefundUseRefundSendShopTypes) {
        this.forceLocalRefundUseRefundSendShopTypes = forceLocalRefundUseRefundSendShopTypes;
    }

    public List<Integer> getForceLocalRefundUseAppendSendShopTypeList() {
        if (StringUtils.isEmpty(forceLocalRefundUseRefundSendShopTypes)) {
            return new ArrayList<>();
        }
        String[] shopTypes = forceLocalRefundUseRefundSendShopTypes.split(",");
        return Arrays.stream(shopTypes).map(Integer::valueOf).collect(Collectors.toList());
    }

    public Boolean getNeedCheckSendRecords() {
        return needCheckSendRecords;
    }

    public void setNeedCheckSendRecords(Boolean needCheckSendRecords) {
        this.needCheckSendRecords = needCheckSendRecords;
    }
}
