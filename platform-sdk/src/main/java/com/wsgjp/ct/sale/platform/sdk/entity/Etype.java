package com.wsgjp.ct.sale.platform.sdk.entity;


import java.math.BigInteger;
import java.util.Date;

public class Etype {

  public Etype(){}

  public Etype(String fullname, BigInteger id){
    this.fullname=fullname;
    this.id=id;
  }

  private BigInteger id;
  private BigInteger profileId;
  private String typeid;
  private String partypeid;
  private String usercode;
  private String fullname;
  private String shortname;
  private String namepy;
  private boolean classed;
  private boolean stoped;
  private boolean deleted;
  private BigInteger rowindex;
  private Date createTime;
  private Date updateTime;
  private String tel;
  private String mobile;
  private String address;
  private String memo;
  private String birthday;
  private String email;
  private boolean loginUser;
  private boolean bindWeixin;
  private String headurl;
  private String nickname;
  private BigInteger dtypeId;
  private String dtypeName;
  private BigInteger sysid;
  private String loginUserName;


  public BigInteger getId() {
    if (id == null)
    {
      return BigInteger.ZERO;
    }
    return id;
  }

  public void setId(BigInteger id) {
    this.id = id;
  }

  public BigInteger getProfileId() {
    return profileId;
  }

  public void setProfileId(BigInteger profileId) {
    this.profileId = profileId;
  }

  public String getTypeid() {
    return typeid;
  }

  public void setTypeid(String typeid) {
    this.typeid = typeid;
  }

  public String getPartypeid() {
    return partypeid;
  }

  public void setPartypeid(String partypeid) {
    this.partypeid = partypeid;
  }

  public String getUsercode() {
    return usercode;
  }

  public void setUsercode(String usercode) {
    this.usercode = usercode;
  }

  public String getFullname() {
    return fullname;
  }

  public void setFullname(String fullname) {
    this.fullname = fullname;
  }

  public String getShortname() {
    return shortname;
  }

  public void setShortname(String shortname) {
    this.shortname = shortname;
  }

  public String getNamepy() {
    return namepy;
  }

  public void setNamepy(String namepy) {
    this.namepy = namepy;
  }

  public boolean isClassed() {
    return classed;
  }

  public void setClassed(boolean classed) {
    this.classed = classed;
  }

  public boolean isStoped() {
    return stoped;
  }

  public void setStoped(boolean stoped) {
    this.stoped = stoped;
  }

  public boolean isDeleted() {
    return deleted;
  }

  public void setDeleted(boolean deleted) {
    this.deleted = deleted;
  }

  public BigInteger getRowindex() {
    return rowindex;
  }

  public void setRowindex(BigInteger rowindex) {
    this.rowindex = rowindex;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public String getTel() {
    return tel;
  }

  public void setTel(String tel) {
    this.tel = tel;
  }

  public String getMobile() {
    return mobile;
  }

  public void setMobile(String mobile) {
    this.mobile = mobile;
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public String getBirthday() {
    return birthday;
  }

  public void setBirthday(String birthday) {
    this.birthday = birthday;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public boolean isLoginUser() {
    return loginUser;
  }

  public void setLoginUser(boolean loginUser) {
    this.loginUser = loginUser;
  }

  public boolean isBindWeixin() {
    return bindWeixin;
  }

  public void setBindWeixin(boolean bindWeixin) {
    this.bindWeixin = bindWeixin;
  }

  public String getHeadurl() {
    return headurl;
  }

  public void setHeadurl(String headurl) {
    this.headurl = headurl;
  }

  public String getNickname() {
    return nickname;
  }

  public void setNickname(String nickname) {
    this.nickname = nickname;
  }

  public BigInteger getDtypeId() {
    return dtypeId;
  }

  public void setDtypeId(BigInteger dtypeId) {
    this.dtypeId = dtypeId;
  }

  public BigInteger getSysid() {
    return sysid;
  }

  public void setSysid(BigInteger sysid) {
    this.sysid = sysid;
  }

  public String getLoginUserName() {
    return loginUserName;
  }

  public void setLoginUserName(String loginUserName) {
    this.loginUserName = loginUserName;
  }

  public String getDtypeName() {
    return dtypeName;
  }

  public void setDtypeName(String dtypeName) {
    this.dtypeName = dtypeName;
  }
}
