package com.wsgjp.ct.sale.platform.sdk.entity.request;

import com.wsgjp.ct.sale.platform.sdk.entity.send.DeliverSendDetail;
import com.wsgjp.ct.sale.platform.sdk.entity.send.SyncFreightBillNoRequest;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR> 2023/11/1 15:21
 */
public class SendOrderRequest {

    @ApiModelProperty(value = "本次发货的列表", required = true)
    private List<SyncFreightBillNoRequest> freightBillList;

    @ApiModelProperty(value = "关联的其他发货单明细列表", required = true)
    private List<DeliverSendDetail> allDeliverDetailList;

    public List<SyncFreightBillNoRequest> getFreightBillList() {
        return freightBillList;
    }

    public void setFreightBillList(List<SyncFreightBillNoRequest> freightBillList) {
        this.freightBillList = freightBillList;
    }

    public List<DeliverSendDetail> getAllDeliverDetailList() {
        return allDeliverDetailList;
    }

    public void setAllDeliverDetailList(List<DeliverSendDetail> allDeliverDetailList) {
        this.allDeliverDetailList = allDeliverDetailList;
    }
}
