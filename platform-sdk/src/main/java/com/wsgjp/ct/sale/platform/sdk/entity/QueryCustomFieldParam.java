package com.wsgjp.ct.sale.platform.sdk.entity;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QueryCustomFieldParam {
    private BigInteger profileId;
    private Integer subType;
    private String fieldName;
    private List<BigInteger> ptypeIds;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public List<BigInteger> getPtypeIds() {
        return ptypeIds;
    }

    public void setPtypeIds(List<BigInteger> ptypeIds) {
        this.ptypeIds = ptypeIds;
    }
}
