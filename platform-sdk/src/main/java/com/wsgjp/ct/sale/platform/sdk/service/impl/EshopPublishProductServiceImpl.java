package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.address.QueryPlatformAddressRequest;
import com.wsgjp.ct.sale.platform.entity.request.product.*;
import com.wsgjp.ct.sale.platform.entity.response.address.QueryPlatformAddressResponse;
import com.wsgjp.ct.sale.platform.entity.response.product.*;
import com.wsgjp.ct.sale.platform.exception.BusinessSupportException;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.product.EshopQueryBrandFeature;
import com.wsgjp.ct.sale.platform.feature.product.EshopUpdateItemPrimaryPicFeature;
import com.wsgjp.ct.sale.platform.feature.product.publish.*;
import com.wsgjp.ct.sale.platform.feature.product.publish.address.EshopQueryPlatformAddressInfoFeature;
import com.wsgjp.ct.sale.platform.feature.product.publish.file.EshopFileUploadFeature;
import com.wsgjp.ct.sale.platform.feature.product.publish.image.EshopImageOperateFeature;
import com.wsgjp.ct.sale.platform.feature.product.publish.image.EshopImageStorageSpaceFeature;
import com.wsgjp.ct.sale.platform.feature.product.publish.prop.*;
import com.wsgjp.ct.sale.platform.feature.product.publish.video.EshopVideoUploadFeature;
import com.wsgjp.ct.sale.platform.feature.purchase.EshopQueryPurchaserFeature;
import com.wsgjp.ct.sale.platform.sdk.service.EshopPublishProductService;
import com.wsgjp.ct.sale.platform.utils.FileUtils;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 发布商品相关
 *
 * <AUTHOR>
 */
@Service
public class EshopPublishProductServiceImpl implements EshopPublishProductService {
    @Override
    public QueryEshopBrandResponse queryEshopBrands(QueryBrandRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryBrandFeature feature = factory.getFeature(EshopQueryBrandFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询品牌目录！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryEshopBrands(request);
    }

    @Override
    public QueryProductCategoryResponse queryProductCategory(BaseRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopProductCategoryFeature feature = factory.getFeature(EshopProductCategoryFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询商品类目！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryProductCategory();
    }

    @Override
    public CreateProductResponse createProduct(CreateProductRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopCreateProductFeature feature = factory.getFeature(EshopCreateProductFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持新增商品信息！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.createProduct(request);
    }

    @Override
    public UpdateProductResponse updateProduct(UpdateProductRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopUpdateProductFeature feature = factory.getFeature(EshopUpdateProductFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持修改商品信息！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.updateProduct(request);
    }

    @Override
    public QuerySaleUnitResponse querySaleUnits(QuerySaleUnitRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQuerySaleUnitsFeature feature = factory.getFeature(EshopQuerySaleUnitsFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询平台销售单位！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.querySaleUnits(request);
    }

    @Override
    public QueryExtPropsResponse queryExtProps(QueryExtPropsRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryExtPropsFeature feature = factory.getFeature(EshopQueryExtPropsFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询扩展属性！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryExtProps(request);
    }

    @Override
    public QuerySalePropsResponse querySaleProps(QuerySalePropsRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQuerySalePropsFeature feature = factory.getFeature(EshopQuerySalePropsFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询销售属性！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.querySaleProps(request);
    }

    @Override
    public QuerySpecPropsResponse querySpecProps(QuerySpecPropsRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQuerySpecPropsFeature feature = factory.getFeature(EshopQuerySpecPropsFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询规格属性！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.querySpecProps(request);
    }

    @Override
    public QueryProductBizResponse queryProductBiz(QueryProductBizRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryProductBizFeature feature = factory.getFeature(EshopQueryProductBizFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询商品特殊业务！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryProductBiz(request);
    }

    @Override
    public AddProductPrimaryPicResponse addProductPrimaryPic(AddProductPrimaryPicRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopAddProductPrimaryPicFeature feature = factory.getFeature(EshopAddProductPrimaryPicFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持添加商品主图信息！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.addProductPrimaryPic(request);
    }

    @Override
    public SetProductConfigResponse batchSetProductConfig(SetProductConfigRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopProductConfigFeature feature = factory.getFeature(EshopProductConfigFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持发布商品配置！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.batchSetProductConfig(request);
    }

    @Override
    public QueryProductPublishApplyInfoResponse queryProductPublishApplyInfo(QueryProductPublishApplyInfoRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopProductPublishApplyInfoFeature feature = factory.getFeature(EshopProductPublishApplyInfoFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询商品发布申请信息！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryProductPublishApplyInfo(request);
    }

    @Override
    public GeVideoUploadPathUrlResponse getVideoUploadUrlInfo(GeVideoUploadUrlInfoRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopVideoUploadFeature feature = factory.getFeature(EshopVideoUploadFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持获取平台视频上传地址信息！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.getVideoUploadUrlInfo(request);
    }

    @Override
    public QueryPurchaserResponse queryPurchaserList(BaseRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryPurchaserFeature feature = factory.getFeature(EshopQueryPurchaserFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询采购员列表信！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryPurchaserList();
    }

    @Override
    public QueryImageStorageSpaceInfoResponse queryImageStorageSpaceInfo(QueryImageStorageSpaceInfoRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopImageStorageSpaceFeature feature = factory.getFeature(EshopImageStorageSpaceFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询平台图片存储空间信息！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryImageStorageSpaceInfo(request);
    }

    @Override
    public QueryDangerGoodsResponse queryDangerGoods(BaseRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryDangerGoodsFeature feature = factory.getFeature(EshopQueryDangerGoodsFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询危险品列表！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryDangerGoods();
    }

    @Override
    public PlatImageUploadResponse imageUpload(PlatImageUploadRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopImageOperateFeature feature = factory.getFeature(EshopImageOperateFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持上传图片到平台图库！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        if (StringUtils.isNotBlank(request.getImageUrl())) {
            request.setImageBytes(FileUtils.fileToBytes(request.getImageUrl(), "GET"));
        }
        return feature.imageUpload(request);
    }

    @Override
    public PlatFileUploadResponse fileUpload(PlatFileUploadRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopFileUploadFeature feature = factory.getFeature(EshopFileUploadFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持上传文件到平台！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.fileUpload(request);
    }

    @Override
    public PlatImageUpdateResponse imageUpdate(PlatImageUpdateRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopImageOperateFeature feature = factory.getFeature(EshopImageOperateFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持修改平台图库图片！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.imageUpdate(request);
    }

    @Override
    public PlatImageDeleteResponse imageDelete(PlatImageDeleteRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopImageOperateFeature feature = factory.getFeature(EshopImageOperateFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持删除平台图库图片！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.imageDelete(request);
    }

    @Override
    public PlatImageQueryResponse imageQuery(PlatImageQueryRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopImageOperateFeature feature = factory.getFeature(EshopImageOperateFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询平台图库图片！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.imageQuery(request);
    }

    @Override
    public QueryMarkByCatIdResponse getBaseMarkByCatId(QueryMarkByCatIdRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryMarkByCatIdFeature feature = factory.getFeature(EshopQueryMarkByCatIdFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询三级类目特殊属性消息！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryMarkByCatId(request);
    }

    @Override
    public QuerySalerResponse querySalerInfo(QuerySalerRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQuerySalerInfoFeature feature = factory.getFeature(EshopQuerySalerInfoFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询销售员名字！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.querySalerInfo(request);
    }

    @Override
    public ItemDynamicFieldGaeaResponse getItemDynamicFieldGaea(ItemDynamicFieldGaeaRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryItemDynamicFieldGaeaFeature feature = factory.getFeature(EshopQueryItemDynamicFieldGaeaFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持根据三级类目id查询动态字段！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.getItemDynamicFieldGaea(request);
    }

    @Override
    public ItemSpuTemplateResponse getItemSpuTemplate(ItemSpuTemplateRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopItemSpuTemplateFeature feature = factory.getFeature(EshopItemSpuTemplateFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持根据三级类目id查询动态字段！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.getItemSpuTemplate(request);
    }

    @Override
    public ItemModelResponse getItemModel(ItemSpuTemplateRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopItemModelFeature feature = factory.getFeature(EshopItemModelFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询型号规则！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.getItemModel(request);
    }

    @Override
    public UpdateProductPrimaryPicResponse updateItemPrimaryPic(UpdateProductPrimaryPicRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopUpdateItemPrimaryPicFeature feature = factory.getFeature(EshopUpdateItemPrimaryPicFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持更新被驳回的主图申请！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.updateItemPrimaryPic(request);
    }

    @Override
    public QueryProductPrimaryPicResponse queryApplyItemPrimaryPic(QueryProductPrimaryPicRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryApplyItemPrimaryPicFeature feature = factory.getFeature(EshopQueryApplyItemPrimaryPicFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持更新被驳回的主图申请！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryApplyItemPrimaryPic(request);
    }

    @Override
    public QueryItemAppliesPrimaryPicResponse queryItemAppliesPrimaryPic(QueryItemAppliesPrimaryPicRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryItemAppliesPrimaryPicFeature feature = factory.getFeature(EshopQueryItemAppliesPrimaryPicFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持获取主图申请列表！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryItemAppliesPrimaryPic(request);
    }

    @Override
    public QueryProductCategoryResponse queryProductCategoryListByParent(QueryCatByParentRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopProductCatByParentFeature feature = factory.getFeature(EshopProductCatByParentFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持根据父节点获取子类目！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryProductCategoryListByParent(request);
    }


    private CheckSupportCategoryResponse buildCheckSupportCategoryResponse(QueryProductCategoryResponse response, String categoryId) {
        CheckSupportCategoryResponse checkResponse = new CheckSupportCategoryResponse();
        if (!response.getSuccess()) {
            checkResponse.setExist(false);
            checkResponse.setSuccess(false);
            checkResponse.setMessage(response.getMessage());
        } else {
            if (CollectionUtils.isEmpty(response.getCategoryInfos())) {
                checkResponse.setExist(false);
            } else {
                boolean isPresent = response.getCategoryInfos().stream()
                        .anyMatch(cate -> StringUtils.equals(cate.getCatId(), categoryId));
                checkResponse.setExist(isPresent);
            }
            checkResponse.setSuccess(true);
        }
        return checkResponse;
    }

    @Override
    public CheckSupportCategoryResponse checkSupportCategory(CheckSupportCategoryRequest request) {
        CheckSupportCategoryResponse checkResponse = new CheckSupportCategoryResponse();
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        PlatformBaseConfig config = EshopFactoryManager.getConfig(request.getShopType());
        if (config != null && config.getNotNeedCheckCategory()) {
            checkResponse.setExist(true);
            checkResponse.setSuccess(true);
            return checkResponse;
        }
        EshopProductCatByParentFeature catByParentFeature = factory.getFeature(EshopProductCatByParentFeature.class);
        if (catByParentFeature != null) {
            QueryCatByParentRequest req = new QueryCatByParentRequest();
            req.setParentCatId(request.getParentCategoryId());
            req.setShopId(request.getShopId());
            req.setSystemParams(request.getSystemParams());
            QueryProductCategoryResponse response = catByParentFeature.queryProductCategoryListByParent(req);
            return buildCheckSupportCategoryResponse(response, request.getCategoryId());
        }
        EshopProductCategoryFeature categoryFeature = factory.getFeature(EshopProductCategoryFeature.class);
        if (categoryFeature != null) {
            QueryProductCategoryResponse response = categoryFeature.queryProductCategory();
            return buildCheckSupportCategoryResponse(response, request.getCategoryId());
        }
        checkResponse.setExist(false);
        checkResponse.setSuccess(false);
        checkResponse.setMessage(String.format("%s不支持检查店铺类目！", request.getSystemParams().getShopType().getName()));
        return checkResponse;
    }

    @Override
    public List<PublishProductFieldsMapping> queryEshopPublishProductFieldsMapping(BaseRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPublishProductFieldsMappingFeature feature = factory.getFeature(EshopPublishProductFieldsMappingFeature.class);
        if (feature == null) {
            if (StringUtils.isNotEmpty(factory.getConfig().getPublishFieldsMapping())) {
                return JsonUtils.toObject(factory.getConfig().getPublishFieldsMapping(), ArrayList.class);
            }
            String msg = String.format("%不支持获取映射！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryEshopPublishProductFieldsMapping();
    }

    @Override
    public ItemDynamicFieldGaeaResponse queryProductSchemaByCatId(ItemDynamicFieldGaeaRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryAndPublishProductSchemaFeature feature = factory.getFeature(EshopQueryAndPublishProductSchemaFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持根据类目id查询动态模版字段！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryProductSchemaByCatId(request);
    }

    @Override
    public ItemDynamicFieldGaeaResponse matchProduct(MatchProductRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryAndPublishProductSchemaFeature feature = factory.getFeature(EshopQueryAndPublishProductSchemaFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持匹配产品！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.matchProduct(request);
    }


    @Override
    public QueryPlatformAddressResponse queryPlatformAddress(QueryPlatformAddressRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryPlatformAddressInfoFeature feature = factory.getFeature(EshopQueryPlatformAddressInfoFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询平台地址库！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.queryPlatformAddress(request);
    }

    @Override
    public ItemDynamicFieldGaeaResponse querySubProps(QuerySubPropsRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQuerySubPropsFeature feature = factory.getFeature(EshopQuerySubPropsFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询级联属性！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.querySubProps(request);
    }

    @Override
    public GetCategoryRuleResponse getCategoryRule(GetCategoryRuleRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopGetCategoryRuleFeature feature = factory.getFeature(EshopGetCategoryRuleFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持查询标品类目规则！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.getCategoryRule(request);
    }

    @Override
    public GetModelOrItemNumListResponse getModelOrItemNumList(GetModelOrItemNumListRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopGetModelOrItemNumListFeature feature = factory.getFeature(EshopGetModelOrItemNumListFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持通过类目、品牌查询标品的型号/货号列表！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.getModelOrItemNumList(request);
    }

    @Override
    public StandardProductSearchResponse standardProductSearch(StandardProductSearchRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopStandardProductSearchFeature feature = factory.getFeature(EshopStandardProductSearchFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持标品查询！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.standardProductSearch(request);
    }

    @Override
    public EshopProductBuildSchemaResponse buildPublishSchema(EshopBuildPublishSchemaRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopProductPublishBuildSchemaFeature feature = factory.getFeature(EshopProductPublishBuildSchemaFeature.class);
        if (feature == null) {
            String msg = String.format("%s不支持构建schema！", request.getSystemParams().getShopType().getName());
            throw new BusinessSupportException(msg);
        }
        return feature.buildPublishSchema(request);
    }
}
