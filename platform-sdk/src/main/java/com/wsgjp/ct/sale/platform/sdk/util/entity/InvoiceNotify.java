package com.wsgjp.ct.sale.platform.sdk.util.entity;

public class InvoiceNotify {
    private String seller_id;
    private String seller_nick;
    private String order_id;
    //发票属性(0:公司；1：个人)
    private int invoice_attr;
    //发票形态 （1:电子发票; 2：纸质发票)
    private int invoice_kind;
    private String company_title;
    private String tax_no;
    private int invoice_type;
    private InvoiceExtendArg extend_arg;

    public String getSeller_id() {
        return seller_id;
    }

    public void setSeller_id(String seller_id) {
        this.seller_id = seller_id;
    }

    public String getSeller_nick() {
        return seller_nick;
    }

    public void setSeller_nick(String seller_nick) {
        this.seller_nick = seller_nick;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public int getInvoice_attr() {
        return invoice_attr;
    }

    public void setInvoice_attr(int invoice_attr) {
        this.invoice_attr = invoice_attr;
    }

    public int getInvoice_kind() {
        return invoice_kind;
    }

    public void setInvoice_kind(int invoice_kind) {
        this.invoice_kind = invoice_kind;
    }

    public String getCompany_title() {
        return company_title;
    }

    public void setCompany_title(String company_title) {
        this.company_title = company_title;
    }

    public String getTax_no() {
        return tax_no;
    }

    public void setTax_no(String tax_no) {
        this.tax_no = tax_no;
    }

    public int getInvoice_type() {
        return invoice_type;
    }

    public void setInvoice_type(int invoice_type) {
        this.invoice_type = invoice_type;
    }

    public InvoiceExtendArg getExtend_arg() {
        return extend_arg;
    }

    public void setExtend_arg(InvoiceExtendArg extend_arg) {
        this.extend_arg = extend_arg;
    }
}