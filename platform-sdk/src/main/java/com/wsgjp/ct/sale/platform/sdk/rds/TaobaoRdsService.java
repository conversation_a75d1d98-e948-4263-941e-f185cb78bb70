package com.wsgjp.ct.sale.platform.sdk.rds;

import bf.datasource.DataSourceManager;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.domain.Item;
import com.taobao.api.domain.Refund;
import com.taobao.api.request.RefundGetRequest;
import com.taobao.api.response.ItemSellerGetResponse;
import com.taobao.api.response.RefundGetResponse;
import com.taobao.api.response.TradeFullinfoGetResponse;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.config.EshopRdsConfig;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.order.RdsOrderEntity;
import com.wsgjp.ct.sale.platform.dto.product.EshopProductEntity;
import com.wsgjp.ct.sale.platform.dto.product.RdsProductEntity;
import com.wsgjp.ct.sale.platform.dto.refund.EshopRefundOrderEntity;
import com.wsgjp.ct.sale.platform.dto.refund.RdsRefundEntity;
import com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderByIdsRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest;
import com.wsgjp.ct.sale.platform.entity.request.product.QueryRdsProductByIdRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.factory.taobao.TaobaoUtils;
import com.wsgjp.ct.sale.platform.factory.taobao.taobaobuilder.TaobaoOrderBuilder;
import com.wsgjp.ct.sale.platform.factory.taobao.taobaobuilder.TaobaoProductBuilder;
import com.wsgjp.ct.sale.platform.factory.taobao.taobaobuilder.TaobaoRefundBuilder;
import com.wsgjp.ct.sale.platform.rds.RdsParams;
import com.wsgjp.ct.sale.platform.rds.RdsService;
import com.wsgjp.ct.sale.platform.sdk.mapper.TaobaoRdsMapper;
import com.wsgjp.ct.sale.platform.slice.DownloadSlice;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import ngp.loadbalancer.context.RouteContext;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.starter.web.base.GeneralException;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class TaobaoRdsService implements RdsService {
    private static final int QUERY_TYPE_ORDER = 0;
    private static final int QUERY_TYPE_REFUND = 1;
    private static final int QUERY_TYPE_PRODUCT = 2;
    private final Logger logger = LoggerFactory.getLogger(TaobaoRdsService.class);
    private final TaobaoRdsMapper taobaoRdsMapper;
    private final EshopRdsConfig rdsConfig;

    public TaobaoRdsService(TaobaoRdsMapper taobaoRdsMapper, EshopRdsConfig rdsConfig) {
        this.taobaoRdsMapper = taobaoRdsMapper;
        this.rdsConfig = rdsConfig;
    }

    private List<EshopOrderEntity> buildOrdersFromRdsEntities(List<RdsOrderEntity> rdsEntities, RdsParams params, String req) {
        if (rdsEntities == null || rdsEntities.size() == 0) {
            return new ArrayList<>();
        }

        PlatformBaseConfig platformBaseConfig = params.getPlatformBaseConfig();
//        TaobaoClient tbClient = new DefaultTaobaoClient
//                (platformBaseConfig.getApiUrl(), platformBaseConfig.getAppKey(), platformBaseConfig.getAppSecret(),
//                        platformBaseConfig.getFormat(), platformBaseConfig.getConnectTimeout(),
//                        platformBaseConfig.getReadTimeout(), platformBaseConfig.getSignMethod());
        TaobaoOrderBuilder builder = new TaobaoOrderBuilder(params.getParams(), params.getParams().getShopType(), platformBaseConfig);

        List<EshopOrderEntity> eshopOrderEntities = new ArrayList<>(rdsEntities.size());
        for (RdsOrderEntity rdsOrder : rdsEntities) {
            try {
                TradeFullinfoGetResponse rsp = com.taobao.api.internal.util.TaobaoUtils.parseResponse(rdsOrder.getResponse()
                        , TradeFullinfoGetResponse.class);
                if (rsp.getTrade().getTradeAttr() == null) {
                    String trade_tst = "\"trade_attr\":\"";
                    int index = rdsOrder.getResponse().indexOf(trade_tst);
                    if (index != -1) {
                        String txt1 = rdsOrder.getResponse().substring(index + trade_tst.length());
                        String txt3 = "}\"\"";
                        int index3 = txt1.indexOf(txt3);
                        String txt2 = txt1.substring(0, index3 + 1).replace("\\", "");
                        rsp.getTrade().setTradeAttr(txt2);
                    }
                }
                EshopOrderEntity eshopOrder = builder.buildEshopOrderEntity(rsp.getTrade(), JsonUtils.toJson(req));
                eshopOrderEntities.add(eshopOrder);
            } catch (Exception ex) {
                logger.error("淘宝rds构建订单失败:", ex);
            }
        }
        return eshopOrderEntities;
    }

    @Override
    public ShopType[] supportedShopTypes() {
        return new ShopType[]{ShopType.TaoBao, ShopType.Tmall,ShopType.TaoBaoXsd};
    }

    @Override
    public List<EshopOrderEntity> downloadOrderById(List<String> tradeIds, RdsParams params) {
        String[] rdsLists = getRdsLists();
        if (rdsLists.length == 0) {
            return new ArrayList<>();
        }
        String name = params.getParams().getRdsName();
        if (StringUtils.isNotEmpty(name)) {
            List<RdsOrderEntity> rdsOrderEntityList = doDownloadByTid(tradeIds, params, name);
            return buildOrdersFromRdsEntities(rdsOrderEntityList, params, JsonUtils.toJson(tradeIds));
        }
        Map<String, EshopOrderEntity> result = new HashMap<>();
        for (String rdsName : rdsLists) {
            List<RdsOrderEntity> entityList = doDownloadByTid(tradeIds, params, rdsName);
            if (CollectionUtils.isEmpty(entityList)) {
                continue;
            }
            List<EshopOrderEntity> dbList = buildOrdersFromRdsEntities(entityList, params, JsonUtils.toJson(tradeIds));
            doAppendOrder(result, dbList);
        }
        return new ArrayList<>(result.values());
    }

    private List<RdsOrderEntity> doDownloadByTid(List<String> tradeIds, RdsParams params, String name) {
        try {
            DataSourceManager.setName(name);
            QueryRdsOrderByIdsRequest request = new QueryRdsOrderByIdsRequest();
            request.setSellerNick(params.getParams().getShopAccount());
            request.setTradeIds(tradeIds);
            return taobaoRdsMapper.queryRdsOrdersByTradeIds(request);
        } catch (Exception ex) {
            logger.error("账套{}通过RDS下载订单时报错：{}", params.getParams().getProfileId(), ex.getMessage(), ex);
            return new ArrayList<>();
        } finally {
            restDataSource();
        }
    }

    private void doAppendOrder(Map<String, EshopOrderEntity> result, List<EshopOrderEntity> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }
        for (EshopOrderEntity entity : sourceList) {
            if (!result.containsKey(entity.getTradeId())) {
                result.put(entity.getTradeId(), entity);
                continue;
            }
            EshopOrderEntity exist = result.get(entity.getTradeId());
            if (exist.getModifiedTime().before(exist.getModifiedTime())) {
                result.put(entity.getTradeId(), entity);
            }
        }
    }

    @Override
    public SliceDownloadResponse<EshopOrderEntity> downloadOrderByCreateTime(DownloadSlice slice, RdsParams params) {
        QueryRdsOrderRequest request = buildQueryRequest(slice, params, QUERY_TYPE_ORDER);
        SliceDownloadResponse<EshopOrderEntity> response = new SliceDownloadResponse<>(slice);
        String[] rdsLists = getRdsLists();
        if (rdsLists.length == 0) {
            logger.info("账套{},网店{},淘宝通过RDS全量获取订单失败.错误原因:RDS配置为空，无法获取订单",
                    params.getParams().getProfileId(),
                    params.getParams().geteShopId());
            throw new RuntimeException("RDS配置为空，无法获取订单");
        }
        String name = params.getParams().getRdsName();
        if (StringUtils.isNotEmpty(name)) {
            doGeyRdsOrderByName(name, params, request, response);
            return response;
        }
        for (String rdsName : rdsLists) {
            doGeyRdsOrderByName(rdsName, params, request, response);
        }
        return response;
    }

    private void doGeyRdsOrderByName(String rdsName, RdsParams params, QueryRdsOrderRequest request, SliceDownloadResponse<EshopOrderEntity> response) {
        try {
            DataSourceManager.setName(rdsName);
            Integer totalCount = taobaoRdsMapper.countRdsOrdersByCreateTime(request);
            if (response.getTotal() > totalCount) {
                return;
            }
            response.setTotal(totalCount);
            List<RdsOrderEntity> rdsOrderEntities = taobaoRdsMapper.queryRdsOrdersByCreateTime(request);
            if (CollectionUtils.isEmpty(rdsOrderEntities)) {
                return;
            }
            response.setList(buildOrdersFromRdsEntities(rdsOrderEntities, params, JsonUtils.toJson(request)));
            response.getList().sort(Comparator.comparing(EshopOrderEntity::getModifiedTime));
        } catch (Exception ex) {
            logger.info("账套{},网店{},淘宝通过RDS全量获取订单失败.错误原因:{}",
                    params.getParams().getProfileId(),
                    params.getParams().geteShopId(),
                    ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            restDataSource();
        }
    }

    private void doGeyRdsIncreaseOrderByName(String rdsName, RdsParams params, QueryRdsOrderRequest request, SliceDownloadResponse<EshopOrderEntity> response) {
        try {
            DataSourceManager.setName(rdsName);
            Integer totalCount = taobaoRdsMapper.countRdsOrdersByModifyTime(request);
            if (response.getTotal() > totalCount) {
                return;
            }
            response.setTotal(totalCount);
            List<RdsOrderEntity> rdsOrderEntities = taobaoRdsMapper.queryRdsOrdersByModifyTime(request);
            if (CollectionUtils.isEmpty(rdsOrderEntities)) {
                return;
            }
            response.setList(buildOrdersFromRdsEntities(rdsOrderEntities, params, JsonUtils.toJson(request)));
            response.getList().sort(Comparator.comparing(EshopOrderEntity::getModifiedTime));
        } catch (Exception ex) {
            logger.info("账套{},网店{},淘宝通过RDS全量获取订单失败.错误原因:{}",
                    params.getParams().getProfileId(),
                    params.getParams().geteShopId(),
                    ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            restDataSource();
        }
    }


    @Override
    public SliceDownloadResponse<EshopOrderEntity> downloadOrderByUpdateTime(DownloadSlice slice, RdsParams params) {
        QueryRdsOrderRequest request = buildQueryRequest(slice, params, QUERY_TYPE_ORDER);
        SliceDownloadResponse<EshopOrderEntity> response = new SliceDownloadResponse<>(slice);
        String[] rdsLists = getRdsLists();
        if (rdsLists.length == 0) {
            logger.info("账套{},网店{},淘宝通过RDS全量获取订单失败.错误原因:RDS配置为空，无法获取订单",
                    params.getParams().getProfileId(),
                    params.getParams().geteShopId());
            throw new RuntimeException("RDS配置为空，无法获取订单");
        }
        String name = params.getParams().getRdsName();
        if (StringUtils.isNotEmpty(name)) {
            doGeyRdsIncreaseOrderByName(name, params, request, response);
            return response;
        }

        for (String rdsName : rdsLists) {
            doGeyRdsIncreaseOrderByName(rdsName, params, request, response);
        }
        return response;
    }

    @Override
    public boolean checkRdsEnabled(String rdsName, String nickName) {
        try {
            DataSourceManager.setName(rdsName);
            Date date = DateUtils.getDate();
            Date begin = DateUtils.addDays(date, -30);
            QueryRdsOrderRequest request = new QueryRdsOrderRequest();
            request.setBeginTime(begin);
            request.setEndTime(date);
            request.setSellerNick(nickName);
            Integer integer = taobaoRdsMapper.countRdsOrdersByCreateTime(request);
            return integer != null && integer > 0;
        } catch (Exception ex) {
            logger.error("网店{}检查RDS报错{}", nickName, ex.getMessage(), ex);
            return false;
        } finally {
            restDataSource();
        }
    }

    private QueryRdsOrderRequest buildQueryRequest(DownloadSlice slice, RdsParams params, int queryType) {
        QueryRdsOrderRequest request = new QueryRdsOrderRequest();
        request.setBeginTime(slice.getStartTime());
        request.setEndTime(slice.getEndTime());
        request.setSellerNick(params.getParams().getShopAccount());
        //limit startIndex,pageSize
        request.setPageSize(slice.getPageSize());
        request.setStartIndex((slice.getPage() - 1) * slice.getPageSize());
        request.setCid(params.getSellerCid());
        if (QUERY_TYPE_ORDER == queryType) {
            String tradeStatus = TaobaoUtils.buildTaobaoOrderStatus(TradeStatus.valueOf(slice.getStatus()));
            request.setStatus(tradeStatus);
        } else if (QUERY_TYPE_PRODUCT == queryType) {
            if (slice.getStatus() == 1) {
                request.setStatus("onsale");
            } else if (slice.getStatus() == 2) {
                request.setStatus("instock");
            }
        }
        return request;
    }

    @Override
    public SliceDownloadResponse<EshopRefundOrderEntity> downloadRefundByCreateTime(DownloadSlice slice, RdsParams params) {
        QueryRdsOrderRequest request = buildQueryRequest(slice, params, QUERY_TYPE_REFUND);
        SliceDownloadResponse<EshopRefundOrderEntity> response = new SliceDownloadResponse<>(slice);
        try {
            String rdsName = params.getParams().getRdsName();
            if (StringUtils.isEmpty(rdsName)) {
                rdsName = rdsConfig.getTaobaoRdsName();
            }
            DataSourceManager.setName(rdsName);
            if (1 == slice.getPage()) {
                Integer totalCount = taobaoRdsMapper.countRdsRefundsByCreateTime(request);
                response.setTotal(totalCount);
            }
            List<RdsRefundEntity> rdsRefundEntities = taobaoRdsMapper.queryRdsRefundsByCreateTime(request);
            response.setList(buildRefundsFromRdsEntities(rdsRefundEntities, params));
            return response;
        } catch (Exception ex) {
            logger.error("账套{},网店{},淘宝通过RDS全量获取售后单失败.错误原因:{}",
                    params.getParams().getProfileId(),
                    params.getParams().geteShopId(),
                    ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            restDataSource();
        }
    }

    private String[] getRdsLists() {
        if (StringUtils.isEmpty(rdsConfig.getTaobaoRdsNameList())) {
            return new String[]{rdsConfig.getTaobaoRdsName()};
        }
        return rdsConfig.getTaobaoRdsNameList().split(",");
    }

    private void restDataSource() {
        DataSourceManager.reset();
        RouteContext context = RouteThreadLocal.getRoute();
        if (context == null) {
            throw new GeneralException("获取路由信息失败,请重新登录");
        }
        DataSourceManager.setName(context.getServerId());
    }

    private List<EshopRefundOrderEntity> buildRefundsFromRdsEntities(List<RdsRefundEntity> rdsRefundEntities, RdsParams params) {
        if (rdsRefundEntities == null || rdsRefundEntities.isEmpty()) {
            return new ArrayList<>();
        }
        PlatformBaseConfig config = params.getPlatformBaseConfig();
        TaobaoClient taobaoClient = new DefaultTaobaoClient
                (config.getApiUrl(), config.getAppKey(), config.getAppSecret(),
                        config.getFormat(), config.getConnectTimeout(),
                        config.getReadTimeout(), config.getSignMethod());

        List<EshopRefundOrderEntity> refundOrderList = new ArrayList<>();
        TaobaoRefundBuilder refundBuilder = new TaobaoRefundBuilder(params.getParams(), params.getParams().getShopType());
        for (RdsRefundEntity rdsRefund : rdsRefundEntities) {
            try {
                RefundGetResponse rsp =
                        com.taobao.api.internal.util.TaobaoUtils.parseResponse(
                                rdsRefund.getResponse(), RefundGetResponse.class);
                if (rsp != null && rsp.getRefund() != null) {
                    String freightRefundNo = TaobaoUtils.getFreightRefundNo(rsp.getRefund());
                    Refund freightRefund = null;
                    if (StringUtils.isNotEmpty(freightRefundNo)) {
                        RefundGetRequest req = new RefundGetRequest();
                        try {
                            req.setRefundId(Long.parseLong(freightRefundNo));
                            RefundGetResponse freightRefundRsp = taobaoClient.execute(req);
                            freightRefund = freightRefundRsp.getRefund();
                        } catch (Exception ex) {
                            //获取退运费售后失败不影响正常流程
                        }
                    }
                    EshopRefundOrderEntity refundOrder = refundBuilder.buildEShopOrderRefund(rsp.getRefund(), freightRefund);
                    if (refundOrder != null) {
                        refundOrderList.add(refundOrder);
                    }
                    if (freightRefund != null) {
                        EshopRefundOrderEntity freightRefundOrder =
                                refundBuilder.buildEShopOrderRefund(freightRefund, null);
                        if (freightRefundOrder == null) {
                            continue;
                        }
                        refundOrderList.add(freightRefundOrder);
                    }
                }
            } catch (ApiException e) {
                logger.error("淘宝rds构建售后单失败:{}", e.getMessage(), e);
            }
        }
        return refundOrderList;
    }

    @Override
    public SliceDownloadResponse<EshopRefundOrderEntity> downloadRefundByUpdateTime(DownloadSlice slice, RdsParams params) {
        QueryRdsOrderRequest request = buildQueryRequest(slice, params, QUERY_TYPE_REFUND);
        SliceDownloadResponse<EshopRefundOrderEntity> response = new SliceDownloadResponse<>(slice);
        try {
            String rdsName = params.getParams().getRdsName();
            DataSourceManager.setName(rdsName);
            if (1 == slice.getPage()) {
                Integer totalCount = taobaoRdsMapper.countRdsRefundsByModifyTime(request);
                response.setTotal(totalCount);
            }
            List<RdsRefundEntity> rdsRefundEntities = taobaoRdsMapper.queryRdsRefundsByModifyTime(request);
            response.setList(buildRefundsFromRdsEntities(rdsRefundEntities, params));
            return response;
        } catch (Exception ex) {
            logger.error("账套{},网店{},淘宝通过RDS增量获取售后单失败.错误原因:{}",
                    params.getParams().getProfileId(),
                    params.getParams().geteShopId(),
                    ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            restDataSource();
        }
    }

    @Override
    public List<EshopRefundOrderEntity> downloadRefundByRefundId(RdsParams params, List<String> refundIds) {
        try {
            String shopAccount = params.getParams().getShopAccount();
            String rdsName = params.getParams().getRdsName();
            if (StringUtils.isEmpty(rdsName)) {
                rdsName = rdsConfig.getTaobaoRdsName();
            }
            DataSourceManager.setName(rdsName);
            List<RdsRefundEntity> rdsRefundEntities = taobaoRdsMapper.queryRdsRefundByRefundIds(shopAccount, refundIds);
            return buildRefundsFromRdsEntities(rdsRefundEntities, params);
        } catch (Exception ex) {
            String errorMsg = String.format("账套Id【%s】通过RDS根据售后单ID获取售后单报错,错误原因:【%s】", params.getParams().getProfileId(), ex.getMessage());
            throw new RuntimeException(errorMsg, ex);
        } finally {
            restDataSource();
        }
    }

    @Override
    public List<EshopRefundOrderEntity> downloadRefundByTradeId(RdsParams params, List<String> tradeIds) {
        try {
            String rdsName = params.getParams().getRdsName();
            if (StringUtils.isEmpty(rdsName)) {
                return new ArrayList<>();
            }
            DataSourceManager.setName(rdsName);
            List<RdsRefundEntity> rdsRefundEntities = taobaoRdsMapper.queryRdsRefundByTradeIds(params.getParams().getShopAccount(), tradeIds);
            return buildRefundsFromRdsEntities(rdsRefundEntities, params);
        } catch (Exception ex) {
            String errorMsg = String.format("账套Id【%s】通过RDS根据订单号获取售后单报错,错误原因:【%s】", params.getParams().getProfileId(), ex.getMessage());
            throw new RuntimeException(errorMsg, ex);
        } finally {
            restDataSource();
        }
    }

    @Override
    public SliceDownloadResponse<EshopProductEntity> downloadProductByCreateTime(DownloadSlice slice, RdsParams params) {
        return null;
    }

    @Override
    public SliceDownloadResponse<EshopProductEntity> downloadAllProducts(DownloadSlice slice, RdsParams params) {
        slice.setStartTime(null);
        slice.setEndTime(null);
        return downloadProducts(slice, params);
    }


    @Override
    public SliceDownloadResponse<EshopProductEntity> downloadProductByUpdateTime(DownloadSlice slice, RdsParams params) {
        return downloadProducts(slice, params);
    }

    private SliceDownloadResponse<EshopProductEntity> downloadProducts(DownloadSlice slice, RdsParams params) {
        QueryRdsOrderRequest request = buildQueryRequest(slice, params, QUERY_TYPE_PRODUCT);
        SliceDownloadResponse<EshopProductEntity> response = new SliceDownloadResponse<>(slice);
        String[] rdsLists = getRdsLists();
        if (rdsLists.length == 0) {
            return response;
        }
        try {
            for (String rdsName : rdsLists) {
                DataSourceManager.setName(rdsName);
                if (1 == slice.getPage()) {
                    Integer totalCount = taobaoRdsMapper.countRdsProducts(request);
                    //同时访问多个数据库,获取count最大的展示
                    if (totalCount > response.getTotal()) {
                        response.setTotal(totalCount);
                    }
                }
                List<RdsProductEntity> rdsProductEntities = taobaoRdsMapper.queryRdsProducts(request);
                buildRdsProducts(rdsProductEntities, params, response);
            }
        } catch (Exception ex) {
            logger.error("账套{},网店{},淘宝通过RDS获取商品失败.错误原因:{}",
                    params.getParams().getProfileId(),
                    params.getParams().geteShopId(),
                    ex.getMessage(), ex);
        } finally {
            restDataSource();
        }
        return response;
    }

    private void buildRdsProducts(List<RdsProductEntity> rdsProductEntities, RdsParams params,
                                  SliceDownloadResponse<EshopProductEntity> response) {
        if (rdsProductEntities == null || rdsProductEntities.isEmpty()) {
            return;
        }
        TaobaoProductBuilder productBuilder = new TaobaoProductBuilder(params.getParams(), params.getParams().getShopType());
        List<Item> itemList = new ArrayList<>();
        try {
            for (RdsProductEntity rdsProductEntity : rdsProductEntities) {
                ItemSellerGetResponse rsp = com.taobao.api.internal.util.TaobaoUtils.parseResponse(rdsProductEntity.getResponse()
                        , ItemSellerGetResponse.class);
                itemList.add(rsp.getItem());
            }
            List<EshopProductEntity> eshopProducts = productBuilder.buildProduct(itemList);
            response.getList().addAll(eshopProducts);
        } catch (Exception ex) {
            logger.error("淘宝rds构建商品失败:", ex);
        }
    }


    @Override
    public int queryRdsOrderCount(DownloadSlice slice, RdsParams params) {
        QueryRdsOrderRequest request = buildQueryRequest(slice, params, QUERY_TYPE_ORDER);
        int maxTotal = 0;
        try {
            String rdsName = params.getParams().getRdsName();
            DataSourceManager.setName(rdsName);
            Integer totalCount = taobaoRdsMapper.countRdsOrdersByCreateTime(request);
            if (totalCount > maxTotal) {
                maxTotal = totalCount;
            }
            return maxTotal;
        } catch (Exception ex) {
            logger.error("账套{},网店{},淘宝通过RDS全量获取订单失败.错误原因:{}",
                    params.getParams().getProfileId(),
                    params.getParams().geteShopId(),
                    ex.getMessage(), ex);
            return 0;
        } finally {
            restDataSource();
        }
    }

    @Override
    public List<EshopProductEntity> downloadProductsByNumIds(RdsParams params, List<String> numIds) {
        QueryRdsProductByIdRequest req = new QueryRdsProductByIdRequest();
        req.setSellerNick(params.getParams().getShopAccount());
        req.setNumIds(numIds);
        List<EshopProductEntity> eshopProductList = new ArrayList<>();
        try {
            String rdsName = params.getParams().getRdsName();
            DataSourceManager.setName(rdsName);
            List<RdsProductEntity> rdsProductEntities = taobaoRdsMapper.queryRdsProductsByNumIds(req);
            if (rdsProductEntities == null || rdsProductEntities.isEmpty()) {
                return new ArrayList<>();
            }
            TaobaoProductBuilder productBuilder = new TaobaoProductBuilder(params.getParams(), params.getParams().getShopType());
            List<Item> itemList = new ArrayList<>();
            for (RdsProductEntity rdsProductEntity : rdsProductEntities) {
                ItemSellerGetResponse rsp = com.taobao.api.internal.util.TaobaoUtils.parseResponse(rdsProductEntity.getResponse()
                        , ItemSellerGetResponse.class);
                itemList.add(rsp.getItem());
            }
            eshopProductList = productBuilder.buildProduct(itemList);

        } catch (Exception ex) {
            logger.error("淘宝rds构建商品失败:", ex);
        } finally {
            restDataSource();
        }
        return eshopProductList;
    }


    @Override
    public Map<String,Item> downloadItemsByNumIds(RdsParams params, List<String> numIds) {
        QueryRdsProductByIdRequest req = new QueryRdsProductByIdRequest();
        req.setSellerNick(params.getParams().getShopAccount());
        req.setNumIds(numIds);
        Map<String,Item> itemMap = new HashMap<>();
        try {
            String rdsName = params.getParams().getRdsName();
            //如果指定rds
            if(StringUtils.isNotEmpty(rdsName)){
                DataSourceManager.setName(rdsName);
                List<RdsProductEntity> rdsProductEntities = taobaoRdsMapper.queryRdsProductsByNumIds(req);
                buildProductItemMap(itemMap, rdsProductEntities);
                return itemMap;
            }

            String[] rdsLists = getRdsLists();
            for (String listName : rdsLists) {
                DataSourceManager.setName(listName);
                List<RdsProductEntity> rdsProductEntities = taobaoRdsMapper.queryRdsProductsByNumIds(req);
                buildProductItemMap(itemMap, rdsProductEntities);
            }
            return itemMap;
        } catch (Exception ex) {
            logger.error("淘宝rds获取商品失败:", ex);
            return new HashMap<>();
        } finally {
            restDataSource();
        }
    }

    private void buildProductItemMap(Map<String, Item> itemMap, List<RdsProductEntity> rdsProductEntities) throws ApiException {
        if(CollectionUtils.isEmpty(rdsProductEntities)){
            return;
        }
        for (RdsProductEntity rdsProductEntity : rdsProductEntities) {
            ItemSellerGetResponse rsp = com.taobao.api.internal.util.TaobaoUtils.parseResponse(rdsProductEntity.getResponse()
                    , ItemSellerGetResponse.class);
            if(rsp!=null && rsp.getItem()!=null && rsp.getItem().getNumIid()!=null){
                //获取时间更新的记录
                if(!itemMap.containsKey(rsp.getItem().getNumIid().toString()) ||
                        itemMap.get(rsp.getItem().getNumIid().toString()).getModified().before(rsp.getItem().getModified())){
                    itemMap.put(rsp.getItem().getNumIid().toString(),rsp.getItem());
                }
            }
        }
    }
}
