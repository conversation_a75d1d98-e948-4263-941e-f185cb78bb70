package com.wsgjp.ct.sale.platform.sdk.entity.send;

import com.wsgjp.ct.common.enums.core.enums.SyncFreightStatus;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class UpdateSyncStateBill {
    private BigInteger vchcode;
    private BigInteger taskId;
    private SyncFreightStatus syncState;
    private String message;
    private List<UpdateSyncStateBillDetail> details;

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public BigInteger getTaskId() {
        return taskId;
    }

    public void setTaskId(BigInteger taskId) {
        this.taskId = taskId;
    }

    public SyncFreightStatus getSyncState() {
        return syncState;
    }

    public void setSyncState(SyncFreightStatus syncState) {
        this.syncState = syncState;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<UpdateSyncStateBillDetail> getDetails() {
        return details;
    }

    public void setDetails(List<UpdateSyncStateBillDetail> details) {
        this.details = details;
    }
}
