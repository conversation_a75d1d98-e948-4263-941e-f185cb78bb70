package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.response.staff.QueryStaffInfosResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.staff.EshopQueryStaffInfosFeature;
import com.wsgjp.ct.sale.platform.sdk.service.EshopStaffDownloadService;
import org.springframework.stereotype.Service;


/**
 * 员工/职员下载
 * <AUTHOR>
 */
@Service
public class EshopStaffDownloadServiceImpl implements EshopStaffDownloadService {
    @Override
    public QueryStaffInfosResponse queryStaffInfos(BaseRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryStaffInfosFeature feature = factory.getFeature(EshopQueryStaffInfosFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持下载员工信息", request.getSystemParams().getShopType().getPlatformName()));
        }
        return feature.queryStaffInfos(request);
    }
}
