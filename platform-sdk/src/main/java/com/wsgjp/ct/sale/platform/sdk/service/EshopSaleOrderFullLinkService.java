package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.common.entity.log.LogOrder;
import com.wsgjp.ct.sale.common.entity.log.OrderLinkStatusEnum;
import com.wsgjp.ct.sale.common.enums.OperationEnum;

import java.math.BigInteger;
import java.util.List;

/**
 * @Author: wcy
 * @Date: 2023/02/01/9:36
 * @Description:全链路埋点日志接口
 */
public interface EshopSaleOrderFullLinkService {


    /**
     * 全链路埋点日志
     *
     * @param profileId     账套id
     * @param eshopId       网店id
     * @param tradeOrderIds 订单编号
     * @param optionStatus  回传状态
     */
    void fullLinkCollect(BigInteger profileId, BigInteger eshopId, List<String> tradeOrderIds, OrderLinkStatusEnum optionStatus);

    /**
     * 批量订单操作日志上报、比如查询订单、打印订单
     * @param orders 订单列表
     * @param operation 操作
     */
    void feedback(List<LogOrder> orders, OperationEnum operation);
}
