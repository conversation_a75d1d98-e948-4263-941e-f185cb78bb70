package com.wsgjp.ct.sale.platform.sdk.util;


import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.common.enums.core.enums.eshopconfig.PlatformEshopConfigEnum;
import com.wsgjp.ct.sale.platform.config.EshopRdsConfig;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.constraint.PlatformConstants;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.sdk.config.PlatformBizConfig;
import com.wsgjp.ct.sale.platform.sdk.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.redis.factory.CacheType;
import ngp.redis.RedisPoolFactory;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class PlatformCommonUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlatformCommonUtil.class);

    public static String toSafeString(String str, int length) {
        try {
            if (StringUtils.isEmpty(str) || str.length() <= length) {
                return str;
            }
            return str.substring(0, length);
        } catch (RuntimeException ex) {
            LOGGER.error("截取字符串失败" + ex.getMessage(), ex);
            return str;
        }
    }


    public static EshopSystemParams toSystemParams(EshopInfo eshopInfo) {
        EshopSystemParams eshopSystemParams = new EshopSystemParams();
        eshopSystemParams.seteShopId(eshopInfo.getOtypeId());
        eshopSystemParams.setShopType(eshopInfo.getEshopType());
        eshopSystemParams.setProfileId(eshopInfo.getProfileId());
        eshopSystemParams.setFullName(eshopInfo.getFullname());
        eshopSystemParams.setShopAccount(eshopInfo.getEshopAccount());
        eshopSystemParams.setOnlineShopId(eshopInfo.getOnlineEshopId());
        eshopSystemParams.setExpiresIn(eshopInfo.getTokenExpireIn());
        eshopSystemParams.setRefreshToken(eshopInfo.getDecryptRefreshToken());
        eshopSystemParams.setReExpiresIn(eshopInfo.getRefreshTokenExpireIn());
        eshopSystemParams.setR1ExpiresIn(eshopInfo.getTokenR1ExpireIn());
        eshopSystemParams.setSpid(eshopInfo.getVendorId());
        eshopSystemParams.setAppKey(eshopInfo.getDecryptAppKey());
        eshopSystemParams.setAppSecret(eshopInfo.getDecryptAppSecret());
        eshopSystemParams.setToken(eshopInfo.getDecryptToken());
        eshopSystemParams.setMutiSelectAppkey(eshopInfo.getMutiSelectAppkey());
        eshopSystemParams.setPlatformEshopSnType(eshopInfo.getPlatformEshopSnType());
        eshopSystemParams.setTokenIsExpired(eshopInfo.isHasTokenExpired());
        eshopSystemParams.setRdsEnabled(eshopInfo.isRdsEnabled());
        eshopSystemParams.setRefreshTime(eshopInfo.getRefreshTime());
        eshopSystemParams.setRdsApplyTime(eshopInfo.getRdsApplyTime());
        eshopSystemParams.setPlatformEshopId(eshopInfo.getPlatformEshopId());
        eshopSystemParams.setRdsCheckTime(eshopInfo.getRdsCheckTime());
        eshopSystemParams.setRdsReadyTime(eshopInfo.getRdsReadyTime());
        eshopSystemParams.setRdsName(eshopInfo.getRdsName());
        eshopSystemParams.setSkuMemoDesired(eshopInfo.isSkuMemoDesired());
        eshopSystemParams.setCreateTime(eshopInfo.getCreateTime());
        eshopSystemParams.setMainEshop(eshopInfo.isMainEshop());
        eshopSystemParams.setGroupId(eshopInfo.getGroupId());
        eshopSystemParams.setAuthCheckRequired(true);
        eshopSystemParams.setOpenTmc(eshopInfo.getTmcEnabled());
        eshopSystemParams.setDownloadOrderType(eshopInfo.getDownloadOrderType());
        //doRdsRegister(eshopSystemParams);
        //0-下载普通订单 1-下载家装分销订单 2-下载全部类型订单
        switch (eshopInfo.getDownloadOrderType()) {
            case 1:
                eshopSystemParams.setNeedDownloadNormalOrder(false);
                eshopSystemParams.setNeedDownloadJzfx(true);
                break;
            case 2:
                eshopSystemParams.setNeedDownloadNormalOrder(true);
                eshopSystemParams.setNeedDownloadJzfx(true);
                break;
            case 0:
            default:
                eshopSystemParams.setNeedDownloadNormalOrder(true);
                eshopSystemParams.setNeedDownloadJzfx(false);
                break;
        }
        doResetRdsEnabled(eshopSystemParams);
        doSetStockLockLv(eshopSystemParams);
        doBuildEshopConfig(eshopSystemParams, eshopInfo);
        doSetDouDianAuthCloseState(eshopSystemParams);
        String profileData = GlobalConfig.get(PlatformConstants.PROFILE_WRITE_API_LOG_METHODS);
        if (StringUtils.isNotEmpty(profileData)) {
            eshopSystemParams.setEshopApiLogMethods(profileData);
        }
        return eshopSystemParams;
    }

    private static void doSetDouDianAuthCloseState(EshopSystemParams systemParams) {
        if (!systemParams.getShopType().equals(ShopType.Doudian) && !systemParams.getShopType().equals(ShopType.DouDianInstantShopping)) {
            return;
        }
//        String key = "dd_auth_lock_" + systemParams.geteShopId();
//        boolean b = Objects.equals(GlobalConfig.get(key, "0"), "1");
        systemParams.setDdAuthClosed(systemParams.isTokenIsExpired());
    }

    private static void doBuildEshopConfig(EshopSystemParams eshopSystemParams, EshopInfo eshopInfo) {
        BigInteger platfromConfig = eshopInfo.getPlatfromConfig();
        if (platfromConfig == null || platfromConfig.compareTo(BigInteger.ZERO) == 0) {
            return;
        }
        long longValue = platfromConfig.longValue();
        long code = PlatformEshopConfigEnum.SPLIT_SEND_ORDER_BY_FIRST.getCodeValue();
        /**
         * 多包裹/多子单同步线上发货开关(总开关)
         */
        long splitSendEnabledCode = PlatformEshopConfigEnum.SPLIT_SEND_ORDER_ENABLED.getCodeValue();
        if ((longValue & splitSendEnabledCode) == splitSendEnabledCode && (longValue & code) == code) {
            eshopSystemParams.setSplitSendByFirstEnabled(true);
        }

        if ((longValue & PlatformEshopConfigEnum.SPLIT_SEND_WHEN_LAST_SYNC.getCodeValue())
                == PlatformEshopConfigEnum.SPLIT_SEND_WHEN_LAST_SYNC.getCodeValue() && (longValue & code) == code) {
            eshopSystemParams.setSplitSendWhenLastSync(true);
        }

        if ((longValue & PlatformEshopConfigEnum.SPEC_UNIT_DOWNLOAD.getCodeValue()) == PlatformEshopConfigEnum.SPEC_UNIT_DOWNLOAD.getCodeValue()) {
            eshopSystemParams.setSpecUnitDownloadEnabled(true);
        }
    }

    public static void doResetRdsEnabled(EshopSystemParams eshopSystemParams) {
        Date readyTime = eshopSystemParams.getRdsReadyTime();
        Date applyTime = eshopSystemParams.getRdsApplyTime();
        ShopType shopType = eshopSystemParams.getShopType();
        if (!eshopSystemParams.getRdsEnabled()) {
            return;
        }
        if (shopType.equals(ShopType.TaoBao) || shopType.equals(ShopType.Tmall)) {
            if (StringUtils.isEmpty(eshopSystemParams.getRdsName())) {
                EshopRdsConfig rdsConfig = BeanUtils.getBean(EshopRdsConfig.class);
                String realRdsName = getRealRdsName(rdsConfig);
                eshopSystemParams.setRdsName(realRdsName);
            }
        }
        if (readyTime == null) {
            if (applyTime != null) {
                PlatformBaseConfig config = EshopFactoryManager.getConfig(shopType);
                if (config != null) {
                    int rdsHoldTime = config.getRdsHoldTime();
                    Date nextTime = DateUtils.addDays(applyTime, rdsHoldTime);
                    eshopSystemParams.setRdsReadyTime(nextTime);
                    if (nextTime.before(DateUtils.getDate())) {
                        eshopSystemParams.setRdsEnabled(true);
                        return;
                    }
                }
            }
            eshopSystemParams.setRdsEnabled(false);
        } else {
            Date nowDate = DateUtils.getDate();
            if (readyTime.before(nowDate)) {
                return;
            }
            eshopSystemParams.setRdsEnabled(false);
        }
    }

    private static void doSetStockLockLv(EshopSystemParams eshopSystemParams) {
        RedisPoolFactory redisPoolFactory = BeanUtils.getBean(RedisPoolFactory.class);
        String key = CurrentUser.getProfileId() + "_" + "stockLockLevel";
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String stockLockLevel = template.opsForValue().get(key);
        if (StringUtils.isEmpty(stockLockLevel)) {
            stockLockLevel = GlobalConfig.get("stockLockLevel", "1");
            PlatformBizConfig config = BeanUtils.getBean(PlatformBizConfig.class);
            template.opsForValue().set(key, stockLockLevel, config.getRedisTime() * 60L, TimeUnit.SECONDS);
        }
        eshopSystemParams.setStockLockLevel(stockLockLevel);
    }


    /**
     * @return 支持仓做业节点回传的平台列表
     * todo 之后可以做成配置
     */
    public static List<ShopType> getWarehouseBizFeedbackSupportedList() {
        List<ShopType> shopTypeList = new ArrayList<>();
        shopTypeList.add(ShopType.TaoBao);
        shopTypeList.add(ShopType.Tmall);
        shopTypeList.add(ShopType.DeWu);
        shopTypeList.add(ShopType.Alibaba);
        shopTypeList.add(ShopType.YaoBangMang);
        return shopTypeList;
    }

    public static String getRealRdsName(EshopRdsConfig rdsConfig) {
        String configVal = GlobalConfig.get(PlatformConstants.TB_APP_KEY_SUB_NAME);
        if (StringUtils.isEmpty(configVal)) {
            return rdsConfig.getTaobaoRdsName();
        }
        if (configVal.equals(PlatformConstants.TB_APP_KEY_FOR_ZYX)) {
            return rdsConfig.getTaobaoRdsNameForZyx();
        }
        if (configVal.equals(PlatformConstants.TB_APP_KEY_FOR_AN_DAN)) {
            return rdsConfig.getTaobaoRdsNameForByPay();
        }
        return rdsConfig.getTaobaoRdsName();
    }
}
