package com.wsgjp.ct.sale.platform.sdk.entity.eshop;

import java.math.BigInteger;

/**
 * <AUTHOR> 2023/12/8 11:27
 */
public class PlatformEshopRegisterInfo {
    private BigInteger id = BigInteger.ZERO;
    private BigInteger profileId = BigInteger.ZERO;
    private BigInteger eshopId = BigInteger.ZERO;
    private int productId = 0;
    private int partition = 0;
    private String fullname = "";
    private String shopAccount = "";
    private int shopType = 0;
    private int state = 0;
    private BigInteger serverId = BigInteger.ZERO;
    private String token = "";
    private String platformAttr = "";

    private String platformShopId = "";

    private boolean tmcEnabled;
    private boolean autoSyncOrderEnabled;
    private boolean rdsEnabled;

    public boolean isTmcEnabled() {
        return tmcEnabled;
    }

    public void setTmcEnabled(boolean tmcEnabled) {
        this.tmcEnabled = tmcEnabled;
    }

    public boolean isAutoSyncOrderEnabled() {
        return autoSyncOrderEnabled;
    }

    public void setAutoSyncOrderEnabled(boolean autoSyncOrderEnabled) {
        this.autoSyncOrderEnabled = autoSyncOrderEnabled;
    }

    public boolean isRdsEnabled() {
        return rdsEnabled;
    }

    public void setRdsEnabled(boolean rdsEnabled) {
        this.rdsEnabled = rdsEnabled;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public int getPartition() {
        return partition;
    }

    public void setPartition(int partition) {
        this.partition = partition;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getShopAccount() {
        return shopAccount;
    }

    public void setShopAccount(String shopAccount) {
        this.shopAccount = shopAccount;
    }

    public int getShopType() {
        return shopType;
    }

    public void setShopType(int shopType) {
        this.shopType = shopType;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public BigInteger getServerId() {
        return serverId;
    }

    public void setServerId(BigInteger serverId) {
        this.serverId = serverId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPlatformAttr() {
        return platformAttr;
    }

    public void setPlatformAttr(String platformAttr) {
        this.platformAttr = platformAttr;
    }

    public String getPlatformShopId() {
        return platformShopId;
    }

    public void setPlatformShopId(String platformShopId) {
        this.platformShopId = platformShopId;
    }
}
