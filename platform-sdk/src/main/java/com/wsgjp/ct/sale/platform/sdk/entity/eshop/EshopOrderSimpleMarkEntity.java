package com.wsgjp.ct.sale.platform.sdk.entity.eshop;


import com.wsgjp.ct.common.enums.core.enums.SelfDeliveryModeEnum;

/**
 * <AUTHOR>
 */
public class EshopOrderSimpleMarkEntity {
    /**
     * 订单号
     */
    private String tradeId;
    /**
     * 平台标记code
     */
    private Integer markCode;

    private String stockId;

    /**
     * 携带数据的标签数据
     */
    private String bigData;
    /**
     * 交互方式
     */

    private SelfDeliveryModeEnum selfDeliveryMode;
    private String bubble;

    public String getStockId() {
        return stockId;
    }

    public void setStockId(String stockId) {
        this.stockId = stockId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public Integer getMarkCode() {
        return markCode;
    }

    public void setMarkCode(Integer markCode) {
        this.markCode = markCode;
    }

    public String getBigData() {
        return bigData;
    }

    public void setBigData(String bigData) {
        this.bigData = bigData;
    }

    public String getBubble() {
        return bubble;
    }

    public void setBubble(String bubble) {
        this.bubble = bubble;
    }

    public SelfDeliveryModeEnum getSelfDeliveryMode() {
        return selfDeliveryMode;
    }

    public void setSelfDeliveryMode(SelfDeliveryModeEnum selfDeliveryMode) {
        this.selfDeliveryMode = selfDeliveryMode;
    }
}
