package com.wsgjp.ct.sale.platform.sdk.mocker;

import bf.datasource.DataSourceManager;
import com.wsgjp.ct.sale.platform.entity.request.mock.QueryDataListParam;
import com.wsgjp.ct.sale.platform.enums.MockQueryType;
import com.wsgjp.ct.sale.platform.mock.ApiMockQueryService;
import com.wsgjp.ct.sale.platform.sdk.mapper.MockMapper;
import com.wsgjp.ct.sale.platform.utils.MockUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2024/4/11 15:05
 */

public class ApiMockerServiceImpl implements ApiMockQueryService {

    private final MockMapper mapper;

    private static final Logger logger = LoggerFactory.getLogger(ApiMockerServiceImpl.class);

    public ApiMockerServiceImpl(MockMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public String queryTradeByTradeId(String shopAccount, String tradeId) {
        try {
            DataSourceManager.setName("log-0");
            return mapper.queryTradeByTradeId(shopAccount, tradeId);
        }catch (Exception ex){
            logger.error("查询mock数据报错:{}",ex.getMessage(), ex);
            return  "";
        }finally {
            MockUtils.restDataSource();
        }
    }

    @Override
    public List<String> queryDataList(QueryDataListParam param) {

        try {
            DataSourceManager.setName("log-0");
            if(param.getType().equals(MockQueryType.Refund)) {
                return mapper.queryRefundDataList(param);
            }
            if(param.getType().equals(MockQueryType.Order)){
                return mapper.queryTradeList(param);
            }
            return new ArrayList<>();
        }catch (Exception ex){
            logger.error("查询mock数据报错:{}",ex.getMessage(), ex);
            return  new ArrayList<>();
        }finally {
            MockUtils.restDataSource();
        }
    }

    @Override
    public long queryDataCount(QueryDataListParam param) {
        try {
            DataSourceManager.setName("log-0");
            if(param.getType().equals(MockQueryType.Refund)){
                return mapper.queryRefundDataCount(param);
            }
            if(param.getType().equals(MockQueryType.Order)){
                return mapper.queryTradeCount(param);
            }
            return 0;
        }catch (Exception ex){
            logger.error("查询mock数据报错:{}",ex.getMessage(), ex);
            return  0;
        }finally {
            MockUtils.restDataSource();
        }
    }

    @Override
    public String queryRefundByRefundId(String shopAccount, String refundId) {
        try {
            DataSourceManager.setName("log-0");
            return mapper.queryRefundData(shopAccount, refundId);
        }catch (Exception ex){
            logger.error("查询mock数据报错:{}",ex.getMessage(), ex);
            return  "";
        }finally {
            MockUtils.restDataSource();
        }
    }

    @Override
    public String queryRefundByTradeId(String shopAccount, String tradeId) {
        return null;
    }
}
