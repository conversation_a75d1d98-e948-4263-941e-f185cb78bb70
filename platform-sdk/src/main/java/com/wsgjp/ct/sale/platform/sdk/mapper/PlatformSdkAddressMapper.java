package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.entity.request.sendgoods.AddressInfo;
import com.wsgjp.ct.sale.platform.sdk.entity.EshopSenderInfo;
import com.wsgjp.ct.sale.platform.sdk.entity.Sender;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

@Mapper
@Repository
public interface PlatformSdkAddressMapper {
    AddressInfo getOnlineReturnAddress(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);
    List<AddressInfo> getOnlineReturnAddressList(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId);

    EshopSenderInfo getSenderById(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    Sender getSenderByOtypeId(@Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId);

    List<Sender> listSender(@Param("profileId") BigInteger profileId, @Param("vchcodes") List<BigInteger> vchcodes, @Param("isQueryCold") boolean isQueryCold);

    Sender querySenderByWarehouseTaskId(@Param("profileId") BigInteger profileId,@Param("warehouseTaskId") BigInteger warehouseTaskId);
}
