package com.wsgjp.ct.sale.platform.sdk.slice;

import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.slice.DownloadSlice;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
public class SliceBundle<T> {
    private int total;
    private int downloaded;
    private boolean more;
    private List<DownloadSlice> sliceList;
    private List<SliceDownloadResponse<T>> responses;
    private CountDownLatch downLatch;
    private int sliceCount;
    private int downloadedSliceCount = 0;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public boolean isMore() {
        return more;
    }

    public void setMore(boolean more) {
        this.more = more;
    }

    public List<DownloadSlice> getSliceList() {
        return sliceList;
    }

    public void setSliceList(List<DownloadSlice> sliceList) {
        this.sliceList = sliceList;
    }

    public void initCountDownLatch() {
        sliceCount = sliceList.size() + responses.size();
        downLatch = new CountDownLatch(sliceCount);
    }

    private void checkCountDown() {
        if (downLatch == null) {
            throw new RuntimeException("请先调用initCountDownLatch初始化技术器，前提请确保所有分片已计算完成");
        }
    }

    public void count() {
        checkCountDown();
        downLatch.countDown();
    }

    public void await() throws InterruptedException {
        checkCountDown();
        downLatch.await();
    }

    public synchronized void addDownloadedSliceCount() {
        downloadedSliceCount++;
    }

    public List<SliceDownloadResponse<T>> getResponses() {
        return responses;
    }

    public void setResponses(List<SliceDownloadResponse<T>> responses) {
        this.responses = responses;
    }

    public int getDownloaded() {
        return downloaded;
    }

    public synchronized void addDownloaded(int downloaded) {

        this.downloaded += downloaded;
    }

    public double getDownloadPercent() {
        return downloadedSliceCount / (double) sliceCount * 100;
    }

    public int getDownloadedSliceCount() {
        return downloadedSliceCount;
    }
}
