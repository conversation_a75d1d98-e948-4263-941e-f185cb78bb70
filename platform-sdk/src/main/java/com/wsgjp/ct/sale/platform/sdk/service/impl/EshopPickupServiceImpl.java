package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.platform.entity.request.delivery.*;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.delivery.*;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.delivery.EshopPickupFeature;
import com.wsgjp.ct.sale.platform.sdk.service.EshopPickupService;
import org.springframework.stereotype.Service;

@Service
public class EshopPickupServiceImpl implements EshopPickupService {
    @Override
    public CreatePickupOrderResponse createPickupOrder(CreatePickupRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.createPickupOrder(request);
        } else {
            CreatePickupOrderResponse response = new CreatePickupOrderResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持创建揽收单!");
            return response;
        }
    }

    @Override
    public QueryPickupOrderResponse queryPickupOrder(QueryPickupOrderRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.queryPickupOrder(request);
        } else {
            QueryPickupOrderResponse response = new QueryPickupOrderResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持查询揽收单!");
            return response;
        }
    }

    @Override
    public CreatePdfResponse createShippingMarkPdf(CreateShippingMarkPdfRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.createShippingMarkPdf(request);
        } else {
            CreatePdfResponse response = new CreatePdfResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持打印箱唛!");
            return response;
        }
    }

    @Override
    public CreatePdfResponse createScItemBarcodePdf(CreateScItemBarcodeRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.createScItemBarcodePdf(request);
        } else {
            CreatePdfResponse response = new CreatePdfResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持打印货品标签!");
            return response;
        }
    }

    @Override
    public CreatePdfResponse createPickupShippingMarkPdf(CreatePickupShippingMarkPdfRequet request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.createPickupShippingMarkPdf(request);
        } else {
            CreatePdfResponse response = new CreatePdfResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持打印揽收面单!");
            return response;
        }
    }

    @Override
    public CreateFullCustodySelfDeliveryPdfResponse createFullCustodySelfDeliveryPdf(CreateFullCustodySelfDeliveryPdfRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.createFullCustodySelfDeliveryPdf(request);
        } else {
            CreateFullCustodySelfDeliveryPdfResponse response = new CreateFullCustodySelfDeliveryPdfResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持自行生成自送面单!");
            return response;
        }
    }

    @Override
    public CreatePdfResponse printFullCustodySelfDeliveryPdf(PrintFullCustodySelfDeliveryPdfRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.printFullCustodySelfDeliveryPdf(request);
        } else {
            CreatePdfResponse response = new CreatePdfResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持打印自送面单!");
            return response;
        }
    }

    @Override
    public BaseResponse cancelPickupOrder(CancelPickupOrderRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.cancelPickupOrder(request);
        } else {
            CreatePdfResponse response = new CreatePdfResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持取消揽收面单!");
            return response;
        }
    }

    @Override
    public BaseResponse updateFullCustodySelfDeliveryPdf(CreateFullCustodySelfDeliveryPdfRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.updateFullCustodySelfDeliveryPdf(request);
        } else {
            CreatePdfResponse response = new CreatePdfResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持修改自行发货信息!");
            return response;
        }
    }

    @Override
    public BaseResponse cancelSelfDelivery(CancelSelfDeliveryRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.cancelSelfDelivery(request);
        } else {
            CreatePdfResponse response = new CreatePdfResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持取消自送单!");
            return response;
        }
    }

    @Override
    public CreateCoErpDeliveryResponse createCoErpDelivery(CreateCoErpDeliveryRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.createCoErpDelivery(request);
        } else {
            CreateCoErpDeliveryResponse response = new CreateCoErpDeliveryResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持创建发货单!");
            return response;
        }
    }

    @Override
    public BaseResponse cancelCoErpDelivery(CancelCoErpDeliveryRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.cancelCoErpDelivery(request);
        } else {
            BaseResponse response = new BaseResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持取消发货单!");
            return response;
        }
    }

    @Override
    public QueryPickupAvailableDateResponse queryPickupAvailableDate(QueryPickupAvailableDateRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.queryPickupAvailableDate(request);
        } else {
            QueryPickupAvailableDateResponse response = new QueryPickupAvailableDateResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持查询揽收时间!");
            return response;
        }
    }

    @Override
    public QuerySelfDeliveryOrderResponse querySelfDeliveryNoByConsignOrderNo(QuerySelfDeliveryOrderRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPickupFeature feature = factory.getFeature(EshopPickupFeature.class);
        if (feature != null) {
            return feature.querySelfDeliveryNoByConsignOrderNo(request);
        } else {
            QuerySelfDeliveryOrderResponse response = new QuerySelfDeliveryOrderResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持查询发货单号!");
            return response;
        }
    }
}
