package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.CancelSendGoodsRequest;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.WriteoffOrderRequest;
import com.wsgjp.ct.sale.platform.entity.response.sendgoods.*;
import com.wsgjp.ct.sale.platform.sdk.callback.SendCallback;
import com.wsgjp.ct.sale.platform.sdk.entity.request.DeliveryEndConfirmRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.SendOrderRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.VirtualSendRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.send.SyncFreightBillNoRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.send.VirtualSendResponse;

import java.util.List;


/**
 * 定义发货标准流程
 */
public interface EshopSendService {
    /**
     * 取消发货
     *
     * @param request 取消发货参数
     * @return 取消结果
     */
    CancelSendGoodsResponse cancelSendGoods(CancelSendGoodsRequest request);

    /**
     * 发货接口
     */
    List<SyncFreightBillNoResponse> send(List<SyncFreightBillNoRequest> requestList, SendCallback sendCallBack);

    WriteoffOrderResponse writeoffOrders(WriteoffOrderRequest request);

    /**
     * 同步单号新接口
     *
     * @param request 发货参数
     * @return 同步单号结果
     */
    List<SyncFreightBillNoResponse> sendNew(SendOrderRequest request);

    /**
     * 确认配送送达接口
     */
    DeliveryEndConfirmResponse deliveryEndConfirm(DeliveryEndConfirmRequest request);
    /**
     * 虚拟发货
     * @param request 虚拟发货参数（订单号）
     * @return 发货结果
     */
    List<VirtualSendResponse> virtualSend(VirtualSendRequest request);

    List<SyncFreightBillNoResponse> sendWithProcessLogger(List<SyncFreightBillNoRequest> requestList,  RedisProcessMessage redisLogger, SendCallback sendCallBack);

    SendCheckResponse validateSendOrders(List<SyncFreightBillNoRequest> sendList);

    SendCheckResponse validatePrintOrders(List<SyncFreightBillNoRequest> sendList);
}
