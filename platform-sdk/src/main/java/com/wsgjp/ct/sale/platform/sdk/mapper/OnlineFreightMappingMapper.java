package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

@Repository
@Mapper
public interface OnlineFreightMappingMapper {

    //获取用户自定义映射配置
    List<FreightMapping> getFreightMappingFromUserConfig(@Param("profileId") BigInteger profileId, @Param("shopType") int shopType);
}
