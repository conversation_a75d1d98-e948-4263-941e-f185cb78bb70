package com.wsgjp.ct.sale.platform.sdk.entity.callback;

import com.wsgjp.ct.sale.platform.enums.SliceType;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SliceDownloadCallbackResult<T> extends CallbackResult {
    private List<T> list;
    /**
     * 本次业务下单请求总共订单数
     */
    private int total;
    /**
     * 累积下载了多少个订单
     */
    private int downloaded;
    /**
     * 下载的进度
     */
    private BigDecimal percent;
    private int page;
    /**
     * 是否存在游标下载的切片，即不完全知道总条数的情况
     */
    private boolean more;
    private SliceType type;

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getDownloaded() {
        return downloaded;
    }

    public void setDownloaded(int downloaded) {
        this.downloaded = downloaded;
    }

    public BigDecimal getPercent() {
        return percent;
    }

    public void setPercent(BigDecimal percent) {
        this.percent = percent;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public boolean isMore() {
        return more;
    }

    public void setMore(boolean more) {
        this.more = more;
    }

    public SliceType getType() {
        return type;
    }

    public void setType(SliceType type) {
        this.type = type;
    }
}
