package com.wsgjp.ct.sale.platform.sdk.entity.request;

import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
/**
 * <AUTHOR>
 */
public class OrderDownloadRequest extends TimeDownloadRequest<EshopOrderEntity> {
    private TradeStatus tradeStatus;

    private Boolean checkRdsWithApiEnabled = false;

    private String threadName;


    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public Boolean getCheckRdsWithApiEnabled() {
        return checkRdsWithApiEnabled;
    }

    public void setCheckRdsWithApiEnabled(Boolean checkRdsWithApiEnabled) {
        this.checkRdsWithApiEnabled = checkRdsWithApiEnabled;
    }

    public String getThreadName() {
        return threadName;
    }

    public void setThreadName(String threadName) {
        this.threadName = threadName;
    }
}
