package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.sdk.entity.PubCustomFiledConfigInfo;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PlatformPubCustomFieldConfigMapper {
    List<PubCustomFiledConfigInfo> queryCustomFieldConfig(@Param("profileId") BigInteger profileId,
                                                          @Param("subType") Integer subType,
                                                          @Param("displayName") String displayName);
}
