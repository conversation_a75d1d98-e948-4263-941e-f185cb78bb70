package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.entity.PlatformSimpleOrder;
import com.wsgjp.ct.sale.platform.entity.entities.PlatformOrderDetail;
import com.wsgjp.ct.sale.platform.entity.entities.QueryOrderParam;
import com.wsgjp.ct.sale.platform.factory.doudiansupermarket.entity.DetailByOrder;
import com.wsgjp.ct.sale.platform.factory.doudiansupermarket.entity.Product;
import com.wsgjp.ct.sale.platform.factory.kuaishou.entity.ProductSku;
import com.wsgjp.ct.sale.platform.factory.xiaohongshu.OrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * 查询商品信息
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PlatformSdkEshopProductMapper {
    /**
     * 查询商品sku信息
     *
     * @param profileId 账套id
     * @return 简单原始订单列表
     */
    String listSaleProductProperties(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("platformNumId")String platformNumId, @Param("platformSkuId")String platformSkuId, @Param("platformXcode") String platformXcode);

    OrderDetail listSaleProductSku(@Param("profileId")BigInteger profileId, @Param("eshopId")BigInteger eshopId, @Param("platformSkuId")String platformSkuId, @Param("platformBarcode")String platformBarcode);



    List<DetailByOrder> listSaleOrderDetailList(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("oid") String oid, @Param("platformNumId")String platformNumId, @Param("platformSkuId")String platformSkuId, @Param("platformXcode")String platformXcode, @Param("eshopOrderId")String eshopOrderId);
    String listSaleOrderByEShopOrderId(@Param("profileId")BigInteger profileId, @Param("eshopId")BigInteger eshopId, @Param("tradeOrderId")String tradeOrderId);

    PlatformOrderDetail listSaleOrderDetails(@Param("profileId")BigInteger profileId, @Param("eshopId")BigInteger eshopId, @Param("oid") String oid, @Param("platformNumId")String platformNumId, @Param("platformSkuId")String platformSkuId, @Param("platformXcode")String platformXcode, @Param("eshopOrderId")String eshopOrderId);
    Product listSaleProductPrice(@Param("profileId") BigInteger profileId, @Param("eshopId")BigInteger eshopId, @Param("platformNumId")String platformNumId, @Param("platformXcode")String platformXcode);
    PlatformSimpleOrder queryPlatformSimpleOrderInfo(@Param("queryOrderParam") QueryOrderParam queryOrderParam);

    List<ProductSku> productSkuListByProperties(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("platformNumId")String platformNumId, @Param("platformSkuId")String platformSkuId, @Param("platformXcode") String platformXcode);
}
