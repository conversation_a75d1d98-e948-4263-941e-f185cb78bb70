package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillCreateType;
import com.wsgjp.ct.bill.core.handle.entity.enums.SelfDeliveryModeEnum;
import com.wsgjp.ct.common.enums.core.entity.BaseMarkBigDataEntity;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.common.enums.core.enums.SyncFreightStatus;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.common.entity.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.common.entity.dto.DeliverFreightSimpleEntity;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.common.mapper.CommonBillDeliverDetailMapper;
import com.wsgjp.ct.sale.common.utils.FreightMappingCacheManger;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.platform.annotation.SyncRule;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.config.PlatformEshopConfig;
import com.wsgjp.ct.sale.platform.dto.sendgoods.DeliveryEndBillNoConfirmEntity;
import com.wsgjp.ct.sale.platform.dto.sendgoods.DeliveryEndBillNoDetailConfirmEntity;
import com.wsgjp.ct.sale.platform.dto.sendgoods.FreightBillNoSyncDetailEntity;
import com.wsgjp.ct.sale.platform.dto.sendgoods.FreightBillNoSyncEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecord;
import com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecordDetail;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.*;
import com.wsgjp.ct.sale.platform.entity.response.order.CreateShipOrderResponse;
import com.wsgjp.ct.sale.platform.entity.response.sendgoods.*;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.enums.SendCheckCodeEnum;
import com.wsgjp.ct.sale.platform.enums.SyncType;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.delivery.EshopDeliveryEndConfirmFeature;
import com.wsgjp.ct.sale.platform.feature.send.*;
import com.wsgjp.ct.sale.platform.sdk.callback.SendCallback;
import com.wsgjp.ct.sale.platform.sdk.config.PlatformBizConfig;
import com.wsgjp.ct.sale.platform.sdk.entity.*;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopOrderSimpleMarkEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderDetailSimpleEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderSimpleEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.request.DeliveryEndConfirmRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.SendOrderRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.VirtualSendRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.send.*;
import com.wsgjp.ct.sale.platform.sdk.exception.SyncCheckException;
import com.wsgjp.ct.sale.platform.sdk.mapper.*;
import com.wsgjp.ct.sale.platform.sdk.service.EshopSaleOrderFullLinkService;
import com.wsgjp.ct.sale.platform.sdk.service.EshopSendService;
import com.wsgjp.ct.sale.platform.sdk.util.PlatformCommonUtil;
import com.wsgjp.ct.sale.platform.sdk.util.SendOrderUtil;
import com.wsgjp.ct.sale.platform.utils.ConfirmUtils;
import com.wsgjp.ct.sale.platform.utils.SendUtils;
import com.wsgjp.ct.support.business.Money;
import com.wsgjp.ct.support.business.MoneyUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.redis.factory.CacheType;
import ngp.loadbalancer.context.RouteContext;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.redis.RedisPoolFactory;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class EshopSendServiceImpl implements EshopSendService {
    protected static final Logger logger = LoggerFactory.getLogger(EshopSendServiceImpl.class);
    protected final PlatformBizConfig sendGoodsConfig;
    protected final PlatformSdkEshopMapper bifrostEshopMapper;
    protected final PlatformSdkEshopFreightSyncRecordMapper freightSyncRecordMapper;
    protected final PlatformSdkEshopFreightSyncRecordDetailMapper freightSyncRecordDetailMapper;
    protected final PlatformSdkEshopOrderMapper eshopOrderMapper;
    protected final PlatformPubCustomFieldConfigMapper customFieldConfigMapper;
    protected final PubCustomFieldBaseInfoMapper customFieldBaseInfoMapper;
    protected final PlatformSdkAddressMapper addressMapper;
    protected final EshopSaleOrderFullLinkService eshopSaleOrderFullLinkService;
    protected final PlatformEshopConfig platformEshopConfig;
    protected final CommonBillDeliverDetailMapper detailMapper;
    protected final PlatformSdkEmployeeMapper employeeMapper;
    protected final RedisPoolFactory redisPoolFactory;
    protected final MonitorService monitorService;
    protected final EshopNotifyMapper notifyMapper;

    protected final OnlineFreightMappingMapper onlineFreightMappingMapper;
    protected final PlatformSdkEshopBaseInfoMapper eshopBaseInfoMapper;

    protected final static String EMPTY = "";

    public EshopSendServiceImpl(PlatformBizConfig sendGoodsConfig,
                                PlatformSdkEshopMapper bifrostEshopMapper,
                                PlatformSdkEshopFreightSyncRecordMapper freightSyncRecordMapper,
                                PlatformSdkEshopFreightSyncRecordDetailMapper freightSyncRecordDetailMapper,
                                PlatformSdkEshopOrderMapper eshopOrderMapper,
                                PlatformSdkAddressMapper addressMapper,
                                EshopSaleOrderFullLinkService eshopSaleOrderFullLinkService,
                                PlatformEshopConfig platformEshopConfig,
                                CommonBillDeliverDetailMapper detailMapper,
                                RedisPoolFactory redisPoolFactory,
                                MonitorService monitorService,
                                PlatformSdkEmployeeMapper employeeMapper,
                                EshopNotifyMapper notifyMapper,
                                PlatformPubCustomFieldConfigMapper customFieldConfigMapper,
                                PubCustomFieldBaseInfoMapper customFieldBaseInfoMapper,
                                PlatformSdkEshopBaseInfoMapper eshopBaseInfoMapper,
                                OnlineFreightMappingMapper onlineFreightMappingMapper) {
        this.sendGoodsConfig = sendGoodsConfig;
        this.customFieldConfigMapper = customFieldConfigMapper;
        this.customFieldBaseInfoMapper = customFieldBaseInfoMapper;
        this.addressMapper = addressMapper;
        this.bifrostEshopMapper = bifrostEshopMapper;
        this.freightSyncRecordMapper = freightSyncRecordMapper;
        this.freightSyncRecordDetailMapper = freightSyncRecordDetailMapper;
        this.eshopOrderMapper = eshopOrderMapper;
        this.eshopSaleOrderFullLinkService = eshopSaleOrderFullLinkService;
        this.platformEshopConfig = platformEshopConfig;
        this.detailMapper = detailMapper;
        this.employeeMapper = employeeMapper;
        this.redisPoolFactory = redisPoolFactory;
        this.monitorService = monitorService;
        this.notifyMapper = notifyMapper;
        this.eshopBaseInfoMapper = eshopBaseInfoMapper;
        this.onlineFreightMappingMapper = onlineFreightMappingMapper;
    }

    @Override
    public CancelSendGoodsResponse cancelSendGoods(CancelSendGoodsRequest request) {
        if (CollectionUtils.isEmpty(request.getCancelSendGoodsEntityList())) {
            throw new RuntimeException("没有取消发货信息，请确认数据是否正确！");
        }

        CancelSendGoodsResponse response = new CancelSendGoodsResponse();
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopOrderCancelSendGoodsFeature cancelSendFeature = factory.getFeature(EshopOrderCancelSendGoodsFeature.class);
        if (cancelSendFeature != null) {
            List<CancelSendGoodsResult> cancelSendGoodsResults =
                    cancelSendFeature.cancelSendGoods(request.getCancelSendGoodsEntityList());
            response.setErrorList(cancelSendGoodsResults);
            response.setSuccess(true);
            //如果有失败的明细,标记任务为失败
            for (CancelSendGoodsResult result : cancelSendGoodsResults) {
                if (!result.isSuccess()) {
                    response.setSuccess(false);
                    response.setMessage(result.getMessage());
                    break;
                }
            }
        } else {
            response.setSuccess(false);
            response.setMessage("平台不支持取消发货!");
        }
        return response;
    }

    @Override
    public WriteoffOrderResponse writeoffOrders(WriteoffOrderRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopSellerWriteoffFeature writeoffFeature = factory.getFeature(EshopSellerWriteoffFeature.class);
        if (writeoffFeature != null) {
            return writeoffFeature.writeoffOrders(request);
        } else {
            WriteoffOrderResponse response = new WriteoffOrderResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持订单核销!");
            return response;
        }
    }

    @Override
    public List<VirtualSendResponse> virtualSend(VirtualSendRequest request) {
        try {
            if (request.getProfileId() == null) {
                throw new RuntimeException("账套ID不能为空！");
            }
            if (request.getEshopId() == null) {
                throw new RuntimeException("网店ID不能为空！");
            }
            EshopInfo eshopInfo = bifrostEshopMapper.getEshopInfoByShopId(request.getProfileId(), request.getEshopId());
            if (eshopInfo == null) {
                throw new RuntimeException("网店信息不存在！");
            }
            ShopType eshopType = eshopInfo.getEshopType();
            EshopSystemParams systemParams = PlatformCommonUtil.toSystemParams(eshopInfo);
            EshopFactory factory = EshopFactoryManager.create(eshopType, systemParams);
            EshopOrderVirtualSendFeature feature = factory.getFeature(EshopOrderVirtualSendFeature.class);
            if (feature == null) {
                throw new RuntimeException(String.format("平台【%s】不支持虚拟发货", eshopType.getName()));
            }
            List<FreightBillNoSyncEntity> syncEntityList = SendOrderUtil.buildVirtualSendParam(request);
            List<SyncFreightBillNoResultDetail> results = feature.virtualSend(syncEntityList);
            List<EshopFreightSyncRecord> recordList = SendOrderUtil.initVirtualSendSyncRecord(results);
            if (CollectionUtils.isNotEmpty(recordList)) {
                freightSyncRecordMapper.add(recordList);
            }
            return SendOrderUtil.buildVirtualSendResponse(results);
        } catch (Exception ex) {
            throw new RuntimeException("虚拟发货失败:" + ex.getMessage(), ex);
        }
    }


    @Override
    public List<SyncFreightBillNoResponse> sendNew(SendOrderRequest request) {
        throw new RuntimeException("请使用send");
    }


    protected void setEmployeeName() {
        try {
            Employee employee = employeeMapper.getEmployee(CurrentUser.getEmployeeId());
            if (employee != null && StringUtils.isNotBlank(employee.getFullname())) {
                RouteContext context = RouteThreadLocal.getRoute();
                HashMap<String, Object> extendProperties = context.getExtendProperties();
                extendProperties.put("etypeName", employee.getFullname());
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    @Override
    public DeliveryEndConfirmResponse deliveryEndConfirm(DeliveryEndConfirmRequest request) {
        List<DeliveryEndConfirmResult> results = new CopyOnWriteArrayList<>();
        List<ConfirmDeliveryEndBillNoRequest> filterRequests =
                filterDeliveryEndConfirmBillNo(request.getDeliveryEndConfirmBillNoEntities(), results);
        if (CollectionUtils.isEmpty(filterRequests)) {
            throw new RuntimeException("没有需要确认送达的订单");
        }
        setEmployeeName();
        Map<BigInteger, List<ConfirmDeliveryEndBillNoRequest>> groupRequestList =
                filterRequests.stream().collect(Collectors.groupingBy(ConfirmDeliveryEndBillNoRequest::getOtypeId));
        for (Map.Entry<BigInteger, List<ConfirmDeliveryEndBillNoRequest>> groupRequestEntry :
                groupRequestList.entrySet()) {
            List<ConfirmDeliveryEndBillNoRequest> confirmBillNoList = groupRequestEntry.getValue();
            try {
                EshopInfo eshopInfo = bifrostEshopMapper.getEshopInfoByShopId(CurrentUser.getProfileId(),
                        groupRequestEntry.getKey());
                EshopSystemParams params = PlatformCommonUtil.toSystemParams(eshopInfo);
                EshopFactory factory = EshopFactoryManager.create(eshopInfo.getEshopType(), params);
                buildAndConfirm(factory, confirmBillNoList, results);
            } catch (Exception ex) {
                logger.error("确认送的出错,错误原因:{}", ex.getMessage(), ex);
                results.addAll(confirmBillNoList.stream().map(x -> buildResponse(x, ex)).collect(Collectors.toList()));
            }
        }
        DeliveryEndConfirmResponse response = new DeliveryEndConfirmResponse();
        response.setDeliveryEndConfirmResults(results);
        return response;
    }

    private List<ConfirmDeliveryEndBillNoRequest> filterDeliveryEndConfirmBillNo(List<ConfirmDeliveryEndBillNoRequest> requests, List<DeliveryEndConfirmResult> errorResults) {
        List<ConfirmDeliveryEndBillNoRequest> filterRequests = new ArrayList<>();
        for (ConfirmDeliveryEndBillNoRequest request : requests) {
            if (!request.getCreateType().equals(BillCreateType.FROM_DOWNLOAD_ORDER) &&
                    !request.getCreateType().equals(BillCreateType.FROM_ORDER_REFUND)) {
                DeliveryEndConfirmResult result = new DeliveryEndConfirmResult();
                result.setDeliverOrderId(request.getDeliverOrderId());
                result.setWarehouseTaskId(request.getWarehouseTaskId());
                result.setSuccess(true);
                errorResults.add(result);
            } else {
                filterRequests.add(request);
            }
        }
        return filterRequests;
    }

    private void buildAndConfirm(EshopFactory factory, List<ConfirmDeliveryEndBillNoRequest> confirmBillNoList,
                                 List<DeliveryEndConfirmResult> results) {
        BigInteger shopId = factory.getParams().geteShopId();
        List<String> tradeIds = SendOrderUtil.getTradeIds(confirmBillNoList);
        //查询原始订单列表 用于后面的数据构建（注意套餐拆分导致发货单数量大于原单数量的情况）
        Map<String, EshopSaleOrderSimpleEntity> orderMap = loadSaleOrders(tradeIds, new ArrayList<>(), shopId);
        for (ConfirmDeliveryEndBillNoRequest deliveryEndConfirmRequest : confirmBillNoList) {
            if (CollectionUtils.isEmpty(deliveryEndConfirmRequest.getDetails())) {
                results.add(buildResponse(deliveryEndConfirmRequest, new RuntimeException("发货明细为空，无法确认送达!")));
                continue;
            }
            doBuildAndConfirmByRequest(factory, deliveryEndConfirmRequest, orderMap, results);
        }
    }

    private void doBuildAndConfirmByRequest(EshopFactory factory, ConfirmDeliveryEndBillNoRequest billNoRequest,
                                            Map<String, EshopSaleOrderSimpleEntity> orderMap, List<DeliveryEndConfirmResult> results) {
        List<DeliveryEndBillNoConfirmEntity> confirmEntityList = new ArrayList<>();
        List<SyncFreightBillNoDetail> confirmBillNoDetailList = billNoRequest.getDetails();
        Map<String, List<SyncFreightBillNoDetail>> confirmBillNoDetailListGroupMap =
                confirmBillNoDetailList.stream().collect(Collectors.groupingBy(SyncFreightBillNoDetail::getTradeId));
        for (Map.Entry<String, List<SyncFreightBillNoDetail>> confirmBillNoDetailListEntry :
                confirmBillNoDetailListGroupMap.entrySet()) {
            String tradeId = confirmBillNoDetailListEntry.getKey();
            List<SyncFreightBillNoDetail> groupDetails = confirmBillNoDetailListEntry.getValue();
            EshopSaleOrderSimpleEntity saleOrder = orderMap.get(tradeId);
            DeliveryEndBillNoConfirmEntity billNoConfirmEntity = new DeliveryEndBillNoConfirmEntity();
            BeanUtils.copyProperties(billNoRequest, billNoConfirmEntity);
            BeanUtils.copyProperties(saleOrder, billNoConfirmEntity);
            doBuildConfirmDetails(billNoConfirmEntity, saleOrder, groupDetails);
            confirmEntityList.add(billNoConfirmEntity);
        }
        List<DeliveryEndConfirmResult> confirmResults = doExecuteDeliveryEndConfirm(factory, confirmEntityList);
        if (CollectionUtils.isNotEmpty(confirmResults)) {
            results.addAll(confirmResults);
        }
    }

    private List<DeliveryEndConfirmResult> doExecuteDeliveryEndConfirm(EshopFactory factory,
                                                                       List<DeliveryEndBillNoConfirmEntity> confirmEntityList) {
        List<DeliveryEndConfirmResult> results = new ArrayList<>();
        EshopDeliveryEndConfirmFeature feature = factory.getFeature(EshopDeliveryEndConfirmFeature.class);
        if (feature == null) {
            for (DeliveryEndBillNoConfirmEntity entity : confirmEntityList) {
                results.addAll(ConfirmUtils.to(entity, false, "该网店平台暂不支持确认送达!"));
            }
        } else {
            results.addAll(feature.deliveryEndConfirm(confirmEntityList));
        }
        return results;
    }

    private void doBuildConfirmDetails(DeliveryEndBillNoConfirmEntity billNoConfirmEntity,
                                       EshopSaleOrderSimpleEntity saleOrder, List<SyncFreightBillNoDetail> groupDetails) {
        Map<String, EshopSaleOrderDetailSimpleEntity> saleOrderDetailMap = saleOrder.getGroupDetails();
        List<DeliveryEndBillNoDetailConfirmEntity> entityList = new ArrayList<>();
        for (SyncFreightBillNoDetail confirmBillNoDetail : groupDetails) {
            DeliveryEndBillNoDetailConfirmEntity newDetail = new DeliveryEndBillNoDetailConfirmEntity();
            BeanUtils.copyProperties(confirmBillNoDetail, newDetail);
            String oid = newDetail.getOid();
            EshopSaleOrderDetailSimpleEntity saleOrderDetail = saleOrderDetailMap.get(oid);
            buildDetailByOrderDetail(newDetail, saleOrderDetail);
            newDetail.setDeliverOrderDetailId(confirmBillNoDetail.getDeliverDetailId());
            entityList.add(newDetail);
        }
        billNoConfirmEntity.setDetails(entityList);
    }

    private void buildDetailByOrderDetail(DeliveryEndBillNoDetailConfirmEntity newDetail,
                                          EshopSaleOrderDetailSimpleEntity saleOrderDetail) {
        if (saleOrderDetail != null) {
            BeanUtils.copyProperties(saleOrderDetail, newDetail);
            newDetail.setPlatformSkuId(saleOrderDetail.getPlatformSkuId());
            newDetail.setPlatformPtypeName(saleOrderDetail.getPlatformPtypeName());
            newDetail.setTotalQty(saleOrderDetail.getQty());
        } else {
            newDetail.setManual(true);
            newDetail.setTotalQty(BigDecimal.ZERO);
        }
    }

    private DeliveryEndConfirmResult buildResponse(ConfirmDeliveryEndBillNoRequest billNoEntity, Throwable throwable) {
        DeliveryEndConfirmResult result = buildResponse(billNoEntity);
        result.setSuccess(false);
        result.setMessage(throwable.getMessage());
        return result;
    }

    private DeliveryEndConfirmResult buildResponse(ConfirmDeliveryEndBillNoRequest billNoEntity) {
        DeliveryEndConfirmResult result = new DeliveryEndConfirmResult();
        result.setDeliverOrderId(billNoEntity.getDeliverOrderId());
        result.setWarehouseTaskId(billNoEntity.getWarehouseTaskId());
        result.setSuccess(true);
        return result;
    }

    private String getShipOrderNo(FreightBillNoSyncEntity entity, List<EshopNotifyChange> eshopNotifyChangeList) {
        String shipOrderNo = "";
        if (eshopNotifyChangeList == null || eshopNotifyChangeList.isEmpty()) {
            return shipOrderNo;
        }
        try {
            List<EshopNotifyChange> notifyChangeList = eshopNotifyChangeList.stream()
                    .filter(notify -> entity.getTradeId().equals(notify.getTradeOrderId()))
                    .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                    .collect(Collectors.toList());
            if (!notifyChangeList.isEmpty()) {
                for (EshopNotifyChange notifyChange : notifyChangeList) {
                    if (StringUtils.isEmpty(notifyChange.getContent())) {
                        continue;
                    }
                    CreateShipOrderResponse createShipOrderResponse = JsonUtils.toObject(notifyChange.getContent(),
                            CreateShipOrderResponse.class);
                    if (createShipOrderResponse != null && StringUtils.isNotEmpty(createShipOrderResponse.getShipOrderNo())) {
                        shipOrderNo = createShipOrderResponse.getShipOrderNo();
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("账套{}，订单{}获取发货单号失败:{}", CurrentUser.getProfileId(), entity.getTradeId(), e.getMessage(), e);
        }
        return shipOrderNo;
    }

    private String getShipOrderNo(SyncFreightBillNoRequest request, List<EshopNotifyChange> eshopNotifyChangeList) {
        String shipOrderNo = "";
        if (eshopNotifyChangeList == null || eshopNotifyChangeList.isEmpty()) {
            return shipOrderNo;
        }
        try {
            List<EshopNotifyChange> notifyChangeList = eshopNotifyChangeList.stream()
                    .filter(notify -> request.getTradeId().equals(notify.getTradeOrderId()))
                    .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                    .collect(Collectors.toList());
            if (!notifyChangeList.isEmpty()) {
                for (EshopNotifyChange notifyChange : notifyChangeList) {
                    if (StringUtils.isEmpty(notifyChange.getContent())) {
                        continue;
                    }
                    CreateShipOrderResponse createShipOrderResponse = JsonUtils.toObject(notifyChange.getContent(),
                            CreateShipOrderResponse.class);
                    if (createShipOrderResponse != null && StringUtils.isNotEmpty(createShipOrderResponse.getShipOrderNo())) {
                        shipOrderNo = createShipOrderResponse.getShipOrderNo();
                        break;
                    }
                }
            }
        } catch (Exception e) {

        }
        return shipOrderNo;
    }


    private void buildRecordStatus(List<SyncFreightBillNoResponse> responseList, EshopFreightSyncRecord record) {
        int maxMessageLength = 250;
        for (SyncFreightBillNoResponse response : responseList) {
            if (response.getRecordId().compareTo(record.getId()) == 0) {
                record.setSyncStatus(response.getStatus());
                record.setSyncMessage(response.getMessage());
                if (record.getSyncMessage().length() > maxMessageLength) {
                    record.setSyncMessage(record.getSyncMessage().substring(0, maxMessageLength));
                }
                for (SyncFreightBillNoResultDetail detail : response.getDetails()) {
                    for (EshopFreightSyncRecordDetail recordDetail : record.getDetails()) {
                        if (detail.getRecordDetailId().compareTo(recordDetail.getId()) == 0) {
                            recordDetail.setSyncStatus(detail.getStatus());
                            recordDetail.setSyncMessage(detail.getMessage());
                            if (recordDetail.getSyncMessage().length() > maxMessageLength) {
                                recordDetail.setSyncMessage(recordDetail.getSyncMessage().substring(0,
                                        maxMessageLength));
                            }
                        }
                    }
                }
            }
        }
    }

    private Map<String, List<EshopFreightSyncRecordDetail>> doBuildSendDetails(EshopFactory factory,
                                                                               FreightBillNoSyncEntity entity, EshopSaleOrderSimpleEntity saleOrder, List<SyncFreightBillNoDetail> groupDetails) {
        Map<String, EshopSaleOrderDetailSimpleEntity> saleOrderDetailMap = saleOrder.getGroupDetails();
        BigInteger profileId = CurrentUser.getProfileId();
        String tradeId = saleOrder.getTradeId();
        List<String> tradeIds = Collections.singletonList(tradeId);
        List<EshopFreightSyncRecordDetail> allRecordDetails = freightSyncRecordDetailMapper.listByTradeIds(profileId,
                tradeIds);
        boolean lastOne = true;
        List<FreightBillNoSyncDetailEntity> entityList = new ArrayList<>();
        for (SyncFreightBillNoDetail billNoDetail : groupDetails) {
            FreightBillNoSyncDetailEntity newDetail = new FreightBillNoSyncDetailEntity();
            BeanUtils.copyProperties(billNoDetail, newDetail);
            String oid = newDetail.getOid();
            EshopSaleOrderDetailSimpleEntity saleOrderDetail = saleOrderDetailMap.get(oid);
            buildDetailByOrderDetail(newDetail, saleOrderDetail);
            newDetail.setDeliverOrderDetailId(billNoDetail.getDeliverDetailId());
            doBuildSendDetailByRecord(entity, newDetail, allRecordDetails);

            BigDecimal sendQty = newDetail.getQty();
            BigDecimal deliveredQty = newDetail.getDeliveredQty();
            if (deliveredQty == null) {
                deliveredQty = BigDecimal.ZERO;
            }
            if ((sendQty.add(deliveredQty)).compareTo(newDetail.getTotalQty()) < 0) {
                newDetail.setLastOne(false);
                lastOne = false;
            } else {
                newDetail.setLastOne(true);
            }
            doReBuildBatchInfo(newDetail, saleOrderDetail);
            entityList.add(newDetail);
            //entity.getDetails().add(newDetail);
        }
        entity.setDetails(entityList);
        entity.setLast(lastOne);
        doAppendSendingFreightInfo(factory, entity, allRecordDetails);
        buildLastFreights(entity, allRecordDetails);
        buildOtherFreightInfoList(factory, allRecordDetails, entity);
        if (CollectionUtils.isNotEmpty(allRecordDetails)) {
            return allRecordDetails.stream().collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getTradeId));
        } else {
            return null;
        }
    }

    private void doBuildSendDetailByRecord(FreightBillNoSyncEntity entity, FreightBillNoSyncDetailEntity sendDetail,
                                           List<EshopFreightSyncRecordDetail> allRecordDetails) {
        if (CollectionUtils.isEmpty(allRecordDetails)) {
            entity.setFirst(true);
            return;
        }
        double sum = allRecordDetails.stream().mapToDouble(x -> x.getSyncQty().doubleValue()).sum();
        BigDecimal deliveredQty = BigDecimal.valueOf(sum);
        sendDetail.setDeliveredQty(deliveredQty);
    }

    private void doAppendSendingFreightInfo(EshopFactory factory, FreightBillNoSyncEntity entity,
                                            List<EshopFreightSyncRecordDetail> allRecordDetails) {
        if (!entity.isLast()) {
            return;
        }
        if (CollectionUtils.isEmpty(allRecordDetails)) {
            return;
        }
        Map<String, List<EshopFreightSyncRecordDetail>> recordDetailsMap = allRecordDetails.stream()
                .filter(x -> x.getSyncStatus().equals(SyncFreightStatus.ING))
                .collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getTradeId));
        List<EshopFreightSyncRecordDetail> details = recordDetailsMap.get(entity.getTradeId());
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        Map<String, List<EshopFreightSyncRecordDetail>> recordDetailMap = details.stream()
                .collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getFreightBillNo));
        for (Map.Entry<String, List<EshopFreightSyncRecordDetail>> item : recordDetailMap.entrySet()) {
            List<EshopFreightSyncRecordDetail> recordDetails = item.getValue();
            String freightBillNo = item.getKey();
            SyncFreightInfo freightInfo = toFreightInfo(recordDetails.get(0), freightBillNo);
            recordDetails.forEach(detail -> detail.setOld(true));
            freightInfo.setSyncDetails(recordDetails);
            SendOrderUtil.getInstance().doMatchFreightMapping(freightInfo, platformEshopConfig, factory);
            entity.getFreightInfoList().add(freightInfo);
        }
    }

    /**
     * 同步单号的公共方法
     *
     * @param requestList  发货列表
     * @param sendCallback 发货成功回调函数
     */
    @Override
    public List<SyncFreightBillNoResponse> send(List<SyncFreightBillNoRequest> requestList, SendCallback sendCallback) {
        return sendWithProcessLogger(requestList, null, sendCallback);
    }

    @Override
    public List<SyncFreightBillNoResponse> sendWithProcessLogger(List<SyncFreightBillNoRequest> requestList,
                                                                 RedisProcessMessage redisLogger, SendCallback sendCallBack) {
        return null;
    }

    @Override
    public SendCheckResponse validatePrintOrders(List<SyncFreightBillNoRequest> sendList) {
        return validateSendOrders(sendList, true);
    }

    private SendCheckResponse validateSendOrders(List<SyncFreightBillNoRequest> requestList, boolean isPrint) {
        if (CollectionUtils.isEmpty(requestList)) {
            return new SendCheckResponse("发货列表为空");
        }
        Long beginTime = System.currentTimeMillis();
        List<SyncFreightBillNoResponse> errorResults = new CopyOnWriteArrayList<>();
        List<SyncFreightBillNoRequest> filterRequestList = filterRequest(requestList, errorResults, false);
        SendCheckResponse response = new SendCheckResponse();

        if (CollectionUtils.isNotEmpty(errorResults)) {
            for (SyncFreightBillNoResponse error : errorResults) {
                SendCheckResult result = new SendCheckResult(error);
                response.getErrorList().add(result);
            }
        }
        if (CollectionUtils.isEmpty(filterRequestList)) {
            return response;
        }
        String dealPage = sendGoodsConfig.getFreightMappingDealPage();
        Map<BigInteger, List<SyncFreightBillNoRequest>> groupList =
                filterRequestList.stream().collect(Collectors.groupingBy(SyncFreightBillNoRequest::getOtypeId));

        for (Map.Entry<BigInteger, List<SyncFreightBillNoRequest>> entry : groupList.entrySet()) {
            List<SyncFreightBillNoRequest> sendList = entry.getValue();
            if (CollectionUtils.isEmpty(sendList)) {
                continue;
            }
            try {
                BigInteger otypeId = entry.getKey();
                if (otypeId == null || BigInteger.ZERO.equals(otypeId)) {
                    continue;
                }
                BigInteger profileId = CurrentUser.getProfileId();
                EshopInfo eshopInfo = bifrostEshopMapper.getEshopInfoByShopId(profileId, otypeId);
                List<FreightMapping> onlineFreightMappings = FreightMappingCacheManger.getFreightMappingByCache(eshopInfo.getEshopType());
                List<FreightMapping> userFreightMappings =
                        onlineFreightMappingMapper.getFreightMappingFromUserConfig(profileId,
                                eshopInfo.getEshopType().getCode());
                List<String> tradeIds = SendOrderUtil.getAllTradeIds(sendList);
                List<String> refundIds = sendList.stream().map(SyncFreightBillNoRequest::getRefundId)
                        .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
                Map<String, EshopSaleOrderSimpleEntity> orderMap = loadSaleOrders(tradeIds, refundIds, otypeId);
                List<EshopNotifyChange> eshopNotifyChangeList = new ArrayList<>();
                //退款状态检查，配置是否该项开启检查（Apollo部署级别）
                if (sendGoodsConfig.getNeedCheckNotifyRefund()) {
                    notifyMapper.queryMessageChangeSorted(profileId, tradeIds, otypeId, TMCType.REFUND_STOP.getCode());
                }
//                Map<String, List<BillDeliverDetailDTO>> deliverBillMap = queryDeliverBillMap(orderMap);
                for (SyncFreightBillNoRequest sendReq : sendList) {
                    EshopSaleOrderSimpleEntity simpleTrade = orderMap.get(sendReq.getTradeId());
                    if (TradeStatus.ALL_CLOSED.equals(simpleTrade.getTradeStatus())) {
                        response.getErrorList().add(
                                new SendCheckResult(buildFailResponse(sendReq, "订单交易关闭"), "", SendCheckCodeEnum.TADES_STATUS_CLOSED));
                        continue;
                    }
                    if (checkOrderDetailChange(sendReq, simpleTrade)) {
                        response.getErrorList().add(
                                new SendCheckResult(buildFailResponse(sendReq, "发货明细跟原始订单明细不一致，线上商品可能存在变更情况"), "", SendCheckCodeEnum.DETAIL_CHANGED));
                        continue;
                    }
                    //拆分订单,对比明细是否全关闭
                    if (sendReq.isSpilt()) {
                        List<EshopSaleOrderDetailSimpleEntity> closedDetails
                                = simpleTrade.getDetails().stream().filter(d -> TradeStatus.ALL_CLOSED.equals(d.getTradeStatus())).collect(Collectors.toList());
                        //发货单明细都能在已关闭明细中找到
                        if (CollectionUtils.isNotEmpty(closedDetails) &&
                                sendReq.getDetails().stream().allMatch(sendDetail ->
                                        closedDetails.stream().anyMatch(closedDetail -> StringUtils.isNotEmpty(closedDetail.getOid())
                                                && closedDetail.getOid().equals(sendDetail.getOid())))) {
                            response.getErrorList().add(new SendCheckResult(buildFailResponse(sendReq, "拆分订单子单明细全部交易关闭"), "", SendCheckCodeEnum.TADES_STATUS_CLOSED));
                            continue;
                        }
                    }
                    if (CollectionUtils.isNotEmpty(eshopNotifyChangeList)
                            && eshopNotifyChangeList.stream().anyMatch(notify -> sendReq.getTradeId().equals(notify.getTradeOrderId()))) {
                        response.getErrorList().add(
                                new SendCheckResult(buildFailResponse(sendReq, "订单存在退款"),
                                        "", SendCheckCodeEnum.TRADE_HAS_REFUND));
                        continue;
                    }
                    //打印验证结束
                    //apollo配置不检查物流映射
                    if (isPrint || !sendGoodsConfig.getNeedCheckFreightMapping()) {
                        continue;
                    }
                    //快递发货再验证
                    if (!SelfDeliveryModeEnum.EXPRESS_LOGISTICS.equals(sendReq.getDeliveryMode())) {
                        continue;
                    }
                    //如果配置了不验证的平台直接返回
                    if (sendGoodsConfig.getNotCheckFreightMappingShopTypeList().contains(eshopInfo.getEshopType().getCode())) {
                        continue;
                    }
                    if (CollectionUtils.isEmpty(sendReq.getFreightInfoList())) {
                        response.getErrorList().add(new SendCheckResult(buildFailResponse(sendReq, "请选择物流公司以后再操作"), dealPage, SendCheckCodeEnum.OTHER));
                        continue;
                    }
                    if (!validateFreight(sendReq.getFreightInfoList(), onlineFreightMappings, userFreightMappings)) {
                        response.getErrorList().add(new SendCheckResult(buildFailResponse(sendReq,
                                "线上物流公司与本地物流公司无对应关系，请处理"), dealPage, SendCheckCodeEnum.FREIGHT_UNMAPPING));
                    }
                }
            } catch (Exception ex) {
//
            }
        }
        return response;
    }


    private boolean checkOrderDetailChange(SyncFreightBillNoRequest sendReq, EshopSaleOrderSimpleEntity simpleTrade) {
        List<EshopSaleOrderDetailSimpleEntity> details = simpleTrade.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return false;
        }
        Map<String, EshopSaleOrderDetailSimpleEntity> detailMap = new HashMap<>();
        for (EshopSaleOrderDetailSimpleEntity detailSimple : details) {
            String oid = detailSimple.getOid();
            if (detailSimple.isCombo()) {
                continue;
            }
            if (detailMap.containsKey(oid)) {
                continue;
            }
            detailMap.put(oid, detailSimple);
        }
        List<SyncFreightBillNoDetail> sendDetails = sendReq.getDetails();
        for (SyncFreightBillNoDetail sendDetail : sendDetails) {
            String sendDetailOid = sendDetail.getOid();
            if (!detailMap.containsKey(sendDetailOid)) {
                continue;
            }
            EshopSaleOrderDetailSimpleEntity detailSimple = detailMap.get(sendDetailOid);
            boolean skuIsSame = SendOrderUtil.checkSendSkuIsSame(sendDetail, detailSimple);
            if (!skuIsSame) {
                return true;
            }
        }
        return false;
    }


    @Override
    public SendCheckResponse validateSendOrders(List<SyncFreightBillNoRequest> requestList) {
        return validateSendOrders(requestList, false);
    }

    private boolean validateFreight(List<SyncFreightInfo> freightInfoList, List<FreightMapping> onlineFreightMappings
            , List<FreightMapping> userFreightMappings) {
        boolean isMatched = false;
        if (CollectionUtils.isNotEmpty(onlineFreightMappings)) {
            isMatched =
                    onlineFreightMappings.stream().anyMatch(o -> freightInfoList.get(0).getFreightCode().equals(o.getLocalCode()));
        }
        if (!isMatched && CollectionUtils.isNotEmpty(userFreightMappings)) {
            isMatched =
                    userFreightMappings.stream().anyMatch(o -> freightInfoList.get(0).getFreightCode().equals(o.getLocalCode()));
        }
        return isMatched;
    }


    /**
     * 周期购订单重新赋值为billno,发货区分期数
     */
    protected void rematchCyclePurchaeTid(FreightBillNoSyncEntity entity, SyncFreightBillNoRequest request) {
        boolean isMerge = request.getTradeId().contains(";");
        if (isMerge) {
            for (String tid : request.getTradeId().split(";")) {
                if (StringUtils.isEmpty(tid)) {
                    continue;
                }
                if (tid.contains(entity.getTradeId())) {
                    entity.setTradeId(tid);
                }
            }
        } else {
            entity.setTradeId(request.getTradeId());
        }
    }

    protected void rebuildSplitLastSendQty(FreightBillNoSyncEntity entity,
                                           EshopSaleOrderSimpleEntity saleOrder,
                                           EshopFactory factory) {
        if (CollectionUtils.isEmpty(entity.getDetails())) {
            return;
        }
        for (FreightBillNoSyncDetailEntity detail : entity.getDetails()) {
            if (detail.isDeleted() || detail.isManual()) {
                continue;
            }
            if (platformEshopConfig.getSendNeedUseComboQtyShopTypeList().contains(factory.getShopType().getCode())) {
                continue;
            }

            EshopSaleOrderDetailSimpleEntity saleOrderDetail = saleOrder.getGroupDetails().get(detail.getOid());
            if (saleOrderDetail == null) {
                continue;
            }
            List<Integer> supportList = platformEshopConfig.getSendNeedUseDeliverQtyShopTypeList();
            boolean needUserDeliverQty = CollectionUtils.isNotEmpty(supportList)
                    && supportList.contains(factory.getShopType().getCode());

            //前面已重新构建,跳过
            if (saleOrderDetail.isCombo() || needUserDeliverQty) {
                continue;
            }

            BigDecimal deliveredQty = detail.getDeliveredQty();
            BigDecimal remainQty = saleOrderDetail.getQty().subtract(deliveredQty);
            if (remainQty.compareTo(BigDecimal.ZERO) > 0) {
                detail.setQty(remainQty);
            }
        }
    }

    protected void buildGongBangBangShipNo(EshopFactory factory, String tradeId, FreightBillNoSyncEntity entity) {
        try {
            if (factory.getShopType().equals(ShopType.GongBangBang)) {
                if (factory.getParams() == null) {
                    return;
                }
                BigInteger profileId = CurrentUser.getProfileId();
                List<String> tradeIds = Collections.singletonList(tradeId);
                List<EshopNotifyChange> eshopNotifyChangeList =
                        notifyMapper.queryMessageChangeSorted(profileId, tradeIds, factory.getParams().geteShopId(),
                                TMCType.CREATE_SHIP_ORDER.getCode());
                entity.setShipOrderNo(getShipOrderNo(entity, eshopNotifyChangeList));
            }
        } catch (Exception ex) {
            logger.error("订单{}工帮帮获取发货单号失败", tradeId, ex);
        }
    }

    protected void buildDeliveredFreightPackageCount(FreightBillNoSyncEntity entity,
                                                     List<EshopFreightSyncRecordDetail> oldRecordDetailList) {
        if (CollectionUtils.isEmpty(oldRecordDetailList)) {
            return;
        }
        Set<String> freightNos = new HashSet<>();
        //oldRecordDetailList 同一个订单明细所有的物流同步明细记录。
        for (EshopFreightSyncRecordDetail recordDetail : oldRecordDetailList) {
            if (SyncFreightStatus.SUCCESS.equals(recordDetail.getSyncStatus()) || SyncFreightStatus.ING.equals(recordDetail.getSyncStatus())) {
                freightNos.add(recordDetail.getFreightBillNo());
            }
        }
        entity.setDeliveredPackageCount(freightNos.size());
    }

    protected void buildNoSubmitCount(FreightBillNoSyncEntity entity, EshopSaleOrderSimpleEntity saleOrder) {
        Map<String, EshopSaleOrderDetailSimpleEntity> details = saleOrder.getGroupDetails();
        List<EshopSaleOrderDetailSimpleEntity> noSubmitList =
                details.values().stream().filter(x -> x.getProcessState() != 1 && !x.getTradeStatus().equals(TradeStatus.ALL_CLOSED)).collect(Collectors.toList());
        try {
            boolean anyMatch = details.values().stream().anyMatch(x ->
                    x.getTradeStatus() == TradeStatus.ALL_CLOSED ||
                            (!x.getRefundStatus().equals(RefundStatus.CANCEL) &&
                                    !x.getRefundStatus().equals(RefundStatus.NONE))
            );
            if (anyMatch) {
                entity.setExistRefund(true);
            }
        } catch (Exception e) {
            logger.error("判断是否存在退款异常:错误原因:" + e.getMessage(), e);
        }
        if (CollectionUtils.isEmpty(noSubmitList)) {
            entity.setNoSubmitCount(0);
        } else {
            List<EshopSaleOrderDetailSimpleEntity> noRefundList =
                    noSubmitList.stream().filter(x -> !x.getRefundStatus().equals(RefundStatus.SUCCESS)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noRefundList)) {
                entity.setNoSubmitCount(noRefundList.size());
            } else {
                entity.setNoSubmitCount(0);
            }
        }
    }

    protected void buildLastFreights(FreightBillNoSyncEntity entity, List<EshopFreightSyncRecordDetail> orderOldRecords) {
        List<SyncFreightInfo> lastFreights = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderOldRecords)) {
            return;
        }
        List<FreightBillNoSyncDetailEntity> entityDetails = entity.getDetails();
        BigInteger deliverOrderId = entityDetails.get(0).getDeliverOrderId();
        Set<BigInteger> deliverOrderDetailIds = new HashSet<>();
        entityDetails.forEach(detail -> deliverOrderDetailIds.add(detail.getDeliverOrderDetailId()));
        List<EshopFreightSyncRecordDetail> details = new ArrayList<>();
        try {
            Map<String, List<EshopFreightSyncRecordDetail>> collect =
                    orderOldRecords.stream().filter(x -> x.getSyncStatus() == SyncFreightStatus.SUCCESS).collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getOid));
            ArrayList<Map.Entry<String, List<EshopFreightSyncRecordDetail>>> entries =
                    new ArrayList<>(collect.entrySet());
            entries.sort(Comparator.comparing(o -> o.getValue().get(o.getValue().size() - 1).getSyncTime()));
            int i = 0;
            for (Map.Entry<String, List<EshopFreightSyncRecordDetail>> entry : entries) {
                int j = entry.getValue().size();
                i++;
                for (EshopFreightSyncRecordDetail recordDetail : entry.getValue()) {
                    recordDetail.setSyncOrderSequence(i);
                    recordDetail.setSyncOrderDetailSequence(j--);
                }
            }
        } catch (Exception e) {
            logger.error("发货成功排序失败:错误原因:" + e.getMessage(), e);
        }
        for (EshopFreightSyncRecordDetail detail : orderOldRecords) {
            if (!detail.getSyncStatus().equals(SyncFreightStatus.SUCCESS)) {
                continue;
            }
            if (deliverOrderId.compareTo(detail.getDeliverOrderId()) != 0) {
                continue;
            }
            if (!deliverOrderDetailIds.contains(detail.getDeliverOrderDetailId())) {
                continue;
            }
            if (!detail.getTradeId().equals(entity.getTradeId())) {
                continue;
            }
            details.add(detail);
        }
        if (details.isEmpty()) {
            return;
        }
        Map<String, List<EshopFreightSyncRecordDetail>> recordDetailMap =
                details.stream().collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getFreightBillNo));
        for (String freightBillNo : recordDetailMap.keySet()) {
            SyncFreightInfo freightInfo = toFreightInfo(recordDetailMap.get(freightBillNo).get(0), freightBillNo);
            lastFreights.add(freightInfo);
        }
        entity.setLastFreightInfoList(lastFreights);
    }

    protected void buildOtherFreightInfoList(EshopFactory factory, List<EshopFreightSyncRecordDetail> orderOldRecords,
                                             FreightBillNoSyncEntity entity) {
        EshopSaleOrderQuerySendRequireFeature querySendRequireFeature =
                factory.getFeature(EshopSaleOrderQuerySendRequireFeature.class);
        if (querySendRequireFeature == null) {
            return;
        }
        QuerySendRequireResult querySendRequire = querySendRequireFeature.getQuerySendRequire();
        if (querySendRequire.isNeedAllFreightInfo() && !CollectionUtils.isEmpty(orderOldRecords)) {
            List<EshopFreightSyncRecordDetail> successList =
                    orderOldRecords.stream().filter(x -> x.getSyncStatus() == SyncFreightStatus.SUCCESS).collect(Collectors.toList());
            entity.setOtherFreightInfoList(successList);
        }
    }

    private void doBuildSendDetails(EshopFactory factory,
                                    Map<BigInteger, List<SyncFreightBillNoDetail>> requestDetailsMap, Map<String, BigDecimal> deliveredQtyMap,
                                    EshopSaleOrderSimpleEntity saleOrder, FreightBillNoSyncEntity entity,
                                    List<EshopFreightSyncRecordDetail> groupRecordDetailList, List<BillDeliverDetailDTO> deliverDetailList) {
        List<FreightBillNoSyncDetailEntity> detailEntityList = new ArrayList<>();
        for (EshopFreightSyncRecordDetail recordDetail : groupRecordDetailList) {
            FreightBillNoSyncDetailEntity newDetail = initFreightBillNoSyncDetailEntityByRecordDetail(recordDetail);
//            BeanUtils.copyProperties(recordDetail, newDetail);
            String oid = recordDetail.getOid();
            EshopSaleOrderDetailSimpleEntity saleOrderDetail = saleOrder.getGroupDetails().get(oid);
            buildDetailByOrderDetail(newDetail, saleOrderDetail);
            newDetail.setQty(recordDetail.getSyncQty());
            List<Integer> supportList = platformEshopConfig.getSendNeedUseDeliverQtyShopTypeList();
            boolean needUserDeliverQty =
                    CollectionUtils.isNotEmpty(supportList) && supportList.contains(factory.getShopType().getCode());
            //拼多多用发货单商品数量检查是否是最后一单
            if (saleOrderDetail != null && (saleOrderDetail.isCombo() != newDetail.isCombo() || needUserDeliverQty)) {
                reBuildTotalQtyWhenComboChange(newDetail, deliverDetailList);
                newDetail.setOriginalQty(saleOrderDetail.getQty());
            }
            if (!Objects.isNull(saleOrderDetail) && platformEshopConfig.getSendNeedUseComboQtyShopTypeList().contains(factory.getShopType().getCode())) {
                saleOrderDetail.setQty(newDetail.getTotalQty());
            }
            doBuildSerialNoList(requestDetailsMap, newDetail);
            BigDecimal deliveredQty = deliveredQtyMap.getOrDefault(this.getCacheQtyKey(recordDetail.getTradeId(),
                    oid), BigDecimal.ZERO);
            if (deliveredQty.compareTo(BigDecimal.ZERO) < 0) {
                deliveredQty = BigDecimal.ZERO;
                deliveredQtyMap.put(this.getCacheQtyKey(recordDetail.getTradeId(), oid), deliveredQty);
            }
            BigDecimal totalDeliveredQty = deliveredQty.subtract(newDetail.getQty());
            if (totalDeliveredQty.compareTo(BigDecimal.ZERO) < 0) {
                totalDeliveredQty = BigDecimal.ZERO;
            }
            newDetail.setDeliveredQty(totalDeliveredQty);
            if ((newDetail.getQty().add(totalDeliveredQty)).compareTo(newDetail.getTotalQty()) >= 0) {
                newDetail.setLastOne(true);
            }
            doReBuildBatchInfo(newDetail, saleOrderDetail);
            deliveredQtyMap.put(this.getCacheQtyKey(recordDetail.getTradeId(), oid),
                    newDetail.getQty().add(newDetail.getDeliveredQty()));
            detailEntityList.add(newDetail);
        }
        Map<String, List<FreightBillNoSyncDetailEntity>> listMap =
                detailEntityList.stream().collect(Collectors.groupingBy(FreightBillNoSyncDetailEntity::getOid));
        List<FreightBillNoSyncDetailEntity> mergeDetails = doMergeDetails(entity, listMap, deliveredQtyMap);
        if (CollectionUtils.isNotEmpty(mergeDetails)) {
            doReBuildDetails(factory, entity, saleOrder, mergeDetails);
        }
    }

    private FreightBillNoSyncDetailEntity
    initFreightBillNoSyncDetailEntityByRecordDetail(EshopFreightSyncRecordDetail recordDetail) {
        FreightBillNoSyncDetailEntity req = new FreightBillNoSyncDetailEntity();
        req.setTradeId(recordDetail.getTradeId());
        req.setOid(recordDetail.getOid());
        req.setPlatformPtypeId(recordDetail.getPlatformPtypeId());
        req.setPlatformSkuId(recordDetail.getPlatformSkuId());
        req.setPlatformPtypeXcode(recordDetail.getPlatformXcode());
        req.setPlatformPtypeName(recordDetail.getPlatformPtypeName());
        req.setTotalQty(recordDetail.getTotalQty());
        req.setDeliverOrderId(recordDetail.getDeliverOrderId());
        req.setDeliverOrderDetailId(recordDetail.getDeliverOrderDetailId());
        req.setWarehouseTaskId(recordDetail.getWarehouseTaskId());
        req.setWarehouseTaskDetailId(recordDetail.getWarehouseTaskDetailId());
        req.setGift(recordDetail.isGift());
        req.setLastOne(recordDetail.getLastOne() == null || (boolean) recordDetail.getLastOne());
        req.setPlatformCodeList(recordDetail.getPlatformCodeList());
        req.setCombo(recordDetail.isCombo());
        req.setDeleted(recordDetail.isDeleted());
        req.setXcode(recordDetail.getXcode());
        req.setBatchInfos(recordDetail.getBatchInfos());
        req.setPlatformFreightId(recordDetail.getPlatformFreightId());
        return req;
    }

    protected List<FreightBillNoSyncDetailEntity> doMergeDetails(FreightBillNoSyncEntity entity,
                                                                 Map<String, List<FreightBillNoSyncDetailEntity>> listMap,
                                                                 Map<String, BigDecimal> deliveredQtyMap) {
        List<FreightBillNoSyncDetailEntity> mergeDetails = new ArrayList<>();
        String tradeId = entity.getTradeId();
        for (String oid : listMap.keySet()) {
            String cacheQtyKey = this.getCacheQtyKey(tradeId, oid);
            List<FreightBillNoSyncDetailEntity> detailEntityList = listMap.get(oid);
            if (CollectionUtils.isEmpty(detailEntityList)) {
                continue;
            }
            if (detailEntityList.size() == 1) {
                mergeDetails.add(detailEntityList.get(0));
                continue;
            }
            FreightBillNoSyncDetailEntity mergeDetail = detailEntityList.get(0);
            double sum = detailEntityList.stream().mapToDouble(x -> x.getQty().doubleValue()).sum();
            //合并序列号和批次号信息
            //第0个作为mergeDetail，这里从第一个开始计算
            for (int i = 1; i < detailEntityList.size(); i++) {
                FreightBillNoSyncDetailEntity detailEntity = detailEntityList.get(i);
                if (CollectionUtils.isNotEmpty(detailEntity.getBatchInfos())) {
                    if (mergeDetail.getBatchInfos() == null) {
                        mergeDetail.setBatchInfos(detailEntity.getBatchInfos());
                    } else {
                        mergeDetail.getBatchInfos().addAll(detailEntity.getBatchInfos());
                    }
                }
                if (CollectionUtils.isNotEmpty(detailEntity.getPlatformCodeList())) {
                    if (mergeDetail.getPlatformCodeList() == null) {
                        mergeDetail.setPlatformCodeList(detailEntity.getPlatformCodeList());
                    } else {
                        mergeDetail.getPlatformCodeList().addAll(detailEntity.getPlatformCodeList());
                    }
                }
            }
            mergeDetail.setQty(BigDecimal.valueOf(sum));
            BigDecimal totalDeliveredQty = deliveredQtyMap.get(cacheQtyKey);
            BigDecimal realDeliveredQty = totalDeliveredQty.subtract(mergeDetail.getQty());
            if (realDeliveredQty.compareTo(BigDecimal.ZERO) < 0) {
                realDeliveredQty = BigDecimal.ZERO;
            }
            mergeDetail.setDeliveredQty(realDeliveredQty);
            mergeDetails.add(mergeDetail);
        }
        return mergeDetails;
    }

    private void doReBuildDetails(EshopFactory factory, FreightBillNoSyncEntity entity,
                                  EshopSaleOrderSimpleEntity saleOrder, List<FreightBillNoSyncDetailEntity> mergeDetails) {
        if (!sendGoodsConfig.getForceCheckSendQtyShopTypeList().contains(factory.getShopType().getCode())) {
            entity.setDetails(mergeDetails);
            return;
        }
        if (factory.getShopType().equals(ShopType.JDong) && saleOrder.getGroupDetails().keySet().size() == 1) {
            entity.setDetails(mergeDetails);
            return;
        }
        if (CollectionUtils.isEmpty(mergeDetails)) {
            entity.setDetails(mergeDetails);
            return;
        }
        entity.setDetails(new ArrayList<>());
        for (FreightBillNoSyncDetailEntity detail : mergeDetails) {
            BigDecimal qty = detail.getQty();
            BigDecimal originalQty = detail.getOriginalQty();
            BigDecimal deliveredQty = detail.getDeliveredQty();
            if (deliveredQty.compareTo(BigDecimal.ZERO) == 0) {
                if (qty.compareTo(originalQty) >= 0) {
                    detail.setQty(originalQty);
                    detail.setLastOne(true);
                    entity.getDetails().add(detail);
                } else {
                    detail.setLastOne(false);
                    entity.getDetails().add(detail);
                }
            } else {
                if (deliveredQty.compareTo(originalQty) >= 0) {
                    continue;
                }
                if ((qty.add(deliveredQty)).compareTo(originalQty) >= 0) {
                    BigDecimal lessQty = originalQty.subtract(deliveredQty);
                    detail.setQty(lessQty);
                    detail.setLastOne(true);
                    entity.getDetails().add(detail);
                } else {
                    detail.setLastOne(false);
                    entity.getDetails().add(detail);
                }
            }
        }
    }

    protected void doReBuildBatchInfo(FreightBillNoSyncDetailEntity newDetail,
                                      EshopSaleOrderDetailSimpleEntity saleOrderDetail) {
        List<BatchInfo> newList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newDetail.getBatchInfos())) {
            newDetail.getBatchInfos().removeIf(x -> !x.isBatchExist());
            for (BatchInfo batchInfo : newDetail.getBatchInfos()) {
                if (!batchInfo.isBatchExist()) {
                    continue;
                }
                if (saleOrderDetail != null) {
                    batchInfo.setProductName(saleOrderDetail.getPlatformPtypeName());
                }
                newList.add(batchInfo);
            }
        }
        newDetail.setBatchInfos(newList);
    }

    protected List<FreightBillNoSyncEntity> buildSendEntityListOld(EshopFactory factory,
                                                                   EshopFreightSyncRecord record,
                                                                   SyncFreightBillNoRequest request,
                                                                   Map<String, EshopSaleOrderSimpleEntity> orderMap,
                                                                   List<SyncFreightBillNoResultDetail> results,
                                                                   Map<String, List<BillDeliverDetailDTO>> deliverBillMap,
                                                                   Map<BigInteger, List<DeliverFreightSimpleEntity>> deliverFreightMap,
                                                                   Map<String, List<EshopFreightSyncRecordDetail>> oldRecordDetailMap) {
        List<FreightBillNoSyncEntity> syncEntities = new ArrayList<>();
        List<EshopFreightSyncRecordDetail> recordDetailList = record.getDetails();
        if (CollectionUtils.isEmpty(recordDetailList)) {
            return syncEntities;
        }
        Map<String, List<EshopFreightSyncRecordDetail>> recordDetailMap =
                recordDetailList.stream().collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getTradeId));
        Map<BigInteger, List<SyncFreightBillNoDetail>> requestDetailsMap =
                request.getDetails().stream().collect(Collectors.groupingBy(SyncFreightBillNoDetail::getDeliverDetailId));
        for (Map.Entry<String, List<EshopFreightSyncRecordDetail>> entry : recordDetailMap.entrySet()) {
            String tradeId = entry.getKey();
            List<EshopFreightSyncRecordDetail> oldRecordDetailList = oldRecordDetailMap.get(tradeId);
            List<EshopFreightSyncRecordDetail> groupRecordDetailList = entry.getValue();
            Map<String, BigDecimal> deliveredQtyMap = buildDeliveredQtyMap(oldRecordDetailList, groupRecordDetailList);
            EshopSaleOrderSimpleEntity saleOrder = orderMap.get(tradeId);
            FreightBillNoSyncEntity entity = initEshopBillNoSyncEntityByRequest(request);
            fillFreightEntityBySaleOrder(saleOrder, entity);
            if (CollectionUtils.isNotEmpty(saleOrder.getMarkDataList())) {
                mergeMarkDataList(entity, saleOrder.getMarkDataList());
            }
            buildNoSubmitCount(entity, saleOrder);
            List<BillDeliverDetailDTO> deliverDetailList = deliverBillMap.get(tradeId);
            doBuildSendDetails(factory, requestDetailsMap, deliveredQtyMap, saleOrder, entity, groupRecordDetailList,
                    deliverDetailList);

            if (CollectionUtils.isNotEmpty(request.getDeliverMarks())
                    && request.getDeliverMarks().stream()
                    .anyMatch(m -> BaseOrderMarkEnum.CYCLE_PURCHASE.equals(m.getOrderMarkEnum()))) {
                rematchCyclePurchaeTid(entity, request);
            }
            if (entity.getDetails().size() == 0) {
                results.addAll(SendOrderUtil.buildNoNeedSendResult(request));
                continue;
            }
            boolean isLast = checkEntityIsLast(saleOrder, deliveredQtyMap, deliverDetailList, entity);
            boolean isFirst = checkEntityIsFirst(oldRecordDetailList);
            entity.setLast(isLast);
            entity.setFirst(isFirst);
            if (isLast && factory.getParams().getSplitSendWhenLastSync()) {
                rebuildSplitLastSendQty(entity, saleOrder, factory);
            }
            entity.getFreightInfoList().forEach(freightInfo -> SendOrderUtil.getInstance().doMatchFreightMapping(freightInfo, platformEshopConfig, factory));
            appendFreightInfos(entity, record, oldRecordDetailList, saleOrder, factory);
            buildLastFreights(entity, oldRecordDetailList);
            buildGongBangBangShipNo(factory, tradeId, entity);
            buildOtherFreightInfoList(factory, oldRecordDetailList, entity);
            buildDeliveredFreightPackageCount(entity, oldRecordDetailList);
            buildSyncType(factory, results, record, request, deliverBillMap, deliverFreightMap, oldRecordDetailList, entity);
            updateSyncType(record, entity.getSyncType());
            entity.setLoginUsername(CurrentUser.getLoginName());
            try {
                checkSameFreight(entity, factory);
                syncEntities.add(entity);
            } catch (SyncCheckException syncCheckException) {
                results.addAll(SendUtils.to(entity, syncCheckException.getStatus(),
                        "同步检查不通过：" + syncCheckException.getMessage()));
            }
        }
        syncEntities = buildEntityByRule(factory, request, syncEntities);
        return syncEntities;
    }

    protected FreightBillNoSyncEntity initEshopBillNoSyncEntityByRequest(SyncFreightBillNoRequest request) {
        FreightBillNoSyncEntity entity = new FreightBillNoSyncEntity();
        entity.setTradeId(request.getTradeId());
        entity.setRefundId(request.getRefundId());
        entity.setTradeStatus(request.getTradeStatus());
        entity.setSendAddress(request.getSendAddress());
        entity.setReturnAddress(request.getReturnAddress());
        entity.setFreightInfoList(request.getFreightInfoList());
        entity.setSpilt(request.isSpilt());
        entity.setMerge(request.isMerge());
        entity.setRemark(request.getRemark());
        entity.setDeliveryMode(request.getDeliveryMode());
        entity.setDeliverMarks(request.getDeliverMarks());
        entity.setSendAddressId(request.getSendAddressId());
        entity.setSendWarehouseId(request.getSendWarehouseId());
        entity.setPlatformOperator(request.getPlatformOperator());
        entity.setShipOrderNo(request.getShipOrderNo());
        entity.setDeliverOrderId(request.getDeliverOrderId());
        entity.setStorageNo(request.getStorageNo());
        entity.setBillCreateType(request.getCreateType());
        entity.setDi(request.getDi());
        //明细后面构建
//        entity.setDetails(request.getDetails());
        return entity;
    }

    protected void fillFreightEntityBySaleOrder(EshopSaleOrderSimpleEntity saleOrder, FreightBillNoSyncEntity entity) {
        //注意:不要重新覆盖tradeId
        entity.setStoreCode(saleOrder.getStoreCode());
        entity.setTradeStatus(saleOrder.getTradeStatus());
        entity.setPayTime(saleOrder.getPayTime());
        entity.setPlatformJson(saleOrder.getPlatformJson());
        entity.setPlatformStockId(saleOrder.getPlatformStockId());
        entity.setCustomerReceiverProvince(saleOrder.getCustomerReceiverProvince());
        entity.setCustomerReceiverCity(saleOrder.getCustomerReceiverCity());
        entity.setCustomerReceiverDistrict(saleOrder.getCustomerReceiverDistrict());
        entity.setCustomerReceiverTown(saleOrder.getCustomerReceiverTown());
    }


    protected boolean isCyclePurchaseOrder(List<MarkData> deliverMarks) {
        return CollectionUtils.isNotEmpty(deliverMarks) &&
                deliverMarks.stream()
                        .anyMatch(m -> m != null && BaseOrderMarkEnum.CYCLE_PURCHASE.equals(m.getOrderMarkEnum()));
    }

    protected void mergeMarkDataList(FreightBillNoSyncEntity entity, List<MarkData> originOrderMarkDataList) {
        //仓储传了标记，这边又查了原单标记，通用用原单标记，防止标记重复。
        //处理合单发货时主单标记丢失问题
        List<MarkData> mergeMarkDataList;
        List<MarkData> otherMarkDataList;
        List<MarkData> mainMarkDataList;
        //周期购的单子标记都在各个周期子单上，主单不会提交到发货单，所以以发货单标记为主。
        if (isCyclePurchaseOrder(entity.getDeliverMarks())) {
            mergeMarkDataList = new ArrayList<>(entity.getDeliverMarks());
            mainMarkDataList = new ArrayList<>(entity.getDeliverMarks());
            otherMarkDataList = new ArrayList<>(originOrderMarkDataList);
        } else {
            mergeMarkDataList = new ArrayList<>(originOrderMarkDataList);
            mainMarkDataList = new ArrayList<>(originOrderMarkDataList);
            otherMarkDataList = new ArrayList<>(entity.getDeliverMarks());
        }
        //对标记去重。
        for (MarkData otherMark : otherMarkDataList) {
            if (otherMark == null) {
                continue;
            }
            boolean isPresent = mainMarkDataList.stream()
                    .anyMatch(m -> m != null && m.getOrderMarkEnum() != null
                            && m.getOrderMarkEnum().equals(otherMark.getOrderMarkEnum()));
            if (isPresent) {
                continue;
            }
            mergeMarkDataList.add(otherMark);
        }
        entity.setDeliverMarks(mergeMarkDataList);
    }

    private boolean checkEntityIsFirst(List<EshopFreightSyncRecordDetail> oldRecordDetailList) {
        if (CollectionUtils.isEmpty(oldRecordDetailList)) {
            return true;
        }
        return oldRecordDetailList.stream().noneMatch(r -> r.getSyncType() != SyncType.REFUND);
    }

    private boolean checkEntityIsLast(EshopSaleOrderSimpleEntity saleOrder,
                                      Map<String, BigDecimal> deliveredQtyMap,
                                      List<BillDeliverDetailDTO> deliverDetailList,
                                      FreightBillNoSyncEntity entity) {
        Map<String, EshopSaleOrderDetailSimpleEntity> groupDetails = saleOrder.getGroupDetails();
        if (CollectionUtils.isEmpty(groupDetails)) {
            return true;
        }
        List<FreightBillNoSyncDetailEntity> details = entity.getDetails();
        Map<String, List<FreightBillNoSyncDetailEntity>> listMap =
                details.stream().collect(Collectors.groupingBy(FreightBillNoSyncDetailEntity::getOid));
        for (Map.Entry<String, EshopSaleOrderDetailSimpleEntity> entry : groupDetails.entrySet()) {
            EshopSaleOrderDetailSimpleEntity saleOrderDetailSimple = entry.getValue();
            if (TradeStatus.ALL_CLOSED.equals(saleOrderDetailSimple.getTradeStatus())) {
                //交易关闭的订单明细是不用发货的
                continue;
            }
            String oid = saleOrderDetailSimple.getOid();
            if (listMap.containsKey(oid)) {
                boolean allMatch = listMap.get(oid).stream().allMatch(FreightBillNoSyncDetailEntity::isLastOne);
                if (allMatch) {
                    continue;
                }
            }
            BigDecimal deliveredQty = BigDecimal.ZERO;
            String key = this.getCacheQtyKey(saleOrder.getTradeId(), oid);
            if (deliveredQtyMap.containsKey(key)) {
                deliveredQty = deliveredQtyMap.get(key);
            }
            BigDecimal qty = saleOrderDetailSimple.getQty();
            if (qty.compareTo(deliveredQty) > 0) {
                return false;
            }
        }
        return true;
    }

    private void buildSyncType(EshopFactory factory,
                               List<SyncFreightBillNoResultDetail> results,
                               EshopFreightSyncRecord record,
                               SyncFreightBillNoRequest request,
                               Map<String, List<BillDeliverDetailDTO>> deliverBillMap,
                               Map<BigInteger, List<DeliverFreightSimpleEntity>> deliverFreightMap,
                               List<EshopFreightSyncRecordDetail> oldRecordDetailList,
                               FreightBillNoSyncEntity entity) {
        BillCreateType createType = request.getCreateType();
        SelfDeliveryModeEnum deliveryMode = request.getDeliveryMode();
        String tradeId = entity.getTradeId();
        boolean wholeGift = record.getDetails().stream().allMatch(EshopFreightSyncRecordDetail::isGift);
        try {
            if (CollectionUtils.isNotEmpty(sendGoodsConfig.getNotSendShopTypeList()) && sendGoodsConfig.getNotSendShopTypeList().contains(factory.getShopType())) {
                //云订货线下订单无需同步
                record.setSyncType(SyncType.DOT_SYNC);
                entity.setSyncType(SyncType.DOT_SYNC);
                results.addAll(SendUtils.to(entity, SyncFreightStatus.SUCCESS, ""));
            }
            if (createType == BillCreateType.FROM_ORDER_REFUND) {
                if (factory.getFeature(EshopOrderRefundSendFeature.class) != null) {
                    entity.setSyncType(SyncType.REFUND);
                    record.setSyncType(entity.getSyncType());
                } else {
                    record.setSyncType(SyncType.DOT_SYNC);
                    entity.setSyncType(SyncType.DOT_SYNC);
                    results.addAll(SendUtils.to(entity, SyncFreightStatus.NOT_NEED, "该平台暂不支持售后补发同步物流单号"));
                }
                return;
            }
            if (createType != BillCreateType.FROM_DOWNLOAD_ORDER) {
                record.setSyncType(SyncType.DOT_SYNC);
                entity.setSyncType(SyncType.DOT_SYNC);
                results.addAll(SendUtils.to(entity, SyncFreightStatus.SUCCESS, ""));
                return;
            }
            if (deliveryMode == SelfDeliveryModeEnum.DELIVERY_BY_SELF
                    || deliveryMode == SelfDeliveryModeEnum.DELIVERY_BY_SIMPLE
                    || deliveryMode == SelfDeliveryModeEnum.INTRA_CITY_DISTRIBUTION) {
                entity.setSyncType(SyncType.SAME_CITY);
                return;
            }
            if (deliveryMode == SelfDeliveryModeEnum.CUSTOMER_COLLECT) {
                entity.setSyncType(SyncType.SELF_PICK);
                return;
            }
            if (isResendOrAppendSend(factory, entity, oldRecordDetailList, deliverFreightMap)) {
                //如果平台不支持追加或者修改物流,继续往后判断拆分等
                if (SyncType.APPEND_SEND.equals(entity.getSyncType()) && null != factory.getFeature(EshopOrderAppendSendFeature.class)) {
                    return;
                } else if (SyncType.RESEND.equals(entity.getSyncType()) && null != factory.getFeature(EshopOrderResendFeature.class)) {
                    return;
                }
            }
//            if (isResend(entity, oldRecordDetailList)) {
//                entity.setSyncType(SyncType.RESEND);
//                return;
//            }
            if (wholeGift) {
                entity.setSyncType(SyncType.GIFT);
                //支持礼物发货的返回，不支持礼物发货的平台降级成后续同步类型
                EshopOrderGiftSendFeature giftSendFeature = factory.getFeature(EshopOrderGiftSendFeature.class);
                if (giftSendFeature != null) {
                    return;
                }
            }
            List<BillDeliverDetailDTO> deliverDetailDTOList = deliverBillMap.get(tradeId);
            isRealSplit(factory, results, entity, record, deliverDetailDTOList);
            setAppendSend(factory, entity);
        } catch (Exception ex) {
            logger.error("账套ID{}订单号{}构建发货类型失败：{}，最终默认使用整单发货", CurrentUser.getProfileId(), tradeId, ex.getMessage(), ex);
            entity.setSyncType(SyncType.WHOLE);
        }
    }

    private boolean isResendOrAppendSend(EshopFactory factory,
                                         FreightBillNoSyncEntity entity,
                                         List<EshopFreightSyncRecordDetail> oldRecordDetails,
                                         Map<BigInteger, List<DeliverFreightSimpleEntity>> deliverFreightMap) {

        if (CollectionUtils.isEmpty(oldRecordDetails)) {
            return false;
        }
        //无同步成功记录直接返回
        List<EshopFreightSyncRecordDetail> successList = oldRecordDetails.stream().filter(
                x -> x.getSyncStatus().equals(SyncFreightStatus.SUCCESS)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(successList)) {
            return false;
        }
        successList.sort((x, y) -> y.getUpdateTime().compareTo(x.getUpdateTime()));


        Map<BigInteger, List<EshopFreightSyncRecordDetail>> oldRecordDetailGroupByTaskDetailMap =
                successList.stream().collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getWarehouseTaskDetailId));

        Map<BigInteger, List<EshopFreightSyncRecordDetail>> oldRecordDetailGroupByVchcodeMap =
                successList.stream().collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getDeliverOrderId));

        List<Integer> shopTypes = platformEshopConfig.getAppendSendCheckSendStatusShopTypeList();

        //根据任务单id初次匹配已发过的明细
        List<FreightBillNoSyncDetailEntity> alreadySentDetails =
                entity.getDetails().stream().filter(detail ->
                        oldRecordDetailGroupByTaskDetailMap.containsKey(detail.getWarehouseTaskDetailId())).collect(Collectors.toList());
        //不影响需求38488已支持的店铺,还是只按老逻辑计算Resend
        if (CollectionUtils.isNotEmpty(alreadySentDetails)) {
            entity.setSyncType(SyncType.RESEND);
        }
        if (shopTypes.contains(factory.getShopType().getCode())) {
            return SyncType.RESEND.equals(entity.getSyncType());
        }


        //未发货不额外计算追加/修改
        //避免拆分未发完订单计算出错
        if (!platformEshopConfig.getResendIgnoreTradeStatusShopTypeList().contains(factory.getShopType().getCode())
                && !TradeStatus.WAIT_BUYER_CONFIRM_GOODS.equals(entity.getTradeStatus()) &&
                !TradeStatus.TRADE_FINISHED.equals(entity.getTradeStatus())) {
            return SyncType.RESEND.equals(entity.getSyncType());
        }

        //拆分
        if (entity.isSpilt()) {
            //1.通过任务单匹配到
            if (CollectionUtils.isNotEmpty(alreadySentDetails)) {
                List<EshopFreightSyncRecordDetail> matchedRecords =
                        oldRecordDetailGroupByTaskDetailMap.get(alreadySentDetails.get(0).getWarehouseTaskDetailId());
                if (checkDeliverFreightExits(matchedRecords, deliverFreightMap)) {
                    entity.setSyncType(SyncType.APPEND_SEND);
                } else {
                    entity.setSyncType(SyncType.RESEND);
                }
                //补充上次同步成功的物流信息
                //就用最近的物流同步信息,如果匹配成功
//                if (CollectionUtils.isEmpty(entity.getLastFreightInfoList())) {
                matchedRecords.sort((x, y) -> y.getUpdateTime().compareTo(x.getUpdateTime()));
                SyncFreightInfo freightInfo = toFreightInfo(matchedRecords.get(0), matchedRecords.get(0).getFreightBillNo());
                entity.setLastFreightInfoList(Collections.singletonList(freightInfo));
//                }
                return true;
            }

            //2.尝试再通过vchcode匹配发货单
            alreadySentDetails = entity.getDetails().stream().filter(detail ->
                    oldRecordDetailGroupByVchcodeMap.containsKey(detail.getDeliverOrderId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(alreadySentDetails)) {
                List<EshopFreightSyncRecordDetail> matchedRecords =
                        oldRecordDetailGroupByVchcodeMap.get(alreadySentDetails.get(0).getDeliverOrderId());
                if (checkDeliverFreightExits(matchedRecords, deliverFreightMap)) {
                    entity.setSyncType(SyncType.APPEND_SEND);
                } else {
                    entity.setSyncType(SyncType.RESEND);
                }
                //补充上次同步成功的物流信息
//                if (CollectionUtils.isEmpty(entity.getLastFreightInfoList())) {
                matchedRecords.sort((x, y) -> y.getUpdateTime().compareTo(x.getUpdateTime()));
                SyncFreightInfo freightInfo = toFreightInfo(matchedRecords.get(0), matchedRecords.get(0).getFreightBillNo());
                entity.setLastFreightInfoList(Collections.singletonList(freightInfo));
//                }
                return true;
            }
            //3.最后方案,比对明细的oid+发货数量(还原后又拆的)
            List<EshopFreightSyncRecordDetail> mappingOldRecords = new ArrayList<>();
            for (FreightBillNoSyncDetailEntity detail : entity.getDetails()) {
                if (detail.isDeleted() || detail.isManual()) {
                    continue;
                }
                Optional<EshopFreightSyncRecordDetail> optionalOldRecord
                        = successList.stream().filter(old -> detail.getOid().equals(old.getOid()) && detail.getQty().equals(old.getSyncQty())).findFirst();
                //不能完全匹配,直接返回
                if (!optionalOldRecord.isPresent()) {
                    return false;
                }
                mappingOldRecords.add(optionalOldRecord.get());
            }
            if (CollectionUtils.isEmpty(mappingOldRecords)) {
                return false;
            }
            Map<BigInteger, List<EshopFreightSyncRecordDetail>> oidMappingRecords =
                    mappingOldRecords.stream().collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getWarehouseTaskId));
            if (oidMappingRecords.size() == 1) {
                //已发的数据在一个任务单里,再判断是否还存在于发货单物流信息中
                if (checkDeliverFreightExits(mappingOldRecords, deliverFreightMap)) {
                    entity.setSyncType(SyncType.APPEND_SEND);
                } else {
                    entity.setSyncType(SyncType.RESEND);
                }
                if (CollectionUtils.isEmpty(entity.getLastFreightInfoList())) {
                    SyncFreightInfo freightInfo = toFreightInfo(mappingOldRecords.get(0), mappingOldRecords.get(0).getFreightBillNo());
                    entity.setLastFreightInfoList(Collections.singletonList(freightInfo));
                }
                return true;
            } else {
                //已发的数据在不同任务单,认定为追加
                entity.setSyncType(SyncType.APPEND_SEND);
                return true;
            }
        } else {
            //整单,有成功的就认定是整单已发,只用比对该物流信息是否还在使用,还在使用则追加新包裹
            if (checkDeliverFreightExits(successList, deliverFreightMap)) {
                entity.setSyncType(SyncType.APPEND_SEND);
            } else {
                entity.setSyncType(SyncType.RESEND);
            }
            //补充上次同步成功的物流信息,如果还原后未匹配到
//            if (CollectionUtils.isEmpty(entity.getLastFreightInfoList())) {
            SyncFreightInfo freightInfo = toFreightInfo(successList.get(0), successList.get(0).getFreightBillNo());
            entity.setLastFreightInfoList(Collections.singletonList(freightInfo));
//            }
            return true;
        }
    }

    private boolean checkDeliverFreightExits(List<EshopFreightSyncRecordDetail> oldRecords,
                                             Map<BigInteger, List<DeliverFreightSimpleEntity>> deliverFreightMap) {
        if (CollectionUtils.isEmpty(oldRecords) || CollectionUtils.isEmpty(deliverFreightMap)) {
            return false;
        }
        return oldRecords.stream().anyMatch(oldRecord -> StringUtils.isNotEmpty(oldRecord.getFreightBillNo())
                && deliverFreightMap.containsKey(oldRecord.getDeliverOrderId())
                && deliverFreightMap.get(oldRecord.getDeliverOrderId()).stream()
                .anyMatch(deliverFreight -> StringUtils.isNotEmpty(deliverFreight.getFreightBillno())
                        && deliverFreight.getFreightBillno().equals(oldRecord.getFreightBillNo())));
    }

    /**
     * 构建追加包裹发货类型
     *
     * @param factory factory
     * @param entity  待发货参数
     */
    private void setAppendSend(EshopFactory factory, FreightBillNoSyncEntity entity) {
        if (!SyncType.SPLIT.equals(entity.getSyncType())) {
            return;
        }
        //如果未实现追加发货，则不设置追加发货
        EshopOrderAppendSendFeature appendSendFeature = factory.getFeature(EshopOrderAppendSendFeature.class);
        if (null == appendSendFeature) {
            return;
        }
        List<FreightBillNoSyncDetailEntity> details = entity.getDetails();
        //支持按发货状态来判断是否追加包裹的店铺类型
        List<Integer> shopTypes = platformEshopConfig.getAppendSendCheckSendStatusShopTypeList();
        List<Integer> checkOriginQtyShopTypes = platformEshopConfig.getAppendSendNeedCheckOriginQtyShopTypeList();
        //是否需要按发货状态来判断追加包裹
        boolean needCheckSendStatus =
                CollectionUtils.isNotEmpty(shopTypes) && shopTypes.contains(factory.getShopType().getCode());

        boolean needCheckOriginQty =
                CollectionUtils.isNotEmpty(checkOriginQtyShopTypes) && checkOriginQtyShopTypes.contains(factory.getShopType().getCode());
        boolean needAppendSend = false;
        //当前待发货订单是否是已发货的状态
        boolean isHasSendStatus =
                entity.getTradeStatus().equals(TradeStatus.WAIT_BUYER_CONFIRM_GOODS) || entity.getTradeStatus().equals(TradeStatus.TRADE_FINISHED);
        for (FreightBillNoSyncDetailEntity detail : details) {
            BigDecimal totalUnitQty = detail.getOriginalQty();
            if (totalUnitQty.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            //已发货数量
            BigDecimal shippedQty = detail.getDeliveredQty();
            boolean hasShipped = shippedQty.compareTo(BigDecimal.ZERO) > 0;
            if (needCheckSendStatus && (isHasSendStatus || hasShipped)) {
                needAppendSend = true;
                break;
            }

            BigDecimal totalShipQty = MoneyUtils.add(detail.getQty(), shippedQty, Money.Qty);
            if (totalShipQty.compareTo(totalUnitQty) < 0) {
                //当前明细是部分发货
                detail.setPartialSend(true);
            }
            //是否跟原单原始数量比对(实际购买数量)
            if (!needCheckOriginQty) {
                continue;
            }
            //当总发货数量<总订单数量时，走拆分发货，反之追加包裹
            boolean doSplitSend = totalShipQty.compareTo(totalUnitQty) <= 0;
            if (doSplitSend) {
                continue;
            }
            needAppendSend = true;
            break;
        }
        if (needAppendSend) {
            entity.setSyncType(SyncType.APPEND_SEND);
        }

    }

    protected void buildDetailByOrderDetail(FreightBillNoSyncDetailEntity newDetail,
                                            EshopSaleOrderDetailSimpleEntity saleOrderDetail) {
        if (saleOrderDetail != null) {
            newDetail.setPlatformPtypeId(saleOrderDetail.getPlatformPtypeId());
            newDetail.setPlatformPtypeXcode(saleOrderDetail.getPlatformPtypeXcode());
            newDetail.setPlatformSkuId(saleOrderDetail.getPlatformSkuId());
            newDetail.setPlatformPtypeName(saleOrderDetail.getPlatformPtypeName());
            newDetail.setTotalQty(saleOrderDetail.getQty());
            newDetail.setOriginalQty(saleOrderDetail.getQty());
            newDetail.setProductMaterialType(saleOrderDetail.getProductMaterialType());
        } else {
            newDetail.setManual(true);
            newDetail.setTotalQty(BigDecimal.ZERO);
            newDetail.setOriginalQty(BigDecimal.ZERO);
        }
    }

    protected void reBuildTotalQtyWhenComboChange(FreightBillNoSyncDetailEntity newDetail,
                                                  List<BillDeliverDetailDTO> deliverDetailList) {
        String oid = newDetail.getOid();
        if (CollectionUtils.isEmpty(deliverDetailList)) {
            return;
        }
        List<BillDeliverDetailDTO> dtoList =
                deliverDetailList.stream().filter(x -> x.getOnlineDetailId().equals(oid)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        double sum;
        if (newDetail.isCombo()) {
            sum =
                    dtoList.stream().filter(BillDeliverDetailDTO::isCombo).mapToDouble(x -> x.getUnitQty().doubleValue()).sum();
        } else {
            sum = dtoList.stream().filter(x -> !x.isCombo()).mapToDouble(x -> x.getUnitQty().doubleValue()).sum();
        }
        newDetail.setTotalQty(BigDecimal.valueOf(sum));
        // 查询出质检信息赋值
        Optional<BillDeliverDetailDTO> first =
                dtoList.stream().filter(x -> StringUtils.isNotEmpty(x.getPlatformQualityRefundInterceptionCode())).findFirst();
        first.ifPresent(billDeliverDetailDTO -> newDetail.setPlatformQualityRefundInterceptionCode(billDeliverDetailDTO.getPlatformQualityRefundInterceptionCode()));
        Optional<BillDeliverDetailDTO> first1 =
                dtoList.stream().filter(x -> StringUtils.isNotEmpty(x.getPlatformQualityBtypeInsureTotal())).findFirst();
        first1.ifPresent(billDeliverDetailDTO -> newDetail.setPlatformQualityBtypeInsureTotal(billDeliverDetailDTO.getPlatformQualityBtypeInsureTotal()));
        Optional<BillDeliverDetailDTO> first2 =
                dtoList.stream().filter(x -> StringUtils.isNotEmpty(x.getPlatformQualityBtypeProduct())).findFirst();
        first2.ifPresent(billDeliverDetailDTO -> newDetail.setPlatformQualityBtypeProduct(billDeliverDetailDTO.getPlatformQualityBtypeProduct()));
        Optional<BillDeliverDetailDTO> first3 =
                dtoList.stream().filter(x -> StringUtils.isNotEmpty(x.getPlatformQualityBtypeCode())).findFirst();
        first3.ifPresent(billDeliverDetailDTO -> newDetail.setPlatformQualityBtypeCode(billDeliverDetailDTO.getPlatformQualityBtypeCode()));

    }

    protected void doBuildSerialNoList(Map<BigInteger, List<SyncFreightBillNoDetail>> requestDetailsMap,
                                       FreightBillNoSyncDetailEntity newDetail) {
        List<SyncFreightBillNoDetail> requestDetails = requestDetailsMap.get(newDetail.getDeliverOrderDetailId());
        if (CollectionUtils.isEmpty(requestDetails)) {
            return;
        }
        SyncFreightBillNoDetail syncFreightBillNoDetail = requestDetails.get(0);
        newDetail.setSerialNoSonList(syncFreightBillNoDetail.getSerialNoSonList());
        newDetail.setPlatformCodeList(syncFreightBillNoDetail.getPlatformCodeList());
        newDetail.setFullBarcode(syncFreightBillNoDetail.getFullBarcode());
    }

    private Map<String, BigDecimal> buildDeliveredQtyMap(List<EshopFreightSyncRecordDetail> oldRecordDetails,
                                                         List<EshopFreightSyncRecordDetail> groupRecordDetailList) {
        Map<String, BigDecimal> deliveredQtyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(groupRecordDetailList)) {
            for (EshopFreightSyncRecordDetail detail : groupRecordDetailList) {
                String key = this.getCacheQtyKey(detail.getTradeId(), detail.getOid());
                BigDecimal cacheQty = getCacheQty(key);
                deliveredQtyMap.put(key, cacheQty);
            }
        }
        if (CollectionUtils.isNotEmpty(oldRecordDetails)) {
            for (EshopFreightSyncRecordDetail detail : oldRecordDetails) {
                if (detail.getSyncStatus() != SyncFreightStatus.SUCCESS && detail.getSyncStatus() != SyncFreightStatus.ING) {
                    continue;
                }
                String key = this.getCacheQtyKey(detail.getTradeId(), detail.getOid());
                BigDecimal deliveredQty = deliveredQtyMap.getOrDefault(key, BigDecimal.ZERO);
                deliveredQty = deliveredQty.add(detail.getSyncQty());
                deliveredQtyMap.put(key, deliveredQty);
            }
        }
        return deliveredQtyMap;
    }

    private List<SyncFreightBillNoRequest> filterRequest(List<SyncFreightBillNoRequest> requestList,
                                                         List<SyncFreightBillNoResponse> errorResult,
                                                         boolean isSend) {
        List<SyncFreightBillNoRequest> localSendList = new ArrayList<>();
        List<SyncFreightBillNoRequest> realRequestList = new ArrayList<>();
        for (SyncFreightBillNoRequest request : requestList) {
            //打印不验证,发货验证
            if (isSend && CollectionUtils.isEmpty(request.getFreightInfoList()) && sendGoodsConfig.isCheckFreightEnabled()) {
                SyncFreightBillNoResponse buildResponse = buildResponse(request, new RuntimeException("缺少物流信息"));
                errorResult.add(buildResponse);
                continue;
            }
            //手工开单等不处理
            if (!BillCreateType.FROM_DOWNLOAD_ORDER.equals(request.getCreateType()) &&
                    !BillCreateType.FROM_ORDER_REFUND.equals(request.getCreateType())) {
                localSendList.add(request);
                if (isSend) {
                    SyncFreightBillNoResponse buildResponse = buildResponse(request);
                    errorResult.add(buildResponse);
                }
                continue;
            }
            realRequestList.add(request);
        }
        if (isSend) {
            doUpdateSaleOrderSendStatus(localSendList);
        }
        return realRequestList;
    }


    private SyncFreightBillNoResponse buildResponse(SyncFreightBillNoRequest request, Throwable throwable) {
        logger.error("账套ID{}订单号{}错误信息：{}", CurrentUser.getProfileId(), request.getTradeId(), throwable.getMessage(),
                throwable);
        SyncFreightBillNoResponse response = buildResponse(request);
        String errorMsg = "操作失败：" + throwable.getMessage();
        response.setStatus(SyncFreightStatus.FAIL);
        response.setMessage(errorMsg);
        for (SyncFreightBillNoResultDetail detail : response.getDetails()) {
            detail.setStatus(SyncFreightStatus.FAIL);
            detail.setMessage(errorMsg);
        }
        return response;
    }

    private SyncFreightBillNoResponse buildFailResponse(SyncFreightBillNoRequest request, String errorMessage) {
        SyncFreightBillNoResponse response = buildResponse(request);
        response.setStatus(SyncFreightStatus.FAIL);
        response.setMessage(errorMessage);
        for (SyncFreightBillNoResultDetail detail : response.getDetails()) {
            detail.setStatus(SyncFreightStatus.FAIL);
            detail.setMessage(errorMessage);
        }
        return response;
    }

    private SyncFreightBillNoResponse buildResponse(SyncFreightBillNoRequest request) {
        SyncFreightBillNoResponse response = new SyncFreightBillNoResponse();
        BeanUtils.copyProperties(request, response);
        response.setStatus(SyncFreightStatus.SUCCESS);
        response.setDetails(new ArrayList<>());
        response.setMessage("同步成功");
        for (SyncFreightBillNoDetail requestDetail : request.getDetails()) {
            SyncFreightBillNoResultDetail detail = new SyncFreightBillNoResultDetail();
            BeanUtils.copyProperties(requestDetail, detail);
            detail.setStatus(SyncFreightStatus.SUCCESS);
            detail.setMessage(response.getMessage());
            response.getDetails().add(detail);
        }
        return response;
    }


    @NotNull
    protected static List<FreightBillNoSyncEntity> buildEntityByRule(EshopFactory factory,
                                                                     SyncFreightBillNoRequest request, List<FreightBillNoSyncEntity> syncEntities) {
        SyncRule syncRule = factory.getClass().getAnnotation(SyncRule.class);
        if (syncRule == null) {
            return syncEntities;
        }
        if (!syncRule.merge()) {
            return syncEntities;
        }
        //syncEntities.size() > 1 说明一个发货单对应多个原单，是合单发货
        if (syncEntities.size() < 2) {
            return syncEntities;
        }
        String mainOrder;
        if (request.getTradeId().contains(";")) {
            String[] tradeIds = request.getTradeId().split(";");
            if (tradeIds.length > 0) {
                mainOrder = tradeIds[0];
            } else {
                mainOrder = request.getTradeId();
            }
        } else {
            mainOrder = request.getTradeId();
        }
        Optional<FreightBillNoSyncEntity> mainSyncEntityOp =
                syncEntities.stream().filter(x -> StringUtils.equals(x.getTradeId(), mainOrder)).findFirst();
        //必然存在
        if (mainSyncEntityOp.isPresent()) {
            FreightBillNoSyncEntity mainFreightBillNoSyncEntity = mainSyncEntityOp.get();
            List<FreightBillNoSyncDetailEntity> detailEntities = new ArrayList<>();
            List<String> mergeTradeIds = new ArrayList<>();
            for (FreightBillNoSyncEntity syncEntity : syncEntities) {
                mergeTradeIds.add(syncEntity.getTradeId());
                detailEntities.addAll(syncEntity.getDetails());
            }
            mainFreightBillNoSyncEntity.setDetails(detailEntities);
            mainFreightBillNoSyncEntity.setMergeTradeIds(mergeTradeIds);
            syncEntities = new ArrayList<>(Collections.singletonList(mainFreightBillNoSyncEntity));
        }
        return syncEntities;
    }


    protected SyncFreightInfo toFreightInfo(EshopFreightSyncRecordDetail oldDetail, String freightBillNo) {
        SyncFreightInfo freightInfo = new SyncFreightInfo();
        String code = oldDetail.getFreightCode();
        if (StringUtils.isEmpty(code) || ":".equals(code)) {
            freightInfo.setFreightName("");
        } else {
            String[] freightCodeName = code.split(":");
            freightInfo.setFreightCode(freightCodeName[0]);
            if (freightCodeName.length > 1) {
                freightInfo.setFreightName(freightCodeName[1]);
            } else {
                freightInfo.setFreightName(freightCodeName[0]);
            }
        }
        freightInfo.setPlatformFreightId(oldDetail.getPlatformFreightId());
        freightInfo.setFreightBillNo(freightBillNo);
        freightInfo.setSyncOrderSequence(oldDetail.getSyncOrderSequence());
        freightInfo.setSyncOrderDetailSequence(oldDetail.getSyncOrderDetailSequence());
        return freightInfo;
    }

    protected void updateSyncType(EshopFreightSyncRecord record, SyncType syncType) {
        record.setSyncType(syncType);
        for (EshopFreightSyncRecordDetail detail : record.getDetails()) {
            detail.setSyncType(syncType);
        }
    }

    private boolean isResend(FreightBillNoSyncEntity entity, List<EshopFreightSyncRecordDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return false;
        }
        List<EshopFreightSyncRecordDetail> successList =
                details.stream().filter(x -> x.getSyncStatus().equals(SyncFreightStatus.SUCCESS)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(successList)) {
            return false;
        }
        Map<BigInteger, List<EshopFreightSyncRecordDetail>> listMap =
                successList.stream().collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getWarehouseTaskDetailId));
        return entity.getDetails().stream().anyMatch(detail -> listMap.containsKey(detail.getWarehouseTaskDetailId()));
    }

    private void updateSyncRecord(List<SyncFreightBillNoResponse> responseList) {
        List<FreightBillNoSyncRecordUpdateInfo> recordUpdateInfos = new ArrayList<>();
        List<FreightBillNoSyncRecordUpdateInfo> recordDetailUpdateInfos = new ArrayList<>();
        int maxMessageLength = 250;
        for (SyncFreightBillNoResponse response : responseList) {
            FreightBillNoSyncRecordUpdateInfo updateInfo = new FreightBillNoSyncRecordUpdateInfo();
            updateInfo.setId(response.getRecordId());
            updateInfo.setMessage(response.getMessage());
            if (updateInfo.getMessage() == null) {
                updateInfo.setMessage("");
            }
            if (updateInfo.getMessage().length() > maxMessageLength) {
                updateInfo.setMessage(updateInfo.getMessage().substring(0, maxMessageLength));
            }
            updateInfo.setStatus(response.getStatus());
            recordUpdateInfos.add(updateInfo);
            for (SyncFreightBillNoResultDetail detail : response.getDetails()) {
                FreightBillNoSyncRecordUpdateInfo detailUpdateInfo = new FreightBillNoSyncRecordUpdateInfo();
                detailUpdateInfo.setId(detail.getRecordDetailId());
                detailUpdateInfo.setMessage(detail.getMessage());
                if (StringUtils.isEmpty(detailUpdateInfo.getMessage())) {
                    detailUpdateInfo.setMessage("");
                }
                if (detailUpdateInfo.getMessage().length() > maxMessageLength) {
                    detailUpdateInfo.setMessage(detailUpdateInfo.getMessage().substring(0, maxMessageLength));
                }
                detailUpdateInfo.setStatus(detail.getStatus());
                detailUpdateInfo.setPlatformFreightId(detail.getPlatformFreightId());
                detailUpdateInfo.setDeliveryCode(detail.getDeliveryCode());
                recordDetailUpdateInfos.add(detailUpdateInfo);
            }
        }
        if (CollectionUtils.isNotEmpty(recordUpdateInfos)) {
            freightSyncRecordMapper.update(CurrentUser.getProfileId(), recordUpdateInfos);
            freightSyncRecordDetailMapper.update(CurrentUser.getProfileId(), recordDetailUpdateInfos);
        }
    }

    protected void appendFreightInfos(FreightBillNoSyncEntity entity, EshopFreightSyncRecord record,
                                      List<EshopFreightSyncRecordDetail> allRecordDetails, EshopSaleOrderSimpleEntity saleOrder, EshopFactory factory) {
        if (CollectionUtils.isEmpty(allRecordDetails)) {
            return;
        }
        if (!entity.isLast() && entity.getDetails().stream().noneMatch(FreightBillNoSyncDetailEntity::isLastOne)) {
            return;
        }
        Map<String, List<EshopFreightSyncRecordDetail>> recordDetailsMap = allRecordDetails.stream()
                .filter(rd -> !rd.isCallStatus() && rd.getSyncStatus() == SyncFreightStatus.ING)
                .collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getTradeId));
        List<EshopFreightSyncRecordDetail> details = recordDetailsMap.get(entity.getTradeId());
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        Map<String, List<EshopFreightSyncRecordDetail>> recordDetailMap = details.stream()
                .collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getFreightBillNo));
        for (String freightBillNo : recordDetailMap.keySet()) {
            List<EshopFreightSyncRecordDetail> recordDetails = recordDetailMap.get(freightBillNo);
            // 处理个别平台最后一张订单发货时，同步中的明细构建发货明细时缺少skuId的问题
            for (EshopFreightSyncRecordDetail detail : recordDetails) {
                if (saleOrder != null && CollectionUtils.isNotEmpty(saleOrder.getDetails())) {
                    EshopSaleOrderDetailSimpleEntity simpleEntity = saleOrder.getGroupDetails().get(detail.getOid());
                    if (simpleEntity != null) {
                        detail.setPlatformSkuId(simpleEntity.getPlatformSkuId());
                        detail.setPlatformPtypeName(simpleEntity.getPlatformPtypeName());
                        detail.setPlatformPtypeId(simpleEntity.getPlatformPtypeId());
                        detail.setTotalQty(simpleEntity.getQty());
                        detail.setPlatformXcode(simpleEntity.getPlatformPtypeXcode());
                    }
                }
            }
            SyncFreightInfo freightInfo = toFreightInfo(recordDetails.get(0), freightBillNo);
            recordDetails.forEach(detail -> detail.setOld(true));
            record.getDetails().addAll(recordDetails);
            freightInfo.setSyncDetails(recordDetails);
            freightInfo.setOldFreight(true);
            SendOrderUtil.getInstance().doMatchFreightMapping(freightInfo, platformEshopConfig, factory);
            entity.getFreightInfoList().add(freightInfo);
        }
    }


    private void doLockDeliveredQty(List<SyncFreightBillNoDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        try {
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            for (SyncFreightBillNoDetail detail : details) {
                //多个订单oid相同合单导致发货数量计算错误
                String key = this.getCacheQtyKey(detail.getTradeId(), detail.getOid());
                String oldVal = template.opsForValue().get(key);
                if (oldVal == null) {
                    template.opsForValue().set(key, detail.getQty().toString(), 10, TimeUnit.MINUTES);
                } else {
                    BigDecimal existVal = new BigDecimal(oldVal);
                    BigDecimal nowVal = MoneyUtils.add(existVal, detail.getQty(), Money.Qty);
                    template.opsForValue().set(key, nowVal.toString(), 10, TimeUnit.MINUTES);
                }
            }
        } catch (Exception ex) {
            logger.error("同步单号过程执行redis加锁失败" + ex.getMessage(), ex);
        }
    }

    protected String getCacheQtyKey(String tradeId, String oid) {
        return String.format("%s;%s", tradeId, oid);
    }

    private BigDecimal getCacheQty(String key) {
        try {
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            String exist = template.opsForValue().get(key);
            if (exist == null) {
                return BigDecimal.ZERO;
            }
            return MoneyUtils.round(new BigDecimal(exist), Money.Qty);
        } catch (Exception ex) {
            return BigDecimal.ZERO;
        }
    }

    private void checkSameFreight(FreightBillNoSyncEntity entity, EshopFactory factory) {
        if (CollectionUtils.isEmpty(entity.getLastFreightInfoList())) {
            return;
        }
        if (CollectionUtils.isEmpty(entity.getFreightInfoList())) {
            return;
        }
        PlatformBaseConfig config = factory.getConfig();
        Boolean sameFreightEnabled = config.getSyncSameFreightEnabled();
        Set<String> oldFreights = new HashSet<>();
        Set<String> newFreights = new HashSet<>();
        entity.getFreightInfoList().forEach(freightInfo -> newFreights.add(String.format("%s%s",
                freightInfo.getFreightCode(), freightInfo.getFreightBillNo())));
        entity.getLastFreightInfoList().forEach(freightInfo -> oldFreights.add(String.format("%s%s",
                freightInfo.getFreightCode(), freightInfo.getFreightBillNo())));
        newFreights.removeAll(oldFreights);
        if (newFreights.size() > 0) {
            return;
        }
        if (sameFreightEnabled) {
            entity.getDetails().forEach(x -> x.setOnlyModifyStatus(true));
        } else {
            throw new SyncCheckException(SyncFreightStatus.SUCCESS, "从上次同步成功后未修改物流，无需再次同步");
        }
    }

    private void doBuildOrderDetail(List<EshopSaleOrderDetailSimpleEntity> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        List<EshopSaleOrderDetailSimpleEntity> comboDetails =
                details.stream().filter(x -> x.getComboRowId().compareTo(BigInteger.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(comboDetails)) {
            return;
        }
        List<BigInteger> comboIds = details.stream()
                .map(EshopSaleOrderDetailSimpleEntity::getComboId)
                .filter(comboId -> comboId.compareTo(BigInteger.ZERO) > 0)
                .distinct().collect(Collectors.toList());
        List<ComboDetail> comboProductDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(comboIds)) {
            BigInteger profileId = CurrentUser.getProfileId();
            comboProductDetails = eshopBaseInfoMapper.batchQueryComboDetail(profileId, comboIds);
        }
        Map<BigInteger, List<ComboDetail>> comboProductDetailMap =
                comboProductDetails.stream().collect(Collectors.groupingBy(ComboDetail::getComboId));
        Map<BigInteger, List<EshopSaleOrderDetailSimpleEntity>> detailMap =
                comboDetails.stream().collect(Collectors.groupingBy(EshopSaleOrderDetailSimpleEntity::getComboRowId));
        for (EshopSaleOrderDetailSimpleEntity detail : details) {
            if (!detail.isCombo()) {
                continue;
            }
            BigInteger id = detail.getId();
            List<EshopSaleOrderDetailSimpleEntity> combos = detailMap.get(id);
            if (CollectionUtils.isEmpty(combos)) {
                continue;
            }
            EshopSaleOrderDetailSimpleEntity detailSimple = combos.get(0);
            detail.setPlatformPtypeId(detailSimple.getPlatformPtypeId());
            detail.setPlatformSkuId(detailSimple.getPlatformSkuId());
            detail.setPlatformPtypeXcode(detailSimple.getPlatformPtypeXcode());
            detail.setPlatformJson(detailSimple.getPlatformJson());
            detail.setTradeStatus(detailSimple.getTradeStatus());
            detail.setOtypeId(detailSimple.getOtypeId());
            detail.setProcessState(detailSimple.getProcessState());
            detail.setRefundStatus(detailSimple.getRefundStatus());
            List<ComboDetail> comboProducts = comboProductDetailMap.get(detail.getComboId());
            if (CollectionUtils.isNotEmpty(comboProducts)) {
                comboProducts.stream().filter(ComboDetail::getMainPtype).findFirst()
                        .ifPresent(comboDetail -> detail.setPtypeId(comboDetail.getPtypeId()));
            }
        }
    }

    private void loadPtypeCustomFieldValue(List<EshopSaleOrderDetailSimpleEntity> details, BigInteger profileId) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        //读取本地商品唯一码材质的自定义商品字段
        List<PubCustomFiledConfigInfo> pubCustomFiledConfigs =
                customFieldConfigMapper.queryCustomFieldConfig(profileId, CustomFieldSubTypeConstant.PRODUCT_TYPE, "唯一码材质类型");
        if (CollectionUtils.isEmpty(pubCustomFiledConfigs)) {
            return;
        }
        PubCustomFiledConfigInfo pubCustomFiledConfigInfo = pubCustomFiledConfigs.get(0);
        String dataField = pubCustomFiledConfigInfo.getDataField();
        if (StringUtils.isEmpty(dataField)) {
            return;
        }
        List<BigInteger> ptypeIdList = details.stream().map(EshopSaleOrderDetailSimpleEntity::getPtypeId)
                .filter(ptypeId -> BigInteger.ZERO.compareTo(ptypeId) < 0).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ptypeIdList)) {
            return;
        }
        QueryCustomFieldParam param = new QueryCustomFieldParam();
        param.setProfileId(profileId);
        param.setSubType(CustomFieldSubTypeConstant.PRODUCT_TYPE);
        param.setFieldName(dataField);
        param.setPtypeIds(ptypeIdList);
        List<PtypeCustomFiledInfo> ptypeCustomFiledInfos = customFieldBaseInfoMapper.queryCustomFieldValues(param);
        if (CollectionUtils.isEmpty(ptypeCustomFiledInfos)) {
            return;
        }
        Map<BigInteger, List<PtypeCustomFiledInfo>> fieldValuesGroup = ptypeCustomFiledInfos.stream()
                .collect(Collectors.groupingBy(PtypeCustomFiledInfo::getPtypeId));
        for (EshopSaleOrderDetailSimpleEntity detail : details) {
            List<PtypeCustomFiledInfo> ptypeFieldValues = fieldValuesGroup.getOrDefault(detail.getPtypeId(),
                    new ArrayList<>());
            //同一个商品的材质类型字段值必然只有一个
            if (CollectionUtils.isNotEmpty(ptypeFieldValues)) {
                detail.setProductMaterialType(ptypeFieldValues.get(0).getFieldValue());
            } else {
                detail.setProductMaterialType(EMPTY);
            }
        }
    }

    protected Map<String, EshopSaleOrderSimpleEntity> loadSaleOrders(List<String> tradeIds, List<String> refundIds,
                                                                     BigInteger otypeId) {
        if (CollectionUtils.isEmpty(tradeIds) && CollectionUtils.isEmpty(refundIds)) {
            throw new RuntimeException("发货实体的订单号不能为空！");
        }
        List<String> tidList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tradeIds)) {
            tidList.addAll(tradeIds);
        }
        if (CollectionUtils.isNotEmpty(refundIds)) {
            tidList.addAll(refundIds);
        }
        BigInteger profileId = CurrentUser.getProfileId();
        List<EshopSaleOrderSimpleEntity> orders = eshopOrderMapper.listSaleOrders(profileId, otypeId, tidList);
        List<BigInteger> orderIds = orders.stream().map(EshopSaleOrderSimpleEntity::getId).collect(Collectors.toList());
        List<EshopSaleOrderDetailSimpleEntity> details = eshopOrderMapper.listSaleOrderDetails(profileId, orderIds);
        List<EshopSaleOrderDetailSimpleEntity> advanceOrderDetails =
                eshopOrderMapper.listAdvanceOrderDetails(profileId, otypeId, tidList);
        List<EshopOrderSimpleMarkEntity> eshopOrderMarkEntities =
                eshopOrderMapper.listEshopOrderMark(CurrentUser.getProfileId(), otypeId, tradeIds);
        Map<String, List<EshopOrderSimpleMarkEntity>> markGroup = null;
        if (CollectionUtils.isNotEmpty(eshopOrderMarkEntities)) {
            markGroup =
                    eshopOrderMarkEntities.stream().collect(Collectors.groupingBy(EshopOrderSimpleMarkEntity::getTradeId));
        }
        if (CollectionUtils.isEmpty(details)) {
            throw new RuntimeException("发货实体有异常，通过订单号查询出的原单明细为空！");
        }
        doBuildOrderDetail(details);
        loadPtypeCustomFieldValue(details, profileId);
        Map<String, List<EshopSaleOrderDetailSimpleEntity>> advanceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(advanceOrderDetails)) {
            advanceMap =
                    advanceOrderDetails.stream().collect(Collectors.groupingBy(EshopSaleOrderDetailSimpleEntity::getOid));
        }
        Map<BigInteger, List<EshopSaleOrderDetailSimpleEntity>> groupDetails =
                details.stream().collect(Collectors.groupingBy(EshopSaleOrderDetailSimpleEntity::getOrderId));
        Map<String, EshopSaleOrderSimpleEntity> result = new HashMap<>();
        for (EshopSaleOrderSimpleEntity order : orders) {
            List<EshopSaleOrderDetailSimpleEntity> orderDetail = groupDetails.getOrDefault(order.getId(),
                    new ArrayList<>());
            order.setDetails(orderDetail);
            order.setGroupDetails(new HashMap<>());
            order.setGroupComboDetails(new HashMap<>());
            for (EshopSaleOrderDetailSimpleEntity detailSimple : orderDetail) {
                String oid = detailSimple.getOid();
                if (advanceMap.containsKey(oid)) {
                    List<EshopSaleOrderDetailSimpleEntity> mapDetails = advanceMap.get(oid);
                    if (CollectionUtils.isNotEmpty(mapDetails)) {
                        EshopSaleOrderDetailSimpleEntity tmpAdDetail = mapDetails.get(0);
                        int processState = tmpAdDetail.getProcessState();
                        detailSimple.setProcessState(processState);
                        detailSimple.setOrderId(tmpAdDetail.getOrderId());
                    }
                }
                if (detailSimple.getComboRowId().compareTo(BigInteger.ZERO) > 0) {
                    if (!order.getGroupComboDetails().containsKey(oid)) {
                        order.getGroupComboDetails().put(oid, new ArrayList<>());
                    }
                    order.getGroupComboDetails().get(oid).add(detailSimple);
                    continue;
                }
                order.getGroupDetails().put(oid, detailSimple);
            }
            if (CollectionUtils.isNotEmpty(markGroup) && CollectionUtils.isNotEmpty(markGroup.getOrDefault(order.getTradeId(), null))) {
                order.setMarkDataList(markGroup.get(order.getTradeId()).stream().filter(markEntity -> markEntity.getMarkCode() != null).map(markEntity -> {
                    MarkData markData = new MarkData();
                    markData.setOrderMarkEnum(BaseOrderMarkEnum.getBaseOrderMarkEnumByCode(markEntity.getMarkCode()));
                    markData.setBubble(markEntity.getBubble());
                    BaseMarkBigDataEntity bigData = JsonUtils.toObject(markEntity.getBigData(), BaseMarkBigDataEntity.class);
                    markData.setBigData(bigData);
                    return markData;
                }).collect(Collectors.toList()));
            }
            result.put(order.getTradeId(), order);
        }
        return result;
    }

    private Map<String, List<EshopFreightSyncRecordDetail>> loadSendRecordMap(List<String> tradeIds) {
        if (CollectionUtils.isEmpty(tradeIds)) {
            return new HashMap<>();
        }
        BigInteger profileId = CurrentUser.getProfileId();
        List<EshopFreightSyncRecordDetail> allRecordDetails = freightSyncRecordDetailMapper.listByTradeIds(profileId,
                tradeIds);
        if (CollectionUtils.isEmpty(allRecordDetails)) {
            return new HashMap<>();
        }
        allRecordDetails.forEach(rd -> {
            if (rd.getSyncTime() == null) {
                rd.setSyncTime(new Date(0));
            }
        });
        List<BigInteger> ids =
                allRecordDetails.stream().map(EshopFreightSyncRecordDetail::getId).collect(Collectors.toList());
        List<BatchInfo> batchInfoList = freightSyncRecordDetailMapper.listByRecordDetailsId(profileId, ids);
        if (CollectionUtils.isNotEmpty(batchInfoList)) {
            Map<BigInteger, List<BatchInfo>> batchMap =
                    batchInfoList.stream().collect(Collectors.groupingBy(BatchInfo::getRecordDetailId));
            for (EshopFreightSyncRecordDetail allRecordDetail : allRecordDetails) {
                allRecordDetail.setBatchInfos(batchMap.get(allRecordDetail.getId()));
            }
        }
        return allRecordDetails.stream().collect(Collectors.groupingBy(EshopFreightSyncRecordDetail::getTradeId));
    }

    private void isRealSplit(EshopFactory factory, List<SyncFreightBillNoResultDetail> results,
                             FreightBillNoSyncEntity entity, EshopFreightSyncRecord record, List<BillDeliverDetailDTO> detailDTOList) {
        EshopSystemParams params = factory.getParams();
        Boolean splitSendByFirstEnabled = params.getSplitSendByFirstEnabled();
        if (splitSendByFirstEnabled) {
            entity.setForceWholeSendEnabled(true);
            doBuildDefaultEntity(entity, record);
            return;
        }
        if (CollectionUtils.isEmpty(detailDTOList)) {
            doBuildDefaultEntity(entity, record);
            return;
        }

        //如果还有未提交的明细还是需要走拆分发货
        if (entity.getNoSubmitCount() > 0) {
            entity.setSyncType(SyncType.SPLIT);
            entity.setSpilt(true);
            entity.setLast(false);
            record.setLastOne(false);
            record.setSyncType(SyncType.SPLIT);
            return;
        }
        if (entity.isFirst() && entity.isLast()) {
            doBuildDefaultEntity(entity, record);
            return;
        }

        BigInteger taskId = record.getWarehouseTaskId();
        Map<BigInteger, List<BillDeliverDetailDTO>> listMap =
                detailDTOList.stream().collect(Collectors.groupingBy(BillDeliverDetailDTO::getWarehouseTaskId));
        if (listMap.size() > 1) {
            entity.setSyncType(SyncType.SPLIT);
            entity.setSpilt(true);
            record.setSyncType(SyncType.SPLIT);
            return;
        }
        if (!listMap.containsKey(taskId)) {
            boolean canSync = checkNeedSync(factory);
            if (!canSync) {
                logger.error("{}订单号{}拆分发货失败,订单号拆分数量大于1,请检查订单号拆分数量是否正确,当前单子的taskid:{}订单号拆分warehouseTaskId:{}",
                        factory.getShopType().getName(), entity.getTradeId(), taskId, JsonUtils.toJson(listMap));
                record.setSyncType(SyncType.DOT_SYNC);
                entity.setSyncType(SyncType.DOT_SYNC);
                results.addAll(SendUtils.to(entity, SyncFreightStatus.SUCCESS, ""));
                return;
            }
            entity.setSyncType(SyncType.SPLIT);
            entity.setSpilt(true);
            record.setSyncType(SyncType.SPLIT);
            return;
        }
        doBuildDefaultEntity(entity, record);
    }

    protected boolean checkNeedSync(EshopFactory factory) {
        String sendShopTypes = sendGoodsConfig.getNotIgnoreGiftSendShopTypes();
        String[] split = sendShopTypes.split(",");
        String s = String.valueOf(factory.getShopType().getCode());
        for (String item : split) {
            if (s.equals(item)) {
                return true;
            }
        }
        return false;
    }

    protected void doBuildDefaultEntity(FreightBillNoSyncEntity entity, EshopFreightSyncRecord record) {
        entity.setSpilt(false);
        entity.setSyncType(SyncType.WHOLE);
        entity.setLast(true);
        record.setLastOne(true);
        record.setSyncType(SyncType.WHOLE);
    }

    private boolean isSplit(List<DeliverSendDetail> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return false;
        }
        Map<BigInteger, List<DeliverSendDetail>> listMap =
                detailList.stream().collect(Collectors.groupingBy(DeliverSendDetail::getDeliverOrderId));
        return listMap.size() != 1;
    }


    private void doUpdateSaleOrderSendStatus(List<SyncFreightBillNoRequest> localSendList) {
        if (CollectionUtils.isEmpty(localSendList)) {
            return;
        }
        Map<String, UpdateSaleOrderSendStatusParam> map = new HashMap<>();
        for (SyncFreightBillNoRequest request : localSendList) {
            List<SyncFreightBillNoDetail> details = request.getDetails();
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            List<SyncFreightInfo> freightInfoList = request.getFreightInfoList();
            if (CollectionUtils.isEmpty(freightInfoList)) {
                continue;
            }
            SyncFreightInfo freightInfo = freightInfoList.get(0);
            for (SyncFreightBillNoDetail detail : details) {
                String tradeId = detail.getTradeId();
                if (map.containsKey(tradeId)) {
                    continue;
                }
                UpdateSaleOrderSendStatusParam param = new UpdateSaleOrderSendStatusParam();
                param.setEshopId(request.getOtypeId());
                param.setProfileId(CurrentUser.getProfileId());
                param.setTradeId(detail.getTradeId());
                param.setFreightCode(freightInfo.getFreightCode());
                param.setFreightName(freightInfo.getFreightName());
                param.setFreightBillNo(freightInfo.getFreightBillNo());
                map.put(tradeId, param);
            }
        }
        if (CollectionUtils.isEmpty(map.values())) {
            return;
        }
        List<UpdateSaleOrderSendStatusParam> paramArrayList = new ArrayList<>(map.values());
        eshopOrderMapper.doUpdateSaleOrderSendStatus(paramArrayList);
    }


}
