package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.entity.request.mock.QueryDataListParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> 2024/4/11 14:37
 */
@Mapper
@Repository
public interface MockMapper {
    /**
     * 根据订单号查询订单
     * @param shopAccount 店铺账号
     * @param tradeId 订单号
     * @return 订单json实体
     */
    String queryTradeByTradeId(@Param("shopAccount")String shopAccount, @Param("tradeId")String tradeId);

    /**
     * 查询售后单总数
     * @param param 查询售后单列表参数
     * @return 总数
     */
    long queryRefundDataCount(QueryDataListParam param);

    /**
     * 查询售后列表
     * @param param 查询参数
     * @return 售后列表
     */
    List<String> queryRefundDataList(QueryDataListParam param);

    /**
     * 根据售后单号查询售后单
     * @param shopAccount  店铺账号
     * @param refundId 售后单号
     * @return 售后json
     */
    String queryRefundData(@Param("shopAccount")String shopAccount, @Param("refundId")String refundId);

    /**
     * 查询订单总数
     * @param param 查询订单参数
     * @return 订单总数
     */
    long queryTradeCount(QueryDataListParam param);

    /**
     * 查询订单列表
     * @param param 查询订单列表参数
     * @return 订单列表
     */
    List<String> queryTradeList(QueryDataListParam param);
}
