package com.wsgjp.ct.sale.platform.sdk.aspect;

import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.constraint.ParamsAspectOrder;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopMapper;
import com.wsgjp.ct.sale.platform.sdk.util.PlatformCommonUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Order(value = ParamsAspectOrder.INIT_PARAMS)
@Aspect
@Component
public class SdkParamsInjectionAspect {
    private final Logger logger = LoggerFactory.getLogger(SdkParamsInjectionAspect.class);
    private final PlatformSdkEshopMapper eshopMapper;
    private final static String TEST_SHOP = "test_shop_";

    public SdkParamsInjectionAspect(PlatformSdkEshopMapper eshopMapper) {
        this.eshopMapper = eshopMapper;
    }

    @Pointcut("execution(public * com.wsgjp.ct.sale.platform.sdk.service.impl.*.*(..))")
    public void execute() {

    }

    @Before(value = "execute()")
    public void intercept(JoinPoint joinPoint) {
        try {
            Object[] params = joinPoint.getArgs();
            if (params == null || params.length == 0) {
                return;
            }
            BigInteger errorShopId = BigInteger.valueOf(-1);
            for (Object param : params) {
                if (param instanceof BaseRequest) {
                    BaseRequest baseRequest = (BaseRequest) param;
                    if (baseRequest.getShopId() == null || BigInteger.ZERO.equals(baseRequest.getShopId()) || errorShopId.equals(baseRequest.getShopId())) {
                        // 针对部分调用实际上是不需要传递具体店铺的，甚至都不需要店铺类型
                        EshopSystemParams systemParams = new EshopSystemParams(baseRequest.getShopType());
                        systemParams.setNoCheckParams(true);
                        baseRequest.setSystemParams(systemParams);
                    } else {
                        try {
                            //新增店铺未保存时，点授权，获取eshop信息获取不到会抛出异常
                            EshopInfo eshopInfo = eshopMapper.getEshopInfoByShopId(CurrentUser.getProfileId(), baseRequest.getShopId());
                            EshopSystemParams systemParams = PlatformCommonUtil.toSystemParams(eshopInfo);
                            systemParams.setTest(GlobalConfig.getBoolean(getTestKey(eshopInfo.getOtypeId())));
                            baseRequest.setShopType(eshopInfo.getEshopType());
                            baseRequest.setSystemParams(systemParams);
                        } catch (Exception ex) {
                            //处理新增店铺未保存时，点授权，用eshopid获取网店信息为null的情况
                            EshopSystemParams systemParams = new EshopSystemParams(baseRequest.getShopType());
                            systemParams.setProfileId(CurrentUser.getProfileId());
                            systemParams.seteShopId(baseRequest.getShopId());
                            baseRequest.setSystemParams(systemParams);
                        }
                    }
                    if (baseRequest.getSystemParams() != null) {
                        baseRequest.getSystemParams().setProductId(CurrentUser.getProductId());
                    }
                }
            }
        } catch (Throwable e) {
            String msg = "SystemParams参数注入遇到问题：" + e.getMessage();
            logger.error(msg, e);
        }
    }

    private String getTestKey(BigInteger otypeId) {
        return TEST_SHOP + otypeId;
    }

}
