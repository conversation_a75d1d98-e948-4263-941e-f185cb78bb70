package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.platform.dto.token.EshopAuthInfo;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopMapper;
import com.wsgjp.ct.sale.platform.support.AuthService;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class AuthServiceSdkImpl implements AuthService {
    private static final Logger logger = LoggerFactory.getLogger(AuthServiceSdkImpl.class);
    private final PlatformSdkEshopMapper eshopMapper;

    public AuthServiceSdkImpl(PlatformSdkEshopMapper eshopMapper) {
        this.eshopMapper = eshopMapper;
    }

    @Override
    public void saveToken(EshopAuthInfo auth) {
        try {
            eshopMapper.updateEshopToken(auth);
            eshopMapper.updateEshopNoticeById(auth.getProfileId(),auth.getEshopId(),0);
            logger.error("排查授权被清空，方法AuthServiceSdkImpl.saveToken:{}", JsonUtils.toJson(auth));
        } catch (Exception ex) {
            logger.error(String.format("网店【%s】保存授权信息报错：%s", auth.getEshopId(), ex.getMessage()), ex);
        }
    }
}
