package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.platform.entity.request.delivery.*;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.delivery.CreatePoDeliveryResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.delivery.EshopPoDeliveryOperateFeature;
import com.wsgjp.ct.sale.platform.sdk.service.EshopPoDeliverService;
import org.springframework.stereotype.Service;

@Service
public class EshopPoDeliverServiceImpl implements EshopPoDeliverService {
    @Override
    public CreatePoDeliveryResponse createPoDelivery(CreatePoDeliveryRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPoDeliveryOperateFeature feature = factory.getFeature(EshopPoDeliveryOperateFeature.class);
        if (feature != null) {
            return feature.createPoDelivery(request);
        } else {
            CreatePoDeliveryResponse response = new CreatePoDeliveryResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持创建出仓单!");
            return response;
        }
    }

    @Override
    public CreatePoDeliveryResponse editPoDelivery(EditPoDeliveryRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPoDeliveryOperateFeature feature = factory.getFeature(EshopPoDeliveryOperateFeature.class);
        if (feature != null) {
            return feature.editPoDelivery(request);
        } else {
            CreatePoDeliveryResponse response = new CreatePoDeliveryResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持修改出仓单!");
            return response;
        }
    }

    @Override
    public BaseResponse cancelPoDelivery(CancelPoDeliveryRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPoDeliveryOperateFeature feature = factory.getFeature(EshopPoDeliveryOperateFeature.class);
        if (feature != null) {
            return feature.cancelPoDelivery(request);
        } else {
            BaseResponse response = new BaseResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持取消出仓单!");
            return response;
        }
    }

    @Override
    public BaseResponse confirmPoDelivery(ConfirmPoDeliveryRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPoDeliveryOperateFeature feature = factory.getFeature(EshopPoDeliveryOperateFeature.class);
        if (feature != null) {
            return feature.confirmPoDelivery(request);
        } else {
            BaseResponse response = new BaseResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持确认出仓单!");
            return response;
        }
    }

    @Override
    public BaseResponse imporPoDeliveryDetails(ImporPoDeliveryDetailRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopPoDeliveryOperateFeature feature = factory.getFeature(EshopPoDeliveryOperateFeature.class);
        if (feature != null) {
            return feature.imporPoDeliveryDetails(request);
        } else {
            BaseResponse response = new BaseResponse();
            response.setSuccess(false);
            response.setMessage("平台不支持导入出仓明细!");
            return response;
        }
    }
}
