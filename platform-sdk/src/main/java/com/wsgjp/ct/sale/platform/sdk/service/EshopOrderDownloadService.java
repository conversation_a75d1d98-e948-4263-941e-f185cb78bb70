package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderByIdRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderCountRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.OrderCountResponse;
import com.wsgjp.ct.sale.platform.sdk.entity.callback.RefundCheckResult;
import com.wsgjp.ct.sale.platform.entity.response.order.OrderDownloadResponse;
import com.wsgjp.ct.sale.platform.sdk.entity.request.OrderDownloadRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.RefundCheckRequest;

import java.util.List;
/**
 * <AUTHOR>
 */
public interface EshopOrderDownloadService {
    /**
     * 根据创建时间下载订单列表(全量下载订单)
     *
     * @param request request
     */
    void downloadOrdersByCreateTime(OrderDownloadRequest request);

    /**
     * 根据修改时间下载订单列表(增量下载订单)
     *
     * @param request request
     */
    void downloadOrdersByModifyTime(OrderDownloadRequest request);

    /**
     * 订单下载老方法，不建议使用
     * @param request 参数
     * @return 订单列表
     */
    OrderDownloadResponse downloadOrderOld(DownloadOrderRequest request);

    /**
     * 根据订单号下载订单
     *
     * @param request
     * @return DownloadOrderByIdRequest
     */
    OrderDownloadResponse downloadOrderByTradeIds(DownloadOrderByIdRequest request);

    /**
     * 查询时间段内的订单量
     * @param request 开始时间和结束时间
     * @return 订单数量实体
     */
    OrderCountResponse getOrderCount(DownloadOrderCountRequest request);

    /**
     * 退款检查
     * @param requestList 订单号列表
     * @return 退款检查结果
     */
    RefundCheckResult checkRefund(List<RefundCheckRequest> requestList);
}
