package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.common.enums.MonitorSourceEnum;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.constraint.PlatformConstants;
import com.wsgjp.ct.sale.platform.dto.refund.EshopRefundOrderEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.refundEntity.OrderDetailOnlineSkuInfo;
import com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundByParamRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundByTimeRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundByTradesRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.SaveRefundToNotifyChangeRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.entity.response.refund.RefundOrderDownloadResponse;
import com.wsgjp.ct.sale.platform.entity.response.refund.SaveRefundToNotifyChangeResponse;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.feature.plugin.EshopPluginFeature;
import com.wsgjp.ct.sale.platform.feature.refund.*;
import com.wsgjp.ct.sale.platform.sdk.entity.PlatformSdkConstant;
import com.wsgjp.ct.sale.platform.sdk.entity.request.RefundDownloadRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.TimeDownloadRequest;
import com.wsgjp.ct.sale.platform.sdk.log.BillType;
import com.wsgjp.ct.sale.platform.sdk.log.EshopSaleOrderSyncTaskLog;
import com.wsgjp.ct.sale.platform.sdk.log.EshopSaleOrderSyncTaskLogDetail;
import com.wsgjp.ct.sale.platform.sdk.mapper.EshopNotifyMapper;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopOrderMapper;
import com.wsgjp.ct.sale.platform.sdk.service.EshopRefundDownloadService;
import com.wsgjp.ct.sale.platform.sdk.slice.SliceDownloadContext;
import com.wsgjp.ct.sale.platform.slice.SliceRequest;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class EshopRefundDownloadServiceImpl extends SliceDownloadServiceBase<EshopRefundOrderEntity> implements EshopRefundDownloadService {

    private final PlatformSdkEshopOrderMapper mapper;
    private final EshopNotifyMapper eshopNotifyMapper;
    private static final Logger logger = LoggerFactory.getLogger(EshopRefundDownloadServiceImpl.class);

    public EshopRefundDownloadServiceImpl(PlatformSdkEshopOrderMapper mapper, EshopNotifyMapper eshopNotifyMapper) {
        this.mapper = mapper;
        this.eshopNotifyMapper = eshopNotifyMapper;
    }

    @Override
    public void downloadRefundsByCreateTime(RefundDownloadRequest request) {
        request.setIncrement(false);
        EshopFactory factory = getFactory(request);
        EshopRefundDownloadByCreateTimeFeature feature = factory.getFeature(EshopRefundDownloadByCreateTimeFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持下载售后单", request.getSystemParams().getShopType().getName()));
        }
        SliceDownloadContext<EshopRefundOrderEntity> context = initContext(request);
        context.setThreadName(PlatformConstants.INCREASE_DOWNLOAD_REFUND_THREAD_NAME);
        context.setSliceDownloader((feature::downloadByCreateTime));
        context.setTimeExtractor(EshopRefundOrderEntity::getCreated);
        PlatformBaseConfig platformConfig = factory.getConfig();
        context.setSingleThreadCount(platformConfig.getSingleThreadCount());
        context.setSourceEnum(MonitorSourceEnum.MANUAL_DOWNLOAD_REFUND);
        if (!request.getSourceEnum().equals(MonitorSourceEnum.NONE)) {
            context.setSourceEnum(request.getSourceEnum());
        }
        download(request, context);
    }

    @Override
    public void downloadRefundsByModifyTime(RefundDownloadRequest request) {
        EshopFactory factory = getFactory(request);
        EshopRefundDownloadByUpdateTimeFeature feature = factory.getFeature(EshopRefundDownloadByUpdateTimeFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持下载售后单", request.getSystemParams().getShopType().getName()));
        }
        request.setIncrement(true);
        SliceDownloadContext<EshopRefundOrderEntity> context = initContext(request);
        context.setThreadName(PlatformConstants.DOWNLOAD_REFUND_THREAD_NAME);
        context.setSliceDownloader((feature::downloadByUpdateTime));
        context.setTimeExtractor(EshopRefundOrderEntity::getModified);
        context.setSourceEnum(request.getSourceEnum());
        download(request, context);
    }

    @Override
    protected void writeOrderSyncTaskLog(TimeDownloadRequest<EshopRefundOrderEntity> request, SliceDownloadResponse<EshopRefundOrderEntity> response, Throwable exception) {
        EshopSystemParams systemParams = request.getSystemParams();
        EshopSaleOrderSyncTaskLog taskLog = new EshopSaleOrderSyncTaskLog(systemParams.getProfileId(), systemParams.geteShopId(), request.getTaskId(), request.getDownloadType(), DateUtils.getDate());
        if (response != null) {
            taskLog.setResponseRefundTotal(response.getTotal());
            taskLog.setResponseId(response.getResponseId() != null ? response.getResponseId() : "");
            if (CollectionUtils.isNotEmpty(response.getList())) {
                List<EshopSaleOrderSyncTaskLogDetail> taskDetailLogs = new ArrayList<>();
                response.getList().forEach(order -> {
                    EshopSaleOrderSyncTaskLogDetail taskLogDetail = new EshopSaleOrderSyncTaskLogDetail();
                    taskLogDetail.setParentId(taskLog.getId());
                    taskLogDetail.setTradeOrderId(order.getTradeId());
                    taskLogDetail.setRefundId(order.getRefundId());
                    taskLogDetail.setDetailType(BillType.REFUND.getCode());
                    taskLogDetail.setPlatformRefundState(order.getRefundStatus() != null ? order.getRefundStatus() : RefundStatus.NONE);
                    taskLogDetail.setPlatformTradeState(order.getTradeStatus() != null ? order.getTradeStatus() : TradeStatus.ABNORMAL);
                    taskDetailLogs.add(taskLogDetail);
                });
                LogService.addRange(taskDetailLogs);
            }
        }
        LogService.add(taskLog);
    }

    private SliceDownloadContext<EshopRefundOrderEntity> initContext(RefundDownloadRequest request) {
        EshopFactory factory = getFactory(request);
        EshopPluginFeature pluginFeature = getPlugin(factory);
        SliceDownloadContext<EshopRefundOrderEntity> context = new SliceDownloadContext<>();
        context.setFactory(factory);
        SliceRequest sliceRequest = new SliceRequest();
        sliceRequest.setIncrement(request.isIncrement());
        context.setSliceParamsList(pluginFeature.getRefundSliceParams(sliceRequest));
        return context;
    }

    @Override
    public RefundOrderDownloadResponse downloadRefundByParam(DownloadRefundByParamRequest request) {
        EshopFactory factory = getFactory(request);
        EshopRefundByIdFeature refundFeature = factory.getFeature(EshopRefundByIdFeature.class);
        if (refundFeature != null) {
            RefundOrderDownloadResponse response = refundFeature.downloadRefundOrderByRefundId(request);
            fillMissDataByResponse(factory, response);
            return response;
        }
        RefundOrderDownloadResponse response = new RefundOrderDownloadResponse();
        response.setSuccess(false);
        response.setMessage("该平台不支持按单号下载售后单");
        response.setCode(PlatformSdkConstant.NOT_SUPPORT_CODE);
        return response;

    }

    @Override
    public RefundOrderDownloadResponse downloadRefundByOrder(DownloadRefundByTradesRequest request) {
        EshopFactory factory = getFactory(request);
        EshopRefundByTradeFeature refundFeature = factory.getFeature(EshopRefundByTradeFeature.class);
        if (refundFeature == null) {
            RefundOrderDownloadResponse response = new RefundOrderDownloadResponse();
            response.setSuccess(false);
            response.setCode(PlatformSdkConstant.NOT_SUPPORT_CODE);
            return response;
        }
        try {
            return refundFeature.downloadRefundOrderByTrade(request);
        } catch (Exception ex) {
            RefundOrderDownloadResponse response = new RefundOrderDownloadResponse();
            logger.error("账套{}网店{}按照原始订单下载售后单报错{}", CurrentUser.getProfileId(), factory.getParams().getFullName(), ex.getMessage(), ex);
            response.setSuccess(false);
            response.setCode(PlatformSdkConstant.SYSTEM_ERROR);
            response.setMessage(ex.getMessage());
            return response;
        }
    }

    @Override
    public RefundOrderDownloadResponse downloadRefundListOld(DownloadRefundByTimeRequest request) {
        EshopFactory factory = getFactory(request);
        RefundOrderDownloadResponse downloadResponse;
        if (request.getIncrementEnabled()) {
            downloadResponse = downloadRefundListOldByIncrease(factory, request);
            if (!downloadResponse.getSuccess() && downloadResponse.getCode() == PlatformSdkConstant.NOT_SUPPORT_CODE) {
                return downloadRefundListOldByCreate(factory, request);
            }
        } else {
            downloadResponse = downloadRefundListOldByCreate(factory, request);
            if (!downloadResponse.getSuccess() && downloadResponse.getCode() == PlatformSdkConstant.NOT_SUPPORT_CODE) {
                return downloadRefundListOldByIncrease(factory, request);
            }
        }
        return downloadResponse;
    }

    private RefundOrderDownloadResponse downloadRefundListOldByCreate(EshopFactory factory, DownloadRefundByTimeRequest request) {
        RefundOrderDownloadResponse refundResponse = new RefundOrderDownloadResponse();
        EshopRefundByCreateTimeFeature refundFeature = factory.getFeature(EshopRefundByCreateTimeFeature.class);
        if (refundFeature == null) {
            refundResponse.setCode(PlatformSdkConstant.NOT_SUPPORT_CODE);
            refundResponse.setSuccess(false);
            return refundResponse;
        }
        try {
            return refundFeature.downloadRefundOrderList(request);
        } catch (Exception ex) {
            refundResponse.setSuccess(false);
            refundResponse.setMessage(ex.getMessage());
            logger.error("账套：{}，网店：{}，全量下载售后单异常：{}", factory.getParams().getProfileId(), factory.getParams().getFullName(), ex.getMessage(), ex);
            return refundResponse;
        }
    }

    private RefundOrderDownloadResponse downloadRefundListOldByIncrease(EshopFactory factory, DownloadRefundByTimeRequest request) {
        RefundOrderDownloadResponse refundResponse = new RefundOrderDownloadResponse();
        EshopRefundIncreaseFeature refundFeature = factory.getFeature(EshopRefundIncreaseFeature.class);
        if (refundFeature == null) {
            refundResponse.setCode(PlatformSdkConstant.NOT_SUPPORT_CODE);
            refundResponse.setSuccess(false);
            return refundResponse;
        }
        try {
            return refundFeature.downloadRefundOrderIncrementally(request);
        } catch (Exception ex) {
            refundResponse.setSuccess(false);
            refundResponse.setMessage(ex.getMessage());
            logger.error("账套：{}，网店：{}，增量下载售后单异常：{}", factory.getParams().getProfileId(), factory.getParams().getFullName(), ex.getMessage(), ex);
            return refundResponse;
        }
    }

    private void fillMissDataByResponse(EshopFactory factory, RefundOrderDownloadResponse response) {
        EshopRefundNeedDetailFeature needDetailFeature = factory.getFeature(EshopRefundNeedDetailFeature.class);
        if (needDetailFeature == null) {
            return;
        }
        // 在获取订单构建售后明细
        if (needDetailFeature.needDownloadOrderDetails()) {
            // 默认查出订单明细传入接口实现
            if (ngp.utils.CollectionUtils.isNotEmpty(response.getRefundList())) {
                List<String> tradeIds = response.getRefundList().stream().map(EshopRefundOrderEntity::getTradeId).collect(Collectors.toList());
                Map<String, OrderDetailOnlineSkuInfo> detailMap = queryOrderDetailByTradeIdAndOid(factory.getParams().geteShopId(), tradeIds);
                needDetailFeature.fillRefundInfoByOrder(response.getRefundList(), detailMap);
            }
        }
    }

    @Override
    protected void fillMissData(EshopFactory factory, SliceDownloadResponse<EshopRefundOrderEntity> response, MonitorSourceEnum source) {
        EshopRefundDownloadRequirementsFeature downloadDetailFeature = factory.getFeature(EshopRefundDownloadRequirementsFeature.class);
        if (downloadDetailFeature == null) {
            return;
        }
        if (downloadDetailFeature.needDownloadRefundDetails(response.getSlice())) {
            //需要售后详情接口
            downloadDetailFeature.fillRefundInfoByRefundDetail(response.getList());
        }
        if (downloadDetailFeature.needDownloadOrderDetails(response.getSlice())) {
            //需要订单信息填充售后明细
            // 默认查出订单明细传入接口实现
            if (CollectionUtils.isNotEmpty(response.getList())) {
                List<String> tradeIds = response.getList().stream().map(EshopRefundOrderEntity::getTradeId).collect(Collectors.toList());
                Map<String, OrderDetailOnlineSkuInfo> detailMap = queryOrderDetailByTradeIdAndOid(factory.getParams().geteShopId(), tradeIds);
                downloadDetailFeature.fillRefundInfoByOrder(response.getList(), detailMap);
            }
        }
    }

    private Map<String, OrderDetailOnlineSkuInfo> queryOrderDetailByTradeIdAndOid(BigInteger eshopId, List<String> tradeIds) {
        if (CollectionUtils.isEmpty(tradeIds)) {
            return Collections.emptyMap();
        }
        List<OrderDetailOnlineSkuInfo> onlineSkuInfos = queryOrderDetailInfo(tradeIds, eshopId);
        if (CollectionUtils.isNotEmpty(onlineSkuInfos)) {
            Map<String, OrderDetailOnlineSkuInfo> detailMap = buildOrderDetailMap(onlineSkuInfos);
            return detailMap;
        }
        return Collections.emptyMap();
    }

    private Map<String, OrderDetailOnlineSkuInfo> buildOrderDetailMap(List<OrderDetailOnlineSkuInfo> onlineSkuInfos) {
        Map<String, OrderDetailOnlineSkuInfo> map = new HashMap<>(onlineSkuInfos.size());
        for (OrderDetailOnlineSkuInfo skuInfo : onlineSkuInfos) {
            String key = String.format("%s_%s", skuInfo.getTradeId(), skuInfo.getOid());
            if (map.containsKey(key)) {
                continue;
            }
            map.put(key, skuInfo);
        }
        return map;
    }

    private List<OrderDetailOnlineSkuInfo> queryOrderDetailInfo(List<String> tradeIds, BigInteger eshopId) {
        if (CollectionUtils.isEmpty(tradeIds)) {
            return new ArrayList<>();
        }
        BigInteger profileId = CurrentUser.getProfileId();
        return mapper.queryDetailSkuInfo(profileId, eshopId, tradeIds);
    }

    @Override
    public void doSaveRefundToNotifyChange(SaveRefundToNotifyChangeRequest request) {
        try {
            if (CollectionUtils.isEmpty(request.getRefundList())) {
                return;
            }
            EshopFactory factory = getFactory(request);
            EshopSaveRefundToNotifyChangeFeature refundFeature = factory.getFeature(EshopSaveRefundToNotifyChangeFeature.class);
            if (refundFeature == null) {
                return;
            }
            List<EshopRefundOrderEntity> refundList = request.getRefundList();
            List<String> tradeIds = refundList.stream().map(EshopRefundOrderEntity::getTradeId).distinct().collect(Collectors.toList());
            List<EshopRefundOrderEntity> realRefunds = refundList.stream().filter(refund -> refund.getRefundStatus() != RefundStatus.CANCEL
                    && refund.getRefundStatus() != RefundStatus.NONE).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(realRefunds)) {
                return;
            }
            BigInteger profileId = request.getSystemParams().getProfileId();
            BigInteger eshopId = request.getShopId();
            List<EshopNotifyChange> oldEshopNotifyChanges = eshopNotifyMapper.queryMessageChangeSorted(profileId, tradeIds, eshopId, TMCType.REFUND_STOP.getCode());
            request.setRefundList(realRefunds);
            request.setOldNotifyChanges(oldEshopNotifyChanges);
            SaveRefundToNotifyChangeResponse response = refundFeature.saveRefundToNotifyChange(request);
            if (!response.getSuccess()) {
                throw new RuntimeException(response.getMessage());
            }
            //售后单没有任何售后消息记录
            if (CollectionUtils.isNotEmpty(response.getNotifyChanges())) {
                eshopNotifyMapper.insertEshopNotifyChanges(response.getNotifyChanges());
            }
        } catch (Exception ex) {
            logger.error("保存售后单到通知变更表异常：{}", ex.getMessage(), ex);
        }
    }
}
