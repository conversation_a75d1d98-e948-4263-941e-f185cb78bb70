package com.wsgjp.ct.sale.platform.sdk.entity.send;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillCreateType;
import com.wsgjp.ct.bill.core.handle.entity.enums.SelfDeliveryModeEnum;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(value = "发货单配送完成请求体")
public class ConfirmDeliveryEndBillNoRequest {
    @ApiModelProperty(value = "发货单的主键", required = true)
    private BigInteger deliverOrderId;
    @ApiModelProperty(value = "任务单主键", required = true, notes = "如果没有仓储环节，请传0")
    private BigInteger warehouseTaskId;
    @ApiModelProperty(value = "店铺Id", required = true)
    private BigInteger otypeId;
    @ApiModelProperty(value = "线上订单id", required = true)
    private String tradeId;
    @ApiModelProperty(value = "订单创建方式,直接取发货单的", required = true)
    private BillCreateType createType;
    @ApiModelProperty(value = "发货方式", required = true)
    private SelfDeliveryModeEnum deliveryMode;
    @ApiModelProperty(value = "订单提醒标记", required = true)
    private List<MarkData> deliverMarks;
    @ApiModelProperty(value = "订单发货明细")
    private List<SyncFreightBillNoDetail> details;
    @ApiModelProperty(value = "交易状态", required = true)
    private TradeStatus tradeStatus;

    public BigInteger getDeliverOrderId() {
        return deliverOrderId;
    }

    public void setDeliverOrderId(BigInteger deliverOrderId) {
        this.deliverOrderId = deliverOrderId;
    }

    public BigInteger getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(BigInteger warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public BillCreateType getCreateType() {
        return createType;
    }

    public void setCreateType(BillCreateType createType) {
        this.createType = createType;
    }

    public SelfDeliveryModeEnum getDeliveryMode() {
        return deliveryMode;
    }

    public void setDeliveryMode(SelfDeliveryModeEnum deliveryMode) {
        this.deliveryMode = deliveryMode;
    }

    public List<MarkData> getDeliverMarks() {
        return deliverMarks;
    }

    public void setDeliverMarks(List<MarkData> deliverMarks) {
        this.deliverMarks = deliverMarks;
    }

    public List<SyncFreightBillNoDetail> getDetails() {
        return details;
    }

    public void setDetails(List<SyncFreightBillNoDetail> details) {
        this.details = details;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }
}
