package com.wsgjp.ct.sale.platform.sdk.slice;

import com.wsgjp.ct.sale.platform.entity.response.product.SliceFinanceDownloadResponse;
import com.wsgjp.ct.sale.platform.slice.DownloadFinanceSlice;
import com.wsgjp.ct.sale.platform.slice.SliceFinanceParams;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SliceFinanceDownConText<T> {
    private SliceDownloader<DownloadFinanceSlice, SliceFinanceDownloadResponse<T>> sliceDownloader;
    private List<SliceFinanceParams> sliceFinanceParams;
    private SliceTimeExtractor<T> timeExtractor;

    public SliceTimeExtractor<T> getTimeExtractor() {
        return timeExtractor;
    }

    public void setTimeExtractor(SliceTimeExtractor<T> timeExtractor) {
        this.timeExtractor = timeExtractor;
    }

    public SliceDownloader<DownloadFinanceSlice, SliceFinanceDownloadResponse<T>> getSliceDownloader() {
        return sliceDownloader;
    }

    public void setSliceDownloader(SliceDownloader<DownloadFinanceSlice, SliceFinanceDownloadResponse<T>> sliceDownloader) {
        this.sliceDownloader = sliceDownloader;
    }

    public List<SliceFinanceParams> getSliceFinanceParams() {
        return sliceFinanceParams;
    }

    public void setSliceFinanceParams(List<SliceFinanceParams> sliceFinanceParams) {
        this.sliceFinanceParams = sliceFinanceParams;
    }
}
