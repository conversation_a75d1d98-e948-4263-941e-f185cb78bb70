package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundByParamRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundByTimeRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundByTradesRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.SaveRefundToNotifyChangeRequest;
import com.wsgjp.ct.sale.platform.entity.response.refund.RefundOrderDownloadResponse;
import com.wsgjp.ct.sale.platform.sdk.entity.request.RefundDownloadRequest;

/**
 * <AUTHOR>
 */
public interface EshopRefundDownloadService {
    /**
     * 根据创建时间下载订单列表(全量下载订单)
     *
     * @param request
     * @return
     */
    void downloadRefundsByCreateTime(RefundDownloadRequest request);

    /**
     * 根据修改时间下载订单列表(增量下载订单)
     *
     * @param request
     * @return
     */
    void downloadRefundsByModifyTime(RefundDownloadRequest request);

    /**
     * 根据售后单号下载售后单
     *
     * @param request 请求参数：售后单号必填
     * @return 售后实体
     */
    RefundOrderDownloadResponse downloadRefundByParam(DownloadRefundByParamRequest request);


    /**
     * 根据订单实体下载售后单
     * @param request 订单列表
     * @return 售后实体
     */
    RefundOrderDownloadResponse downloadRefundByOrder(DownloadRefundByTradesRequest request);

    /**
     * 老方案非分页方式下载售后单
     * @param request 下载售后的时间参数
     * @return 售后列表
     */
    RefundOrderDownloadResponse downloadRefundListOld(DownloadRefundByTimeRequest request);

    /**
     * 将售后单信息保存到pl_eshop_notify_change表中
     */
    void doSaveRefundToNotifyChange(SaveRefundToNotifyChangeRequest request);
}
