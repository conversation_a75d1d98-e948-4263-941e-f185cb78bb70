package com.wsgjp.ct.sale.platform.sdk.slice;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.constraint.PlatformConstants;
import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.enums.SliceType;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.feature.plugin.EshopPluginFeature;
import com.wsgjp.ct.sale.platform.sdk.entity.request.TimeDownloadRequest;
import com.wsgjp.ct.sale.platform.slice.DownloadSlice;
import com.wsgjp.ct.sale.platform.slice.SliceParams;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import ngp.utils.DateUtils;
import ngp.utils.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SliceBuilder<T> {
    private final Date startTime;
    private final Date endTime;
    private final EshopSystemParams eshopParams;
    private final List<SliceParams> sliceParamsList = new ArrayList<>();
    private final SliceBundle<T> sliceBundle;
    private final SliceDownloader<DownloadSlice, SliceDownloadResponse<T>> sliceDownloader;
    private final SliceTimeExtractor<T> timeExtractor;

    private final boolean forceByApi;

    public SliceBuilder(TimeDownloadRequest<T> request,
                        EshopFactory factory,
                        SliceDownloader<DownloadSlice, SliceDownloadResponse<T>> sliceDownloader,
                        SliceTimeExtractor<T> timeExtractor,
                        List<SliceParams> sliceParams,
                        boolean forceByApi) {
        this.startTime = request.getStartTime();
        this.endTime = request.getEndTime();
        this.eshopParams = request.getSystemParams();
        this.sliceDownloader = sliceDownloader;
        factory.getFeature(EshopPluginFeature.class);
        sliceBundle = new SliceBundle<>();
        sliceBundle.setSliceList(new ArrayList<>());
        sliceBundle.setResponses(new ArrayList<>());
        this.timeExtractor = timeExtractor;
        sliceParamsList.addAll(sliceParams);
        this.forceByApi = forceByApi;
    }

    private DownloadSlice createSlice(Integer businessType, SliceParams params) {
        DownloadSlice downloadSlice = new DownloadSlice();
        downloadSlice.setStartTime(DateUtils.addMinutes(startTime, -params.getDownloadOffsetTime()));
        downloadSlice.setEndTime(endTime);
        downloadSlice.setBusinessType(businessType);
        downloadSlice.setPageSize(params.getPageSize());
        downloadSlice.setPage(params.getPageStartIndex());
        downloadSlice.setType(params.getType());
        downloadSlice.setSliceParams(params);
        downloadSlice.setStatus(params.getStatus());
        return downloadSlice;
    }

    private List<DownloadSlice> buildSlice(SliceParams params) {
        List<DownloadSlice> sliceList = new ArrayList<>();
        DownloadSlice downloadSlice = createSlice(params.getBusinessType(), params);
        boolean rdsEnabled = eshopParams.getRdsEnabled();
        if (forceByApi) {
            rdsEnabled = false;
        }
        boolean taoBao=ShopType.TaoBao.equals(eshopParams.getShopType())
                || ShopType.Tmall.equals(eshopParams.getShopType());
        if (rdsEnabled && eshopParams.getRdsReadyTime() != null) {
            Date rdsLastTime = eshopParams.getRdsReadyTime();
            if (downloadSlice.getStartTime().after(rdsLastTime)) {
                downloadSlice.setRds(true);
            }
        }
        if (!downloadSlice.isRds() && isIntervalLimited(downloadSlice, params.getDownloadInterval())) {
            sliceList.addAll(split(downloadSlice, params.getDownloadInterval()));
        } else {
            if (params.isNoSupportRds() && isIntervalLimited(downloadSlice, params.getDownloadInterval())){
                sliceList.addAll(split(downloadSlice, params.getDownloadInterval()));
            }else if(!taoBao && isIntervalLimited(downloadSlice, params.getDownloadInterval())){
                sliceList.addAll(split(downloadSlice, params.getDownloadInterval()));
            }
            else{
                sliceList.add(downloadSlice);
            }
        }
        return sliceList;
    }

    public List<DownloadSlice> buildOneLevelSlice() {
        List<DownloadSlice> sliceList = new ArrayList<>();
        for (SliceParams params : sliceParamsList) {
            sliceList.addAll(buildSlice(params));
        }
        return sliceList;
    }

    private List<DownloadSlice> split(DownloadSlice apiSlice, int downloadInterval) {
        List<DownloadSlice> slices = new ArrayList<>();
        DownloadSlice lastSplice = null;
        do {
            DownloadSlice timeSplice = new DownloadSlice();
            slices.add(timeSplice);
            timeSplice.from(apiSlice);
            timeSplice.setStartTime(lastSplice == null ? apiSlice.getStartTime() : lastSplice.getEndTime());
            timeSplice.setStartTime(DateUtils.addSeconds(timeSplice.getStartTime(), 1));
            timeSplice.setEndTime(DateUtils.addSeconds(lastSplice == null ? apiSlice.getStartTime() : lastSplice.getEndTime(), downloadInterval));
            if (timeSplice.getEndTime().getTime() >= apiSlice.getEndTime().getTime()) {
                timeSplice.setEndTime(apiSlice.getEndTime());
                return slices;
            }
            lastSplice = timeSplice;
        } while (true);
    }

    private boolean isIntervalLimited(DownloadSlice downloadSlice, int interval) {
        if (interval <= 0) {
            return false;
        }
        double time = downloadSlice.getEndTime().getTime() - downloadSlice.getStartTime().getTime();
        time /= 1000d;
//        time /= 60d;
        return time > interval;
    }


    public List<DownloadSlice> buildPageSlice(DownloadSlice firstSlice, int start, int maxPage) {
        List<DownloadSlice> sliceList = new ArrayList<>();
        if (firstSlice.getPage() == 0){
            maxPage -=1;
        }
        for (int page = start; page <= maxPage; page++) {
            DownloadSlice slice = new DownloadSlice();
            slice.from(firstSlice);
            slice.setPage(page);
            sliceList.add(slice);
        }
        return sliceList;
    }

    public void buildAllPageSlice(SliceDownloadResponse<T> baseResponse) {
        if (baseResponse.getSlice().getPage() < baseResponse.getSlice().getSliceParams().getMaxPage()) {
            List<DownloadSlice> pageSliceList = buildPageSlice(baseResponse.getSlice(), baseResponse.getSlice().getPage() + 1, baseResponse.getSlice().getSliceParams().getMaxPage());
            DownloadSlice maxPageSlice = pageSliceList.get(pageSliceList.size() - 1);
            pageSliceList.remove(pageSliceList.size() - 1);
            sliceBundle.getSliceList().addAll(pageSliceList);
            SliceDownloadResponse<T> maxPageResponse = sliceDownloader.download(maxPageSlice);
            sliceBundle.getResponses().add(maxPageResponse);
            buildAllPageSlice(maxPageResponse);
        }
        if (baseResponse.getSlice().getPage() == baseResponse.getSlice().getSliceParams().getMaxPage()) {
            Date newStartTime = timeExtractor.extractor(baseResponse.getList().get(baseResponse.getList().size() - 1));
            DownloadSlice slice = new DownloadSlice();
            slice.from(baseResponse.getSlice());
            slice.setPage(baseResponse.getSlice().getSliceParams().getPageStartIndex());
            slice.setStartTime(newStartTime);
            SliceDownloadResponse<T> response = sliceDownloader.download(slice);
            sliceBundle.getResponses().add(response);
            if (slice.getSliceParams().hasPageLimit() && response.getPageCount() > slice.getSliceParams().getMaxPage()) {
                buildAllPageSlice(response);
            } else {
                sliceBundle.getSliceList().addAll(buildPageSlice(slice, response.getSlice().getPage() + 1, response.getPageCount()));
            }
        }
    }

    public SliceBundle<T> buildSliceBundle(boolean multiThreadedDownloadOrderEnable) {
        // 获得一级切片，即所有符合时间跨度限制且页码为1的首页切片
        List<DownloadSlice> sliceList = this.buildOneLevelSlice();
        //探查所有按页码下载的一级切片，获得总页数
        int total = 0;
        List<DownloadSlice> pageSliceList = sliceList.stream().filter(slice -> slice.getType() == SliceType.PAGE).collect(Collectors.toList());
        List<DownloadSlice> cursorSliceList = sliceList.stream().filter(slice -> slice.getType() == SliceType.CURSOR).collect(Collectors.toList());
        if (pageSliceList.size() == 0) {
            //在所有待下载的切片中移除，页码类的一级切片，因为他即将执行探测，无需再次执行
            sliceList.removeAll(cursorSliceList);
            //游标类的切片无法提前切割，直接返回
            ThreadPool threadPool = ThreadPoolFactory.build(PlatformConstants.SLICE_BUILDER_THREAD_NAME);
            List<SliceDownloadResponse<T>> orderResponseList = threadPool.submitTaskList(sliceDownloader::download, cursorSliceList);
            for (SliceDownloadResponse<T> response : orderResponseList) {
                DownloadSlice slice = response.getSlice();
                sliceBundle.getResponses().add(response);
                if (!StringUtils.isEmpty(response.getCursor())) {
                    slice.setCursor(response.getCursor());
                    //快手售后虽然是游标方式 但是也要传入当前页
                    slice.setPage(slice.getPage() + 1);
                    sliceList.add(slice);
                }
            }
        } else {
            //在所有待下载的切片中移除，页码类的一级切片，因为他即将执行探测，无需再次执行
            sliceList.removeAll(pageSliceList);
            List<SliceDownloadResponse<T>> orderResponseList;
            if (multiThreadedDownloadOrderEnable){
                //如果开启了多线程下载，就按照页码类的一级切片进行探测
                ThreadPool threadPool = ThreadPoolFactory.build(PlatformConstants.SLICE_BUILDER_THREAD_NAME);
                orderResponseList = threadPool.submitTaskList(sliceDownloader::download, pageSliceList);
            }else{
                orderResponseList = new ArrayList<>();
                pageSliceList.forEach(slice -> orderResponseList.add(sliceDownloader.download(slice)));
            }

            for (SliceDownloadResponse<T> response : orderResponseList) {
                DownloadSlice slice = response.getSlice();
                SliceParams sliceParams = slice.getSliceParams();
                sliceBundle.getResponses().add(response);
                total += response.getTotal();
                if (sliceParams.hasPageLimit() && response.getPageCount() > sliceParams.getMaxPage()) {
                    buildAllPageSlice(response);
                } else {
                    sliceList.addAll(buildPageSlice(slice, sliceParams.getPageStartIndex() + 1, response.getPageCount()));
                }
            }
        }

        sliceBundle.getSliceList().addAll(sliceList);
        //存在游标下载的，就需要设置more标记为true，表示总数不确定
        sliceBundle.setMore(sliceBundle.getSliceList().stream().anyMatch(slice -> slice.getType() == SliceType.CURSOR));
        sliceBundle.setTotal(total);
        sliceBundle.initCountDownLatch();
        return sliceBundle;
    }
}
