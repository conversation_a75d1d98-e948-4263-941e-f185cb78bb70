package com.wsgjp.ct.sale.platform.sdk.entity.eshop;

import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class EshopSaleOrderSimpleEntity {
    private BigInteger id;

    private BigInteger profileId;
    private BigInteger otypeId;
    private String tradeId;
    private String storeId;
    private String storeCode;
    private TradeStatus tradeStatus;
    private Date payTime;
    private String platformJson;
    private List<EshopSaleOrderDetailSimpleEntity> details;
    private Map<String,EshopSaleOrderDetailSimpleEntity> groupDetails;

    private Map<String,List<EshopSaleOrderDetailSimpleEntity>> groupComboDetails;
    private String platformStockId;
    private String platformStockCode;

    private List<MarkData> markDataList;

    private String customerReceiverProvince;
    private String customerReceiverCity;
    private String customerReceiverDistrict;
    private String customerReceiverTown;
    /**
     * 创建方式(0:手工，1:下载）
     */
    private int createType;

    public String getCustomerReceiverTown() {
        return customerReceiverTown;
    }

    public void setCustomerReceiverTown(String customerReceiverTown) {
        this.customerReceiverTown = customerReceiverTown;
    }

    public String getCustomerReceiverProvince() {
        return customerReceiverProvince;
    }

    public void setCustomerReceiverProvince(String customerReceiverProvince) {
        this.customerReceiverProvince = customerReceiverProvince;
    }

    public String getCustomerReceiverCity() {
        return customerReceiverCity;
    }

    public void setCustomerReceiverCity(String customerReceiverCity) {
        this.customerReceiverCity = customerReceiverCity;
    }

    public String getCustomerReceiverDistrict() {
        return customerReceiverDistrict;
    }

    public void setCustomerReceiverDistrict(String customerReceiverDistrict) {
        this.customerReceiverDistrict = customerReceiverDistrict;
    }

    public String getPlatformStockId() {
        return platformStockId;
    }

    public void setPlatformStockId(String platformStockId) {
        this.platformStockId = platformStockId;
    }

    public String getPlatformStockCode() {
        return platformStockCode;
    }

    public void setPlatformStockCode(String platformStockCode) {
        this.platformStockCode = platformStockCode;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public List<EshopSaleOrderDetailSimpleEntity> getDetails() {
        return details;
    }

    public void setDetails(List<EshopSaleOrderDetailSimpleEntity> details) {
        this.details = details;
    }

    public Map<String, EshopSaleOrderDetailSimpleEntity> getGroupDetails() {
        return groupDetails;
    }

    public void setGroupDetails(Map<String, EshopSaleOrderDetailSimpleEntity> groupDetails) {
        this.groupDetails = groupDetails;
    }

    public String getPlatformJson() {
        return platformJson;
    }

    public void setPlatformJson(String platformJson) {
        this.platformJson = platformJson;
    }

    public List<MarkData> getMarkDataList() {
        if(markDataList == null){
            markDataList = new ArrayList<>();
        }
        return markDataList;
    }

    public void setMarkDataList(List<MarkData> markDataList) {
        this.markDataList = markDataList;
    }

    public Map<String, List<EshopSaleOrderDetailSimpleEntity>> getGroupComboDetails() {
        return groupComboDetails;
    }

    public void setGroupComboDetails(Map<String, List<EshopSaleOrderDetailSimpleEntity>> groupComboDetails) {
        this.groupComboDetails = groupComboDetails;
    }

    public int getCreateType() {
        return createType;
    }

    public void setCreateType(int createType) {
        this.createType = createType;
    }
}
