package com.wsgjp.ct.sale.platform.sdk.slice;

import com.wsgjp.ct.sale.common.enums.MonitorSourceEnum;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.platform.constraint.PlatformConstants;
import com.wsgjp.ct.sale.platform.entity.response.order.SliceDownloadResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.slice.DownloadSlice;
import com.wsgjp.ct.sale.platform.slice.SliceParams;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SliceDownloadContext<T> {
    private EshopFactory factory;
    private SliceDownloader<DownloadSlice, SliceDownloadResponse<T>> sliceDownloader;
    private SliceTimeExtractor<T> timeExtractor;
    private List<SliceParams> sliceParamsList;
    private boolean forceByApi = false;

    private String threadName;

    /**
     * 单个账套最大线程数
     */
    private int singleThreadCount = 4;

    private MonitorSourceEnum sourceEnum;

    private MonitorService monitor;

    public EshopFactory getFactory() {
        return factory;
    }

    public void setFactory(EshopFactory factory) {
        this.factory = factory;
    }

    public SliceTimeExtractor<T> getTimeExtractor() {
        return timeExtractor;
    }

    public void setTimeExtractor(SliceTimeExtractor<T> timeExtractor) {
        this.timeExtractor = timeExtractor;
    }

    public List<SliceParams> getSliceParamsList() {
        return sliceParamsList;
    }

    public void setSliceParamsList(List<SliceParams> sliceParamsList) {
        this.sliceParamsList = sliceParamsList;
    }

    public SliceDownloader<DownloadSlice, SliceDownloadResponse<T>> getSliceDownloader() {
        return sliceDownloader;
    }

    public void setSliceDownloader(SliceDownloader<DownloadSlice, SliceDownloadResponse<T>> sliceDownloader) {
        this.sliceDownloader = sliceDownloader;
    }

    public boolean isForceByApi() {
        return forceByApi;
    }

    public void setForceByApi(boolean forceByApi) {
        this.forceByApi = forceByApi;
    }

    public void setThreadName(String threadName) {
        this.threadName = threadName;
    }

    public String getThreadName() {
        if (threadName == null) {
            threadName = PlatformConstants.COMMON_THREAD_NAME;
        }
        return threadName;
    }

    public int getSingleThreadCount() {
        return singleThreadCount;
    }

    public void setSingleThreadCount(int singleThreadCount) {
        this.singleThreadCount = singleThreadCount;
    }

    public MonitorSourceEnum getSourceEnum() {
        if (sourceEnum == null) {
            sourceEnum = MonitorSourceEnum.NONE;
        }
        return sourceEnum;
    }

    public void setSourceEnum(MonitorSourceEnum sourceEnum) {
        this.sourceEnum = sourceEnum;
    }

    public MonitorService getMonitor() {
        return monitor;
    }

    public void setMonitor(MonitorService monitor) {
        this.monitor = monitor;
    }
}
