package com.wsgjp.ct.sale.platform.sdk.entity;

import java.math.BigInteger;

public class BaseQuery {
    private BigInteger profileId;

    private BigInteger etypeId;
    /**
     * 是否启用职员权限范围
     */
    private boolean etypeLimited;

    /**
     * 是否启用仓库权限范围
     */
    private boolean ktypeLimited;

    /**
     * 是否启用销售机构权限范围
     */
    private boolean otypeLimited;


    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEtypeId() {
        return etypeId;
    }

    public void setEtypeId(BigInteger etypeId) {
        this.etypeId = etypeId;
    }

    public boolean isEtypeLimited() {
        return etypeLimited;
    }

    public void setEtypeLimited(boolean etypeLimited) {
        this.etypeLimited = etypeLimited;
    }

    public boolean isKtypeLimited() {
        return ktypeLimited;
    }

    public void setKtypeLimited(boolean ktypeLimited) {
        this.ktypeLimited = ktypeLimited;
    }

    public boolean isOtypeLimited() {
        return otypeLimited;
    }

    public void setOtypeLimited(boolean otypeLimited) {
        this.otypeLimited = otypeLimited;
    }
}
