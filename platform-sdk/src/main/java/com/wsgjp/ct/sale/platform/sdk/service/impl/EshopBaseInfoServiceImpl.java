package com.wsgjp.ct.sale.platform.sdk.service.impl;

import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.entity.request.auth.TmcRegisterRequest;
import com.wsgjp.ct.sale.platform.entity.request.baseinfo.EshopBaseInfoRequest;
import com.wsgjp.ct.sale.platform.entity.request.baseinfo.QueryPlatformShopInfoRequest;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.baseinfo.EshopBaseInfoResponse;
import com.wsgjp.ct.sale.platform.entity.response.baseinfo.QueryPlatformShopInfoResponse;
import com.wsgjp.ct.sale.platform.entity.response.tmc.TmcRegisterResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.EshopTmcRegisterFeature;
import com.wsgjp.ct.sale.platform.feature.auth.EshopCheckAuthBaseInfoFeature;
import com.wsgjp.ct.sale.platform.feature.baseinfo.EshopBaseInfoFeature;
import com.wsgjp.ct.sale.platform.feature.baseinfo.EshopQueryPlatformShopInfoFeature;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.CheckTmcRegisterResponse;
import com.wsgjp.ct.sale.platform.sdk.entity.eshop.RegisterType;
import com.wsgjp.ct.sale.platform.sdk.service.EshopBaseInfoService;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class EshopBaseInfoServiceImpl implements EshopBaseInfoService {

    private static final Logger logger = LoggerFactory.getLogger(EshopBaseInfoServiceImpl.class);

    private static final String ENCRYPT_START = "ECODE";

    @Override
    public EshopBaseInfoResponse queryEshopBaseInfo(EshopBaseInfoRequest request) {
        //授权的时候调用该接口，授权信息还未落库。需要上面传入
        if (request.getSystemParams() != null) {
            //首次授权的时候去调用时pl_eshop表没有存授权相关的信息，不需要检查授权过期
            if (StringUtils.isBlank(request.getSystemParams().getToken())
                    || DateUtils.getDate().compareTo(request.getSystemParams().getExpiresIn()) > 0) {
                request.getSystemParams().setAuthCheckRequired(false);
            }
            request.getSystemParams().setToken(request.getToken());
        }
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopBaseInfoFeature feature = factory.getFeature(EshopBaseInfoFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持查询店铺基础信息", request.getSystemParams().getShopType().getPlatformName()));
        }
        return feature.queryEshopBaseInfo(request);
    }

    @Override
    public QueryPlatformShopInfoResponse queryPlatformShopInfo(QueryPlatformShopInfoRequest request) {
        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopQueryPlatformShopInfoFeature feature = factory.getFeature(EshopQueryPlatformShopInfoFeature.class);
        if (feature == null) {
            throw new RuntimeException(String.format("%s平台不支持查询平台店铺信息", request.getSystemParams().getShopType().getPlatformName()));
        }
        return feature.queryPlatformShopInfo(request);
    }

    @Override
    public CheckTmcRegisterResponse checkAndRegisterTmc(TmcRegisterRequest request) {

        EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
        EshopTmcRegisterFeature feature = factory.getFeature(EshopTmcRegisterFeature.class);
        CheckTmcRegisterResponse response = new CheckTmcRegisterResponse();
        if (feature == null) {
            response.setSuccess(false);
            response.setMessage(String.format("%s平台不支持注册TMC", request.getSystemParams().getShopType().getPlatformName()));
            response.setRegisterType(RegisterType.NOT_SUPPORT);
            return response;
        }
        boolean checkRegisterEnabled = checkRegisterEnabled(feature, request);
        if (checkRegisterEnabled) {
            response.setSuccess(true);
            response.setRegisterType(RegisterType.REPEAT);
            return response;
        }
        TmcRegisterResponse tmcRegisterResponse = feature.registerNotify(request);
        response.setSuccess(tmcRegisterResponse.isSuccess());
        response.setRegisterType(tmcRegisterResponse.isSuccess() ? RegisterType.SUCCESS : RegisterType.FAIL);
        return response;
    }

    @Override
    public BaseResponse checkAuthBaseInfo(EshopBaseInfoRequest request) {
        try {
            if (request.getSystemParams() == null) {
                request.setSystemParams(new EshopSystemParams());
                request.getSystemParams().seteShopId(request.getShopId());
                request.getSystemParams().setProfileId(CurrentUser.getProfileId());
            }

            //首次授权的时候去调用时pl_eshop表没有存授权相关的信息，不需要检查授权过期
            if (StringUtils.isBlank(request.getSystemParams().getToken())
                    || DateUtils.getDate().compareTo(request.getSystemParams().getExpiresIn()) > 0) {
                request.getSystemParams().setAuthCheckRequired(false);
            }

            //用请求中的最新token和shopaccount替换数据库中的数据
            if (StringUtils.isNotEmpty(request.getToken()) && !request.getToken().startsWith(ENCRYPT_START)) {
                request.getSystemParams().setToken(request.getToken());
            }

            if (StringUtils.isNotEmpty(request.getEshopAccount())) {
                request.getSystemParams().setShopAccount(request.getEshopAccount());
            }
            logger.error("检查店铺基础信息,昵称等,店铺id:{},request:{},systemParams:{}",
                    request.getShopId(), JsonUtils.toJson(request),
                    request.getSystemParams() == null ? "null" : JsonUtils.toJson(request.getSystemParams()));
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), request.getSystemParams());
            EshopCheckAuthBaseInfoFeature checkAuthFeature = factory.getFeature(EshopCheckAuthBaseInfoFeature.class);
            BaseResponse response = new BaseResponse();
            if (checkAuthFeature != null) {
                response = checkAuthFeature.checkAuthBaseInfo(request);
            }
            logger.error("检查店铺基础信息,昵称等,店铺id:{},response:{}",
                    request.getShopId(), JsonUtils.toJson(response));
            return response;
        } catch (Exception e) {
            logger.error("检查店铺基础信息,昵称等出现异常,店铺id:{},exception:{}",
                    request.getShopId(), e.getMessage(), e);
            return new BaseResponse();
        }
    }

    private boolean checkRegisterEnabled(EshopTmcRegisterFeature feature, TmcRegisterRequest request) {
        try {
            EshopSystemParams systemParams = request.getSystemParams();
            Boolean openTmc = systemParams.getOpenTmc();
            Boolean registerEnabled = request.getCheckRegisterEnabled();
            if (openTmc && registerEnabled) {
                return feature.checkRegister(request);
            }
            return false;
        } catch (Exception ex) {
            logger.error("检查TMC注册状态失败", ex);
            return false;
        }
    }
}
