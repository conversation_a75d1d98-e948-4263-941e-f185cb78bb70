package com.wsgjp.ct.sale.platform.sdk.entity.send;

import com.wsgjp.ct.common.enums.core.enums.SyncFreightStatus;

import java.math.BigInteger;

public class FreightBillNoSyncRecordUpdateInfo {
    private BigInteger id;
    private SyncFreightStatus status;
    private String message;
    private String platformFreightId;
    /**
     * 发货单号
     */
    private String deliveryCode;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public SyncFreightStatus getStatus() {
        return status;
    }

    public void setStatus(SyncFreightStatus status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getPlatformFreightId() {
        return platformFreightId;
    }

    public void setPlatformFreightId(String platformFreightId) {
        this.platformFreightId = platformFreightId;
    }

    public String getDeliveryCode() {
        return deliveryCode;
    }

    public void setDeliveryCode(String deliveryCode) {
        this.deliveryCode = deliveryCode;
    }
}
