package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.address.QueryPlatformAddressRequest;
import com.wsgjp.ct.sale.platform.entity.request.product.*;
import com.wsgjp.ct.sale.platform.entity.response.address.QueryPlatformAddressResponse;
import com.wsgjp.ct.sale.platform.entity.response.product.*;

import java.util.List;

/**
 * 发布商品相关
 */
public interface EshopPublishProductService {
    /**
     * 查询品牌
     * @return
     */
    QueryEshopBrandResponse queryEshopBrands(QueryBrandRequest request);

    /**
     * 查询商品类目
     */
    QueryProductCategoryResponse queryProductCategory(BaseRequest request);

    /**
     * 新增平台商品
     */
    CreateProductResponse createProduct(CreateProductRequest request);

    /**
     *修改商品信息
     */
    UpdateProductResponse updateProduct(UpdateProductRequest request);

    /**
     * 查询平台销售单位
     */
    QuerySaleUnitResponse querySaleUnits(QuerySaleUnitRequest request);
    /**
     * 查询扩展属性
     */
    QueryExtPropsResponse queryExtProps(QueryExtPropsRequest request);

    /**
     * 查询销售属性
     */
    QuerySalePropsResponse querySaleProps(QuerySalePropsRequest request);

    /**
     * 查询规格属性
     */
    QuerySpecPropsResponse querySpecProps(QuerySpecPropsRequest request);

    /**
     * 查询商品特殊业务
     * 是否是otc分类商品
     * 是否是处方药分类
     * 是否是资质敏感分类
     * 是否时多属性商品
     */
    QueryProductBizResponse queryProductBiz(QueryProductBizRequest request);

    /**
     * 添加商品主图信息
     */
    AddProductPrimaryPicResponse addProductPrimaryPic(AddProductPrimaryPicRequest request);

    /**
     * 批量发布商品配置
     */
    SetProductConfigResponse batchSetProductConfig(SetProductConfigRequest request);

    /**
     * 查询商品发布申请信息
     */
    QueryProductPublishApplyInfoResponse queryProductPublishApplyInfo(QueryProductPublishApplyInfoRequest request);

    /**
     * 获取平台视频上传地址信息
     */
    GeVideoUploadPathUrlResponse getVideoUploadUrlInfo(GeVideoUploadUrlInfoRequest request);

    /**
     * 查询采购员列表信息
     */
    QueryPurchaserResponse queryPurchaserList(BaseRequest request);

    /**
     * 查询平台图片存储空间信息
     */
    QueryImageStorageSpaceInfoResponse queryImageStorageSpaceInfo(QueryImageStorageSpaceInfoRequest request);

    /**
     * 查询危险品列表
     * 用于商品发布信息维护
     */
    QueryDangerGoodsResponse queryDangerGoods(BaseRequest request);

    /**
     * 上传图片
     */
    PlatImageUploadResponse imageUpload(PlatImageUploadRequest request);

    /**
     * 文件上传平台
     */
    PlatFileUploadResponse fileUpload(PlatFileUploadRequest request);
    /**
     * 更新图片信息
     */
    PlatImageUpdateResponse imageUpdate(PlatImageUpdateRequest request);

    /**
     * 删除图片
     */
    PlatImageDeleteResponse imageDelete(PlatImageDeleteRequest request);

    /**
     * 查询图片
     */
    PlatImageQueryResponse imageQuery(PlatImageQueryRequest request);

    /**
     * 获取三级类目特殊属性
     * @param request
     * @return
     */
    QueryMarkByCatIdResponse getBaseMarkByCatId(QueryMarkByCatIdRequest request);

    /**
     * 根据销售员code获取中文名称
     * @param request
     * @return
     */
    QuerySalerResponse querySalerInfo(QuerySalerRequest request);

    /**
     * 根据三级类目id获取商品信息动态字段
     * @param request
     * @return
     */
    ItemDynamicFieldGaeaResponse getItemDynamicFieldGaea(ItemDynamicFieldGaeaRequest request);

    /**
     *获取标品模板配置
     * @param request
     * @return
     */
    ItemSpuTemplateResponse getItemSpuTemplate(ItemSpuTemplateRequest request);
    /**
     * 查询型号规则
     * @param request
     * @return
     */
    ItemModelResponse getItemModel(ItemSpuTemplateRequest request);

    /**
     * 更新被驳回的主图申请
     * @param request
     * @return
     */
    UpdateProductPrimaryPicResponse updateItemPrimaryPic(UpdateProductPrimaryPicRequest request);

    /**
     * 根据申请编号查询商品主图
     * @param request
     * @return
     */
    QueryProductPrimaryPicResponse queryApplyItemPrimaryPic(QueryProductPrimaryPicRequest request);

    /**
     * 获取商品主图申请列表
     * @param request
     * @return
     */
    QueryItemAppliesPrimaryPicResponse queryItemAppliesPrimaryPic(QueryItemAppliesPrimaryPicRequest request);


    /**
     * 根据父节点获取子类目列表
     *
     * @param request
     * @return
     */
    QueryProductCategoryResponse queryProductCategoryListByParent(QueryCatByParentRequest request);

    /**
     * 检查网店是否有某个类目
     */

    CheckSupportCategoryResponse checkSupportCategory(CheckSupportCategoryRequest request);


    List<PublishProductFieldsMapping> queryEshopPublishProductFieldsMapping(BaseRequest request);

    /**
     * 查询产品信息(非商品,是商品需要挂靠的产品信息)
     */
    ItemDynamicFieldGaeaResponse queryProductSchemaByCatId(ItemDynamicFieldGaeaRequest request);

    ItemDynamicFieldGaeaResponse matchProduct(MatchProductRequest request);

    /**
     * 查询平台地址库
     */
    QueryPlatformAddressResponse queryPlatformAddress(QueryPlatformAddressRequest request);

    ItemDynamicFieldGaeaResponse querySubProps(QuerySubPropsRequest request);

    /**
     * 查询标品类目规则
     */
    GetCategoryRuleResponse getCategoryRule(GetCategoryRuleRequest request);
    /**
     * 通过类目、品牌查询标品的型号/货号列表
     */
    GetModelOrItemNumListResponse getModelOrItemNumList(GetModelOrItemNumListRequest request);
    /**
     * 标品查询
     */
    StandardProductSearchResponse standardProductSearch(StandardProductSearchRequest request);

    EshopProductBuildSchemaResponse buildPublishSchema(EshopBuildPublishSchemaRequest request);
}
