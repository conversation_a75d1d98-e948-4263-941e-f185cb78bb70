package com.wsgjp.ct.sale.platform.sdk.entity;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.support.kms.KmsClient;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.Date;


/**
 * @author: lyz
 * @description:
 */
public class EshopInfo {
    private static final Logger sysLogger = LoggerFactory.getLogger(EshopInfo.class);

    private BigInteger otypeId;
    private BigInteger profileId;
    private ShopType eshopType;
    private int eshopTypeInt;
    private int mappingType;
    private int isAuth;
    private int eshopSalePlatform;
    private String fullname="";
    private String eshopAccount="";
    private String onlineEshopId="";
    private String token="";
    private String appKey="";
    private String appSecret="";
    private String refreshToken="";
    private Date tokenExpireIn;
    private Date tokenR1ExpireIn;
    private Date refreshTokenExpireIn;
    private Date refreshTime;
    private Date createTime;
    private Date modifyTime;
    private boolean hasTokenExpired;
    private boolean hasException;
    private boolean deleted;
    private boolean stoped;
    private boolean newShop;
    private boolean autoSyncOrderEnabled;
    private boolean autoSyncStockEnabled;
    private boolean soldOutSyncEnabled = true;
    private String userName;
    private String password;
    private boolean rdsEnabled;
    private Date rdsApplyTime;
    private Date rdsReadyTime;
    private Date rdsCheckTime;
    private Integer ocategory;
    private String ktypeIds;
    private String ruleCron;
    private String vendorId="";
    private String platformEshopId="";
    private boolean agEnabled;
    private String platformEshopSnType="";
    private Integer mutiSelectAppkey;
    private boolean isOpenXcode;
    private Integer mallType;
    // 最后一次同步至中台时间
    private Date lastUploadBusinessTime;

    private int btypeGenerateType;

    private String rdsName;

    private BigInteger platfromConfig;

    private boolean isSkuMemoDesired;

    private int downloadOrderType;
    private Boolean tmcEnabled = false;

    private boolean mainEshop;
    private String groupId;

    private boolean reissiueSyncFreight;

    public boolean isReissiueSyncFreight() {
        return reissiueSyncFreight;
    }

    public void setReissiueSyncFreight(boolean reissiueSyncFreight) {
        this.reissiueSyncFreight = reissiueSyncFreight;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public ShopType getEshopType() {
        return eshopType;
    }

    public void setEshopType(ShopType eshopType) {
        this.eshopType = eshopType;
    }

    public int getEshopTypeInt() {
        return eshopTypeInt;
    }

    public void setEshopTypeInt(int eshopTypeInt) {
        this.eshopTypeInt = eshopTypeInt;
    }

    public int getMappingType() {
        return mappingType;
    }

    public void setMappingType(int mappingType) {
        this.mappingType = mappingType;
    }

    public int getEshopSalePlatform() {
        return eshopSalePlatform;
    }

    public void setEshopSalePlatform(int eshopSalePlatform) {
        this.eshopSalePlatform = eshopSalePlatform;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getEshopAccount() {
        return eshopAccount;
    }

    public void setEshopAccount(String eshopAccount) {
        this.eshopAccount = eshopAccount;
    }

    public String getOnlineEshopId() {
        return onlineEshopId;
    }

    public void setOnlineEshopId(String onlineEshopId) {
        this.onlineEshopId = onlineEshopId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getTokenExpireIn() {
        return tokenExpireIn;
    }

    public void setTokenExpireIn(Date tokenExpireIn) {
        this.tokenExpireIn = tokenExpireIn;
    }

    public Date getTokenR1ExpireIn() {
        return tokenR1ExpireIn;
    }

    public void setTokenR1ExpireIn(Date tokenR1ExpireIn) {
        this.tokenR1ExpireIn = tokenR1ExpireIn;
    }

    public Date getRefreshTokenExpireIn() {
        return refreshTokenExpireIn;
    }

    public void setRefreshTokenExpireIn(Date refreshTokenExpireIn) {
        this.refreshTokenExpireIn = refreshTokenExpireIn;
    }

    public Date getRefreshTime() {
        return refreshTime;
    }

    public void setRefreshTime(Date refreshTime) {
        this.refreshTime = refreshTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public boolean isHasTokenExpired() {
        return hasTokenExpired;
    }

    public void setHasTokenExpired(boolean hasTokenExpired) {
        this.hasTokenExpired = hasTokenExpired;
    }

    public boolean isHasException() {
        return hasException;
    }

    public void setHasException(boolean hasException) {
        this.hasException = hasException;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public boolean isStoped() {
        return stoped;
    }

    public void setStoped(boolean stoped) {
        this.stoped = stoped;
    }

    public boolean isNewShop() {
        return newShop;
    }

    public void setNewShop(boolean newShop) {
        this.newShop = newShop;
    }

    public boolean isAutoSyncOrderEnabled() {
        return autoSyncOrderEnabled;
    }

    public void setAutoSyncOrderEnabled(boolean autoSyncOrderEnabled) {
        this.autoSyncOrderEnabled = autoSyncOrderEnabled;
    }

    public boolean isAutoSyncStockEnabled() {
        return autoSyncStockEnabled;
    }

    public void setAutoSyncStockEnabled(boolean autoSyncStockEnabled) {
        this.autoSyncStockEnabled = autoSyncStockEnabled;
    }

    public boolean isSoldOutSyncEnabled() {
        return soldOutSyncEnabled;
    }

    public void setSoldOutSyncEnabled(boolean soldOutSyncEnabled) {
        this.soldOutSyncEnabled = soldOutSyncEnabled;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isRdsEnabled() {
        return rdsEnabled;
    }

    public void setRdsEnabled(boolean rdsEnabled) {
        this.rdsEnabled = rdsEnabled;
    }

    public Date getRdsApplyTime() {
        return rdsApplyTime;
    }

    public void setRdsApplyTime(Date rdsApplyTime) {
        this.rdsApplyTime = rdsApplyTime;
    }

    public Integer getOcategory() {
        return ocategory;
    }

    public void setOcategory(Integer ocategory) {
        this.ocategory = ocategory;
    }

    public String getKtypeIds() {
        return ktypeIds;
    }

    public void setKtypeIds(String ktypeIds) {
        this.ktypeIds = ktypeIds;
    }

    public String getRuleCron() {
        return ruleCron;
    }

    public void setRuleCron(String ruleCron) {
        this.ruleCron = ruleCron;
    }

    public String getVendorId() {
        return vendorId;
    }

    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    public String getPlatformEshopId() {
        return platformEshopId;
    }

    public void setPlatformEshopId(String platformEshopId) {
        this.platformEshopId = platformEshopId;
    }

    public boolean isAgEnabled() {
        return agEnabled;
    }

    public void setAgEnabled(boolean agEnabled) {
        this.agEnabled = agEnabled;
    }

    public String getPlatformEshopSnType() {
        return platformEshopSnType;
    }

    public void setPlatformEshopSnType(String platformEshopSnType) {
        this.platformEshopSnType = platformEshopSnType;
    }

    public Integer getMutiSelectAppkey() {
        return mutiSelectAppkey;
    }

    public void setMutiSelectAppkey(Integer mutiSelectAppkey) {
        this.mutiSelectAppkey = mutiSelectAppkey;
    }

    public boolean isMainEshop() {
        return mainEshop;
    }

    public void setMainEshop(boolean mainEshop) {
        this.mainEshop = mainEshop;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    @Override
    public String toString() {
        return "EshopInfo{" +
                "otypeId=" + otypeId +
                ", profileId=" + profileId +
                ", eshopType=" + eshopType +
                ", eshopTypeInt=" + eshopTypeInt +
                ", mappingType=" + mappingType +
                ", eshopSalePlatform=" + eshopSalePlatform +
                ", fullname='" + fullname + '\'' +
                ", eshopAccount='" + eshopAccount + '\'' +
                ", onlineEshopId='" + onlineEshopId + '\'' +
                ", token='" + token + '\'' +
                ", appKey='" + appKey + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", refreshToken='" + refreshToken + '\'' +
                ", tokenExpireIn=" + tokenExpireIn +
                ", tokenR1ExpireIn=" + tokenR1ExpireIn +
                ", refreshTokenExpireIn=" + refreshTokenExpireIn +
                ", refreshTime=" + refreshTime +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                ", hasTokenExpired=" + hasTokenExpired +
                ", hasException=" + hasException +
                ", deleted=" + deleted +
                ", stoped=" + stoped +
                ", newShop=" + newShop +
                ", autoSyncOrderEnabled=" + autoSyncOrderEnabled +
                ", autoSyncStockEnabled=" + autoSyncStockEnabled +
                ", soldOutSyncEnabled=" + soldOutSyncEnabled +
                ", userName='" + userName + '\'' +
                ", password='" + password + '\'' +
                ", rdsEnabled=" + rdsEnabled +
                ", rdsApplyTime=" + rdsApplyTime +
                ", ocategory=" + ocategory +
                ", ktypeIds='" + ktypeIds + '\'' +
                ", ruleCron='" + ruleCron + '\'' +
                ", vendorId='" + vendorId + '\'' +
                ", platformEshopId='" + platformEshopId + '\'' +
                ", agEnabled=" + agEnabled +
                ", platformEshopSnType='" + platformEshopSnType + '\'' +
                '}';
    }

    public boolean isOpenXcode() {
        return isOpenXcode;
    }

    public void setOpenXcode(boolean openXcode) {
        isOpenXcode = openXcode;
    }

    public Integer getMallType() {
        return mallType;
    }

    public void setMallType(Integer mallType) {
        this.mallType = mallType;
    }

    public Date getLastUploadBusinessTime() {
        return lastUploadBusinessTime;
    }

    public void setLastUploadBusinessTime(Date lastUploadBusinessTime) {
        this.lastUploadBusinessTime = lastUploadBusinessTime;
    }

    public int getIsAuth() {
        return isAuth;
    }

    public void setIsAuth(int isAuth) {
        this.isAuth = isAuth;
    }

    public int getBtypeGenerateType() {
        return btypeGenerateType;
    }

    public void setBtypeGenerateType(int btypeGenerateType) {
        this.btypeGenerateType = btypeGenerateType;
    }

    public Date getRdsReadyTime() {
        return rdsReadyTime;
    }

    public void setRdsReadyTime(Date rdsReadyTime) {
        this.rdsReadyTime = rdsReadyTime;
    }

    public Date getRdsCheckTime() {
        return rdsCheckTime;
    }

    public void setRdsCheckTime(Date rdsCheckTime) {
        this.rdsCheckTime = rdsCheckTime;
    }

    public String getRdsName() {
        return rdsName;
    }

    public void setRdsName(String rdsName) {
        this.rdsName = rdsName;
    }

    public BigInteger getPlatfromConfig() {
        return platfromConfig;
    }

    public void setPlatfromConfig(BigInteger platfromConfig) {
        this.platfromConfig = platfromConfig;
    }

    public boolean isSkuMemoDesired() {
        return isSkuMemoDesired;
    }

    public void setSkuMemoDesired(boolean skuMemoDesired) {
        isSkuMemoDesired = skuMemoDesired;
    }

    public int getDownloadOrderType() {
        return downloadOrderType;
    }

    public void setDownloadOrderType(int downloadOrderType) {
        this.downloadOrderType = downloadOrderType;
    }
    public String getDecryptToken() {
        String decryptToken = "";
        if (StringUtils.isNotEmpty(getToken())) {
            decryptToken = KmsClient.decrypt(getToken());
            try {
                if (StringUtils.isNotEmpty(decryptToken) && decryptToken.contains(":")){
                    decryptToken = decryptToken.split(":")[0];
                    if (StringUtils.startsWith(decryptToken,"ECODE")){
                        throw new RuntimeException("KmsClient解密token失败：密文信息:" + getToken());
                    }
                }
            } catch (Exception e) {
                sysLogger.error("账套id:{},店铺id:{},{}",getProfileId(),getOtypeId(),e.getMessage(),e);
            }

        }
        return decryptToken;
    }

    public String getDecryptRefreshToken() {
        String decryptToken = "";
        if (StringUtils.isNotEmpty(getRefreshToken())) {
            decryptToken = KmsClient.decrypt(getRefreshToken());
            try {
                if (StringUtils.isNotEmpty(decryptToken) && decryptToken.contains(":")){
                    decryptToken = decryptToken.split(":")[0];
                    if (StringUtils.startsWith(decryptToken,"ECODE")){
                        throw new RuntimeException("KmsClient解密刷新token失败：密文信息:" + getRefreshToken());
                    }
                }
            } catch (Exception e) {
                sysLogger.error("账套id:{},店铺id:{},{}",getProfileId(),getOtypeId(),e.getMessage(),e);
            }
        }
        return decryptToken;
    }

    public String getDecryptAppKey() {
        String decryptKey = "";
        if (StringUtils.isNotEmpty(getAppKey())) {
            decryptKey = KmsClient.decrypt(getAppKey());
        }
        return decryptKey;
    }

    public String getDecryptAppSecret() {
        String decryptSecret = "";
        if (StringUtils.isNotEmpty(getAppSecret())) {
            decryptSecret = KmsClient.decrypt(getAppSecret());
        }
        return decryptSecret;
    }

    public Boolean getTmcEnabled() {
        return tmcEnabled;
    }

    public void setTmcEnabled(Boolean tmcEnabled) {
        this.tmcEnabled = tmcEnabled;
    }
}
