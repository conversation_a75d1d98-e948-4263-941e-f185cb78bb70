package com.wsgjp.ct.sale.platform.sdk.mapper;

import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.token.EshopAuthInfo;
import com.wsgjp.ct.sale.platform.sdk.entity.EshopInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PlatformSdkEshopMapper {

    /**
     * 根据系统店铺id获取店铺信息 包含pl_eshop_config信息
     *
     * @param profileId 账套id
     * @param shopId    系统店铺id
     * @return
     */
    EshopInfo getEshopInfoByShopId(@Param("profileId") BigInteger profileId, @Param("shopId") BigInteger shopId);

    /**
     * 更新网店授权信息
     *
     * @param tokenInfo
     * @return
     */
    int updateEshopToken(EshopAuthInfo tokenInfo);

    boolean updateEshopNoticeById(@Param("profileId") BigInteger profileId, @Param("shopId") BigInteger shopId,@Param("expireNotice") int expireNotice);

    /**
     * rds 状态更新
     * @param systemParams 店铺授权基本信息
     */
    void updateEshopConfigRdsState(EshopSystemParams systemParams);

}
