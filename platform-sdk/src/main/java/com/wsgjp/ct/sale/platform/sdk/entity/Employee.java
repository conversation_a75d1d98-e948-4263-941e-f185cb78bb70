package com.wsgjp.ct.sale.platform.sdk.entity;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class Employee {
    private BigInteger id;
    private String fullname;
    private BigInteger dtypeId;
    private boolean stoped;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public BigInteger getDtypeId() {
        return dtypeId;
    }

    public void setDtypeId(BigInteger dtypeId) {
        this.dtypeId = dtypeId;
    }

    public boolean isStop() {
        return stoped;
    }

    public void setStop(boolean stop) {
        this.stoped = stop;
    }
}
