package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.platform.entity.request.delivery.*;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.delivery.CreatePoDeliveryResponse;

public interface EshopPoDeliverService {
    /**
     * 创建出库单
     * @param request
     * @return
     */
    CreatePoDeliveryResponse createPoDelivery(CreatePoDeliveryRequest request);


    /**
     * 编辑出库单
     * @param request
     * @return
     */
    CreatePoDeliveryResponse editPoDelivery(EditPoDeliveryRequest request);

    /**
     * 取消出库单
     * @param request
     * @return
     */
    BaseResponse cancelPoDelivery(CancelPoDeliveryRequest request);

    /**
     * 确认出库单
     * @param request
     * @return
     */
    BaseResponse confirmPoDelivery(ConfirmPoDeliveryRequest request);


    /**
     * 导入出库明细
     * @param request
     * @return
     */
    BaseResponse  imporPoDeliveryDetails(ImporPoDeliveryDetailRequest request);
}
