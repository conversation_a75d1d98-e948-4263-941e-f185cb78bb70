package com.wsgjp.ct.sale.platform.sdk.mapper;


import com.wsgjp.ct.sale.platform.sdk.entity.ComboDetail;
import com.wsgjp.ct.sale.platform.sdk.entity.Etype;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PlatformSdkEshopBaseInfoMapper {
    List<Etype> getEtypeList(BigInteger profileId);

    List<ComboDetail> batchQueryComboDetail(@Param("profileId") BigInteger profileId, @Param("comboIds") List<BigInteger> comboIds);
}
