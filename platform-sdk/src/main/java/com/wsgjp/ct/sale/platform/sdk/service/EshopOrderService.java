package com.wsgjp.ct.sale.platform.sdk.service;

import com.wsgjp.ct.sale.platform.dto.order.entity.OrderInvoice;
import com.wsgjp.ct.sale.platform.entity.request.download.DownloadFileRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.*;
import com.wsgjp.ct.sale.platform.entity.response.download.DownloadFileResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.*;

/**
 * <AUTHOR>
 */
public interface EshopOrderService {
    /**
     * 根据自提核销码查询订单信息
     * @param request QueryOrderBySelfFetchCodeRequest
     * @return QueryOrderBySelfFetchCodeResponse
     */
    QueryOrderBySelfFetchCodeResponse queryOrderBySelfFetchCode(QueryOrderBySelfFetchCodeRequest request);

    /**
     * 根据发货地址 订单号 获取物流信息
     * @param deliveryExpressRequset
     * @return
     */
    DeliveryExpressResponse getDeliveryExpress(DeliveryExpressRequset deliveryExpressRequset);


    /**
     * 根据发货地址 订单号 获取黑名单物流信息
     * @param deliveryExpressRequset
     * @return
     */
    DeliveryExpressResponse getDeliveryExpressBlacklist(DeliveryExpressRequset deliveryExpressRequset);

    /**
     * 批量打印订单箱唛(包裹标签)(订单装箱标签)
     * @param request PrintOrderShippingMarksRequest
     * @return PrintOrderShippingMarksResponse
     */
    PrintOrderShippingMarksResponse batchPrintOrderShippingMarks(PrintOrderShippingMarksRequest request);

    /**
     * 批量打印商品条码
     * @param request PrintProductBarcodeRequest
     * @return PrintProductBarcodeResponse
     */
    PrintProductBarcodeResponse batchPrintOrderProductBarcode(PrintProductBarcodeRequest request);

    /**
     * 查询订单可以使用的物流公司列表
     */
    QueryExpressCompanyListResponse queryExpressCompanyList(QueryExpressCompanyListRequest request);

    /**
     * 创建发货单
     * @param request
     * @return
     */
    CreateShipOrderResponse createShipOrder(CreateShipOrderRequest request);



    UpdateWeightResponse updateWeight(UpdateWeightRequest request);

    /**
     * 订单上传序列号
     *
     * @param request
     * @return
     */
    UploadSnResponse uploadSnToOrders(UploadSnInfo request);
    /**
     * 骑手轨迹上传
     */
    LocationSyncResponse locationSync(LocationSyncRequest request);

    /**
     * 自配送骑手状态上传
     */
    SelfDeliveryStateSyncResponse selfDeliveryStateSync(SelfDeliveryStateSyncRequest request);

    /**
     * 订单拣货完成回传
     *
     * @param request
     * @return
     */
    OrderPickCompleteResponse orderPickComplete(OrderPickCompleteRequest request);

    /**
     * 下载文件
     */
    DownloadFileResponse downloadFile(DownloadFileRequest request);

    /**
     * 获取订单发票
     */
    OrderInvoice getInvoice(GetInvoiceRequest invoiceRequest);
}
