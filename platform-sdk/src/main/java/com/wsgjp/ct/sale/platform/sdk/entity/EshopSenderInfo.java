package com.wsgjp.ct.sale.platform.sdk.entity;

import ngp.utils.Md5Utils;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class EshopSenderInfo {
    public EshopSenderInfo(){
        senderCountry="中国";
        senderProvince="";
        senderCity="";
        senderDistrict="";
        senderTown="";
        senderAddress="";
        senderMobile="";
        senderPhone="";
    }
    private BigInteger profileId;
    private BigInteger id;
    private String senderName;
    private String senderMobile;
    private String senderPhone;
    private String senderCountry;
    private String senderProvince;
    private String senderCity;
    private String senderDistrict;
    private String senderTown;
    private String senderAddress;
    private String senderZipCode;
    private String senderFullAddress;
    private String hashKey;
    private String senderDetailAddress;

    public void setHashKey(String hashKey) {
        this.hashKey = hashKey;
    }

    public String getSenderDetailAddress() {
        return senderDetailAddress;
    }

    public void setSenderDetailAddress(String senderDetailAddress) {
        this.senderDetailAddress = senderDetailAddress;
    }

    public void setSenderFullAddress(String senderFullAddress) {
        this.senderFullAddress = senderFullAddress;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getSenderName() {
        if(senderName==null){
            return "";
        }
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderMobile() {
        if(senderMobile==null){
            senderMobile="";
        }
        return senderMobile;
    }

    public void setSenderMobile(String senderMobile) {
        this.senderMobile = senderMobile;
    }

    public String getSenderPhone() {
        if(senderPhone==null){
            senderPhone="";
        }
        return senderPhone;
    }

    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }

    public String getSenderProvince() {
        if(senderProvince==null){
            senderProvince="";
        }
        return senderProvince;
    }

    public void setSenderProvince(String senderProvince) {
        this.senderProvince = senderProvince;
    }

    public String getSenderCity() {
        if(senderCity==null){
            senderCity="";
        }
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    public String getSenderDistrict() {
        if(senderDistrict==null){
            senderDistrict="";
        }
        return senderDistrict;
    }

    public void setSenderDistrict(String senderDistrict) {
        this.senderDistrict = senderDistrict;
    }

    public String getSenderAddress() {
        if(senderAddress==null){
            return "";
        }
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    public String getSenderFullAddress() {
        StringBuilder sb=new StringBuilder();
        if(senderCountry!=null&&!"".equals(senderCountry)){
            sb.append(senderCountry);
        }
        if(senderProvince!=null&&!"".equals(senderProvince)){
            sb.append(senderProvince);
        }
        if(senderCity!=null&&!"".equals(senderCity)){
            sb.append(senderCity);
        }
        if(senderDistrict!=null&&!"".equals(senderDistrict)){
            sb.append(senderDistrict);
        }
        if(senderTown!=null&&!"".equals(senderTown)){
            sb.append(senderTown);
        }
        if(senderAddress!=null&&!"".equals(senderAddress)){
            sb.append(senderAddress);
        }
        return sb.toString();
    }

    public String getHashKey() {
        return Md5Utils.md5(String.format("%d%s%s%s",profileId, getSenderName(),getSenderPhone(),getSenderFullAddress()));
    }

    public String getSenderCountry() {
        if(senderCountry==null){
            return "中国";
        }
        return senderCountry;
    }

    public void setSenderCountry(String senderCountry) {
        this.senderCountry = senderCountry;
    }

    public String getSenderZipCode() {
        if(senderZipCode==null){
            senderZipCode="";
        }
        return senderZipCode;
    }

    public void setSenderZipCode(String senderZipCode) {
        this.senderZipCode = senderZipCode;
    }

    public String getSenderTown() {
        if(senderTown==null){
            senderTown="";
        }
        return senderTown;
    }

    public void setSenderTown(String senderTown) {
        this.senderTown = senderTown;
    }
}