package com.wsgjp.ct.sale.platform.sdk.entity;


import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class ComboDetail implements Cloneable {
    private BigInteger id;
    private BigInteger comboId;
    private String comboCode;
    private String comboName;
    private BigInteger ptypeId;
    private BigInteger skuId;
    private String xcode;
    private BigInteger unitId;
    private BigInteger baseUnitId;
    private BigDecimal qty;
    private BigDecimal price;
    private BigDecimal total;
    private BigDecimal unitRate;
    private Boolean necessarySku;
    private Boolean joinAdjustTotal;
    private Boolean gifted;
    private Boolean stoped;
    private BigDecimal scale;
    private BigDecimal taxRate;
    /**
     * 是否有代销标签
     */
    private Boolean saleProxyLabel = false;
    private Boolean mainPtype;


    public Boolean getSaleProxyLabel() {
        return saleProxyLabel;
    }

    public void setSaleProxyLabel(Boolean saleProxyLabel) {
        this.saleProxyLabel = saleProxyLabel;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigInteger getComboId() {
        return comboId;
    }

    public void setComboId(BigInteger comboId) {
        this.comboId = comboId;
    }

    public BigInteger getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public BigInteger getUnitId() {
        return unitId;
    }

    public void setUnitId(BigInteger unitId) {
        this.unitId = unitId;
    }

    public BigDecimal getQty() {
        if(qty==null || qty.compareTo(BigDecimal.ZERO)<=0){
            return BigDecimal.ONE;
        }
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getBaseUnitId() {
        return baseUnitId;
    }

    public void setBaseUnitId(BigInteger baseUnitId) {
        this.baseUnitId = baseUnitId;
    }

    public BigDecimal getUnitRate() {
        if(unitRate==null || unitRate.compareTo(BigDecimal.ZERO)==0){
            return BigDecimal.ONE;
        }
        return unitRate;
    }

    public void setUnitRate(BigDecimal unitRate) {
        this.unitRate = unitRate;
    }

    public Boolean getNecessarySku() {
        return necessarySku;
    }

    public void setNecessarySku(Boolean necessarySku) {
        this.necessarySku = necessarySku;
    }

    public Boolean getJoinAdjustTotal() {
        return joinAdjustTotal;
    }

    public void setJoinAdjustTotal(Boolean joinAdjustTotal) {
        this.joinAdjustTotal = joinAdjustTotal;
    }

    public Boolean getGifted() {
        return gifted;
    }

    public void setGifted(Boolean gifted) {
        this.gifted = gifted;
    }

    public Boolean getStoped() {
        return stoped;
    }

    public void setStoped(Boolean stoped) {
        this.stoped = stoped;
    }

    public String getComboCode() {
        return comboCode;
    }

    public void setComboCode(String comboCode) {
        this.comboCode = comboCode;
    }

    public String getComboName() {
        return comboName;
    }

    public void setComboName(String comboName) {
        this.comboName = comboName;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }


    public ComboDetail doClone() {
        ComboDetail clone = new ComboDetail();
        BeanUtils.copyProperties(this, clone);
        return clone;
    }
    public BigDecimal getScale() {
        if(scale == null)
        {
            return BigDecimal.ZERO;
        }
        return scale;
    }

    public void setScale(BigDecimal scale) {
        this.scale = scale;
    }

    public Boolean getMainPtype() {
        return mainPtype;
    }

    public void setMainPtype(Boolean mainPtype) {
        this.mainPtype = mainPtype;
    }
}
