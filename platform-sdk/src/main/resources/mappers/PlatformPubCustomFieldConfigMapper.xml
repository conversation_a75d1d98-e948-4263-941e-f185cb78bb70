<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformPubCustomFieldConfigMapper">
    <select id="queryCustomFieldConfig"
            resultType="com.wsgjp.ct.sale.platform.sdk.entity.PubCustomFiledConfigInfo">
        select profile_id, sub_type, business_type, display_name, data_field, field_name, field_id
        from pub_custom_field_config
        where profile_id = #{profileId}
          and sub_type = #{subType}
          and display_name = #{displayName}
          and business_type = 1
    </select>
</mapper>