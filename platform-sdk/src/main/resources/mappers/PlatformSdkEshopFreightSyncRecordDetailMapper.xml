<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopFreightSyncRecordDetailMapper">
    <insert id="add">
        replace into pl_eshop_freight_sync_record_detail(id, record_id, profile_id, sale_order_id, sale_order_detail_id,
        oid,
        deliver_order_id, deliver_order_detail_id, warehouse_task_id,
        warehouse_task_detail_id, trade_id, freight_code, freight_bill_no,
        sync_type, sync_qty, call_status, sync_status, sync_message,
        platform_freight_id,sync_time)
        values
        <foreach collection="details" item="detail" separator=",">
            (#{detail.id}, #{detail.recordId}, #{detail.profileId}, #{detail.saleOrderId}, #{detail.saleOrderDetailId},
            #{detail.oid},
            #{detail.deliverOrderId}, #{detail.deliverOrderDetailId}, #{detail.warehouseTaskId},
            #{detail.warehouseTaskDetailId}, #{detail.tradeId}, #{detail.freightCode}, #{detail.freightBillNo},
            #{detail.syncType}, #{detail.syncQty}, #{detail.callStatus}, #{detail.syncStatus}, #{detail.syncMessage},
            #{detail.platformFreightId},now())
        </foreach>
    </insert>

    <update id="update">
        <foreach collection="updateInfos" separator=";" item="updateInfo">
            update pl_eshop_freight_sync_record_detail
            set sync_status=#{updateInfo.status},
            sync_time=now(),
            sync_message=#{updateInfo.message}
            <if test="updateInfo.platformFreightId!=null and updateInfo.platformFreightId!=''">
                ,platform_freight_id=#{updateInfo.platformFreightId}
            </if>

            where profile_id=#{profileId} and id=#{updateInfo.id}
        </foreach>
    </update>
    <select id="list" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecordDetail">
        select * from pl_eshop_freight_sync_record_detail
        where profile_id=#{profileId}
        and record_id in
        <foreach collection="recordIds" close=")" open="(" separator="," item="recordId">
            #{recordId}
        </foreach>
    </select>
    <select id="listByTradeIds" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecordDetail">
        select d.id, d.record_id, d.profile_id, d.sale_order_id, d.sale_order_detail_id, d.oid, d.deliver_order_id, d.deliver_order_detail_id, d.warehouse_task_id,
        d.warehouse_task_detail_id, d.trade_id, d.freight_code, d.freight_bill_no, d.sync_type, d.sync_qty, d.call_status,
        case when d.sync_status=1 then 1
        when d.sync_status=2 then 2
        when d.sync_status=3 then r.sync_status
        else 0 end as sync_status,
        d.sync_message, d.platform_freight_id, d.sync_time,d.update_time,d.create_time
        from pl_eshop_freight_sync_record_detail d
        join pl_eshop_freight_sync_record r on d.profile_id=r.profile_id and d.record_id=r.id
        where d.profile_id=#{profileId}
        and  d.trade_id in
        <foreach collection="tradeIds" close=")" open="(" separator="," item="tradeId">
            #{tradeId}
        </foreach>
        and (d.sync_status in (1,2) or (d.sync_status=3 and r.sync_status=2))
        order by d.id desc
        limit 200
    </select>



    <select id="listByRecordDetailsId"
            resultType="com.wsgjp.ct.sale.platform.entity.request.sendgoods.BatchInfo">
        select id, profile_id, trade_order_detail_id oid, record_detail_id, xcode, qty, batch_price price, batchno batchNum, expire_date validity, produce_date prodDate, product_name
        from pl_eshop_freight_sync_record_batchinfos where profile_id=#{profileId}
        and record_detail_id in
        <foreach collection="ids" close=")" open="(" separator="," item="recordDetailId">
            #{recordDetailId}
        </foreach>
    </select>

    <insert id="addBatch">
        insert into pl_eshop_freight_sync_record_batchinfos (id, profile_id, trade_order_detail_id, record_detail_id,
        xcode, qty, batch_price, batchno, expire_date, produce_date, product_name)
        values
        <foreach collection="batchInfoList" item="batchInfo" separator=",">
            (#{batchInfo.id},#{batchInfo.profileId},#{batchInfo.oid},#{batchInfo.recordDetailId},#{batchInfo.xcode},
            #{batchInfo.qty},#{batchInfo.price},#{batchInfo.batchNum},#{batchInfo.validity},#{batchInfo.prodDate},#{batchInfo.productName})
        </foreach>

    </insert>
</mapper>