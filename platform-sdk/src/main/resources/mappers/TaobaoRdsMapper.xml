<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.TaobaoRdsMapper">
    <select id="queryRdsOrdersByCreateTime"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="com.wsgjp.ct.sale.platform.dto.order.RdsOrderEntity">
        SELECT tid AS tradeId,`status`,TYPE,seller_nick AS sellerNick,jdp_response AS response,
        created,modified,jdp_created AS pushCreated,jdp_modified AS pushModified
        FROM `jdp_tb_trade`
        WHERE seller_nick= #{sellerNick}
        AND created <![CDATA[>=]]> #{beginTime}
        AND created <![CDATA[<=]]> #{endTime}
        <if test="status!=null and status!=''">
            AND status=#{status}
        </if>
        limit #{startIndex},#{pageSize}
    </select>

    <select id="countRdsOrdersByCreateTime"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `jdp_tb_trade`
        WHERE seller_nick= #{sellerNick}
        AND created <![CDATA[>=]]> #{beginTime}
        AND created <![CDATA[<=]]> #{endTime}
        <if test="status!=null and status!=''">
            AND status=#{status}
        </if>
    </select>

    <select id="queryRdsOrdersByModifyTime"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="com.wsgjp.ct.sale.platform.dto.order.RdsOrderEntity">
        SELECT tid AS tradeId,`status`,TYPE,seller_nick AS sellerNick,jdp_response AS response,
        created,modified,jdp_created AS pushCreated,jdp_modified AS pushModified
        FROM `jdp_tb_trade`
        WHERE seller_nick= #{sellerNick}
        AND jdp_modified <![CDATA[>=]]> #{beginTime}
        AND jdp_modified <![CDATA[<=]]> #{endTime}
        <if test="status!=null and status!=''">
            AND status=#{status}
        </if>
        limit #{startIndex},#{pageSize}
    </select>

    <select id="countRdsOrdersByModifyTime"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `jdp_tb_trade`
        WHERE seller_nick= #{sellerNick}
        AND jdp_modified <![CDATA[>=]]> #{beginTime}
        AND jdp_modified <![CDATA[<=]]> #{endTime}
        <if test="status!=null and status!=''">
            AND status=#{status}
        </if>
    </select>

    <select id="queryRdsOrdersByTradeIds"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderByIdsRequest"
            resultType="com.wsgjp.ct.sale.platform.dto.order.RdsOrderEntity">
        SELECT tid AS tradeId,`status`,TYPE,seller_nick AS sellerNick,jdp_response AS response,
        created,modified,jdp_created AS pushCreated,jdp_modified AS pushModified
        FROM jdp_tb_trade
        WHERE seller_nick= #{sellerNick}
        AND tid in
        <foreach collection="tradeIds" item="tradeId" index="index" open="(" close=")" separator=",">
            #{tradeId}
        </foreach>
    </select>

    <select id="queryRdsRefundsByCreateTime"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="com.wsgjp.ct.sale.platform.dto.refund.RdsRefundEntity">
        SELECT tid AS tradeId,refund_id as refundId,`status`,seller_nick AS sellerNick,jdp_response AS response,
        created,modified,jdp_created AS pushCreated,jdp_modified AS pushModified
        FROM `jdp_tb_refund`
        WHERE seller_nick= #{sellerNick}
          AND created <![CDATA[>=]]> #{beginTime}
          AND created <![CDATA[<=]]> #{endTime}
        order by created
        limit #{startIndex},#{pageSize}
    </select>

    <select id="countRdsRefundsByCreateTime"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `jdp_tb_refund`
        WHERE seller_nick= #{sellerNick}
          AND created <![CDATA[>=]]> #{beginTime}
          AND created <![CDATA[<=]]> #{endTime}
    </select>

    <select id="queryRdsRefundsByModifyTime"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="com.wsgjp.ct.sale.platform.dto.refund.RdsRefundEntity">
        SELECT tid AS tradeId,refund_id as refundId,`status`,seller_nick AS sellerNick,jdp_response AS response,
               created,modified,jdp_created AS pushCreated,jdp_modified AS pushModified
        FROM `jdp_tb_refund`
        WHERE seller_nick= #{sellerNick}
          AND jdp_modified <![CDATA[>=]]> #{beginTime}
          AND jdp_modified <![CDATA[<=]]> #{endTime}
            order by jdp_modified
        limit #{startIndex},#{pageSize}
    </select>

    <select id="countRdsRefundsByModifyTime"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `jdp_tb_refund`
        WHERE seller_nick= #{sellerNick}
          AND jdp_modified <![CDATA[>=]]> #{beginTime}
          AND jdp_modified <![CDATA[<=]]> #{endTime}
    </select>

    <select id="countRdsProducts"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `jdp_tb_item`
        WHERE nick= #{sellerNick}
            AND jdp_delete=0
        <if test="beginTime!=null  and endTime!=null ">
            AND modified &gt;= #{beginTime}
            AND modified &lt;= #{endTime}
        </if>
        <if test="status!=null and status!=''">
            AND approve_status=#{status}
        </if>
        <if test="cid!=null and cid!=''">
            AND cid=#{cid}
        </if>
    </select>

    <select id="queryRdsProducts"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.order.QueryRdsOrderRequest"
            resultType="com.wsgjp.ct.sale.platform.dto.product.RdsProductEntity">
        SELECT num_iid as platformNumId, nick as sellerNick, approve_status as status,cid as sellerCid,
        jdp_response as response,created,modified,jdp_created as pushCreated,jdp_modified as pushModified
        FROM `jdp_tb_item`
        WHERE nick = #{sellerNick}
            AND jdp_delete=0
        <if test="beginTime!=null  and endTime!=null ">
            AND modified &gt;= #{beginTime}
            AND modified &lt;= #{endTime}
        </if>
        <if test="status!=null and status!=''">
            AND approve_status=#{status}
        </if>
        <if test="cid!=null and cid!=''">
            AND cid=#{cid}
        </if>
    </select>

    <select id="queryRdsRefundByRefundIds" resultType="com.wsgjp.ct.sale.platform.dto.refund.RdsRefundEntity">
        SELECT tid AS tradeId,refund_id as refundId,`status`,seller_nick AS sellerNick,jdp_response AS response,
               created,modified,jdp_created AS pushCreated,jdp_modified AS pushModified
        FROM `jdp_tb_refund`
        WHERE seller_nick= #{sellerNick}
        and refund_id in
        <foreach collection="refundIds" item="refundId" index="index" open="(" close=")" separator=",">
            #{refundId}
        </foreach>
    </select>

    <select id="queryRdsRefundByTradeIds" resultType="com.wsgjp.ct.sale.platform.dto.refund.RdsRefundEntity">
        SELECT tid AS tradeId,refund_id as refundId,`status`,seller_nick AS sellerNick,jdp_response AS response,
        created,modified,jdp_created AS pushCreated,jdp_modified AS pushModified
        FROM `jdp_tb_refund`
        WHERE seller_nick= #{sellerNick}
        and tid in
        <foreach collection="tradeIds" item="tid" index="index" open="(" close=")" separator=",">
            #{tid}
        </foreach>
    </select>

    <select id="queryRdsProductsByNumIds"
            parameterType="com.wsgjp.ct.sale.platform.entity.request.product.QueryRdsProductByIdRequest"
            resultType="com.wsgjp.ct.sale.platform.dto.product.RdsProductEntity">
        SELECT num_iid as platformNumId, nick as sellerNick, approve_status as status,cid as sellerCid,
        jdp_response as response,created,modified,jdp_created as pushCreated,jdp_modified as pushModified
        FROM `jdp_tb_item`
        WHERE nick = #{sellerNick}
        AND jdp_delete=0
        AND num_iid in
        <foreach collection="numIds" item="numId" index="index" open="(" close=")" separator=",">
            #{numId}
        </foreach>
    </select>
</mapper>