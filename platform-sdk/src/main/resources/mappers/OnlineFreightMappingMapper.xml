<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.OnlineFreightMappingMapper">

    <select id="getFreightMappingFromUserConfig"
            resultType="com.wsgjp.ct.sale.common.entity.freight.FreightMapping">
            SELECT mp.profile_id,
            mp.`shop_type`,
            mp.`platform_freight_name` as onlineName,
            mp.`platform_freight_code` as onlineCode,
            b.fullname as localName,
            b.usercode as localCode
            FROM pl_eshop_online_freight_mapping mp
            JOIN base_btype b on b.profile_id = mp.profile_id and b.id = mp.sys_freight_id
            WHERE b.profile_id = #{profileId}
            and mp.`shop_type` = #{shopType}
            and b.deleted = 0
            and b.stoped = 0
    </select>
</mapper>