<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PubCustomFieldBaseInfoMapper">
    <select id="queryCustomFieldValues"
            resultType="com.wsgjp.ct.sale.platform.sdk.entity.PtypeCustomFiledInfo">
        select
        xtype_id as ptypeId,
        <choose>
            <when test="fieldName == 'customHead01'">custom_head01</when>
            <when test="fieldName == 'customHead02'">custom_head02</when>
            <when test="fieldName == 'customHead03'">custom_head03</when>
            <when test="fieldName == 'customHead04'">custom_head04</when>
            <when test="fieldName == 'customHead05'">custom_head05</when>
            <when test="fieldName == 'customHead06'">custom_head06</when>
            <when test="fieldName == 'customHead07'">custom_head07</when>
            <when test="fieldName == 'customHead08'">custom_head08</when>
            <when test="fieldName == 'customHead09'">custom_head09</when>
            <when test="fieldName == 'customHead10'">custom_head10</when>
            <when test="fieldName == 'customHead11'">custom_head11</when>
            <when test="fieldName == 'customHead12'">custom_head12</when>
            <when test="fieldName == 'customHead13'">custom_head13</when>
            <when test="fieldName == 'customHead14'">custom_head14</when>
            <otherwise>''</otherwise>
        </choose>
        as fieldValue
        from pub_custom_field_baseinfo
        where profile_id = #{profileId}
          and sub_type = #{subType}
        <choose>
        <when test="ptypeIds!=null and ptypeIds.size()>0">
            and xtype_id in
            <foreach collection="ptypeIds" separator="," item="ptypeId" open="(" close=")">
                #{ptypeId}
            </foreach>
        </when>
        <otherwise>
            and xtype_id = -1
        </otherwise>
        </choose>
    </select>
</mapper>