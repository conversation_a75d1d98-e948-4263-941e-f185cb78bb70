<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkAddressMapper">
    <select id="getOnlineReturnAddress"
            resultType="com.wsgjp.ct.sale.platform.entity.request.sendgoods.AddressInfo">
        select refund_address_id    as id,
               online_address_id    as platform_id,
               online_province_name as province,
               online_city_name     as city,
               online_district_name as district,
               online_full_address  as address,
               online_refund_name   as contact,
               online_refund_phone  as phone,
               online_refund_tel    as tel

        from pl_eshop_address_mapping
        where profile_id = #{profileId}
          and refund_address_id = #{id}
    </select>
    <select id="getOnlineReturnAddressList"
            resultType="com.wsgjp.ct.sale.platform.entity.request.sendgoods.AddressInfo">
        select refund_address_id    as id,
               online_address_id    as platform_id,
               online_province_name as province,
               online_city_name     as city,
               online_district_name as district,
               online_full_address  as address,
               online_refund_name   as contact,
               online_refund_phone  as phone,
               online_refund_tel    as tel

        from pl_eshop_address_mapping
        where profile_id = #{profileId}
            and eshop_id=#{eshopId}
    </select>

    <select id="getSenderById" resultType="com.wsgjp.ct.sale.platform.sdk.entity.EshopSenderInfo">
        select * from pl_sender where profile_id=#{profileId} and id=#{id}
    </select>

    <select id="getSenderByOtypeId" resultType="com.wsgjp.ct.sale.platform.sdk.entity.Sender">
        select bd.people    as senderName,
               bd.cellphone as senderMobile,
               bd.address   as senderAddress,
               bd.province  as senderProvince,
               bd.city      as senderCity,
               bd.district  as senderDistrict,
               bd.street    as senderTown
        from base_deliveryinfo bd
                 left join base_otype bo on bd.id = bo.deliver_id and bd.profile_id = bo.profile_id
        where bd.profile_id = #{profileId}
          and bo.id = #{otypeId}
            limit 1;
    </select>

    <select id="listSender" resultType="com.wsgjp.ct.sale.platform.sdk.entity.Sender">
        SELECT core.vchcode AS id,
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_province WHEN 1 THEN d3.sender_province WHEN 2 THEN
        d2.sender_province WHEN 4 THEN d1.sender_province ELSE d3.sender_province END AS 'sender_province',
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_city WHEN 1 THEN d3.sender_city WHEN 2 THEN d2.sender_city
        WHEN 4 THEN d1.sender_city ELSE d3.sender_city END AS 'sender_city',
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_district WHEN 1 THEN d3.sender_district WHEN 2 THEN
        d2.sender_district WHEN 4 THEN d1.sender_district ELSE d3.sender_district END AS 'sender_district',
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_town WHEN 1 THEN d3.sender_town WHEN 2 THEN
        d2.sender_town WHEN 4 THEN d1.sender_town ELSE d3.sender_town END AS 'sender_town',
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_address WHEN 1 THEN d3.sender_address WHEN 2 THEN
        d2.sender_address WHEN 4 THEN d1.sender_address ELSE d3.sender_address END AS 'sender_address',
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_name WHEN 1 THEN d3.sender_name WHEN 2 THEN
        d2.sender_name WHEN 4 THEN d3.sender_name ELSE d3.sender_name END AS 'sender_name',
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_phone WHEN 1 THEN d3.sender_phone WHEN 2 THEN
        d2.sender_phone WHEN 4 THEN d3.sender_phone  ELSE d3.sender_phone END AS 'sender_phone',
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_mobile WHEN 1 THEN d3.sender_mobile WHEN 2 THEN
        d2.sender_mobile WHEN 4 THEN d3.sender_mobile ELSE d3.sender_mobile END AS 'sender_mobile',
        CASE senderConfig.print_sender_source_type WHEN 0 THEN d1.sender_zip_code WHEN 1 THEN d3.sender_zip_code WHEN 2 THEN
        d2.sender_zip_code WHEN 4 THEN d1.sender_zip_code ELSE d3.sender_zip_code END AS 'senderZipCode'
        FROM
        <if test="isQueryCold==false">
            td_bill_core core
        </if>
        <if test="isQueryCold==true">
            acc_bill_core core
        </if>
        LEFT JOIN td_sender_config senderConfig ON senderConfig.profile_id=core.profile_id AND
        senderConfig.otype_id=core.otype_id
        LEFT JOIN base_ktype_deliveryinfo ktype ON ktype.profile_id = core.profile_id AND ktype.ktype_id=core.ktype_id
        and ktype.deliverytype=1 and ktype.defaulted = 1
        left join pl_sender d1 on d1.id=ktype.delivery_id
        LEFT JOIN base_btype_deliveryinfo btype ON btype.profile_id = core.profile_id AND btype.btype_id=core.btype_id
        and btype.deliverytype=1 and
        btype.defaulted=1
        left join pl_sender d2 on d2.id=btype.delivery_id
        LEFT JOIN base_otype otype ON otype.id=core.otype_id
        left join pl_sender d3 on d3.id=otype.deliver_id
        WHERE core.profile_id=#{profileId} AND core.vchcode in
        <foreach item="vchcode" collection="vchcodes" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
    </select>

    <select id="querySenderByWarehouseTaskId" resultType="com.wsgjp.ct.sale.platform.sdk.entity.Sender">
        select core.sender_id AS id,sender_province,sender_city,sender_district,sender_town,sender_address,sender_name,sender_phone,sender_mobile,sender_zip_code
        from td_bill_warehouse_task core left join pl_sender sender on core.profile_id = sender.profile_id and core.sender_id = sender.id
        where core.profile_id = #{profileId} and core.warehouse_task_id = #{warehouseTaskId} limit 1
    </select>

</mapper>