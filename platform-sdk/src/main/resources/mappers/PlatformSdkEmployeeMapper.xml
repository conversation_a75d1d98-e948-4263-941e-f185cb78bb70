<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEmployeeMapper">
    <select id="getEmployee" resultType="com.wsgjp.ct.sale.platform.sdk.entity.Employee">

        SELECT `id`,
               `profile_id`,
               `typeid`,
               `partypeid`,
               `usercode`,
               `fullname`,
               `shortname`,
               `namepy`,
               `classed`,
               `stoped`,
               `deleted`,
               `rowindex`,
               `create_time`,
               `update_time`,
               `tel`,
               `mobile`,
               `address`,
               `memo`,
               `birthday`,
               `email`,
               `login_user`,
               `dtype_id`,
               `sysid`
        FROM base_etype
        where id = #{id}
    </select>
</mapper>