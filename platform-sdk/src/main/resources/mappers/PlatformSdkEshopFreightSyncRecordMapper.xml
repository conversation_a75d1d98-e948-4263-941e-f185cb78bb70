<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopFreightSyncRecordMapper">
    <insert id="add">
        <foreach collection="addList" separator=";" item="record">
            replace into pl_eshop_freight_sync_record(id, profile_id, sale_order_id, deliver_order_id, warehouse_task_id,
            trade_id, freight_code, freight_bill_no, sync_type, sync_count, last_one, call_status, sync_status,sync_message
            <if test="record.syncTime==null">
                )
            </if>
            <if test="record.syncTime!=null">
                ,sync_time)
            </if>
            VALUES
            (#{record.id}, #{record.profileId}, #{record.saleOrderId}, #{record.deliverOrderId},
            #{record.warehouseTaskId}, #{record.tradeId}, #{record.freightCode},
            #{record.freightBillNo}, #{record.syncType}, #{record.syncCount},#{record.lastOne}, #{record.callStatus},
            #{record.syncStatus}, #{record.syncMessage}
            <if test="record.syncTime==null">
                )
            </if>
            <if test="record.syncTime!=null">
                ,#{record.syncTime})
            </if>
        </foreach>
    </insert>
    <update id="update">
        <foreach collection="updateInfos" separator=";" item="updateInfo">
            update pl_eshop_freight_sync_record
            set sync_status=#{updateInfo.status},
            sync_time=now(),
            sync_message=#{updateInfo.message}
            where profile_id=#{profileId} and id=#{updateInfo.id}
        </foreach>
    </update>
    <select id="list" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecord">
        select *
        from pl_eshop_freight_sync_record where profile_id=#{profileId}
        <if test="deliverOrderIds!=null and deliverOrderIds.size()>0">
            and deliver_order_id in
            <foreach collection="deliverOrderIds" separator="," item="deliverOrderId" open="(" close=")">
                #{deliverOrderId}
            </foreach>
        </if>
    </select>
    <select id="queryById" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecord">
        select * from pl_eshop_freight_sync_record where profile_id=#{profileId} and id={id}
    </select>
</mapper>