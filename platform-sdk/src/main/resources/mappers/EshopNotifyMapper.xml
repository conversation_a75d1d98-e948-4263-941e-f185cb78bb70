<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.EshopNotifyMapper">
    <select id="queryMessageChangeSorted" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange">
        select `id`, `profile_id`,`trade_order_id`, `eshop_id`, `content`,`type`,`create_time`,`update_time`
        from pl_eshop_notify_change
        where profile_id = #{profileId} and eshop_id = #{otypeId}
        and type=#{type}
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tradeOrderId">
                #{tradeOrderId}
            </foreach>
        </if>
        order by `create_time` desc
    </select>

    <select id="queryMessageChangeCount" resultType="java.lang.Integer">
        select count(0)
        from pl_eshop_notify_change
        where profile_id = #{profileId} and eshop_id = #{otypeId}
          and type=#{type}
          AND trade_order_id =#{tradeOrderId} limit 1
    </select>
    <select id="queryMessageChange" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange">
        select `id`, `profile_id`,`trade_order_id`, `eshop_id`, `content`,`type`,`create_time`,`update_time`,`subtype`,`retry_times`
        from pl_eshop_notify_change
        where profile_id = #{profileId} and eshop_id = #{otypeId}
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tradeOrderId">
                #{tradeOrderId}
            </foreach>
        </if>
    </select>


    <update id="updatePddEshopTokenRemark">
        update pl_eshop
        set app_secret = #{appSecret}
        WHERE profile_id = #{profileId}
          AND otype_id = #{eshopId}
    </update>
    <update id="updateTBEshopRDS">
        update pl_eshop_config
        set rds_apply_time = #{rdsApplyTime},rds_enabled = #{rdsEnabled}
        WHERE profile_id = #{profileId}
          AND eshop_id = #{eshopId}
    </update>
    <update id="updateWxVideoShopToken">
        update pl_eshop
        set token = #{token}
        <if test="tokenExpireIn!=null">
            ,token_expire_in = #{tokenExpireIn}
        </if>
        WHERE profile_id = #{profileId}
        AND otype_id = #{eshopId}
    </update>
    <update id="updateDouDianHasTokenExpire">
        update pl_eshop
        set has_token_expired = #{hasTokenExpireIn}
        WHERE profile_id = #{profileId}
        AND otype_id = #{eshopId}
    </update>

    <insert id="insertEshopNotifyChange">
        INSERT INTO `pl_eshop_notify_change` (`id`,
                                              `profile_id`,
                                              `trade_order_id`,
                                              `eshop_id`,
                                              `content`,
                                              `type`,`update_time`,`subtype`,`retry_times`)
        VALUES (#{id},
                #{profileId},
                #{tradeOrderId},
                #{eshopId},
                #{content},
                #{type},
                #{updateTime},#{subType},#{retryTimes});
    </insert>

    <insert id="insertEshopNotifyChanges">
        INSERT INTO `pl_eshop_notify_change` (`id`,
        `profile_id`,
        `trade_order_id`,
        `eshop_id`,
        `content`,
        `type`,`update_time`,`subtype`,`retry_times`)
        VALUES
        <foreach collection="notifyChanges" separator="," item="notifyChange">
            (#{notifyChange.id},
            #{notifyChange.profileId},
            #{notifyChange.tradeOrderId},
            #{notifyChange.eshopId},
            #{notifyChange.content},
            #{notifyChange.type},
            #{notifyChange.updateTime},
            #{notifyChange.subType},
            #{notifyChange.retryTimes})
        </foreach>
        ON DUPLICATE KEY UPDATE
        `content` = VALUES(content),
        `update_time`= VALUES(update_time)
    </insert>

    <insert id="insertEshopNotifyChangeBySubmit">
        INSERT INTO `pl_eshop_notify_change` (`id`,
                                              `profile_id`,
                                              `trade_order_id`,
                                              `eshop_id`,
                                              `message`,
                                              `content`,
                                              `type`,`update_time`)
        VALUES (#{id},
                #{profileId},
                #{tradeOrderId},
                #{eshopId},
                #{message},
                #{content},
                #{type},
                #{updateTime});
    </insert>
</mapper>

