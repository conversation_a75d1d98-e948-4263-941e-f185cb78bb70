<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopRefundMapper">
    <select id="getRefundByRefundId"
            resultType="com.wsgjp.ct.sale.platform.entity.entities.SimpleRefundEntity">
        SELECT trade_order_id,
               trade_refund_order_number,
               receive_state,
               create_type
        FROM pl_eshop_refund
        WHERE profile_id = #{profileId}
        and otype_id = #{otypeId}
        AND trade_refund_order_number in
        <foreach item="refundId" collection="refundIds" separator="," open="(" close=")">
            #{refundId}
        </foreach>
    </select>
</mapper>