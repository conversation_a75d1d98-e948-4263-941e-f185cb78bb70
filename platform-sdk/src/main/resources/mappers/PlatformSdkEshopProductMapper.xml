<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopProductMapper">
    <select id="listSaleProductProperties" resultType="java.lang.String">
        select platform_properties_name from pl_eshop_product_sku where profile_id = #{profileId} and eshop_id =
        #{eshopId}
        <if test="platformNumId!=null and platformNumId != ''">
            and platform_num_id = #{platformNumId}
        </if>
        <if test="platformSkuId!=null and platformSkuId != ''">
            and platform_sku_id = #{platformSkuId}
        </if>
        <if test="platformXcode!=null and platformXcode != ''">
            and platform_xcode = #{platformXcode}
        </if>
        limit 1;
    </select>
    <select id="listSaleProductSku" resultType="com.wsgjp.ct.sale.platform.factory.xiaohongshu.OrderDetail">
        select platform_properties_name as property,platform_num_id as numId,platform_xcode as xcode,platform_price as price from
        pl_eshop_product_sku where profile_id = #{profileId} and eshop_id = #{eshopId}
        <if test="platformSkuId!=null and platformSkuId != ''">
            and platform_sku_id = #{platformSkuId}
        </if>
        <if test="platformBarcode!=null and platformBarcode != ''">
            and platform_barcode = #{platformBarcode}
        </if>
        limit 1;

    </select>
    <select id="listSaleProductPrice" resultType="com.wsgjp.ct.sale.platform.factory.doudiansupermarket.entity.Product">
        select platform_price as price,platform_fullname as productName from pl_eshop_product where profile_id =
        #{profileId} and eshop_id = #{eshopId}
        <if test="platformNumId!=null and platformNumId != ''">
            and platform_num_id = #{platformNumId}
        </if>
        <if test="platformXcode!=null and platformXcode != ''">
            and platform_xcode = #{platformXcode}
        </if>
        limit 1;
    </select>
    <select id="listSaleOrderByEShopOrderId" resultType="java.lang.String">
        SELECT id
        FROM pl_eshop_sale_order
        where profile_id = #{profileId}
          and otype_id = #{eshopId}
          and trade_order_id = #{tradeOrderId}
    </select>

    <select id="queryPlatformSimpleOrderInfo" resultType="com.wsgjp.ct.sale.platform.entity.PlatformSimpleOrder">
        SELECT trade_order_id, peso.otype_id, peso.profile_id, local_trade_state, logistics_status, confirm_status
        FROM pl_eshop_sale_order peso
                 left join pl_eshop_sale_order_extend pesoe
                           on peso.profile_id = pesoe.profile_id and peso.otype_id = pesoe.otype_id
                               and peso.id = pesoe.eshop_order_id
        where peso.profile_id = #{queryOrderParam.profileId}
          and peso.otype_id = #{queryOrderParam.eshopId}
          and trade_order_id = #{queryOrderParam.tradeId}
    </select>

    <select id="listSaleOrderDetails"
            resultType="com.wsgjp.ct.sale.platform.entity.entities.PlatformOrderDetail">
        select platform_ptype_id as numId,platform_ptype_xcode as xcode,
        trade_order_detail_id as oid,platform_sku_id as skuId,
        platform_ptype_name as productName,
        platform_properties_name as propertiesName,
        trade_price as price,
        unit_qty as qty
        from pl_eshop_sale_order_detail where profile_id = #{profileId} and otype_id = #{eshopId}
        <if test="platformNumId!=null and platformNumId != ''">
            and platform_ptype_id = #{platformNumId}
        </if>
        <if test="platformXcode!=null and platformXcode != ''">
            and platform_ptype_xcode = #{platformXcode}
        </if>
        <if test="oid!=null and oid != ''">
            and trade_order_detail_id = #{oid}
        </if>
        <if test="platformSkuId!=null and platformSkuId != ''">
            and platform_sku_id = #{platformSkuId}
        </if>
        <if test="eshopOrderId!=null and eshopOrderId != ''">
            and eshop_order_id = #{eshopOrderId}
        </if>
        limit 1;
    </select>
    <select id="listSaleOrderDetailList"
            resultType="com.wsgjp.ct.sale.platform.factory.doudiansupermarket.entity.DetailByOrder">
        select platform_ptype_id as numId,platform_ptype_xcode as xcode,
        trade_order_detail_id as oid,platform_sku_id as skuId,
        platform_ptype_name as productName,
        platform_properties_name as propertiesName,
        trade_price as price,
        qty as qty
        from pl_eshop_sale_order_detail where profile_id = #{profileId} and otype_id = #{eshopId}
        <if test="platformNumId!=null and platformNumId != ''">
            and platform_ptype_id = #{platformNumId}
        </if>
        <if test="platformXcode!=null and platformXcode != ''">
            and platform_ptype_xcode = #{platformXcode}
        </if>
        <if test="oid!=null and oid != ''">
            and trade_order_detail_id = #{oid}
        </if>
        <if test="platformSkuId!=null and platformSkuId != ''">
            and platform_sku_id = #{platformSkuId}
        </if>
        <if test="eshopOrderId!=null and eshopOrderId != ''">
            and eshop_order_id = #{eshopOrderId}
        </if>
    </select>
    <select id="productSkuListByProperties"
            resultType="com.wsgjp.ct.sale.platform.factory.kuaishou.entity.ProductSku">
        select platform_properties_name as platformPropertiesName,storage_type as storageType,platform_num_id as numId,platform_sku_id  as skuId,platform_price as price,platform_xcode as xcode from pl_eshop_product_sku where profile_id = #{profileId} and eshop_id =
        #{eshopId}
        <if test="platformNumId!=null and platformNumId != ''">
            and platform_num_id = #{platformNumId}
        </if>
        <if test="platformSkuId!=null and platformSkuId != ''">
            and platform_sku_id = #{platformSkuId}
        </if>
        <if test="platformXcode!=null and platformXcode != ''">
            and platform_xcode = #{platformXcode}
        </if>
    </select>
</mapper>