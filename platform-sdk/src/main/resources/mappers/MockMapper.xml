<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.MockMapper">

    <select id="queryTradeByTradeId" resultType="java.lang.String">
        select trade_data from pl_trade_mock
        where shop_account = #{shopAccount}
        and tid = #{tradeId}
    </select>

    <select id="queryRefundDataCount" resultType="long">
        select count(1) from pl_refund_mock
        where shop_account = #{shopAccount}
        <if test="increaseEnabled">
            and refund_modifid <![CDATA[>=]]> #{begin}
            and refund_modifid <![CDATA[<=]]> #{end}
        </if>
        <if test="!increaseEnabled">
            and refund_created <![CDATA[>=]]> #{begin}
            and refund_created <![CDATA[<=]]> #{end}
        </if>
    </select>

    <select id="queryRefundDataList" resultType="java.lang.String">
        select refund_data from pl_refund_mock
        where shop_account = #{shopAccount}
        <if test="increaseEnabled">
            and refund_modifid <![CDATA[>=]]> #{begin}
            and refund_modifid <![CDATA[<=]]> #{end}
            order by refund_modifid
        </if>
        <if test="!increaseEnabled">
            and refund_created <![CDATA[>=]]> #{begin}
            and refund_created <![CDATA[<=]]> #{end}
            order by refund_created
        </if>
        limit #{startIndex},#{pageSize}
    </select>

    <select id="queryTradeCount" resultType="long">
        select count(1) from pl_trade_mock
        where shop_account = #{shopAccount}
        <choose>
            <when test="increaseEnabled">
               and trade_modified <![CDATA[>=]]> #{begin}
               and trade_modified <![CDATA[<=]]> #{end}
            </when>
            <otherwise>
                and trade_created <![CDATA[>=]]> #{begin}
                and trade_created <![CDATA[<=]]> #{end}
            </otherwise>
        </choose>
        <if test="stateList!=null and stateList.size()>0">
            and trade_state in
            <foreach collection="stateList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryTradeList" resultType="java.lang.String">
        select trade_data from pl_trade_mock
        where shop_account = #{shopAccount}
        <if test="stateList!=null and stateList.size()>0">
            and trade_state in
            <foreach collection="stateList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="increaseEnabled">
                and trade_modified <![CDATA[>=]]> #{begin}
                and trade_modified <![CDATA[<=]]> #{end}
                order by trade_modified
            </when>
            <otherwise>
                and trade_created <![CDATA[>=]]> #{begin}
                and trade_created <![CDATA[<=]]> #{end}
                order by trade_created
            </otherwise>
        </choose>
        limit #{startIndex},#{pageSize}
    </select>

    <select id="queryRefundData" resultType="java.lang.String">
      select refund_data from pl_refund_mock
        where shop_account = #{shopAccount}
        and refund_id= #{refundId}
    </select>
</mapper>