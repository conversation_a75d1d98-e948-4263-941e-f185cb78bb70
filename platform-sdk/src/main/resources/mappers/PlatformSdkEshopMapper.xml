<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopMapper">
    <select id="getEshopInfoByShopId" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.platform.sdk.entity.EshopInfo">
        select
        eshop.*,config.rds_enabled,config.rds_apply_time,config.mapping_type,config.ag_enabled,config.platform_eshop_sn_type,config.muti_select_appkey
        as mutiSelectAppkey,mall_type as mallType,
        config.rds_check_time,
        config.rds_ready_time,
        config.rds_name,
        config.platfrom_config,
        config.is_sku_memo_desired,
        config.download_order_type * 1 AS 'download_order_type',
        config.tmc_enabled,
        config.reissiue_sync_freight
        from pl_eshop eshop
        left join pl_eshop_config config on config.profile_id=eshop.profile_id and config.eshop_id=eshop.otype_id
        <where>
            eshop.profile_id=#{profileId} and eshop.otype_id =#{shopId}
        </where>
    </select>

    <update id="updateEshopToken">
        update pl_eshop
        <trim prefix="set" suffixOverrides=",">
            app_key = #{appKey},
            app_secret = #{appSecret},
            token= #{token},
            refresh_token = #{refreshToken},
            <if test="expiresIn!=null">
                token_expire_in = #{expiresIn},
            </if>
            <if test="isAuth!=null">
                is_auth = #{isAuth},
                expire_notice=0,
            </if>
            <if test="reExpiresIn!=null">
                refresh_token_expire_in = #{reExpiresIn},
            </if>
            <if test="r1ExpireIn!=null">
                token_r1expire_in = #{r1ExpireIn},
            </if>
            <if test="onlineShopId!=null">
                online_eshop_id = #{onlineShopId},
            </if>
        </trim>
        WHERE profile_id= #{profileId} AND otype_id = #{eshopId}
    </update>

    <update id="updateEshopNoticeById">
        UPDATE pl_eshop
        SET expire_notice = #{expireNotice}
        where profile_id = #{profileId}
          and otype_id = #{shopId}
    </update>

    <update id="updateEshopConfigRdsState">
        update pl_eshop_config
            set rds_enabled = 1,
                <if test="rdsApplyTime==null">
                    rds_apply_time = now(),
                </if>
                rds_check_time = now(),
                rds_ready_time = DATE_ADD(now(), INTERVAL 4 DAY)
        where profile_id=#{profileId}
          and eshop_id=#{eShopId}
    </update>
</mapper>