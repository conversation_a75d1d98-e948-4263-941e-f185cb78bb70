<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopBaseInfoMapper">
    <select id="getEtypeList" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.platform.sdk.entity.Etype">
        select *
        from base_etype
        where profile_id = #{profileId}
          and stoped = 0
          and deleted = 0
    </select>

    <select id="batchQueryComboDetail" resultType="com.wsgjp.ct.sale.platform.sdk.entity.ComboDetail">
        select id,
        combo_id,
        profile_id,
        ptype_id,
        sku_id,
        unit_id,
        qty,
        price,
        total,
        necessary_sku,
        stoped,
        gifted,
        main_ptype,
        create_time,
        update_time
        from base_ptype_combo_detail
        where profile_id = #{profileId}
        <if test="comboIds!=null and comboIds.size()>0">
            and combo_id in
            <foreach collection="comboIds" close=")" open="(" separator="," item="comboId">
                #{comboId}
            </foreach>
        </if>
    </select>
</mapper>