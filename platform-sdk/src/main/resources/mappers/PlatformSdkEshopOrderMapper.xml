<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopOrderMapper">
    <select id="listSaleOrders"
            resultType="com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderSimpleEntity">
        select
        pl_eshop_sale_order.id,
        pl_eshop_sale_order.profile_id,
        pl_eshop_sale_order.otype_id,
        pl_eshop_sale_order.create_type,
        trade_order_id as trade_id,
        platform_store_id as store_id,
        platform_store_code as store_code,
        local_trade_state as trade_status,
        trade_pay_time as pay_time,
        platform_stock_id,
        platform_stock_code,
        platform_json,
        pl_buyer.customer_receiver_province,
        pl_buyer.customer_receiver_city,
        pl_buyer.customer_receiver_district,
        pl_buyer.customer_receiver_town
        from pl_eshop_sale_order
        left join pl_eshop_sale_order_extend on pl_eshop_sale_order.profile_id=pl_eshop_sale_order_extend.profile_id and
        pl_eshop_sale_order.id = pl_eshop_sale_order_extend.eshop_order_id
        left join pl_buyer on pl_eshop_sale_order.profile_id=pl_buyer.profile_id and pl_eshop_sale_order.buyer_id =
        pl_buyer.buyer_id
        where pl_eshop_sale_order.profile_id=#{profileId}
        and pl_eshop_sale_order.otype_id=#{eshopId}
        and trade_order_id in
        <foreach item="tradeId" collection="tradeIds" separator="," open="(" close=")">
            #{tradeId}
        </foreach>
    </select>

    <select id="listSaleOrderDetails"
            resultType="com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderDetailSimpleEntity">
        select
        id,
        profile_id,
        otype_id,
        ptype_id,
        platform_ptype_id,
        platform_sku_id,
        platform_ptype_xcode,
        platform_ptype_name,
        platform_properties_name,
        unit_qty,
        unit_qty as qty,
        combo_row_id,
        '0' as combo_id,
        eshop_order_id as order_id,
        false as combo,
        trade_order_detail_id as oid,
        process_state,
        platform_detail_trade_state as tradeStatus,
        local_refund_state as refundStatus
        from pl_eshop_sale_order_detail
        where profile_id=#{profileId}
        and eshop_order_id in
        <foreach item="orderId" collection="saleOrderIds" separator="," open="(" close=")">
            #{orderId}
        </foreach>
        union
        select
        eshop_order_detail_combo_row_id as id,
        profile_id,
        0 as ptype_id,
        0 as otype_id,
        '' as platform_ptype_id,
        '' as platform_sku_id,
        '' as platform_ptype_xcode,
        '' as platform_ptype_name,
        '' as platform_properties_name,
        qty as unit_qty,
        qty,
        0 as combo_row_id,
        combo_id,
        eshop_order_id as order_id,
        true as combo,
        trade_order_detail_id as oid,
        0 as process_state,
        0 as tradeStatus,
        0 as refundStatus
        from pl_eshop_sale_order_detail_combo
        where profile_id=#{profileId}
        and eshop_order_id in
        <foreach item="orderId" collection="saleOrderIds" separator="," open="(" close=")">
            #{orderId}
        </foreach>
    </select>
    <select id="listEshopOrderMark" resultType="com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopOrderSimpleMarkEntity">
        select
        pl_eshop_sale_order.trade_order_id as trade_id,
        pl_eshop_order_mark.mark_code,
        pl_eshop_order_mark.bubble,
        platform_stock_id as stock_id,
        mark_data.big_data
        from pl_eshop_sale_order
        left join pl_eshop_order_mark on pl_eshop_sale_order.profile_id = pl_eshop_order_mark.profile_id and
        pl_eshop_sale_order.id = pl_eshop_order_mark.order_id
        left join mark_data on pl_eshop_order_mark.mark_data_id = mark_data.id
        where pl_eshop_sale_order.profile_id=#{profileId}
        and pl_eshop_sale_order.otype_id=#{otypeId}
        and pl_eshop_order_mark.mark_code is not null
        and pl_eshop_sale_order.trade_order_id in
        <foreach item="tradeId" collection="tradeIds" separator="," open="(" close=")">
            #{tradeId}
        </foreach>
    </select>

    <select id="listEshopRefundMark" resultType="com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopRefundSimpleMarkEntity">
        select
        pl_eshop_refund.trade_refund_order_number as refund_id,
        pl_eshop_order_mark.mark_code,
        mark_data.big_data
        from pl_eshop_refund
        left join pl_eshop_order_mark on pl_eshop_refund.profile_id = pl_eshop_order_mark.profile_id and
        pl_eshop_refund.id = pl_eshop_order_mark.order_id
        left join mark_data on pl_eshop_order_mark.mark_data_id = mark_data.id
        where pl_eshop_refund.profile_id=#{profileId}
        and pl_eshop_refund.otype_id=#{otypeId}
        and pl_eshop_order_mark.mark_code is not null
        and pl_eshop_refund.trade_refund_order_number in
        <foreach item="refundId" collection="refundIds" separator="," open="(" close=")">
            #{refundId}
        </foreach>
    </select>


    <update id="doUpdateSaleOrderSendStatus">
        <foreach collection="list" item="od">
            update pl_eshop_sale_order
            set local_freight_code=#{od.freightCode},
            local_freight_name = #{od.freightName},
            local_freight_bill_no = #{od.freightBillNo},
            local_trade_state = 3
            where profile_id = #{od.profileId}
            and otype_id = #{od.eshopId}
            and trade_order_id = #{od.tradeId};
            update pl_eshop_sale_order_detail d
            left join pl_eshop_sale_order s on d.profile_id=s.profile_id and d.otype_id=s.otype_id and d.eshop_order_id = s.id
            set d.platform_detail_trade_state = 3
            where d.profile_id = #{od.profileId}
            and d.otype_id = #{od.eshopId}
            and s.trade_order_id = #{od.tradeId};
        </foreach>
    </update>


    <select id="listAdvanceOrderVchcodes"
            resultType="com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopAdvanceSaleOrderSimpleEntity">
        select
        vchcode
        from td_orderbill_platform
        where td_orderbill_platform.profile_id=#{profileId}
        and trade_id in
        <foreach item="tradeId" collection="tradeIds" separator="," open="(" close=")">
            #{tradeId}
        </foreach>
    </select>

    <update id="modifyOrderStatus">
        update pl_eshop_sale_order
        set local_trade_state = #{state}
        where profile_id = #{profileId}
        and otype_id = #{otypeId}
        and trade_order_id = #{tradeId}
    </update>

    <select id="queryOrderNotSendDetailCount" resultType="int">
        select count(1) from pl_eshop_sale_order_detail
        where profile_id=#{profileId}
        and otype_id=#{otypeId}
        and eshop_order_id=#{id}
        and platform_detail_trade_state!=3
    </select>

    <update id="modifyDetailStatus">
        update pl_eshop_sale_order_detail
        set platform_detail_trade_state = 3
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
          and eshop_order_id=#{orderId}
          and trade_order_detail_id = #{oid}
    </update>

    <select id="listAdvanceOrderDetails"
            resultType="com.wsgjp.ct.sale.platform.sdk.entity.eshop.EshopSaleOrderDetailSimpleEntity">
        select d.trade_order_detail_id oid,o.trade_id,o.vchcode as orderId,d.detail_id as id,d.process_state
        from td_orderbill_platform o
        left join td_orderbill_detail_platform d on d.profile_id=o.profile_id and d.vchcode=o.vchcode
        where o.profile_id=#{profileId}
        and o.eshop_id=#{eshopId}
        and o.trade_id in
        <foreach item="tradeId" collection="tradeIds" separator="," open="(" close=")">
            #{tradeId}
        </foreach>
    </select>

    <select id="queryDetailSkuInfo"
            resultType="com.wsgjp.ct.sale.platform.entity.refundEntity.OrderDetailOnlineSkuInfo">
        select pesod.profile_id,
        pesod.otype_id,
        peso.trade_order_id as tradeId,
        pesod.eshop_order_id as orderId,
        pesod.id as detailId,
        pesod.platform_sku_id,
        pesod.platform_ptype_id as platformNumId,
        pesod.platform_ptype_xcode as platformXcode,
        pesod.platform_properties_name as platformProperties,
        pesod.trade_order_detail_id as oid,
        pesod.platform_ptype_name as platformPtypeName,
        pesod.trade_price as price,
        pesod.qty as qty,
        peso.local_trade_state as trade_status
        from pl_eshop_sale_order_detail pesod
        left join pl_eshop_sale_order peso on pesod.profile_id=peso.profile_id and pesod.eshop_order_id=peso.id
        where pesod.profile_id=#{profileId}
        and pesod.otype_id=#{eshopId}
        AND peso.trade_order_id IN
        <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
            #{tid}
        </foreach>
    </select>
    <select id="querySaleOrderDetailFreightList"
            resultType="com.wsgjp.ct.sale.platform.dto.order.entity.EshopOrderDetailFreight">
        select id, trade_order_id, profile_id, otype_id, trade_order_detail_id, freight_name,
        freight_bill_no, extend_info, create_type, send_qty, sync_state, sync_type, has_last,
        hash_key, freight_code, create_time, update_time
        from pl_eshop_order_detail_freight
        where profile_id=#{profileId}
        and otype_id= #{otypeId}
        and trade_order_id IN
        <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
            #{tid}
        </foreach>
    </select>
</mapper>