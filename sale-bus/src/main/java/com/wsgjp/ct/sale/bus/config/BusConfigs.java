package com.wsgjp.ct.sale.bus.config;

import com.wsgjp.ct.sale.bus.entity.TaskState;
import com.wsgjp.ct.sale.bus.entity.TaskType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "sale-bus")
public class BusConfigs {
    /**
     * 每次自动采集的数量
     */
    private int pageSize = 2000;
    /**
     * 加锁超过 20 分钟的自动释放
     */
    private int lockerExpireMinute = 20;

    /**
     * 期望事件已超过 20 分钟的，直接打捞执行
     */
    private int expirationMinutes = 20;
    private int splitDeliverBillMinutes = 1;
    /**
     * 是否释放期望超时 时间
     */
    private boolean releaseBusExpirationMinutesLock = true;
//    /**
//     * 过期需要执行的任务类型
//     */
//    @Value("CalcDeliverTiming,InvoiceUploading,SendedStockPostBill,CalcSyncTiming,DeliverableInventoryPoint,RepairManager")
//    private List<TaskType> expirationTypeScope = null;
    /**
     * 需要立即执行的任务
     */
    private List<TaskType> executeAtPresent = null;
    private boolean closeDelay = false;
    private List<TaskState> stateScope = Arrays.asList(TaskState.None,TaskState.Error);

    private boolean closeAllErrorTask = false;

    private int closeErrorTaskAfterTimeHour = 24;

    public int getCloseErrorTaskAfterTimeHour() {
        return closeErrorTaskAfterTimeHour;
    }

    public void setCloseErrorTaskAfterTimeHour(int closeErrorTaskAfterTimeHour) {
        this.closeErrorTaskAfterTimeHour = closeErrorTaskAfterTimeHour;
    }

    public boolean isCloseAllErrorTask() {
        return closeAllErrorTask;
    }

    public void setCloseAllErrorTask(boolean closeAllErrorTask) {
        this.closeAllErrorTask = closeAllErrorTask;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<TaskState> getStateScope() {
        return stateScope;
    }

    public void setStateScope(List<TaskState> stateScope) {
        this.stateScope = stateScope;
    }

//    public List<TaskType> getExpirationTypeScope() {
//        return expirationTypeScope;
//    }
//
//    public void setExpirationTypeScope(List<TaskType> expirationTypeScope) {
//        this.expirationTypeScope = expirationTypeScope;
//    }

    public int getExpirationMinutes() {
        return expirationMinutes;
    }

    public void setExpirationMinutes(int expirationMinutes) {
        this.expirationMinutes = expirationMinutes;
    }


    public int getLockerExpireMinute() {
        return lockerExpireMinute;
    }

    public void setLockerExpireMinute(int lockerExpireMinute) {
        this.lockerExpireMinute = lockerExpireMinute;
    }

    public List<TaskType> getExecuteAtPresent() {
        return executeAtPresent;
    }

    public void setExecuteAtPresent(List<TaskType> executeAtPresent) {
        this.executeAtPresent = executeAtPresent;
    }

    public boolean isCloseDelay() {
        return closeDelay;
    }

    public void setCloseDelay(boolean closeDelay) {
        this.closeDelay = closeDelay;
    }

    public boolean isReleaseBusExpirationMinutesLock() {
        return releaseBusExpirationMinutesLock;
    }

    public void setReleaseBusExpirationMinutesLock(boolean releaseBusExpirationMinutesLock) {
        this.releaseBusExpirationMinutesLock = releaseBusExpirationMinutesLock;
    }

    public int getSplitDeliverBillMinutes() {
        return splitDeliverBillMinutes;
    }

    public void setSplitDeliverBillMinutes(int splitDeliverBillMinutes) {
        this.splitDeliverBillMinutes = splitDeliverBillMinutes;
    }
}
