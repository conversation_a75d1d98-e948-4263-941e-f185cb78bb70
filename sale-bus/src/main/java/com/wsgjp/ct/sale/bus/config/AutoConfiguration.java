package com.wsgjp.ct.sale.bus.config;


import com.wsgjp.ct.sale.bus.actuator.BusStarterImpl;
import com.wsgjp.ct.sale.bus.actuator.plugs.ForwardActuatorImpl;
import com.wsgjp.ct.sale.bus.datas.BusDataCenterImpl;
import com.wsgjp.ct.sale.bus.utils.BusDataLockerImpl;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Import;

@Import({BusDataCenterImpl.class,
        AppConfiguration.class,
        BusConfigs.class,
        BusStarterImpl.class,
        ForwardActuatorImpl.class,
        BusDataLockerImpl.class})
@MapperScan("com.wsgjp.ct.sale.bus.mapper")
public class AutoConfiguration {
}
