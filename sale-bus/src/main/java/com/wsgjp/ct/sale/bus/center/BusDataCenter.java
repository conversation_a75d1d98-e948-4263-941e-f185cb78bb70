package com.wsgjp.ct.sale.bus.center;

import com.wsgjp.ct.sale.bus.entity.*;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

public interface BusDataCenter {
    /**
     * 批量拉取消息
     *
     * @param size      查询分页大小
     * @param taskState 任务状态
     * @param taskTypes 任务类型
     * @return 任务数据
     */
    List<TaskData> pull(int size, List<TaskState> taskState, List<TaskType> taskTypes, Date expectedTime, Date minExpectedTime);
    List<TaskData> pullNewMethod(int size, List<TaskState> taskState, List<TaskType> taskTypes, Date expectedTime, Date minExpectedTime);
    /**
     * 批量推送消息
     *
     * @param data 任务列表
     */
    List<Task> sendBusMq(List<Task> data, BigInteger profileId);
    void insertBusData(List<Task> taskList);

    List<TaskType> getExpirationTaskType();

    void reInsertBusData(List<Task> taskList);

    /**
     * 处理任务执行结果
     */
    void ack(List<TaskResult> result,String source);

//    /**
//     * 判断该任务类型是否允许执行
//     */
//    boolean isOpenTraining(TaskType taskType);

    /**
     * 是否立即执行
     * @param taskType
     * @return
     */
    boolean isAtPresent(TaskType taskType);

    void closeAllErrorTasks();

    void insertBigBusContentBatch(BigInteger profileId, List<TextAssembly> textAssemblies);

    List<TextAssembly> getBigBusContents(BigInteger profileId, BigInteger taskId);
    void insertBusBatch(List<BusData> busDatas);
}
