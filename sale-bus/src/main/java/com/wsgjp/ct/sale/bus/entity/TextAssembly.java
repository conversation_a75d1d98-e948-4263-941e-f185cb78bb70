package com.wsgjp.ct.sale.bus.entity;

import java.io.Serializable;
import java.math.BigInteger;

public class TextAssembly implements Serializable {
    private BigInteger id;
    private BigInteger profileId;
    private String bigData;
    private BigInteger primaryId;
    private int dataIndex;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getBigData() {
        return bigData;
    }

    public void setBigData(String bigData) {
        this.bigData = bigData;
    }

    public BigInteger getPrimaryId() {
        return primaryId;
    }

    public void setPrimaryId(BigInteger primaryId) {
        this.primaryId = primaryId;
    }

    public int getDataIndex() {
        return dataIndex;
    }

    public void setDataIndex(int dataIndex) {
        this.dataIndex = dataIndex;
    }
}
