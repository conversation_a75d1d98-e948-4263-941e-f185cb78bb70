package com.wsgjp.ct.sale.bus.actuator.plugs;

import com.wsgjp.ct.sale.bus.center.BusActuator;
import com.wsgjp.ct.sale.bus.entity.*;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.loadbalancer.support.SupportLoadBalancer;
import ngp.starter.web.base.GeneralException;
import ngp.starter.web.base.GeneralResult;
import ngp.starter.web.base.ResultCode;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ForwardActuatorImpl implements BusActuator {
    private SupportLoadBalancer supportLoadBalancer;
    private Logger logger = LoggerFactory.getLogger(ForwardActuatorImpl.class);

    public ForwardActuatorImpl(SupportLoadBalancer supportLoadBalancer) {
        this.supportLoadBalancer = supportLoadBalancer;
    }

    @Override
    public List<TaskResult> execute(List<TaskData> taskData) {
        // 读取到报文信息
        if (taskData == null || taskData.size() == 0) {
            return null;
        }
        List<TaskResult> results = new ArrayList<>();
        for (TaskData taskDatum : taskData) {
            String busContent = taskDatum.getBusContent();
            ForwardMessage forwardMessage = JsonUtils.toObject(busContent, ForwardMessage.class);
            try{
                String result = post(forwardMessage);
                results.add(new TaskResult(result,TaskState.Over,taskDatum));
            }catch (Exception ex) {
                results.add(new TaskResult(ex.getMessage(), TaskState.Error, taskDatum));
            }
        }
        return results;
    }
    public String post(ForwardMessage message) {
        String url = message.getRedirectUrl();

        try {
            String toolName = getServerName(message.getRedirectUrl());
            GeneralResult response = supportLoadBalancer.post(toolName, url, message.getBody(), GeneralResult.class);
            if (null == response) {
                return "";
            }
            long code = response.getCode();
            if (ResultCode.SUCCESS.getCode() != code) {
                throw new GeneralException("访问服务返回状态错误" + response.getMessage());
            } else {
                return JsonUtils.toJson(response);
            }
        } catch (Exception e) {
            // 调用远程服务出现了异常信息
            logger.error(String.format("[sale-bus]执行信息转发出错%s", CurrentUser.getProfileId()), e);
            throw e;
        }
    }

    private String getServerName(String redirectUrl) {
        if (redirectUrl == null) {
            throw new GeneralException("转发使用的URL是错误的");
        }
        String[] split = redirectUrl.split("/");
        return split[0];
    }


    @Override
    public TaskType taskType() {
        return TaskType.Forward;
    }
}
