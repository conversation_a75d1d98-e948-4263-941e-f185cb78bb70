package com.wsgjp.ct.sale.bus.entity;

import bf.datasource.typehandler.CodeEnum;

public enum TaskState implements CodeEnum {
    None(0,"未执行"),
    Error(3,"执行失败"),
    Over(10,"执行成功"),
    Unconsumed(11,"没有消费"),
    FORCE_OVER(12, "强制结束")
    ;

    private int code;
    private String title;

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    TaskState(int code, String title) {
        this.code = code;
        this.title = title;
    }
}
