package com.wsgjp.ct.sale.bus.actuator;

import com.wsgjp.ct.sale.bus.center.BusActuator;
import com.wsgjp.ct.sale.bus.center.BusDataCenter;
import com.wsgjp.ct.sale.bus.center.BusStarter;
import com.wsgjp.ct.sale.bus.config.BusConfigs;
import com.wsgjp.ct.sale.bus.entity.*;
import com.wsgjp.ct.sale.bus.utils.BusDataLockerImpl;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.dao.entity.SysDataEntity;
import com.wsgjp.ct.support.dao.mapper.SysDataMapper;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class BusStarterImpl implements BusStarter {
    Logger logger = LoggerFactory.getLogger(BusStarterImpl.class);
    private BusDataCenter busDataCenter;
    private List<BusActuator> busActuatorList;
    private BusConfigs configs;

    private List<TaskType> taskTypes;
    SysDataMapper sysDataMapper;
    private BusDataLockerImpl busDataLocker;

    private String lockerKey = "sale-bus.lock";

    public BusStarterImpl(BusDataCenter busDataCenter,
                          List<BusActuator> busActuatorList,
                          BusConfigs configs,
                          BusDataLockerImpl busDataLocker,
                          SysDataMapper sysDataMapper) {
        this.busDataCenter = busDataCenter;
        this.busActuatorList = busActuatorList;
        this.configs = configs;
        this.taskTypes = busActuatorList.stream().map(a -> a.taskType()).collect(Collectors.toList());
        this.sysDataMapper = sysDataMapper;
        this.busDataLocker = busDataLocker;

    }

    public LockParams getBusTrainingLocker(BigInteger profileId){
        String key = String.format("sale-bus:%s", profileId);
        LockParams lockParams = new LockParams(key, configs.getExpirationMinutes(), String.valueOf(new Date()));
        lockParams.setRelease(configs.isReleaseBusExpirationMinutesLock());
        return lockParams;
    }
    public LockParams getBusBusinessLocker(BigInteger profileId){
        String key = String.format("sale-bus-business:%s", profileId);
        return new LockParams(key,configs.getExpirationMinutes(),String.valueOf(new Date()));
    }

    public LockParams getSplitDeliverBillLocker(BigInteger profileId){
        String key = String.format("split-deliver-bill:%s", profileId);
        return new LockParams(key,configs.getSplitDeliverBillMinutes(),String.valueOf(new Date()));
    }

    private void setSysLocker(String value) {
        SysDataEntity locker = new SysDataEntity();
        locker.setProfileId(CurrentUser.getProfileId());
        locker.setId(UId.newId());
        locker.setSubName(this.lockerKey);
        locker.setSubValue(value);
        // 设置锁
        sysDataMapper.saveDataIgnoreCheckProfileState(locker);
    }

    @Override
    public boolean invokeTraining(TrainingParams trainingParams) {
        //获取当前允许消费的任务类型列表
        List<TaskType> types = trainingParams.getTaskTypes();
        logger.debug("[sale-bus]可消费的任务类型{}:{}", CurrentUser.getProfileId(), types);
        if (types.isEmpty()) {
            logger.debug("[sale-bus]没有配置退出任务{}", CurrentUser.getProfileId());
            return false;
        }
        //拉取任务数据
        Date minExpectedTime = DateUtils.addDays(new Date(), -trainingParams.getMinExpirationDay());
        Date expectedTime = DateUtils.addMinutes(new Date(), -trainingParams.getExpirationMinutes());
        List<TaskData> taskData = busDataCenter.pullNewMethod(trainingParams.getPageSize(), trainingParams.getStateScope(), types, expectedTime,minExpectedTime);
        logger.debug("[sale-bus]加载到数据总量{}:{}", CurrentUser.getProfileId(), taskData.size());
        // 没有数据退出
        if (taskData.isEmpty()) {
            return true;
        }
        execute(taskData);
        return true;
    }

    @Override
    public void closeAllErrorTasks() {
        busDataCenter.closeAllErrorTasks();
    }


    @Override
    public TrainingParams defaultTrainingConfigs() {
        TrainingParams trainingParams = new TrainingParams(this.configs);
        trainingParams.setTaskTypes(busDataCenter.getExpirationTaskType());
        trainingParams.setPageSize(configs.getPageSize());
        trainingParams.setExpirationMinutes(configs.getExpirationMinutes());
        trainingParams.setStateScope(configs.getStateScope());
//        List<TaskType> taskTypes = filterTraining(trainingParams.getTaskTypes());
//        trainingParams.setTaskTypes(taskTypes);
        return trainingParams;
    }

    @Override
    public boolean splitAndAddBigBusContent(BigInteger profileId, BigInteger taskId, String json) {
        if (StringUtils.isEmpty(json)) {
            return true;
        }
        List<String> strlist = splitStr(json, 10000);
        List<TextAssembly> textAssemblies = new ArrayList<>(strlist.size());
        for (int i = 0; i < strlist.size(); i++) {
            TextAssembly textAssembly = new TextAssembly();
            textAssembly.setId(UId.newId());
            textAssembly.setBigData(strlist.get(i));
            textAssembly.setPrimaryId(taskId);
            textAssembly.setDataIndex(i);
            textAssembly.setProfileId(profileId);
            textAssemblies.add(textAssembly);
        }
        busDataCenter.insertBigBusContentBatch(profileId, textAssemblies);
        return true;
    }

    public List<String> splitStr(String strs, int length) {
        List<String> finalStrs  = new ArrayList<>(5);
        if (strs.length() <= length) {
            finalStrs.add(strs);
        } else {
            for (int i = 0; i < strs.length(); i += length) {
                int endIndex = Math.min(i + length, strs.length());
                String part = strs.substring(i, endIndex);
                finalStrs.add(part);
            }
        }
        return finalStrs;
    }

    private void compareResultsAndInitNull(List<TaskResult> errors, List<TaskData> taskDataList, List<TaskResult> execute) {
        if (execute.size() != taskDataList.size()) {
            // 数据结果有差异，找出丢失的数据用户更新
            compareResults(errors, taskDataList, execute);
        }
    }

    private void compareResults(List<TaskResult> errors, List<TaskData> taskDataList, List<TaskResult> execute) {
        if (execute == null || taskDataList == null || errors == null) {
            return;
        }
        for (TaskData taskData : taskDataList) {
            if (has(taskData, execute)) {
                continue;
            }
            buildUnconsumedMsg(errors, taskData,"存在一部分任务未消费");
        }
    }

    private static boolean has(TaskData taskData, List<TaskResult> a) {
        // 将空数据过滤掉
        if (taskData == null) {
            return true;
        }
        boolean has = false;
        for (TaskResult taskResult : a) {
            if (taskResult == null || taskResult.getTaskData() == null || taskResult.getTaskData().getId() == null) {
                continue;
            }
            if (taskResult.getTaskData().getId().equals(taskData.getId())) {
                has = true;
                break;
            }
        }
        return has;
    }

    private void buildErrorMsg(List<TaskResult> errors, List<TaskData> taskDataList, String msg) {
        for (TaskData taskDatum : taskDataList) {
            buildErrorMsg(errors, taskDatum, msg);
        }
    }

    private static void buildErrorMsg(List<TaskResult> errors, TaskData taskDatum, String msg) {
        errors.add(new TaskResult(String.format("消费任务失败，错误信息：%s", msg), TaskState.Error, taskDatum));
    }

    private static void buildUnconsumedMsg(List<TaskResult> errors, TaskData taskDatum, String msg) {
        errors.add(new TaskResult(String.format("任务没有消费，msg：%s", msg), TaskState.Unconsumed, taskDatum));
    }

    /**
     * 通过配置项目过滤需要轮训消费的任务列表
     *
     * @param taskTypes 所有的任务类型
     * @return 当前允许消费的任务类型
     */
//    private List<TaskType> filterTraining(List<TaskType> taskTypes) {
//        List<TaskType> types = new ArrayList<>();
//        for (TaskType taskType : taskTypes) {
//            if (busDataCenter.isOpenTraining(taskType)) {
//                types.add(taskType);
//            }
//        }
//        return types;
//    }


    private void execute(List<TaskData> taskData) {
        //根据任务类型分类
        Map<TaskType, List<TaskData>> taskDataListMap = taskData.stream().collect(Collectors.groupingBy(TaskData::getTaskType));

        List<TaskResult> errors = new ArrayList<>();
        List<TaskResult> success = new ArrayList<>();
        for (TaskType taskType : taskDataListMap.keySet()) {
            //根据任务类型获取执行器
            BusActuator busActuator = this.getActuator(taskType);
            if (busActuator == null) {
                continue;
            }
            //获取任务数据列表
            List<TaskData> taskDataList = taskDataListMap.get(taskType);
            if (CollectionUtils.isEmpty(taskDataList)) {
                continue;
            }
            try {
                //消费任务
                List<TaskResult> execute = busActuator.execute(taskDataList);
                if (execute == null || execute.isEmpty()) {
                    logger.debug("[sale-bus]消费任务完成，单次执行总量{}，返回结果为空", taskDataList.size());
                    buildErrorMsg(errors, taskDataList, "没有返回消费结果，识别为消费失败");
                    continue;
                }
                // 具体要放到那个列表里面其实不重要
                logger.debug("[sale-bus]消费任务完成，单次执行总量{},返回结果总量{}", taskDataList.size(), execute.size());
                compareResultsAndInitNull(errors, taskDataList, execute);
                success.addAll(execute);
            } catch (Exception e) {
                logger.error(String.format("[sale-bus]消费任务失败%s，错误信息：%s", CurrentUser.getProfileId(), e.getMessage()), e);
                buildErrorMsg(errors, taskDataList, e.getMessage());
            }
        }
        try {
            //处理任务的消费结果
            busDataCenter.ack(success,"expiration");
            busDataCenter.ack(errors,"expiration");
        } catch (Exception e) {
            logger.error(String.format("[sale-bus]处理任务的消费结果异常%s，错误信息：%s", CurrentUser.getProfileId(), e.getMessage()), e);
        }
    }
    public List<TaskResult> execute(TaskData taskData) {
        return execute(taskData,"bus-mq");
    }
    public List<TaskResult> execute(TaskData taskData,String source) {
        BusActuator actuator = this.getActuator(taskData.getTaskType());
        try {
            List<TaskData> tasks = Arrays.asList(taskData);
            List<TaskResult> execute = actuator.execute(tasks);
            compareResultsAndInitNull(execute, tasks, execute);
            //处理任务的消费结果
            busDataCenter.ack(execute, source);
            return execute;
        } catch (Exception e) {
            logger.error(String.format("[sale-bus]处理任务的消费结果异常%s，错误信息：%s", CurrentUser.getProfileId(), e.getMessage()), e);
            List<TaskResult> taskResults = new ArrayList<>();
            buildErrorMsg(taskResults, taskData, e.getMessage());
            busDataCenter.ack(taskResults,source);
        }
        return new ArrayList<>();
    }

    public BusActuator getActuator(TaskType taskType) {
        for (BusActuator busActuator : busActuatorList) {
            if (busActuator.taskType().equals(taskType)) {
                return busActuator;
            }
        }
        return null;
    }

    @Override
    public void sendBusOrExecute(List<Task> taskData,BigInteger profileId) {
        List<Task> toMq = new ArrayList<>();
        List<Task> atPresent = new ArrayList<>();
        for (Task datum : taskData) {
            // 通过类型配置是否执行 mq
            if (busDataCenter.isAtPresent(datum.getTaskType())) {
                atPresent.add(datum);
            } else {
                toMq.add(datum);
            }
        }
        if (CollectionUtils.isNotEmpty(toMq)) {
            busDataCenter.sendBusMq(toMq,profileId);
        }
        if (CollectionUtils.isNotEmpty(atPresent)) {
            busDataCenter.insertBusData(atPresent);
            // 假装写如到 bus-data 表中，执行完成有个记录
            for (Task task : atPresent) {
                execute(new TaskData(task));
            }
        }
    }

}
