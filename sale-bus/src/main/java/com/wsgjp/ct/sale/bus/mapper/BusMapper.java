package com.wsgjp.ct.sale.bus.mapper;

import com.wsgjp.ct.sale.bus.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Mapper
public interface BusMapper {
    void push(@Param("profileId")BigInteger profileId,@Param("taskDataList")List<Task> taskDataList);
    void pushTasks(@Param("taskDataList")List<Task> taskDataList);
    void update(@Param("profileId") BigInteger profileId,@Param("busTaskInfos") List<BusMqMapping> busTaskInfos);
    void ack(@Param("profileId")BigInteger profileId, @Param("ackData")List<AckData> ackData);
    List<TaskData> pull(@Param("profileId") BigInteger profileId,
                        @Param("limit") int limit,
                        @Param("taskStates")List<TaskState> taskStates,
                        @Param("taskTypes") List<TaskType> taskTypes,@Param("expectedTime") Date expectedTime,@Param("minExpectedTime") Date minExpectedTime);
    List<TaskData> pullSimple(@Param("profileId") BigInteger profileId,
                        @Param("limit") int limit,
                        @Param("taskStates")List<TaskState> taskStates,
                        @Param("taskTypes") List<TaskType> taskTypes,@Param("expectedTime") Date expectedTime,@Param("minExpectedTime") Date minExpectedTime);
    TaskData select(@Param("profileId") BigInteger profileId,@Param("id") BigInteger id);

    void closeAllErrorTasks(@Param("profileId") BigInteger profileId,@Param("msg") String msg,@Param("afterTimeHour") int closeErrorTaskAfterTimeHour);

    void insertBigBusContentBatch(@Param("textAssemblies") List<TextAssembly> textAssemblies);

    List<TextAssembly> getBigBusContents(@Param("profileId") BigInteger profileId, @Param("taskId") BigInteger taskId);

    void insertBusData(@Param("busDatas") List<BusData> busDatas);
}
