package com.wsgjp.ct.sale.bus.entity;

import bf.datasource.typehandler.CodeEnum;

public enum TaskType implements CodeEnum {
    None(0,"配置：没有开启,直接执行","总线未开启",DbManager.sendDb,ExpirationProcessType.giveUp),
    @Deprecated
    Forward(1,"消息转发", "sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    @Deprecated
    BillUpdate(2,"订单更新", "sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    CalcDeliverTiming(3,"发货超时计算", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    InvoiceUploading(4,"上传发票", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    @Deprecated
    FeedbackPlatform(5,"回告平台", "sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    @Deprecated
    RefundNoticeDeliver(6,"售后通知交易单", "sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    @Deprecated
    RefundNoticeSaleOrder(7,"售后通知原始订单", "sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    InvoiceSubmit(8,"提交发票源", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    SendedStockPostBill(15,"库存已全部出入库(进销存核算发消息给我)", "sale-bus",DbManager.resendDb,ExpirationProcessType.needCatch),

    @Deprecated
    ExecuteTaskAfterAudit(16, "执行审核后置流程", "sale-bus-business",DbManager.sendDb,ExpirationProcessType.giveUp),

    @Deprecated
    CompensateAfterAudit(17, "执行审核后置流程（补偿机制）", "sale-bus-business",DbManager.sendDb,ExpirationProcessType.giveUp),
    DeliverableInventoryPoint(18, "可发货库存占用变动(正/负 边界)", "sale-bus",DbManager.resendDb,ExpirationProcessType.needCatch),

    @Deprecated
    PlanSendDeliveryTime(19, "预计发货时间变动", "sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    @Deprecated
    RepairManager(20,"修复程序","sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    FreightCancel(21,"取消物流单号","sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),

    CalcSyncTiming(22,"同步单号超时计算", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    Finance_Calc(23,"销售提成计算", "sale-bus-bi",DbManager.sendDb,ExpirationProcessType.giveUp),
    @Deprecated
    MarkPartialSend(24,"标记分批发货", "sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    SplitDeliverBill(25,"拆分交易单","sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    SyncBill(26,"同步单号","sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    CalcBillData(28, "汇总计算交易单记录", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    WmsSendMessage(31, "WMS推送消息", "sale-bus",DbManager.resendDb,ExpirationProcessType.needCatch),
    DataProbe(29, "数据探查", "sale-bus",DbManager.sendDb,ExpirationProcessType.giveUp),
    NotifyWms(33, "通知wms (不需要走bus mq)", "",DbManager.sendDb,ExpirationProcessType.giveUp),
    QueryDeliverDetailInfo(34, "查询交易单对应的明细信息", "sale-bus-order",DbManager.sendDb,ExpirationProcessType.needCatch),
    MergeCheck(32, "合并检查", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    resetAuditBills(36, "重算已审核单据信息", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    AfterSuccessSend(37, "重算延迟发货徽标", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    autoCheckin(100, "自动生单入库", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    asyncAfterSendOrRejectEshopOrder(5710, "发货后通知原单处理售后的换补状态", "sale-bus",DbManager.sendDb,ExpirationProcessType.needCatch),
    ;

    private int code;
    private String name;
    private String mqTopic;
    private DbManager dbManager;
    private ExpirationProcessType expirationProcessType;

    TaskType(int code, String name, String mqTopic,DbManager dbManager,ExpirationProcessType expirationProcessType) {
        this.code = code;
        this.name = name;
        this.mqTopic = mqTopic;
        this.dbManager = dbManager;
        this.expirationProcessType=expirationProcessType;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public String getMqTopic() {
        return mqTopic;
    }

    public void setMqTopic(String mqTopic) {
        this.mqTopic = mqTopic;
    }

    public DbManager getDbManager() {
        return dbManager;
    }

    public ExpirationProcessType getExpirationProcessType() {
        return expirationProcessType;
    }

    public void setExpirationProcessType(ExpirationProcessType expirationProcessType) {
        this.expirationProcessType = expirationProcessType;
    }
}
