package com.wsgjp.ct.sale.bus.mapper;


import com.wsgjp.ct.support.dao.entity.SysDataEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.List;

@Mapper
public interface BusSysDataMapper {
    List<SysDataEntity> list(@Param("profileId") BigInteger profileId, @Param("subName") String subName);
    List<SysDataEntity> listByKeys(@Param("profileId") BigInteger profileId, @Param("subNames") List<String> subNames);

    int updateById(@Param("profileId") BigInteger profileId, @Param("data") SysDataEntity data, @Param("oldId") BigInteger oldId);

    int insert(@Param("profileId") BigInteger profileId, @Param("data") SysDataEntity data);

    void setValue(@Param("profileId") BigInteger profileId, @Param("subName") String subName, @Param("subValue") String subValue);
}
