package com.wsgjp.ct.sale.bus.utils;


import bf.datasource.wings.delegates.FuncOne;
import com.wsgjp.ct.sale.bus.entity.LockParams;
import com.wsgjp.ct.sale.bus.entity.LockerData;
import com.wsgjp.ct.sale.bus.mapper.LockerDataMapper;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Component
public class BusDataLockerImpl {
    Logger logger = LoggerFactory.getLogger(BusDataLockerImpl.class);

    LockerDataMapper lockerDataMapper;

    public BusDataLockerImpl(LockerDataMapper lockerDataMapper) {
        this.lockerDataMapper = lockerDataMapper;
    }

    public boolean lock(LockParams lockParams) {
        return locker(lockParams.getLockKey(), String.valueOf(new Date().getTime()), lockParams::checkTime);
    }
    public boolean release(LockParams lockParams) {
        if (!lockParams.isRelease()) {
            return false;
        }
        return release(lockParams.getLockKey(), lockParams.getDefaultValue());
    }

    public boolean locker(String key, String value, FuncOne<String> check) {
        logger.info("正在执行加锁检查:{},{},{}",key,value,CurrentUser.getProfileId());
        try {
            List<LockerData> sysDataEntities = lockerDataMapper.list(CurrentUser.getProfileId(), key);
            LockerData sysDataEntity = null;
            if (!CollectionUtils.isEmpty(sysDataEntities)) {
                sysDataEntity = sysDataEntities.get(0);
            }
            if (sysDataEntity == null || check.func(sysDataEntity.getLockHead())) {
                if (sysDataEntity == null) {
                    LockerData insertData = new LockerData();
                    insertData.setId(UId.newId());
                    insertData.setProfileId(CurrentUser.getProfileId());
                    insertData.setDescription("locker");
                    insertData.setLockKey(key);
                    insertData.setLockHead(value);
                    lockerDataMapper.insert(CurrentUser.getProfileId(), insertData);
                    return true;
                } else {
                    BigInteger oldId = sysDataEntity.getId();
                    sysDataEntity.setLockHead(value);
                    sysDataEntity.setId(UId.newId());
                    int update = lockerDataMapper.updateById(CurrentUser.getProfileId(), sysDataEntity, oldId);
                    if (update == 1) {
                        return true;
                    }
                }
            }
        } catch (Throwable e) {
            logger.error("正在执行加锁检查异常:{},{},{}",key,value,CurrentUser.getProfileId(),e);
        }
        logger.info("正在执行加锁检查-失败:{},{},{}",key,value,CurrentUser.getProfileId());
        return false;
    }

    public boolean release(String key, String value) {
        try{
            lockerDataMapper.setValue(CurrentUser.getProfileId(),key,value);
            return true;
        }catch (Exception e) {
            return false;
        }
    }
}
