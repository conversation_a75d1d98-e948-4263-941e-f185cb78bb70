package com.wsgjp.ct.sale.bus.entity;

import java.math.BigInteger;

public class LockerData {

    private BigInteger id;
    private BigInteger profileId;
    private String lockKey;
    private String lockHead;
    private String description;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getLockKey() {
        return lockKey;
    }

    public void setLockKey(String lockKey) {
        this.lockKey = lockKey;
    }

    public String getLockHead() {
        return lockHead;
    }

    public void setLockHead(String lockHead) {
        this.lockHead = lockHead;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
