package com.wsgjp.ct.sale.bus.entity;

import java.util.Date;

public class LockParams {
    private String lockKey;
    private int expireMinute;
    private String defaultValue  = "0";
    private boolean release = true;

    public LockParams(String lockKey, int expireMinute, String defaultValue) {
        this.lockKey = lockKey;
        this.expireMinute = expireMinute;
        this.defaultValue = defaultValue;
    }

    public String getLockKey() {
        return lockKey;
    }

    public void setLockKey(String lockKey) {
        this.lockKey = lockKey;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public boolean checkTime(String value) {
        try {
            if (null == value) {
                return true;
            }
            long subValue = Long.parseLong(value);
            // 超过expireMinute分钟 标识锁失效
            long now = new Date().getTime();
            if ((now - subValue) / 1000 / 60 > expireMinute) {
                return true;
            }
            return false;
        } catch (Exception exception) {
            return true;
        }
    }

    public boolean isRelease() {
        return release;
    }

    public void setRelease(boolean release) {
        this.release = release;
    }
}
