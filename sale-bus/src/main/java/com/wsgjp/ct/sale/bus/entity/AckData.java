package com.wsgjp.ct.sale.bus.entity;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AckData {
    private List<BigInteger> ids;
    private TaskState taskState;
    private String busResult;
    private String userApp;
    private Date executeTime = new Date();

    public AckData(List<BigInteger> ids, TaskState taskState, String busResult,String userApp) {
        this.setIds(ids);
        this.setTaskState(taskState);
        this.setBusResult(busResult);
        this.setUserApp(userApp);
    }

    public List<BigInteger> getIds() {
        return ids;
    }

    public void setIds(List<BigInteger> ids) {
        if (ids == null) {
            this.ids = new ArrayList<>();
        } else {
            this.ids = ids;
        }
    }

    public TaskState getTaskState() {
        return taskState;
    }

    public void setTaskState(TaskState taskState) {
        this.taskState = taskState;
    }

    public String getBusResult() {
        return busResult;
    }

    public void setBusResult(String busResult) {
        if (busResult == null) {
            this.busResult = "";
        } else {
            this.busResult = busResult;
        }
    }

    public String getUserApp() {
        return userApp;
    }

    public void setUserApp(String userApp) {
        if (userApp != null && userApp.length() > 110) {
            this.userApp = userApp.substring(0,100);
        } else {
            this.userApp = userApp;
        }
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }
}
