package com.wsgjp.ct.sale.bus.entity;

import com.wsgjp.ct.sale.bus.config.BusConfigs;

import java.util.List;

public class TrainingParams {
    private int pageSize;
    private List<TaskState> stateScope;
    private int expirationMinutes;
    private int minExpirationDay = 3;
    private List<TaskType> taskTypes;
    public TrainingParams(BusConfigs configs) {
        this(configs.getPageSize(),configs.getStateScope(),configs.getExpirationMinutes());
    }

    public TrainingParams(int pageSize, List<TaskState> stateScope, int expirationMinutes) {
        this.pageSize = pageSize;
        this.stateScope = stateScope;
        this.expirationMinutes = expirationMinutes;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<TaskState> getStateScope() {
        return stateScope;
    }

    public void setStateScope(List<TaskState> stateScope) {
        this.stateScope = stateScope;
    }

    public int getExpirationMinutes() {
        return expirationMinutes;
    }

    public void setExpirationMinutes(int expirationMinutes) {
        this.expirationMinutes = expirationMinutes;
    }

    public List<TaskType> getTaskTypes() {
        return taskTypes;
    }

    public void setTaskTypes(List<TaskType> taskTypes) {
        this.taskTypes = taskTypes;
    }

    public int getMinExpirationDay() {
        return minExpirationDay;
    }

    public void setMinExpirationDay(int minExpirationDay) {
        this.minExpirationDay = minExpirationDay;
    }
}
