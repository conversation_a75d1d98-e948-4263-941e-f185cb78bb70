package com.wsgjp.ct.sale.bus.mapper;

import com.wsgjp.ct.sale.bus.entity.LockerData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.List;

@Mapper
public interface LockerDataMapper {
    List<LockerData> list(@Param("profileId") BigInteger profileId, @Param("lockKey") String lockKey);

    int updateById(@Param("profileId") BigInteger profileId, @Param("data") LockerData data, @Param("oldId") BigInteger oldId);

    int insert(@Param("profileId") BigInteger profileId, @Param("data") LockerData data);

    void setValue(@Param("profileId") BigInteger profileId, @Param("lockKey") String lockKey, @Param("lockHead") String lockHead);
}
