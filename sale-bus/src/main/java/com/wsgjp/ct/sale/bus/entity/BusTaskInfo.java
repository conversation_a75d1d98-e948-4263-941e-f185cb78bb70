package com.wsgjp.ct.sale.bus.entity;

import java.math.BigInteger;

public class BusTaskInfo {
    private BigInteger profileId;
    private String busMessage;
    /**
     * 缓存字段用于监控推送后回写消息使用
     */
    private BigInteger busId;
    private String accountingMessage;
    private String wmsMessage;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getBusMessage() {
        return busMessage;
    }

    public void setBusMessage(String busMessage) {
        this.busMessage = busMessage;
    }

    public String getAccountingMessage() {
        return accountingMessage;
    }

    public void setAccountingMessage(String accountingMessage) {
        this.accountingMessage = accountingMessage;
    }

    public BigInteger getBusId() {
        return busId;
    }

    public void setBusId(BigInteger busId) {
        this.busId = busId;
    }

    public String getWmsMessage() {
        return wmsMessage;
    }

    public void setWmsMessage(String wmsMessage) {
        this.wmsMessage = wmsMessage;
    }
}
