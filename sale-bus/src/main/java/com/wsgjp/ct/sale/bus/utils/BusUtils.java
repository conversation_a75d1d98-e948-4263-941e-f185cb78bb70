package com.wsgjp.ct.sale.bus.utils;

import com.wsgjp.ct.sale.bus.entity.MapActive;
import com.wsgjp.ct.sale.bus.entity.TaskData;
import ngp.utils.JsonUtils;
import rx.functions.Func2;

import java.util.ArrayList;
import java.util.List;

public class BusUtils {

    public  static  <D> List<MapActive<D>> toDataByJson(List<TaskData> taskData, Class<D> classz) {
        return toData(taskData,classz,JsonUtils::toObject);
    }
    public  static  <D> List<MapActive<D>> toData(List<TaskData> taskData, Class<D> classz, Func2<String,Class<D>,D> func) {
        List<MapActive<D>> data = new ArrayList<>();
        for (TaskData taskDatum : taskData) {
            String busContent = taskDatum.getBusContent();
            D o = func.call(busContent, classz);
            data.add(new MapActive<D>(o, taskDatum));
        }
        return data;
    }
}
