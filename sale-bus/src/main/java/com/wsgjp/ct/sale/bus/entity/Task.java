package com.wsgjp.ct.sale.bus.entity;

import java.math.BigInteger;
import java.util.Date;

public class Task {
    /**
     * 任务类型
     */
    private TaskType taskType;
    /**
     * 任务ID
     */
    private BigInteger id;
    /**
     * 任务的期望完成时间
     */
    private Date expectedTime;
    /**
     * MD5值-身份标识
     */
    private String identityLabel;
    /**
     * 任务内容
     */
    private String busContent;
    /**
     * 程序版本信息
     */
    private String sourceApp;
    private BigInteger profileId;
    private String MqId;

    public Task() {
    }
    public Task(Task task) {
        this.id = task.id;
        this.expectedTime = task.expectedTime;
        this.identityLabel = task.identityLabel;
        this.taskType = task.taskType;
        this.busContent = task.busContent;
        this.sourceApp = task.sourceApp;
        this.profileId=task.profileId;
    }

    public Task(String identityLabel, TaskType taskType, Date expectedTime, String busContent) {
        this.identityLabel = identityLabel;
        this.taskType = taskType;
        this.expectedTime = expectedTime;
        this.busContent = busContent;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public Date getExpectedTime() {
        return expectedTime;
    }

    public void setExpectedTime(Date expectedTime) {
        this.expectedTime = expectedTime;
    }

    public String getIdentityLabel() {
        return identityLabel;
    }

    public void setIdentityLabel(String identityLabel) {
        this.identityLabel = identityLabel;
    }

    public TaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(TaskType taskType) {
        this.taskType = taskType;
    }

    public String getBusContent() {
        return busContent;
    }

    public void setBusContent(String busContent) {
        this.busContent = busContent;
    }

    public String getSourceApp() {
        return sourceApp;
    }

    public void setSourceApp(String sourceApp) {
        if (sourceApp != null && sourceApp.length() > 110) {
            this.sourceApp = sourceApp.substring(0,100);
        } else{
            this.sourceApp = sourceApp;
        }
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getMqId() {
        return MqId;
    }

    public void setMqId(String mqId) {
        MqId = mqId;
    }
}
