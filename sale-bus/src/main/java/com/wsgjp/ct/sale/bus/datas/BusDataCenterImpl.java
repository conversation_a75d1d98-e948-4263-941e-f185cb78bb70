package com.wsgjp.ct.sale.bus.datas;

import com.wsgjp.ct.sale.bus.center.BusDataCenter;
import com.wsgjp.ct.sale.bus.config.AppConfiguration;
import com.wsgjp.ct.sale.bus.config.BusConfigs;
import com.wsgjp.ct.sale.bus.entity.*;
import com.wsgjp.ct.sale.bus.mapper.BusMapper;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.mq.SysMqSend;
import ngp.idgenerator.UId;
import ngp.mq.MqSendResult;
import ngp.mq.SysMq;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;


@Component
public class BusDataCenterImpl implements BusDataCenter {

    private BusMapper busMapper;
    private BusConfigs configs;
    private AppConfiguration appConfiguration;
    private SysMq sysMq;
    private Logger logger = LoggerFactory.getLogger(BusDataCenterImpl.class);

    public BusDataCenterImpl(BusConfigs configs,
                             BusMapper busMapper,
                             AppConfiguration appConfiguration,
                             SysMq sysMq) {
        this.configs = configs;
        this.busMapper = busMapper;
        this.appConfiguration = appConfiguration;
        this.sysMq = sysMq;
    }

    @Override
    public List<TaskData> pull(int size, List<TaskState> taskState, List<TaskType> taskTypes,Date expectedTime,Date minExpectedTime) {
        return busMapper.pull(CurrentUser.getProfileId(), size, taskState, taskTypes, expectedTime, minExpectedTime);
    }
    @Override
    public List<TaskData> pullNewMethod(int size, List<TaskState> taskState, List<TaskType> taskTypes,Date expectedTime,Date minExpectedTime) {
        List<TaskData> result=new ArrayList<>();
        List<TaskData> unMergeResult= busMapper.pullSimple(CurrentUser.getProfileId(), size, taskState, taskTypes, expectedTime, minExpectedTime);
        if(CollectionUtils.isEmpty(unMergeResult)){
            return result;
        }
        Map<TaskType,Map<String,List<TaskData>>> groups=unMergeResult.stream().collect(Collectors.groupingBy(Task::getTaskType,Collectors.groupingBy(Task::getIdentityLabel)));
        if(CollectionUtils.isEmpty(groups)){
            return result;
        }
        Comparator<TaskData> comparator=new Comparator<TaskData>() {
            @Override
            public int compare(TaskData date1, TaskData date2) {
                if(null==date1.getExpectedTime()&&null==date2.getExpectedTime()){
                    return 0;
                }
                if(null==date1.getExpectedTime()){
                    return -1;
                }
                if(null==date2.getExpectedTime()){
                    return 1;
                }
                return date1.getExpectedTime().compareTo(date2.getExpectedTime());
            }
        };
        groups.forEach((taskType,taskTypeGroups)->{
            if(CollectionUtils.isEmpty(taskTypeGroups)){
                return;
            }
            taskTypeGroups.forEach((identityLabel,taskGroupItem)->{
                if(CollectionUtils.isEmpty(taskGroupItem)){
                    return;
                }
                taskGroupItem.sort(comparator);
                TaskData item=taskGroupItem.get(taskGroupItem.size()-1);
                List<BigInteger> idList=taskGroupItem.stream().map(TaskData::getId).collect(Collectors.toList());
                item.setIds(idList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                result.add(item);
            });
        });
        if(CollectionUtils.isEmpty(result)){
            return result;
        }
        result.sort(comparator);
        if(result.size()<=size){
            return result;
        }else{
            return result.subList(0,size);
        }

    }
    public List<MqSendResult<BusTaskInfo>> sendMq(List<Task> taskList, BigInteger profileId) {
        List<MqSendResult<BusTaskInfo>> results = new ArrayList<>();
        Map<String, List<Task>> taskListByTopic = taskList.stream().collect(Collectors.groupingBy(t -> t.getTaskType().getMqTopic()));
        for (Map.Entry<String, List<Task>> taskEntry : taskListByTopic.entrySet()) {
            try {
                List<BusTaskInfo> buses = new ArrayList<>();
                Map<Long, List<BusTaskInfo>> delayBuses = new HashMap<>();
                long now = new Date().getTime() + 100;
                for (Task task : taskEntry.getValue()) {
                    logger.debug("[{}]profileId【{}】向MQ发生消息：{}", taskEntry.getKey(), profileId, task.getBusContent());
                    BusTaskInfo bus = createBusMs(task,profileId);
                    buses.add(bus);
//                    splitBusMs(buses, delayBuses, now, task, bus);
                }
                List<MqSendResult<BusTaskInfo>> mqRes = sendNormalMq(buses, taskEntry.getKey());
//                List<MqSendResult<BusTaskInfo>> mqResDelay = sendDelayMq(delayBuses, taskEntry.getKey());
                results.addAll(mqRes);
//                results.addAll(mqResDelay);
            } catch (Exception ex) {
                logger.error("向MQ发送消息失败", ex);
            }
        }
        return results;
    }

    private List<MqSendResult<BusTaskInfo>> sendDelayMq(Map<Long, List<BusTaskInfo>> delayBuses, String topicName) {
        List<MqSendResult<BusTaskInfo>> results = new ArrayList<>();
        for (Long time : delayBuses.keySet()) {
            if (configs.isCloseDelay()) {
                for (BusTaskInfo busTaskInfo : delayBuses.get(time)) {
                    MqSendResult<BusTaskInfo> objectMqSendResult = new MqSendResult<>();
                    objectMqSendResult.setMessage(busTaskInfo);
                    objectMqSendResult.setMessageId("未开启延迟消息提交MQ");
                    objectMqSendResult.setErrorMsg("未开启延迟消息提交MQ");
                    objectMqSendResult.setSuccess(true);
                    results.add(objectMqSendResult);
                }
                continue;
            }
            try {
                List<MqSendResult<BusTaskInfo>> res = sysMq.sendDelayTime(delayBuses.get(time), topicName, time);
                logger.info(String.format("DelayMQ-发送结果：%s", JsonUtils.toJson(res)));
                results.addAll(res);
            } catch (Exception exception) {
                logger.error("[{}]消息发送失败", topicName, exception);
            }
        }
        return results;
    }

    private List<MqSendResult<BusTaskInfo>> sendNormalMq(List<BusTaskInfo> buses, String topicName) {
        try {
            List<MqSendResult<BusTaskInfo>> res = SysMqSend.send(buses, topicName);
            logger.info(String.format("NormalMQ-发送结果：%s",JsonUtils.toJson(res)));
            return res;
        } catch (Exception exception) {
            logger.error("[{}]消息发送失败", topicName, exception);
        }
        return null;
    }

    private static void splitBusMs(List<BusTaskInfo> buses, Map<Long, List<BusTaskInfo>> delayBuses, long now, Task task, BusTaskInfo bus) {
        long delayTime = task.getExpectedTime().getTime();
        long timeLimit = (delayTime - now) / 1000;

        if (timeLimit <= 0) {
            buses.add(bus);
        } else {
            if (delayBuses.containsKey(delayTime)) {
                delayBuses.get(delayTime).add(bus);
            } else {
                ArrayList arrayList = new ArrayList<>();
                arrayList.add(bus);
                delayBuses.put(delayTime, new ArrayList<>(arrayList));
            }
        }
    }
    private static boolean isDelay(long now, Task task) {
        long delayTime = task.getExpectedTime().getTime();
        long timeLimit = (delayTime - now) / 1000;

        if (timeLimit <= 0) {
            return false;
        } else {
            return true;
        }
    }
    @NotNull
    private static BusTaskInfo createBusMs(Task task, BigInteger profileId) {
        BusTaskInfo bus = new BusTaskInfo();
        bus.setProfileId(profileId);
        bus.setBusMessage(JsonUtils.toJson(task));
        bus.setBusId(task.getId());
        return bus;
    }

    @Override
    public List<Task> sendBusMq(List<Task> taskList, BigInteger profileId) {
        if (CollectionUtils.isEmpty(taskList)) {
            return new ArrayList<>();
        }
        List<Task> normalTaskList=new ArrayList<>();
        List<Task> delayTaskList=new ArrayList<>();
        filterTaskType(taskList, delayTaskList, normalTaskList);
        try {
            insertBusData(taskList);
        } catch (Exception e) {
            logger.error(String.format("[sale-bus]写入总线失败，错误信息：%s", e.getMessage()), e);
            throw e;
        } finally {
            if(CollectionUtils.isNotEmpty(normalTaskList)){
                logger.info(String.format("[sale-bus]推送MQ报文：%s", normalTaskList));
                List<MqSendResult<BusTaskInfo>> results = sendMq(normalTaskList, profileId);
                // 将消息ID更新到任务中
//            updateMqIdToBus(profileId,results,taskList);
            }
        }
        return taskList;
    }

    private void filterTaskType(List<Task> taskList, List<Task> delayTaskList, List<Task> normalTaskList) {
        Map<String, List<Task>> taskListByTopic = taskList.stream().collect(Collectors.groupingBy(t -> t.getTaskType().getMqTopic()));
        List<TaskType> expirationTaskTypes=getExpirationTaskType();
        String delayUnwatchErrorMessage="该延时bus任务不会被打捞，请联系开发处理!";
        for (Map.Entry<String, List<Task>> taskEntry : taskListByTopic.entrySet()) {
            try {
                long now = new Date().getTime() + 100;
                for (Task task : taskEntry.getValue()) {
                    repair(task);
                    if(isDelay(now,task)){
                        Date expectedTime = DateUtils.addMinutes(task.getExpectedTime(), -this.configs.getExpirationMinutes());
                        task.setExpectedTime(expectedTime);
                        if(CollectionUtils.isEmpty(expirationTaskTypes)||!expirationTaskTypes.contains(task.getTaskType())){
                            throw new RuntimeException(String.format("%s task:%s",delayUnwatchErrorMessage,JsonUtils.toJson(task)));
                        }
                        delayTaskList.add(task);
                    }else {
                        normalTaskList.add(task);
                    }
                }
            } catch (Exception ex) {
                logger.error("区分正常延时失败", ex);
                if(StringUtils.isNotEmpty(ex.getMessage())&&ex.getMessage().contains(delayUnwatchErrorMessage)){
                    throw ex;
                }
            }
        }
    }
    @Override
    public List<TaskType> getExpirationTaskType(){
        List<TaskType> taskTypes=new ArrayList<>();
        for(TaskType taskType:TaskType.values()){
            if(ExpirationProcessType.needCatch.equals(taskType.getExpirationProcessType())){
                taskTypes.add(taskType);
            }
        }
        return taskTypes;
    }
    private void updateMqIdToBus(BigInteger profileId,List<MqSendResult<BusTaskInfo>> results,List<Task> taskList) {
        try {
            List<BusMqMapping> busMqMappings = new ArrayList<>();
            for (MqSendResult<BusTaskInfo> result : results) {
                if (result.getMessage().getBusId() == null) {
                    logger.error("没有BusId",result);
                    continue;
                }
                Optional<Task> any = taskList.stream().filter(a -> a.getId().equals(result.getMessage().getBusId()) && a.getTaskType().getDbManager().equals(DbManager.sendDb)).findAny();
                if (!any.isPresent()){
                    continue;
                }
                busMqMappings.add(new BusMqMapping(result.getMessage().getBusId(),result.getMessageId()));
            }
            if (busMqMappings.isEmpty()) {
                return;
            }
            busMapper.update(profileId,busMqMappings);
        } catch (Exception exception) {
            logger.error("回填总线MQId出错", exception);
        }
    }

    @Override
    public void reInsertBusData(List<Task> taskList) {
        insertBusData(taskList,DbManager.resendDb);
    }
    @Override
    public void insertBusData(List<Task> taskList) {
        insertBusData(taskList,DbManager.sendDb);
    }
    public void insertBusData(List<Task> taskList,DbManager dbManager) {
        List<Task> tasks = new ArrayList<>();
        for (Task datum : taskList) {
            repair(datum);
            if (!dbManager.equals(datum.getTaskType().getDbManager())) {
                continue;
            }
            tasks.add(datum);
            //设置一些默认值；判断传值，如果有一个有误，直接报错，该批次推送的任务都无法写库
        }
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        if (tasks.isEmpty()) {
            return;
        }
        busMapper.pushTasks(tasks);
    }

    private void repair(Task datum) {
        if (null == datum.getId()) {
            datum.setId(UId.newId());
        }
        // 初始化期望完成时间
        if (null == datum.getExpectedTime()) {
            datum.setExpectedTime(new Date());
        }
        if (null == datum.getIdentityLabel() || "".equals(datum.getIdentityLabel())) {
            throw new RuntimeException("请输入正常的身份标识");
        }
        if(Objects.isNull(datum.getIdentityLabel())){
            datum.setIdentityLabel(String.format("%s:%s", datum.getTaskType().toString(), datum.getIdentityLabel()));
        }
        if (null == datum.getTaskType()) {
            throw new RuntimeException("注册的任务类型是错误的");
        }
        if (null == datum.getSourceApp()) {
            datum.setSourceApp(createMachine("i"));
            if (!StringUtils.isEmpty(datum.getMqId())) {
                datum.setSourceApp(String.format("%s:%s",datum.getSourceApp(),datum.getMqId()));
            }
        }
        if (null == datum.getBusContent()) {
            datum.setBusContent("");
        }
        if (null==datum.getProfileId())
        {
            datum.setProfileId(CurrentUser.getProfileId());
        }
        if(!StringUtils.isEmpty(datum.getBusContent()) && datum.getBusContent().length() > 65530) {
            logger.error("不讲道理,太长了，虽然你执行不了也要给你阶段：{}",datum.getBusContent());
            datum.setBusContent(datum.getBusContent().substring(0,60000));
        }
    }

    private String createMachine(String type) {
        return String.format("%s:%s", type, appConfiguration.getApplication());
    }

    @Override
    public void ack(List<TaskResult> result,String source) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<AckData> ackData = new ArrayList<>();
        int count = 0;
        for (TaskResult taskResult : result) {
            //这里的ids，表示更新同一任务，因为有可能同一任务有多条数据，那么统一更新
            String idsTemp = taskResult.getTaskData().getIds();
            List<BigInteger> ids = new ArrayList<>();
            if (!StringUtils.isNotEmpty(idsTemp)) {
                ids.add(taskResult.getTaskData().getId());
            } else {
                String[] split = idsTemp.split(",");
                for (String s : split) {
                    if (s == null || s.isEmpty()) {
                        continue;
                    }
                    ids.add(new BigInteger(s));
                }
                count += ids.size();
            }
            ackData.add(new AckData(ids, taskResult.getTaskState(), taskResult.getResult(), createMachine(source)));
        }
        // 这里后期考虑分页 处理，避免数据库条数过多
        try {
            busMapper.ack(CurrentUser.getProfileId(), ackData);
        } catch (Exception e) {
            logger.error("[sale-bus]保存消费结果失败：{}",JsonUtils.toJson(result), e);
            throw e;
        }
    }

//    @Override
//    public boolean isOpenTraining(TaskType taskType) {
//        //开启的任务类型
//        if (!CollectionUtils.isNotEmpty(configs.getExpirationTypeScope())) {
//            return false;
//        }
//        //没有配置关闭，也没有配置开启，则默认开启
//        return this.configs.getExpirationTypeScope().contains(taskType);
//    }

    @Override
    public boolean isAtPresent(TaskType taskType) {
        if (!CollectionUtils.isNotEmpty(configs.getExecuteAtPresent())) {
            return false;
        }
        if (configs.getExecuteAtPresent().contains(TaskType.None)) {
            return true;
        }
        return this.configs.getExecuteAtPresent().contains(taskType);
    }

    @Override
    public void closeAllErrorTasks() {
        if (configs.isCloseAllErrorTask()) {
            logger.info("开始执行关闭所有超过预期执行时间过多的任务");
            busMapper.closeAllErrorTasks(CurrentUser.getProfileId(), "自动关闭超时未执行任务", configs.getCloseErrorTaskAfterTimeHour());
        } else {
            logger.info("没有开启关闭所有超过预期执行时间过多的任务");
        }
    }

    @Override
    public void insertBigBusContentBatch(BigInteger profileId, List<TextAssembly> textAssemblies) {
        busMapper.insertBigBusContentBatch(textAssemblies);
    }

    @Override
    public List<TextAssembly> getBigBusContents(BigInteger profileId, BigInteger taskId) {
        return busMapper.getBigBusContents(profileId, taskId);
    }

    @Override
    public void insertBusBatch(List<BusData> busDatas) {
        if (CollectionUtils.isEmpty(busDatas)) {
            return;
        }
        busMapper.insertBusData(busDatas);
    }
}
