package com.wsgjp.ct.sale.bus.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AppConfiguration {

    @Value("${spring.application.name}")
    private String application;
    @Value("${app.id}")
    private String app;

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    //public AppConfiguration(String application, String app) {
    //    this.application = application;
    //    this.app = app;
    //}
}
