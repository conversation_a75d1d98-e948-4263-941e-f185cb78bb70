package com.wsgjp.ct.sale.bus.entity;

import java.math.BigInteger;
import java.util.Date;

public class BusData {
    /**
     * 任务类型
     */
    private TaskType taskType;
    /**
     * 任务ID
     */
    private BigInteger id;
    /**
     * 任务的期望完成时间
     */
    private Date expectedTime;
    /**
     * MD5值-身份标识
     */
    private String identityLabel;
    /**
     * 任务内容
     */
    private String busContent;
    /**
     * 程序版本信息
     */
    private String sourceApp;
    private BigInteger profileId;
    private String MqId;
    private TaskState taskState;
    private String busResult;
    private String userApp;
    private Date executeTime;

    public TaskType getTaskType() {
        return taskType;
    }

    public void setTaskType(TaskType taskType) {
        this.taskType = taskType;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public Date getExpectedTime() {
        return expectedTime;
    }

    public void setExpectedTime(Date expectedTime) {
        this.expectedTime = expectedTime;
    }

    public String getIdentityLabel() {
        return identityLabel;
    }

    public void setIdentityLabel(String identityLabel) {
        this.identityLabel = identityLabel;
    }

    public String getBusContent() {
        return busContent;
    }

    public void setBusContent(String busContent) {
        this.busContent = busContent;
    }

    public String getSourceApp() {
        return sourceApp;
    }

    public void setSourceApp(String sourceApp) {
        this.sourceApp = sourceApp;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getMqId() {
        return MqId;
    }

    public void setMqId(String mqId) {
        MqId = mqId;
    }

    public TaskState getTaskState() {
        return taskState;
    }

    public void setTaskState(TaskState taskState) {
        this.taskState = taskState;
    }

    public String getBusResult() {
        return busResult;
    }

    public void setBusResult(String busResult) {
        this.busResult = busResult;
    }

    public String getUserApp() {
        return userApp;
    }

    public void setUserApp(String userApp) {
        this.userApp = userApp;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }
}
