package com.wsgjp.ct.sale.bus.center;

import com.wsgjp.ct.sale.bus.entity.TaskData;
import com.wsgjp.ct.sale.bus.entity.TaskResult;
import com.wsgjp.ct.sale.bus.entity.TaskState;
import com.wsgjp.ct.sale.bus.entity.TaskType;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务执行器
 */
public interface BusActuator {

    /**
     * 获取执行器对应的任务类型
     */
    TaskType taskType();

    /**
     * 消费任务
     *
     * @param taskData 任务数据
     * @return 消费结果
     */
    List<TaskResult> execute(List<TaskData> taskData);

    default List<TaskResult> build(String msg, TaskState taskState, List<TaskData> taskData) {
        List<TaskResult> taskResults = new ArrayList<>();
        for (TaskData taskDatum : taskData) {
            taskResults.add(new TaskResult(msg,taskState,taskDatum));
        }
        return taskResults;
    }

}
