package com.wsgjp.ct.sale.bus.center;

import com.wsgjp.ct.sale.bus.entity.*;

import java.math.BigInteger;
import java.util.List;

public interface BusStarter {

    /**
     * 执行任务
     */
    boolean invokeTraining(TrainingParams trainingParams);

    List<TaskResult> execute(TaskData taskData);
    List<TaskResult> execute(TaskData taskData,String source);
    /*
     * 执行关闭所有预期执行时间超长的任务
     */
    void closeAllErrorTasks();

    /**

    /**
     * 通过配置检查是否立即执行 任务，不将任务发送到Mq中
     * @param taskData
     * @param profileId
     */
    void sendBusOrExecute(List<Task> taskData, BigInteger profileId);

    LockParams getBusTrainingLocker(BigInteger profileId);
    LockParams getBusBusinessLocker(BigInteger profileId);
    LockParams getSplitDeliverBillLocker(BigInteger profileId);
    TrainingParams defaultTrainingConfigs();

    boolean splitAndAddBigBusContent( BigInteger profileId, BigInteger taskId, String json);
}
