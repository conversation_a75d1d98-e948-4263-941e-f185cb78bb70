<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.bus.mapper.BusSysDataMapper">
    <select id="list" resultType="com.wsgjp.ct.support.dao.entity.SysDataEntity">
        select  `id`,`profile_id`,`sub_name`,`sub_value`, `description`,last_modify_etype_id
        from sys_data where profile_id=#{profileId} and sub_name=#{subName}
    </select>

    <update id="updateById">
        update sys_data set sub_value= #{data.subValue},id=#{data.id}
                        where profile_id=#{profileId} and sub_name=#{data.subName} and id = #{oldId}
    </update>
    <insert id="insert">
        insert into sys_data (id,profile_id,sub_name,sub_value,description,last_modify_etype_id) value
            (#{data.id},#{profileId},#{data.subName},#{data.subValue},#{data.description},#{data.lastModifyEtypeId})
    </insert>
    <update id="setValue">
        update sys_data set sub_value= #{subValue}
        where profile_id=#{profileId} and sub_name=#{subName}
    </update>

    <select id="listByKeys" resultType="com.wsgjp.ct.support.dao.entity.SysDataEntity">
        select `id`,`profile_id`,`sub_name`,`sub_value`, `description`,last_modify_etype_id from sys_data where profile_id=#{profileId} and
        sub_name in
        <foreach collection="subNames" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
    </select>
</mapper>