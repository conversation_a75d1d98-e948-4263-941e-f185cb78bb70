<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.bus.mapper.BusMapper">

    <insert id="push">
        INSERT INTO td_bus_data
        (id, profile_id, expected_time, execute_time, source_app, identity_label, task_type, bus_content,bus_result)
        VALUES
        <foreach collection="taskDataList"  item="item" separator=",">
        (#{item.id},#{item.profileId},#{item.expectedTime},#{item.expectedTime},#{item.sourceApp},#{item.identityLabel},#{item.taskType},#{item.busContent},"")
        </foreach>
    </insert>
    <insert id="pushTasks">
        INSERT INTO td_bus_data
        (id, profile_id, expected_time, execute_time, source_app, identity_label, task_type, bus_content,bus_result)
        VALUES
        <foreach collection="taskDataList"  item="item" separator=",">
            (#{item.id},#{item.profileId},#{item.expectedTime},#{item.expectedTime},#{item.sourceApp},#{item.identityLabel},#{item.taskType},#{item.busContent},"")
        </foreach>
    </insert>
    <update id="ack">
        <foreach collection="ackData" item="item" separator=";">
            update td_bus_data set task_state = #{item.taskState},bus_result=#{item.busResult},
            user_app=left(concat(user_app,':',#{item.userApp}),150),execute_count=execute_count+1,execute_time=#{item.executeTime}
            where id in
            <foreach collection="item.ids" open="(" item="item" close=")" separator=",">
                #{item}
            </foreach>
            and profile_id = #{profileId}
        </foreach>
    </update>
    <update id="closeAllErrorTasks">
        update td_bus_data set task_state = 12,bus_result=#{msg}
                           where profile_id=#{profileId}
                             and expected_time &lt; DATE_SUB(now(), INTERVAL #{afterTimeHour} hour) and task_state = 0
    </update>
    <update id="update">
        <foreach collection="busTaskInfos" item="item">
            update td_bus_data set source_app=left(concat(source_app,':',#{item.msgId}),180)
            where profile_id=#{profileId} and id = #{item.busId};
        </foreach>
    </update>
    <select id="select" resultType="com.wsgjp.ct.sale.bus.entity.TaskData">
        select tba.id, tba.profile_id, tba.expected_time, tba.execute_time,
               tba.task_state, tba.execute_count, tba.source_app, tba.identity_label,
               tba.task_type, tba.bus_content, tba.bus_result, tba.user_app, tba.create_time, tba.update_time,
               tba.id ids
            from td_bus_data tba where tba.profile_id = #{profileId} and tba.id = #{id}
    </select>
    <select id="pull" resultType="com.wsgjp.ct.sale.bus.entity.TaskData">
        select tba.id, tba.profile_id, tba.expected_time, tba.execute_time,
        tba.task_state, tba.execute_count, tba.source_app, tba.identity_label,
        tba.task_type, tba.bus_content, tba.bus_result, tba.user_app, tba.create_time, tba.update_time,
        data.ids from
        (select
            group_concat(tba.id order by tba.execute_time asc) ids,
            SUBSTRING_INDEX(GROUP_CONCAT(tba.id ORDER BY tba.execute_time desc),',',1) id,
            tba.profile_id from td_bus_data tba
            where tba.profile_id = #{profileId}
            <if test="taskTypes != null">
                <foreach collection="taskTypes" item="type" open="and tba.task_type in (" separator="," close=")">#{type}
                </foreach>
            </if>
            and tba.expected_time <![CDATA[<=]]> #{expectedTime}
            and tba.expected_time <![CDATA[>=]]> #{minExpectedTime}
            and tba.execute_count <![CDATA[<=]]> 3
            <if test="taskStates != null">
                <foreach collection="taskStates" item="state" open="and tba.task_state in (" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            group by tba.task_type,tba.identity_label
            order by max(tba.execute_time) asc
            limit #{limit}
        ) data
        left join td_bus_data tba on tba.profile_id = data.profile_id and tba.id = data.id
        where data.profile_id = #{profileId}
    </select>
    <select id="pullSimple" resultType="com.wsgjp.ct.sale.bus.entity.TaskData">
        select
        tba.id, tba.profile_id, tba.expected_time, tba.execute_time,
        tba.task_state, tba.execute_count, tba.source_app, tba.identity_label,
        tba.task_type, tba.bus_content, tba.bus_result, tba.user_app, tba.create_time,
        tba.update_time
        from
        td_bus_data tba
        where
        tba.profile_id = #{profileId}
        <if test="taskTypes != null">
            <foreach collection="taskTypes" item="type" open="and tba.task_type in (" separator="," close=")">#{type}
            </foreach>
        </if>
        and tba.expected_time <![CDATA[<=]]> #{expectedTime}
        and tba.expected_time <![CDATA[>=]]> #{minExpectedTime}
        and tba.execute_count <![CDATA[<=]]> 3
        <if test="taskStates != null">
            <foreach collection="taskStates" item="state" open="and tba.task_state in (" separator="," close=")">
                #{state}
            </foreach>
        </if>
        limit
        <if test="limit ==null or limit lte 10000">
            10000
        </if>
        <if test="limit !=null and limit gt 10000">
            #{limit}
        </if>

    </select>
    <insert id="insertBigBusContentBatch">
        INSERT INTO td_text_assembly
        (id, profile_id, primary_id, big_data, data_index)
        VALUES
        <foreach collection="textAssemblies"  item="item" separator=",">
            (#{item.id},#{item.profileId},#{item.primaryId},#{item.bigData},#{item.dataIndex})
        </foreach>
    </insert>

    <select id="getBigBusContents" resultType="com.wsgjp.ct.sale.bus.entity.TextAssembly">
        select id, profile_id, primary_id, big_data, data_index from td_text_assembly where profile_id=#{profileId} and primary_id=#{taskId}
        order by data_index
    </select>

    <insert id="insertBusData">
        INSERT INTO td_bus_data
        (id, profile_id, expected_time, execute_time,task_state, source_app, identity_label, task_type, bus_content,bus_result, user_app)
        VALUES
        <foreach collection="busDatas"  item="item" separator=",">
            (#{item.id},#{item.profileId},#{item.expectedTime},#{item.expectedTime},#{item.taskState},#{item.sourceApp},#{item.identityLabel},#{item.taskType},#{item.busContent},#{item.busResult},#{item.userApp})
        </foreach>
    </insert>
</mapper>
