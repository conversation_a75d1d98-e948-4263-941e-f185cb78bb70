<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.bus.mapper.LockerDataMapper">
    <select id="list" resultType="com.wsgjp.ct.sale.bus.entity.LockerData">
        select  `id`,`profile_id`,`lock_key`,`lock_head`, `description`
        from pub_concurrent_lock_cache where profile_id=#{profileId} and lock_key=#{lockKey}
    </select>
    <update id="updateById">
        update pub_concurrent_lock_cache set lock_head= #{data.lockHead},id=#{data.id}
        where profile_id=#{profileId} and lock_key=#{data.lockKey} and id = #{oldId}
    </update>
    <insert id="insert">
        insert into pub_concurrent_lock_cache (id,profile_id,lock_key,lock_head,description) value
            (#{data.id},#{profileId},#{data.lockKey},#{data.lockHead},#{data.description})
    </insert>
    <update id="setValue">
        update pub_concurrent_lock_cache set lock_head= #{lockHead}
        where profile_id=#{profileId} and lock_key=#{lockKey}
    </update>
</mapper>