package com.wsgjp.ct.sale.monitor.bifrost.entity;

import ngp.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashSet;
import java.util.Set;

public class BifrostApiMonitorInfo {
    /**
     * api接口名称
     */
    private String apiName;
    /**
     * 平台返回的状态码
     */
    private String statusCode;
    /**
     * 平台返回的错误信息
     */
    private String message;

    private boolean success;

    /**
     * 应用名称
     */
    private String applicationName;
    /**
     * 御城河日志上传api或拼多多指纹 api名称集合
     */
    private Set<String> logApiList = new HashSet<>();

    public boolean getSuccess() {
        return success;
    }

    public Set<String> getLogApiList() {
        return logApiList;
    }

    public void addLogApis(Set<String> logApiList) {
        if (CollectionUtils.isNotEmpty(logApiList)) {
            this.logApiList.addAll(logApiList);
        }
    }

    public void addLogApi(String logApi) {
        if (StringUtils.isNotEmpty(logApi)) {
            this.logApiList.add(logApi);
        }
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getApiName() {
        if (apiName == null) {
            apiName = "";
        }
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getMessage() {
        if (StringUtils.isEmpty(message)) {
            message = "";
        }
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    @Override
    public String toString() {
        return "BifrostApiMonitorInfo{" +
                "apiName='" + apiName + '\'' +
                ", statusCode='" + statusCode + '\'' +
                ", message='" + message + '\'' +
                ", success=" + success +
                '}';
    }

    public String getApplicationName() {
        if (applicationName == null) {
            applicationName = "";
        }
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }
}
