package com.wsgjp.ct.sale.monitor.config;

import com.wsgjp.ct.sale.monitor.JarvisMonitorBuilder;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.builder.NgpMonitorResourceBuilder;
import ngp.monitor.prometheus.NgpPrometheusMonitor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2021/8/19 19:28
 */
@Configuration
@Import({MonitorService.class, JarvisMonitorBuilder.class})
public class EshopOrderMonitorAutoConfig {
    @Bean
    public NgpMonitorResourceBuilder ngpMonitorResourceBuilder(NgpPrometheusMonitor monitor) {
        return new NgpMonitorResourceBuilder(monitor);
    }
}
