package com.wsgjp.ct.sale.monitor;

import com.wsgjp.ct.support.context.CurrentUser;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Tags;
import ngp.monitor.prometheus.NgpPrometheusMonitor;
import ngp.monitor.support.MeterType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className JarvisMonitorBuilder
 */
@Component
public class JarvisMonitorBuilder {
    private static final Logger logger = LoggerFactory.getLogger(NgpResource.class);
    private static final ConcurrentMap<String, AtomicLong> maps = new ConcurrentHashMap(10);
    private static NgpPrometheusMonitor monitor;

    public JarvisMonitorBuilder(NgpPrometheusMonitor monitor) {
        JarvisMonitorBuilder.monitor = monitor;
    }

    public static NgpResource create(String key, String tags, MeterType type) {
        return new NgpResource(key, tags, type,1);
    }

    public static NgpResource create(String key, String tags, MeterType type, int length) {
        return new NgpResource(key, tags, type, length);
    }

    public static class NgpResource {
        protected String keyName;
        protected String tags;
        protected String[] tagList;
        protected MeterType type;
        protected long time;
        protected int length;
        protected long limitTime;

        private NgpResource(String key, String tags, MeterType type, int length) {
            this.keyName = key;
            this.tags = tags;
            this.tagList = StringUtils.tokenizeToStringArray(tags, ",");
            this.type = type;
            this.length = length<=0 ? 1 : length;
        }

        public void start() {
            this.time = System.currentTimeMillis();
        }
        public void update(int length) {
            this.length = length;
        }
        public void  updateTags(String tags) {
            this.tagList = StringUtils.tokenizeToStringArray(tags, ",");
        }
        protected AtomicLong addAndSet(long value,String type) {
            AtomicLong atomicInteger = this.getAtomicInteger(this.keyName,type);
            if (atomicInteger != null) {
                atomicInteger.addAndGet(value);
            }
            return atomicInteger;
        }

        public long getLimitTime() {
            return limitTime;
        }

        public void end(boolean hasEx) {
            try {
                long len = (length == 0 ? 1L : length);
                // 简化逻辑，所有调度的监控都对时间和总数据量进行保存处理
                this.limitTime = System.currentTimeMillis() - this.time;
                if(this.limitTime > 60 * 1000 * 10) {
                    logger.error("自动审核耗时超过10分钟的记录：账套：{}", CurrentUser.getProfileId());
                }
                // 合计时间
                setSummary(limitTime, this.keyName, "time");
                // 平均时间
//                setSummary(limitTime / len,"average");
                // 合计单数
                setSummary(len, this.keyName, "flow");
                if (hasEx) {
                    // 失败次数
                    setErrorSummary(1L, "biz.monitor.error", this.keyName);
                }
            } catch (Throwable var4) {
                JarvisMonitorBuilder.logger.error("资源监控初始化失败", var4);
            }
        }
        protected void setErrorSummary(double data,String key,String sourceKeyName) {
            String meterKey = key;
            String value = sourceKeyName.replace('.', '_') + ":" + this.tags.replace(',', '_');
            String[] tagList = new String[]{"errorType", value};
            Tags tags = Tags.of(tagList);
            logger.error(meterKey + "@" + value, new RuntimeException("检查到任务执行失败，记录一个堆栈"));
            DistributionSummary summary = JarvisMonitorBuilder.monitor.summary(meterKey, meterKey, tags);
            if (summary == null) {
                logger.error("获取监控失败-Null");
                return;
            }
            summary.record(data);
        }
        protected void setSummary(double data,String key,String type) {
            String meterKey = key;
            if (!StringUtils.isEmpty(type)) {
                meterKey = key + "." + type;
            }
            DistributionSummary summary = JarvisMonitorBuilder.monitor.summary(meterKey, meterKey, Tags.of(this.tagList));
            if(summary == null) {
                logger.error("获取监控失败-Null");
                return;
            }
            summary.record(data);
        }

        protected AtomicLong getAtomicInteger(String key,String type) {
            String meterKey = key;

            if (!StringUtils.isEmpty(type)) {
                meterKey = key + "." + type;
            }

            if (!JarvisMonitorBuilder.maps.containsKey(meterKey)) {
                AtomicLong atomicInteger = new AtomicLong();
                try{
                    JarvisMonitorBuilder.monitor.gauge(meterKey, Tags.of(this.tagList), atomicInteger, AtomicLong::get);
                }catch (Exception exception) {
                    throw exception;
                }
                JarvisMonitorBuilder.maps.put(meterKey, atomicInteger);
            }

            return (AtomicLong)JarvisMonitorBuilder.maps.get(meterKey);
        }
    }
}
