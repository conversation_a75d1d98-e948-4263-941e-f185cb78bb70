package com.wsgjp.ct.sale.monitor.entity;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021/8/23 11:32
 */
public enum MonitorTypeEnum implements CodeEnum {

    /**
     * 自动下单
     */
    PL_BS_ORDER_AUTO_DOWNLOAD_USE_TIME_TP(1000, "自动下单-单次切片任务订单耗时", "pl.bs.order.auto.download。use.time.tp"),
    PL_BS_ORDER_AUTO_DOWNLOAD_DELAY_TP(1001, "自动下单-消费延迟", "pl.bs.order.auto.download.delay.tp"),
    PL_BS_ORDER_AUTO_DOWNLOAD_DELAY(1002, "自动下单延迟", "pl.bs.order.auto.download.delay"),
    PL_BS_ORDER_BUILD_TIME(1030, "构建订单耗时", "pl.bs.order.build.time"),
    PL_BS_ORDER_SAVE_TIME(1033, "保存订单耗时", "pl.bs.order.save.time"),
    //通过订单上的修改时间与本地时间做比较 计算平均耗时
    PL_BS_ORDER_MODIFY_TIME(1097, "修改时间耗时", "pl.bs.order.modify.time"),

    PL_BS_ORDER_DOWNLOAD_AUTO_DATA_CREATE_COUNT(1003, "新增订单量", "pl.bs.order.download.auto.data.create.count"),
    PL_BS_ORDER_DOWNLOAD_AUTO_DATA_MODIFY_COUNT(1004, "更新订单量", "pl.bs.order.download.auto.data.modify.count"),
    PL_BS_ORDER_DOWNLOAD_ERROR_COUNT(1005, "自动下单-切片失败数量", "pl.bs.order.download.auto.error.count"),

    PL_BS_ORDER_DOWNLOAD_AUTO_DATA_SLICE_SUCCESS_COUNT(1101, "自动下单-切片成功数量", "pl.bs.order.download.auto.data.slice" +
            ".success.count"),
    PL_BS_ORDER_DOWNLOAD_AUTO_DATA_SLICE_FAIL_COUNT(1102, "自动下单-切片失败数量", "pl.bs.order.download.auto.data.slice.fail" +
            ".count"),

    PL_BS_ORDER_ALLDOWNLOAD_AUTO_DATA_RETRY_THREE_COUNT(1103, "自动下单重试-次数超过3", "pl.bs.order.alldownload.auto.data" +
            ".retry.three.count"),
    /**
     * 增量补单
     */
    //通过订单上的修改时间与本地时间做比较 计算平均耗时
    PL_BS_ORDER_INCREMENTDOWNLOAD_AUTO_MODIFY_TIME(1098, "修改时间耗时", "pl.bs.order.incrementdownload.auto.modify.time"),
    PL_BS_ORDER_INCREMENTDOWNLOAD_AUTO_DATA_CREATE_COUNT(1006, "增量补单-新增订单量", "pl.bs.order.incrementdownload.auto.data" +
            ".create.count"),
    PL_BS_ORDER_INCREMENTDOWNLOAD_AUTO_DATA_MODIFY_COUNT(1007, "增量补单-更新订单量", "pl.bs.order.incrementdownload.auto.data" +
            ".modify.count"),
    /**
     * 全量补单 ⭐
     */
    PL_BS_ORDER_ALLDOWNLOAD_AUTO_DATA_CREATE_COUNT(1008, "全量补单-新增订单量", "pl.bs.order.alldownload.auto.data.create" +
            ".count"),
    PL_BS_ORDER_ALLDOWNLOAD_AUTO_DATA_MODIFY_COUNT(1009, "全量补单-更新订单量", "pl.bs.order.alldownload.auto.data.modify" +
            ".count"),
    /**
     * TMC下单
     */
    PL_BS_ORDER_DOWNLOAD_TMC_TP_DELAY(1010, "TMC下单-TP延迟", "pl.bs.order.download.tmc.tp.delay"),
    PL_BS_ORDER_DOWNLOAD_TMC_QPS(1011, "TMC消费速率", "pl.bs.order.download.tmc.qps"),
    PL_BS_ORDER_DOWNLOAD_TMC_USE_TIME(1012, "TMC下耗时", "pl.bs.order.download.tmc.use.time"),
    PL_BS_ORDER_DOWNLOAD_TMC_PRODUCE_TP(1013, "TMC生产者TP", "pl.bs.order.download.tmc.produce.tp"),
    /**
     * 下单订单接口组监控
     */
    PL_API_ORDER_DOWNLOAD_SLICE_COUNT(1015, "下载订单切片执行数量", "pl.api.order.download.count"),
    PL_API_ORDER_DOWNLOAD_SLICE_ERROR_COUNT(1017, "下载订单切片执行异常数量", "pl.api.order.download.error.count"),
    PL_BS_ORDER_DOWNLOAD_MANUAL_DATA_CREATE_COUNT(1018, "手工下单-新增订单量", "pl.bs.order.download.manual.data.create.count"),
    PL_BS_ORDER_DOWNLOAD_MANUAL_DATA_MODIFY_COUNT(1019, "手工下单-更新订单量", "pl.bs.order.download.manual.data.modify.count"),
    /**
     * 自动刷新网店商品 ⭐ 之前方法返回值有问题，提交构建之后试试
     */
    PL_BS_PLATFORM_PRUDUCTSKU_REFRESH_AUTO_TP_TIME(1020, "自动刷新网店商品-TP延时", "pl.bs.platform.pruductsku.refresh.auto.tp" +
            ".time"),
    pl_BS_PRODUCT_REFRESH_AUTO_COUNT(1021, "自动刷新网店商品-切片次数", "pl.bs.product.refresh.auto.count"),
    PL_BS_PRODUCT_REFRESH_AUTO_ERROR_COUNT(1022, "自动刷新网店商品-异常切片次数", "pl.bs.product.refresh.auto.error.count"),
    PL_BS_PLATFORM_PRUDUCTSKU_REFRESH_AUTO_DATA_CREATE_COUNT(1023, "自动刷新网店商品-新增网店商品数量", "pl.bs.platform.pruductsku" +
            ".refresh.auto.data.create.count"),
    PL_BS_PLATFORM_PRUDUCTSKU_REFRESH_AUTO_DATA_MODIFY_COUNT(1024, "自动刷新网店商品-更新网店商品数量", "pl.bs.platform.pruductsku" +
            ".refresh.auto.data.modify.count"),
    /**
     * 手动刷新网店商品 之前方法返回值有问题，提交构建之后试试
     */
    PL_BS_PLATFORM_PRUDUCTSKU_REFRESH_MANUAL_TP_TIME(1025, "手动刷新网店商品-TP延时", "pl.bs.platform.pruductsku.refresh.manual" +
            ".tp.time"),
    PL_BS_PLATFORM_PRUDUCTSKU_REFRESH_MANUAL_QPS(1026, "手动刷新网店商品-QPS", "pl.bs.platform.pruductsku.refresh.manual.qps"),
    PL_BS_PLATFORM_PRUDUCTSKU_REFRESH_MANUAL_ERROR_QPS(1027, "手动刷新网店商品-异常QPS", "pl.bs.platform.pruductsku.refresh" +
            ".manual.error.qps"),
    PL_BS_PLATFORM_PRUDUCTSKU_REFRESH_MANUAL_DATA_CREATE_COUNT(1028, "手动刷新网店商品-新增网店商品数量", "pl.bs.platform.pruductsku" +
            ".refresh.manual.data.create.count"),
    PL_BS_PLATFORM_PRUDUCTSKU_REFRESH_MANUAL_DATA_MODIFY_COUNT(1029, "手动刷新网店商品-更新网店商品数量", "pl.bs.platform.pruductsku" +
            ".refresh.manual.data.modify.count"),


    PL_BS_ORDER_SAVE_QPS(1034, "QPS（根据订单量来）", "pl.bs.order.save.qps"),
    PL_BS_ORDER_SAVE_ERROR_QPS(1035, "异常QPS（根据订单量来）", "pl.bs.order.save.error.qps"),
    /**
     * 授权成功率
     */
    PL_BS_AUTH(1036, "授权成功率", "pl.bs.auth"),
    /**
     * 手工同步发货
     */
    PL_BS_ORDER_REQ_PLATFORM_DELIVER_MANUAL_TP_TIME(1037, "订单发货延时（订单付款时间到同步发货时间）", "pl.bs.order.req.platform.deliver" +
            ".manual.tp.time"),
    PL_BS_ORDER_REQ_PLATFORM_DELIVER_MANUAL_QPS(1038, "QPS", "pl.bs.order.req.platform.deliver.manual.qps"),
    PL_BS_ORDER_REQ_PLATFORM_DELIVER_MANUAL_ERROR_QPS(1039, "异常QPS", "pl.bs.order.req.platform.deliver.manual.error" +
            ".qps"),
    PL_BS_ORDER_REQ_PLATFORM_DELIVER_MANUAL_TP_WAIT_COUNT(1040, "TP待同步发货订单数量", "pl.bs.order.req.platform.deliver" +
            ".manual.tp.wait.count"),
    PL_BS_ORDER_REQ_PLATFORM_DELIVER_MANUAL_TP_COUNT(1041, "TP同步发货订单数量", "pl.bs.order.req.platform.deliver.manual.tp" +
            ".count"),
    /**
     * 手动同步库存
     */
    PL_PLATFORM_SYNC_STOCK_COUNT(1042, "库存同步调用接口的SKU数量", "pl.api.sync.stock.count"),
    PL_PLATFORM_SYNC_STOCK_ERROR_COUNT(1043, "库存同步调用接口失败的SKU数量", "pl.api.sync.stock.error.count"),
    /**
     * 自动同步库存
     */
    PL_BS_PLATFORM_PRODUCTSKU_REQ_PLATFORM_SYNCSTOCK_AUTO_TP_TIME(1045, "TP RT", "pl.bs.platform.productsku.req" +
            ".platform.syncstock.auto.tp.time"),
    PL_BS_PLATFORM_PRODUCTSKU_REQ_PLATFORM_SYNCSTOCK_AUTO_QPS(1046, "QPS", "pl.bs.platform.productsku.req.platform" +
            ".syncstock.auto.qps"),
    PL_BS_PLATFORM_PRODUCTSKU_REQ_PLATFORM_SYNCSTOCK_AUTO_ERROR_QPS(1047, "异常QPS", "pl.bs.platform.productsku.req" +
            ".platform.syncstock.auto.error.qps"),

    /**
     * 自动下载售后单
     */
    PL_BS_REFUND_DOWNLOAD_AUTO_TP_DELAY(1048, "TP", "pl.bs.refund.download.auto.tp.delay"),
    PL_BS_REFUND_DOWNLOAD_AUTO_QPS(1049, "QPS", "pl.bs.refund.download.auto.qps"),
    PL_BS_REFUND_DOWNLOAD_AUTO_ERROR_QPS(1050, "异常QPS", "pl.bs.refund.download.auto.error.qps"),
    PL_BS_REFUND_DOWNLOAD_AUTO_DATA_CREATE_COUNT(1051, "自动下载售后单新增订单数", "pl.bs.refund.download.auto.data.create.count"),
    PL_BS_REFUND_DOWNLOAD_AUTO_DATA_MODIFY_COUNT(1052, "自动下载售后单修改订单数", "pl.bs.refund.download.auto.data.modify.count"),

    /**
     * 手动下载售后单
     */
    PL_BS_REFUND_DOWNLOAD_MANUAL_TP_DELAY(1053, "TP延迟", "pl.bs.refund.download.manual.tp.delay"),
    /**
     * 构建售后单
     */
    PL_BS_REFUND_BUILD_TP_TIME(1058, "TP RT", "pl.bs.refund.build.tp.time"),
    PL_BS_REFUND_BUILD_QPS(1059, "QPS", "pl.bs.refund.build.qps"),
    PL_BS_REFUND_BUILD_ERROR_QPS(1060, "异常QPS", "pl.bs.refund.build.error.qps"),
    /**
     * 保存售后单
     */
    PL_BS_REFUND_SAVE_TP_TIME(1061, "TP RT", "pl.bs.refund.save.tp.time"),

    /**
     * 手动提交订单
     */
    PL_BS_ORDER_COMMIT_MANUAL_TP_TIME(1064, "TP RT", "pl.bs.order.commit.manual.tp.time"),
    PL_BS_ORDER_COMMIT_MANUAL_QPS(1065, "QPS", "pl.bs.order.commit.manual.qps"),
    PL_BS_ORDER_COMMIT_MANUAL_ERROR_QPS(1066, "异常QPS", "pl.bs.order.commit.manual.error.qps"),
    PL_BS_ORDER_COMMIT_MANUAL_TP_WAIT_COUNT(1067, "TP待提交订单数", "pl.bs.order.commit.manual.tp.wait.count"),
    PL_BS_ORDER_COMMIT_PRESALE_MANUAL_TP_TIME(1068, "TP提交预售 RT", "pl.bs.order.commit.presale.manual.tp.time"),
    PL_BS_ORDER_COMMIT_PRESALE_MANUAL_QPS(1069, "提交预售QPS", "pl.bs.order.commit.presale.manual.qps"),
    PL_BS_ORDER_COMMIT_PRESALE_MANUAL_ERROR_QPS(1070, "提交预售异常QPS", "pl.bs.order.commit.presale.manual.error.qps"),
    PL_BS_ORDER_COMMIT_PRESALE_MANUAL_MQ_LENGTH(1071, "提交预售队列长度", "pl.bs.order.commit.presale.manual.mq.length"),
    PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_TP_TIME(1072, "TP提交交易单 RT", "pl.bs.order.commit.deliverbill.manual.tp.time"),
    PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_QPS(1073, "提交交易单QPS", "pl.bs.order.commit.deliverbill.manual.qps"),
    PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_ERROR_QPS(1074, "提交交易单异常QPS", "pl.bs.order.commit.deliverbill.manual.error" +
            ".qps"),
    PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_MQ_LENGTH(1075, "提交交易单队列长度", "pl.bs.order.commit.deliverbill.manual.mq" +
            ".length"),

    /**
     * 自动提交订单
     */
    PL_BS_ORDER_COMMIT_AUTO_QPS(1076, "QPS", "pl.bs.order.commit.auto.qps"),
    PL_BS_ORDER_COMMIT_AUTO_ERROR_QPS(1077, "异常QPS", "pl.bs.order.commit.auto.error.qps"),
    PL_BS_ORDER_COMMIT_AUTO_MQ_LENGTH(1078, "队列长度", "pl.bs.order.commit.auto.mq.length"),
    PL_BS_ORDER_COMMIT_AUTO_TP_WAIT_COUNT(1079, "TP-待提交订单数", "pl.bs.order.commit.auto.tp.wait.count"),
    PL_BS_ORDER_COMMIT_PRESALE_AUTO_TP_TIME(1080, "TP提交预售RT（批次/单量平均值）", "pl.bs.order.commit.presale.auto.tp.time"),
    PL_BS_ORDER_COMMIT_PRESALE_AUTO_QPS(1081, "提交预售QPS", "pl.bs.order.commit.presale.auto.qps"),
    PL_BS_ORDER_COMMIT_PRESALE_AUTO_ERROR_QPS(1082, "提交预售异常QPS", "pl.bs.order.commit.presale.auto.error.qps"),
    PL_BS_ORDER_COMMIT_PRESALE_AUTO_MQ_LENGTH(1083, "提交预售队列长度", "pl.bs.order.commit.presale.auto.mq.length"),
    PL_BS_ORDER_COMMIT_DELIVERBILL_AUTO_TP_TIME(1084, "TP提交交易单RT", "pl.bs.order.commit.deliverbill.auto.tp.time"),
    PL_BS_ORDER_COMMIT_DELIVERBILL_AUTO_QPS(1085, "提交交易单QPS", "pl.bs.order.commit.deliverbill.auto.qps"),

    STOCK_BS_CALCULATE_MAIN_TP(1088, "汇总耗时", "stock.calculate.use.time"),
    STOCK_BS_CALCULATE_DELAY_TP(1090, "汇总延迟", "stock.calculate.delay"),
    STOCK_BS_CALCULATE_QUEUE_HEAP(1093, "库存汇总队列堆积", "stock.calculate.queue.size"),
    STOCK_BS_CALCULATE_ERROR_PROFILE_COUNT(1094, "出现异常帐套的次数", "stock.calculate.error.profile.count"),
    STOCK_BS_SEND_MQ_COUNT(1095, "库存同步生产数量", "stock.send.mq.count"),
    STOCK_BS_AUTO_FIX_TP(1096, "自动修复耗时", "stock.calculate.auto.fix.time"),

    /**
     * 订单漏单检查工具
     */
    PL_BS_ORDER_CHECK_EXISTENCE_OMISSION_SHOP_COUNT(1099, "订单检查存在漏单的网店数", "pl.bs.order.check.existence.omission.shop" +
            ".count"),
    PL_BS_ORDER_CHECK_EXISTENCE_OMISSION_ORDER_COUNT(1100, "订单检查漏单的数量", "pl.bs.order.check.existence.omission.order" +
            ".count"),
    PL_BS_ORDER_CHECK_EXISTENCE_OMISSION_PROFILE_COUNT(1101, "订单检查漏单的账套数量", "pl.bs.order.check.existence.omission" +
            ".profile.count"),


    /**
     * 接口组新增的埋点(2000开始)
     */
    PL_RDS_REQUEST_COUNT(2000, "RDS请求次数", "pl.rds.request.count"),
    PL_API_REQUEST_COUNT(2001, "API请求次数", "pl.api.request.count"),
    PL_RDS_REQUEST_ERROR_COUNT(2002, "RDS请求失败次数", "pl.rds.request.error.count"),
    PL_API_REQUEST_ERROR_COUNT(2003, "API请求失败次数", "pl.api.request.error.count"),
    PL_API_ORDER_DOWNLOAD_COUNT(2004, "订单获取次数", "pl.api.download.order.count"),
    PL_API_ORDER_DOWNLOAD_FEEDBACK_COUNT(2005, "订单下载回传次数", "pl.api.download.order.feedback.count"),
    PL_API_ORDER_DOWNLOAD_FEEDBACK_ERROR_COUNT(2006, "订单下载回传失败次数", "pl.api.download.order.feedback.error.count"),


    /**
     * POS组新增埋点(3000开始)
     */
    PL_API_SALE_POS_LOGIN_DEVICE_COUNT(3000, "统计设备登录数", "pl.api.sale.pos.login.device.count"),
    PL_API_SALE_POS_LOGIN_DEVICE_TP(3001, "统计设备登录TPS", "pl.api.sale.pos.login.device.tp"),
    PL_API_SALE_POS_SUBMIT_TP_TIME(3002, "单据提交耗时TP", "pl.api.sale.pos.submit.tp.time"),
    PL_API_SALE_POS_PAY_TP_TIME(3003, "三方支付耗时", "pl.api.sale.pos.pay.tp.time"),
    PL_API_SALE_POS_SELECT_PTYPE_TP_TIME(3004, "查询商品耗时", "pl.api.sale.pos.select.ptype.tp.time"),
    PL_API_SALE_POS_GET_VIP_TP_TIME(3005, "获取会员列表耗时", "pl.api.sale.pos.get_vip.tp.time"),
    PL_API_SALE_POS_GET_VIP_DETAIL_TP_TIME(3006, "获取会员详情耗时", "pl.api.sale.pos.get_vip_detail.tp.time"),
    PL_API_SALE_POS_PROMOTION_LIST_TP_TIME(3007, "获取促销耗时", "pl.api.sale.pos.promotion_list.tp.time"),
    PL_API_SALE_POS_GET_SHIFT_CHANGE_STATISTICS_TP_TIME(3008, "获取交接班数据耗时", "pl.api.sale.pos.get" +
            ".shift_change_statistics" +
            ".tp.time"),
    PL_API_SALE_POS_SHIFT_CHANGE_TP_TIME(3009, "交接班耗时", "pl.api.sale.pos.shift_change.tp.time"),
    PL_API_SALE_POS_SUBMIT_SAVE_SDK_TIME(3010, "单据提交sdk保存耗时", "pl.api.sale.pos.submit.save.sdk.time"),
    PL_API_SALE_POS_SUBMIT_VIP_ASSERT_TIME(3011, "单据提交资产处理耗时", "pl.api.sale.pos.submit.vip.assert.time"),

    PL_API_SALE_POS_SUBMIT_POST_BILL_TIME(3012, "单据保存提交核算耗时", "pl.api.sale.pos.submit.post.bill.time"),

    PL_API_SALE_POS_GET_RECHARGE_CHECK_TIME(3013, "会员充值前验证", "pl.api.sale.pos.recharge.check.time"),
    PL_API_SALE_POS_GET_RECHARGE_GFIT_TIME(3014, "会员充值赠品保存", "pl.api.sale.pos.recharge.gift.time"),
    PL_API_SALE_POS_GET_RECHARGE_TIME(3015, "会员充值前验证", "pl.api.sale.pos.recharge.time");


    private final int code;
    private final String name;
    private final String topic;

    MonitorTypeEnum(int code, String name, String topic) {
        this.name = name;
        this.code = code;
        this.topic = topic;
    }


    public String getTopic() {
        return topic;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
