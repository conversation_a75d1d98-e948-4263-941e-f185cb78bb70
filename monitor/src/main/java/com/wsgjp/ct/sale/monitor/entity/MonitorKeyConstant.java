package com.wsgjp.ct.sale.monitor.entity;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className MonitorKeyConstant
 */
public class MonitorKeyConstant {
    //直接调度的 策略审核 监控
    public final static String BIZ_DEALING_TOOL_AUDIT = "biz.dealing.tool.audit";
    // 自动工具 工具入口 12
    public final static String BIZ_DEALING_TOOL_IN_AUDIT = "biz.dealing.tool.in.audit";
    //手工审核监控 3
    public final static String BIZ_DEALING_AUDIT = "biz.dealing.audit";
    //执行单个策略监控  7,8
    public final static String BIZ_DEALING_STRATEGY = "biz.dealing.strategy";
    //策略审核 执行策略监控 5
    public final static String BIZ_DEALING_ALL_STRATEGY = "biz.dealing.all.strategy";
    //标记变更监控
    public final static String BIZ_NOTIFY_MARK = "biz.notify.mark";
    //提交监控  1,10
    public final static String BIZ_DEALING_SUBMIT = "biz.dealing.submit";
    public final static String BIZ_QUERY_DELIVER = "biz.query.deliver";
    public final static String BIZ_CANCEL_SPLIT = "biz.cancel.split";
    public final static String BIZ_CANCEL_MERGE = "biz.cancel.merge";
    public final static String BIZ_RESET_BILL = "biz.reset.bill";

    public final static String BIZ_BUS_CUSTOMER = "biz.bus.customer";

    // 提交仓储 20
    public static final String BIZ_SUBMIT_WMS = "biz.submit.wms";

    //售后审核
    public final static String BIZ_AFTERSLAE_AUDIT= "biz.dealing.refund.audit";
    //售后 生成单据
    public final static String BIZ_AFTERSLAE_GENERATE = "biz.dealing.refund.audit";
    //预售提交
    public final static String BIZ_ADVANCE_SUBMIT = "biz.dealing.advance";
    //预售收款
    public final static String BIZ_ADVANCE_GATHER = "biz.dealing.tool.advance";
    public final static String BIZ_REDIS_LOCK = "biz.dealing.redis.lock";
    /**
     * 工具执行/web执行任务的启动耗时
     */
    public final static String BIZ_PROCESS_EXECUTE = "biz.process.execute";




}
