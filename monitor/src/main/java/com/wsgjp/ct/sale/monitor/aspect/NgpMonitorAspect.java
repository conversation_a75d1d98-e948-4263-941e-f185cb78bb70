package com.wsgjp.ct.sale.monitor.aspect;

import com.wsgjp.ct.sale.monitor.anno.NgpMonitorResource;
import com.wsgjp.ct.sale.monitor.builder.NgpMonitorResourceBuilder;
import ngp.monitor.aspect.NgpResourceAspect;
import ngp.monitor.prometheus.NgpPrometheusMonitor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

@Aspect
public class NgpMonitorAspect {
    private final Logger logger = LoggerFactory.getLogger(NgpResourceAspect.class);
    private final NgpPrometheusMonitor monitor;
    private final ConcurrentMap<String, AtomicInteger> maps = new ConcurrentHashMap(10);



    public NgpMonitorAspect(NgpPrometheusMonitor monitor) {
        this.monitor = monitor;
    }

    @Pointcut("@annotation(resource)")
    public void serviceStatistics(NgpMonitorResource resource) {
    }

    @Around("serviceStatistics(resource)")
    public Object doAround(ProceedingJoinPoint joinPoint, NgpMonitorResource resource) throws Throwable {
        NgpMonitorResourceBuilder.NgpMonitorResource monitorResource = NgpMonitorResourceBuilder.create(resource.name(), resource.tagStrings(), resource.type());
        monitorResource.start();
        boolean hasEx = false;

        Object var5;
        try {
            var5 = joinPoint.proceed();
        } catch (Throwable var14) {
            hasEx = true;
            throw var14;
        } finally {
            try {
                monitorResource.end(hasEx);
            } catch (Throwable var13) {
                this.logger.error("资源监控初始化失败", var13);
            }

        }

        return var5;
    }

    private String getTagsString(ProceedingJoinPoint joinPoint, NgpMonitorResource resource) {
        String name = resource.name();
        if (StringUtils.isEmpty(resource.tagStrings())) {
            return null;
        } else {
            Method method = ((MethodSignature)joinPoint.getSignature()).getMethod();
            Object value = this.getValue(resource.tagStrings(), method, joinPoint.getArgs());

            assert value != null;

            return value.toString();
        }
    }

    private Object getValue(String spel, Method method, Object[] args) {
        try {
            ExpressionParser parser = new SpelExpressionParser();
            LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
            String[] params = discoverer.getParameterNames(method);
            Expression expression = parser.parseExpression(spel);
            EvaluationContext context = new StandardEvaluationContext();

            for(int len = 0; len < ((String[]) Objects.requireNonNull(params)).length; ++len) {
                context.setVariable(params[len], args[len]);
            }

            return expression.getValue(context);
        } catch (Exception var10) {
            this.logger.error(String.format("资源监控参数异常:%s", spel), var10);
            return null;
        }
    }
}
