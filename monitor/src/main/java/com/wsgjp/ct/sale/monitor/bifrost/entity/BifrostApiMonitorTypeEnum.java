package com.wsgjp.ct.sale.monitor.bifrost.entity;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */
public enum BifrostApiMonitorTypeEnum implements CodeEnum {
    //平台接口响应能力
    PL_INTERFACE_PLATFORM_API_TP_TIME(900001,"API TP RT","pl.interface.platform.api.tp.time"),
    PL_INTERFACE_PLATFORM_API_QPS(900002,"API QPS","pl.interface.platform.api.qps"),
    PL_INTERFACE_PLATFORM_API_ERROR_QPS(900003,"API 异常QPS","pl.interface.platform.api.error.qps"),
    PL_INTERFACE_PLATFORM_API_DATA_COUNT(900004,"API 返回数据的count","pl.interface.platform.api.data.count"),
    PL_INTERFACE_PLATFORM_RDS_TP_TIME(900005,"RDS TP RT","pl.interface.platform.rds.tp.time"),
    PL_INTERFACE_PLATFORM_RDS_QPS(900006,"RDS QPS","pl.interface.platform.rds.qps"),
    PL_INTERFACE_PLATFORM_RDS_ERROR_QPS(900007,"RDS 异常QPS","pl.interface.platform.rds.error.qps"),
    PL_INTERFACE_PLATFORM_RDS_DATA_COUNT(900008,"RDS返回数据的count","pl.interface.platform.rds.data.count"),
    PL_INTERFACE_PLATFORM_MQ_TP_TIME(900009,"MQ TP RT","pl.interface.platform.mq.tp.time"),
    PL_INTERFACE_PLATFORM_MQ_QPS(900010,"MQ QPS","pl.interface.platform.mq.qps"),
    PL_INTERFACE_PLATFORM_MQ_ERROR_QPS(900011,"MQ 异常QPS","pl.interface.platform.mq.error.qps"),
    //平台接口资源使用情况
    PL_INTERFACE_PLATFORM_API_REQ_TOTAL_COUNT(900012,"	API请求总量","pl.interface.platform.api.req.total.count"),
    PL_INTERFACE_PLATFORM_API_REQ_ERROR_TOTAL_COUNT(900013,"API请求出错总量","pl.interface.platform.api.req.error.total.count"),
    PL_INTERFACE_PLATFORM_API_REQ_ERROR_INFO_TOTAL_COUNT(900014,"API 请求出错的报错明细总量","pl.interface.platform.api.req.error.info.total.count"),
    //平台业务接口响应能力
    PL_INTERFACE_API_TP_TIME(900015,"TP RT","pl.interface.api.tp.time"),
    PL_INTERFACE_API_QPS(900016,"业务接口请求QPS","pl.interface.api.qps"),
    PL_INTERFACE_API_ERROR_QPS(900017,"业务接口请求异常QPS","pl.interface.api.error.qps"),
    PL_INTERFACE_API_REQ_TOTAL_COUNT(900019,"业务接口请求总量","pl.interface.api.req.total.count"),
    PL_INTERFACE_API_REQ_ERROR_TOTAL_COUNT(900020,"业务接口请求出错总量","pl.interface.api.req.error.total.count"),
    PL_INTERFACE_API_REQ_ERROR_INFO_TOTAL_COUNT(900021,"业务接口请求出错信息总量","pl.interface.api.req.error.info.total.count"),
    //彩虹桥接口响应能力
    PL_INTERFACE_BIFROST_API_TP_TIME(910001,"TP RT","pl.interface.bifrost.api.tp.time"),
    PL_INTERFACE_BIFROST_API_QPS( 910002,"QPS","pl.interface.bifrost.api.qps"),
    PL_INTERFACE_BIFROST_API_ERROR_QPS(910003,"异常QPS","pl.interface.bifrost.api.error.qps"),
    //加密系统
    PL_INTERFACE_SIS_API_TP_TIME(920001,"API TP RT","pl.interface.sis.api.tp.time"),
    PL_INTERFACE_SIS_API_QPS(920002,"API QPS","pl.interface.sis.api.qps"),
    PL_INTERFACE_SIS_API_REQ_ERROR_COUNT(920003,"API错误请求总次数","pl.interface.sis.api.req.error.count"),
    PL_INTERFACE_SIS_API_REQ_ERROR_CODE(920004,"API错误请求报错明细","pl.interface.sis.api.req.error.code"),
    //登录中心
    PL_INTERFACE_LOGIN_API_TP_TIME(930001,"API TP RT","pl.interface.login.api.tp.time"),
    PL_INTERFACE_LOGIN_API_QPS(930002,"API QPS","pl.interface.login.api.qps"),
    PL_INTERFACE_LOGIN_API_REQ_ERROR_COUNT(930003,"API错误请求总次数","pl.interface.login.api.req.error.count"),
    PL_INTERFACE_LOGIN_API_REQ_ERROR_CODE(930004,"API错误请求报错明细","pl.interface.login.api.req.error.code"),
    //平台监控（御城河、拼多多指纹）
    PL_INTERFACE_PLATFORM_LOG_API_TP_TIME(940001,"API TP RT","pl.interface.platform.log.api.tp.time"),
    PL_INTERFACE_PLATFORM_LOG_API_QPS(940002,"API QPS","pl.interface.platform.log.api.qps"),
    PL_INTERFACE_PLATFORM_LOG_API_REQ_ERROR_COUNT(940003,"API错误请求总次数","pl.interface.platform.log.api.req.error.count"),
    PL_INTERFACE_PLATFORM_LOG_API_REQ_ERROR_CODE(940004,"API错误请求报错明细","pl.interface.platform.log.api.req.error.code"),

    //TMC:元气订单推送相关
    PL_INTERFACE_YQ_TP_TIME(950001,"TP RT","pl.interface.yq.tp.time"),
    PL_INTERFACE_YQ_QPS(950002,"tmc请求QPS","pl.interface.yq.qps"),
    PL_INTERFACE_YQ_REQ_ERROR_TOTAL_COUNT(950003,"元气tmc请求出错总量","pl.interface.api.yq.error.total.count"),
    PL_INTERFACE_YQ_REQ_ERROR_INFO_TOTAL_COUNT(950004,"元气tmc请求出错信息总量","pl.interface.api.yq.error.info.total.count"),
    ;

    private final int code;
    private final String name;
    private final String topic;

    BifrostApiMonitorTypeEnum(int code, String name, String topic) {
        this.name = name;
        this.code = code;
        this.topic = topic;
    }


    public String getTopic() {
        return topic;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
