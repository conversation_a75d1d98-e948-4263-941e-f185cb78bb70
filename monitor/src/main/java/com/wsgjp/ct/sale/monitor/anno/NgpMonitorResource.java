package com.wsgjp.ct.sale.monitor.anno;

import ngp.monitor.support.MeterType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * NGP监控注解
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface NgpMonitorResource {
    String name();

    MeterType type() default MeterType.Summary;

    String tagStrings() default "";
}
