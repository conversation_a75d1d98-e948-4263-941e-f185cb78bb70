package com.wsgjp.ct.sale.monitor;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import ngp.monitor.prometheus.NgpPrometheusMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2022/4/26 10:25
 */
@Service
public class MonitorService {
    private static final ConcurrentMap<String, AtomicLong> maps = new ConcurrentHashMap<>(10);

    private final NgpPrometheusMonitor prometheusMonitor;

    public MonitorService(NgpPrometheusMonitor prometheusMonitor) {
        this.prometheusMonitor = prometheusMonitor;
    }

    private static final Logger logger = LoggerFactory.getLogger(MonitorService.class);


    /**
     * 统计总量
     *
     * @param topic
     * @param shopType
     * @param total
     */
    public void recordSum(String topic, ShopType shopType, int total) {
        AtomicLong delayLong = getAtomicInteger(topic, "shopType", shopType.getName());
        delayLong.addAndGet(total);
    }

    /**
     * 统计总量
     *
     * @param topic
     * @param shopType
     * @param total
     */
    public void recordSum(String topic, ShopType shopType, BigInteger total) {
        AtomicLong delayLong = getAtomicInteger(topic, "shopType", shopType.getName());
        delayLong.addAndGet(Long.parseLong(String.valueOf(total)));
    }

    /**
     * @param topic     监控标题
     * @param shopType  网店类型
     * @param stateCode 成功失败标签（成功：200，失败：500）
     */
    public void recordOPS(String topic, ShopType shopType, String stateCode) {
        Tag tag = Tag.of("shopType", shopType.getName());
        Tags tags = Tags.of(tag).and(Tag.of("state", stateCode));
        AtomicLong atomicLong = getAtomicInteger(topic, tags);
        atomicLong.addAndGet(1);
    }

    /**
     * 统计TP延时----平台
     *
     * @param topic
     * @param shopType
     * @param delay
     */
    public void recordTP(String topic, ShopType shopType, long delay) {
        try {
            Tag tag = Tag.of("shopType", shopType.getName());
            Tags tags = Tags.of(tag);
            String key = String.format("%s_%s",topic, tags);
            DistributionSummary summary = prometheusMonitor.summary(key, topic, tags);
            summary.record(delay);
        } catch (Exception ex) {
            logger.error("记录监控报错:" + ex.getMessage(), ex);
        }
    }

    public void recordTP(String topic, ShopType shopType, String tagKey, String tagVal, long delay) {
        try {
            Tag tag = Tag.of("shopType", shopType.getName());
            Tags tags = Tags.of(tag).and(Tag.of(tagKey, tagVal));;
            String key = String.format("%s_%s",topic, tags);
            DistributionSummary summary = prometheusMonitor.summary(key, topic, tags);
            summary.record(delay);
        } catch (Exception ex) {
            logger.error("记录监控报错:" + ex.getMessage(), ex);
        }
    }


    /**
     * 统计TP延时----通用
     */
    public void recordTP(String topic, String name, String type, long delay) {
        Tag tag = Tag.of(name, type);
        Tags tags = Tags.of(tag);
        String key = String.format("%s_%s", name, type);
        DistributionSummary summary = prometheusMonitor.summary(key, topic, tags);
        summary.record(delay);
    }

    /**
     * 通用
     *
     * @param topic 监控标题
     * @param type  网店类型
     */
    public void recordOPSSSuccess(String topic, String veidoo, String type, Integer count) {
        String stateCode = "200";
        Tag tag = Tag.of(veidoo, type);
        Tags tags = Tags.of(tag).and(Tag.of("state", stateCode));
        AtomicLong stateLong = getAtomicInteger(topic, tags);
        stateLong.addAndGet(count);
    }

    /**
     * 通用
     *
     * @param topic 监控标题
     * @param type  类型
     */
    public void recordOPSSFail(String topic, String veidoo, String type, Integer count) {
        String stateCode = "500";
        Tag tag = Tag.of(veidoo, type);
        Tags tags = Tags.of(tag).and(Tag.of("state", stateCode));
        AtomicLong stateLong = getAtomicInteger(topic, tags);
        stateLong.addAndGet(count);
    }


    /**
     * 统计总量
     *
     * @param topic
     * @param type
     * @param total
     */
    public void recordSum(String topic, String veidoo, String type, int total) {
        AtomicLong delayLong = getAtomicInteger(topic, veidoo, type);
        delayLong.addAndGet(total);
    }

    public void recordSum(String topic, String veidoo, String type, long total) {
        AtomicLong delayLong = getAtomicInteger(topic, veidoo, type);
        delayLong.addAndGet(total);
    }

    public void recordSum(String topic,  ShopType shopType, String tagKey, String tagVal, long total) {
        Tags tags = Tags.of(Tag.of("shopType", shopType.getName())).and( Tag.of(tagKey, tagVal));
        AtomicLong delayLong = getAtomicInteger(topic, tags);
        delayLong.addAndGet(total);
    }

    public void recordSum(String topic, Tags tags, long total) {
        AtomicLong delayLong = getAtomicInteger(topic, tags);
        delayLong.addAndGet(total);
    }

    public void increment(String topic, ShopType shopType) {
        Tags tags = Tags.of(Tag.of("shopType", shopType.getName()));
        prometheusMonitor.increment(topic, 1, tags);
    }

    public void increment(String topic, ShopType shopType, Tags tags) {
        tags.and(Tag.of("shopType", shopType.getName()));
        prometheusMonitor.increment(topic, 1, tags);
    }

    public void recordSum(String topic) {
        if (!maps.containsKey(topic)) {
            AtomicLong atomicLong = new AtomicLong();
            prometheusMonitor.gauge(topic, atomicLong, AtomicLong::get);
            maps.put(topic, atomicLong);
            atomicLong.incrementAndGet();
        }
        maps.get(topic).incrementAndGet();
    }

    public void recordSum(String topic, long count) {
        if (!maps.containsKey(topic)) {
            AtomicLong atomicLong = new AtomicLong();
            prometheusMonitor.gauge(topic, atomicLong, AtomicLong::get);
            maps.put(topic, atomicLong);
            atomicLong.addAndGet(count);
        }
        maps.get(topic).addAndGet(count);
    }

    private AtomicLong getAtomicInteger(String topic, String tagKey, String tagVal) {
        Tags tags = Tags.of(Tag.of(tagKey, tagVal));
        return getAtomicInteger(topic, tags);
    }

    private AtomicLong getAtomicInteger(String topic, Tags tags) {
        String mapKey = String.format("%s-%s", topic, tags.toString());
        if (!maps.containsKey(mapKey)) {
            AtomicLong atomicLong = new AtomicLong();
            prometheusMonitor.gauge(topic, tags, atomicLong, AtomicLong::get);
            maps.put(mapKey, atomicLong);
            return atomicLong;
        }
        return maps.get(mapKey);
    }
}
