package com.wsgjp.ct.sale.monitor.entity;

import bf.datasource.typehandler.CodeEnum;

public enum JarvisMonitorTypeEnum implements CodeEnum {
//    BIZ_AUTO_AUDIT_SUCCESS_RATE(100,"审核 自动审核成功率","biz.dealing.auto.audit.success.rate"),
    BIZ_AUTO_AUDIT_SUM(101,"审核 自动审核数量","biz.dealing.auto.audit.sum"),
    BIZ_AUTO_AUDIT_TP_TIME(102,"审核 自动审核平均耗时TP","biz.dealing.auto.audit.tp.time"),
    BIZ_AUTO_AUDIT_QPS(103,"审核 自动审核QPS","biz.dealing.auto.audit.count"),
    BIZ_AUTO_AUDIT_ERROR_QPS(104,"审核 自动审核异常QPS","biz.dealing.auto.audit.error"),
    BIZ_AUTO_COMPENSATE_AUDIT_SUM(105,"审核 自动补偿数量","biz.dealing.auto.compensate.audit.sum"),

    BIZ_AUTO_MARK_EXCEPTION_QPS(200,"徽标 计算徽标QPS", "biz.dealing.auto.mark.exception.count"),
    BIZ_AUTO_MARK_EXCEPTION_SUM(201,"徽标 计算徽标订单数量", "biz.dealing.auto.mark.exception.sum"),
    BIZ_AUTO_MARK_EXCEPTION_TP_TIME(202,"徽标 计算徽标平均耗时TP", "biz.dealing.auto.mark.exception.tp.time"),
    BIZ_AUTO_MARK_EXCEPTION_ERROR_QPS(203,"徽标 计算徽标异常QPS", "biz.dealing.auto.mark.exception.error"),
    BIZ_LOCK_SUM(204,"Jarvis redis锁数量", "biz.dealing.lock.sum"),
    BIZ_LOCK_OVERTIME_SUM(205,"Jarvis redis锁超时数量", "biz.dealing.lock.overtime.sum"),
    BIZ_LOCK_FAIL_SUM(206,"Jarvis redis锁获取失败数量", "biz.dealing.lock.fail.sum"),
    BIZ_SALE_BUS_SUM(207,"Sale-Bus消息数量", "biz.sale.bus.sum"),
    BIZ_SALE_BUS_FAIL_SUM(208,"Sale-Bus消息数量失败", "biz.sale.bus.fail.sum"),
    BIZ_SALE_BUS_USE_SUM(209,"Sale-Bus消息数量", "biz.sale.bus.use.sum"),
    BIZ_SALE_BUSS_USE_FAIL_SUM(210,"Sale-Bus消息数量失败", "biz.sale.bus.use.fail.sum"),
    BIZ_SALE_BUSS_USE_TP(211,"Sale-Bus消息单次执行时间", "biz.sale.bus.use.tp"),

    ;
    private final int code;
    private final String name;
    private final String topic;

    JarvisMonitorTypeEnum(int code, String name, String topic) {
        this.code = code;
        this.name = name;
        this.topic = topic;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public String getTopic() {
        return topic;
    }
}
