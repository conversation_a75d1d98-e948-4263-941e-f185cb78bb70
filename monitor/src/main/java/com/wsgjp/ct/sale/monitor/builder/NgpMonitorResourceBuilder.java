package com.wsgjp.ct.sale.monitor.builder;


import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Tags;
import ngp.monitor.prometheus.NgpMonitorBuilder;
import ngp.monitor.prometheus.NgpPrometheusMonitor;
import ngp.monitor.support.MeterType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

public class NgpMonitorResourceBuilder {
    private static final Logger logger = LoggerFactory.getLogger(NgpMonitorBuilder.NgpResource.class);
    private static final ConcurrentMap<String, AtomicLong> maps = new ConcurrentHashMap(10);
    private  static NgpPrometheusMonitor monitor;

    public NgpMonitorResourceBuilder(NgpPrometheusMonitor monitor) {
        NgpMonitorResourceBuilder.monitor = monitor;
    }

    public static NgpMonitorResourceBuilder.NgpMonitorResource create(String key, String tags, MeterType type) {
        return new NgpMonitorResourceBuilder.NgpMonitorResource(key, tags, type);
    }

    public static class NgpMonitorResource {
        private String key;
        private String tags;
        private String[] tagList;
        private MeterType type;
        private long time;


        private NgpMonitorResource(String key, String tags, MeterType type) {
            this.key = key;
            this.tags = tags;
            this.tagList = StringUtils.tokenizeToStringArray(tags, ",");
            this.type = type;
        }

        public void start() {
            this.time = System.currentTimeMillis();
        }

        public void end(boolean hasEx) {
            try {
                AtomicLong atomicInteger;
                if (this.type.equals(MeterType.Gauge)) {
                    atomicInteger = this.getAtomicInteger("sum");
                    if (atomicInteger != null) {
                        atomicInteger.addAndGet(System.currentTimeMillis() - this.time);
                    }

                    AtomicLong count = this.getAtomicInteger("count");
                    if (count != null) {
                        count.addAndGet(1L);
                    }
                }

                if (this.type.equals(MeterType.Summary)) {
                    DistributionSummary summary = this.getSummary();
                    if (summary != null) {
                        summary.record((double) (System.currentTimeMillis() - this.time));
                    }
                }

                if (hasEx) {
                    atomicInteger = this.getAtomicInteger("error");
                    if (atomicInteger != null) {
                        atomicInteger.addAndGet(1L);
                    }
                }
            } catch (Throwable var4) {
                NgpMonitorResourceBuilder.logger.error("资源监控初始化失败", var4);
            }

        }

        private DistributionSummary getSummary() {
            DistributionSummary summary = NgpMonitorResourceBuilder.monitor.summary(this.tags, this.key, Tags.of(this.tagList));
            return summary;
        }

        private AtomicLong getAtomicInteger(String type) {
            String meterKey = this.key;
            if (!StringUtils.isEmpty(type)) {
                meterKey = this.key + "." + type;
            }

            if (!NgpMonitorResourceBuilder.maps.containsKey(meterKey)) {
                AtomicLong atomicInteger = new AtomicLong();
                NgpMonitorResourceBuilder.monitor.gauge(meterKey, Tags.of(this.tagList), atomicInteger, AtomicLong::get);
                NgpMonitorResourceBuilder.maps.put(meterKey, atomicInteger);
            }

            return (AtomicLong) NgpMonitorResourceBuilder.maps.get(meterKey);
        }
    }
}