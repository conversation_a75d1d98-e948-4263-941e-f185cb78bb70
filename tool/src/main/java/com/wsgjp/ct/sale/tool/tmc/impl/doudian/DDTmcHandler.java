package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import cn.hutool.core.bean.BeanUtil;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderRefundStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyBase;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.dto.refund.EshopTmcRefundMsgDto;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.doudian.build.DoudianRefundBuilder;
import com.wsgjp.ct.sale.platform.factory.doudian.entity.DouDianRefundMessage;
import com.wsgjp.ct.sale.tool.tmc.controller.TmcConst;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
public class DDTmcHandler extends DDNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DDTmcHandler.class);
    private final TmcNotifyBase notifyBase;

    private final EshopTmcRefundMsgService tmcRefundMsgService;


    public DDTmcHandler(TmcNotifyBase notifyBase, EshopTmcRefundMsgService tmcRefundMsgService) {
        this.notifyBase = notifyBase;
        this.tmcRefundMsgService = tmcRefundMsgService;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======抖店进入DDTmcOrderHandler.invoker方法======");
        if (StringUtils.isEmpty(invokeMessage.getMessage())) {
            LOGGER.error("抖店消息为空{}", JsonUtils.toJson(invokeMessage));
            return TmcConst.FAIL;
        }
        EshopNotifyChange change = buildEshopNotifyChange(invokeMessage);
        if (Objects.isNull(change)) {
            //当前未对接该类型的 tmc 消息
            LOGGER.info("账套ID{},转换成EshopNotifyChange为空", invokeMessage.getProfileId());
            return TmcConst.SUCCESS;
        }
        //这里发订单变更消息，系统会按单号下载
        SupportUtil.doOrderNotify(change.getOnlineShopId(), change, invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.Doudian.getCode());
        return TmcConst.SUCCESS;
    }

    protected void saveTmcRefundMsg(BigInteger profileId, BigInteger eshopId, ShopType shopType, String message, DouDianTmcData data) {
        try {
            if (data == null) {
                return;
            }
            Object afterSaleStatus;
            afterSaleStatus = data.getRefundStatus();
            if (afterSaleStatus == null) {
                afterSaleStatus = -1;
            }
            String refundId = data.getAfterSaleId();
            if (StringUtils.isBlank(refundId)) {
                return;
            }
            if (shopType == null) {
                shopType = ShopType.Doudian;
            }
            String tradeId = data.getTradeId();
            RefundStatus refundStatus = DoudianRefundBuilder.getRefundStatus(Integer.parseInt(afterSaleStatus.toString()));
            EshopTmcRefundMsgDto refundMsgDto = new EshopTmcRefundMsgDto(profileId, eshopId, shopType, tradeId, refundId, message, refundStatus);
            tmcRefundMsgService.saveOrUpdateTmcRefundMsg(refundMsgDto);
        } catch (Exception ex) {
            LOGGER.error("账套ID{},店铺ID{},保持售后消息失败，失败原因：{}.message:{}", profileId, eshopId, ex.getMessage(), message, ex);
        }
    }

    @Override
    public String serviceName() {
        return TmcConst.DOU_DIAN_HANDLER_NAME;
    }


    /**
     * 获取EshopNotifyChange
     *
     * @return tmc 消息
     */
    public EshopNotifyChange buildEshopNotifyChange(InvokeMessageEntity invokeMessage) {
        DouDianTmcMessage tmcMessage = JsonUtils.toObject(invokeMessage.getMessage(), DouDianTmcMessage.class);
        DouDianTmcData data = JsonUtils.toObject(tmcMessage.getData(), DouDianTmcData.class);
        String tag = tmcMessage.getTag();
        DouDianTmcType ddTmcType = DouDianTmcType.getDouDianTmcTypeByTag(tag);
        if (null == ddTmcType) {
            return null;
        }
        EshopNotifyChange changeInfo = handlerMessageByType(data, ddTmcType.getTmcType());
        if (DouDianTmcType.doudian_logistics_orderTagPush.equals(ddTmcType)) {
            handlerOrderTagMessage(data, invokeMessage);
        }
        if (TMCType.RefundOrder.equals(ddTmcType.getTmcType())) {
            changeInfo.setRefundOrderId(data.getAfterSaleId());
            ShopType shopType = invokeMessage.getShopType() != null ? invokeMessage.getShopType() : ShopType.Doudian;
            saveTmcRefundMsg(invokeMessage.getProfileId(), invokeMessage.getEshopId(), shopType, invokeMessage.getMessage(), data);
            saveRefundToPlEshopNotifyChange(changeInfo, invokeMessage, tmcMessage);
        }
        changeInfo.setContent(invokeMessage.getMessage());
        if (invokeMessage.getEshopId() != null) {
            changeInfo.setEshopId(invokeMessage.getEshopId());
        }
        return changeInfo;
    }

    private Date parseDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX", Locale.ENGLISH);
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(date, formatter);
            Instant instant = zonedDateTime.toInstant();
            return Date.from(instant);
        } catch (Exception ex) {
            LOGGER.error("解析时间失败：{}", date, ex);
        }
        return null;
    }

    private void saveRefundToPlEshopNotifyChange(EshopNotifyChange change, InvokeMessageEntity invokeMessage, DouDianTmcMessage tmcMessage) {
        if (StringUtils.isBlank(invokeMessage.getMessage()) || change == null || tmcMessage == null) {
            return;
        }
        try {
            EshopNotifyChange curNotifyChange = new EshopNotifyChange();
            BeanUtil.copyProperties(change, curNotifyChange);
            curNotifyChange.setId(UId.newId());
            curNotifyChange.setUpdateTime(DateUtils.getDate());
            curNotifyChange.setProfileId(invokeMessage.getProfileId());
            curNotifyChange.setEshopId(invokeMessage.getEshopId());
            if (StringUtils.isBlank(tmcMessage.getTag())) {
                return;
            }
            String tag = tmcMessage.getTag();
            //只处理以下三种类型
            //买家发起售后申请消息200
            //售后关闭消息207
            //退款成功消息 206
            if (!StringUtils.equals(tag, "200") && !StringUtils.equals(tag, "207") && !StringUtils.equals(tag, "206")) {
                return;
            }
            curNotifyChange.setContent(tmcMessage.getData());
            DouDianRefundMessage curRefundMessage = JsonUtils.toObject(tmcMessage.getData(), DouDianRefundMessage.class);
            if (curRefundMessage == null) {
                return;
            }
            TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
            TMCType tmcType;
            UpdateOrderRefundStateRequest request = new UpdateOrderRefundStateRequest();
            request.setTradeOrderId(curNotifyChange.getTradeOrderId());
            request.setShopId(curNotifyChange.getEshopId());
            request.setOidList(new ArrayList<>(Collections.singletonList(curRefundMessage.getS_id())));
            if (StringUtils.equals(tag, "200") || StringUtils.equals(tag, "206")) {
                tmcType = TMCType.REFUND_STOP;
                if (StringUtils.equals(tag, "206")) {
                    request.setRefundState(ReturnState.SUCCESS);
                } else {
                    request.setRefundState(ReturnState.REFUNDING);
                }
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CREATE_BY_TMC);
            } else {
                tmcType = TMCType.REFUND_STOP_CANCEL;
                request.setRefundState(ReturnState.NONE);
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CANCEL_BY_TMC);
            }
            curNotifyChange.setType(tmcType);
            EshopSaleOrderService eshopSaleOrderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
            List<EshopNotifyChange> eshopNotifyChanges = tmcMapper.queryMessageChangeSorted(curNotifyChange.getProfileId(),
                    Collections.singletonList(curNotifyChange.getTradeOrderId()), curNotifyChange.getEshopId(), TMCType.REFUND_STOP.getCode());
            boolean duplicateMsg = false;
            Date latestMsgTime = null;
            Date curMsgTime = parseDate(curRefundMessage.getUpdate_time());
            EshopNotifyChange latestNotifyChange = null;
            if (CollectionUtils.isNotEmpty(eshopNotifyChanges) && curMsgTime != null) {
                for (EshopNotifyChange eshopNotifyChange : eshopNotifyChanges) {
                    String content = eshopNotifyChange.getContent();
                    if (StringUtils.isEmpty(content)) {
                        continue;
                    }
                    DouDianRefundMessage oldRefundMessage = JsonUtils.toObject(content, DouDianRefundMessage.class);
                    if (oldRefundMessage == null || !StringUtils.equals(oldRefundMessage.getAftersale_id(), curRefundMessage.getAftersale_id())) {
                        continue;
                    }
                    Date msgTime = parseDate(oldRefundMessage.getUpdate_time());
                    if (msgTime == null) {
                        continue;
                    }
                    if (curMsgTime.getTime() <= msgTime.getTime()) {
                        //消息重复。当前消息已经处理过
                        duplicateMsg = true;
                        break;
                    }
                    if (latestNotifyChange == null || latestMsgTime.getTime() < msgTime.getTime()) {
                        latestNotifyChange = eshopNotifyChange;
                        latestMsgTime = msgTime;
                    }
                }
            }
            if (duplicateMsg) {
                return;
            }
            if (latestNotifyChange != null) {
                curNotifyChange.setId(latestNotifyChange.getId());
                tmcMapper.updateEshopNotifyChangeById(curNotifyChange);
            } else {
                tmcMapper.insertMessageChange(curNotifyChange);
            }
            eshopSaleOrderService.updateOrderRefundState(request);
        } catch (Exception ex) {
            if (ex.getMessage() != null && ex.getMessage().contains("订单尚未流入系统")) {
                return;
            }
            LOGGER.error("账套ID{},店铺ID{},保持售后消息到eshop_order_notify表失败，失败原因：{}.message:{}"
                    , change.getProfileId(), change.getEshopId(), ex.getMessage(), invokeMessage.getMessage(), ex);
        }
    }


    private void handlerOrderTagMessage(DouDianTmcData data, InvokeMessageEntity invokeMessage) {
        if (StringUtils.isEmpty(data.getTagKey()) || StringUtils.isEmpty(data.getTagInfo())) {
            return;
        }
        if (!(DouDianTmcConst.PriorityDelivery.equals(data.getTagKey()) && data.getTagInfo().contains(DouDianTmcConst.PriorityDeliveryVal))) {
            return;
        }
        //先将数据插入change表
        EshopNotifyChange eshopSaleOrderChangeInfo = new EshopNotifyChange();
        eshopSaleOrderChangeInfo.setId(UId.newId());
        eshopSaleOrderChangeInfo.setProfileId(invokeMessage.getProfileId());
        eshopSaleOrderChangeInfo.setTradeOrderId(data.getTradeId());
        if (null == invokeMessage.getEshopId() || BigInteger.ZERO.equals(invokeMessage.getEshopId())) {
            // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
            EshopRegisterNotify notify = SupportUtil.buildNotify(data.getShopId(), invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.Doudian.getCode());
            eshopSaleOrderChangeInfo.setEshopId(notify.getId());
        } else {
            eshopSaleOrderChangeInfo.setEshopId(invokeMessage.getEshopId());
        }
        eshopSaleOrderChangeInfo.setContent(invokeMessage.getMessage());
        eshopSaleOrderChangeInfo.setType(TMCType.YOUXIAN_SEND);
        eshopSaleOrderChangeInfo.setCreateTime(new Date());
        notifyBase.insertMessageChange(Collections.singletonList(eshopSaleOrderChangeInfo));
    }

    /**
     * 根据类别创建EshopNotifyChange
     *
     * @param data    tmc消息data内容
     * @param tmcType tmc类型
     * @return EshopNotifyChange
     */
    public EshopNotifyChange handlerMessageByType(DouDianTmcData data, TMCType tmcType) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent("");
        change.setTradeOrderId(data.getTradeId());
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(data.getShopId());
        return change;
    }
}
