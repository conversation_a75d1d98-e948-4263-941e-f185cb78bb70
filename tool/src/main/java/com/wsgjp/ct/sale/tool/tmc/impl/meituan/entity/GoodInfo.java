package com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GoodInfo {
    @JsonProperty("item_id")
    private String itemId;
    @JsonProperty("food_name")
    private String foodName;
    @JsonProperty("count")
    private String count;

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }
}
