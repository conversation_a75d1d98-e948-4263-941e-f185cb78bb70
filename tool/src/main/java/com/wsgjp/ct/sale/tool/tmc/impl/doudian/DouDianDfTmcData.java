package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 抖店tmc消息
 */
public class DouDianDfTmcData {

    /**
     * 厂家id
     */
    @JsonProperty("shop_id")
    private String shopId;
    /**
     * 代打店铺id
     */
    @JsonProperty("user_id")
    private String userId;

    /**
     * 代打订单号
     */
    @JsonProperty("distr_order_id")
    private String distrOrderId;

    /**
     * 回传状态：0，更换电子面单，1：回传
     */
    @JsonProperty("return_status")
    private String returnStatus;

    /**
     * 卖家备注
     */
    @JsonProperty("seller_remark")
    private String sellerRemark;

    /**
     * 取消原因
     */
    @JsonProperty("reason")
    private String reason;

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDistrOrderId() {
        return distrOrderId;
    }

    public void setDistrOrderId(String distrOrderId) {
        this.distrOrderId = distrOrderId;
    }

    public String getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(String returnStatus) {
        this.returnStatus = returnStatus;
    }

    public String getSellerRemark() {
        return sellerRemark;
    }

    public void setSellerRemark(String sellerRemark) {
        this.sellerRemark = sellerRemark;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
