package com.wsgjp.ct.sale.tool.tmc.impl.lst;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 零售通售后单信息
 * <AUTHOR>
 */
public class LstRefundInfo implements Serializable {
    /**
     * 主订单id
     */
    @JsonProperty("main_order_id")
    private String mainOrderId;
    /**
     * 退款单id
     */
    @JsonProperty("refund_id")
    private String refundId;
    /**
     * 卖家登录id
     */
    @JsonProperty("seller_login_id")
    private String sellerLoginId;
    /**
     * 子订单id,多个子订单id以英文逗号分隔
     */
    @JsonProperty("sub_order_ids")
    private String subOrderIds;

    public String getMainOrderId() {
        return mainOrderId;
    }

    public void setMainOrderId(String mainOrderId) {
        this.mainOrderId = mainOrderId;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getSellerLoginId() {
        return sellerLoginId;
    }

    public void setSellerLoginId(String sellerLoginId) {
        this.sellerLoginId = sellerLoginId;
    }

    public String getSubOrderIds() {
        return subOrderIds;
    }

    public void setSubOrderIds(String subOrderIds) {
        this.subOrderIds = subOrderIds;
    }
}
