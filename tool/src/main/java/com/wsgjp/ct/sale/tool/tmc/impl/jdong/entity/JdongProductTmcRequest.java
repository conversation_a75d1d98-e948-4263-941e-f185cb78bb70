package com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity;

/**
 * <AUTHOR>
 */
public class JdongProductTmcRequest extends JdongTmcRequest {
    /**
     * 迁移时间
     */
    private long migrateTime;
    /**
     * 迁移源productId
     */
    private Long sourceProductId;
    /**
     * 迁移目标productId与skuId的关系
     * 值是json结构，Key为新的productId，Value是集合为新的SkuIds
     * {\"3000000\":[4,5],\"2000000\":[1,2,3]}
     */
    private String targetProductIdAndSkuIdsMap;
    /**
     * 三级类目ID
     */
    private String categoryId;

    public long getMigrateTime() {
        return migrateTime;
    }

    public void setMigrateTime(Long migrateTime) {
        this.migrateTime = migrateTime;
    }

    public Long getSourceProductId() {
        return sourceProductId;
    }

    public void setSourceProductId(long sourceProductId) {
        this.sourceProductId = sourceProductId;
    }

    public String getTargetProductIdAndSkuIdsMap() {
        return targetProductIdAndSkuIdsMap;
    }

    public void setTargetProductIdAndSkuIdsMap(String targetProductIdAndSkuIdsMap) {
        this.targetProductIdAndSkuIdsMap = targetProductIdAndSkuIdsMap;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }
}
