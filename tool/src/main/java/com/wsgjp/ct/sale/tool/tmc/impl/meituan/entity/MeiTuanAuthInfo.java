package com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import ngp.utils.HttpUtils;
import utils.JsonUtils;

/**
 * <AUTHOR>
 */
public class MeiTuanAuthInfo {
    /**
     * 操作类型：1-绑定门店；2-解绑门店；3-官网推送token
     */
    @JsonProperty("op_type")
    private String opType;

    /**
     * 门店相关信息
     */
    @JsonProperty("poi_info")
    private String poiInfo;

    private PoiInfo poiInfoObj;

    /**
     * 操作时间
     */
    @JsonProperty("op_time  ")
    private String opTime;

    /**
     * 当前操作来源：1.商家侧通过开放平台门店绑定授权工具操作，推送具体的商家账号信息。2.商家侧申请美团运营协助操作，推送“美团运营”。
     */
    @JsonProperty("op_source")
    private String opSource;

    /**
     * 该字段仅适用于通过接口实现门店自入驻绑定冲突时更改app_poi_code进行重新绑定消息推送。
     */
    @JsonProperty("settle_rebind ")
    private String settleRebind;

    /**
     * ISV类型的app适用的独有推送字段，当门店绑定至ISV的app时会推送门店授权码。 注意，针对第三方软件服务商申请的 ISV 类型的 app，需要确认前期是否已接入“绑定门店后OAuth相关数据推送”接口，该接口与本接口不可兼容。背景为“绑定门店后OAuth相关数据推送”更名为“推送门店绑定和解绑消息”，数据结构有修改，并且两种结构是共存的。所以，如之前对接的本推送与此文档的数据结构不一致，需要在代码改造后，提工单申请进行切换。
     */
    @JsonProperty("authorize_code")
    private String authorizeCode;

    /**
     * 推送token信息
     */
    @JsonProperty("token_info")
    private String tokenInfo;

    /**
     * token信息
     */
    private TokenInfo tokenObj;

    public String getOpTime() {
        return opTime;
    }

    public void setOpTime(String opTime) {
        this.opTime = opTime;
    }

    public String getOpSource() {
        return opSource;
    }

    public void setOpSource(String opSource) {
        this.opSource = opSource;
    }

    public String getSettleRebind() {
        return settleRebind;
    }

    public void setSettleRebind(String settleRebind) {
        this.settleRebind = settleRebind;
    }

    public String getAuthorizeCode() {
        return authorizeCode;
    }

    public void setAuthorizeCode(String authorizeCode) {
        this.authorizeCode = authorizeCode;
    }


    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public String getPoiInfo() {
        return poiInfo;
    }

    public void setPoiInfo(String poiInfo) {
        this.poiInfo = poiInfo;
    }

    public PoiInfo getPoiInfoObj() {
        try {
            if (StringUtils.isNotEmpty(poiInfo) && null == poiInfoObj) {
                //平台数据是URL编码了两次
                poiInfoObj = JsonUtils.toObject(HttpUtils.urlDecode(HttpUtils.urlDecode(poiInfo)), PoiInfo.class);
            }
        }
        catch (Exception ex){
            throw new RuntimeException(ex);
        }
        return poiInfoObj;
    }

    public String getTokenInfo() {
        return tokenInfo;
    }

    public void setTokenInfo(String tokenInfo) {
        this.tokenInfo = tokenInfo;
    }

    public TokenInfo getTokenObj() {
        if (StringUtils.isNotBlank(tokenInfo)) {
            tokenObj = JsonUtils.toObject(HttpUtils.urlDecode(HttpUtils.urlDecode(tokenInfo)), TokenInfo.class);
        }
        return tokenObj;
    }
}
