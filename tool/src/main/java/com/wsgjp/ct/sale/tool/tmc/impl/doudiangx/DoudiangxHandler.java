package com.wsgjp.ct.sale.tool.tmc.impl.doudiangx;

import com.doudian.open.gson.JsonArray;
import com.doudian.open.gson.JsonElement;
import com.doudian.open.utils.JsonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Map;

@Component
public class DoudiangxHandler extends DoudiangxNotifyBase implements MessageHandler {

    private static final Logger logger = LoggerFactory.getLogger(DoudiangxHandler.class);
    private final EshopTmcUtils eshopTmcUtils;
    private final TmcNotifyProxy tmcNotifyProxy;

    public DoudiangxHandler(EshopTmcUtils eshopTmcUtils, TmcNotifyProxy tmcNotifyProxy) {
        this.eshopTmcUtils = eshopTmcUtils;
        this.tmcNotifyProxy = tmcNotifyProxy;
    }

    private final String TradeId = "supply_order_no";
    private final String OnlineEshopId = "supplier_id";

    private final String RefundId = "after_sale_id";


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        logger.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        GeneralResult res = new GeneralResult();
        try {
            JsonArray jsonArray = JsonUtil.fromJsonAsJsonArray(tmMessage);
            if (jsonArray != null) {
                for (JsonElement jsonElement : jsonArray) {
                    String dataJson = JsonUtils.toHashMap(jsonElement.toString()).get("data").toString();
                    Map<String, Object> data = JsonUtils.toHashMap(dataJson);
                    BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
                    if (BigInteger.ZERO.equals(invokeMessage.getEshopId())) {
                        // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                        EshopRegisterNotify notify = SupportUtil.buildNotify(data.get(OnlineEshopId).toString(), 113);
                        if (notify == null || notify.getId() == null || notify.getId().equals(BigInteger.ZERO)) {
                            logger.info("抖店供销消息处理失败：eshopId拿取失败");
                            res.setCode(500L);
                            res.setMessage("店铺id寻找失败");
                            return JsonUtils.toJson(res);
                        }
                        eshopId = notify.getId();
                    } else {
                        eshopId = invokeMessage.getEshopId();
                    }

                    EshopNotifyChange change = handleMessage(data);
                    change.setProfileId(invokeMessage.getProfileId());
                    change.setEshopId(eshopId);
                    SupportUtil.doOrderNotify(data.get(OnlineEshopId).toString(), change, 113);
                }
                res.setCode(0L);
                res.setMessage("success");
                return JsonUtils.toJson(res);
            }
            res.setCode(500L);
            res.setMessage("消息体处理失败");
            return JsonUtils.toJson(res);
        } catch (Exception e) {
            logger.error("{}tmMessage数据转换成OrderRequest实体出错，错误信息：{}", shopTypeName, e.getMessage());
            res.setCode(500L);
            res.setMessage("未知异常:" + e.getMessage());
            return JsonUtils.toJson(res);
        }
    }

    private EshopNotifyChange handleMessage(Map<String, Object> data) {
        EshopNotifyChange changeInfo;
        //只有售后消息才有售后单号
        if (data.get(RefundId) == null || StringUtils.isEmpty(data.get(RefundId).toString())) {
            //订单消息
            changeInfo = handleMessageByType(data, TMCType.Order);
        } else {
            //售后消息
            changeInfo = handleMessageByType(data, TMCType.RefundOrder);
        }
        return changeInfo;
    }

    private EshopNotifyChange handleMessageByType(Map<String, Object> data, TMCType tmcType) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(data.toString());
        // 目前只支持订单号下载
        change.setTradeOrderId(data.get(TradeId).toString());
        // 支持售后单号下载后使用
        if (tmcType == TMCType.RefundOrder) {
            change.setRefundOrderId(data.get(RefundId).toString());
        }
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(data.get(OnlineEshopId).toString());
        change.setCreateTime(new Date());
        return change;
    }


    @Override
    public String serviceName() {
        return "doudiangx.orderandrefund";
    }

}
