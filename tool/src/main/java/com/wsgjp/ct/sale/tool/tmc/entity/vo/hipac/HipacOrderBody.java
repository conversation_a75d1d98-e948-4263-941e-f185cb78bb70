package com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class HipacOrderBody {

    @XmlElement(name = "Order")
    public HipacOrder orderInfo;

    @XmlElement(name = "PayInfo")
    public PayInfo payInfo;

    @XmlElement(name = "Customer")
    public Customer customerInfo;

    @XmlElementWrapper(name = "OrderItemList")
    @XmlElement(name ="OrderItem")
    public List<OrderItem> orderItems;

    public HipacOrder getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(HipacOrder orderInfo) {
        this.orderInfo = orderInfo;
    }

    public PayInfo getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(PayInfo payInfo) {
        this.payInfo = payInfo;
    }

    public Customer getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(Customer customerInfo) {
        this.customerInfo = customerInfo;
    }

    public List<OrderItem> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<OrderItem> orderItems) {
        this.orderItems = orderItems;
    }
}
