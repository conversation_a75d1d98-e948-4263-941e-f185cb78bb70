package com.wsgjp.ct.sale.tool.tmc.impl.meituan;

import cn.hutool.json.JSONUtil;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.impl.auth.AuthManager;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcAuthMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.MeiTuanAuthInfo;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.TokenInfo;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class MeiTuanAuthHandler extends MeiTuanNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(MeiTuanAuthHandler.class);

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        String tmMessage = invokeMessage.getMessage();
        MeiTuanAuthInfo apiRequest;
        try {
            apiRequest = JsonUtils.toObject(tmMessage, MeiTuanAuthInfo.class);
        } catch (Exception ex) {
            LOGGER.error("{}tmMessage数据转换成美团授权信息实体出错，错误信息：{},tmMessage:{}", shopTypeName, ex.getMessage(), tmMessage, ex);
            return buildResponse(ex.getMessage());
        }
        if (null == apiRequest || StringUtils.isBlank(apiRequest.getPoiInfo())) {
            LOGGER.error("{}美团授权信息缺失 ShopAccount,tmMessage:{},apiRequest:{}", shopTypeName, tmMessage, JSONUtil.toJsonStr(apiRequest));
            return buildResponse("授权信息缺失三方门店id");
        }
        //操作类型：1-绑定门店；2-解绑门店；3-官网推送token；4-修改app_poi_code
        if (StringUtils.equals(apiRequest.getOpType(), "2") || StringUtils.equals(apiRequest.getOpType(), "4")) {
            return buildResponse("");
        }
        EshopInfo eshopInfo = SupportUtil.getEshopByShopAccount(invokeMessage.getProfileId(), apiRequest.getPoiInfoObj().getAppPoiCode());
        if (null == eshopInfo) {
            LOGGER.error("美团授权找不到对应的店铺信息,shopAccount:{},tmMessage:{},", apiRequest.getPoiInfoObj().getAppPoiCode(), tmMessage);
            return buildResponse("授权消息=》找不到对应的店铺信息");
        }
        invokeMessage.setEshopId(eshopInfo.getOtypeId());
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiRequest.getPoiInfoObj().getAppPoiCode(), invokeMessage.getShopType().getCode());
        if (Objects.isNull(eshopRegister)) {
            LOGGER.error("profileId:{},店铺类型:{},appPoiCode:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, apiRequest.getPoiInfoObj().getAppPoiCode(), tmMessage);
            return buildResponse("管家婆未找到对应店铺!");
        }
        EshopNotifyChange eshopNotifyChange = buildInsertTmcMsg(eshopRegister, apiRequest, invokeMessage);
        SupportUtil.saveNotifyChange(eshopNotifyChange);
        SupportUtil.doWriteTmcMqLog(invokeMessage, eshopNotifyChange.getTradeOrderId(), "authChange");
        TmcAuthMessage tmcMessage = buildAuthMessage(eshopNotifyChange, apiRequest);
        AuthManager download = new AuthManager();
        String result = download.getTokenAndSave(tmcMessage);
        return buildResponse(result);
    }

    @Override
    public String serviceName() {
        return "authChange";
    }

    private EshopNotifyChange buildInsertTmcMsg(EshopRegisterNotify eshopRegister, MeiTuanAuthInfo apiRequest, InvokeMessageEntity invokeMessage) {
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setProfileId(eshopRegister.getProfileId());
        eshopNotifyChange.setOnlineShopId(apiRequest.getPoiInfoObj().getAppPoiCode());
        eshopNotifyChange.setTradeOrderId(StringUtils.isEmpty(apiRequest.getAuthorizeCode()) ? "" : apiRequest.getAuthorizeCode());
        eshopNotifyChange.setType(TMCType.SAVE_TOKEN);
        eshopNotifyChange.setEshopId(invokeMessage.getEshopId());
        eshopNotifyChange.setContent(invokeMessage.getMessage());
        return eshopNotifyChange;
    }

    private TmcAuthMessage buildAuthMessage(EshopNotifyChange change, MeiTuanAuthInfo apiRequest) {
        TmcAuthMessage message = new TmcAuthMessage();
        message.setAuthCode(apiRequest.getAuthorizeCode());
        message.setEshopId(change.getEshopId());
        message.setShopType(ShopType.MeiTuan);
        if (apiRequest.getPoiInfoObj() != null) {
            message.setShopAccount(apiRequest.getPoiInfoObj().getAppPoiCode());
        }
        message.setProfileId(change.getProfileId());
        TokenInfo tokenObj = apiRequest.getTokenObj();
        if (tokenObj != null) {
            message.setToken(tokenObj.getAccessToken());
            message.setRefreshToken(tokenObj.getRefreshToken());
            Date expireIn = StringUtils.isEmpty(tokenObj.getExpiresIn()) ? null : DateUtils.addMinutes(new Date(), Integer.parseInt(tokenObj.getExpiresIn()));
            Date reExpireIn = StringUtils.isEmpty(tokenObj.getReExpiresIn()) ? null : DateUtils.addMinutes(new Date(), Integer.parseInt(tokenObj.getReExpiresIn()));
            message.setExpiresIn(expireIn);
            message.setReExpiresIn(reExpireIn);
        }
        return message;
    }
}
