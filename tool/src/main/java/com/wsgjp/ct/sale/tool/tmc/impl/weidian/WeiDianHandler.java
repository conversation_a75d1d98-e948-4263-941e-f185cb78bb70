package com.wsgjp.ct.sale.tool.tmc.impl.weidian;

import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.controller.TmcConst;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.BaseNotifyHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Component
public class WeiDianHandler extends BaseNotifyHandler {

    private static final Logger logger = LoggerFactory.getLogger(WeiDianHandler.class);
    private static final String WEI_DIAN_RESPONSE="{\"status\":\"success\"}";

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        //微店消息都有订单id 且售后消息也会通过更新订单更新售后单
        try {
            String message = invokeMessage.getMessage();
            Map body = JsonUtils.toObject(message, Map.class);
            String messageType = getValueByKey(body,"type");
            WeiDianTmcType tmcType = WeiDianTmcType.getPddTmcMsgTypeByMsgTypeName(messageType);
            if(Objects.isNull(tmcType)){
                logger.error("不支持的微店tmc类型", invokeMessage.getMessage());
                return WEI_DIAN_RESPONSE;
            }
            String shopId = getValueByKey(body,"shopId");
            if (StringUtils.isEmpty(shopId)) {
                logger.error("获取shopId失败", invokeMessage.getMessage());
                return WEI_DIAN_RESPONSE;
            }
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(shopId, invokeMessage.getShopType().getCode());
            if (eshopRegister == null) {
                logger.error(String.format("找不到账套微店店铺映射关系，传入的消息 : %s", invokeMessage.getMessage()));
                return WEI_DIAN_RESPONSE;
            }
            EshopNotifyChange change = new EshopNotifyChange();
            change.setProfileId(eshopRegister.getProfileId());
            change.setEshopId(eshopRegister.getId());
            change.setContent(invokeMessage.getMessage());
            //获取订单id
            Map msgBody = (Map) body.get("message");
            String tradeId = getValueByKey(msgBody,"order_id");
            change.setTradeOrderId(tradeId);
            change.setId(UId.newId());
            change.setType(tmcType.getTmcType());
            change.setOnlineShopId(shopId);
            change.setCreateTime(new Date());
            SupportUtil.doNotify(change.getOnlineShopId(), change, invokeMessage.getShopType().getCode());
            return WEI_DIAN_RESPONSE;

        } catch (Exception e) {
            return WEI_DIAN_RESPONSE;
        }
    }

    @Override
    public String serviceName() {
        return TmcConst.WEI_DIAN_HANDLER_NAME;
    }

    private String getValueByKey(Map source, String key){
        if(null == source || source.size()==0){
            return null;
        }
        if(source.containsKey(key)){
            return source.get(key).toString();
        }
        return null;
    }

}
