package com.wsgjp.ct.sale.tool.tmc.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TaoBaoModifyAddressDto {

    @JsonProperty("sellerNick")
    private String sellerNick;
    @JsonProperty("buyerNick")
    private String buyerNick;
    @JsonProperty("originalAddress")
    private OriginalAddressDTO originalAddress;
    @JsonProperty("bizOrderId")
    private String bizOrderId;
    @JsonProperty("modifiedAddress")
    private ModifiedAddressDTO modifiedAddress;
    @JsonProperty("oaid")
    private String oaid;

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public OriginalAddressDTO getOriginalAddress() {
        return originalAddress;
    }

    public void setOriginalAddress(OriginalAddressDTO originalAddress) {
        this.originalAddress = originalAddress;
    }

    public String getBizOrderId() {
        return bizOrderId;
    }

    public void setBizOrderId(String bizOrderId) {
        this.bizOrderId = bizOrderId;
    }

    public ModifiedAddressDTO getModifiedAddress() {
        return modifiedAddress;
    }

    public void setModifiedAddress(ModifiedAddressDTO modifiedAddress) {
        this.modifiedAddress = modifiedAddress;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public class OriginalAddressDTO {
        @JsonProperty("area")
        private String area;
        @JsonProperty("country")
        private String country;
        @JsonProperty("addressDetail")
        private String addressDetail;
        @JsonProperty("province")
        private String province;
        @JsonProperty("town")
        private String town;
        @JsonProperty("city")
        private String city;
        @JsonProperty("phone")
        private String phone;
        @JsonProperty("name")
        private String name;
        @JsonProperty("postCode")
        private String postCode;

        public String getArea() {
            return area;
        }

        public void setArea(String area) {
            this.area = area;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getAddressDetail() {
            return addressDetail;
        }

        public void setAddressDetail(String addressDetail) {
            this.addressDetail = addressDetail;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getTown() {
            return town;
        }

        public void setTown(String town) {
            this.town = town;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPostCode() {
            return postCode;
        }

        public void setPostCode(String postCode) {
            this.postCode = postCode;
        }
    }

    public static class ModifiedAddressDTO {
        @JsonProperty("area")
        private String area;
        @JsonProperty("country")
        private String country;
        @JsonProperty("addressDetail")
        private String addressDetail;
        @JsonProperty("province")
        private String province;
        @JsonProperty("town")
        private String town;
        @JsonProperty("city")
        private String city;
        @JsonProperty("phone")
        private String phone;
        @JsonProperty("name")
        private String name;
        @JsonProperty("postCode")
        private String postCode;

        public String getArea() {
            return area;
        }

        public void setArea(String area) {
            this.area = area;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getAddressDetail() {
            return addressDetail;
        }

        public void setAddressDetail(String addressDetail) {
            this.addressDetail = addressDetail;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getTown() {
            return town;
        }

        public void setTown(String town) {
            this.town = town;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPostCode() {
            return postCode;
        }

        public void setPostCode(String postCode) {
            this.postCode = postCode;
        }
    }
}
