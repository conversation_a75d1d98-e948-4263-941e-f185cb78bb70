package com.wsgjp.ct.sale.tool.tmc.impl.wxvideo.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class VideoMsg {
    private String toUserName;
    private String encrypt;

    @JsonProperty("ToUserName")
    public String getToUserName() { return toUserName; }
    @JsonProperty("ToUserName")
    public void setToUserName(String value) { this.toUserName = value; }

    @JsonProperty("Encrypt")
    public String getEncrypt() { return encrypt; }
    @JsonProperty("Encrypt")
    public void setEncrypt(String value) { this.encrypt = value; }
}
