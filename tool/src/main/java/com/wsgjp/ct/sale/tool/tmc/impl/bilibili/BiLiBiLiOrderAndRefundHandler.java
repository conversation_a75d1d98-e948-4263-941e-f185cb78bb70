package com.wsgjp.ct.sale.tool.tmc.impl.bilibili;

import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.exception.PlatformInterfaceException;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.bilibili.entity.BiLiBiLiMessage;
import com.wsgjp.ct.sale.tool.tmc.impl.bilibili.entity.BiLiBiLiUtils;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class BiLiBiLiOrderAndRefundHandler extends BiLiBiLiNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(BiLiBiLiOrderAndRefundHandler.class);
    private final EshopTmcUtils eshopTmcUtils;

    public BiLiBiLiOrderAndRefundHandler(EshopTmcUtils eshopTmcUtils) {
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        String tmMessage = invokeMessage.getMessage();
        BiLiBiLiMessage biLiBiLiMessage;
        try {
            biLiBiLiMessage = JsonUtils.toObject(tmMessage, BiLiBiLiMessage.class);
            if (biLiBiLiMessage == null) {
                throw new PlatformInterfaceException("解析消息报文出错!");
            }
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), invokeMessage.getEshopId());
            if (Objects.isNull(eshopInfo)) {
                LOGGER.error("profileId:{},otypeId:{},店铺类型:{},shopId:{},查询店铺信息为空!tmMessage:{}",
                        invokeMessage.getProfileId(), invokeMessage.getEshopId(), shopTypeName, biLiBiLiMessage.getShopId(), tmMessage);
                return BiLiBiLiUtils.buildResponse("管家婆未找到对应店铺!");
            }
            EshopNotifyChange change = new EshopNotifyChange();
            change.setContent(invokeMessage.getMessage());
            change.setTradeOrderId(biLiBiLiMessage.getTradeId());
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(biLiBiLiMessage.getShopId());
            change.setCreateTime(new Date());
            SupportUtil.sendMessage(change, eshopInfo);
            return BiLiBiLiUtils.buildResponse("200");
        } catch (Exception ex) {
            LOGGER.error("profileId:{},otypeId:{},店铺类型:{},tmMessage:{},发送消息到队列失败,失败原因:{}",
                    invokeMessage.getProfileId(), invokeMessage.getEshopId(),
                    shopTypeName, tmMessage, ex.getMessage(), ex);
            String errMsg = ex.getMessage();
            errMsg = StringUtils.isEmpty(errMsg) ? "500" : errMsg;
            return BiLiBiLiUtils.buildResponse(errMsg);
        }
    }

    @Override
    public String serviceName() {
        return "biLiBiLiOrderAndRefundInvoker";
    }
}
