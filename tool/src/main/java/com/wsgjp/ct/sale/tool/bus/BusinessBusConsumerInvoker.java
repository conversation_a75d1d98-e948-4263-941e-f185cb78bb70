//package com.wsgjp.ct.sale.tool.bus;
//
//import com.wsgjp.ct.sale.biz.jarvis.config.platform.AutoAuditSysConfig;
//import com.wsgjp.ct.sale.bus.center.BusDataCenter;
//import com.wsgjp.ct.sale.bus.center.BusStarter;
//import com.wsgjp.ct.sale.bus.entity.BusTaskInfo;
//import com.wsgjp.ct.sale.bus.entity.Task;
//import com.wsgjp.ct.sale.bus.entity.TaskData;
//import com.wsgjp.ct.sale.bus.entity.TaskType;
//import com.wsgjp.ct.sale.bus.utils.BusDataLockerImpl;
//import com.wsgjp.ct.sale.tool.logo.config.ToolCalculateConfig;
//import com.wsgjp.ct.support.mq.SysMqSend;
//import ngp.utils.Md5Utils;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.stereotype.Service;
//
//import java.math.BigInteger;
//import java.util.Collections;
//import java.util.Date;
//import java.util.List;
//
///**
// * 业务型的轮训任务
// */
//@Service
//@ConditionalOnProperty(value = "sale-bus-business.enabled", havingValue = "true")
//public class BusinessBusConsumerInvoker extends AbstractBusConsumerInvoker<BusTaskInfo> {
//
//    private AutoAuditSysConfig autoAuditSysConfig;
//    public BusinessBusConsumerInvoker(ToolCalculateConfig toolConfig, BusStarter busStarter, BusDataCenter busDataCenter,
//                                      AutoAuditSysConfig autoAuditSysConfig, BusDataLockerImpl busDataLocker) {
//        super(toolConfig, busStarter, busDataCenter, busDataLocker);
//        this.autoAuditSysConfig = autoAuditSysConfig;
//    }
//
//    @Override
//    protected void invokeTraining(BusTaskInfo taskInfo) {
//        logger.debug("[{}]开始执行轮训补偿任务", name());
//        Task task = new Task(Md5Utils.md5(taskInfo.getProfileId().toString()), TaskType.CompensateAfterAudit, new Date(),
//                "");
//        try {
//            getBusDataCenter().insertBusData(Collections.singletonList(task));
//        } catch (Exception exception) {
//            logger.error("[sale-bus]将结果初始化到副本失败", exception);
//        }
//        executorTask(new TaskData(task));
//    }
//
//    @Override
//    public String name() {
//        return "sale-bus-business";
//    }
//
//    @Override
//    public List<BusTaskInfo> producer(String profileId) {
//        ToolCalculateConfig toolConfig = getToolConfig();
//        if (!toolConfig.isBusinessProducerEnable()) {
//            logger.info("profileId：{}，业务型的轮训任务未开启", profileId);
//            return Collections.emptyList();
//        }
//        logger.info("[{}]profileId【{}】向MQ发生消息", name(), profileId);
//        BusTaskInfo busTaskInfo = new BusTaskInfo();
//        busTaskInfo.setProfileId(new BigInteger(profileId));
//        SysMqSend.send(Collections.singletonList(busTaskInfo), name());
//        return Collections.emptyList();
//    }
//}
