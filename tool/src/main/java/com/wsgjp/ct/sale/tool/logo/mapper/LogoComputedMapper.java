package com.wsgjp.ct.sale.tool.logo.mapper;

import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.sale.tool.logo.service.computed.advance.entity.LogoEntityByAdvance;
import com.wsgjp.ct.sale.tool.logo.service.computed.deliver.entity.DeliverMarkToLogo;
import com.wsgjp.ct.sale.tool.logo.service.computed.deliver.entity.LogoEntityByDeliver;
import com.wsgjp.ct.sale.tool.logo.service.computed.eshoporder.entity.LogoEntityByOrder;
import com.wsgjp.ct.sale.tool.logo.service.computed.refund.entity.LogoEntityByRefund;
import com.wsgjp.ct.sale.tool.logo.service.computed.warehouse.entity.LogoEntityByTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-03-15 15:32
 */
@Mapper
@Repository
@Component
public interface LogoComputedMapper {

    /*----- 交易单 start -----*/
    //查询交易单热表的所有vchcode
//    List<BigInteger> queryDeliverBillVchcodeAll(@Param("profileId") BigInteger profileId);
//    List<BigInteger> queryDeliverBillVchcodeAllBatch(@Param("profileId")BigInteger profileId,
//                                                     @Param("pageIndex") int pageIndex, @Param("pageCount")int pageCount);
    List<BigInteger> queryWarehouseIdsAllBatch(@Param("profileId") BigInteger profileId,
                                               @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount);

    //    List<BigInteger> queryDeliverBillVchcodeAllBatchDay(@Param("profileId") BigInteger profileId,
//                                                        @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount,
//                                                        @Param("startTime") String startTime,@Param("endTime") String endTime);
    List<BigInteger> queryWarehouseIdsAllBatchDay(@Param("profileId") BigInteger profileId,
                                                  @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount,
                                                  @Param("startTime") String startTime, @Param("endTime") String endTime);

    //    List<LogoEntityByDeliver> queryBillCore(@Param("profileId") BigInteger profileId, @Param("vchcodeList") List<BigInteger> vchcodeList);
    List<LogoEntityByDeliver> queryWarehouseTaskCore(@Param("profileId") BigInteger profileId, @Param("warehouseTaskIds") List<BigInteger> warehouseTaskIds);

    //    List<LogoEntityByDeliver> queryBillDeliver(@Param("profileId") BigInteger profileId, @Param("vchcodeList") List<BigInteger> vchcodeList);
    List<LogoEntityByDeliver> queryWarehouseDeliver(@Param("profileId") BigInteger profileId, @Param("warehouseTaskIds") List<BigInteger> warehouseTaskIds);

    //    List<LogoEntityByDeliver> queryBillDeliverState(@Param("profileId") BigInteger profileId, @Param("vchcodeList") List<BigInteger> vchcodeList);
    List<LogoEntityByDeliver> queryWarehouseDeliverState(@Param("profileId") BigInteger profileId, @Param("warehouseTaskIds") List<BigInteger> warehouseTaskIds);

    List<DeliverMarkToLogo> queryDeliverMark(@Param("profileId") BigInteger profileId,
                                             @Param("vchcodeList") List<BigInteger> vchcodeList,
                                             @Param("markList") List<BaseOrderMarkEnum> markList);

    List<DeliverMarkToLogo> queryWarehouseDeliverMark(@Param("profileId") BigInteger profileId,
                                                      @Param("warehouseTaskIds") List<BigInteger> vchcodeList,
                                                      @Param("markList") List<BaseOrderMarkEnum> markList);
    /*----- 交易单 end -----*/

    /*----- 原始订单 start -----*/
    List<BigInteger> queryOrderVchcodeAll(@Param("profileId") BigInteger profileId);

    List<BigInteger> queryOrderVchcodeAllBatch(@Param("profileId") BigInteger profileId,
                                               @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount);

    List<BigInteger> queryOrderVchcodeAllBatchDay(@Param("profileId") BigInteger profileId,
                                                  @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount,
                                                  @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<LogoEntityByOrder> findExceptionForOrder(@Param("profileId") BigInteger profileId, @Param("vchcodeList") List<BigInteger> list);

    List<BigInteger> queryAdvanceOrderVchcodeAll(@Param("profileId") BigInteger profileId);

    List<BigInteger> queryAdvanceOrderVchcodeAllBatch(@Param("profileId") BigInteger profileId, @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount);

    List<BigInteger> queryAdvanceOrderVchcodeAllBatchDay(@Param("profileId") BigInteger profileId, @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount,
                                                         @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<LogoEntityByAdvance> findExceptionForAdvanceOrder(@Param("profileId") BigInteger profileId, @Param("vchcodeList") List<BigInteger> list);
    /*----- 原始订单 end -----*/

    /* ----- task start ------*/
    List<BigInteger> queryTaskIdAll(@Param("profileId") BigInteger profileId);

    List<LogoEntityByTask> queryTasks(@Param("profileId") BigInteger profileId, @Param("taskIds") List<BigInteger> taskIds);

    List<BigInteger> queryTaskIdAllBatch(@Param("profileId") BigInteger profileId, @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount);

    List<BigInteger> queryTaskIdAllBatchDay(@Param("profileId") BigInteger profileId, @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount,
                                            @Param("startTime") String startTime, @Param("endTime") String endTime);

    /* ----- task end ------*/

    /* ----- 售后单 start ------*/

    List<BigInteger> getAllVchcodeBatchForRefund(@Param("profileId") BigInteger profileId, @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount);

    List<BigInteger> getAllVchcodeBatchDayForRefund(@Param("profileId") BigInteger profileId, @Param("pageIndex") int pageIndex, @Param("pageCount") int pageCount, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<LogoEntityByRefund> getLogoDataForRefund(@Param("profileId") BigInteger profileId, @Param("vchcodeList") List<BigInteger> vchcodeList);

    /*----- 售后单 end -----*/
}
