package com.wsgjp.ct.sale.tool.logo.util;

import com.wsgjp.ct.sale.biz.common.entity.LogoExceptionChange;
import com.wsgjp.ct.sale.biz.common.entity.LogoExceptionState;
import com.wsgjp.ct.sale.sdk.logo.state.LogoExceptionStatusEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoProcessStateEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoSourceTypeEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoUsePhaseEnum;
import ngp.idgenerator.UId;
import rx.functions.Func1;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-02-24 9:58
 */
public class LogoUtil {

    private static int PAGESIZE = 200;

    public static <T, R> List<R> syncExecuteFuncByBatch(Func1<List<T>, R> function, List<T> request) {
        int pageCount = request.size() % PAGESIZE == 0 ? request.size() / PAGESIZE : request.size() / PAGESIZE + 1;
        List<List<T>> groups = new ArrayList<>();
        for (int i = 0; i < pageCount; i++) {
            List<T> temp = request.stream().skip((long) i * PAGESIZE).limit(PAGESIZE).collect(Collectors.toList());
            groups.add(temp);
        }
        List<R> results = new ArrayList<>();
        for (List<T> group : groups) {
            R result = function.call(group);
            results.add(result);
        }
        return results;
    }

    /**
     * 构建新的change表数据
     */
    public static void buildNewChangeList(List<LogoExceptionChange> changeList, BigInteger profileId,
                                          List<BigInteger> allVchcode, LogoSourceTypeEnum sourceType) {
        for (BigInteger vchcode : allVchcode) {
            LogoExceptionChange change = new LogoExceptionChange();
            change.setId(UId.newId());
            change.setVchcode(vchcode);
            change.setProfileId(profileId);
            change.setSourceType(sourceType);
            change.setProcessState(LogoProcessStateEnum.PENDING);
            changeList.add(change);
        }
    }

    public static void buildReQueueList(List<LogoExceptionChange> changeList, BigInteger profileId,
                                          List<BigInteger> allVchcode, LogoSourceTypeEnum sourceType) {
        for (BigInteger vchcode : allVchcode) {
            LogoExceptionChange change = new LogoExceptionChange();
//            change.setId(UId.newId());
            change.setVchcode(vchcode);
            change.setProfileId(profileId);
            change.setSourceType(sourceType);
            change.setProcessState(LogoProcessStateEnum.PENDING);
            changeList.add(change);
        }
    }

    /**
     * 构建新的state表数据
     */
    public static void buildNewStateList(List<LogoExceptionState> list,
                                         BigInteger profileId,
                                         BigInteger vchcode,
                                         LogoExceptionStatusEnum stateType,
                                         LogoSourceTypeEnum sourceType,
                                         LogoUsePhaseEnum usePhase,
                                         Date tradeCreateTime
    ) {
        LogoExceptionState state = new LogoExceptionState();
        state.setId(UId.newId());
        state.setProfileId(profileId);
        state.setVchcode(vchcode);
        state.setSourceType(sourceType);
        state.setStateType(stateType);
        state.setUsePhase(usePhase);
        if(null!=tradeCreateTime){
            state.setTradeCreateTime(tradeCreateTime);
        }
        list.add(state);
    }

}
