package com.wsgjp.ct.sale.tool.tmc.impl.eleme.entity;

public class ElemeRetailTmcBaseRequest {
    private String ticket;
    private String encrypt;
    private String sign;
    private String cmd;
    private String source;
    private ElemeRetailBaseRequestBody bodyEntity;
    private String body;
    private int version;
    private long timestamp;

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public String getEncrypt() {
        return encrypt;
    }

    public void setEncrypt(String encrypt) {
        this.encrypt = encrypt;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public ElemeRetailBaseRequestBody getBodyEntity() {
        return bodyEntity;
    }

    public void setBodyEntity(ElemeRetailBaseRequestBody bodyEntity) {
        this.bodyEntity = bodyEntity;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }
}
