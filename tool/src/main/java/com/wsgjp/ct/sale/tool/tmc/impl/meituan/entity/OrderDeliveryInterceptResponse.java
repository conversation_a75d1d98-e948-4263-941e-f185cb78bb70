package com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderDeliveryInterceptResponse extends MeiTuanBaseResponse {
    @JsonProperty("result_code")
    private int resultCode;
    @JsonProperty("error_list")
    private List<ErrorInfo> errorList;

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public List<ErrorInfo> getErrorList() {
        return errorList;
    }

    public void setErrorList(List<ErrorInfo> errorList) {
        this.errorList = errorList;
    }
}
