package com.wsgjp.ct.sale.tool.tmc.entity;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "deliverRequirement")
public class DeliverRequirement {
    private String appointArrivedTime;
    private String appointDeliveryTime;

    public String getAppointArrivedTime() {
        return appointArrivedTime;
    }

    public void setAppointArrivedTime(String appointArrivedTime) {
        this.appointArrivedTime = appointArrivedTime;
    }

    public String getAppointDeliveryTime() {
        return appointDeliveryTime;
    }

    public void setAppointDeliveryTime(String appointDeliveryTime) {
        this.appointDeliveryTime = appointDeliveryTime;
    }
}
