package com.wsgjp.ct.sale.tool.logo.biz;

import com.wsgjp.ct.sale.biz.common.entity.ComputeQueue;
import com.wsgjp.ct.sale.biz.common.entity.LogoCompute;
import com.wsgjp.ct.sale.biz.common.entity.LogoExceptionChange;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.LogoSysConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.logo.LogoResetStatus;
import com.wsgjp.ct.sale.sdk.logo.entity.LogoExceptionSummary;
import com.wsgjp.ct.sale.tool.logo.config.LogoCalculateConfig;
import org.apache.commons.lang.time.StopWatch;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

public interface LogoCalculateBiz {
    //计算之前
    boolean computeBefore();
    //查询change表
    ComputeQueue getChangeList(BigInteger profileId, long threadId, StopWatch stopWatch, LogoSysConfig sysData);
    //计算徽标
    LogoCompute compute(List<LogoExceptionChange> changeList, BigInteger profileId, long threadId, StopWatch stopWatch);
    //写库
    boolean refreshDb(LogoCompute logoCompute, StopWatch stopWatch,ComputeQueue computeQueue);
    //计算之后
    boolean computeAfter(StopWatch stopWatch, int size);

    //查询sysData数据
//    String querySysDataValue(BigInteger profileId, long threadId, String subName);
//    boolean querySysData(BigInteger profileId, long threadId, String subName);
    //清空summary表
    boolean cleanSummaryData(BigInteger profileId, long threadId, StopWatch stopWatch);
    //初始化summary表
    List<LogoExceptionSummary> initSummaryData(BigInteger profileId, long threadId, StopWatch stopWatch);
    boolean resetSummaryData(BigInteger profileId, long threadId, StopWatch stopWatch);
    //校准state表和summary表-汇总state表，得到汇总数据
//    List<LogoExceptionSummary> queryExceptionState(BigInteger profileId, long threadId, StopWatch stopWatch);
    //校准state表和summary表-更新summary表
//    boolean calibrationAll(BigInteger profileId, long threadId, StopWatch stopWatch,List<LogoExceptionSummary> updateSummaryList);
    void handleChangeAllListBatchTwo(LogoResetStatus resetStatus, long threadId, StopWatch stopWatch);
    //处理热表数据
//    void handleChangeAllListBatch(BigInteger profileId, long threadId, StopWatch stopWatch);
    //处理state数据
    void handleStateAllListBatch(BigInteger profileId, long threadId, StopWatch stopWatch);
    //修改配置
//    boolean computeAllUpdateSysData(BigInteger profileId, long threadId, StopWatch stopWatch);
    boolean computeAllUpdateSysDataBatchTwo(LogoResetStatus resetStatus,LogoSysConfig logoSysConfig, long threadId);

    boolean cleanComputeAllStartTime();


//    void clearExState(BigInteger profileId, long threadId, StopWatch stopWatch);

    LogoResetStatus crateResetInfo(LogoSysConfig logoSysConfig, LogoCalculateConfig logoCalculateConfig);

    void cleanStateByDate(BigInteger profileId, Date endDate);

    void cleanReQueueData(BigInteger profileId);
}
