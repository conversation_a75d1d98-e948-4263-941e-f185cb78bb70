package com.wsgjp.ct.sale.tool.tmc.producer;

import bf.datasource.DataSourceManager;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.internal.tmc.Message;
import com.taobao.api.internal.tmc.TmcClient;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrderSellerFlag;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrderSyncType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderByTmcRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillDeliverUtils;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ConsumerType;
import com.wsgjp.ct.sale.common.utils.PlatformProfileMappingUtils;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.constraint.PlatformConstants;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.factory.taobao.TaobaoConfig;
import com.wsgjp.ct.sale.platform.factory.taobaosupplier.TaobaoSupplierConfig;
import com.wsgjp.ct.sale.tool.common.log.EshopTmcMessageLog;
import com.wsgjp.ct.sale.tool.stock.biz.ProfileCacheBiz;
import com.wsgjp.ct.sale.tool.tmc.entity.TaobaoTmcContent;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.log.service.LogService;
import ngp.idgenerator.UId;
import ngp.loadbalancer.context.RouteContext;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.mq.MqSendResult;
import ngp.mq.SysMq;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(value = "eshoporder-tool-tmc.enabled", havingValue = "true")
public class TaobaoTmcProducer extends BaseMessageProducer {

    private final EshopTmcConfig config;
    private final TaobaoConfig taobaoConfig;
    private final TaobaoSupplierConfig taobaoSupplierConfig;
    private final MonitorService monitorService;
    private final ProfileCacheBiz cacheBiz;
    private static final Logger logger = LoggerFactory.getLogger(TaobaoTmcProducer.class);

    private static final String PLATFORM_TID = "platform_tid";
    private static final String TID = "tid";

    private final EshopSaleOrderService saleOrderService;

    private final SysMq sysMq;


    public TaobaoTmcProducer(EshopTmcConfig config, TaobaoConfig taobaoConfig, TaobaoSupplierConfig taobaoSupplierConfig, MonitorService monitorService, ProfileCacheBiz cacheBiz, EshopSaleOrderService saleOrderService, SysMq sysMq) {
        this.config = config;
        this.taobaoConfig = taobaoConfig;
        this.taobaoSupplierConfig = taobaoSupplierConfig;
        this.monitorService = monitorService;
        this.cacheBiz = cacheBiz;
        this.saleOrderService = saleOrderService;
        this.sysMq = sysMq;
    }

    @Override
    void doStart() {
        if (!config.isEnableTaobaoTmcClient()) {
            return;
        }
        List<ShopType> topTypeList = new ArrayList<>();
        topTypeList.add(ShopType.Tmall);
        topTypeList.add(ShopType.TaoBao);
        TmcClient client = new TmcClient(config.getTaobaoAppKey(), config.getTaobaoAppSecret(), config.getTaobaoMessageGroup());
        TmcClient supplierClient = new TmcClient(taobaoSupplierConfig.getAppKey(), taobaoSupplierConfig.getAppSecret(), config.getTaobaoMessageGroup());
        doConnectByClient(client, topTypeList);
        doConnectByClient(supplierClient, Collections.singletonList(ShopType.TaobaoSupplier));
        Map<String, TaobaoConfig> configMap = taobaoConfig.getExtraConfigMap();
        TaobaoConfig zxyConfig = configMap.get(PlatformConstants.TB_APP_KEY_FOR_ZYX);
        if (zxyConfig != null) {
            TmcClient zyxClient = new TmcClient(zxyConfig.getAppKey(), zxyConfig.getAppSecret(), config.getTaobaoMessageGroup());
            doConnectByClient(zyxClient, topTypeList);
        }

    }

    private void doConnectByClient(TmcClient client, List<ShopType> shopTypeList) {
        try {
            client.connect(config.getTaobaoTmcUrl());
            client.setThreadCount(config.getThreadCount());
        } catch (Exception ex) {
            client.close();
            logger.error("淘宝tmc连接失败:{}", ex.getMessage(), ex);
        }
        client.setMessageHandler((message, messageStatus) -> {
            String nick = message.getUserNick();
            logTmcMessage(message);
            for (ShopType shopType : shopTypeList) {
                List<EshopRegisterNotify> tbList = PlatformProfileMappingUtils.getRegisterNotifyList(nick, shopType);
                if (CollectionUtils.isNotEmpty(tbList)) {
                    for (EshopRegisterNotify notify : tbList) {
                        executeMessage(message, notify, shopType);
                    }
                }
            }
        });
    }

    public void executeMessage(Message message, EshopRegisterNotify notify, ShopType shopType) {
        if (message == null) {
            return;
        }
        Date execTime = DateUtils.getDate();
        String content = message.getContent();
        String topic = message.getTopic();
        try {
            BigInteger profileId = notify.getProfileId();
            if (profileId == null) {
                return;
            }
            RouteContext context = cacheBiz.getRouteContext(profileId);
            if (context == null) {
                logger.error("帐套路由信息为空：profileId:{}", profileId);
                return;
            }
            RouteThreadLocal.setRoute(context);
            DataSourceManager.setName(context.getServerId());
            if (!BillDeliverUtils.isOpenEshopFunc()) {
                logger.info(String.format("账套【%s】【%s】TMC生产任务已关闭,无网店资产", profileId, OrderSyncType.AUTO_INCREASE.getName()));
                return;
            }
            TmcOrderMessage tmcOrderMessage = buildMessage(notify, content);
            TMCType tmcType = buildTMCType(message);
            saveNotifyChange(tmcOrderMessage, content, tmcType);
            notifyMemoChange(tmcOrderMessage, content, tmcType);
            List<TmcOrderMessage> messageList = Collections.singletonList(tmcOrderMessage);
            List<MqSendResult<TmcOrderMessage>> sendResults = sysMq.sendDelayTime(messageList,
                    config.getTmcMqTopic(),
                    taobaoConfig.getTmcSendDelayTime());
            SupportUtil.insertMqConsumeLogByMqResults(sendResults, tmcOrderMessage, tmcType);
            doWriteTmcMqLog(sendResults, shopType, message, tmcOrderMessage);
            saveTaoBaoRefundMsg(content, topic, shopType, notify);
            saveTaoBaoRefundToPlEshopNotifyChange(content, topic, notify);
            long useTime = DateUtils.getDate().getTime() - execTime.getTime();
            monitorService.recordTP(MonitorTypeEnum.PL_BS_ORDER_DOWNLOAD_TMC_PRODUCE_TP.getTopic(), shopType, "topic", topic, useTime);
        } catch (Exception ex) {
            logger.error("淘宝/天猫TMC消息处理失败,失败原因:{}", ex.getMessage(), ex);
        } finally {
            DataSourceManager.reset();
            RouteThreadLocal.remove();
        }
    }

    private void saveNotifyChange(TmcOrderMessage message, String content, TMCType tmcType) {
        EshopNotifyChange notifyChange = buildNotifyChange(message, content);
        notifyChange.setType(tmcType);
        SupportUtil.saveNotifyChange(notifyChange);
    }

    private void notifyMemoChange(TmcOrderMessage message, String content, TMCType tmcType) {
        if (!tmcType.equals(TMCType.DIRECT_UPDATE_MEMO)) {
           return;
        }
        try {
            UpdateOrderByTmcRequest updateRequest = buildUpdateOrderByTmcRequest(message, content);
            saleOrderService.updateOrderByTmcRequest(updateRequest);
        } catch (Exception e) {
            logger.error("tmc直接修改失败:{},tmc原始报文:{}",e.getMessage(), JsonUtils.toJson(message), e);
        }
    }

    private TMCType buildTMCType(Message message) {
        String topic = message.getTopic();
        List<String> memoUpdateTopics = convertConfigToList(taobaoConfig.getMemoUpdateTopics());
        if (CollectionUtils.isNotEmpty(memoUpdateTopics) && memoUpdateTopics.contains(topic)) {
            return TMCType.DIRECT_UPDATE_MEMO;
        }
        if (INVOICE_APPLY_TOPIC.equals(topic)) {
            return TMCType.Invoice_Apply;
        }
        return TMCType.Order;
    }

    private UpdateOrderByTmcRequest buildUpdateOrderByTmcRequest(TmcOrderMessage message, String content) {
        TaobaoTmcContent tbContent = JsonUtils.toObject(content, TaobaoTmcContent.class);
        UpdateOrderByTmcRequest req = new UpdateOrderByTmcRequest();
        req.setTradeOrderId(message.getTradeId());
        req.setShopId(message.getEshopId());
        req.setSellerMemo(tbContent.getSeller_memo());
        req.setSellerFlag(buildSellerFlag(Integer.parseInt(tbContent.getSeller_flag())));
        req.setConsumerType(ConsumerType.DIRECT_UPDATE_MEMO);
        return req;
    }

    private OrderSellerFlag buildSellerFlag(Integer sellerFlag) {
        if (sellerFlag == null) {
            return OrderSellerFlag.WHITE;
        }
        OrderSellerFlag orderFlag;
        switch (sellerFlag) {

            case 1:
                orderFlag = OrderSellerFlag.RED;
                break;
            case 2:
                orderFlag = OrderSellerFlag.YELLOW;
                break;
            case 3:
                orderFlag = OrderSellerFlag.GREEN;
                break;
            case 4:
                orderFlag = OrderSellerFlag.BLUE;
                break;
            case 5:
                orderFlag = OrderSellerFlag.VIOLET;
                break;
            case 6:
                orderFlag = OrderSellerFlag.ORANGE;
                break;
            case 7:
                orderFlag = OrderSellerFlag.PURPLE;
                break;
            case 8:
                orderFlag = OrderSellerFlag.CHING;
                break;
            case 9:
                orderFlag = OrderSellerFlag.TENDER_GREEN;
                break;
            case 10:
                orderFlag = OrderSellerFlag.ROSE_RED;
                break;
            case 0:
            default:
                orderFlag = OrderSellerFlag.WHITE;
                break;
        }
        return orderFlag;
    }


    private EshopNotifyChange buildNotifyChange(TmcOrderMessage message, String content) {
        EshopNotifyChange tmcMsg = new EshopNotifyChange();
        tmcMsg.setId(UId.newId());
        tmcMsg.setTradeOrderId(message.getTradeId());
        tmcMsg.setTradeStatus("");
        tmcMsg.setContent(content);
        tmcMsg.setEshopId(message.getEshopId());
        tmcMsg.setProfileId(message.getProfileId());
        return tmcMsg;
    }

    private List<String> convertConfigToList(String topicsConfig) {
        if (StringUtils.isEmpty(topicsConfig)) {
            return new ArrayList<>();
        }
        return Arrays.asList(topicsConfig.split(","));
    }


    private void logTmcMessage(Message message) {
        try {
            String accountList = config.getLogTmcMsgAccountList();
            String[] split = accountList.split(",");
            for (String account : split) {
                if (account.equals(message.getUserNick())) {
                    logger.warn("淘宝TMC开始消费,消息内容:{}", JsonUtils.toJson(message));
                    break;
                }
            }
        } catch (Exception ex) {
            logger.error("淘宝TMC消息日志记录失败,失败原因:{}", ex.getMessage(), ex);
        }
    }

    private void doWriteTmcMqLog(List<MqSendResult<TmcOrderMessage>> sendResults, ShopType shopType, Message tbMessage, TmcOrderMessage message) {
        if (CollectionUtils.isEmpty(sendResults)) {
            return;
        }
        try {
            if (config.getLogTmcMsgAccountList().contains(tbMessage.getUserNick())) {
                logger.warn("淘宝TMC消息开始写入日志");
            }
            EshopTmcMessageLog tmcLog = buildTmcLog(shopType, tbMessage, message);
            if(CollectionUtils.isNotEmpty(sendResults)){
                String messageId = sendResults.get(0).getMessageId();
                tmcLog.setMsgId(messageId);
            }
            LogService.addNow(tmcLog);
        } catch (Exception ex) {
            logger.error("记录淘宝tmc日志是报错：{}", ex.getMessage(), ex);
        }
    }

    private static EshopTmcMessageLog buildTmcLog(ShopType shopType, Message tbMessage, TmcOrderMessage message) {
        EshopTmcMessageLog tmcLog = new EshopTmcMessageLog();
        tmcLog.setId(UId.newId());
        tmcLog.setProfileId(message.getProfileId());
        tmcLog.setEshopId(message.getEshopId());
        tmcLog.setTmcInfo(JsonUtils.toJson(tbMessage));
        tmcLog.setReturnInfo("");
        tmcLog.setStatus(0);
        tmcLog.setErrorMsg("");
        tmcLog.setEtypeId(BigInteger.ZERO);
        tmcLog.setTmcId(message.getTradeId());
        tmcLog.setEshopType(shopType.getCode());
        tmcLog.setMethod(tbMessage.getTopic());
        return tmcLog;
    }

    @Override
    String buildTradeId(String message) {
        try {
            Map<String, Object> jsonMap = JSONObject.parseObject(message);
            if (jsonMap.containsKey(TID)) {
                return jsonMap.get(TID).toString();
            } else if (jsonMap.containsKey(PLATFORM_TID)) {
                return jsonMap.get(PLATFORM_TID).toString();
            }
        } catch (Exception ex) {
            logger.error("解析淘宝TMC消息失败，没有包含tid：{}", message);
        }
        return "";
    }

    @Override
    String buildRefundId(String message) {
        try {
            Map<String, Object> jsonMap = JSONObject.parseObject(message);
            if (jsonMap.containsKey("refund_id")) {
                return jsonMap.get("refund_id").toString();
            }
        } catch (Exception ex) {
            logger.error("解析淘宝TMC消息失败，没有包含refund_id：{}", message);
        }
        return "";
    }
}
