package com.wsgjp.ct.sale.tool.tmc.impl.taobaomaicai.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class TaobaomaicaiOrderMessage implements Serializable {
    private String platformMerchantCode;
    private String bizOrderId;
    private String supplierNo;
    private String status;

    public String getPlatformMerchantCode() {
        return platformMerchantCode;
    }

    @JsonProperty("platformMerchantCode")
    public void setPlatformMerchantCode(String platformMerchantCode) {
        this.platformMerchantCode = platformMerchantCode;
    }

    public String getBizOrderId() {
        return bizOrderId;
    }

    @JsonProperty("bizOrderId")
    public void setBizOrderId(String bizOrderId) {
        this.bizOrderId = bizOrderId;
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    @JsonProperty("supplierNo")
    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

    public String getStatus() {
        return status;
    }

    @JsonProperty("status")
    public void setStatus(String status) {
        this.status = status;
    }
}
