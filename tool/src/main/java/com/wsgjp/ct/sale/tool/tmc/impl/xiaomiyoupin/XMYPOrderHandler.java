package com.wsgjp.ct.sale.tool.tmc.impl.xiaomiyoupin;

import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.sale.tool.tmc.utils.AesUtils;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import xiaomiyoupinsdk.entity.OrderInfoNew;

import java.math.BigInteger;
import java.text.ParseException;
import java.util.Date;
import java.util.Objects;

@Component
public class XMYPOrderHandler extends XMYPNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(XMYPOrderHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    private final EshopTmcUtils eshopTmcUtils;
    public XMYPOrderHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper, EshopTmcUtils eshopTmcUtils) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        XMYPTmcResponse apiOrder;
        try {
            apiOrder = JsonUtils.toObject(tmMessage, XMYPTmcResponse.class);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成OrderRequest实体出错，错误信息：{}",shopTypeName,e.getMessage());
            return "{\n" +
                    "    \"code\":210, \n" +
                    "    \"message\": \"服务异常\"\n" +
                    "}";
        }
        BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
        if (BigInteger.ZERO.equals(invokeMessage.getEshopId())){
            // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
            EshopRegisterNotify notify = SupportUtil.buildNotify(apiOrder.partnerId, 85);
            if (notify == null || notify.getId()==null|| notify.getId().equals(BigInteger.ZERO)){
                return "{\n" +
                        "    \"code\":210, \n" +
                        "    \"message\": \"服务异常\"\n" +
                        "}";
            }
            eshopId=notify.getId();
        }else {
            eshopId = invokeMessage.getEshopId();
        }
        EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
        if(Objects.isNull(eshopInfo)){
            LOGGER.info("profileId:{},eshopId:{},店铺类型:{},查询店铺信息为空!",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName);
            return "{\n" +
                    "    \"code\":210, \n" +
                    "    \"message\": \"服务异常\"\n" +
                    "}";
        }
        if(!checkSign(apiOrder.partnerId,apiOrder.data,apiOrder.timestamp,eshopInfo.getAppSecret(),apiOrder.sign)){
            return "{\n" +
                    "    \"code\":110, \n" +
                    "    \"message\": \"签名校验失败\"\n" +
                    "}";
        }
        LOGGER.info("profileId:{},eshopId:{},店铺类型：{}",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName);
        // 解密
        String result = decryptOrder(apiOrder.data,eshopInfo.getToken());
        if (result == null){
            LOGGER.error("小米有品数据解密失败");
            return "{\n" +
                    "    \"code\":120, \n" +
                    "    \"message\": \"AES解密失败\"\n" +
                    "}";
        }
        // 解析JSON
        OrderInfoNew orderInfo = null;
        try {
            orderInfo = JsonUtils.toObject(result, OrderInfoNew.class);
        } catch (Exception e) {
            LOGGER.error("TMC小米有品json转换实体失败："+ e.getMessage());
            return "{\n" +
                    "    \"code\":130, \n" +
                    "    \"message\": \"JSON解析错误\"\n" +
                    "}";
        }

        try{
            saveTmcOrderMsg(eshopInfo,orderInfo,apiOrder);
        }catch (Exception ex){
            LOGGER.error("{}保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,invokeMessage.getProfileId(),invokeMessage.getEshopId(),tmMessage,ex.getMessage(),ex);
            return "{\n" +
                    "    \"code\":210, \n" +
                    "    \"message\": \"服务异常\"\n" +
                    "}";
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(apiOrder.partnerId);
        eshopNotifyChange.setTradeOrderId(String.valueOf(orderInfo.getOrderId()));
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(JsonUtils.toJson(orderInfo));
        SupportUtil.doOrderNotify(apiOrder.partnerId,eshopNotifyChange,eshopInfo.getEshopType().getCode());
        return "{\n" +
                "    \"code\":0, \n" +
                "    \"message\": \"ok\"\n" +
                "}";
    }

    private String decryptOrder(String data, String aesKey) {
        // AES解密
        String result = null;
        try {
            result = AesUtils.decrypt(aesKey, data);
            LOGGER.info("data:{}", result);
        } catch (Exception e) {
            LOGGER.error("TMC小米有品数据解密失败：",e.getMessage());
            return null;
        }
        return result;
    }

    public int saveTmcOrderMsg(EshopInfo eshopInfo, OrderInfoNew orderInfo, XMYPTmcResponse orderRequest){
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(eshopInfo.getProfileId(),eshopInfo.getOtypeId(),String.valueOf(orderInfo.getOrderId()));
        if (Objects.isNull(orderMsgEntity)){
            orderMsgEntity = buildEshopTmcOrderMsgEntity(eshopInfo,orderInfo,orderRequest);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        }else {
            //orderMsgEntity.setStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(JsonUtils.toJson(orderRequest));
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(EshopInfo eshopInfo, OrderInfoNew orderInfo, XMYPTmcResponse orderRequest) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopInfo.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopInfo.getOtypeId());
        tmcOrderMsgEntity.setShopType(eshopInfo.getEshopType());
        tmcOrderMsgEntity.setTradeOrderId(String.valueOf(orderInfo.getOrderId()));
        tmcOrderMsgEntity.setMessage(JsonUtils.toJson(orderRequest));
        tmcOrderMsgEntity.setMsgStatus(0);

        //默认值已付款
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        try {
            Date date = DateUtils.parseDate(orderRequest.timestamp, "yyyy-mm-dd HH:mm:ss");
            tmcOrderMsgEntity.setCreateTime(new Date(orderInfo.getCtime() * 1000));
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (ParseException e) {

        }
        return tmcOrderMsgEntity;
    }

//    public <T> T convertToJavaBean(String xml, Class<T> t) throws Exception {
//        T obj = null;
//        JAXBContext context = JAXBContext.newInstance(t);
//        Unmarshaller unmarshaller = context.createUnmarshaller();
//        obj = (T) unmarshaller.unmarshal(new StringReader(xml));
//        return obj;
//    }

    @Override
    public String serviceName() {
        return "xmypInvoker";
    }

    // 校验sign
    public static Boolean checkSign(String partnerId, String data, String timestamp, String key, String sign) {

        String targetString = String.format("data=%s&partnerId=%s&timestamp=%s%s", data, partnerId, timestamp, key);
        String calSign = DigestUtils.md5DigestAsHex(targetString.getBytes());

        LOGGER.info("小米有品calSign: {} ", calSign);
        LOGGER.info("小米有品sign: {} ", sign);
        return calSign.equals(sign);
    }
}
