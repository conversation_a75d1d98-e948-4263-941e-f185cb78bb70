package com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity;

import bf.datasource.typehandler.CodeEnum;

public enum MeiTuanTopicEnum implements CodeEnum {
    ALL_REFUND(1001,"全额退款信息"),
    PART_REFUND(1002,"部分退款信息"),
    REFUND_FAIL_OR_SUCCESS(1003,"退款状态（成功/失败）信息");
    private final int code;
    private final String desc;

    MeiTuanTopicEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return desc;
    }
}
