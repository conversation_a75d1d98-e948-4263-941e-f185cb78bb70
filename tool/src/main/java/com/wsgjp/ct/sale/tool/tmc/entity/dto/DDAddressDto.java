package com.wsgjp.ct.sale.tool.tmc.entity.dto;

public class DDAddressDto {
    /**
     *变更的收货人姓名
     */
    private String  postReceiver;
    /**
     * 收货人手机
     */
    private String postTel;
    /**
     * 省
     */
    private String Province;
    /**
     * 市
     */
    private String City;
    /**
     * 区
     */
    private String district;
    /**
     * 镇
     */
    private String town;
    /**
     * 街道
     */
    private String street;
    /**
     * 详细地址
     */
    private String AddressDetail;

    private boolean needPlatformDecrypt;

    public boolean isNeedPlatformDecrypt() {
        return needPlatformDecrypt;
    }

    public void setNeedPlatformDecrypt(boolean needPlatformDecrypt) {
        this.needPlatformDecrypt = needPlatformDecrypt;
    }

    public String getPostReceiver() {
        return postReceiver;
    }

    public void setPostReceiver(String postReceiver) {
        this.postReceiver = postReceiver;
    }

    public String getPostTel() {
        return postTel;
    }

    public void setPostTel(String postTel) {
        this.postTel = postTel;
    }

    public String getProvince() {
        return Province;
    }

    public void setProvince(String province) {
        Province = province;
    }

    public String getCity() {
        return City;
    }

    public void setCity(String city) {
        City = city;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getAddressDetail() {
        return AddressDetail;
    }

    public void setAddressDetail(String addressDetail) {
        AddressDetail = addressDetail;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }
}
