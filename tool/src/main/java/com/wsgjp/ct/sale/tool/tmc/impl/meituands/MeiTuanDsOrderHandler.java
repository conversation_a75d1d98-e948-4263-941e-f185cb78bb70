package com.wsgjp.ct.sale.tool.tmc.impl.meituands;

import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.meituands.entity.MeiTuanDsOrderData;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.util.Date;
import java.util.Objects;

@Component
/**
 * <AUTHOR>
 **/
public class MeiTuanDsOrderHandler extends MeiTuanDsNotifyBase{
    private static final Logger LOGGER = LoggerFactory.getLogger(MeiTuanDsOrderHandler.class);
    private final EshopTmcUtils eshopTmcUtils;

    public MeiTuanDsOrderHandler(EshopTmcUtils eshopTmcUtils) {
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        String tmcMessage = invokeMessage.getMessage();
        MeiTuanDsOrderData data;
        try {
            data = JsonUtils.toObject(tmcMessage,MeiTuanDsOrderData.class);
        } catch (Exception e) {
            LOGGER.error(String.format("%stmc消息转换成美团电商订单实体失败",shopTypeName),e);
            return buildResponse(e.getMessage());
        }
        try {
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), invokeMessage.getEshopId());
            if (Objects.isNull(eshopInfo)) {
                LOGGER.error("profileId:{},店铺类型:{},poi_id:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, data.getPoiId(), tmcMessage);
                return buildResponse("管家婆未找到对应店铺!");
            }
            EshopNotifyChange change = new EshopNotifyChange();
            change.setContent(invokeMessage.getMessage());
            change.setTradeOrderId(String.valueOf(data.getOrderViewId()));
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(data.getPoiId());
            change.setCreateTime(new Date());
            SupportUtil.sendMessage(change, eshopInfo);
            return buildResponse("ok");
        } catch (Exception e) {
            String errMsg = e.getMessage();
            errMsg = StringUtils.isEmpty(errMsg) ? "tmc订单消息构建异常" : errMsg;
            LOGGER.error(String.format("账套id:%s,网店id:%s,店铺类型：%s,tmcMessage:%s",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName,tmcMessage),e);
            return buildResponse(errMsg);
        }
    }

    @Override
    public String serviceName() {
        return "orderMessage";
    }
}
