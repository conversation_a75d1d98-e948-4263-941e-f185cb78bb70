package com.wsgjp.ct.sale.tool.tmc.controller;

import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.QmMessageType;
import com.wsgjp.ct.sale.tool.tmc.entity.qimen.QmRestParam;
import com.wsgjp.ct.sale.tool.tmc.impl.qimen.QiMenService;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.starter.web.annotation.NotWrapper;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.math.BigInteger;


/**
 * <AUTHOR> 2024/7/9 18:39
 */
@Api(tags = "接收奇门转发消息接口")
@RequestMapping("/qm/rest")
@RestController
public class QiMenController {

    private static final Logger logger = LoggerFactory.getLogger(QiMenController.class);

    private final EshopTmcUtils eshopTmcUtils;

    private final QiMenService qiMenService;


    public QiMenController(EshopTmcUtils eshopTmcUtils, QiMenService qiMenService) {
        this.eshopTmcUtils = eshopTmcUtils;
        this.qiMenService = qiMenService;
    }

    @PostMapping("/invoker")
    @ApiOperation(value = "奇门消息接收接口", notes = "奇门消息接收接口")
    @NotWrapper
    public GeneralResult<Boolean> invoker(@RequestBody QmRestParam param) {
        GeneralResult<Boolean> result = new GeneralResult<>();
        result.setTraceId(UId.newId().toString());
        logger.error("账套ID:{},店铺ID:{},奇门接收消息 param:{}.", param.getProfileId(), param.getOtypeId(), JsonUtils.toJson(param));
        try {
            checkBaseParam(param, result);
            EshopInfo eshopInfo = queryEshopInfo(param, result);
            QmMessageType qmType = QmMessageType.getByName(param.getQmType());
            if (qmType == null) {
                result.setData(false);
                result.setMessage("qmType不合法，无法解析");
                return result;
            }
            if (Boolean.FALSE.equals(result.getData())) {
                return result;
            }
            qiMenService.doSaveTmcData(param, qmType, eshopInfo);
            result.setData(true);
        } catch (Exception ex) {
            logger.error("账套ID:{},店铺ID:{},奇门消息处理出错，param:{},错误信息：{}."
                    , param.getProfileId(), param.getOtypeId(), JsonUtils.toJson(param), ex.getMessage(), ex);
            result.setData(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

    private void checkBaseParam(QmRestParam param, GeneralResult<Boolean> result) {
        if (param.getProfileId() == null || param.getOtypeId() == null) {
            result.setData(false);
            result.setMessage("profileId or otypeId is null");
            logger.error("奇门接收消息：{} profileId or otypeId is null", JsonUtils.toJson(param));
            return;
        }
        if (StringUtils.isEmpty(param.getQmType())) {
            result.setData(false);
            result.setMessage("qmType is null");
            logger.error("奇门接收消息：{} qmType is null", JsonUtils.toJson(param));
            return;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        if (!profileId.equals(param.getProfileId())) {
            result.setData(false);
            String msg = String.format("传入的账套id跟当前用户不匹配:%s:%s", profileId, param.getProfileId());
            result.setMessage(msg);
            return;
        }
        if (StringUtils.isEmpty(param.getQmData())) {
            result.setData(false);
            String msg = String.format("账套Id%s,接收奇门转发的数据为空:%s", profileId, JsonUtils.toJson(param));
            result.setMessage(msg);
            logger.error(msg);
        }
    }

    private EshopInfo queryEshopInfo(QmRestParam param, GeneralResult<Boolean> result) {
        BigInteger profileId = CurrentUser.getProfileId();
        EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(profileId, param.getOtypeId());
        if (eshopInfo == null) {
            result.setData(false);
            String msg = String.format("传入的账套id跟当前用户不匹配:%s:%s", profileId, param.getProfileId());
            result.setMessage(msg);
            logger.error("奇门接收消息：{} 账套信息不存在", JsonUtils.toJson(param));
        }
        return eshopInfo;
    }
}
