package com.wsgjp.ct.sale.tool.logo.biz.impl;

import com.wsgjp.ct.sale.biz.common.mapper.MarkCalculateBizMapper;
import com.wsgjp.ct.sale.biz.jarvis.service.mark.MarkService;
import com.wsgjp.ct.sale.tool.logo.biz.MarkCalculateBiz;
import com.wsgjp.ct.sale.tool.logo.config.MarkCalculateConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigInteger;

/**
 * <AUTHOR> csh
 * @create 2023-06-16 13:39
 */
@Service
public class MarkCalculateBizImpl implements MarkCalculateBiz {

    private final static Logger logger = LoggerFactory.getLogger(MarkCalculateBizImpl.class);
    private MarkCalculateConfig markConfig;
    private MarkCalculateBizMapper markMapper;
    private MarkService markService;

    public MarkCalculateBizImpl(MarkCalculateConfig markConfig,
                                MarkCalculateBizMapper markMapper,
                                MarkService markService) {
        this.markConfig = markConfig;
        this.markMapper = markMapper;
        this.markService = markService;
    }

    @Override
    public void compute(BigInteger profileId, long threadId) {
//        try {
//            List<BigInteger> vchcodeList = new ArrayList<>();
//
//            int pageIndex = 0;
//            int pageCount = markConfig.getQueryCount();
//            int loopCount = 0;
//            int maxLoopCount = markConfig.getMaxLoopCount();
//            do{
//                //判断最大循环次数，防止死循环
//                if (loopCount>=maxLoopCount) {
//                    break;
//                }
//                loopCount++;
//                //清空结果集合
//                vchcodeList.clear();
//                //查询发货超时的订单
//                vchcodeList.addAll(markMapper.querySendOverTime(profileId,pageIndex,pageCount));
//                if (CollectionUtils.isEmpty(vchcodeList)) {
//                    break;
//                }
//                //打标
//                markService.addMarkBatch(BillMarkParams.buildParamsByDeliver(profileId, ErrorFace.看我看我别忘了改(vchcodeList), Arrays.asList(BaseOrderMarkEnum.SEND_OVERTIME)));
//                //更新Limit条件
//                pageIndex += pageCount;
//            }while (vchcodeList.size() > 0);
//
//        } catch (Exception ex) {
//            String errMsg = String.format("[标记工具-计算]profileId【%s】Thread【%s】,计算报错：%s | %s",
//                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
//            logger.error(errMsg, ex);
//        }
    }
}
