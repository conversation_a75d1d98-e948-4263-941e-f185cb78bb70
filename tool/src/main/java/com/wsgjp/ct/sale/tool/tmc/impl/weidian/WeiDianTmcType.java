package com.wsgjp.ct.sale.tool.tmc.impl.weidian;

import com.wsgjp.ct.sale.common.enums.TMCType;
import ngp.utils.ArrayUtils;
import ngp.utils.StringUtils;

public enum WeiDianTmcType {
    /**
     *待付款（直接到账+担保交易）
     **/
    weiDian_order_non_payment("weidian.order.non_payment",TMCType.Order),

    /**
     *已关闭（订单非退款时最终态）
     **/
    weiDian_order_close("weidian.order.close",TMCType.Order),

    /**
     *已付款（直接到账）/已付款待发货（担保交易）
     **/
    weiDian_order_already_payment("weidian.order.already_payment",TMCType.Order),

    /**
     *已发货（担保交易）/已完成（直接到账+货到付款）
     **/
    weiDian_order_delivered("weidian.order.delivered",TMCType.Order),

    /**
     *(未上线)卖家提交退款（担保交易）
     **/
    weiDian_order_seller_submit_refund("weidian.order.seller_submit_refund",TMCType.Order),

    /**
     *(未上线)买家申请退款（直接到账+担保交易）
     **/
    weiDian_order_buyers_apply_refund("weidian.order.buyers_apply_refund",TMCType.Order),

    /**
     *(未上线)卖家拒绝退款（直接到账+担保交易）
     **/
    weiDian_order_sellers_refused_refund("weidian.order.sellers_refused_refund",TMCType.Order),

    /**
     *退款成功（直接到账+担保交易）（退款发生后订单终态）
     **/
    weiDian_order_refund_success("weidian.order.refund_success", TMCType.Order),

    /**
     *交易成功（直接到账+担保交易）
     **/
    weiDian_order_finished("weidian.order.finished",TMCType.Order),

    /**
     *部分发货(首次)
     **/
    weiDian_order_split_deliver("weidian.order.split_deliver",TMCType.Order),

    /**
     *修改订单收货地址
     **/
    weiDian_order_modify_address("weidian.order.modify_address",TMCType.CHANGE_ADDRESS),
    /**
     * 订单有更新
     **/
    weiDian_order_change("weidian.order.change",TMCType.Order),
    /**
     * 申请退款
     **/
    weiDian_order_refund_apply("weidian.order.refund_apply",TMCType.RefundOrder),
    /**
     * 退款达成(双方接受退款)
     **/
    weiDian_order_refund_accept("weidian.order.refund_accept",TMCType.RefundOrder),
    /**
     * 退款关闭
     **/
    weiDian_order_refund_close("weidian.order.refund_close",TMCType.RefundOrder),
    /**
     * 退款成功
     **/
    weiDian_order_refund_suc("weidian.order.refund_suc",TMCType.RefundOrder),
    /**
     * 退款协商
     **/
    weiDian_order_refund_negotiate("weidian.order.refund_negotiat",TMCType.RefundOrder),
    /**
     * 微店退款中
     **/
    weiDian_order_refund_ByWeiDian("weidian.order.refund_byweidian",TMCType.RefundOrder);


    private final String msgType;
    /**
     * 对应ngp TMC业务类型
     */
    private final TMCType tmcType;

    WeiDianTmcType(String msgType, TMCType tmcType) {
        this.msgType = msgType;
        this.tmcType = tmcType;
    }

    public String getMsgType() {
        return msgType;
    }

    public TMCType getTmcType() {
        return tmcType;
    }

    public static WeiDianTmcType getPddTmcMsgTypeByMsgTypeName(String flag) {
        WeiDianTmcType[] msgTypes = WeiDianTmcType.values();
        if (ArrayUtils.isEmpty(msgTypes) || StringUtils.isEmpty(flag)) {
            return null;
        }
        for (WeiDianTmcType msgType : msgTypes) {
            if(msgType.getMsgType().equals(flag)){
                return  msgType;
            }
        }
        return null;
    }
}
