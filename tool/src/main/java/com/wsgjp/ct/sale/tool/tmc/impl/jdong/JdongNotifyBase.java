package com.wsgjp.ct.sale.tool.tmc.impl.jdong;


import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.JdongTmcResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import ngp.utils.JsonUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 */
public abstract class JdongNotifyBase implements MessageHandler {
    protected final List<Integer> shopTypes = Arrays.asList(ShopType.JDong.getCode(),ShopType.JdongVC.getCode(),ShopType.JdongZGB.getCode(),ShopType.JdongFBP.getCode());
    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(true);
        return result;
    }

    protected String buildResponse(String code, String msg) {
        return JsonUtils.toJson(new JdongTmcResponse(code, msg));
    }
}
