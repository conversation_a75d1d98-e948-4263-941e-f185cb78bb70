package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.utils.DDUtil;
import ngp.utils.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigInteger;

public abstract class DDNotifyBase implements MessageHandler {

    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        String ddMessage = getBody(request);
        DDUtil.setProfileid(BigInteger.valueOf(Integer.valueOf(request.getHeader("profileid")).intValue()));
        boolean resultCheck = DDUtil.checkSign(ddMessage, request.getHeader("timestamp"), request.getHeader("sign"),eshopTmcConfig.getDoudianAppKey(),eshopTmcConfig.getDoudianAppSecret());
        String shopType = request.getHeader("shopType");
        if (!StringUtils.isEmpty(shopType) && ShopType.DouDianInstantShopping.getCode() == Integer.parseInt(shopType)){
            resultCheck = DDUtil.checkSign(ddMessage, request.getHeader("timestamp"), request.getHeader("sign"),eshopTmcConfig.getDoudianJsAppKey(),eshopTmcConfig.getDoudianJsAppSecret());
        }
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(resultCheck);
        if(!resultCheck){
            result.setCode(10001);
            result.setBody("验签失败");
        }else{
            result.setCode(0);
            result.setBody("验签成功");
        }

        return result;
    }



    private String getBody(HttpServletRequest request) {
        StringBuilder wholeStr = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()))){
            String str = "";
            while ((str = reader.readLine()) != null) {
                wholeStr.append(str.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return wholeStr.toString();
    }
}
