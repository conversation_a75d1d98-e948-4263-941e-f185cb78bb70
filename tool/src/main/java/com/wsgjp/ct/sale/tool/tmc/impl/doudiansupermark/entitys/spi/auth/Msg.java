package com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark.entitys.spi.auth;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Msg {
    private Integer actionType;
    private Long appID;
    private String authorityID;
    private String code;
    private String codeGenerateTime;

    @JsonProperty("action_type")
    public Integer getActionType() { return actionType; }
    @JsonProperty("action_type")
    public void setActionType(Integer value) { this.actionType = value; }

    @JsonProperty("app_id")
    public Long getAppID() { return appID; }
    @JsonProperty("app_id")
    public void setAppID(Long value) { this.appID = value; }

    @JsonProperty("authority_id")
    public String getAuthorityID() { return authorityID; }
    @JsonProperty("authority_id")
    public void setAuthorityID(String value) { this.authorityID = value; }

    @JsonProperty("code")
    public String getCode() { return code; }
    @JsonProperty("code")
    public void setCode(String value) { this.code = value; }

    @JsonProperty("code_generate_time")
    public String getCodeGenerateTime() { return codeGenerateTime; }
    @JsonProperty("code_generate_time")
    public void setCodeGenerateTime(String value) { this.codeGenerateTime = value; }
}