package com.wsgjp.ct.sale.tool.tmc.impl.yuanqi;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.DownloadType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.purchase.PurchaseOrderDownloadResult;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopSaleOrderDownloadTask;
import com.wsgjp.ct.sale.biz.eshoporder.impl.purchase.BaseStandardOrderDownloader;
import com.wsgjp.ct.sale.biz.eshoporder.impl.purchase.DownloadPurchaseOrder;
import com.wsgjp.ct.sale.biz.eshoporder.impl.purchase.DownloadSaleOrder;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.constant.SyncOrderConst;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.syncorder.common.OrderSyncUtil;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class YQOrderDownloadManager {

    private static final Logger logger = LoggerFactory.getLogger(YQOrderDownloadManager.class);
    protected static Map<String, BaseStandardOrderDownloader> maps = new HashMap<>();
    protected static Map<String, DownloadType> downloadTypemaps = new HashMap<>();

    static {
        maps.put(SyncOrderConst.TmcSalesOrderTag, new DownloadSaleOrder());
        maps.put(SyncOrderConst.TmcPurchaseOrdersTag, new DownloadPurchaseOrder());
        downloadTypemaps.put(SyncOrderConst.TmcSalesOrderTag, DownloadType.SALES_ORDER_DOWNLOAD);
        downloadTypemaps.put(SyncOrderConst.TmcPurchaseOrdersTag, DownloadType.PURCHASE_ORDER_DOWNLOAD);
    }

    public PurchaseOrderDownloadResult doYuanQiOrderSync(TmcOrderMessage message)
    {
        PurchaseOrderDownloadResult response = new PurchaseOrderDownloadResult();
        try {
            logger.info(String.format("元气[%s]消息开始处理下载订单",message.getTag()));
            EshopService eshopService = GetBeanUtil.getBean(EshopService.class);
            Otype otype = eshopService.getOtypeById(message.getEshopId());
            EshopSaleOrderDownloadTask task = buildTask(message, otype.getEshopInfo());
            BaseStandardOrderDownloader downloader = maps.get(message.getTag());
            response = downloader.download(task);
            SaveResultInfo(response, message);
            logger.info(String.format("元气下载[%s]订单结束，返回结果：%s",message.getTag(), JsonUtils.toJson(response)));
            return response;
        }catch (Exception ex)
        {
            String errorMessage =String.format("元气下载[%s]订单出错，错误原因：%s",message.getTag(),ex.getMessage());
            logger.error(errorMessage,ex);
            response.setMessage(errorMessage);
            response.setSuccess(false);
            return response;
        }
    }


    private void SaveResultInfo(PurchaseOrderDownloadResult response,TmcOrderMessage message)
    {
        TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
        EshopNotifyChange notify = new EshopNotifyChange();
        notify.setId(message.getNotifyChangeId());
        notify.setProfileId(message.getProfileId());
        if(message.getRetryTime() != 0){
            notify.setRetryTimes(message.getRetryTime());
        }
        if (response.isSuccess())
        {
            notify.setStatus(1);
            notify.setMessage(String.format("success"));
            tmcMapper.updateEshopNotifyChangeStatus(notify);
            return;
        }
        notify.setStatus(2);
        notify.setMessage(String.format("Error：%s",response.getMessage()));
        tmcMapper.updateEshopNotifyChangeStatus(notify);
    }

    private EshopSaleOrderDownloadTask buildTask(TmcOrderMessage message, EshopInfo eshopInfo){
        EshopSaleOrderDownloadTask task = OrderSyncUtil.buildTask(message, eshopInfo);
        task.setDownloadType(downloadTypemaps.get(message.getTag()));
        return task;
    }

}
