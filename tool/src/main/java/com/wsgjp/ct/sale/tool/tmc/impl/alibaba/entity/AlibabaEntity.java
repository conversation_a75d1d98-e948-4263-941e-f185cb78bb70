package com.wsgjp.ct.sale.tool.tmc.impl.alibaba.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class AlibabaEntity {
    private Long orderId;
    private String currentStatus;
    private String msgSendTime;
    private String buyerMemberId;
    private String refundAction;
    private String operator;
    private String refundId;


    private String appKey;
    private String appName;
    private Long isvUserID;
    private String loginID;
    private String memberID;
    private Long userID;
    private Long adminUserID;
    private Long timestamp;
    private Reason reason;
    private String openUid;
    private boolean subAuth;
    private String subOpenUid;
    private String adminOpenUid;

    private String sellerId;
    private String status;
    private Long opTime;
    private String attributes;

    public Long getIsvUserID() {
        return isvUserID;
    }

    public void setIsvUserID(Long isvUserID) {
        this.isvUserID = isvUserID;
    }

    public boolean isSubAuth() {
        return subAuth;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getOpTime() {
        return opTime;
    }

    public void setOpTime(Long opTime) {
        this.opTime = opTime;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public static class Reason {
        private String operation;
        private String operator;
        private String operatorType;

        @JsonProperty("operation")
        public String getOperation() { return operation; }
        @JsonProperty("operation")
        public void setOperation(String value) { this.operation = value; }

        @JsonProperty("operator")
        public String getOperator() { return operator; }
        @JsonProperty("operator")
        public void setOperator(String value) { this.operator = value; }

        @JsonProperty("operatorType")
        public String getOperatorType() { return operatorType; }
        @JsonProperty("operatorType")
        public void setOperatorType(String value) { this.operatorType = value; }
    }

    @JsonProperty("appKey")
    public String getAppKey() { return appKey; }
    @JsonProperty("appKey")
    public void setAppKey(String value) { this.appKey = value; }

    @JsonProperty("appName")
    public String getAppName() { return appName; }
    @JsonProperty("appName")
    public void setAppName(String value) { this.appName = value; }

    @JsonProperty("isvUserId")
    public Long getISVUserID() { return isvUserID; }
    @JsonProperty("isvUserId")
    public void setISVUserID(Long value) { this.isvUserID = value; }

    @JsonProperty("loginId")
    public String getLoginID() { return loginID; }
    @JsonProperty("loginId")
    public void setLoginID(String value) { this.loginID = value; }

    @JsonProperty("memberId")
    public String getMemberID() { return memberID; }
    @JsonProperty("memberId")
    public void setMemberID(String value) { this.memberID = value; }

    @JsonProperty("userId")
    public Long getUserID() { return userID; }
    @JsonProperty("userId")
    public void setUserID(Long value) { this.userID = value; }

    @JsonProperty("adminUserId")
    public Long getAdminUserID() { return adminUserID; }
    @JsonProperty("adminUserId")
    public void setAdminUserID(Long value) { this.adminUserID = value; }

    @JsonProperty("timestamp")
    public Long getTimestamp() { return timestamp; }
    @JsonProperty("timestamp")
    public void setTimestamp(Long value) { this.timestamp = value; }

    @JsonProperty("reason")
    public Reason getReason() { return reason; }
    @JsonProperty("reason")
    public void setReason(Reason value) { this.reason = value; }

    @JsonProperty("openUid")
    public String getOpenUid() { return openUid; }
    @JsonProperty("openUid")
    public void setOpenUid(String value) { this.openUid = value; }

    @JsonProperty("subAuth")
    public boolean getSubAuth() { return subAuth; }
    @JsonProperty("subAuth")
    public void setSubAuth(boolean value) { this.subAuth = value; }

    @JsonProperty("subOpenUid")
    public String getSubOpenUid() { return subOpenUid; }
    @JsonProperty("subOpenUid")
    public void setSubOpenUid(String value) { this.subOpenUid = value; }

    @JsonProperty("adminOpenUid")
    public String getAdminOpenUid() { return adminOpenUid; }
    @JsonProperty("adminOpenUid")
    public void setAdminOpenUid(String value) { this.adminOpenUid = value; }
    
    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    public String getMsgSendTime() {
        return msgSendTime;
    }

    public void setMsgSendTime(String msgSendTime) {
        this.msgSendTime = msgSendTime;
    }

    public String getBuyerMemberId() {
        return buyerMemberId;
    }

    public void setBuyerMemberId(String buyerMemberId) {
        this.buyerMemberId = buyerMemberId;
    }

    public String getRefundAction() {
        return refundAction;
    }

    public void setRefundAction(String refundAction) {
        this.refundAction = refundAction;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }
}
