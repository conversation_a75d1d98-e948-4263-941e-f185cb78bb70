package com.wsgjp.ct.sale.tool.tmc.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class TaoBaoResultVO implements Serializable {
    private ResultInfo result;

    public ResultInfo getResult() {
        return result;
    }

    public void setResult(ResultInfo result) {
        this.result = result;
    }

    public static class ResultInfo implements Serializable {
        private boolean success;
        @JsonProperty("error_msg")
        private String errorMsg;
        @JsonProperty("error_code")
        private String errorCode;
        private String data;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }
    }
}
