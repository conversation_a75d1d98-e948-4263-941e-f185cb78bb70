package com.wsgjp.ct.sale.tool.tmc.impl.meituan;

import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.MeiTuanOrderMessage;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class MeiTuanChangeAddressHandler extends MeiTuanNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(MeiTuanChangeAddressHandler.class);

    private final TmcNotifyProxy notifyProxy;

    public MeiTuanChangeAddressHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        String tmMessage = invokeMessage.getMessage();
        MeiTuanOrderMessage order;
        try {
            order = JsonUtils.toObject(tmMessage, MeiTuanOrderMessage.class);
        } catch (Exception ex) {
            LOGGER.error("{}tmMessage数据转换成美团订单信息实体出错，错误信息：{}", shopTypeName, ex.getMessage(), ex);
            return buildResponse(ex.getMessage());
        }
        try {
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(order.getAppPoiCode(), invokeMessage.getShopType().getCode());
            if (Objects.isNull(eshopRegister)) {
                LOGGER.error("profileId:{},店铺类型:{},appPoiCode:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, order.getAppPoiCode(), tmMessage);
                return buildResponse("管家婆未找到对应店铺!");
            }
            EshopNotifyChange change = new EshopNotifyChange();
            change.setContent(invokeMessage.getMessage());
            change.setTradeOrderId(order.getOrderId());
            change.setId(UId.newId());
            change.setType(TMCType.CHANGE_ADDRESS);
            change.setOnlineShopId(order.getAppPoiCode());
            change.setCreateTime(new Date());
            TmcInvokeRequest invokeRequest = new TmcInvokeRequest();
            invokeRequest.setEshopId(change.getEshopId());
            invokeRequest.setProfileId(change.getProfileId());
            invokeRequest.setMethod(TmcNotifyMethodEnum.MODIFY_ADDRESS_NOTIFY);
            invokeRequest.setTradeId(change.getTradeOrderId());
            invokeRequest.setMessage(change.getContent());
            //订单地址变动：通知业务段
            TmcInvokeResponse invokeResponse = notifyProxy.execute(invokeRequest);
            String msg = null == invokeResponse ? "通知订单用户变更消息失败：null" : invokeResponse.getMessage();
            if (invokeResponse != null && !invokeResponse.isError()) {
                msg = "ok";
            }
            return buildResponse(msg);
        } catch (Exception ex) {
            String errMsg = ex.getMessage();
            errMsg = StringUtils.isEmpty(errMsg) ? "" : errMsg;
            return buildResponse(errMsg);
        }
    }


    @Override
    public String serviceName() {
        return "orderAddressChange";
    }
}
