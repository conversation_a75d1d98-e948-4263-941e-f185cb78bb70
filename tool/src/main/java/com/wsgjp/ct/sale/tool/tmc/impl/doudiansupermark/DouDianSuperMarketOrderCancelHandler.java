package com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark;

import com.doudian.open.spi.yunc_wms_outbound_cancel.param.YuncWmsOutboundCancelParam;
import com.doudian.open.utils.JsonUtil;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.response.logistics.BaseResponse;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.dto.tmc.DataPushDataEntity;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.dto.tmc.DataPushDataEntity;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.request.refund.OrderCancelFeedbackRequest;
import com.wsgjp.ct.sale.platform.entity.response.tmc.CancelOrderResponse;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.enums.MsgDataTypeEnum;
import com.wsgjp.ct.sale.platform.service.TmcMsgService;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

/**
 * 订单取消消息处理
 */
@Component
public class DouDianSuperMarketOrderCancelHandler extends DDSNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DouDianSuperMarketOrderCancelHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    private final EshopTmcConfig config;
    public DouDianSuperMarketOrderCancelHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper,EshopTmcConfig config) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
        this.config = config;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        ShopType shopType = invokeMessage.getShopType();
        LOGGER.info("======{}进入invoker方法======",shopType.getName());
        // 一、获取订单列表
        YuncWmsOutboundCancelParam yuncWmsOutboundCancelParam;
        try {
            yuncWmsOutboundCancelParam = JsonUtil.fromJson(invokeMessage.getMessage(), YuncWmsOutboundCancelParam.class);
        } catch (Exception e) {
            LOGGER.error("{}数据转换成YuncWmsOutboundCancelParam实体出错,错误信息：{},tmcMessage:{}",
                    shopType.getName(),e.getMessage(),invokeMessage.getMessage());
            return JsonUtils.toJson(new GeneralResult(10003L,"序列化失败",null));
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(yuncWmsOutboundCancelParam.getWarehouseCode(), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",
                    invokeMessage.getProfileId(),shopType.getName(),yuncWmsOutboundCancelParam.getWarehouseCode(),invokeMessage.getMessage());
            return JsonUtils.toJson(new GeneralResult(10003L,"店铺信息查询失败",null));
        }
        String shopAccount = yuncWmsOutboundCancelParam.getWarehouseCode();
        CancelOrderResponse cancelOrderResponse = new CancelOrderResponse();
        cancelOrderResponse.setTradeId(yuncWmsOutboundCancelParam.getOutboundOrderNo());

        //  二、判断订单能否取消
        DeliverService deliverService = GetBeanUtil.getBean(DeliverService.class);
        BaseResponse baseResponse = deliverService.checkCanCancelDeliver(cancelOrderResponse.getTradeId());
        // 0表示可以取消  1表示不可以取消（msg会有消息）
        OrderCancelFeedbackRequest orderCancelFeedbackRequest = new OrderCancelFeedbackRequest();
        orderCancelFeedbackRequest.setCancelReason(baseResponse.getMsg());
        orderCancelFeedbackRequest.setTradeId(cancelOrderResponse.getTradeId());
        orderCancelFeedbackRequest.setShopId(eshopRegister.getId());
        if ("0".equals(baseResponse.getCode())) {
            orderCancelFeedbackRequest.setCancelResult(true);
        } else {
            orderCancelFeedbackRequest.setCancelResult(false);
        }
        if (!orderCancelFeedbackRequest.getCancelResult()){
            return JsonUtils.toJson(new GeneralResult(10003L,"不能取消",null));
        }
        try {
            //更新消息表中的订单状态
            updateTmcOrderStatus(invokeMessage.getProfileId(),eshopRegister.getId(),cancelOrderResponse.getTradeId());

            try {
                TmcMsgService tmcMsgService = BeanUtils.getBean(TmcMsgService.class);
                DataPushDataEntity msgEntity = new DataPushDataEntity();
                msgEntity.setOnlineShopId(eshopRegister.getCode());
                msgEntity.setTradeId(cancelOrderResponse.getTradeId());
                msgEntity.setShopType(ShopType.DouDianSupermarket.getCode());
                msgEntity.setUpdateTime(new Date());
                msgEntity.setDataType(MsgDataTypeEnum.ORDER);
                tmcMsgService.updateByTradeId(msgEntity);
            } catch (Exception e) {
                LOGGER.error("订单号:{}更新消息表订单状态出错！错误信息：{}",cancelOrderResponse.getTradeId(),e.getMessage(),e);
            }

            //取消订单回告成功,发送消息修改原始订单状态
            EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
            eshopNotifyChange.setOnlineShopId(shopAccount);
            //必传
            eshopNotifyChange.setContent("");
            eshopNotifyChange.setTradeOrderId(cancelOrderResponse.getTradeId());
            eshopNotifyChange.setType(TMCType.Order);
            SupportUtil.doOrderNotifyByDelayWay(shopAccount,eshopNotifyChange,invokeMessage.getShopType().getCode(),config.getDouDianSuperMarkSendDelayTime());
            return JsonUtils.toJson(new GeneralResult(0L,"success",null));
        }catch (Exception e){
            LOGGER.error("{}修改数据库或发起tmc订单更新失败,错误信息：{},tmcMessage:{}",
                    shopType.getName(),e.getMessage(),invokeMessage.getMessage());
            return JsonUtils.toJson(new GeneralResult(10003L,e.getMessage(),null));
        }

    }

    private void updateTmcOrderStatus(BigInteger profileId,BigInteger eshopId,String tradeId){
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setProfileId(profileId);
        tmcOrderMsgEntity.setEshopId(eshopId);
        tmcOrderMsgEntity.setTradeOrderId(tradeId);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.ALL_CLOSED);
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgMapper.updateTmcOrderMsg(tmcOrderMsgEntity);
    }

    @Override
    public String serviceName() {
        return "yunc.wms.outbound.cancel";
    }
}
