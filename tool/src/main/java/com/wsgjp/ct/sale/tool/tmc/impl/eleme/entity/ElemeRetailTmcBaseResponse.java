package com.wsgjp.ct.sale.tool.tmc.impl.eleme.entity;

public class ElemeRetailTmcBaseResponse {
    private ElemeRetailBaseResponseBody body;
    private String cmd;
    private String sign;
    private String source;
    private String ticket;
    private long timestamp;
    private int version;

    public ElemeRetailBaseResponseBody getBody() {
        return body;
    }

    public void setBody(ElemeRetailBaseResponseBody body) {
        this.body = body;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }
}
