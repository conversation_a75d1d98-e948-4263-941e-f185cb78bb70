package com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.response.logistics.BaseResponse;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopRefundService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.entities.TmallSuperMarketOrderCancelEntity;
import com.wsgjp.ct.sale.platform.entity.request.order.PushMessageRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.OrderCancelFeedbackRequest;
import com.wsgjp.ct.sale.platform.entity.response.tmc.CancelOrderResponse;
import com.wsgjp.ct.sale.platform.enums.OrderCancelType;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl.entity.refund.OrderCancel;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

/**
 * 订单取消消息处理
 */
@Component
public class AlibabaGylOrderCancelHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlibabaGylOrderCancelHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    public AlibabaGylOrderCancelHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        ShopType shopType = invokeMessage.getShopType();
        LOGGER.info("======{}进入invoker方法======",shopType.getName());
        // 一、获取订单列表
        BifrostEshopOrderService eshopOrderService = GetBeanUtil.getBean(BifrostEshopOrderService.class);
        BifrostEshopRefundService eshopRefundService = GetBeanUtil.getBean(BifrostEshopRefundService.class);
        OrderCancel orderCancel;
        try {
            orderCancel = JsonUtils.toObject(invokeMessage.getMessage(), OrderCancel.class);
            if (orderCancel == null || orderCancel.getRequest() == null){
               throw new RuntimeException("数据转换成OrderCancel实体为空");
            }
        } catch (Exception e) {
            LOGGER.error("{}数据转换成TmallSuperMarketOrderCancelEntity实体出错,错误信息：{},tmcMessage:{}",
                    shopType.getName(),e.getMessage(),invokeMessage.getMessage());
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        TmallSuperMarketOrderCancelEntity cancelOrderEntity = orderCancel.getRequest();
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(cancelOrderEntity.getSupplierId(), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",
                    invokeMessage.getProfileId(),shopType.getName(),cancelOrderEntity.getSupplierId(),invokeMessage.getMessage());
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        String shopAccount = cancelOrderEntity.getSupplierId();
        PushMessageRequest pushMessageRequest = new PushMessageRequest();
        pushMessageRequest.setMessage(invokeMessage.getMessage());
        pushMessageRequest.setShopId(eshopRegister.getId());
        CancelOrderResponse cancelOrderResponse = eshopOrderService.cancelOrder(pushMessageRequest);
        //  二、判断订单能否取消
        DeliverService deliverService = GetBeanUtil.getBean(DeliverService.class);
        BaseResponse baseResponse = deliverService.checkCanCancelDeliver(cancelOrderResponse.getTradeId());
        // 0表示可以取消  1表示不可以取消（msg会有消息）
        OrderCancelFeedbackRequest orderCancelFeedbackRequest = new OrderCancelFeedbackRequest();
        orderCancelFeedbackRequest.setCancelReason(baseResponse.getMsg());
        orderCancelFeedbackRequest.setTradeId(cancelOrderResponse.getTradeId());
        orderCancelFeedbackRequest.setShopId(eshopRegister.getId());
        if ("0".equals(baseResponse.getCode())) {
            orderCancelFeedbackRequest.setCancelResult(true);
        } else {
            // 判定是否是强制取消,如果是则进行拦截
            PushMessageRequest request = new PushMessageRequest();
            request.setMessage(invokeMessage.getMessage());
            if (cancelOrderResponse.getCancelType() == OrderCancelType.Force_Cancel) {
                orderCancelFeedbackRequest.setCancelResult(true);
                deliverService.platformCancelDeliver(cancelOrderResponse.getTradeId());
            } else {
                orderCancelFeedbackRequest.setCancelResult(false);
            }
        }
        com.wsgjp.ct.sale.platform.entity.response.BaseResponse response = eshopRefundService.orderCancelFeedback(orderCancelFeedbackRequest);
        if (!response.getSuccess()){
            LOGGER.info("账套ID：{},店铺ID：{},订单【{}】取消订单回告接口调用返回错误！错误信息：msg:{},code:{}",
                    invokeMessage.getProfileId(),eshopRegister.getId(),orderCancelFeedbackRequest.getTradeId(),response.getMessage(),response.getCode());
            return JsonUtils.toJson(new AlibabaGylResponse(false,String.valueOf(response.getCode()),response.getMessage(),true));
        }
        //更新消息表中的订单状态
        updateTmcOrderStatus(invokeMessage.getProfileId(),eshopRegister.getId(),cancelOrderResponse.getTradeId());
        //取消订单回告成功,发送消息修改原始订单状态
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(shopAccount);
        //必传
        eshopNotifyChange.setContent("");
        eshopNotifyChange.setTradeOrderId(cancelOrderResponse.getTradeId());
        eshopNotifyChange.setType(TMCType.Order);
        SupportUtil.doOrderNotify(shopAccount,eshopNotifyChange,invokeMessage.getShopType().getCode());
        AlibabaGylResponse alibabaGylResponse = new AlibabaGylResponse(true,"0","成功",false);
        return JsonUtils.toJson(alibabaGylResponse);
    }

    private void updateTmcOrderStatus(BigInteger profileId,BigInteger eshopId,String tradeId){
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setProfileId(profileId);
        tmcOrderMsgEntity.setEshopId(eshopId);
        tmcOrderMsgEntity.setTradeOrderId(tradeId);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.ALL_CLOSED);
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgMapper.updateTmcOrderMsg(tmcOrderMsgEntity);
    }

    @Override
    public String serviceName() {
        return "odc.alibaba.ascp.uop.consignorder.cancel";
    }
}
