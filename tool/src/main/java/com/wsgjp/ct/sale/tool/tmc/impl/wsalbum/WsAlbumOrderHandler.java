package com.wsgjp.ct.sale.tool.tmc.impl.wsalbum;

import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.dto.WsAlbumOrderMessageDto;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
public class WsAlbumOrderHandler implements MessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(WsAlbumOrderHandler.class);
    private static final String ERROR_CODE = "-1";
    private static final String SUCCESS_CODE = "0";

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        WsAlbumTmcResponse response = new WsAlbumTmcResponse();
        try {
            WsAlbumOrderMessageDto wsAlbumMessage =
                    JsonUtils.toObject(invokeMessage.getMessage(), WsAlbumOrderMessageDto.class);
            String albumId = wsAlbumMessage.getAlbumId();
            if (StringUtils.isEmpty(albumId)) {
                logger.error("获取albumId失败{}", invokeMessage.getParams() == null ? "" : ngp.utils.JsonUtils.toJson(invokeMessage.getParams()));
                response.setErrcode(ERROR_CODE);
                response.setErrmsg("获取albumId失败");
                response.setResult("failed");
                return ngp.utils.JsonUtils.toJson(response);
            }
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(albumId, invokeMessage.getShopType().getCode());
            EshopNotifyChange change = new EshopNotifyChange();
            change.setProfileId(eshopRegister.getProfileId());
            change.setEshopId(eshopRegister.getId());
            change.setContent(invokeMessage.getMessage());
            change.setTradeOrderId(wsAlbumMessage.getSaleOrderId());
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(albumId);
            change.setCreateTime(new Date());
            SupportUtil.doNotify(change.getOnlineShopId(), change, invokeMessage.getShopType().getCode());
            response.setResult("success");
            response.setErrcode(SUCCESS_CODE);
            response.setErrmsg("");
        } catch (Exception e) {
            response.setResult("failed");
            response.setErrmsg(e.getMessage());
            response.setErrcode("-1");
        }
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "invokeWsAlbum/orderReceive";
    }

    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(true);
        return result;
    }
}
