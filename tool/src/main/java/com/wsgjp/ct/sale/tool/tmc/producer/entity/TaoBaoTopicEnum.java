package com.wsgjp.ct.sale.tool.tmc.producer.entity;

import bf.datasource.typehandler.CodeEnum;

public enum TaoBaoTopicEnum implements CodeEnum {
    /**
     * taobao_refund_RefundSuccess 退款成功
     * taobao_refund_RefundClosed   退款关闭
     * taobao_refund_RefundSellerAgreeAgreement   买家同意
     * taobao_refund_RefundSellerRefuseAgreement  卖家拒绝
     * taobao_refund_RefundCreated  退款创建
     */
    TAO_BAO_REFUND_REFUND_SUCCESS(1001,"FulFillOrder_cancelSubOrder","退款成功"),
    TAO_BAO_REFUND_REFUND_CLOSED(1002,"taobao_refund_RefundClosed","退款关闭"),
    TAO_BAO_REFUND_REFUND_SELLER_AGREE_AGREEMENT(1003,"taobao_refund_RefundSellerAgreeAgreement","买家同意"),
    TAO_BAO_REFUND_REFUND_SELLER_REFUSE_AGREEMENT(1004,"taobao_refund_RefundSellerRefuseAgreement","卖家拒绝"),
    TAO_BAO_REFUND_REFUND_CREATED(1005,"taobao_refund_RefundCreated","退款创建");
    private final int code;
    private final String topic;
    private final String desc;

    TaoBaoTopicEnum(int code, String topic, String desc) {
        this.code = code;
        this.topic = topic;
        this.desc = desc;
    }
    @Override
    public int getCode() {
        return this.code;
    }

    public String getTopic() {
        return topic;
    }

    public String getDesc() {
        return desc;
    }
}
