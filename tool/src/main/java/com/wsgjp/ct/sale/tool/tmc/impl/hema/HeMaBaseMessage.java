package com.wsgjp.ct.sale.tool.tmc.impl.hema;

import com.wsgjp.ct.sale.platform.factory.hema.entity.FulFillMainOrderDTO;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class HeMaBaseMessage {
    private String requestId;
    private String merchantCode;
    private String routeType;
    private String fulfillOrderId;
    private String fulFillMainOrderDTO;
    private FulFillMainOrderDTO fulFillMainOrder;

    public void setFulFillMainOrderDTO(String fulFillMainOrderDTO) {
        this.fulFillMainOrderDTO = fulFillMainOrderDTO;
    }

    public void setFulFillMainOrder(FulFillMainOrderDTO fulFillMainOrder) {
        this.fulFillMainOrder = fulFillMainOrder;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }

    public String getFulfillOrderId() {
        if(this.fulFillMainOrderDTO != null){
            FulFillMainOrderDTO order = this.getFulFillMainOrder();
            if(order != null){
                return order.getFulfillOrderId();
            }else{
                return fulfillOrderId;
            }
        }
        return fulfillOrderId;
    }

    public void setFulfillOrderId(String fulfillOrderId) {
        this.fulfillOrderId = fulfillOrderId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getFulFillMainOrderDTO() {
        return fulFillMainOrderDTO;
    }

    public FulFillMainOrderDTO getFulFillMainOrder() {
        if(ngp.utils.StringUtils.isBlank(this.fulFillMainOrderDTO)){
            return null;
        }
        this.fulFillMainOrder = JsonUtils.toObject(this.fulFillMainOrderDTO,FulFillMainOrderDTO.class);
        return fulFillMainOrder;
    }

    public String getOnlineShopId() {
        if (StringUtils.isNotBlank(this.merchantCode)) {
            return this.merchantCode;
        }
        FulFillMainOrderDTO fulFillMainOrder = this.getFulFillMainOrder();
        if (fulFillMainOrder != null) {
            return fulFillMainOrder.getMerchantCode();
        }
        return "";
    }
}
