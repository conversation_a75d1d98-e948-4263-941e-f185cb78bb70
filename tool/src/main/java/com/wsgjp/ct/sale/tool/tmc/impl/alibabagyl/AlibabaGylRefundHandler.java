package com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcRefundMsgMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcRefundMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl.entity.refund.RefundEntity.RefundRequest;
import com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl.entity.refund.RefundEntity.TmallSuperMarketRefundEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class AlibabaGylRefundHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlibabaGylOrderHandler.class);
    private final EshopTmcUtils eshopTmcUtils;
    private final EshopTmcRefundMsgMapper tmcRefundMsgMapper;

    public AlibabaGylRefundHandler(EshopTmcUtils eshopTmcUtils, EshopTmcRefundMsgMapper tmcRefundMsgMapper) {
        this.eshopTmcUtils = eshopTmcUtils;
        this.tmcRefundMsgMapper = tmcRefundMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        RefundRequest request;
        com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl.entity.refund.RefundEntity.TmallSuperMarketRefundEntity apiRefund;
        try {
            request = JsonUtils.toObject(tmMessage, RefundRequest.class);
            if (request == null || request.getRequest() == null){
                throw new RuntimeException("阿里供应链售后实体转换出错");
            }
            apiRefund = request.getRequest();
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成TmallSuperMarketRefundEntity实体出错，错误信息：{},tmMessage:{}", shopTypeName, e.getMessage(),tmMessage);
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiRefund.getSupplierId(), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",invokeMessage.getProfileId(),shopTypeName,apiRefund.getSupplierId(),tmMessage);
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        LOGGER.info("profileId:{},eshopId:{},店铺类型：{}", eshopRegister.getProfileId(), eshopRegister.getId(), eshopRegister.getType());
        try {
            saveTmcRefundMsg(eshopRegister, tmMessage, apiRefund);
        } catch (Exception ex) {
            LOGGER.error("{}保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", shopTypeName, eshopRegister.getProfileId(), eshopRegister.getId(), tmMessage, ex.getMessage(),ex);
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setProfileId(eshopRegister.getProfileId());
        eshopNotifyChange.setEshopId(eshopRegister.getId());
        eshopNotifyChange.setTradeOrderId(apiRefund.getForwardOrderCode());
        eshopNotifyChange.setContent("");
        eshopNotifyChange.setId(UId.newId());
        eshopNotifyChange.setType(TMCType.Order);
        SupportUtil.doOrderNotify(apiRefund.getSupplierId(), eshopNotifyChange, invokeMessage.getShopType().getCode());
        AlibabaGylResponse response = new AlibabaGylResponse(true,"0","成功",false);
        return JsonUtils.toJson(response);
    }

    public int saveTmcRefundMsg(EshopRegisterNotify eshopRegister, String tmMessage, TmallSuperMarketRefundEntity apiRefund) {
        //bizOrderCode 售后单号
        EshopTmcRefundMsgEntity refundMsgEntity = tmcRefundMsgMapper.queryTmcRefundMsgByRefundId(eshopRegister.getProfileId(), eshopRegister.getId(), apiRefund.getBizOrderCode());
        if (Objects.isNull(refundMsgEntity)) {
            refundMsgEntity = buildEshopTmcRefundMsgEntity(eshopRegister, tmMessage, apiRefund);
            return tmcRefundMsgMapper.insertTmcRefundMsg(refundMsgEntity);
        } else {
            //todo  refundMsgEntity.setMsgStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            refundMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            refundMsgEntity.setUpdateTime(new Date());
            refundMsgEntity.setMessage(tmMessage);
            return tmcRefundMsgMapper.updateTmcRefundMsg(refundMsgEntity);
        }
    }

    private EshopTmcRefundMsgEntity buildEshopTmcRefundMsgEntity(EshopRegisterNotify eshopRegister, String tmMessage, TmallSuperMarketRefundEntity apiRefund) {
        EshopTmcRefundMsgEntity tmcRefundMsgEntity = new EshopTmcRefundMsgEntity();
        tmcRefundMsgEntity.setId(UId.newId());
        tmcRefundMsgEntity.setProfileId(eshopRegister.getProfileId());
        tmcRefundMsgEntity.setEshopId(eshopRegister.getId());
        tmcRefundMsgEntity.setShopType(ShopType.valueOf(eshopRegister.getType()));
        tmcRefundMsgEntity.setTradeOrderId(apiRefund.getForwardOrderCode());
        tmcRefundMsgEntity.setRefundOrderId(apiRefund.getBizOrderCode());
        tmcRefundMsgEntity.setMessage(tmMessage);
        tmcRefundMsgEntity.setMsgStatus(0);
        tmcRefundMsgEntity.setMsgCreateTime(new Date());
        tmcRefundMsgEntity.setMsgUpdateTime(new Date());
        tmcRefundMsgEntity.setCreateTime(new Date());
        return tmcRefundMsgEntity;
    }

    @Override
    public String serviceName() {
        return "odc.alibaba.ascp.uop.reverseorder.instorage.notify";
    }
}
