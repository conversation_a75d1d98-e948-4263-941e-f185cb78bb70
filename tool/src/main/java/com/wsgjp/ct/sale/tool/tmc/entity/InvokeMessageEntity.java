package com.wsgjp.ct.sale.tool.tmc.entity;

import com.wsgjp.ct.common.enums.core.enums.ShopType;

import java.math.BigInteger;
import java.util.Map;

public class InvokeMessageEntity {
    private Map<String, String> params;
    private BigInteger eshopId;
    private BigInteger profileId;
    private String message;
    private ShopType shopType;
    private String onlineEshopId;
    private boolean needRecordLog;

    public String getOnlineEshopId() {
        return onlineEshopId;
    }

    public void setOnlineEshopId(String onlineEshopId) {
        this.onlineEshopId = onlineEshopId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public boolean isNeedRecordLog() {
        return needRecordLog;
    }

    public void setNeedRecordLog(boolean needRecordLog) {
        this.needRecordLog = needRecordLog;
    }
}
