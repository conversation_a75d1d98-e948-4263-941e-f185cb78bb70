package com.wsgjp.ct.sale.tool.tmc.impl.alibabaszxd;

import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibaba.entity.AlibabaRes;
import com.wsgjp.ct.sale.tool.tmc.impl.alibabaszxd.entity.AlibabaSZXDEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibabaszxd.entity.AlibabaSZXDResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

@Component
public class AlibabaSZXDOrderRefundHandler extends AlibabaSZXDNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlibabaSZXDOrderRefundHandler.class);
    private final EshopTmcUtils eshopTmcUtils;

    public AlibabaSZXDOrderRefundHandler(EshopTmcUtils eshopTmcUtils) {
        this.eshopTmcUtils = eshopTmcUtils;
    }

    /**
     * 订单消息相关
     */

    private final String XIAODIAN_LINK_ORDER_STATUS_CHANGE = "XIAODIAN_LINK_ORDER_STATUS_CHANGE";
    private final String XIAODIAN_LINK_ORDER_ENTRY_STATUS_CHANGE = "XIAODIAN_LINK_ORDER_ENTRY_STATUS_CHANGE";
    /**
     * 售后消息
     */
    private final String XIAODIAN_REVERSE_ORDER_GOODS_STATUS_CHANGE = "XIAODIAN_REVERSE_ORDER_GOODS_STATUS_CHANGE";
    private final String XIAODIAN_REVERSE_ORDER_STATUS_CHANGE = "XIAODIAN_REVERSE_ORDER_STATUS_CHANGE";
    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        AlibabaSZXDResponse response;
        AlibabaRes res = new AlibabaRes();
        try {
            response = JsonUtils.toObject(tmMessage, AlibabaSZXDResponse.class);
            AlibabaSZXDEntity entity = response.getData();
            if (entity == null) {
                res.setSuccess(false);
                res.setError_code(204);
                res.setError_msg("Json转实体失败");
                return JsonUtils.toJson(res);
            }
            BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
            if (BigInteger.ZERO.equals(invokeMessage.getEshopId())) {
                // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                EshopRegisterNotify notify = SupportUtil.buildNotify(response.getUserInfo(), 4);
                if (notify == null || notify.getId() == null || notify.getId().equals(BigInteger.ZERO)) {
                    LOGGER.error("阿里巴巴数字小店消息处理失败：eshopId拿取失败");
                    res.setSuccess(false);
                    res.setError_code(201);
                    res.setError_msg("店铺id寻找失败");
                    return JsonUtils.toJson(res);
                }
                eshopId = notify.getId();
            } else {
                eshopId = invokeMessage.getEshopId();
            }
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
            if (Objects.isNull(eshopInfo)) {
                LOGGER.info("profileId:{},eshopId:{},店铺类型:{},阿里巴巴数字小店查询店铺信息为空!", invokeMessage.getProfileId(), invokeMessage.getEshopId(), shopTypeName);
                res.setSuccess(false);
                res.setError_code(202);
                res.setError_msg("店铺信息获取失败");
                return JsonUtils.toJson(res);
            }

            EshopNotifyChange change = handleMessage(response, entity, eshopInfo);
            SupportUtil.doOrderNotify(response.getUserInfo(), change, eshopInfo.getEshopType().getCode());
            res.setSuccess(true);
            return JsonUtils.toJson(res);
        } catch (Exception e) {
            LOGGER.error("{}未知错误，错误信息：{}", shopTypeName, e.getMessage());
            res.setSuccess(false);
            res.setError_code(205);
            res.setError_msg("未知异常:" + e.getMessage());
            return JsonUtils.toJson(res);
        }

    }


    private EshopNotifyChange handleMessage(AlibabaSZXDResponse msg, AlibabaSZXDEntity order, EshopInfo eshopInfo) {
        if (msg == null || StringUtils.isEmpty(msg.getType())) {
            return null;
        }
        EshopNotifyChange changeInfo;
        switch (msg.getType()) {
            case XIAODIAN_LINK_ORDER_STATUS_CHANGE:
            case XIAODIAN_LINK_ORDER_ENTRY_STATUS_CHANGE:
                changeInfo = handleMessageByType(msg, TMCType.Order, order);
                break;
            case XIAODIAN_REVERSE_ORDER_GOODS_STATUS_CHANGE:
            case XIAODIAN_REVERSE_ORDER_STATUS_CHANGE:
                changeInfo = handleMessageByType(msg, TMCType.RefundOrder, order);
//                saveTmcRefundMsg(eshopInfo, msg);
                break;
            default:
                throw new RuntimeException("erp未接入此类型消息处理:" + msg.getType());
        }
        return changeInfo;
    }


    private EshopNotifyChange handleMessageByType(AlibabaSZXDResponse msg, TMCType tmcType, AlibabaSZXDEntity order) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(JsonUtils.toJson(msg.getData()));
        // 目前只支持订单号下载
        change.setTradeOrderId(order.getLinkOrderId());
        // 后续支持售后单号下载后使用
        if (tmcType == TMCType.RefundOrder) {
            change.setTradeOrderId(order.getReverseOrderID());
            change.setRefundOrderId(order.getReverseOrderID());
        }
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(msg.getUserInfo());
        change.setCreateTime(new Date());
        if (StringUtils.isNotEmpty(order.getMsgSendTime())) {
            change.setUpdateTime(DateUtils.parse(order.getMsgSendTime()));
        }
        return change;
    }


    @Override
    public String serviceName() {
        return "alibabaSZXDInvoker";
    }

}
