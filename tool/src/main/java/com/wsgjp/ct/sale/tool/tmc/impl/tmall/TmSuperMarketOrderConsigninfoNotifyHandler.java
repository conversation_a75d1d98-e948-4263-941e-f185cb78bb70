package com.wsgjp.ct.sale.tool.tmc.impl.tmall;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.constant.PlatformTmcType;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.OrderConsignInfoNotifyRequest;
import com.wsgjp.ct.sale.tool.tmc.entity.OrderRequest;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class TmSuperMarketOrderConsigninfoNotifyHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(TmSuperMarketOrderConsigninfoNotifyHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    public TmSuperMarketOrderConsigninfoNotifyHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        OrderConsignInfoNotifyRequest apiOrder;
        try {
            apiOrder = JsonUtils.toObject(invokeMessage.getMessage(), OrderConsignInfoNotifyRequest.class);
            if (apiOrder == null || apiOrder.getRequest() == null){
                return "{\n" +
                        "    \"response\":{\n" +
                        "        \"success\":false,\n" +
                        "        \"responseCode\":\"1001\",\n" +
                        "        \"responseMsg\":\"json序列化失败\"\n" +
                        "    }\n" +
                        "}";
            }
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成OrderRequest实体出错，错误信息：{}",shopTypeName,e.getMessage());
            return "{\n" +
                    "    \"response\":{\n" +
                    "        \"success\":false,\n" +
                    "        \"responseCode\":\"1001\",\n" +
                    "        \"responseMsg\":\"json序列化失败\"\n" +
                    "    }\n" +
                    "}";
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiOrder.getRequest().getSupplierId(), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",invokeMessage.getProfileId(),shopTypeName,apiOrder.getRequest().getSupplierId(),tmMessage);
            return "{\n" +
                    "    \"response\":{\n" +
                    "        \"success\":false,\n" +
                    "        \"responseCode\":\"1001\",\n" +
                    "        \"responseMsg\":\"未查到对应店铺信息\"\n" +
                    "    }\n" +
                    "}";
        }
        LOGGER.info("profileId:{},eshopId:{},店铺类型：{}",eshopRegister.getProfileId(),eshopRegister.getId(),shopTypeName);
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(apiOrder.getRequest().getSupplierId());
        eshopNotifyChange.setTradeOrderId(apiOrder.getRequest().getBizOrderCode());
        eshopNotifyChange.setType(TMCType.SERIES_FIELD_CHANGE);
        eshopNotifyChange.setContent(tmMessage);
        eshopNotifyChange.setPlatformMsgType(PlatformTmcType.ORDER_MSG);
        SupportUtil.doOrderNotify(apiOrder.getRequest().getSupplierId(),eshopNotifyChange,invokeMessage.getShopType().getCode());
        return "{\n" +
                "  \"response\": {\n" +
                "    \"success\": true\n" +
                "  }\n" +
                "}";
    }

    public int saveTmcOrderMsg(EshopRegisterNotify eshopRegister, String tmMessage, OrderRequest orderRequest){
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(eshopRegister.getProfileId(),eshopRegister.getId(),orderRequest.getBizOrderCode());
        if (Objects.isNull(orderMsgEntity)){
            orderMsgEntity = buildEshopTmcOrderMsgEntity(eshopRegister,tmMessage,orderRequest);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        }else {
            //orderMsgEntity.setStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(EshopRegisterNotify eshopRegister, String tmMessage, OrderRequest orderRequest) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopRegister.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopRegister.getId());
        tmcOrderMsgEntity.setShopType(ShopType.valueOf(eshopRegister.getType()));
        tmcOrderMsgEntity.setTradeOrderId(orderRequest.getBizOrderCode());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        //默认值已付款
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        try {
            Date date = DateUtils.parseDate(orderRequest.getOrderCreateTime(), "yyyy-mm-dd HH:mm:ss");
            tmcOrderMsgEntity.setCreateTime(date);
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (Exception e) {

        }
        return tmcOrderMsgEntity;
    }

    @Override
    public String serviceName() {
        return "qimen.alibaba.ascp.fxorders.consigninfo.notify";
    }
}
