package com.wsgjp.ct.sale.tool.tmc.impl.dewu;

import com.wsgjp.ct.common.enums.core.enums.tmc.TmcNotifyResponseEnum;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.AddressChangeMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibaba.AlibabaNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.impl.dewu.entity.MsgEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.dewu.entity.ParamWrapper;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DeWuOrderRefundHandler extends AlibabaNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeWuOrderRefundHandler.class);

    private final EshopTmcConfig config;

    private final EshopTmcUtils eshopTmcUtils;
    private final TmcNotifyProxy notifyProxy;

    public DeWuOrderRefundHandler(EshopTmcConfig config,EshopTmcUtils eshopTmcUtils,TmcNotifyProxy notifyProxy) {
        this.config = config;
        this.eshopTmcUtils = eshopTmcUtils;
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        ParamWrapper wrapper = null;
        try {
            if (StringUtils.isEmpty(tmMessage)){
                throw new RuntimeException("得物获取tmc消息为空");
            }
            wrapper = JsonUtils.toObject(tmMessage, ParamWrapper.class);
            if (wrapper == null){
                throw new RuntimeException("得物转换消息对象失败");
            }
            String msg = wrapper.getMsg();
            if (StringUtils.isEmpty(msg)){
                throw new RuntimeException("得物获取msg为空");
            }
            String aesDecode = "";
            aesDecode = AESDecode.aesDecode(config.getDeWuAppSecret(), msg);
            MsgEntity msgEntity = JsonUtils.toObject(aesDecode, MsgEntity.class);
            if (msgEntity == null){
                throw new RuntimeException("得物转换msg实体失败aesDecode：" + aesDecode);
            }
            wrapper.setMsgEntity(msgEntity);
            /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
            BigInteger eshopId =BigInteger.ZERO;
            if (BigInteger.ZERO.equals(invokeMessage.getEshopId())){
                // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                EshopRegisterNotify notify = SupportUtil.buildNotify(msgEntity.getOpenID(), 95);
                if (notify == null || notify.getId()==null|| notify.getId().equals(BigInteger.ZERO)){
                    throw new RuntimeException("得物获取eshopId失败");
                }
                eshopId=notify.getId();
            }else {
                eshopId = invokeMessage.getEshopId();
            }
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
            if(Objects.isNull(eshopInfo)){
                throw new RuntimeException(String.format("profileId:%s,eshopId:%s,店铺类型:%s,查询店铺信息为空!",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName));
            }
            //买家修改地址消息
            if("MODIFY_BUYER_ADDRESS".equals(wrapper.getType())){
                TmcInvokeRequest tmcInvokeRequest = buildUpdateAddressRequest(msgEntity);
                tmcInvokeRequest.setEshopId(eshopInfo.getOtypeId());
                tmcInvokeRequest.setShopType(eshopInfo.getEshopType());
                tmcInvokeRequest.setProfileId(eshopInfo.getProfileId());
                TmcInvokeResponse resp = notifyProxy.execute(tmcInvokeRequest);
                if (!TmcNotifyResponseEnum.SUCCESS.getCode().equals(resp.getCode())) {
                    LOGGER.error("通知订单收货地址变更失败");
                }
            }
            EshopNotifyChange change = handleMessage(wrapper);
            SupportUtil.doOrderNotify(msgEntity.getOpenID(),change,eshopInfo.getEshopType().getCode());
            DeWuResponse response = new DeWuResponse();
            response.setCode(200);
            response.setData(wrapper.getUuid());
            response.setMsg("SUCCESS");
            return JsonUtils.toJson(response);
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
            DeWuResponse response = new DeWuResponse();
            response.setCode(500);
            response.setData(wrapper == null ? "" : wrapper.getUuid());
            response.setMsg(e.getMessage());
            return JsonUtils.toJson(response);
        }
    }

    private EshopNotifyChange handleMessage(ParamWrapper paramWrapper) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(JsonUtils.toJson(paramWrapper));
        change.setTradeOrderId(StringUtils.isNotEmpty(paramWrapper.getMsgEntity().getOrderNo()) ? paramWrapper.getMsgEntity().getOrderNo() : paramWrapper.getMsgEntity().getSubOrderNo());
        change.setId(UId.newId());
        change.setType(TMCType.Order);
        change.setOnlineShopId(paramWrapper.getMsgEntity().getOpenID());
        change.setCreateTime(new Date());
        change.setUpdateTime( new Date());
        return change;
    }

    private TmcInvokeRequest buildUpdateAddressRequest(MsgEntity msgEntity) {
        TmcInvokeRequest request = new TmcInvokeRequest();
        AddressChangeMessage changeMessage = new AddressChangeMessage();
        request.setTradeId(msgEntity.getSubOrderNo());
        request.setMessage(JsonUtils.toJson(changeMessage));
        request.setMethod(TmcNotifyMethodEnum.MODIFY_ADDRESS_NOTIFY);
        return request;
    }

    @Override
    public String serviceName() {
        return "deWuInvoker";
    }
}
