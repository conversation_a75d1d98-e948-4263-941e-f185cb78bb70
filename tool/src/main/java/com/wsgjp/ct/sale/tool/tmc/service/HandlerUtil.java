package com.wsgjp.ct.sale.tool.tmc.service;

import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class HandlerUtil {
    private final List<MessageHandler> handlerList;

    @Autowired
    public HandlerUtil(List<MessageHandler> handlerList) {
        this.handlerList = handlerList;
    }

    public MessageHandler findHandler(String method) {
        return this.handlerList.stream().filter(p -> p.serviceName().equals(method)).findFirst().orElse(null);
    }
}
