package com.wsgjp.ct.sale.tool.tmc.impl.eleme.entity;

public class ElemeRetailBaseRequestBody {
    private String order_id;
    /**
     * 平台店铺ID
     */
    private String platform_shop_id;

    private String status;
    /**
     * 超时类型，枚举值：confirm_order=接单即将超时 refund_order=退款审核即将超时 pick_order=拣货即将超时
     */
    private String timeout_type;

    private String refund_order_id;

    private Integer refund_status;

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getPlatform_shop_id() {
        return platform_shop_id;
    }

    public void setPlatform_shop_id(String platform_shop_id) {
        this.platform_shop_id = platform_shop_id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTimeout_type() {
        return timeout_type;
    }

    public void setTimeout_type(String timeout_type) {
        this.timeout_type = timeout_type;
    }

    public String getRefund_order_id() {
        return refund_order_id;
    }

    public void setRefund_order_id(String refund_order_id) {
        this.refund_order_id = refund_order_id;
    }

    public Integer getRefund_status() {
        return refund_status;
    }

    public void setRefund_status(Integer refund_status) {
        this.refund_status = refund_status;
    }
}
