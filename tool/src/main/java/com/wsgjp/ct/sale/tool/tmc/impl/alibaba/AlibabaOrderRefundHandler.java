package com.wsgjp.ct.sale.tool.tmc.impl.alibaba;

import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.log.SystemLog;
import com.wsgjp.ct.sale.biz.eshoporder.impl.auth.AuthManager;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.dto.refund.EshopTmcRefundMsgDto;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.alibaba.AlibabaConfig;
import com.wsgjp.ct.sale.platform.sdk.entity.Etype;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopBaseInfoMapper;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibaba.entity.AlibabaEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibaba.entity.AlibabaRes;
import com.wsgjp.ct.sale.tool.tmc.impl.alibaba.entity.AlibabaResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.dao.entity.SysDataEntity;
import com.wsgjp.ct.support.dao.mapper.SysDataMapper;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.pubmessage.PubMessageCenterPip;
import com.wsgjp.ct.support.pubmessage.PubMessageCenterRequest;
import ngp.idgenerator.UId;
import ngp.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class AlibabaOrderRefundHandler extends AlibabaNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlibabaOrderRefundHandler.class);
    private final EshopTmcUtils eshopTmcUtils;
    private final EshopTmcRefundMsgService tmcRefundMsgService;
    private final AlibabaConfig alibabaConfig;
    private final List<String> refundActionCancelStatusList;

    public AlibabaOrderRefundHandler(EshopTmcUtils eshopTmcUtils, EshopTmcRefundMsgService tmcRefundMsgService, AlibabaConfig alibabaConfig) {
        this.eshopTmcUtils = eshopTmcUtils;
        this.tmcRefundMsgService = tmcRefundMsgService;
        this.alibabaConfig = alibabaConfig;
        this.refundActionCancelStatusList = alibabaConfig.getRefundActionCancelStatusList();
    }

    /**
     * 订单消息相关
     */

    private final String ORDER_BUYER_MAKE = "ORDER_BUYER_MAKE";
    private final String ORDER_ANNOUNCE_SENDGOODS = "ORDER_ANNOUNCE_SENDGOODS";
    private final String ORDER_PART_PART_SENDGOODS = "ORDER_PART_PART_SENDGOODS";
    private final String ORDER_ORDER_SELLER_CLOSE = "ORDER_ORDER_SELLER_CLOSE";
    private final String ORDER_MODIFY_MEMO = "ORDER_MODIFY_MEMO";
    private final String ORDER_ORDER_COMFIRM_RECEIVEGOODS = "ORDER_ORDER_COMFIRM_RECEIVEGOODS";
    private final String ORDER_ORDER_PRICE_MODIFY = "ORDER_ORDER_PRICE_MODIFY";
    private final String ORDER_ORDER_STEP_PAY = "ORDER_ORDER_STEP_PAY";
    private final String ORDER_PAY = "ORDER_PAY";
    private final String ORDER_ORDER_SUCCESS = "ORDER_ORDER_SUCCESS";

    /**
     * 售后单消息相关
     */
    private final String ORDER_ORDER_REFUND_AFTER_SALES = "ORDER_ORDER_REFUND_AFTER_SALES";
    private final String ORDER_ORDER_BUYER_REFUND_IN_SALES = "ORDER_ORDER_BUYER_REFUND_IN_SALES";
    private final String AUTHORIZATION_CANCEL = "AUTHORIZATION_CANCEL";
    private final String ASCP_SUPPLYCHAIN_MERCHANT_SIGN_UNSIGN = "ASCP_SUPPLYCHAIN_MERCHANT_SIGN_UNSIGN";


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
//        try {
//            String encode = PlatformEventSecurityUtil.encode("{\n" +
//                    "    \"eventId\":\"12312\",\n" +
//                    "    \"bizId\":2232700121995010,\n" +
//                    "    \"openId\":\"f198e2b6591c7e0314bf5724b80aaca5\",\n" +
//                    "    \"event\":\"kwaishop_order_statusChange\",\n" +
//                    "    \"info\":{\n" +
//                    "        \"oid\":2232700121995010,\n" +
//                    "        \"sellerId\":123,\n" +
//                    "        \"openId\":\"f198e2b6591c7e0314bf5724b80aaca5\",\n" +
//                    "        \"status\":30,\n" +
//                    "        \"beforeStatus\":80,\n" +
//                    "        \"updateTime\":1669617309000\n" +
//                    "    },\n" +
//                    "    \"createTime\":1669617309000\n" +
//                    "}", "6bKH1VD4Td7sCgTF6FdJJw==");
//            System.out.println(encode);
//        }catch (Exception e){
//
//        }

        AlibabaResponse response;
        AlibabaRes res = new AlibabaRes();
        try {
            response = JsonUtils.toObject(tmMessage, AlibabaResponse.class);
            AlibabaEntity entity = response.getData();
            if (entity == null) {
                res.setSuccess(false);
                res.setError_code(204);
                res.setError_msg("Json转实体失败");
                return JsonUtils.toJson(res);
            }
            BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
            if (BigInteger.ZERO.equals(invokeMessage.getEshopId())) {
                // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                EshopRegisterNotify notify = SupportUtil.buildNotify(response.getUserInfo(), 4);
                if (notify == null || notify.getId() == null || notify.getId().equals(BigInteger.ZERO)) {
                    LOGGER.error("阿里巴巴消息处理失败：eshopId拿取失败");
                    res.setSuccess(false);
                    res.setError_code(201);
                    res.setError_msg("店铺id寻找失败");
                    return JsonUtils.toJson(res);
                }
                eshopId = notify.getId();
            } else {
                eshopId = invokeMessage.getEshopId();
            }
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
            if (Objects.isNull(eshopInfo)) {
                LOGGER.info("profileId:{},eshopId:{},店铺类型:{},阿里巴巴查询店铺信息为空!", invokeMessage.getProfileId(), invokeMessage.getEshopId(), shopTypeName);
                res.setSuccess(false);
                res.setError_code(202);
                res.setError_msg("店铺信息获取失败");
                return JsonUtils.toJson(res);
            }
            if (ASCP_SUPPLYCHAIN_MERCHANT_SIGN_UNSIGN.equals(response.getType())) {
                SysDataMapper sysDataMapper = BeanUtils.getBean(SysDataMapper.class);
                if (response.getData() != null) {
                    String subName = String.format("%s_%s", "SCP_SUPPLYCHAIN_MERCHANT_SIGN_UNSIGN", eshopInfo.getOtypeId());
                    SysDataEntity sysData = new SysDataEntity();
                    sysData.setProfileId(CurrentUser.getProfileId());
                    sysData.setDescription("是否支持翱翔上传");
                    sysData.setLastModifyEtypeId(CurrentUser.getEmployeeId());
                    sysData.setId(UId.newId());
                    sysData.setSubName(subName);
                    if ("1".equals(response.getData().getStatus())) {
                        sysData.setSubValue("1");
                    } else {
                        sysData.setSubValue("0");
                    }
                    String subValue = sysDataMapper.getSysData(CurrentUser.getProfileId(), subName);
                    String logging = "";
                    if (StringUtils.isNotEmpty(subValue)) {
                        if (!subValue.equals(sysData.getSubValue())) {
                            sysDataMapper.updateData(CurrentUser.getProfileId(), sysData.getSubName(), sysData.getSubValue());
                            logging = String.format("阿里巴巴店铺网店名称[%s],由%s变为%s", eshopInfo.getFullname(), "0".equals(subValue) ? "未签约" : "已签约", "0".equals(sysData.getSubValue()) ? "未签约" : "已签约");
                        }
                    } else {
                        sysDataMapper.insertData(sysData);
                        if ("1".equals(sysData.getSubValue())) {
                            logging = String.format("阿里巴巴店铺网店名称[%s],已签约", eshopInfo.getFullname());
                        }
                    }
                    if (StringUtils.isNotEmpty(logging)) {
                        SystemLog systemLog = buildEshopConfigLog(logging);
                        LogService.add(systemLog);
                    }
                }
                res.setSuccess(true);
                return JsonUtils.toJson(res);
            }

            //买家修改地址消息
            EshopNotifyChange change = handleMessage(response, entity, eshopInfo);
            if (change.getType() == TMCType.AUTHORIZATION_CANCEL) {
                change.setProfileId(eshopInfo.getProfileId());
                change.setEshopId(eshopInfo.getOtypeId());
                SupportUtil.saveNotifyChange(change);
                SupportUtil.doWriteTmcMqLog(invokeMessage, change.getTradeOrderId(), response.getType());
                AuthManager authManager = GetBeanUtil.getBean(AuthManager.class);
                boolean refreshToken = authManager.doRefreshToken(change.getProfileId(), change.getEshopId());
                if (!refreshToken) {
                    PubMessageCenterRequest request = new PubMessageCenterRequest();
                    List<BigInteger> etypeList = getetypeList(CurrentUser.getProfileId());
                    request.setEventType("业务提醒");
                    request.setSubjectType("pubMsgEshopAuthorization");
                    String msg = "未知原因";
                    if (entity.getReason() != null) {
                        String operation = entity.getReason().getOperation();
                        if (StringUtils.isNotEmpty(operation)) {
                            msg = getMsg(operation);
                        }
                    }
                    request.setContext("【" + eshopInfo.getFullname() + "】已经授权过期" + "，请尽快重新授权!导致过期原因:" + msg);
                    request.setEtypeIds(etypeList);
                    request.setTitle("网店授权");
                    String url = "sale/eshoporder/eshop/EShopList.gspx";
                    request.setUri(url);
                    PubMessageCenterPip.Util.push(request);
                }
                res.setSuccess(true);
                return JsonUtils.toJson(res);
            }
            SupportUtil.doOrderNotify(response.getUserInfo(), change, eshopInfo.getEshopType().getCode());
            res.setSuccess(true);
            return JsonUtils.toJson(res);
        } catch (Exception e) {
            LOGGER.error("{}未知错误，错误信息：{}", shopTypeName, e.getMessage());
            res.setSuccess(false);
            res.setError_code(205);
            res.setError_msg("未知异常:" + e.getMessage());
            return JsonUtils.toJson(res);
        }

    }

    //账号改密码（ACCOUNT_PASSWORD_MODIFIED）、订购到期（ORDER_EXPIRED）、子账号被停用（SUB_ACCOUNT_INACTIVE）、子账号被删除（SUB_ACCOUNT_DELETED）、账号被安全处罚（ACCOUNT_PUBLISHED）、单用户对接应用授权账号被修改（BOND_ACCOUNT_MODIFIED）、手动取消账号授权（MANUAL_CANCEL）
    private String getMsg(String operation) {
        switch (operation) {
            case "ACCOUNT_PASSWORD_MODIFIED":
                return "账号改密码";
            case "ORDER_EXPIRED":
                return "订购到期";
            case "SUB_ACCOUNT_INACTIVE":
                return "子账号被停用";
            case "SUB_ACCOUNT_DELETED":
                return "子账号被删除";
            case "ACCOUNT_PUBLISHED":
                return "账号被安全处罚";
            case "BOND_ACCOUNT_MODIFIED":
                return "单用户对接应用授权账号被修改";
            case "MANUAL_CANCEL":
                return "手动取消账号授权";
        }
        return "未知原因";
    }

    private SystemLog buildEshopConfigLog(String logMsg) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        SystemLog baseInfoLog = new SystemLog();
        baseInfoLog.setProfileId(profileId);
        baseInfoLog.setEtypeId(employeeId);
        baseInfoLog.setId(UId.newId());
        baseInfoLog.setBody(logMsg);
        baseInfoLog.setEfullname("工具");
        baseInfoLog.setLogTime(DateUtils.getDate());
        baseInfoLog.setIp(IpUtils.getLocalHostIp());
        return baseInfoLog;
    }

    private List<BigInteger> getetypeList(BigInteger profileId) {
        PlatformSdkEshopBaseInfoMapper baseInfoMapper = BeanUtils.getBean(PlatformSdkEshopBaseInfoMapper.class);
        List<Etype> etypeInfoList = baseInfoMapper.getEtypeList(profileId);

        List<BigInteger> etypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(etypeInfoList)) {
            etypeList = etypeInfoList.stream().map(Etype::getId).collect(Collectors.toList());
        }
        return etypeList;
    }

    private EshopNotifyChange handleMessage(AlibabaResponse msg, AlibabaEntity order, EshopInfo eshopInfo) {
        if (msg == null || StringUtils.isEmpty(msg.getType())) {
            return null;
        }
        EshopNotifyChange changeInfo;
        switch (msg.getType()) {
            case ORDER_BUYER_MAKE:
            case ORDER_ANNOUNCE_SENDGOODS:
            case ORDER_PART_PART_SENDGOODS:
            case ORDER_ORDER_SELLER_CLOSE:
            case ORDER_MODIFY_MEMO:
            case ORDER_ORDER_COMFIRM_RECEIVEGOODS:
            case ORDER_ORDER_PRICE_MODIFY:
            case ORDER_ORDER_STEP_PAY:
            case ORDER_PAY:
            case ORDER_ORDER_SUCCESS:
                changeInfo = handleMessageByType(msg, TMCType.Order, order);
                break;
            case ORDER_ORDER_REFUND_AFTER_SALES:
            case ORDER_ORDER_BUYER_REFUND_IN_SALES:
                changeInfo = handleMessageByType(msg, TMCType.RefundOrder, order);
                saveTmcRefundMsg(eshopInfo, msg);
                break;
            case AUTHORIZATION_CANCEL:
                changeInfo = handleMessageByType(msg, TMCType.AUTHORIZATION_CANCEL, order);
                break;
            default:
                throw new RuntimeException("erp未接入此类型消息处理:" + msg.getType());
        }
        return changeInfo;
    }

    protected RefundStatus buildRefundStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return RefundStatus.NONE;
        }
        if (refundActionCancelStatusList.contains(status)) {
            return RefundStatus.CANCEL;
        }
        return RefundStatus.NONE;
    }

    protected int saveTmcRefundMsg(EshopInfo eshopInfo, AlibabaResponse msgObj) {
        String message = JsonUtils.toJson(msgObj);
        try {
            AlibabaEntity data = msgObj.getData();
            RefundStatus refundStatus = buildRefundStatus(data.getRefundAction());
            //只保存取消售后消息
            if (refundStatus != RefundStatus.CANCEL) {
                return 0;
            }
            EshopTmcRefundMsgDto refundMsgDto = new EshopTmcRefundMsgDto(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), eshopInfo.getEshopType(),
                    String.valueOf(data.getOrderId()), data.getRefundId(), message, refundStatus);
            return tmcRefundMsgService.saveOrUpdateTmcRefundMsg(refundMsgDto);
        } catch (Exception ex) {
            LOGGER.error("账套ID{},店铺ID{},保持售后消息失败，失败原因：{}.message:{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), ex.getMessage(), message, ex);
        }
        return 0;
    }

    private EshopNotifyChange handleMessageByType(AlibabaResponse msg, TMCType tmcType, AlibabaEntity order) {
        if (TMCType.AUTHORIZATION_CANCEL == tmcType) {
            EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
            eshopNotifyChange.setOnlineShopId(msg.getUserInfo());
            eshopNotifyChange.setTradeOrderId(StringUtils.isEmpty(order.getMemberID()) ? "" : order.getMemberID());
            eshopNotifyChange.setType(TMCType.AUTHORIZATION_CANCEL);
            eshopNotifyChange.setContent(JsonUtils.toJson(msg));
            return eshopNotifyChange;
        }
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(JsonUtils.toJson(msg.getData()));
        // 目前只支持订单号下载
        change.setTradeOrderId(order.getOrderId().toString());
        // 后续支持售后单号下载后使用
        if (tmcType == TMCType.RefundOrder) {
            change.setTradeOrderId(order.getRefundId());
            change.setRefundOrderId(order.getRefundId());
        }
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(msg.getUserInfo());
        change.setCreateTime(new Date());
        if (StringUtils.isNotEmpty(order.getMsgSendTime())) {
            change.setUpdateTime(DateUtils.parse(order.getMsgSendTime()));
        }
        return change;
    }


    @Override
    public String serviceName() {
        return "alibabaInvoker";
    }

}
