package com.wsgjp.ct.sale.tool.platform;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.util.CommonUtil;
import com.wsgjp.ct.sale.biz.common.BaseInfoCacheUtil;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryModifiedBaseInfoRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.BaseInfoUploadMapper;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformEshopConfig;
import com.wsgjp.ct.sale.platform.dto.stock.BaseWareHouseInfo;
import com.wsgjp.ct.sale.platform.entity.entities.upload.*;
import com.wsgjp.ct.sale.platform.entity.request.upload.*;
import com.wsgjp.ct.sale.platform.entity.response.upload.BaseUploadResponse;
import com.wsgjp.ct.sale.platform.entity.response.upload.UploadKtypeResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.upload.EshopBaseInfoUploadFeature;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.support.global.GlobalConfig;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2023/11/6 14:01
 */
@Service
public class TaoBaoSyncService {
    private static final Logger logger = LoggerFactory.getLogger(TaoBaoSyncService.class);

    private final BaseInfoUploadMapper mapper;
    private final BaseInfoCacheUtil cacheUtil;

    private final PlatformToolConfig config;

    public TaoBaoSyncService(BaseInfoUploadMapper mapper, BaseInfoCacheUtil cacheUtil, PlatformToolConfig config) {
        this.mapper = mapper;
        this.cacheUtil = cacheUtil;
        this.config = config;
    }


    public void doUpload(BigInteger profileId) {
        try {
            List<Otype> otypeList = cacheUtil.getAllOtype(profileId);
            if (CollectionUtils.isEmpty(otypeList)) {
                return;
            }
            if (!config.isAutoSyncAoXiang()) {
                return;
            }
            String enabledStr = GlobalConfig.get(PlatformToolConst.AX_SYNC_ENABLED, PlatformToolConst.ENABLED_STR);
            if (!enabledStr.equals(PlatformToolConst.ENABLED_STR)) {
                return;
            }
            for (Otype otype : otypeList) {
                EshopInfo eshopInfo = otype.getEshopInfo();
                if (eshopInfo.getEshopType() == ShopType.Alibaba) {
                    String subName = String.format("%s_%s", "SCP_SUPPLYCHAIN_MERCHANT_SIGN_UNSIGN", eshopInfo.getOtypeId());
                    String subValue = GlobalConfig.get(subName);
                    if (StringUtils.isEmpty(subValue) || !"1".equals(subValue)) {
                        continue;
                    }
                }
                EshopBaseInfoUploadFeature feature = null;
                try {
                    EshopSystemParams systemParams = CommonUtil.toSystemParams(eshopInfo);
                    ShopType shopType = systemParams.getShopType();
                    EshopFactory factory = EshopFactoryManager.create(shopType, systemParams);
                    feature = factory.getFeature(EshopBaseInfoUploadFeature.class);
                } catch (Exception e) {
                    continue;
                }

                if (feature == null) {
                    continue;
                }
                boolean init = checkInit(eshopInfo);
                if (init) {
                    doInitSync(profileId, eshopInfo, feature);
                    doUpdateSyncDate(PlatformToolConst.AX_SYNC_INIT_DATE, eshopInfo);
                    continue;
                }
                syncInventory(profileId, feature);
                doIncreaseSync(profileId, eshopInfo, feature);
            }
        } catch (Exception ex) {
            logger.error("账套{}执行翱象同步报错{}", profileId, ex.getMessage(), ex);
        }
    }

    private boolean checkInit(EshopInfo eshopInfo) {
        try {
            String key = String.format("%s_%s", PlatformToolConst.AX_SYNC_INIT_DATE, eshopInfo.getOtypeId());
            String initStr = GlobalConfig.get(key);
            return StringUtils.isEmpty(initStr);
        } catch (Exception ignore) {
            return false;
        }
    }

    private void doUpdateSyncDate(String preKey, EshopInfo eshopInfo) {
        try {
            String key = String.format("%s_%s", preKey, eshopInfo.getOtypeId());
            String formatDate = DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss");
            GlobalConfig.put(key, formatDate);
        } catch (Exception ex) {
            logger.error("账套{}网店{}保存同步时间报错：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), ex.getMessage(), ex);
        }
    }

    private void syncInventory(BigInteger profileId, EshopBaseInfoUploadFeature feature) {
        QueryModifiedBaseInfoRequest parameter = new QueryModifiedBaseInfoRequest();
        parameter.setProfileId(profileId);
        Date lastTime = DateUtils.addMinutes(DateUtils.getDate(), -config.getAxSyncInterval());
        parameter.setModifyDate(lastTime);
        doSyncInventory(profileId, parameter, feature);
    }

    private void doSyncInventory(BigInteger profileId, QueryModifiedBaseInfoRequest parameter, EshopBaseInfoUploadFeature feature) {
        try {
            List<InventoryEntity> inventoryList = mapper.queryInventoryList(parameter);
            if (CollectionUtils.isEmpty(inventoryList)) {
                return;
            }
            UpdateInventoryRequest request = new UpdateInventoryRequest();
            request.setInventoryEntityList(inventoryList);
            BaseUploadResponse response = feature.updateInventory(request);
            if (!response.getSuccess()) {
                logger.error("账套ID{}翱象同步库存报错：{}", profileId, response.getMessage());
            }
        } catch (Exception ex) {
            logger.error("账套ID{}翱象同步库存报错：{}", profileId, ex.getMessage(), ex);
        }
    }

    private void doIncreaseSync(BigInteger profileId, EshopInfo eshopInfo, EshopBaseInfoUploadFeature feature) {
        boolean canSync = checkCanSync(eshopInfo);
        if (!canSync) {
            return;
        }
        try {
            Date lastTime = DateUtils.addMinutes(DateUtils.getDate(), -config.getAxSyncInterval());
            QueryModifiedBaseInfoRequest parameter = new QueryModifiedBaseInfoRequest();
            parameter.setProfileId(profileId);
            parameter.setModifyDate(lastTime);
            parameter.setEshopId(eshopInfo.getOtypeId());
            doSync(profileId, eshopInfo, parameter, feature);
            doUpdateSyncDate(PlatformToolConst.AX_SYNC_LAST_DATE, eshopInfo);
        } catch (Exception ex) {
            logger.error("账套ID{}翱象增量同步基础信息报错：{}", profileId, ex.getMessage(), ex);
        }
    }

    private void doInitSync(BigInteger profileId, EshopInfo eshopInfo, EshopBaseInfoUploadFeature feature) {
        try {
            QueryModifiedBaseInfoRequest parameter = new QueryModifiedBaseInfoRequest();
            parameter.setProfileId(profileId);
            parameter.setEshopId(eshopInfo.getOtypeId());
            doSync(profileId, eshopInfo, parameter, feature);
            doSyncInventory(profileId, parameter, feature);
        } catch (Exception ex) {
            logger.error("翱象初始化基本信息报错:{}", ex.getMessage(), ex);
        }
    }

    private void doSync(BigInteger profileId, EshopInfo eshopInfo, QueryModifiedBaseInfoRequest parameter, EshopBaseInfoUploadFeature feature) {
        try {
            doSyncKtype(profileId, parameter, feature);
        } catch (Exception e) {
            logger.error("账套ID: " + profileId + "店铺ID:" + eshopInfo.getOtypeId() + "翱翔同步仓库报错:" + e.getMessage());
        }

        try {
            doSyncLogistics(parameter, eshopInfo, feature);
        } catch (Exception e) {
            logger.error("账套ID: " + profileId + "店铺ID:" + eshopInfo.getOtypeId() + "翱翔同步物流公司报错:" + e.getMessage());
        }

        try {
            doSyncPtype(profileId, parameter, feature);
        } catch (Exception e) {
            logger.error("账套ID: " + profileId + "店铺ID:" + eshopInfo.getOtypeId() + "翱翔同步商品信息报错:" + e.getMessage());
        }

        try {
            doSyncCombo(profileId, parameter, feature);
        } catch (Exception e) {
            logger.error("账套ID: " + profileId + "店铺ID:" + eshopInfo.getOtypeId() + "翱翔同步套餐报错:" + e.getMessage());
        }

        try {
            int relationCount = mapper.queryPtypeRelationCount(parameter);
            int comboCount = mapper.queryPtypeRelationForComboCount(parameter);
            syncPtypeRelation(parameter, feature, relationCount);
            syncPtypeRelationForCombo(parameter, feature, comboCount);
        } catch (Exception e) {
            logger.error("账套ID: " + profileId + "店铺ID:" + eshopInfo.getOtypeId() + "翱翔同步商品对应关系报错:" + e.getMessage());
        }
    }

    private void doSyncKtype(BigInteger profileId, QueryModifiedBaseInfoRequest parameter, EshopBaseInfoUploadFeature feature) {
        List<BaseWareHouseInfo> wareHouseInfos = mapper.queryWarehouseList(parameter);
        if (CollectionUtils.isEmpty(wareHouseInfos)) {
            return;
        }
        doFilterWarehouseContactor(profileId, wareHouseInfos);
        UploadKtypeListRequest request = new UploadKtypeListRequest();
        request.setKtypeList(wareHouseInfos);
        UploadKtypeResponse ktypeResponse = feature.uploadKtypeList(request);
        if (!ktypeResponse.getSuccess()) {
            logger.error("账套{}翱象同步仓库报错{}", profileId, ktypeResponse.getMessage());
        }
    }

    private void doFilterWarehouseContactor(BigInteger profileId, List<BaseWareHouseInfo> wareHouseInfos) {
        Map<BigInteger, BaseWareHouseInfo> listMap = new HashMap<>();
        for (BaseWareHouseInfo wareHouseInfo : wareHouseInfos) {
            listMap.put(wareHouseInfo.getKtypeId(), wareHouseInfo);
        }
        List<BigInteger> ktypeIds = new ArrayList<>(listMap.keySet());
        List<Contactor> contactorList = mapper.queryKtypeContactor(profileId, ktypeIds);
        if (CollectionUtils.isEmpty(contactorList)) {
            return;
        }
        for (Contactor contactor : contactorList) {
            BigInteger ktypeId = contactor.getKtypeId();
            if (!listMap.containsKey(ktypeId)) {
                continue;
            }
            BaseWareHouseInfo baseWareHouse = listMap.get(ktypeId);
            baseWareHouse.setPostCode(contactor.getZipcode());
            baseWareHouse.setAddress(contactor.getDetailAddress());
            baseWareHouse.setDistrict(contactor.getDistrict());
            baseWareHouse.setProvince(contactor.getProvince());
            baseWareHouse.setCity(contactor.getCity());
            baseWareHouse.setTown(contactor.getTown());
            baseWareHouse.getContactors().add(contactor);
        }
    }

    private void doSyncLogistics(QueryModifiedBaseInfoRequest parameter, EshopInfo eshopInfo, EshopBaseInfoUploadFeature feature) {
        List<LogisticsCompanyInfo> logisticsList = mapper.queryLogisticsList(parameter);
        if (CollectionUtils.isEmpty(logisticsList)) {
            return;
        }
        PlatformEshopConfig platformEshopConfig = BeanUtils.getBean(PlatformEshopConfig.class);
        String comps = platformEshopConfig.getFreight().get(eshopInfo.getEshopType().toString().toLowerCase());
        List<FreightMapping> freightMappings = JsonUtils.toList(comps, FreightMapping.class);
        for (LogisticsCompanyInfo logisticsCompanyInfo : logisticsList) {
            Optional<FreightMapping> mapping = freightMappings.stream().filter(freightMapping -> {
                return logisticsCompanyInfo.getErpLogisticsCompanyCode().contains(freightMapping.getLocalCode());
            }).findFirst();
            if (mapping.isPresent()) {
                logisticsCompanyInfo.setOnlineCompanyCode(mapping.get().getOnlineCode());
                logisticsCompanyInfo.setOnlineCompanyName(mapping.get().getOnlineName());
            }
        }

        UploadLogisticsListRequest request = new UploadLogisticsListRequest();
        request.setLogisticsCompanyList(logisticsList);
        BaseUploadResponse res = feature.uploadLogisticsList(request);
        if (!res.getSuccess()) {
            logger.error("翱象同步物流公司报错{}", res.getMessage());
        }
    }

    private void doSyncCombo(BigInteger profileId, QueryModifiedBaseInfoRequest parameter, EshopBaseInfoUploadFeature feature) {
        List<ComboInfo> comboList = mapper.queryComboList(parameter);
        if (CollectionUtils.isEmpty(comboList)) {
            return;
        }
        doSyncDeletedCombo(feature, comboList);
        List<ComboInfo> syncList = comboList.stream().filter(x -> !x.isStoped() && !x.isDeleted()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncList)) {
            return;
        }
        doFilterComboDetail(profileId, syncList);
        UploadComboListRequest request = new UploadComboListRequest();
        request.setComboInfoList(syncList);
        BaseUploadResponse response = feature.uploadComboList(request);
        if (!response.getSuccess()) {
            logger.error("账套{}同步套餐到翱象报错:{}", profileId, response.getMessage());
        }
    }

    private void doSyncDeletedCombo(EshopBaseInfoUploadFeature feature, List<ComboInfo> comboList) {
        try {
            List<ComboInfo> deletedList = comboList.stream().filter(x -> x.isDeleted() || x.isStoped()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deletedList)) {
                return;
            }
            List<String> comboIds = deletedList.stream().map(x -> x.getComboId().toString()).collect(Collectors.toList());
            DeleteComboListRequest request = new DeleteComboListRequest();
            request.setComboIdList(comboIds);
            feature.deleteComboList(request);
        } catch (Exception ex) {
            logger.error("翱象删除套餐报错:{}", ex.getMessage(), ex);
        }
    }

    private void doFilterComboDetail(BigInteger profileId, List<ComboInfo> syncList) {
        List<BigInteger> comboIds = syncList.stream().map(ComboInfo::getComboId).collect(Collectors.toList());
        List<ComboDetail> comboDetails = mapper.queryComboDetails(profileId, comboIds);
        if (CollectionUtils.isEmpty(comboDetails)) {
            return;
        }
        Map<BigInteger, List<ComboDetail>> listMap = comboDetails.stream().collect(Collectors.groupingBy(ComboDetail::getComboId));
        for (ComboInfo comboInfo : syncList) {
            BigInteger comboId = comboInfo.getComboId();
            if (!listMap.containsKey(comboId)) {
                continue;
            }
            List<ComboDetail> details = listMap.get(comboId);
            comboInfo.setComboDetailList(details);
        }
    }

    private void doSyncPtype(BigInteger profileId, QueryModifiedBaseInfoRequest parameter, EshopBaseInfoUploadFeature feature) {
        List<PtypeUploadEntity> ptypeList = mapper.queryPtypeUpload(parameter);
        if (CollectionUtils.isEmpty(ptypeList)) {
            return;
        }
        if (parameter.getModifyDate() != null) {
            doSyncDeletePtype(feature, ptypeList);
        }
        List<PtypeUploadEntity> syncList = ptypeList.stream().filter(x -> !x.isStop() && !x.isDeleted()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncList)) {
            return;
        }
        doFilterPtypeInfo(profileId, syncList);
        UploadPtypeListRequest request = new UploadPtypeListRequest();
        request.setPtypeUploadEntityList(syncList);
        BaseUploadResponse response = feature.uploadPtypeList(request);
        if (!response.getSuccess()) {
            logger.error("翱象同步商品信息报错{}", response.getMessage());
        }
    }

    private void doSyncDeletePtype(EshopBaseInfoUploadFeature feature, List<PtypeUploadEntity> ptypeList) {
        try {
            List<PtypeUploadEntity> deletedList = ptypeList.stream().filter(x -> x.isStop() || x.isDeleted()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deletedList)) {
                return;
            }
            List<String> unitSkuList = deletedList.stream().map(PtypeUploadEntity::getUnitSkuId).collect(Collectors.toList());
            DeletePtypeRequest request = new DeletePtypeRequest();
            request.setUnitSkuIdList(unitSkuList);
            BaseUploadResponse response = feature.deletePtypeList(request);
            if (!response.getSuccess()) {
                throw new RuntimeException(response.getMessage());
            }
        } catch (Exception ex) {
            logger.error("删除翱象商品同步报错：{}", ex.getMessage(), ex);
        }
    }

    private void doFilterPtypeInfo(BigInteger profileId, List<PtypeUploadEntity> ptypeList) {
        List<BigInteger> ptypeIds = ptypeList.stream().map(PtypeUploadEntity::getPtypeId).collect(Collectors.toList());
        List<ItemKtypeRelation> itemKtypeRelations = mapper.queryKtypeRelation(profileId, ptypeIds);
        if (CollectionUtils.isEmpty(itemKtypeRelations)) {
            return;
        }
        Map<BigInteger, List<ItemKtypeRelation>> listMap = itemKtypeRelations.stream().collect(Collectors.groupingBy(ItemKtypeRelation::getPtypeId));
        for (PtypeUploadEntity ptype : ptypeList) {
            BigInteger ptypeId = ptype.getPtypeId();
            if (!listMap.containsKey(ptypeId)) {
                continue;
            }
            List<ItemKtypeRelation> relations = listMap.get(ptypeId);
            ptype.setKtypeMapping(relations);
        }
    }

    private void syncPtypeRelation(QueryModifiedBaseInfoRequest parameter, EshopBaseInfoUploadFeature feature, int total) {
        try {
            if(total<=0){
                return;
            }
            int pageSize = config.getAxQueryPageSize();
            for (int i = 0; i < total; i += pageSize) {
                QueryModifiedBaseInfoRequest request = new QueryModifiedBaseInfoRequest();
                request.setEshopId(parameter.getEshopId());
                request.setProfileId(parameter.getProfileId());
                request.setPageIndex(i / pageSize + 1);
                request.setPageSize(pageSize);
                List<PtypeRelationMapping> pageData = mapper.queryPtypeRelation(request);
                doSyncPtypeRelation(parameter, feature, pageData);
            }
        } catch (Exception ex) {
            logger.error("账套{}网店{}同步翱象商品对应关系报错:{}", parameter.getProfileId(), parameter.getEshopId(), ex.getMessage(), ex);
        }
    }

    private void syncPtypeRelationForCombo(QueryModifiedBaseInfoRequest parameter, EshopBaseInfoUploadFeature feature, int total) {
        try {
            if(total<=0){
                return;
            }
            int pageSize = config.getAxQueryPageSize();
            for (int i = 0; i < total; i += pageSize) {
                QueryModifiedBaseInfoRequest request = new QueryModifiedBaseInfoRequest();
                request.setEshopId(parameter.getEshopId());
                request.setProfileId(parameter.getProfileId());
                request.setPageIndex(i / pageSize + 1);
                request.setPageSize(pageSize);
                List<PtypeRelationMapping> list = mapper.queryPtypeRelationForCombo(request);
                doSyncPtypeRelation(parameter, feature, list);
            }
        } catch (Exception ex) {
            logger.error("账套{}网店{}同步翱象商品对应关系报错:{}", parameter.getProfileId(), parameter.getEshopId(), ex.getMessage(), ex);
        }
    }

    private void doSyncPtypeRelation(QueryModifiedBaseInfoRequest parameter, EshopBaseInfoUploadFeature feature, List<PtypeRelationMapping> mappingList ){
        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }
        if (parameter.getModifyDate() != null) {
            doDeletedMappingSync(mappingList, feature);
        }
        List<PtypeRelationMapping> syncList = mappingList.stream().filter(PtypeRelationMapping::getMappingEnabled).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncList)) {
            return;
        }
        SyncRelationMappingRequest request = new SyncRelationMappingRequest();
        request.setRelationMappingList(syncList);
        feature.syncPtypeRelationMapping(request);
    }

    private void doDeletedMappingSync(List<PtypeRelationMapping> mappingList, EshopBaseInfoUploadFeature feature) {
        List<PtypeRelationMapping> syncList = mappingList.stream().filter(x -> !x.getMappingEnabled()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncList)) {
            return;
        }
        DeletRelationMappingRequest request = new DeletRelationMappingRequest();
        request.setRelationMappingList(syncList);
        feature.deletePtypeRelationMapping(request);
    }

    private boolean checkCanSync(EshopInfo eshopInfo) {
        try {
            String key = String.format("%s_%s", PlatformToolConst.AX_SYNC_LAST_DATE, eshopInfo.getOtypeId());
            String lastDateStr = GlobalConfig.get(key);
            int interval = config.getAxSyncInterval();
            if (StringUtils.isEmpty(lastDateStr)) {
                return true;
            }
            Date date = DateUtils.parseDate(lastDateStr, "yyyy-MM-dd HH:mm:ss");
            Date nextTime = DateUtils.addMinutes(date, interval);
            Date nowTime = DateUtils.getDate();
            return nowTime.after(nextTime);
        } catch (Exception ex) {
            logger.error("账套{}网店{}查询是否可以执行翱象同步时报错：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), ex.getMessage(), ex);
            return false;
        }
    }
}
