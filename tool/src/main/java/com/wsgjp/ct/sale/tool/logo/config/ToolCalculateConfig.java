package com.wsgjp.ct.sale.tool.logo.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> csh
 * @create 2023-06-16 10:37
 */
@Configuration
@ConfigurationProperties(prefix = "sale-tool-calculate")
public class ToolCalculateConfig {
    /**
     * 是否生产账套，默认开启（部署级别）
     */
    private boolean enabled = true;

    private boolean producerEnabled = true;

    private boolean businessProducerEnable = true;

    public boolean isBusinessProducerEnable() {
        return businessProducerEnable;
    }

    public void setBusinessProducerEnable(boolean businessProducerEnable) {
        this.businessProducerEnable = businessProducerEnable;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isProducerEnabled() {
        return producerEnabled;
    }

    public void setProducerEnabled(boolean producerEnabled) {
        this.producerEnabled = producerEnabled;
    }
}
