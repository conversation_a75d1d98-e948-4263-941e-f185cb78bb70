package com.wsgjp.ct.sale.tool.tmc.impl.meituan;

import com.alibaba.fastjson.JSON;
import com.wsgjp.ct.sale.tool.tmc.impl.eleme.ElemeRetailNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.MeiTuanBaseResponse;
import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 */
public abstract class MeiTuanNotifyBase extends ElemeRetailNotifyBase {

    public static String buildResponse(String errMsg) {
        return JSON.toJSONString(buildResponseObj(errMsg));
    }

    public static MeiTuanBaseResponse buildResponseObj(String errMsg) {
        MeiTuanBaseResponse response = new MeiTuanBaseResponse();
        if (StringUtils.isEmpty(errMsg)) {
            response.setData("ok");
        } else {
            response.setData(errMsg);
        }
        return response;
    }
}
