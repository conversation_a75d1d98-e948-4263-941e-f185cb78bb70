package com.wsgjp.ct.sale.tool.logo.service.base;

import com.wsgjp.ct.sale.biz.common.entity.LogoExceptionState;
import com.wsgjp.ct.sale.sdk.logo.state.LogoExceptionStatusEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoSourceTypeEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoUsePhaseEnum;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-03-14 16:57
 */
public interface BaseLogoComputed<T extends BaseLogoEntity> {
    //获取本身的来源
    LogoSourceTypeEnum getSourceType();
    //获取来源类型-写入change表
//    LogoSourceTypeEnum getSourceTypeWriteChange();
    //获取来源类型-写入state表
//    LogoSourceTypeEnum getSourceTypeWriteState();
    //查询所有热表vchcode
    List<BigInteger> getAllVchcodeBatch(BigInteger profileId,int pageIndex,int pageCount);
    List<BigInteger> getAllVchcodeBatchDay(BigInteger profileId,int pageIndex,int pageCount,String startTime,String endTime);
    //查询订单数据
    List<T> getLogoData(BigInteger profileId, List<BigInteger> vchcodeList);
    //获取需要计算的异常枚举列表
    List<LogoExceptionStatusEnum> getLogoStateList(LogoUsePhaseEnum logoUsePhase);
    //获取该订单需要在那些界面显示
    List<LogoUsePhaseEnum> getLogoUsePhase(T logoData);

    //重算
    void computed(List<LogoExceptionState> result, List<T> logoDataList, LogoSourceTypeEnum sourceType);
}
