package com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.List;

public class Order {
    private Long platformID;
    private Long companyID;
    private String thirdCustCode;
    private String companyName;
    private Long orderState;
    private BigDecimal originalOrderAmount;
    private BigDecimal discountSumFee;
    private BigDecimal freightAmount;
    private BigDecimal orderAmount;
    private String orderNote;
    private Long payWay;
    private Long payChannel;
    private Long payTime;
    private String payTrxID;
    private String consigneeMobile;
    private String purchaserLinkPhone;
    private String consigneeAddress;
    private String consigneeName;
    private String consigneeProvince;
    private String consigneeProvinceCode;
    private String consigneeCity;
    private String consigneeCityCode;
    private String consigneeArea;
    private String consigneeAreaCode;
    private String orderCode;
    private Long orderTime;
    private String stampsType;
    private List<OrderDetailList> orderDetailList;

    @JsonProperty("platformId")
    public Long getPlatformID() { return platformID; }
    @JsonProperty("platformId")
    public void setPlatformID(Long value) { this.platformID = value; }

    @JsonProperty("companyId")
    public Long getCompanyID() { return companyID; }
    @JsonProperty("companyId")
    public void setCompanyID(Long value) { this.companyID = value; }

    @JsonProperty("thirdCustCode")
    public String getThirdCustCode() { return thirdCustCode; }
    @JsonProperty("thirdCustCode")
    public void setThirdCustCode(String value) { this.thirdCustCode = value; }

    @JsonProperty("companyName")
    public String getCompanyName() { return companyName; }
    @JsonProperty("companyName")
    public void setCompanyName(String value) { this.companyName = value; }

    @JsonProperty("orderState")
    public Long getOrderState() { return orderState; }
    @JsonProperty("orderState")
    public void setOrderState(Long value) { this.orderState = value; }

    @JsonProperty("originalOrderAmount")
    public BigDecimal getOriginalOrderAmount() { return originalOrderAmount; }
    @JsonProperty("originalOrderAmount")
    public void setOriginalOrderAmount(BigDecimal value) { this.originalOrderAmount = value; }

    @JsonProperty("discountSumFee")
    public BigDecimal getDiscountSumFee() { return discountSumFee; }
    @JsonProperty("discountSumFee")
    public void setDiscountSumFee(BigDecimal value) { this.discountSumFee = value; }

    @JsonProperty("freightAmount")
    public BigDecimal getFreightAmount() { return freightAmount; }
    @JsonProperty("freightAmount")
    public void setFreightAmount(BigDecimal value) { this.freightAmount = value; }

    @JsonProperty("orderAmount")
    public BigDecimal getOrderAmount() { return orderAmount; }
    @JsonProperty("orderAmount")
    public void setOrderAmount(BigDecimal value) { this.orderAmount = value; }

    @JsonProperty("orderNote")
    public String getOrderNote() { return orderNote; }
    @JsonProperty("orderNote")
    public void setOrderNote(String value) { this.orderNote = value; }

    @JsonProperty("payWay")
    public Long getPayWay() { return payWay; }
    @JsonProperty("payWay")
    public void setPayWay(Long value) { this.payWay = value; }

    @JsonProperty("payChannel")
    public Long getPayChannel() { return payChannel; }
    @JsonProperty("payChannel")
    public void setPayChannel(Long value) { this.payChannel = value; }

    @JsonProperty("payTime")
    public Long getPayTime() { return payTime; }
    @JsonProperty("payTime")
    public void setPayTime(Long value) { this.payTime = value; }

    @JsonProperty("payTrxId")
    public String getPayTrxID() { return payTrxID; }
    @JsonProperty("payTrxId")
    public void setPayTrxID(String value) { this.payTrxID = value; }

    @JsonProperty("consigneeMobile")
    public String getConsigneeMobile() { return consigneeMobile; }
    @JsonProperty("consigneeMobile")
    public void setConsigneeMobile(String value) { this.consigneeMobile = value; }

    @JsonProperty("purchaserLinkPhone")
    public String getPurchaserLinkPhone() { return purchaserLinkPhone; }
    @JsonProperty("purchaserLinkPhone")
    public void setPurchaserLinkPhone(String value) { this.purchaserLinkPhone = value; }

    @JsonProperty("consigneeAddress")
    public String getConsigneeAddress() { return consigneeAddress; }
    @JsonProperty("consigneeAddress")
    public void setConsigneeAddress(String value) { this.consigneeAddress = value; }

    @JsonProperty("consigneeName")
    public String getConsigneeName() { return consigneeName; }
    @JsonProperty("consigneeName")
    public void setConsigneeName(String value) { this.consigneeName = value; }

    @JsonProperty("consigneeProvince")
    public String getConsigneeProvince() { return consigneeProvince; }
    @JsonProperty("consigneeProvince")
    public void setConsigneeProvince(String value) { this.consigneeProvince = value; }

    @JsonProperty("consigneeProvinceCode")
    public String getConsigneeProvinceCode() { return consigneeProvinceCode; }
    @JsonProperty("consigneeProvinceCode")
    public void setConsigneeProvinceCode(String value) { this.consigneeProvinceCode = value; }

    @JsonProperty("consigneeCity")
    public String getConsigneeCity() { return consigneeCity; }
    @JsonProperty("consigneeCity")
    public void setConsigneeCity(String value) { this.consigneeCity = value; }

    @JsonProperty("consigneeCityCode")
    public String getConsigneeCityCode() { return consigneeCityCode; }
    @JsonProperty("consigneeCityCode")
    public void setConsigneeCityCode(String value) { this.consigneeCityCode = value; }

    @JsonProperty("consigneeArea")
    public String getConsigneeArea() { return consigneeArea; }
    @JsonProperty("consigneeArea")
    public void setConsigneeArea(String value) { this.consigneeArea = value; }

    @JsonProperty("consigneeAreaCode")
    public String getConsigneeAreaCode() { return consigneeAreaCode; }
    @JsonProperty("consigneeAreaCode")
    public void setConsigneeAreaCode(String value) { this.consigneeAreaCode = value; }

    @JsonProperty("orderCode")
    public String getOrderCode() { return orderCode; }
    @JsonProperty("orderCode")
    public void setOrderCode(String value) { this.orderCode = value; }

    @JsonProperty("orderTime")
    public Long getOrderTime() { return orderTime; }
    @JsonProperty("orderTime")
    public void setOrderTime(Long value) { this.orderTime = value; }

    @JsonProperty("stampsType")
    public String getStampsType() { return stampsType; }
    @JsonProperty("stampsType")
    public void setStampsType(String value) { this.stampsType = value; }

    @JsonProperty("orderDetailList")
    public List<OrderDetailList> getOrderDetailList() { return orderDetailList; }
    @JsonProperty("orderDetailList")
    public void setOrderDetailList(List<OrderDetailList> value) { this.orderDetailList = value; }


    public static class OrderDetailList {
        private Long itemStoreID;
        private String prodNo;
        private BigDecimal orderNumber;
        private BigDecimal settlementPrice;
        private BigDecimal rowPrice;

        @JsonProperty("itemStoreId")
        public Long getItemStoreID() {
            return itemStoreID;
        }

        @JsonProperty("itemStoreId")
        public void setItemStoreID(Long value) {
            this.itemStoreID = value;
        }

        @JsonProperty("prodNo")
        public String getProdNo() {
            return prodNo;
        }

        @JsonProperty("prodNo")
        public void setProdNo(String value) {
            this.prodNo = value;
        }

        @JsonProperty("orderNumber")
        public BigDecimal getOrderNumber() {
            return orderNumber;
        }

        @JsonProperty("orderNumber")
        public void setOrderNumber(BigDecimal value) {
            this.orderNumber = value;
        }

        @JsonProperty("settlementPrice")
        public BigDecimal getSettlementPrice() {
            return settlementPrice;
        }

        @JsonProperty("settlementPrice")
        public void setSettlementPrice(BigDecimal value) {
            this.settlementPrice = value;
        }

        @JsonProperty("rowPrice")
        public BigDecimal getRowPrice() {
            return rowPrice;
        }

        @JsonProperty("rowPrice")
        public void setRowPrice(BigDecimal value) {
            this.rowPrice = value;
        }
    }
}