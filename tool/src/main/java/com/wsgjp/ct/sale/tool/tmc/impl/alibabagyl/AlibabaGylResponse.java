package com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl;

public class AlibabaGylResponse {
    private Boolean success;
    private String errorCode;
    private String errorMsg;
    private Boolean retry;

    public AlibabaGylResponse() {
    }

    public AlibabaGylResponse(Boolean success, String errorCode, String errorMsg, Boolean retry) {
        this.success = success;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.retry = retry;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Boolean getRetry() {
        return retry;
    }

    public void setRetry(Boolean retry) {
        this.retry = retry;
    }
}
