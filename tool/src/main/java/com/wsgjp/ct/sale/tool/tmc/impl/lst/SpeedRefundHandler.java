package com.wsgjp.ct.sale.tool.tmc.impl.lst;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.common.enums.core.enums.tmc.TmcNotifyResponseEnum;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopRefundService;
import com.wsgjp.ct.sale.biz.bifrost.service.impl.BifrostEshopRefundServiceImpl;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.platform.entity.request.refund.RefundBackNoticeRequest;
import com.wsgjp.ct.sale.platform.entity.response.NormalResponse;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import ngp.utils.JsonUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 零售通急速退款
 */
@Component
public class SpeedRefundHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(SpeedRefundHandler.class);

    private final TmcNotifyProxy notifyProxy;

    public SpeedRefundHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String message = invokeMessage.getMessage();
        LstRefundInfo refundInfo = JsonUtils.toObject(message, LstRefundInfo.class);
        TmcInvokeRequest request = buildInvokeRequest(invokeMessage, refundInfo);
        TmcInvokeResponse resp = notifyProxy.execute(request);
        //是否需要退款
        boolean isNeedRefund = TmcNotifyResponseEnum.SUCCESS.getCode().equals(resp.getCode());
        BifrostEshopRefundService bifrostSvc = GetBeanUtil.getBean(BifrostEshopRefundServiceImpl.class);
        RefundBackNoticeRequest backNoticeRequest = buildBackNoticeRequest(invokeMessage, refundInfo, isNeedRefund);
        NormalResponse response = bifrostSvc.backNotice(backNoticeRequest);
        LOGGER.debug("零售通急速退款回告结果：" + JsonUtils.toJson(response));
        return "";
    }

    @NotNull
    private TmcInvokeRequest buildInvokeRequest(InvokeMessageEntity invokeMessage, LstRefundInfo refundInfo) {
        TmcInvokeRequest request = new TmcInvokeRequest();

        request.setEshopId(invokeMessage.getEshopId());
        request.setTradeId(refundInfo.getMainOrderId());
        request.setMessage(invokeMessage.getMessage());
        request.setMethod(TmcNotifyMethodEnum.AG);
        return request;
    }

    @Override
    public String serviceName() {
        return "lst";
    }

    private RefundBackNoticeRequest buildBackNoticeRequest(InvokeMessageEntity invokeMessage, LstRefundInfo refundInfo, boolean isNeedRefund) {
        RefundBackNoticeRequest backNoticeRequest = new RefundBackNoticeRequest();
        backNoticeRequest.setShopId(invokeMessage.getEshopId());
        backNoticeRequest.setShopType(ShopType.Lst);
        backNoticeRequest.setRefundId(refundInfo.getRefundId());
        backNoticeRequest.setTradeId(refundInfo.getMainOrderId());
        backNoticeRequest.setoId(refundInfo.getSubOrderIds());
        //未收货时，可以退款，已收货不能退款
        backNoticeRequest.setWarehouseStatus(isNeedRefund ? 0 : 1);
        return backNoticeRequest;
    }
}
