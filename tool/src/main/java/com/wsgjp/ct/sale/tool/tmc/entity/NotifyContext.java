package com.wsgjp.ct.sale.tool.tmc.entity;

import com.wsgjp.ct.sale.common.enums.TMCType;

/**
 * <AUTHOR>
 */
public class NotifyContext {
    private String userNick;
    private String notifyId;
    private String content;
    private String topic;
    private com.wsgjp.ct.sale.common.enums.TMCType TMCType;


    public String getUserNick() {
        return userNick;
    }

    public void setUserNick(String userNick) {
        this.userNick = userNick;
    }

    public String getNotifyId() {
        return notifyId;
    }

    public void setNotifyId(String notifyId) {
        this.notifyId = notifyId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public TMCType getNotifyType() {
        return TMCType;
    }

    public void setNotifyType(TMCType TMCType) {
        this.TMCType = TMCType;
    }
}
