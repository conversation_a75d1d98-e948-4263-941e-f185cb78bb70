package com.wsgjp.ct.sale.tool.tmc.impl.eleme.utils;

import com.google.common.base.Joiner;
import com.wsgjp.ct.sale.tool.tmc.impl.eleme.entity.ElemeRetailTmcBaseResponse;

import java.util.HashMap;
import java.util.Map;

public class ElemeRetailUtils {
    public static String signMethod(ElemeRetailTmcBaseResponse data, String secret){
        Map<String, Object> params = new HashMap<>();
        params.put("timestamp",data.getTimestamp());
        params.put("version",data.getVersion());
        params.put("ticket",data.getTicket());
        params.put("source",data.getSource());
        params.put("secret",secret);
        params.put("cmd",data.getCmd());
        //在body中添加需要输入的参数
        params.put("body", data.getBody());
        //sort排序后的参数
        Map<String, Object> paramsMap = MapSortUtil.sortMapByKey(params);
        //用&拼接成字符串
        String bodySign = Joiner.on("&").withKeyValueSeparator("=").join(paramsMap);
        //md5工具类
        return MD5Utils.getMd5(bodySign);
    }
}
