package com.wsgjp.ct.sale.tool.tmc.impl.hema.entity;

import bf.datasource.typehandler.CodeEnum;

public enum HemaTopicEnum implements CodeEnum {
    /**
     * taobao_refund_RefundSuccess 退款成功
     * taobao_refund_RefundClosed   退款关闭
     * taobao_refund_RefundSellerAgreeAgreement   买家同意
     * taobao_refund_RefundSellerRefuseAgreement  卖家拒绝
     * taobao_refund_RefundCreated  退款创建
     */
    FulFillOrder_cancelSubOrder(1001,"FulFillOrder_cancelSubOrder","订单部分取消"),
    FulFillOrder_cancelMainOrder(1002,"FulFillOrder_cancelMainOrder","订单全部取消"),
    FulFillOrder_accepted(1003,"FulFillOrder_accepted","订单创建");
    private final int code;
    private final String topic;
    private final String desc;

    HemaTopicEnum(int code, String topic, String desc) {
        this.code = code;
        this.topic = topic;
        this.desc = desc;
    }
    @Override
    public int getCode() {
        return this.code;
    }

    public String getTopic() {
        return topic;
    }

    public String getDesc() {
        return desc;
    }
}
