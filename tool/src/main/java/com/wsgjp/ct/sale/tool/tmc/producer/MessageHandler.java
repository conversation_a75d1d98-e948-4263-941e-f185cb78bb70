package com.wsgjp.ct.sale.tool.tmc.producer;

import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Component
public interface MessageHandler {
    /**
     *
     *
     * @param invokeMessage@return 事件处理器
     */
    String invoker(InvokeMessageEntity invokeMessage);

    String serviceName();

    CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig);
}
