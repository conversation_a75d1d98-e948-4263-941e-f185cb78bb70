package com.wsgjp.ct.sale.tool.tmc.service;

import com.wsgjp.ct.sale.tool.tmc.producer.BaseMessageProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class ProducerUtil {

    private final List<BaseMessageProducer> producerList;

    @Autowired
    public ProducerUtil(List<BaseMessageProducer> producer) {
        this.producerList = producer;
    }

    public List<BaseMessageProducer> getProducerList() {
        return this.producerList;
    }
}
