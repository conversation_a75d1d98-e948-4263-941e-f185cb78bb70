package com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class HipacOrder {

    @XmlElement(name = "orderNum")
    public String orderNum;

    @XmlElement(name = "orderDate")
    public String orderDate;

    @XmlElement(name = "totalOrderAmount")
    public String totalOrderAmount;

    @XmlElement(name = "totalTaxAmount")
    public String totalTaxAmount;

    @XmlElement(name = "totalPayAmount")
    public String totalPayAmount;

    @XmlElement(name = "logisticsAmount")
    public String logisticsAmount;

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getTotalOrderAmount() {
        return totalOrderAmount;
    }

    public void setTotalOrderAmount(String totalOrderAmount) {
        this.totalOrderAmount = totalOrderAmount;
    }

    public String getTotalTaxAmount() {
        return totalTaxAmount;
    }

    public void setTotalTaxAmount(String totalTaxAmount) {
        this.totalTaxAmount = totalTaxAmount;
    }

    public String getTotalPayAmount() {
        return totalPayAmount;
    }

    public void setTotalPayAmount(String totalPayAmount) {
        this.totalPayAmount = totalPayAmount;
    }

    public String getLogisticsAmount() {
        return logisticsAmount;
    }

    public void setLogisticsAmount(String logisticsAmount) {
        this.logisticsAmount = logisticsAmount;
    }
}
