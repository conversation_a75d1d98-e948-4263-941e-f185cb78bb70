package com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark;

import com.doudian.open.spi.yunc_wms_cargo_single_push.param.YuncWmsCargoSinglePushParam;
import com.doudian.open.utils.JsonUtil;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.profile.ProfileApi;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopProductService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.RefreshProductResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.platform.entity.request.product.DownloadProductByTmcRequest;
import com.wsgjp.ct.sale.platform.entity.response.product.ProductDownloadResponse;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;

@Component
public class DouDianSupermarkProductHandler extends DDSNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DouDianSupermarkProductHandler.class);
    private final ProfileApi profileApi;
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;

    public DouDianSupermarkProductHandler(ProfileApi profileApi, EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.profileApi = profileApi;
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======抖店超市进入invoker方法======");
        String tmMessage = invokeMessage.getMessage();
        YuncWmsCargoSinglePushParam yuncWmsCargoSinglePushParam;
        try {
            yuncWmsCargoSinglePushParam = JsonUtil.fromJson(tmMessage, YuncWmsCargoSinglePushParam.class);
        } catch (Exception e) {
            String errMsg = String.format("抖店超市tmMessage数据转换成yuncWmsInboundCreateParam实体出错，错误信息：%s", e.getMessage());
            return JsonUtils.toJson(new GeneralResult(500L,"序列化失败",null));
        }
        try {
            EshopRegisterNotify notify = SupportUtil.buildNotify(yuncWmsCargoSinglePushParam.getWarehouseCode(), 148);
            saveTmcProductMsg(invokeMessage.getProfileId(), notify.getId(), tmMessage, yuncWmsCargoSinglePushParam);
        } catch (Exception ex) {
            LOGGER.error("错误",ex);
            LOGGER.error("抖店超市商品到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", invokeMessage.getProfileId(), invokeMessage.getEshopId(), tmMessage, ex.getMessage());
            return JsonUtils.toJson(new GeneralResult(500, "抖店超市订单数据保存数据库出错!", null));
        }
        return JsonUtils.toJson(new GeneralResult(0L,"success",null));
    }

    private void saveTmcProductMsg(BigInteger profileId, BigInteger id, String tmMessage, YuncWmsCargoSinglePushParam yuncWmsCargoSinglePushParam) {
        try {
            BifrostEshopProductService eshopProductService = GetBeanUtil.getBean(BifrostEshopProductService.class);
            DownloadProductByTmcRequest request = new DownloadProductByTmcRequest();
            request.setMessage(tmMessage);
            request.setShopId(id);
            request.setShopType(ShopType.DouDianSupermarket);
            ProductDownloadResponse response = eshopProductService.getProductByTmcMessage(request);
            EshopProductService productService = GetBeanUtil.getBean(EshopProductService.class);
            if (CollectionUtils.isEmpty(response.getProductList())){
                throw new RuntimeException("没有需要保存的商品");
            }
            RefreshProductResponse refreshProductResponse = productService.buildAndSaveProdcutForDoudian(ShopType.DouDianSupermarket, id, response.getProductList());
            if (StringUtils.isNotEmpty(refreshProductResponse.getMessage())){
                throw new RuntimeException(refreshProductResponse.getMessage());
            }
        }catch (Exception e){
            LOGGER.error("构建并保存抖店超市商品到数据库出错,profileId:{},eshopId:{},错误信息：{}", profileId, id,e.getMessage(),e);
            throw new RuntimeException(e.getMessage());
        }
    }


    @Override
    public String serviceName() {
        return "yunc.wms.cargo.single.push";
    }
}
