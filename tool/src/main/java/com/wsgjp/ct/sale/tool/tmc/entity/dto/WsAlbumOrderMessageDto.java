package com.wsgjp.ct.sale.tool.tmc.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class WsAlbumOrderMessageDto {
    private String pushType;
    private String saleOrderId;
    private String albumId;

    @JsonProperty("pushType")
    public String getPushType() {
        return pushType;
    }

    @JsonProperty("pushType")
    public void setPushType(String pushType) {
        this.pushType = pushType;
    }

    @JsonProperty("saleOrderId")
    public String getSaleOrderId() {
        return saleOrderId;
    }

    @JsonProperty("saleOrderId")
    public void setSaleOrderId(String saleOrderId) {
        this.saleOrderId = saleOrderId;
    }

    @JsonProperty("albumId")
    public String getAlbumId() {
        return albumId;
    }

    @JsonProperty("albumId")
    public void setAlbumId(String albumId) {
        this.albumId = albumId;
    }
}
