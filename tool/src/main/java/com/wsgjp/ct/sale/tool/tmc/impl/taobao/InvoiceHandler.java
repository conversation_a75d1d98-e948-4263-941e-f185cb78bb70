package com.wsgjp.ct.sale.tool.tmc.impl.taobao;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qimencloud.api.sceneqimen.response.TaobaoAlphaxOpenJxtInvoiceResponse;
import com.wsgjp.ct.common.enums.core.enums.tmc.TmcNotifyResponseEnum;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvoiceMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import ngp.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Map;

/**
 * 开发票
 * <AUTHOR>
 */
@Component
public class InvoiceHandler extends TaobaoNotifyBase {

    private final TmcNotifyProxy notifyProxy;

    public InvoiceHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String message = invokeMessage.getMessage();
        TaobaoAlphaxOpenJxtInvoiceResponse resp = new TaobaoAlphaxOpenJxtInvoiceResponse();
        TaobaoAlphaxOpenJxtInvoiceResponse.Data result = new TaobaoAlphaxOpenJxtInvoiceResponse.Data();

        Invoice invoice = JsonUtils.toObject(message, Invoice.class);

        Map<String, String> params = invokeMessage.getParams();


        TmcInvoiceMessage invoiceMessage = new TmcInvoiceMessage();
        invoiceMessage.setOrderId(params.get("order_id"));
        invoiceMessage.setSellerId(params.get("seller_id"));
        invoiceMessage.setSellerNick(params.get("seller_nick"));

        invoiceMessage.setInvoiceUserType(invoice.getInvoiceAttr());
        invoiceMessage.setInvoiceType(getInvoiceType(invoice.getInvoiceKind(), invoice.getInvoiceType()));
        invoiceMessage.setInvoiceTitle(invoice.getCompanyTitle());
        invoiceMessage.setInvoiceTax(invoice.getTaxNo());
        ExtendArg extendArg = invoice.getExtendArg();
        invoiceMessage.setBank(extendArg.getBank());
        invoiceMessage.setBanAccount(extendArg.getBankAccount());
        invoiceMessage.setPhone(extendArg.getRegisterPhone());
        invoiceMessage.setAddress(extendArg.getRegisterAddress());

        TmcInvokeRequest invokeRequest = new TmcInvokeRequest();
        invokeRequest.setEshopId(invokeMessage.getEshopId());
        invokeRequest.setTradeId(invoiceMessage.getOrderId());
        invokeRequest.setMethod(TmcNotifyMethodEnum.INVOICE);
        invokeRequest.setMessage(JsonUtils.toJson(invoiceMessage));
        TmcInvokeResponse invokeResponse = notifyProxy.execute(invokeRequest);
        if (TmcNotifyResponseEnum.SUCCESS.getCode().equals(invokeResponse.getCode())) {
            result.setSuccess(true);
            result.setData("亲，已经申请开票");
            result.setErrorCode("");
            result.setErrorMessage("");
        } else {
            result.setSuccess(false);
            result.setData(invokeResponse.getMessage());
            result.setErrorCode("999");
            result.setErrorMessage(invokeResponse.getMessage());
        }
        resp.setResult(result);
        return JsonUtils.toJson(resp);
    }

    private int getInvoiceType(int invoiceKind, int invoiceType) {
        // 电子普通
        if (invoiceKind == 1 && invoiceType == 1) {
            return 1;
        }
        // 电子增值
        if (invoiceKind == 1 && invoiceType == 2) {
            return 2;
        }
        // 纸质普通
        if (invoiceKind == 2 && invoiceType == 1) {
            return 3;
        }
        // 纸质增值
        if (invoiceKind == 2 && invoiceType == 2) {
            return 4;
        }
        return 0;
    }

    @Override
    public String serviceName() {
        return "invokedxm.invoice";
    }

    public static class Invoice implements Serializable {
        @JsonProperty("invoice_attr")
        private Integer invoiceAttr;
        @JsonProperty("invoice_kind")
        private Integer invoiceKind;
        @JsonProperty("company_title")
        private String companyTitle;
        @JsonProperty("tax_no")
        private String taxNo;
        @JsonProperty("invoice_type")
        private Integer invoiceType;
        @JsonProperty("extend_arg")
        private ExtendArg extendArg;

        public Integer getInvoiceAttr() {
            return invoiceAttr;
        }

        public void setInvoiceAttr(Integer invoiceAttr) {
            this.invoiceAttr = invoiceAttr;
        }

        public Integer getInvoiceKind() {
            return invoiceKind;
        }

        public void setInvoiceKind(Integer invoiceKind) {
            this.invoiceKind = invoiceKind;
        }

        public String getCompanyTitle() {
            return companyTitle;
        }

        public void setCompanyTitle(String companyTitle) {
            this.companyTitle = companyTitle;
        }

        public String getTaxNo() {
            return taxNo;
        }

        public void setTaxNo(String taxNo) {
            this.taxNo = taxNo;
        }

        public Integer getInvoiceType() {
            return invoiceType;
        }

        public void setInvoiceType(Integer invoiceType) {
            this.invoiceType = invoiceType;
        }

        public ExtendArg getExtendArg() {
            return extendArg;
        }

        public void setExtendArg(ExtendArg extendArg) {
            this.extendArg = extendArg;
        }
    }

    public static class ExtendArg implements Serializable {
        private String bank;
        @JsonProperty("bank_account")
        private String bankAccount;
        @JsonProperty("register_phone")
        private String registerPhone;
        @JsonProperty("register_address")
        private String registerAddress;

        public String getBank() {
            return bank;
        }

        public void setBank(String bank) {
            this.bank = bank;
        }

        public String getBankAccount() {
            return bankAccount;
        }

        public void setBankAccount(String bankAccount) {
            this.bankAccount = bankAccount;
        }

        public String getRegisterPhone() {
            return registerPhone;
        }

        public void setRegisterPhone(String registerPhone) {
            this.registerPhone = registerPhone;
        }

        public String getRegisterAddress() {
            return registerAddress;
        }

        public void setRegisterAddress(String registerAddress) {
            this.registerAddress = registerAddress;
        }
    }
}
