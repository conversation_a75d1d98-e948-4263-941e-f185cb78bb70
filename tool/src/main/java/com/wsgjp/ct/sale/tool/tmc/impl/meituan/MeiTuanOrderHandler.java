package com.wsgjp.ct.sale.tool.tmc.impl.meituan;

import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderRefundStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.dto.refund.EshopTmcRefundMsgDto;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.meituan.MeiTuanConfig;
import com.wsgjp.ct.sale.platform.factory.meituan.builder.MtRefundBuilder;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.GoodInfo;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.MeiTuanOrderMessage;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.MeiTuanTopicEnum;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MeiTuanOrderHandler extends MeiTuanNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(MeiTuanOrderHandler.class);
    private final EshopTmcRefundMsgService tmcRefundMsgService;
    private final EshopTmcUtils eshopTmcUtils;
    private final MeiTuanConfig meiTuanConfig;

    private static final Map<String, RefundStatus> LOCAL_REFUND_MAP = new HashMap<>();

    static {
        //            0-等待处理中；1-商家驳回退款请求；2-商家同意退款；
        //            3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请 8-用户取消退款申诉
        LOCAL_REFUND_MAP.put("0", RefundStatus.WAIT_SELLER_AGREE);
        LOCAL_REFUND_MAP.put("1", RefundStatus.CANCEL);
        LOCAL_REFUND_MAP.put("2", RefundStatus.SUCCESS);
        LOCAL_REFUND_MAP.put("3", RefundStatus.CANCEL);
        LOCAL_REFUND_MAP.put("4", RefundStatus.SUCCESS);
        LOCAL_REFUND_MAP.put("5", RefundStatus.SUCCESS);
        LOCAL_REFUND_MAP.put("6", RefundStatus.SUCCESS);
        LOCAL_REFUND_MAP.put("7", RefundStatus.CANCEL);
        LOCAL_REFUND_MAP.put("8", RefundStatus.CANCEL);
    }

    public MeiTuanOrderHandler(EshopTmcRefundMsgService tmcRefundMsgService, EshopTmcUtils eshopTmcUtils, MeiTuanConfig meiTuanConfig) {
        this.tmcRefundMsgService = tmcRefundMsgService;
        this.eshopTmcUtils = eshopTmcUtils;
        this.meiTuanConfig = meiTuanConfig;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        String tmMessage = invokeMessage.getMessage();
        MeiTuanOrderMessage order;
        try {
            order = JsonUtils.toObject(tmMessage, MeiTuanOrderMessage.class);
        } catch (Exception ex) {
            LOGGER.error("{}tmMessage数据转换成美团订单信息实体出错，错误信息：{}", shopTypeName, ex.getMessage(), ex);
            return buildResponse(ex.getMessage());
        }
        try {
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), invokeMessage.getEshopId());
            if (Objects.isNull(eshopInfo)) {
                LOGGER.error("profileId:{},店铺类型:{},appPoiCode:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, order.getAppPoiCode(), tmMessage);
                return buildResponse("管家婆未找到对应店铺!");
            }
            EshopNotifyChange change = new EshopNotifyChange();
            change.setContent(invokeMessage.getMessage());
            change.setTradeOrderId(order.getTradeId());
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(order.getAppPoiCode());
            change.setProfileId(invokeMessage.getProfileId());
            change.setEshopId(eshopInfo.getOtypeId());
            change.setCreateTime(new Date());
            change.setShopType(invokeMessage.getShopType().getCode());
            if (StringUtils.isNotBlank(order.getExceptionReason())) {
                //配送异常消息
                change.setSubType(3);
            }
            if (StringUtils.isNotBlank(order.getRefundId())) {
                saveTmcRefundMsg(eshopInfo, tmMessage, order);
                saveRefundToPlEshopNotifyChange(eshopInfo, tmMessage, order);
            }
            //status 1-用户已提交订单；2-向商家推送订单；4-商家已确认；8-订单已完成；9-订单已取消
            //status=4 是接单消息
            if (StringUtils.equals(order.getStatus(), "4")) {
                SupportUtil.doOrderNotifyByDelayWay(change.getOnlineShopId(), change, eshopInfo.getEshopType().getCode(), meiTuanConfig.getTmcDelayTime());
            } else {
                SupportUtil.sendMessage(change, eshopInfo);
            }
            return buildResponse("ok");
        } catch (Exception ex) {
            String errMsg = ex.getMessage();
            errMsg = StringUtils.isEmpty(errMsg) ? "" : errMsg;
            return buildResponse(errMsg);
        }
    }

    private void saveRefundToPlEshopNotifyChange(EshopInfo eshopInfo, String tmMessage, MeiTuanOrderMessage curRefundMessage) {
        try {
            MeiTuanTopicEnum topicEnum = null;
            if (StringUtils.isNotBlank(curRefundMessage.getRefundStatus())) {
                //refund_status 推送退款状态（成功/失败）信息 消息
                topicEnum = MeiTuanTopicEnum.REFUND_FAIL_OR_SUCCESS;
            } else if (StringUtils.isNotBlank(curRefundMessage.getFood())) {
                //部分退款信息
                topicEnum = MeiTuanTopicEnum.PART_REFUND;
            } else if (StringUtils.isNotBlank(curRefundMessage.getStatus())) {
                //全额退款信息
                topicEnum = MeiTuanTopicEnum.ALL_REFUND;
            }
            if (topicEnum == null) {
                return;
            }
            RefundStatus localRefundStatus = getLocalRefundStatus(curRefundMessage);
            if (localRefundStatus == RefundStatus.NONE) {
                return;
            }
            TMCType tmcType;
            if (localRefundStatus == RefundStatus.CANCEL) {
                tmcType = TMCType.REFUND_STOP_CANCEL;
            } else {
                tmcType = TMCType.REFUND_STOP;
            }
            EshopNotifyChange curNotifyChange = new EshopNotifyChange();
            curNotifyChange.setId(UId.newId());
            curNotifyChange.setProfileId(eshopInfo.getProfileId());
            curNotifyChange.setEshopId(eshopInfo.getOtypeId());
            curNotifyChange.setTradeOrderId(curRefundMessage.getOrderId());
            curNotifyChange.setUpdateTime(DateUtils.getDate());
            curNotifyChange.setContent(tmMessage);
            curNotifyChange.setType(tmcType);
            UpdateOrderRefundStateRequest request = new UpdateOrderRefundStateRequest();
            request.setTradeOrderId(curNotifyChange.getTradeOrderId());
            request.setShopId(curNotifyChange.getEshopId());
            if (tmcType == TMCType.REFUND_STOP) {
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CREATE_BY_TMC);
                if (localRefundStatus == RefundStatus.SUCCESS) {
                    request.setRefundState(ReturnState.SUCCESS);
                } else {
                    request.setRefundState(ReturnState.REFUNDING);
                }
            } else {
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CANCEL_BY_TMC);
                request.setRefundState(ReturnState.NONE);
            }
            List<GoodInfo> foodList = curRefundMessage.getFoodList();
            if (CollectionUtils.isNotEmpty(foodList)) {
                List<String> oidList = foodList.stream().map(GoodInfo::getItemId).collect(Collectors.toList());
                request.setOidList(oidList);
            }
            TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
            EshopSaleOrderService eshopSaleOrderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
            List<EshopNotifyChange> eshopNotifyChanges = tmcMapper.queryMessageChangeSorted(curNotifyChange.getProfileId(),
                    Collections.singletonList(curNotifyChange.getTradeOrderId()), curNotifyChange.getEshopId(), TMCType.REFUND_STOP.getCode());
            EshopNotifyChange latestNotifyChange = null;
            boolean duplicateMsg = false;
            long latestMsgTime = 0;
            long curMsgTime = StringUtils.isNotBlank(curRefundMessage.getTimestamp()) ? Long.parseLong(curRefundMessage.getTimestamp()) : 0;
            if (CollectionUtils.isNotEmpty(eshopNotifyChanges)) {
                for (EshopNotifyChange eshopNotifyChange : eshopNotifyChanges) {
                    String content = eshopNotifyChange.getContent();
                    if (StringUtils.isEmpty(content)) {
                        continue;
                    }
                    MeiTuanOrderMessage refundMessage = JsonUtils.toObject(content, MeiTuanOrderMessage.class);
                    if (refundMessage == null || !StringUtils.equals(refundMessage.getRefundId(), curRefundMessage.getRefundId())) {
                        continue;
                    }
                    long msgTime = StringUtils.isNotBlank(refundMessage.getTimestamp()) ? Long.parseLong(refundMessage.getTimestamp()) : 0;
                    if (msgTime >= curMsgTime) {
                        //消息重复。当前消息已经处理过
                        duplicateMsg = true;
                        break;
                    }
                    if (latestNotifyChange == null || latestMsgTime != 0 && latestMsgTime < msgTime) {
                        latestNotifyChange = eshopNotifyChange;
                        latestMsgTime = msgTime;
                    }
                }
            }
            if (duplicateMsg) {
                return;
            }
            if (latestNotifyChange != null) {
                curNotifyChange.setId(latestNotifyChange.getId());
                tmcMapper.updateEshopNotifyChangeById(curNotifyChange);
            } else {
                tmcMapper.insertMessageChange(curNotifyChange);
            }
            eshopSaleOrderService.updateOrderRefundState(request);
        } catch (Exception ex) {
            if (ex.getMessage() != null && ex.getMessage().contains("订单尚未流入系统")) {
                return;
            }
            LOGGER.error("账套ID{},店铺ID{},保存售后消息失败，失败原因：{}.message:{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), ex.getMessage(), tmMessage, ex);
        }
    }

    private RefundStatus getLocalRefundStatus(MeiTuanOrderMessage order) {
        RefundStatus refundStatus = RefundStatus.NONE;
        if (StringUtils.isNotBlank(order.getStatus())) {
            refundStatus = MtRefundBuilder.getRefundStatus(Integer.parseInt(order.getStatus()), MtRefundBuilder.platformRefundStatusMap);
        } else if (StringUtils.isNotBlank(order.getRefundStatus())) {
            //退款状态：3-全部退款退款成功；4-全部退款失败；13-部分退款退款成功；14-部款退款失败
            if (StringUtils.equals(order.getRefundStatus(), "3") || StringUtils.equals(order.getRefundStatus(), "13")) {
                refundStatus = RefundStatus.SUCCESS;
            } else if (StringUtils.equals(order.getRefundStatus(), "4") || StringUtils.equals(order.getRefundStatus(), "14")) {
                refundStatus = RefundStatus.CANCEL;
            }
        } else {
            if (StringUtils.isNotBlank(order.getResType())) {
                refundStatus = LOCAL_REFUND_MAP.getOrDefault(order.getResType(), RefundStatus.NONE);
            }
        }
        return refundStatus;
    }

    protected void saveTmcRefundMsg(EshopInfo eshopInfo, String message, MeiTuanOrderMessage order) {
        RefundStatus refundStatus = getLocalRefundStatus(order);
        try {
            EshopTmcRefundMsgDto refundMsgDto = new EshopTmcRefundMsgDto(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), eshopInfo.getEshopType(),
                    order.getTradeId(), order.getRefundId(), message, refundStatus);
            tmcRefundMsgService.saveOrUpdateTmcRefundMsg(refundMsgDto);
        } catch (Exception ex) {
            LOGGER.error("账套ID{},店铺ID{},保持售后消息失败，失败原因：{}.message:{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), ex.getMessage(), message, ex);
        }
    }

    @Override
    public String serviceName() {
        return "orderChange";
    }
}
