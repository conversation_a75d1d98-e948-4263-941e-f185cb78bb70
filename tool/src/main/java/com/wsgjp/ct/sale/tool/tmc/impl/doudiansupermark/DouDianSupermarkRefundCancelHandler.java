package com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark;

import com.doudian.open.spi.yunc_wms_inbound_cancel.param.YuncWmsInboundCancelParam;
import com.doudian.open.spi.yunc_wms_inbound_createSaleOrder.param.OrderDetailsItem;
import com.doudian.open.spi.yunc_wms_inbound_createSaleOrder.param.YuncWmsInboundCreateSaleOrderParam;
import com.doudian.open.utils.JsonUtil;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcRefundMsgMapper;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderRefundStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.member.utils.StringUtils;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcRefundMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class DouDianSupermarkRefundCancelHandler extends DDSNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DouDianSupermarkRefundCancelHandler.class);

    private final EshopTmcRefundMsgMapper tmcRefundMsgMapper;

    public DouDianSupermarkRefundCancelHandler(EshopTmcRefundMsgMapper tmcRefundMsgMapper) {
        this.tmcRefundMsgMapper = tmcRefundMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======抖店超市进入invoker方法======");
        String tmMessage = invokeMessage.getMessage();
        YuncWmsInboundCancelParam yuncWmsInboundCancelParam;
        try {
            yuncWmsInboundCancelParam = JsonUtil.fromJson(tmMessage, YuncWmsInboundCancelParam.class);
        } catch (Exception e) {
            String errMsg = String.format("抖店超市客tmMessage数据转换成YuncWmsInboundCancelParam实体出错，错误信息：%s", e.getMessage());
            LOGGER.error(errMsg,e);
            return JsonUtils.toJson(new GeneralResult(100003L,"序列化失败",null));
        }
        EshopRegisterNotify notify;
        EshopTmcRefundMsgEntity refundMsgEntity;
        try {
            notify = SupportUtil.buildNotify(yuncWmsInboundCancelParam.getWarehouseCode(), 148);
            refundMsgEntity = tmcRefundMsgMapper.queryTmcRefundMsgByRefundId(invokeMessage.getProfileId(), notify.getId(), yuncWmsInboundCancelParam.getInboundOrderNo());
            if (Objects.isNull(refundMsgEntity)) {
                return JsonUtils.toJson(new GeneralResult(6004L, "未收到入库单!", null));
            } else {
                //todo  refundMsgEntity.setMsgStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
                refundMsgEntity.setMsgUpdateTime(new Date());
                //平台没有返回更新时间，平台推送一次就更新一次
                refundMsgEntity.setUpdateTime(new Date());
                refundMsgEntity.setMessage(refundMsgEntity.getMessage());
                refundMsgEntity.setRefundStatus(6);
                refundMsgEntity.setMsgStatus(0);
                tmcRefundMsgMapper.updateTmcRefundMsg(refundMsgEntity);
                saveRefundToPlEshopNotifyChange(notify, tmMessage, yuncWmsInboundCancelParam, refundMsgEntity);
            }
        } catch (Exception ex) {
            LOGGER.error("错误",ex);
            LOGGER.error("抖店超市保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", invokeMessage.getProfileId(), invokeMessage.getEshopId(), tmMessage, ex.getMessage());
            return JsonUtils.toJson(new GeneralResult(100003L, "抖店超市修改数据库出错!", null));
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setProfileId(notify.getProfileId());
        eshopNotifyChange.setEshopId(notify.getId());
        eshopNotifyChange.setTradeOrderId(refundMsgEntity.getTradeOrderId());
        eshopNotifyChange.setContent("");
        eshopNotifyChange.setId(UId.newId());
        eshopNotifyChange.setRefundOrderId(yuncWmsInboundCancelParam.getInboundOrderNo());
        eshopNotifyChange.setType(TMCType.Order);
        SupportUtil.doOrderNotify(yuncWmsInboundCancelParam.getWarehouseCode(), eshopNotifyChange, invokeMessage.getShopType().getCode());
        return JsonUtils.toJson(new GeneralResult(0L,"success",null));
    }

    private void saveRefundToPlEshopNotifyChange(EshopRegisterNotify notify, String tmMessage, YuncWmsInboundCancelParam refundCancelMessage, EshopTmcRefundMsgEntity refundMsg) {
        try {
            EshopNotifyChange curNotifyChange = new EshopNotifyChange();
            curNotifyChange.setProfileId(notify.getProfileId());
            curNotifyChange.setEshopId(notify.getId());
            curNotifyChange.setTradeOrderId(refundMsg.getTradeOrderId());
            curNotifyChange.setContent(tmMessage);
            curNotifyChange.setType(TMCType.REFUND_STOP_CANCEL);
            curNotifyChange.setUpdateTime(DateUtils.getDate());
            UpdateOrderRefundStateRequest request = new UpdateOrderRefundStateRequest();
            request.setTradeOrderId(curNotifyChange.getTradeOrderId());
            request.setShopId(curNotifyChange.getEshopId());
            request.setRefundState(ReturnState.NONE);
            request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CANCEL_BY_TMC);
            YuncWmsInboundCreateSaleOrderParam refundMessage = null;
            try{
                refundMessage = JsonUtil.fromJson(refundMsg.getMessage(), YuncWmsInboundCreateSaleOrderParam.class);
            }catch (Exception ex){
                LOGGER.error(String.format("抖店超市客tmMessage数据转换成YuncWmsInboundCancelParam实体出错，错误信息：%s", ex.getMessage()));
            }
            if (refundMessage != null && CollectionUtils.isNotEmpty(refundMessage.getOrderDetails())) {
                List<String> oidList = new ArrayList<>();
                for (OrderDetailsItem detail : refundMessage.getOrderDetails()) {
                    oidList.add(String.format("%s_%s", detail.getLineNo(), detail.getCargoCode()));
                }
                request.setOidList(oidList);
            }
            EshopSaleOrderService eshopSaleOrderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
            TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
            List<EshopNotifyChange> eshopNotifyChanges = tmcMapper.queryMessageChangeSorted(curNotifyChange.getProfileId(),
                    Collections.singletonList(curNotifyChange.getTradeOrderId()), curNotifyChange.getEshopId(), TMCType.REFUND_STOP.getCode());
            if (CollectionUtils.isNotEmpty(eshopNotifyChanges)) {
                for (EshopNotifyChange eshopNotifyChange : eshopNotifyChanges) {
                    String content = eshopNotifyChange.getContent();
                    if (StringUtils.isEmpty(content)) {
                        continue;
                    }
                    YuncWmsInboundCreateSaleOrderParam oldRefundMessage = null;
                    try {
                        oldRefundMessage = JsonUtil.fromJson(tmMessage, YuncWmsInboundCreateSaleOrderParam.class);
                    } catch (Exception ex) {
                        LOGGER.error("json转换出错，错误信息：{}", ex.getMessage());
                    }
                    if (oldRefundMessage == null) {
                        continue;
                    }
                    if (StringUtils.equals(oldRefundMessage.getInboundOrderNo(), refundCancelMessage.getInboundOrderNo())) {
                        curNotifyChange.setId(eshopNotifyChange.getId());
                        tmcMapper.updateEshopNotifyChangeById(curNotifyChange);
                        eshopSaleOrderService.updateOrderRefundState(request);
                        break;
                    }
                }
            }
        } catch (Exception ex) {
            if (ex.getMessage() != null && ex.getMessage().contains("订单尚未流入系统")) {
                return;
            }
            LOGGER.error("账套ID{},店铺ID{},保存售后消息到pl_eshop_notify_change失败，失败原因：{}.message:{}",
                    notify.getProfileId(), notify.getId(), ex.getMessage(), tmMessage, ex);
        }
    }


    @Override
    public String serviceName() {
        return "yunc.wms.inbound.cancel";
    }
}
