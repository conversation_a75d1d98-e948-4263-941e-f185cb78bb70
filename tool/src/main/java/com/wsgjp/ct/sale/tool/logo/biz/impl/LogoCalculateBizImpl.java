package com.wsgjp.ct.sale.tool.logo.biz.impl;

import com.wsgjp.ct.sale.biz.common.entity.*;
import com.wsgjp.ct.sale.biz.common.mapper.LogoCalculateBizMapper;
import com.wsgjp.ct.sale.biz.common.param.QueueParam;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.LogoSysConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.logo.LogoResetStatus;
import com.wsgjp.ct.sale.biz.jarvis.utils.CommonUtils;
import com.wsgjp.ct.sale.biz.jarvis.utils.Functions;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.JarvisMonitorTypeEnum;
import com.wsgjp.ct.sale.sdk.logo.entity.LogoExceptionSummary;
import com.wsgjp.ct.sale.sdk.logo.state.LogoExceptionStatusEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoProcessStateEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoSourceTypeEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoUsePhaseEnum;
import com.wsgjp.ct.sale.tool.logo.biz.LogoCalculateBiz;
import com.wsgjp.ct.sale.tool.logo.config.LogoCalculateConfig;
import com.wsgjp.ct.sale.tool.logo.service.LogoComputedService;
import com.wsgjp.ct.sale.tool.logo.util.LogoUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.apache.commons.lang.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LogoCalculateBizImpl implements LogoCalculateBiz {

    private static final Logger logger = LoggerFactory.getLogger(LogoCalculateBizImpl.class);
    private LogoCalculateBizMapper calculateMapper;
    private LogoCalculateConfig config;
    private MonitorService monitorService;
    private LogoComputedService logoComputedService;

    public LogoCalculateBizImpl(LogoCalculateBizMapper calculateMapper,
                                LogoCalculateConfig config,
                                MonitorService monitorService,
                                LogoComputedService logoComputedService) {
        this.calculateMapper = calculateMapper;
        this.config = config;
        this.monitorService = monitorService;
        this.logoComputedService = logoComputedService;
    }

    @Override
    public boolean computeBefore() {
        //监控请求量
        monitorService.recordOPSSSuccess(JarvisMonitorTypeEnum.BIZ_AUTO_MARK_EXCEPTION_QPS.getTopic(), "TYPE", "AUTO", 1);
        return true;
    }

    @Override
    public ComputeQueue getChangeList(BigInteger profileId, long threadId, StopWatch stopWatch, LogoSysConfig sysData) {

        List<LogoExceptionChange> changeList = new ArrayList<>();
        List<LogoExceptionChange> reChangeList = new ArrayList<>();

        ComputeQueue computeQueue = new ComputeQueue(changeList, reChangeList, sysData.getLastId(), sysData.getLastTimeSliceId());
        try {
            long startWatchTime = stopWatch.getTime();
            getQueue(profileId, changeList, sysData, computeQueue);
            // 获取重算队列数据
            int limitCount = config.getBillNumber() - changeList.size();
            if (limitCount > 0) {
                getReQueue(profileId, reChangeList, sysData, limitCount, computeQueue);
            }
            long stopWatchTime = stopWatch.getTime();
            logger.debug("[徽标工具-计算]profileId【{}】Thread【{}】,查询change表数据完成，查询耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
        } catch (Exception ex) {
            String errMsg = String.format("[徽标工具-计算]profileId【%s】Thread【%s】,查询change表数据报错：%s | %s",
                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
            logger.error(errMsg, ex);
        }
        return computeQueue;
    }

    /**
     * 获取正常计算队列数据
     *
     * @param profileId
     * @param changeList
     * @param sysData
     * @param computeQueue
     */
    private void getQueue(BigInteger profileId, List<LogoExceptionChange> changeList, LogoSysConfig sysData, ComputeQueue computeQueue) {
        QueueParam queueParam = new QueueParam(profileId, config.isProcess(), config.getBillNumber(), sysData.getLastTimeSliceId());

        List<LogoExceptionChange> list = calculateMapper.getQueue(queueParam);
        for (LogoExceptionChange item : list) {
            if (null != item.getSourceType()) {
                changeList.add(item);
            }
        }
        if (!list.isEmpty()) {
            computeQueue.setLastTimeSliceId(list.get(list.size() - 1).getTimeSliceId());
        }
    }

    public static class IdGroup {
        private HashSet<BigInteger> ids = new HashSet<>();
        private HashSet<String> idGroupKeys = new HashSet<>();
        public IdGroup(List<LogoExceptionChange> logos){
            for (LogoExceptionChange logoExceptionChange : logos) {
                ids.add(logoExceptionChange.getVchcode());
                String key = key(logoExceptionChange.getVchcode(),logoExceptionChange.getSourceType());
                idGroupKeys.add(key);
            }
        }
        public static String key(BigInteger vchcode, LogoSourceTypeEnum sourceType) {
            return String.format("%s-%s", sourceType, vchcode);
        }
    }

    /**
     * 获取重算计算队列数据
     *
     * @param profileId
     * @param changeList
     * @param sysData
     * @param computeQueue
     */
    private void getReQueue(BigInteger profileId, List<LogoExceptionChange> changeList, LogoSysConfig sysData, int limitCount, ComputeQueue computeQueue) {
        QueueParam queueParam = new QueueParam(profileId, config.isProcess(), limitCount, sysData.getLastId());

        List<LogoExceptionChange> list = calculateMapper.getReQueue(queueParam);
        for (LogoExceptionChange item : list) {
            if (null != item.getSourceType()) {
                changeList.add(item);
            }
        }
        if (!list.isEmpty()) {
            computeQueue.setLastId(list.get(list.size() - 1).getId());
        }
    }


    @Override
    public LogoCompute compute(List<LogoExceptionChange> changeListAll, BigInteger profileId, long threadId, StopWatch stopWatch) {
        LogoCompute logoCompute = new LogoCompute();
        try {
            long startWatchTime = stopWatch.getTime();

            logoCompute.setProfileId(profileId);
            logoCompute.setThreadId(threadId);
            List<BigInteger> idList = changeListAll.stream().map(LogoExceptionChange::getId).collect(Collectors.toList());
            logoCompute.setIdList(idList);
            //监控处理数量
            monitorService.recordSum(JarvisMonitorTypeEnum.BIZ_AUTO_MARK_EXCEPTION_SUM.getTopic(), "TYPE", "AUTO", changeListAll.size());
            //去重
            List<LogoExceptionChange> changeList = changeListAll.stream().distinct().collect(Collectors.toList());
            IdGroup idGroup = new IdGroup(changeList);

            //删除state表
            List<LogoExceptionState> deleteStateList = new ArrayList<>();
            //新增state表
            List<LogoExceptionState> addStateList = new ArrayList<>();
            //新增summary表
            List<LogoExceptionSummary> addSummaryList = new ArrayList<>();
            //更新summary表
            List<LogoExceptionSummary> updateSummaryList = new ArrayList<>();
            //原先的summary数据
            List<LogoExceptionSummary> oldSummaryList = calculateMapper.queryExceptionSummaryAll(profileId);

            //执行判断异常订单的具体业务
//            List<BigInteger> billIds = changeList.stream().map(LogoExceptionChange::getVchcode).distinct().collect(Collectors.toList());
            //获取之前的state
            List<LogoExceptionState> oldStateList = CommonUtils.executeFuncByBatchQuery(CommonUtils.QUERY_SIZE_FIVE, new ArrayList<>(idGroup.ids),
                    item -> calculateMapper.queryExceptionStateByVchcode(profileId, item));
            // 过滤不属于当前sourceType + vchcode 的数据
            oldStateList = oldStateList.stream().filter(a -> idGroup.idGroupKeys.contains(IdGroup.key(a.getVchcode(), a.getSourceType()))).collect(Collectors.toList());
            //获取新的state
            List<LogoExceptionState> newStates = new ArrayList<>();
            logoComputedService.computed(newStates, profileId, changeList);

//            logger.debug("[徽标工具-计算]profileId【{}】Thread【{}】,比对参数【oldStateList】{}【newStates】{}【oldSummaryList】{}",
//                    profileId, threadId, JsonUtils.toJson(oldStateList), JsonUtils.toJson(newStates), JsonUtils.toJson(oldSummaryList));
            //进行对比
            if (CollectionUtils.isEmpty(newStates)) {
                if (CollectionUtils.isNotEmpty(oldStateList)) {
                    //之前全部异常，现在全部正常，则全部删除
                    buildDeleteStateList(oldSummaryList, updateSummaryList, deleteStateList, oldStateList, profileId);
                }
            } else {
                //去重，因为工具会多跑一次
                List<LogoExceptionState> newStateList = newStates.stream().distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(oldStateList)) {
                    //之前全部正常，现在全部异常，则全部新增
                    buildAddStateList(oldSummaryList, addSummaryList, updateSummaryList, addStateList, newStateList, profileId);
                } else {
                    //考虑一个订单可能有多个异常状态的情况，所以这里一单一单来进行比对
                    Map<String, List<LogoExceptionState>> newStateListMap = newStateList.stream().collect(Collectors.groupingBy(a->IdGroup.key(a.getVchcode(),a.getSourceType())));
                    Map<String, List<LogoExceptionState>> oldStateListMap = oldStateList.stream().collect(Collectors.groupingBy(a->IdGroup.key(a.getVchcode(),a.getSourceType())));
                    for (String idKey : idGroup.idGroupKeys) {
                        List<LogoExceptionState> newStateValueList = newStateListMap.get(idKey);
                        List<LogoExceptionState> oldStateValueList = oldStateListMap.get(idKey);
                        //该订单，如果之前存在异常，则删除
                        if (CollectionUtils.isNotEmpty(oldStateValueList)) {
                            buildDeleteStateList(oldSummaryList, updateSummaryList, deleteStateList, oldStateValueList, profileId);
                        }
                        //该订单，如果现在存在异常，则新增
                        if (CollectionUtils.isNotEmpty(newStateValueList)) {
                            buildAddStateList(oldSummaryList, addSummaryList, updateSummaryList, addStateList, newStateValueList, profileId);
                        }
                    }
                }
            }

            //删除state表
            if (CollectionUtils.isNotEmpty(deleteStateList)) {
                logoCompute.setDeleteStateList(deleteStateList);
            }
            //新增state表
            if (CollectionUtils.isNotEmpty(addStateList)) {
                logoCompute.setAddStateList(addStateList.stream().distinct().collect(Collectors.toList()));
            }
            //新增summary表
            if (CollectionUtils.isNotEmpty(addSummaryList)) {
                logoCompute.setAddSummaryList(addSummaryList);
            }
            //更新summary表
            if (CollectionUtils.isNotEmpty(updateSummaryList)) {
                logoCompute.setUpdateSummaryList(updateSummaryList);
            }
            logoCompute.setSuccess(true);

            long stopWatchTime = stopWatch.getTime();
            logger.debug("[徽标工具-计算]profileId【{}】Thread【{}】,构建徽标数据完成，构建耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
        } catch (Exception ex) {
            String errMsg = String.format("[徽标工具-计算]profileId【%s】", profileId);
            logger.error(errMsg, ex);
            monitorService.recordOPSSFail(JarvisMonitorTypeEnum.BIZ_AUTO_MARK_EXCEPTION_ERROR_QPS.getTopic(), "TYPE", "AUTO", 1);
            logoCompute.setSuccess(false);
        }
        return logoCompute;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refreshDb(LogoCompute logoCompute, StopWatch stopWatch, ComputeQueue computeQueue) {
        try {
            long startWatchTime = stopWatch.getTime();
            List<LogoExceptionSummary> addSummaryList = null;
            if (logoCompute.isSuccess()) {
                //删除state表
                if (CollectionUtils.isNotEmpty(logoCompute.getDeleteStateList())) {
                    List<BigInteger> idList = logoCompute.getDeleteStateList().stream().map(LogoExceptionState::getId).distinct().collect(Collectors.toList());
                    LogoUtil.syncExecuteFuncByBatch(subId -> calculateMapper.deleteExceptionStateBatch(CurrentUser.getProfileId(), subId), idList);
                }
                //新增state表
                if (CollectionUtils.isNotEmpty(logoCompute.getAddStateList())) {
                    LogoUtil.syncExecuteFuncByBatch(subStateList -> calculateMapper.addExceptionStateBatch(subStateList), logoCompute.getAddStateList());
                }
                //新增summary表
                if (CollectionUtils.isNotEmpty(logoCompute.getAddSummaryList())) {
                    //正常来说，这里不会存在新增的summary数据了，因为账套重算会初始化所有的异常状态
                    logger.error("[徽标工具-计算]profileId【{}】Thread【{}】,新增的类型，通过晚间的重算进行处理", CurrentUser.getProfileId(), logoCompute.getThreadId());
//                   加了这个逻辑之后，就一直不准，推测是并发原因，未必然重现。注释掉，通过每日的重算来初始化新的类型
//                    addSummaryList = CommonUtils.distinct(logoCompute.getAddSummaryList(), a -> String.format("%s/%s/%s", a.getStateType().getEnumCode(), a.getSourceType().getCode(), a.getUsePhase().getCode()));
//                    //写入时删除并发时可能导致得 重复数据
//                    calculateMapper.addAndClearExceptionSummary(addSummaryList);
                }
                //更新summary表
                if (CollectionUtils.isNotEmpty(logoCompute.getUpdateSummaryList())) {
                    LogoUtil.syncExecuteFuncByBatch(subSummaryList -> calculateMapper.updateExceptionSummary(subSummaryList), logoCompute.getUpdateSummaryList());
                }
                //更新change表
                if (CollectionUtils.isNotEmpty(logoCompute.getIdList())) {
                    if (logoCompute.isReChange()) {
                        LogoUtil.syncExecuteFuncByBatch(subIdList -> calculateMapper.updateExceptionReChangeAll(CurrentUser.getProfileId(), subIdList, LogoProcessStateEnum.PROCESSED), logoCompute.getIdList());
                    } else {
                        LogoUtil.syncExecuteFuncByBatch(subIdList -> calculateMapper.updateExceptionChangeAll(CurrentUser.getProfileId(), subIdList, LogoProcessStateEnum.PROCESSED), logoCompute.getIdList());
                    }
                }
                if (computeQueue.getLastId() != null && logoCompute.isReChange()) {
                    List<LogoSysData> logoSysDataList = new ArrayList<>();
                    // 记录lastId
                    LogoSysData sysGlobalLastId = new LogoSysData(UId.newId(), CurrentUser.getProfileId(), "sysGlobalLastId", computeQueue.getLastId().toString(), "徽标工具-计算LastId", CurrentUser.getEmployeeId());
                    logoSysDataList.add(sysGlobalLastId);
                    // 记录最后一次消费时间
                    LogoSysData sysGlobalLastExecTime = new LogoSysData(UId.newId(), CurrentUser.getProfileId(), "sysGlobalLastExecTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date()), "徽标工具-最后重算时间", CurrentUser.getEmployeeId());
                    logoSysDataList.add(sysGlobalLastExecTime);
                    calculateMapper.addSysDataList(logoSysDataList);
                }
                if (computeQueue.getLastTimeSliceId() != null) {
                    LogoSysData logoSysData = new LogoSysData(UId.newId(), CurrentUser.getProfileId(), "sysGlobalLastTimeSliceId", computeQueue.getLastTimeSliceId().toString(), "徽标工具-计算LastTimeSliceId", CurrentUser.getEmployeeId());
                    calculateMapper.addSysDataList(Collections.singletonList(logoSysData));

                    // 纠正 TimeSliceId
                    List<LogoExceptionChange> changeByLastTimeSliceId = calculateMapper.getChangeByLastTimeSliceId(CurrentUser.getProfileId(), computeQueue.getLastTimeSliceId());
                    if (changeByLastTimeSliceId != null && !changeByLastTimeSliceId.isEmpty()) {
                        List<BigInteger> ids = changeByLastTimeSliceId.stream().map(LogoExceptionChange::getId).collect(Collectors.toList());
                        calculateMapper.updateChangeByIds(CurrentUser.getProfileId(), ids);
                    }
                }

            } else {
                if (CollectionUtils.isNotEmpty(logoCompute.getIdList())) {
                    if (logoCompute.isReChange()) {
                        LogoUtil.syncExecuteFuncByBatch(subIdList -> calculateMapper.updateExceptionReChangeAll(CurrentUser.getProfileId(), subIdList, LogoProcessStateEnum.PROCESSING_FAILURE), logoCompute.getIdList());
                    } else {
                        LogoUtil.syncExecuteFuncByBatch(subIdList -> calculateMapper.updateExceptionChangeAll(CurrentUser.getProfileId(), subIdList, LogoProcessStateEnum.PROCESSING_FAILURE), logoCompute.getIdList());
                    }
                }
            }

            long stopWatchTime = stopWatch.getTime();
            logger.debug("[徽标工具-计算]profileId【{}】Thread【{}】,写库完成，写库耗时【{}】写库参数：{}", CurrentUser.getProfileId(), logoCompute.getThreadId(), stopWatchTime - startWatchTime, JsonUtils.toJson(logoCompute));
            return true;
        } catch (Exception ex) {
            String errMsg = String.format("[徽标工具-计算]profileId【%s】Thread【%s】,写库报错：%s | %s", CurrentUser.getProfileId(), logoCompute.getThreadId(), ex.getMessage(), ex.getCause().getMessage());
            logger.error(errMsg, ex);
            monitorService.recordOPSSFail(JarvisMonitorTypeEnum.BIZ_AUTO_MARK_EXCEPTION_ERROR_QPS.getTopic(), "TYPE", "AUTO", 1);
            //更新change表
            if (CollectionUtils.isNotEmpty(logoCompute.getIdList())) {
                if (logoCompute.isReChange()) {
                    LogoUtil.syncExecuteFuncByBatch(subIdList -> calculateMapper.updateExceptionReChangeAll(CurrentUser.getProfileId(), subIdList, LogoProcessStateEnum.PROCESSING_FAILURE), logoCompute.getIdList());
                } else {
                    LogoUtil.syncExecuteFuncByBatch(subIdList -> calculateMapper.updateExceptionChangeAll(CurrentUser.getProfileId(), subIdList, LogoProcessStateEnum.PROCESSING_FAILURE), logoCompute.getIdList());
                }
            }
            return true;
        }
    }

    @Override
    public boolean computeAfter(StopWatch stopWatch, int size) {
        if (size > 0) {
            //监控处理一条change数据的耗时
            monitorService.recordTP(JarvisMonitorTypeEnum.BIZ_AUTO_MARK_EXCEPTION_TP_TIME.getTopic(),
                    "TYPE", "AUTO", stopWatch.getTime() / size);
        }
        //监控处理数量
        monitorService.recordSum(JarvisMonitorTypeEnum.BIZ_AUTO_MARK_EXCEPTION_SUM.getTopic(), "TYPE", "AUTO", 0);
        //监控耗时
        monitorService.recordTP(JarvisMonitorTypeEnum.BIZ_AUTO_MARK_EXCEPTION_TP_TIME.getTopic(), "TYPE", "AUTO", stopWatch.getTime());
        return true;
    }

//    @Override
//    public String querySysDataValue(BigInteger profileId, long threadId, String subName) {
//        try {
//            List<LogoSysData> logoSysData = calculateMapper.querySysData(profileId, subName);
//            if (CollectionUtils.isNotEmpty(logoSysData)) {
//                String subValue = logoSysData.get(0).getSubValue();
//                if (StringUtils.isNotEmpty(subValue)) {
//                    return subValue;
//                }
//            }
//            return null;
//        } catch (Exception ex) {
//            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,查询SysData报错：%s | %s",
//                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
//            logger.error(errMsg, ex);
//            return null;
//        }
//    }

//    @Override
//    public boolean querySysData(BigInteger profileId, long threadId, String subName) {
//        try {
//            List<LogoSysData> logoSysData = calculateMapper.querySysData(profileId, subName);
//            if (CollectionUtils.isNotEmpty(logoSysData)) {
//                String subValue = logoSysData.get(0).getSubValue();
//                if (StringUtils.isNotEmpty(subValue) && LogoState.ONE.equals(subValue)) {
//                    return true;
//                }
//            }
//            return false;
//        } catch (Exception ex) {
//            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,查询SysData报错：%s | %s",
//                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
//            logger.error(errMsg, ex);
//            return false;
//        }
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cleanSummaryData(BigInteger profileId, long threadId, StopWatch stopWatch) {
        try {
            long startWatchTime = stopWatch.getTime();

            calculateMapper.deleteExceptionSummary(profileId);

            long stopWatchTime = stopWatch.getTime();
            logger.debug("[徽标工具-全量计算]profileId【{}】Thread【{}】,清空summary表完成，清空summary表耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
            return true;
        } catch (Exception ex) {
            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,清空summary表报错：%s | %s",
                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
            logger.error(errMsg, ex);
            return false;
        }
    }

//    public boolean reloadExceptionSummary(List<LogoExceptionSummary> summaryList) {
//        return calculateMapper.reloadExceptionSummary(summaryList, CurrentUser.getProfileId());
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetSummaryData(BigInteger profileId, long threadId, StopWatch stopWatch) {
        try {
            this.cleanSummaryData(profileId, threadId, stopWatch);
            List<LogoExceptionSummary> logoExceptionSummaries = this.initSummaryData(profileId, threadId, stopWatch);
            calculateMapper.reloadExceptionSummary(logoExceptionSummaries, CurrentUser.getProfileId());
        } catch (Exception exception) {
            logger.error("[徽标工具-全量计算]profileId【%s】Thread【%s】,初始化summary表报错：%s | %s", exception);
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<LogoExceptionSummary> initSummaryData(BigInteger profileId, long threadId, StopWatch stopWatch) {
        try {
            long startWatchTime = stopWatch.getTime();
            List<LogoExceptionSummary> summaryListInit = new ArrayList<>();
            for (LogoSourceTypeEnum sourceType : LogoSourceTypeEnum.values()) {
                //非工具来源
                if (!sourceType.isAuto()) {
                    for (LogoUsePhaseEnum usePhase : LogoUsePhaseEnum.values()) {
                        //获取该来源对应的页面
                        if (sourceType.equals(usePhase.getSourceType())) {
                            //构建数据
                            for (LogoExceptionStatusEnum state : LogoExceptionStatusEnum.values()) {
                                LogoExceptionSummary summary = new LogoExceptionSummary();
                                summary.setId(UId.newId());
                                summary.setProfileId(profileId);
                                summary.setSourceType(sourceType);
                                summary.setUsePhase(usePhase);
                                summary.setStateType(state);
                                summary.setStateCount(0);
                                summaryListInit.add(summary);
                            }
                        }
                    }
                }
            }
            calculateMapper.addExceptionSummary(summaryListInit);
            long stopWatchTime = stopWatch.getTime();
            logger.debug("[徽标工具-全量计算]profileId【{}】Thread【{}】,初始化summary表完成，初始化summary表耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
            return summaryListInit;
        } catch (Exception ex) {
            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,初始化summary表报错：%s | %s",
                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
            logger.error(errMsg, ex);
            return null;
        }
    }

//    @Override
//    public List<LogoExceptionSummary> queryExceptionState(BigInteger profileId, long threadId, StopWatch stopWatch) {
//        try {
//            long startWatchTime = stopWatch.getTime();
//
//            //汇总state表，得到汇总数据
//            List<LogoExceptionSummary> updateSummaryList = calculateMapper.queryExceptionState(profileId);
//
//            long stopWatchTime = stopWatch.getTime();
//            logger.debug("[徽标工具-全量计算]profileId【{}】Thread【{}】,汇总state表完成，汇总state表耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
//            return updateSummaryList;
//        } catch (Exception ex) {
//            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,汇总state表报错：%s | %s",
//                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
//            logger.error(errMsg, ex);
//            return null;
//        }
//    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean calibrationAll(BigInteger profileId, long threadId, StopWatch stopWatch, List<LogoExceptionSummary> updateSummaryList) {
//        try {
//            long startWatchTime = stopWatch.getTime();
//
//            if (CollectionUtils.isNotEmpty(updateSummaryList)) {
//                calculateMapper.updateExceptionSummary(updateSummaryList);
//            }
//
//            long stopWatchTime = stopWatch.getTime();
//            logger.debug("[徽标工具-全量计算]profileId【{}】Thread【{}】,校准数据完成，校准数据耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
//            return true;
//        } catch (Exception ex) {
//            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,校准数据报错：%s | %s",
//                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
//            logger.error(errMsg, ex);
//            return false;
//        }
//    }

    /**
     * 开始拉取数据内容
     *
     * @param resetStatus
     * @param threadId
     * @param stopWatch
     */
    @Override
    public void handleChangeAllListBatchTwo(LogoResetStatus resetStatus, long threadId, StopWatch stopWatch) {
        try {
            long startWatchTime = stopWatch.getTime();

            List<LogoExceptionChange> changeAllList = new ArrayList<>();
            CommonUtils.executeActionByBatch(new Functions.ActionTwo<Date, Date>() {
                @Override
                public void invoke(Date data1, Date data2) {
                    int pageIndex = 0;
                    int pageCount = config.getComputeAllBillNumber();
                    do {
                        changeAllList.clear();
                        logoComputedService.buildChangeListByTdBatch(changeAllList, CurrentUser.getProfileId(), pageIndex, pageCount, data1, data2, config);
                        if (CollectionUtils.isEmpty(changeAllList)) {
                            break;
                        }
                        //写入change表
                        LogoUtil.syncExecuteFuncByBatch(subChangeList -> calculateMapper.addReQueueBatch(subChangeList), changeAllList);
                        //更新Limit条件
                        pageIndex += pageCount;
                    } while (changeAllList.size() > 0);
                }
            }, resetStatus.getStart(), resetStatus.getEnd(), 1);
            long stopWatchTime = stopWatch.getTime();
            logger.debug("[徽标工具-全量计算]profileId【{}】Thread【{}】,处理热表数据完成，构建耗时【{}】", CurrentUser.getProfileId(), threadId, stopWatchTime - startWatchTime);
        } catch (Exception ex) {
            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,处理热表数据报错：%s | %s",
                    CurrentUser.getProfileId(), threadId, ex.getMessage(), ex.getCause().getMessage());
            logger.error(errMsg, ex);
        }
    }

//    @Override
//    public void handleChangeAllListBatch(BigInteger profileId, long threadId, StopWatch stopWatch) {
//        try {
//            long startWatchTime = stopWatch.getTime();
//
//            List<LogoExceptionChange> changeAllList = new ArrayList<>();
//
//            //默认值
//            boolean computeAllDayNumEnabled = false;
//            int computeAllDayNum = 3;
//            Calendar calendar = Calendar.getInstance();
//            Date computeAllEndDate = calendar.getTime();
//            //账套级
//            EshopOrderGlobalConfig sysData = GlobalConfig.get(EshopOrderGlobalConfig.class);
//            int computeAllDayNumEnabledByProfile = sysData.getComputeAllDayNumEnabled();
//            if (0 == computeAllDayNumEnabledByProfile) {
//                //0：和部署保持一致
//                computeAllDayNumEnabled = config.isComputeAllDayNumEnabled();
//                computeAllDayNum = config.getComputeAllDayNum();
//                if (StringUtils.isNotEmpty(config.getComputeAllEndDate())) {
//                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//                    computeAllEndDate = formatter.parse(config.getComputeAllEndDate());
//                }
//            } else if (1 == computeAllDayNumEnabledByProfile) {
//                //1：账套级，开启按天数全量计算徽标
//                computeAllDayNumEnabled = true;
//                computeAllDayNum = sysData.getComputeAllDayNum();
//                if (StringUtils.isNotEmpty(sysData.getComputeAllEndDate())) {
//                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//                    computeAllEndDate = formatter.parse(sysData.getComputeAllEndDate());
//                }
//            }
//
//            int pageIndex = 0;
//            int pageCount = config.getComputeAllBillNumber();
//            do {
//                //清空结果集合
//                changeAllList.clear();
//
//                logoComputedService.buildChangeListByTdBatch(changeAllList, profileId, pageIndex, pageCount, computeAllDayNumEnabled, computeAllDayNum, computeAllEndDate);
//                if (CollectionUtils.isEmpty(changeAllList)) {
//                    break;
//                }
//                //写入change表
//                LogoUtil.syncExecuteFuncByBatch(subChangeList -> calculateMapper.addExceptionChangeBatch(subChangeList), changeAllList);
//                //更新Limit条件
//                pageIndex += pageCount;
//            } while (changeAllList.size() > 0);
//
//            long stopWatchTime = stopWatch.getTime();
//            logger.debug("[徽标工具-全量计算]profileId【{}】Thread【{}】,处理热表数据完成，构建耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
//        } catch (Exception ex) {
//            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,处理热表数据报错：%s | %s",
//                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
//            logger.error(errMsg, ex);
//        }
//    }

    @Override
    public void handleStateAllListBatch(BigInteger profileId, long threadId, StopWatch stopWatch) {
        try {
            long startWatchTime = stopWatch.getTime();

            List<LogoExceptionChange> changeAllList = new ArrayList<>();
            // 每页条数,默认10000
            int pageSize = config.getComputeAllStateNumber();
            // 最大条数
            int computeAllCount = config.getComputeAllStateCount();
            // 已读取条数
            int loadCount = 0;
            // 最后一条id
            BigInteger lastId = BigInteger.ZERO;
            // 最近变更天数
            int stateLastDays = config.getComputeAllStateLastDays();

            Date startDate = DateUtils.addDays(new Date(), -stateLastDays);
            // 循环次数，最大15次，避免死循环
            int times = 0;
            do {
                //清空结果集合
                changeAllList.clear();

                //查询该账套的state表
                List<LogoExceptionState> exceptionStateList = calculateMapper.queryExceptionStateAllBatch(profileId, lastId, pageSize, startDate);
                logoComputedService.buildReQueueListByState(changeAllList, profileId, exceptionStateList);
                if (CollectionUtils.isEmpty(changeAllList)) {
                    break;
                }
                //写入change表
                LogoUtil.syncExecuteFuncByBatch(subChangeList -> calculateMapper.addReQueueBatch(subChangeList), changeAllList);
                //更新下次查询条件
                lastId = exceptionStateList.get(exceptionStateList.size() - 1).getId();
                loadCount += exceptionStateList.size();
                times += 1;
            } while (changeAllList.size() > 0 && loadCount <= computeAllCount && times <= 15);

            long stopWatchTime = stopWatch.getTime();
            logger.debug("[徽标工具-全量计算]profileId【{}】Thread【{}】,处理state数据完成，构建耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
        } catch (Exception ex) {
            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,处理state数据报错：%s | %s",
                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
            logger.error(errMsg, ex);
        }
    }

//    @Override
//    public boolean computeAllUpdateSysData(BigInteger profileId, long threadId, StopWatch stopWatch) {
//        try {
//            long startWatchTime = stopWatch.getTime();
//
//            //修改sysData-全量计算时间为现在
//            int updateCount1 = calculateMapper.updateSysData(profileId, LogoState.LAST_RECALCULATION_EXCEPTION, DateUtils.formatDateTime(DateUtils.getDate()));
//            if (updateCount1 == 0) {
//                LogoSysData sysData = new LogoSysData();
//                sysData.setId(UId.newId());
//                sysData.setProfileId(profileId);
//                sysData.setSubName(LogoState.LAST_RECALCULATION_EXCEPTION);
//                sysData.setSubValue(DateUtils.formatDateTime(DateUtils.getDate()));
//                sysData.setDescription(LogoState.LAST_RECALCULATION_EXCEPTION_DESC);
//                sysData.setLastModifyEtypeId(null == CurrentUser.getEmployeeId() ? BigInteger.ZERO : CurrentUser.getEmployeeId());
//                calculateMapper.addSysData(sysData);
//            }
//            //修改sysData-立即计算的配置为0
//            int updateCount2 = calculateMapper.updateSysData(profileId, LogoState.AT_ONCE_RECALCULATION_EXCEPTION, LogoState.ZERO);
//            if (updateCount2 == 0) {
//                LogoSysData sysData = new LogoSysData();
//                sysData.setId(UId.newId());
//                sysData.setProfileId(profileId);
//                sysData.setSubName(LogoState.AT_ONCE_RECALCULATION_EXCEPTION);
//                sysData.setSubValue(LogoState.ZERO);
//                sysData.setDescription(LogoState.AT_ONCE_RECALCULATION_EXCEPTION_DESC);
//                sysData.setLastModifyEtypeId(null == CurrentUser.getEmployeeId() ? BigInteger.ZERO : CurrentUser.getEmployeeId());
//                calculateMapper.addSysData(sysData);
//            }
//
//            long stopWatchTime = stopWatch.getTime();
//            logger.debug("[徽标工具-全量计算]profileId【{}】Thread【{}】,写入change表完成，写入耗时【{}】", profileId, threadId, stopWatchTime - startWatchTime);
//            return true;
//        } catch (Exception ex) {
//            String errMsg = String.format("[徽标工具-全量计算]profileId【%s】Thread【%s】,写入change表报错：%s | %s",
//                    profileId, threadId, ex.getMessage(), ex.getCause().getMessage());
//            logger.error(errMsg, ex);
//            return false;
//        }
//    }


//    @Override
//    public void clearExState(BigInteger profileId, long threadId, StopWatch stopWatch) {
//        calculateMapper.clearAllState(profileId);
//    }

    @Override
    public boolean cleanComputeAllStartTime() {
        BigInteger profileId = CurrentUser.getProfileId();
        // 删除
        calculateMapper.deleteLogoSysData(profileId, Arrays.asList("sysGlobalStart2"));
        return true;
    }


    @Override
    public boolean computeAllUpdateSysDataBatchTwo(LogoResetStatus resetStatus, LogoSysConfig logoSysConfig, long threadId) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger etypeId = CurrentUser.getEmployeeId();
        // 删除
        calculateMapper.deleteLogoSysData(profileId, Arrays.asList("sysGlobalStart2", "sysGlobalEnd"));

        // 添加
        List<LogoSysData> logoSysDataList = new ArrayList<>();
        logoSysDataList.add(new LogoSysData(UId.newId(), profileId, "sysGlobalLastResetDate", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date()), "", etypeId));
        logoSysDataList.add(new LogoSysData(UId.newId(), profileId, "sysGlobalAtOnceRecalculationException", "0", "", etypeId));
//        logoSysDataList.add(new LogoSysData(UId.newId(), profileId, "sysGlobalLastId", "0", "", etypeId));
//        logoSysDataList.add(new LogoSysData(UId.newId(), profileId, "sysGlobalLastTimeSliceId", "0", "", etypeId));

        calculateMapper.addSysDataList(logoSysDataList);

        // calculateMapper.updateLogoSysData(CurrentUser.getProfileId(), UId.newId(), UId.newId(), Arrays.asList("sysGlobalStart", "sysGlobalEnd"), "sysGlobalAtOnceRecalculationException", "sysGlobalLastResetDate", new Date());
        return true;
    }

    @Override
    public LogoResetStatus crateResetInfo(LogoSysConfig logoSysConfig, LogoCalculateConfig logoCalculateConfig) {
        LogoResetStatus status = new LogoResetStatus();
        // 强制执行，要伪造开始时间
        // 新用户与新上线客户
        if (logoSysConfig == null) {
            logoSysConfig = new LogoSysConfig();
        }
        if (logoSysConfig.getStart2() != null) {
            status.setStart(logoSysConfig.getStart2());
        } else {
            // 重算时间范围内的时候
            initDayReset(logoSysConfig, logoCalculateConfig, status);
        }
        /**
         * 1. start == null 表示不执行
         */
        if (status.getStart() == null) {
            return null;
        }
        // 否则补充 截止时间
        if (logoSysConfig.getEnd() == null) {
            status.setEnd(new Date());
        } else {
            status.setEnd(logoSysConfig.getEnd());
        }
        return status;
    }

    @Override
    public void cleanStateByDate(BigInteger profileId, Date endDate) {
        calculateMapper.cleanStateByDate(profileId, endDate);
    }

    @Override
    public void cleanReQueueData(BigInteger profileId) {
        calculateMapper.cleanReQueueData(profileId);
    }

    /**
     * 分析是否需要当日重算
     */
    private static void initDayReset(LogoSysConfig logoSysConfig, LogoCalculateConfig logoCalculateConfig, LogoResetStatus status) {
//        Date startTime = DateUtils.parse(logoCalculateConfig.getTimeIntervalStart(), LogoState.HH_MM);
//        Date endTime = DateUtils.parse(logoCalculateConfig.getTimeIntervalEnd(), LogoState.HH_MM);
//        String now = DateUtils.formatDate(DateUtils.getDate(), LogoState.HH_MM);
//        Date nowTime = DateUtils.parse(now, LogoState.HH_MM);
//        // 不在全量计算时间范围内
//        if (!(startTime.after(nowTime) && nowTime.before(endTime))) {
//            return;
//        }
//        // 已执行
//        if (logoSysConfig.getLastResetDate() != null && startTime.after(logoSysConfig.getLastResetDate())) {
//            return;
//        }
//
//        if (logoSysConfig.getLastResetDate() != null
//                && new Date().before(DateUtils.addHours(logoSysConfig.getLastResetDate(), logoSysConfig.getResetComputeRate()))) {
//            return;
//        }

        Duration MIN_INTERVAL = Duration.ofHours(logoCalculateConfig.getResetComputeRate());
        LocalTime START_TIME = LocalTime.of(logoCalculateConfig.getIntervalStartTime(), 0);
        LocalTime END_TIME = LocalTime.of(logoCalculateConfig.getIntervalEndTime(), 0);

        LocalTime now = LocalTime.now();
        // 不在全量计算时间范围内
        if(!isInTimeWindow(now, START_TIME, END_TIME)){
            return;
        }

        // 查距离上次执行是否已超过18小时
        if(!shouldExecute(logoSysConfig, MIN_INTERVAL)){
            return;
        }

        status.setStart(DateUtils.addDays(new Date(), -logoSysConfig.getDateLimit()));
        logoSysConfig.setEnd(null);
    }

    private static boolean shouldExecute(LogoSysConfig logoSysConfig, Duration MIN_INTERVAL) {
        if (logoSysConfig.getLastResetDate() == null) {
            return true;
        }

        try {
            LocalDateTime lastRun = logoSysConfig.getLastResetDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            return Duration.between(lastRun, LocalDateTime.now()).compareTo(MIN_INTERVAL) >= 0;
        } catch (Exception e) {
            return true;
        }
    }

    private static boolean isInTimeWindow(LocalTime now, LocalTime START_TIME, LocalTime END_TIME) {
        return now.isAfter(START_TIME) || now.isBefore(END_TIME);
    }

    //构建删除数据
    private void buildDeleteStateList(List<LogoExceptionSummary> oldSummaryList,
                                      List<LogoExceptionSummary> updateSummaryList,
                                      List<LogoExceptionState> deleteStateList,
                                      List<LogoExceptionState> oldStateList,
                                      BigInteger profileId) {
        //删除
        deleteStateList.addAll(oldStateList);
        for (LogoExceptionState state : oldStateList) {
            if (CollectionUtils.isNotEmpty(oldSummaryList)) {
                List<LogoExceptionSummary> oldSummarys = oldSummaryList.stream()
                        .filter(s -> (null != s.getSourceType() && s.getSourceType().equals(state.getSourceType()))
                                && (null != s.getUsePhase() && s.getUsePhase().equals(state.getUsePhase()))
                                && (null != s.getStateType() && s.getStateType().equals(state.getStateType())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(oldSummarys)) {
                    //已经存在
                    buildSummary(updateSummaryList, profileId, state, false, false);
                }
            }
        }
    }

    //构建新增数据
    private void buildAddStateList(List<LogoExceptionSummary> oldSummaryList,
                                   List<LogoExceptionSummary> addSummaryList,
                                   List<LogoExceptionSummary> updateSummaryList,
                                   List<LogoExceptionState> addStateList,
                                   List<LogoExceptionState> newStateList,
                                   BigInteger profileId) {
        //新增
        addStateList.addAll(newStateList);
        for (LogoExceptionState state : newStateList) {
            if (CollectionUtils.isNotEmpty(oldSummaryList)) {
                List<LogoExceptionSummary> oldSummarys = oldSummaryList.stream()
                        .filter(s -> (null != s.getSourceType() && s.getSourceType().equals(state.getSourceType()))
                                && (null != s.getUsePhase() && s.getUsePhase().equals(state.getUsePhase()))
                                && (null != s.getStateType() && s.getStateType().equals(state.getStateType())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(oldSummarys)) {
                    //已经存在
                    buildSummary(updateSummaryList, profileId, state, true, false);
                } else {
                    //不存在
                    buildSummary(addSummaryList, profileId, state, true, true);
                }
            } else {
                //不存在
                List<LogoExceptionSummary> collect = addSummaryList.stream()
                        .filter(s -> (null != s.getSourceType() && s.getSourceType().equals(state.getSourceType()))
                                && (null != s.getUsePhase() && s.getUsePhase().equals(state.getUsePhase()))
                                && (null != s.getStateType() && s.getStateType().equals(state.getStateType())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    buildSummary(updateSummaryList, profileId, state, true, false);
                } else {
                    buildSummary(addSummaryList, profileId, state, true, true);
                }
            }
        }
    }

    //构建summary
    private void buildSummary(List<LogoExceptionSummary> summaryList,
                              BigInteger profileId,
                              LogoExceptionState state,
                              boolean isAdd,
                              boolean isNewAdd) {
        LogoExceptionSummary summary = new LogoExceptionSummary();
        summary.setProfileId(profileId);
        summary.setSourceType(state.getSourceType());
        summary.setStateType(state.getStateType());
        summary.setUsePhase(state.getUsePhase());
        if (isAdd) {
            if (isNewAdd) {
                summary.setId(UId.newId());
            }
            summary.setStateCount(summary.getStateCount() + 1);
        } else {
            summary.setStateCount(summary.getStateCount() - 1);
        }
        summaryList.add(summary);
    }

}
