package com.wsgjp.ct.sale.tool.tmc.impl.alibabaszxd.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class AlibabaSZXDEntity {
    private String reverseOrderID;
    private String linkOrderId;
    private String subOrderId;
    private String currentGoodsStatus;
    private String msgSendTime;
    private String currentStatus;


    public String getLinkOrderId() {
        return linkOrderId;
    }

    public void setLinkOrderId(String linkOrderId) {
        this.linkOrderId = linkOrderId;
    }

    public String getSubOrderId() {
        return subOrderId;
    }

    public void setSubOrderId(String subOrderId) {
        this.subOrderId = subOrderId;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    @JsonProperty("reverseOrderId")
    public String getReverseOrderID() { return reverseOrderID; }
    @JsonProperty("reverseOrderId")
    public void setReverseOrderID(String value) { this.reverseOrderID = value; }

    @JsonProperty("currentGoodsStatus")
    public String getCurrentGoodsStatus() { return currentGoodsStatus; }
    @JsonProperty("currentGoodsStatus")
    public void setCurrentGoodsStatus(String value) { this.currentGoodsStatus = value; }

    @JsonProperty("msgSendTime")
    public String getMsgSendTime() { return msgSendTime; }
    @JsonProperty("msgSendTime")
    public void setMsgSendTime(String value) { this.msgSendTime = value; }
}
