package com.wsgjp.ct.sale.tool.tmc.controller.yuanqi;

import com.wsgjp.ct.sale.monitor.bifrost.entity.BifrostApiMonitorInfo;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.yuanqi.YuanQiTMCResponse;
import com.wsgjp.ct.sale.tool.tmc.utils.TmcMonitor;
import ngp.utils.JsonUtils;

/**
 * <AUTHOR>
 */
public class YuanQiInvoker {

    public static void recordYqTmcMonitor(YuanQiTMCResponse response, String serviceName, InvokeMessageEntity invokeMessage, long start) {
        long end = System.currentTimeMillis();
        BifrostApiMonitorInfo monitorInfo = TmcMonitor.getApiMonitorInfo(serviceName, !response.isError(), response.getMessage(), response.getError() + "");
        String result = JsonUtils.toJson(response);
        TmcMonitor.monitor(monitorInfo, result, invokeMessage, end - start, response.isError());
    }
}
