package com.wsgjp.ct.sale.tool.tmc.biz;

import com.wsgjp.ct.sale.biz.eshoporder.impl.order.TmcOrderDownload;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderDetailEntity;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.order.entity.ReceiverInfo;
import com.wsgjp.ct.sale.tool.tmc.entity.NotifyEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.OrderItem;
import com.wsgjp.ct.sale.tool.tmc.entity.OrderRequest;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TMExecutor {
    private static final Logger sysLogger = LoggerFactory.getLogger(TMExecutor.class);

    public EshopOrderEntity buildTmOrder(OrderRequest request, BigInteger profileid, BigInteger eshopid){
        EshopOrderEntity eshopSaleOrder = new EshopOrderEntity();
        eshopSaleOrder.setProfileId(profileid);
        eshopSaleOrder.setEshopId(eshopid);
        eshopSaleOrder.setBuyerFreightFee(request.getPostFee());
        eshopSaleOrder.setTradeId(request.getBizOrderCode());
        eshopSaleOrder.setCreateTime(stringToDate(request.getOrderCreateTime(),"yyyy-MM-dd HH:mm:ss"));
        eshopSaleOrder.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        ReceiverInfo receiverInfo = new ReceiverInfo();
        receiverInfo.setCustomerPayAccount(request.getReceiverInfo().getReceiverName());
        receiverInfo.setReceiver(request.getReceiverInfo().getReceiverName());
        receiverInfo.setCustomerMobile(request.getReceiverInfo().getReceiverMobile());
        receiverInfo.setCustomerTel(request.getReceiverInfo().getReceiverPhone());
        receiverInfo.setCustomerAddress(request.getReceiverInfo().getReceiverAddress());
        receiverInfo.setCustomerCity(request.getReceiverInfo().getReceiverCity());
        receiverInfo.setCustomerCountry("");
        receiverInfo.setCustomerDistrict(request.getReceiverInfo().getReceiverArea());
        receiverInfo.setCustomerProvince(request.getReceiverInfo().getReceiverProvince());
        eshopSaleOrder.setReceiverInfo(receiverInfo);
        eshopSaleOrder.setStoreId(request.getStoreCode());
        eshopSaleOrder.setFreightName(request.getTmsServiceName());
        if(request.getDeliverRequirement()!=null){
            //order.OsDate = orderBody.DeliverRequirement.AppointDeliveryTime;
           // order.EsDate = orderBody.DeliverRequirement.AppointArrivedTime;
        }
        eshopSaleOrder.setOrderDetails(buildOrderDetails(request.getOrderItems().getOrderItems()));
        sysLogger.info(String.format("订单号:{%s},主表金额:{%s},明细总金额:{%s}", eshopSaleOrder.getTradeId(), eshopSaleOrder.getTradeTotal(), eshopSaleOrder.getOrderDetails().stream().map(EshopOrderDetailEntity::getTradeTotal).reduce(BigDecimal.ZERO,BigDecimal::add)));
        eshopSaleOrder.setPreferenceTotal (new BigDecimal(0));

        return eshopSaleOrder;
    }

    public List<EshopOrderDetailEntity> buildOrderDetails(List<OrderItem>orderItems){
        List<EshopOrderDetailEntity> details= new ArrayList<>();
        for(OrderItem orderItem : orderItems){
            EshopOrderDetailEntity eshopSaleOrderDetail = new EshopOrderDetailEntity();
            eshopSaleOrderDetail.setTitle(orderItem.getScItemName());
            eshopSaleOrderDetail.setNumId(orderItem.getScItemId());
            eshopSaleOrderDetail.setXcode(orderItem.getOuterId());
            eshopSaleOrderDetail.setQty(new BigDecimal(orderItem.getQuantity()));
            eshopSaleOrderDetail.setOid(orderItem.getSubOrderCode());
            eshopSaleOrderDetail.setTradeTotal(orderItem.getItemAmount().divide(new BigDecimal(100)));
            eshopSaleOrderDetail.setTaxFee(new BigDecimal(0));
            eshopSaleOrderDetail.setPrice(eshopSaleOrderDetail.getTradeTotal().divide(eshopSaleOrderDetail.getQty()));
            eshopSaleOrderDetail.setPreferentialPrice(new BigDecimal(0));
            details.add(eshopSaleOrderDetail);
        }
        return  details;
    }


    public Date stringToDate(String date, String format) {
        Date time = null;
        try {
            SimpleDateFormat ft = new SimpleDateFormat(format);
            time = ft.parse(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return time;
    }

    public void execute(NotifyEntity notifyEntity) throws Exception {
        if(notifyEntity==null|| StringUtils.isEmpty(notifyEntity.getContent())){
            sysLogger.error("天猫超市订单推送消息为空！");
            return;
        }
        OrderRequest request1 = convertToJavaBean(notifyEntity.getContent(), OrderRequest.class);
        EshopOrderEntity eshopOrderEntity = buildTmOrder(request1, notifyEntity.getProfileId(), notifyEntity.getEshopId());
        TmcOrderDownload tmcOrderDownload = new TmcOrderDownload();
        List<EshopOrderEntity> entities= new ArrayList<>();
        entities.add(eshopOrderEntity);
        tmcOrderDownload.tmcOrderDownload(entities,notifyEntity.getEshopId());

    }
    public static <T> T convertToJavaBean(String xml, Class<T> t) throws Exception {
        int i = xml.indexOf('>');
        String substring = xml.substring(i+1);
        T obj = null;
        JAXBContext context = JAXBContext.newInstance(t);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        obj = (T) unmarshaller.unmarshal(new StringReader(xml));
        return obj;
    }
}
