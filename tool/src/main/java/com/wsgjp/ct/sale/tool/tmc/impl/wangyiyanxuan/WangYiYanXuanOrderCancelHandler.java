package com.wsgjp.ct.sale.tool.tmc.impl.wangyiyanxuan;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.wangyiyanxuan.response.CallbackCancleResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import wangyiyanxuansdk.entity.Order;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 订单取消消息处理
 */
@Component
public class WangYiYanXuanOrderCancelHandler extends WangYiYanXuanNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(WangYiYanXuanOrderCancelHandler.class);
    private final TmcNotifyProxy notifyProxy;
    public WangYiYanXuanOrderCancelHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        ShopType shopType = invokeMessage.getShopType();
        LOGGER.info("======{}进入invoker方法======",shopType.getName());
        List<Order> cancelOrderEntity;
        try {
             cancelOrderEntity = JsonUtils.toList(invokeMessage.getMessage(), Order.class);
        } catch (Exception e) {
            LOGGER.error("{}数据转换成Order实体出错,错误信息：{},tmcMessage:{}",
                    shopType.getName(),e.getMessage(),invokeMessage.getMessage());
            return "{\n" +
                    "    \"code\":\"401\",\n" +
                    "    \"errorString\":\"发生异常了\"\n" +
                    "}";
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(invokeMessage.getParams().get("appKey"), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",
                    invokeMessage.getProfileId(),shopType.getName(),invokeMessage.getParams().get("appKey"),invokeMessage.getMessage());
            return "{\n" +
                    "    \"code\":\"401\",\n" +
                    "    \"errorString\":\"发生异常了\"\n" +
                    "}";
        }
        List<CallbackCancleResponse.Data> data = new ArrayList<>();
        for (Order order : cancelOrderEntity) {
            //更新消息表中的订单状态
            //updateTmcOrderStatus(invokeMessage.getProfileId(),eshopRegister.getId(),order.getOutNumber());
            //取消订单回告成功,发送消息修改原始订单状态
            TmcInvokeRequest request = new TmcInvokeRequest();
            request.setProfileId(eshopRegister.getProfileId());
            request.setMethod(TmcNotifyMethodEnum.ORDER_CANCEL);
            request.setShopType(ShopType.WangYiYanXuan);
            request.setEshopId(eshopRegister.getId());
            request.setTradeId(order.getOutNumber());
            request.setAsynCallback(false);
            TmcInvokeResponse resp = notifyProxy.execute(request);
            if (!"200".equals(resp.getCode())){
                String errMsg = String.format("账套id【%d】网店ID【%d】订单号【%s】 回告订单是否取消失败,错误信息%s"
                        , eshopRegister.getProfileId()
                        , eshopRegister.getId()
                        , order.getOutNumber()
                        , resp.getMessage());
                LOGGER.error(errMsg);
                CallbackCancleResponse.Data data1 = new CallbackCancleResponse.Data();
                data1.setOutNumber(order.getOutNumber());
                data1.setResult("0");
                data1.setErrorString(resp.getMessage());
                data.add(data1);
            }else {
                CallbackCancleResponse.Data data1 = new CallbackCancleResponse.Data();
                data1.setOutNumber(order.getOutNumber());
                data1.setResult("1");
                data.add(data1);
            }
        }
        CallbackCancleResponse response = new CallbackCancleResponse();
        response.setCode("200");
        response.setErrorString("");
        response.setData(data);
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "yx.supplier.push.order.cancel";
    }
}
