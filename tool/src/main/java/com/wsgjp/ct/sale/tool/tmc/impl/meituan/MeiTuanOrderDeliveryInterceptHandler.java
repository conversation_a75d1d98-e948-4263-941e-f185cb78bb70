package com.wsgjp.ct.sale.tool.tmc.impl.meituan;

import com.alibaba.fastjson.JSON;
import com.wsgjp.ct.common.enums.core.enums.tmc.TmcNotifyResponseEnum;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.tmc.CustomOrderLogMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.ErrorInfo;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.MeiTuanOrderMessage;
import com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity.OrderDeliveryInterceptResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;

/**
 * 自配订单配送中秒退配送拦截
 *
 * <AUTHOR>
 */
@Component
public class MeiTuanOrderDeliveryInterceptHandler extends MeiTuanNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(MeiTuanOrderDeliveryInterceptHandler.class);
    private final TmcNotifyProxy notifyProxy;
    private final EshopTmcUtils eshopTmcUtils;

    public MeiTuanOrderDeliveryInterceptHandler(TmcNotifyProxy notifyProxy, EshopTmcUtils eshopTmcUtils) {
        this.notifyProxy = notifyProxy;
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        String tmMessage = invokeMessage.getMessage();
        MeiTuanOrderMessage order;
        try {
            order = JsonUtils.toObject(tmMessage, MeiTuanOrderMessage.class);
        } catch (Exception ex) {
            LOGGER.error("{}tmMessage数据转换成美团订单信息实体出错，错误信息：{}", shopTypeName, ex.getMessage(), ex);
            return buildResponse(ex.getMessage());
        }
        try {
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), invokeMessage.getEshopId());
            if (Objects.isNull(eshopInfo)) {
                LOGGER.error("profileId:{},店铺类型:{},appPoiCode:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, order.getAppPoiCode(), tmMessage);
                return buildResponse("管家婆未找到对应店铺!");
            }
            TmcInvokeRequest request = buildTmcInvokeRequest(order, invokeMessage);
            TmcInvokeResponse resp = notifyProxy.execute(request);
            EshopNotifyChange change = new EshopNotifyChange();
            change.setContent(invokeMessage.getMessage());
            change.setTradeOrderId(order.getTradeId());
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(order.getAppPoiCode());
            change.setProfileId(invokeMessage.getProfileId());
            change.setEshopId(eshopInfo.getOtypeId());
            change.setCreateTime(new Date());
            change.setShopType(invokeMessage.getShopType().getCode());
            SupportUtil.sendMessage(change, eshopInfo);
            //是否需要退款
            return buildResponse(TmcNotifyResponseEnum.SUCCESS.getCode().equals(resp.getCode()) ? "" : resp.getMessage());
        } catch (Exception ex) {
            String errMsg = ex.getMessage();
            errMsg = StringUtils.isEmpty(errMsg) ? "" : errMsg;
            return buildResponse(errMsg);
        }
    }

    private TmcInvokeRequest buildTmcInvokeRequest(MeiTuanOrderMessage order, InvokeMessageEntity invokeMessage) {
        TmcInvokeRequest request = new TmcInvokeRequest();
        request.setEshopId(invokeMessage.getEshopId());
        request.setTradeId(order.getTradeId());
        request.setMessage(invokeMessage.getMessage());
        request.setMethod(TmcNotifyMethodEnum.AG);
        CustomOrderLogMessage logMessage = new CustomOrderLogMessage();
        logMessage.setSuccessMsg("收到平台自配订单配送中秒退配送拦截，回告平台成功");
        logMessage.setFailMsg("收到平台自配订单配送中秒退配送拦截，回告平台失败");
        request.setCustomOrderLogMessage(logMessage);
        return request;
    }

    public static String buildResponse(String errMsg) {
        return JSON.toJSONString(buildResponseObj(errMsg));
    }

    public static OrderDeliveryInterceptResponse buildResponseObj(String errMsg) {
        OrderDeliveryInterceptResponse response = new OrderDeliveryInterceptResponse();
        //result_code 1-查询成功，3-查询失败，4-限流、验签等系统级错误
        if (StringUtils.isBlank(errMsg)) {
            response.setResultCode(1);
            response.setData("ok");
        } else {
            ErrorInfo errorInfo = new ErrorInfo();
            errorInfo.setMsg(errMsg);
            errorInfo.setCode(3);
            response.setErrorList(Collections.singletonList(errorInfo));
            response.setData(errMsg);
            response.setResultCode(3);
        }
        return response;
    }

    @Override
    public String serviceName() {
        return "orderIntercept";
    }
}
