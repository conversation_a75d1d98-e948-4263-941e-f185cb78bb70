package com.wsgjp.ct.sale.tool.tmc.utils;

import cn.hutool.json.JSONUtil;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

public class HttpRequestUtils {

    private static final Logger logger = LoggerFactory.getLogger(HttpRequestUtils.class);
    public static Map<String, String>  parseForm(String body) throws UnsupportedEncodingException {
        Map<String, String> form = new HashMap<>();
        String[] kvs = body.split("&");
        for (String kv : kvs) {
            String[] kv2 = kv.split("=");
            String value = kv2.length > 1 ? kv2[1] : null;
            if (value != null) {
                value = URLDecoder.decode(value, "UTF-8");
            }
            form.put(kv2[0], value);
        }
        return form;
    }

    public static String parseFormToJsonStr(String body) throws UnsupportedEncodingException{
        return JSONUtil.toJsonStr(parseForm(body));
    }

    public static BigInteger getOtypeIdByHeader(HttpServletRequest request){
        try {
            String otypeid = request.getHeader("otypeid");
            if (StringUtils.isEmpty(otypeid)){
                return null;
            }
            return new BigInteger(otypeid);
        }catch (Exception ex){
            logger.error("getOtypeIdByHeader error:{}", ex.getMessage(), ex);
            return null;
        }
    }

    public static Map<String, String> getHeaderMap(HttpServletRequest request, String charset) throws IOException {
        Map<String, String> headerMap = new HashMap();
        String signList = request.getHeader("top-sign-list");
        if (!com.taobao.api.internal.util.StringUtils.isEmpty(signList)) {
            String[] keys = signList.split(",");
            String[] var5 = keys;
            int var6 = keys.length;

            for (int var7 = 0; var7 < var6; ++var7) {
                String key = var5[var7];
                String value = request.getHeader(key);
                if (com.taobao.api.internal.util.StringUtils.isEmpty(value)) {
                    headerMap.put(key, "");
                } else {
                    headerMap.put(key, URLDecoder.decode(value, charset));
                }
            }
        }

        return headerMap;
    }
}
