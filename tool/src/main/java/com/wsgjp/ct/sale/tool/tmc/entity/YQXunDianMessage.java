package com.wsgjp.ct.sale.tool.tmc.entity;

public class YQXunDianMessage {
    /**
     * 接入appkey
     */
    private String appId;
    /**
     * 客户编码=》对应shopaccount
     */
    private String merchantNumber;
    /**
     * 经销商再erp系统中的唯一编码
     */
    private String merchantErpId;
    /**
     * 客户名称
     */
    private String merchantName;
    /**
     * 创建时间
     */
    private String createdAT;
    /**
     * 事件类型
     */
    private String method;
    /**
     * 本次消息对象唯一标识
     */
    private String _id;
    /**
     * Orderid
     */
    private String orderId;
    private String remark;
    private String name;
    private String mobile;
    private int status;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMerchantNumber() {
        return merchantNumber;
    }

    public void setMerchantNumber(String merchantNumber) {
        this.merchantNumber = merchantNumber;
    }

    public String getMerchantErpId() {
        return merchantErpId;
    }

    public void setMerchantErpId(String merchantErpId) {
        this.merchantErpId = merchantErpId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCreatedAT() {
        return createdAT;
    }

    public void setCreatedAT(String createdAT) {
        this.createdAT = createdAT;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
