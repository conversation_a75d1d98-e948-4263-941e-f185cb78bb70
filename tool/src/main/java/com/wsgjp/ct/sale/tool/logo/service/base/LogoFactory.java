package com.wsgjp.ct.sale.tool.logo.service.base;

import com.wsgjp.ct.sale.sdk.logo.state.LogoSourceTypeEnum;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-03-15 9:53
 */
@Component
public class LogoFactory {

    private List<BaseLogoComputedAbstract> computedList;

    public LogoFactory(List<BaseLogoComputedAbstract> computedList) {
        this.computedList = computedList;
    }

    public BaseLogoComputedAbstract getComputed(LogoSourceTypeEnum sourceType){
        if (null==sourceType) {
            return null;
        }
        for (BaseLogoComputedAbstract computed : computedList) {
            if (sourceType.equals(computed.getSourceType())) {
                return computed;
            }
        }
        return null;
    }
}
