package com.wsgjp.ct.sale.tool.tmc.impl.alijiankang;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.response.logistics.BaseResponse;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.request.order.PushMessageRequest;
import com.wsgjp.ct.sale.platform.entity.response.tmc.CancelOrderResponse;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.dto.AliJianKangOrderCancelDto;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

/**
 * 订单取消消息处理
 */
@Component
public class AliJianKangOrderCancelHandler extends AliJianKangNotifyBase implements MessageHandler {
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;

    public AliJianKangOrderCancelHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        ShopType shopType = invokeMessage.getShopType();
        LOGGER.info("======{}进入invoker方法======", shopType.getName());
        // 一、获取订单列表
        BifrostEshopOrderService eshopOrderService = GetBeanUtil.getBean(BifrostEshopOrderService.class);
        AliJianKangOrderCancelDto cancelOrderEntity;
        try {
            cancelOrderEntity = JsonUtils.toObject(invokeMessage.getMessage(), AliJianKangOrderCancelDto.class);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成AliJianKangOrderCancelDto实体出错，错误信息：{}", shopType.getName(), e.getMessage());
            return buildInvokeResult(false, "isv系统转换消息失败", "201");
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(cancelOrderEntity.getSupplierId(), invokeMessage.getShopType().getCode());
        if (Objects.isNull(eshopRegister)) {
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",
                    invokeMessage.getProfileId(), shopType.getName(), cancelOrderEntity.getSupplierId(), invokeMessage.getMessage());
            return buildInvokeResult(false, "isv系统内未找到对应的注册账号信息", "201");
        }
        PushMessageRequest pushMessageRequest = new PushMessageRequest();
        pushMessageRequest.setMessage(invokeMessage.getMessage());
        pushMessageRequest.setShopId(eshopRegister.getId());
        CancelOrderResponse cancelOrderResponse = eshopOrderService.cancelOrder(pushMessageRequest);
        // 判断订单能否取消
        DeliverService deliverService = GetBeanUtil.getBean(DeliverService.class);
        // 0表示可以取消  1表示不可以取消（msg会有消息）
        BaseResponse baseResponse = deliverService.checkCanCancelDeliver(cancelOrderResponse.getTradeId());
        if ("1".equals(baseResponse.getCode())) {
            return buildInvokeResult(false, "已发货不能取消", "ZFSCM_CANCEL_SHIPORDER_DELIVERED");
        }
        //更新消息表中的订单状态
        updateTmcOrderStatus(invokeMessage.getProfileId(), eshopRegister.getId(), cancelOrderResponse.getTradeId());
        //取消订单回告成功,发送消息修改原始订单状态
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(cancelOrderEntity.getSupplierId());
        //必传
        eshopNotifyChange.setContent("");
        eshopNotifyChange.setTradeOrderId(cancelOrderResponse.getTradeId());
        eshopNotifyChange.setType(TMCType.Order);
        SupportUtil.doOrderNotify(cancelOrderEntity.getSupplierId(), eshopNotifyChange, invokeMessage.getShopType().getCode());
        return buildInvokeResult(true, "", "");
    }

    private void updateTmcOrderStatus(BigInteger profileId, BigInteger eshopId, String tradeId) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setProfileId(profileId);
        tmcOrderMsgEntity.setEshopId(eshopId);
        tmcOrderMsgEntity.setTradeOrderId(tradeId);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.ALL_CLOSED);
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgMapper.updateTmcOrderMsg(tmcOrderMsgEntity);
    }

    @Override
    public String serviceName() {
        return "qimen.alibaba.alihealth.shiporder.cancel";
    }
}
