package com.wsgjp.ct.sale.tool.bus;

import com.wsgjp.ct.sale.bus.center.BusDataCenter;
import com.wsgjp.ct.sale.bus.center.BusStarter;
import com.wsgjp.ct.sale.bus.entity.BusTaskInfo;
import com.wsgjp.ct.sale.bus.utils.BusDataLockerImpl;
import com.wsgjp.ct.sale.tool.logo.config.ToolCalculateConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@ConditionalOnProperty(value = "sale-bus-order.enabled", havingValue = "true")
public class OrderBusConsumerInvoker extends AbstractBusConsumerInvoker<BusTaskInfo>{
    Logger logger = LoggerFactory.getLogger(OrderBusConsumerInvoker.class);
    public OrderBusConsumerInvoker(ToolCalculateConfig toolConfig, BusStarter busStarter, BusDataCenter busDataCenter, BusDataLockerImpl busDataLocker) {
        super(toolConfig, busStarter, busDataCenter, busDataLocker);
        logger.info("sale-bus-order start");
    }

    @Override
    protected void invokeTraining(BusTaskInfo taskInfo) {
    }

    @Override
    public String name() {
        return "sale-bus-order";
    }

    @Override
    public List<BusTaskInfo> producer(String s) {
        return Collections.emptyList();
    }
}
