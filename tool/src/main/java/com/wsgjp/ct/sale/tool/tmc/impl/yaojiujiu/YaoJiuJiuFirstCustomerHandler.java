package com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu;

import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu.entity.Customer;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

@Component
public class YaoJiuJiuFirstCustomerHandler extends TaobaoNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(YaoJiuJiuFirstCustomerHandler.class);
    private final TmcEshopNotifyChangeMapper tmcMapper;
    public YaoJiuJiuFirstCustomerHandler(TmcEshopNotifyChangeMapper tmcMapper) {
        this.tmcMapper = tmcMapper;
    }


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        YJJResponse resp = new YJJResponse();
        if (invokeMessage.getEshopId() == null || invokeMessage.getEshopId().compareTo(BigInteger.ZERO) == 0){
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(invokeMessage.getOnlineEshopId(), invokeMessage.getShopType().getCode());
            invokeMessage.setEshopId(eshopRegister.getId());
        }
        Customer customer;
        try {
            customer = JsonUtils.toObject(tmMessage, Customer.class);
            if (customer == null){
                throw new RuntimeException("药九九转换实体为空");
            }
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成Customer实体出错，错误信息：{}",shopTypeName,e.getMessage(),e);
            resp.setCode(203);
            resp.setSuccess(false);
            resp.setMsg("json转换实体报错!");
            return ngp.utils.JsonUtils.toJson(resp);
        }

        try{
            saveTmcNotifyChange(invokeMessage,customer);
        }catch (Exception ex){
            LOGGER.error("{}保存TMC首次开户消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,invokeMessage.getProfileId(),invokeMessage.getEshopId(),tmMessage,ex.getMessage(),ex);
            resp.setCode(203);
            resp.setSuccess(false);
            resp.setMsg("首次开户信息保存到数据库报错!");
            return ngp.utils.JsonUtils.toJson(resp);
        }
        resp.setCode(200);
        resp.setSuccess(true);
        resp.setMsg("成功");
        return ngp.utils.JsonUtils.toJson(resp);
    }

    private int saveTmcNotifyChange(InvokeMessageEntity invokeMessage, Customer customer) {
        try {
            EshopNotifyChange notifyChange = tmcMapper.queryMessageChangeByTradeId(invokeMessage.getProfileId(), invokeMessage.getEshopId(), customer.getB2BAccounts());
            if (Objects.isNull(notifyChange)){
                notifyChange = buildEshopNotifyChangeMsgEntity(invokeMessage,customer);
                return tmcMapper.insertMessageChange(notifyChange);
            }else {
                notifyChange.setUpdateTime(new Date());
                notifyChange.setContent(invokeMessage.getMessage());
                return tmcMapper.updateEshopNotifyChange(notifyChange);
            }
        } catch (Exception e) {
            LOGGER.error("账单和订单关系保存notify_change表失败：profileid:{},店铺id:{}，错误信息:{}",invokeMessage.getProfileId(),invokeMessage.getEshopId(),e.getMessage(), e);
        }
        return 0;
    }

    private EshopNotifyChange buildEshopNotifyChangeMsgEntity(InvokeMessageEntity invokeMessage, Customer customer) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setId(UId.newId());
        change.setProfileId(invokeMessage.getProfileId());
        change.setEshopId(invokeMessage.getEshopId());
        change.setTradeOrderId(customer.getB2BAccounts());
        change.setContent(invokeMessage.getMessage());
        change.setCreateTime(new Date());
        change.setUpdateTime(new Date());
        change.setType(TMCType.CUSTOMER_INFORMATION);
        return change;
    }

    @Override
    public String serviceName() {
        return "yjjFirstCustomer";
    }
}
