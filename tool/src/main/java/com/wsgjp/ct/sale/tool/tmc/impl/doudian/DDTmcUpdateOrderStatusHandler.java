package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.factory.doudian.util.FangXinGouEShopUtils;
import com.wsgjp.ct.sale.tool.tmc.controller.TmcConst;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Component
public class DDTmcUpdateOrderStatusHandler extends DDNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DDTmcUpdateOrderStatusHandler.class);
    private final EshopTmcConfig config;
    public DDTmcUpdateOrderStatusHandler(EshopTmcConfig eshopTmcConfig) {
        this.config = eshopTmcConfig;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======抖店进入DDUpdateStatusTrade方法======");
        if (StringUtils.isEmpty(invokeMessage.getMessage())) {
            LOGGER.error("抖店消息为空{}",JsonUtils.toJson(invokeMessage));
            return TmcConst.FAIL;
        }

        EshopNotifyChange change = buildEshopNotifyChange(invokeMessage);
        if (Objects.isNull(change)) {
            //当前未对接该类型的 tmc 消息
            LOGGER.info("账套ID{},转换成EshopNotifyChange为空", invokeMessage.getProfileId());
            return TmcConst.SUCCESS;
        }

        //这里发订单变更消息，系统会直接更新
//        try {
//            SupportUtil.doUpdateOrderStatusNotifyByDelayWay(change.getOnlineShopId(), change, invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.Doudian.getCode(),config.getTmcDDSendDelayTime());
//        } catch (Exception e) {
//            LOGGER.error("抖店直接修改订单交易状态失败" + e.getMessage(),e);
//        }
        // 为什么还要走常规更新，因为只修改交易状态可能还需要其他参数更新，比如已发货的物流信息。
        change.setType(TMCType.Order);
        change.setId(UId.newId());
        SupportUtil.doOrderNotifyByDelayWay(change.getOnlineShopId(), change, invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.Doudian.getCode(),config.getTmcDDSendDelayTime());
        return TmcConst.SUCCESS;
    }

    private EshopNotifyChange buildEshopNotifyChange(InvokeMessageEntity invokeMessage) {
        Map<String, Object> ddMessage = JsonUtils.toHashMap(invokeMessage.getMessage());
        Map<String, Object> data = JsonUtils.toHashMap(ddMessage.get("data").toString());
        String tag = (String) ddMessage.get("tag");
        EshopNotifyChange changeInfo;
        switch (Integer.valueOf(tag)){
            case 100:
            case 101:
            case 110:
            case 108:
            case 102:
            case 106:
            case 103:
                changeInfo = handlerMessageByType(data,TMCType.DIRECT_UPDATE_TRADE_STATUS);
                break;
            //备注旗帜相关
            case 113:
                changeInfo = handlerMessageByType(data,TMCType.DIRECT_UPDATE_MEMO);
                break;
            default:
                changeInfo = null;
        }
        return changeInfo;
    }

    private EshopNotifyChange handlerMessageByType(Map<String, Object> data, TMCType tmcType) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(JsonUtils.toJson(data));
        Object pId = data.get("p_id");
        Object shopId = data.get("shop_id");
        Object orderStatus = data.get("order_status");
        Object remark = data.get("remark");
        Object star = data.get("star");
        if (orderStatus != null){
            change.setOrderStatus(FangXinGouEShopUtils.getLocalOrderStatus(String.valueOf(orderStatus)));
        }
        if (remark != null){
            change.setSellerMemo(remark.toString());
        }
        if (star != null){
            change.setSellerFlag(FangXinGouEShopUtils.getSellerFlag(star.toString()));
        }
        change.setTradeOrderId(pId.toString());
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(shopId.toString());
        Object createTime = data.get("create_time");
        buildPubTime(createTime,change);
        Object payTime = data.get("pay_time");
        buildPubTime(payTime,change);
        Object updateTime = data.get("update_time");
        buildPubTime(updateTime,change);
        Object cancelTime = data.get("cancel_time");
        buildPubTime(cancelTime,change);
        Object completeTime = data.get("complete_time");
        buildPubTime(completeTime,change);
        return change;
    }

    private void buildPubTime(Object time, EshopNotifyChange change) {
        if (time != null){
            change.setPubTime(new Date(Long.parseLong(time.toString()) * 1000));
            return;
        }
        change.setPubTime(DateUtils.getDate());
    }

    @Override
    public String serviceName() {
        return "dou.dian.update.tradeStatus";
    }
}
