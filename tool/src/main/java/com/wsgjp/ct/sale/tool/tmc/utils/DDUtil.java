package com.wsgjp.ct.sale.tool.tmc.utils;

import com.google.gson.*;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

public class DDUtil {

    private static BigInteger profileid;
    private static final Gson GSON = new GsonBuilder().disableHtmlEscaping().create();
    private static final Gson GSON_WITH_NULL = new GsonBuilder().disableHtmlEscaping().serializeNulls().create();

    /**
     * 验签检查
     * @param paramJson
     * @param timestamp
     * @param sign
     * @return
     */
    public static boolean checkSign(String paramJson,String timestamp,String sign,String appKey,String appSecret){
        JsonObject jsonObject = fromJsonAsJsonObject(paramJson);
        LinkedHashMap<String, Object> sortedMap = sortParamJson(jsonObject);
        String sortedParamStr = null;
        if (sortedMap != null) {
            sortedParamStr = toJsonWithNull(sortedMap);
        } else {
            sortedParamStr = "{}";
        }
        String signPattern = appSecret + "app_key" + appKey + "param_json" + sortedParamStr +"timestamp" + timestamp + appSecret;
        String newSign = stringToMD5(signPattern);
        if (newSign.equals(sign)){
            return true;
        }else {
            sortedParamStr = sortedParamStr.replaceAll("&", "\\\\u0026");
            signPattern = appSecret + "app_key" + appKey + "param_json" + sortedParamStr +"timestamp" + timestamp + appSecret;
            String newSign1 = stringToMD5(signPattern);
            if (newSign1.equals(sign)){
                return true;
            }else {
                return false;
            }
        }
    }

    /**
     * 排序
     * @param paramJsonObject
     * @return
     */
    public static LinkedHashMap<String, Object> sortParamJson(JsonObject paramJsonObject){
        if (paramJsonObject == null) {
            return null;
        }
        if (paramJsonObject.size() == 0) {
            return new LinkedHashMap<>();
        }
        List<String> keys = new ArrayList<>(paramJsonObject.keySet());
        Collections.sort(keys);
        LinkedHashMap<String, Object> retMap = new LinkedHashMap<>();
        for (String key : keys) {
            Object valueObj = paramJsonObject.get(key);
            if (valueObj instanceof JsonObject) {
                retMap.put(key, sortParamJson((JsonObject) valueObj));
            } else if(valueObj instanceof JsonArray) {
                JsonArray arrayObj = (JsonArray) valueObj;
                if (arrayObj.size() > 0 && arrayObj.get(0) instanceof JsonObject) {
                    List<LinkedHashMap<String, Object>> newList = new ArrayList<>();
                    for(Object obj : arrayObj) {
                        if (obj instanceof JsonObject) {
                            newList.add(sortParamJson((JsonObject) obj));
                        }
                    }
                    retMap.put(key, newList);
                } else {
                    retMap.put(key, paramJsonObject.get(key));
                }
            }
            else {
                retMap.put(key, paramJsonObject.get(key));
            }
        }
        return retMap;
    }

    /**
     * md5加密
     * @param plainText
     * @return
     */
    public static String stringToMD5(String plainText){
        byte[] secretBytes = null;
        try {
            secretBytes = MessageDigest.getInstance("md5").digest(
                    plainText.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        StringBuilder md5code = new StringBuilder(new BigInteger(1, secretBytes).toString(16));
        while (md5code.length() < 32) {
            md5code.insert(0, "0");
        }
        return md5code.toString();
    }

    public static String toJson(Object obj){
        return GSON.toJson(obj);
    }


    public static String toJsonWithNull(Object obj){
        return GSON_WITH_NULL.toJson(obj);
    }

    public static <T> T fromJson(String jsonStr, Class<T> clazz)  {
        return GSON.fromJson(jsonStr, clazz);
    }

    public static JsonObject fromJsonAsJsonObject(String jsonStr) {
        return JsonParser.parseString(jsonStr).getAsJsonObject();
    }

    public static void setProfileid(BigInteger pro ){
        profileid = pro;
    }

    public static BigInteger getProfileid(){
        return profileid;
    }
}
