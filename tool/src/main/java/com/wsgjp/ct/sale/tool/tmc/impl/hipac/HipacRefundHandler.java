package com.wsgjp.ct.sale.tool.tmc.impl.hipac;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcRefundMsgMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcRefundMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.factory.hipac.HipacRefund;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac.newhipac.HipacNewRefundResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class HipacRefundHandler extends HipacNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(HipacOrderHandler.class);

    private final EshopTmcRefundMsgMapper tmcRefundMsgMapper;

    public HipacRefundHandler(EshopTmcRefundMsgMapper tmcRefundMsgMapper) {
        this.tmcRefundMsgMapper = tmcRefundMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======海拍客进入invoker方法======");
        String tmMessage = invokeMessage.getMessage();
        HipacNewRefundResponse hipacNewRefundResponse;
        try {
            hipacNewRefundResponse = JsonUtils.toObject(tmMessage, HipacNewRefundResponse.class);
        } catch (Exception e) {
            String errMsg = String.format("海拍客tmMessage数据转换成HipacNewRefundResponse实体出错，错误信息：%s", e.getMessage());
            return JsonUtils.toJson(new HipacTmcResponse(500, errMsg, "false"));
        }
        EshopRegisterNotify notify;
        try {
            notify = SupportUtil.buildNotify(hipacNewRefundResponse.getSupplier().getSupplierSenderID(), 59);
            saveTmcRefundOrderMsg(invokeMessage.getProfileId(), notify, tmMessage, hipacNewRefundResponse.getOrder());
        } catch (Exception ex) {
            LOGGER.error("错误", ex);
            LOGGER.error("海拍客保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", invokeMessage.getProfileId(), invokeMessage.getEshopId(), tmMessage, ex.getMessage());
            return JsonUtils.toJson(new HipacTmcResponse(500, "海拍客订单数据保存数据库出错!", "false"));
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setProfileId(notify.getProfileId());
        eshopNotifyChange.setEshopId(notify.getId());
        eshopNotifyChange.setTradeOrderId(hipacNewRefundResponse.getOrder().getOrderNum());
        eshopNotifyChange.setContent("");
        eshopNotifyChange.setId(UId.newId());
        eshopNotifyChange.setRefundOrderId(hipacNewRefundResponse.getOrder().getRefundNo());
        eshopNotifyChange.setType(TMCType.Order);
        SupportUtil.doOrderNotify(hipacNewRefundResponse.getSupplier().getSupplierSenderID(), eshopNotifyChange, invokeMessage.getShopType().getCode());
        return JsonUtils.toJson(new HipacTmcResponse(200, "成功!", "true"));
    }

    private void saveTmcRefundOrderMsg(BigInteger profileId, EshopRegisterNotify notify, String tmMessage, HipacNewRefundResponse.Order order) {
        //bizOrderCode 售后单号
        EshopTmcRefundMsgEntity refundMsgEntity = tmcRefundMsgMapper.queryTmcRefundMsgByRefundId(profileId, notify.getId(), order.getRefundNo());
        if (Objects.isNull(refundMsgEntity)) {
            refundMsgEntity = buildEshopTmcRefundMsgEntity(notify, tmMessage, order);
            tmcRefundMsgMapper.insertTmcRefundMsg(refundMsgEntity);
        } else {
            refundMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            refundMsgEntity.setUpdateTime(new Date());
            refundMsgEntity.setMessage(tmMessage);
            refundMsgEntity.setRefundStatus(HipacRefund.builRefundStatus(order.getRefundStatus()));
            tmcRefundMsgMapper.updateTmcRefundMsg(refundMsgEntity);
        }
    }

    private EshopTmcRefundMsgEntity buildEshopTmcRefundMsgEntity(EshopRegisterNotify notify, String tmMessage, HipacNewRefundResponse.Order order) {
        EshopTmcRefundMsgEntity tmcRefundMsgEntity = new EshopTmcRefundMsgEntity();
        tmcRefundMsgEntity.setId(UId.newId());
        tmcRefundMsgEntity.setProfileId(notify.getProfileId());
        tmcRefundMsgEntity.setEshopId(notify.getId());
        tmcRefundMsgEntity.setShopType(ShopType.valueOf(notify.getType()));
        tmcRefundMsgEntity.setTradeOrderId(order.getOrderNum());
        tmcRefundMsgEntity.setRefundOrderId(order.getRefundNo());
        tmcRefundMsgEntity.setMessage(tmMessage);
        tmcRefundMsgEntity.setRefundStatus(HipacRefund.builRefundStatus(order.getRefundStatus()));
        tmcRefundMsgEntity.setMsgStatus(0);
        tmcRefundMsgEntity.setMsgCreateTime(new Date());
        tmcRefundMsgEntity.setMsgUpdateTime(new Date());
        tmcRefundMsgEntity.setCreateTime(new Date());
        return tmcRefundMsgEntity;
    }

    @Override
    public String serviceName() {
        return "hipac.hsc.order.refund.push";
    }
}
