package com.wsgjp.ct.sale.tool.tmc.impl.taobao;

import com.qimencloud.api.sceneqimen.response.TaobaoQianniuCloudkefuAddressSelfModifyResponse;
import com.wsgjp.ct.common.enums.core.enums.tmc.TmcNotifyResponseEnum;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.tmc.AddressChangeMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.dto.TaoBaoModifyAddressDto;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import ngp.utils.JsonUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Component
public class ChangeAddressHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ChangeAddressHandler.class);

    private final TmcNotifyProxy notifyProxy;

    public ChangeAddressHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        TaobaoQianniuCloudkefuAddressSelfModifyResponse addressSelfModifyResponse = new TaobaoQianniuCloudkefuAddressSelfModifyResponse();
        TaobaoQianniuCloudkefuAddressSelfModifyResponse.ResultDO resultDO = new TaobaoQianniuCloudkefuAddressSelfModifyResponse.ResultDO();
        try {
            TaoBaoModifyAddressDto modifyAddress = JsonUtils.toObject(invokeMessage.getMessage(), TaoBaoModifyAddressDto.class);
            TmcInvokeRequest request = buildInvokeRequest(modifyAddress);
            request.setEshopId(invokeMessage.getEshopId());
            TmcInvokeResponse resp = notifyProxy.execute(request);
            if (TmcNotifyResponseEnum.SUCCESS.getCode().equals(resp.getCode())) {
                resultDO.setSuccess(true);
                LOGGER.debug("订单通过店小秘自动修改。返回改地址成功。");
            } else {
                resultDO.setSuccess(false);
                resultDO.setErrorCode("1008");
                resultDO.setErrorMsg("订单进入审单不支持改地址");
                LOGGER.error("订单通过店小秘自动修改。返回改地址失败：{}", resp.getMessage());
            }
            addressSelfModifyResponse.setResult(resultDO);
            return JsonUtils.toJson(addressSelfModifyResponse);
        } catch (Exception ex) {
            resultDO.setSuccess(false);
            resultDO.setErrorCode("3005");
            resultDO.setErrorMsg("系统异常");
            addressSelfModifyResponse.setResult(resultDO);
            LOGGER.error("订单通过店小秘自动修改。返回改地址失败{}", ex.getMessage(), ex);
            return JsonUtils.toJson(addressSelfModifyResponse);
        }
    }

    @NotNull
    private TmcInvokeRequest buildInvokeRequest(TaoBaoModifyAddressDto modifyAddress) {
        TmcInvokeRequest request = new TmcInvokeRequest();

        TaoBaoModifyAddressDto.ModifiedAddressDTO modifiedAddress = modifyAddress.getModifiedAddress();
        AddressChangeMessage changeMessage = new AddressChangeMessage();
        changeMessage.setPostReceiver(modifiedAddress.getName());
        changeMessage.setPostTel(modifiedAddress.getPhone());
        changeMessage.setProvince(modifiedAddress.getProvince());
        changeMessage.setCity(modifiedAddress.getCity());
        changeMessage.setDistrict(modifiedAddress.getArea());
        changeMessage.setStreet(modifiedAddress.getTown());
        changeMessage.setAddressDetail(modifiedAddress.getAddressDetail());

        request.setEshopId(BigInteger.valueOf(648827764375324662L));
        request.setTradeId(modifyAddress.getBizOrderId());
        request.setMessage(JsonUtils.toJson(changeMessage));
        request.setMethod(TmcNotifyMethodEnum.MODIFY_ADDRESS_NOTIFY);
        return request;
    }

    @Override
    public String serviceName() {
        return "invoketaobao.taobaocloudkefu";
    }
}
