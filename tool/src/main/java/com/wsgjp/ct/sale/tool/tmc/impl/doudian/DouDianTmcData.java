package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.fasterxml.jackson.annotation.JsonProperty;
import ngp.utils.StringUtils;
import utils.JsonUtils;

import java.util.Map;

/**
 * 抖店tmc消息
 */
public class DouDianTmcData {
    @JsonProperty("p_id")
    private String _pId;
    @JsonProperty("shop_id")
    private String shopId;
    @JsonProperty("shop_order_id")
    private String shopOrderId;

    @JsonProperty("tag_key")
    private String tagKey;

    @JsonProperty("tag_info")
    private String tagInfo;

    @JsonProperty("aftersale_id")
    private String afterSaleId;

    @JsonProperty("aftersale_status")
    private String aftersaleStatus;

    @JsonProperty("after_sale_status")
    private String afterSaleStatus;

    public String get_pId() {
        return _pId;
    }

    public void set_pId(String _pId) {
        this._pId = _pId;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getShopOrderId() {
        return shopOrderId;
    }

    public void setShopOrderId(String shopOrderId) {
        this.shopOrderId = shopOrderId;
    }

    public String getTradeId() {
        if (StringUtils.isEmpty(this._pId)) {
            return this.shopOrderId;
        }
        return this._pId;
    }

    public String getTagKey() {
        return tagKey;
    }

    public void setTagKey(String tagKey) {
        this.tagKey = tagKey;
    }

    public String getTagInfo() {
        return tagInfo;
    }

    public void setTagInfo(String tagInfo) {
        this.tagInfo = tagInfo;
    }

    public Map<String, Object> getTagMap() {
        if (StringUtils.isEmpty(tagInfo)) {
            return null;
        }
        return JsonUtils.toObject(tagInfo, Map.class);
    }

    public String getAfterSaleId() {
        return afterSaleId;
    }

    public void setAfterSaleId(String afterSaleId) {
        this.afterSaleId = afterSaleId;
    }

    public String getAftersaleStatus() {
        return aftersaleStatus;
    }

    public void setAftersaleStatus(String aftersaleStatus) {
        this.aftersaleStatus = aftersaleStatus;
    }

    public String getAfterSaleStatus() {
        return afterSaleStatus;
    }

    public void setAfterSaleStatus(String afterSaleStatus) {
        this.afterSaleStatus = afterSaleStatus;
    }

    public String getRefundStatus() {
        if (StringUtils.isNotBlank(aftersaleStatus)) {
            return aftersaleStatus;
        }
        return afterSaleStatus;
    }
}
