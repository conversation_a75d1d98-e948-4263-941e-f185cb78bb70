package com.wsgjp.ct.sale.tool.tmc.producer;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderRefundStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.member.utils.StringUtils;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.dto.refund.EshopTmcRefundMsgDto;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.taobao.entity.TaoBaoRefundMessage;
import com.wsgjp.ct.sale.tool.tmc.producer.entity.TaoBaoTopicEnum;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 */
public abstract class BaseMessageProducer implements MessageProducer {

    protected static String INVOICE_APPLY_TOPIC = "alibaba_invoice_Apply";
    private static final Logger logger = LoggerFactory.getLogger(BaseMessageProducer.class);

    protected static final List<String> REFUND_TOPICS = new ArrayList<>();

    protected static final Map<String, RefundStatus> LOCAL_REFUND_STATUS_MAP = new HashMap<>();

    static {
        REFUND_TOPICS.add(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SUCCESS.getTopic());
        REFUND_TOPICS.add(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_CLOSED.getTopic());
        REFUND_TOPICS.add(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SELLER_AGREE_AGREEMENT.getTopic());
        REFUND_TOPICS.add(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SELLER_REFUSE_AGREEMENT.getTopic());
        REFUND_TOPICS.add(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_CREATED.getTopic());

        LOCAL_REFUND_STATUS_MAP.put(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SUCCESS.getTopic(), RefundStatus.SUCCESS);
        LOCAL_REFUND_STATUS_MAP.put(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_CLOSED.getTopic(), RefundStatus.CANCEL);
        LOCAL_REFUND_STATUS_MAP.put(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SELLER_AGREE_AGREEMENT.getDesc(), RefundStatus.SELLER_AGREE);
        LOCAL_REFUND_STATUS_MAP.put(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SELLER_REFUSE_AGREEMENT.getTopic(), RefundStatus.SELLER_REFUSE);
        LOCAL_REFUND_STATUS_MAP.put(TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_CREATED.getTopic(), RefundStatus.WAIT_SELLER_AGREE);
    }

    protected BaseMessageProducer() {

    }

    @Override
    public void start() {
        doStart();
    }

    abstract void doStart();

    abstract String buildTradeId(String message);

    abstract String buildRefundId(String message);

    protected void saveTaoBaoRefundMsg(String message, String topic, ShopType shopType, EshopRegisterNotify notify) {
        if (!REFUND_TOPICS.contains(topic) || (!ShopType.TaoBao.equals(shopType) && !ShopType.Tmall.equals(shopType))) {
            return;
        }
        try {
            String refundId = buildRefundId(message);
            String tradeId = buildTradeId(message);
            if (StringUtils.isEmpty(refundId) || StringUtils.isEmpty(tradeId)) {
                return;
            }
            RefundStatus refundStatus = LOCAL_REFUND_STATUS_MAP.getOrDefault(topic, RefundStatus.NONE);
            EshopTmcRefundMsgService tmcRefundMsgService = GetBeanUtil.getBean(EshopTmcRefundMsgService.class);
            EshopTmcRefundMsgDto refundMsgDto = new EshopTmcRefundMsgDto(notify.getProfileId(), notify.getId(), ShopType.valueOf(notify.getType())
                    , tradeId, refundId, message, refundStatus);
            tmcRefundMsgService.saveOrUpdateTmcRefundMsg(refundMsgDto);
        } catch (Exception e) {
            logger.error("淘宝tmc,记录售后报文失败,{}", e.getMessage(), e);
        }
    }

    protected void saveTaoBaoRefundToPlEshopNotifyChange(String message, String topic, EshopRegisterNotify notify) {
        try {
            TMCType tmcType = null;
            //目前只保存着三种类型的售后单消息
            if (StringUtils.equals(topic, TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_CREATED.getTopic())
                    || StringUtils.equals(topic, TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SUCCESS.getTopic())) {
                tmcType = TMCType.REFUND_STOP;
            } else if (StringUtils.equals(topic, TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_CLOSED.getTopic())) {
                tmcType = TMCType.REFUND_STOP_CANCEL;
            }
            if (tmcType == null) {
                return;
            }
            TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
            String refundId = buildRefundId(message);
            String tradeId = buildTradeId(message);
            if (StringUtils.isEmpty(refundId) || StringUtils.isEmpty(tradeId)) {
                return;
            }
            TaoBaoRefundMessage curRefundMessage = JsonUtils.toObject(message, TaoBaoRefundMessage.class);
            if (curRefundMessage == null) {
                return;
            }
            EshopNotifyChange curNotifyChange = new EshopNotifyChange();
            curNotifyChange.setId(UId.newId());
            curNotifyChange.setProfileId(notify.getProfileId());
            curNotifyChange.setEshopId(notify.getId());
            curNotifyChange.setTradeOrderId(tradeId);
            curNotifyChange.setType(tmcType);
            curNotifyChange.setContent(message);
            curNotifyChange.setUpdateTime(DateUtils.getDate());
            EshopSaleOrderService eshopSaleOrderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
            UpdateOrderRefundStateRequest request = new UpdateOrderRefundStateRequest();
            request.setTradeOrderId(tradeId);
            request.setShopId(notify.getId());
            request.setUpdateOrderRefundType(tmcType == TMCType.REFUND_STOP ? UpdateOrderRefundType.REFUND_CREATE_BY_TMC : UpdateOrderRefundType.REFUND_CANCEL_BY_TMC);
            request.setRefundState(buildRefundState(topic));
            request.setOidList(new ArrayList<>(Collections.singletonList(curRefundMessage.getOid())));
            List<EshopNotifyChange> eshopNotifyChanges = tmcMapper.queryMessageChangeSorted(notify.getProfileId(), Collections.singletonList(tradeId), notify.getId(), TMCType.REFUND_STOP.getCode());
            boolean duplicateMsg = false;
            Date latestMsgTime = null;
            Date curMsgTime = curRefundMessage.getModified();
            EshopNotifyChange latestNotifyChange = null;
            if (CollectionUtils.isNotEmpty(eshopNotifyChanges) && curMsgTime != null) {
                for (EshopNotifyChange eshopNotifyChange : eshopNotifyChanges) {
                    String content = eshopNotifyChange.getContent();
                    if (StringUtils.isEmpty(content)) {
                        continue;
                    }
                    TaoBaoRefundMessage oldRefundMessage = JsonUtils.toObject(content, TaoBaoRefundMessage.class);
                    if (oldRefundMessage == null) {
                        continue;
                    }
                    if (!StringUtils.equals(oldRefundMessage.getRefund_id(), curRefundMessage.getRefund_id())
                            || !StringUtils.equals(oldRefundMessage.getOid(), curRefundMessage.getOid())) {
                        continue;
                    }
                    Date msgTime = oldRefundMessage.getModified();
                    if (msgTime == null) {
                        continue;
                    }
                    if (curMsgTime.getTime() <= msgTime.getTime()) {
                        //消息重复。当前消息已经处理过
                        duplicateMsg = true;
                        break;
                    }
                    if (latestNotifyChange == null || latestMsgTime.getTime() < msgTime.getTime()) {
                        latestNotifyChange = eshopNotifyChange;
                        latestMsgTime = msgTime;
                    }
                }
            }
            if (duplicateMsg) {
                return;
            }
            if (latestNotifyChange != null) {
                curNotifyChange.setId(latestNotifyChange.getId());
                tmcMapper.updateEshopNotifyChangeById(curNotifyChange);
            } else {
                tmcMapper.insertMessageChange(curNotifyChange);
            }
            eshopSaleOrderService.updateOrderRefundState(request);
        } catch (Exception ex) {
            if (ex.getMessage() != null && ex.getMessage().contains("订单尚未流入系统")) {
                return;
            }
            logger.error("保存淘宝售后tmc消息到pl_eshop_notify_change表失败,错误信息{}，message：{}", ex.getMessage(), message, ex);
        }
    }

    private ReturnState buildRefundState(String topic) {
        if (StringUtils.equals(topic, TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_CREATED.getTopic())
                || StringUtils.equals(topic, TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SELLER_AGREE_AGREEMENT.getTopic())
                || StringUtils.equals(topic, TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SELLER_REFUSE_AGREEMENT.getTopic())) {
            return ReturnState.REFUNDING;
        }
        if (StringUtils.equals(topic, TaoBaoTopicEnum.TAO_BAO_REFUND_REFUND_SUCCESS.getTopic())) {
            return ReturnState.SUCCESS;
        }
        return ReturnState.NONE;
    }


    TmcOrderMessage buildMessage(EshopRegisterNotify notify, String message) {
        TmcOrderMessage tmcMsg = new TmcOrderMessage();
        tmcMsg.setEshopId(notify.getId());
        tmcMsg.setProfileId(notify.getProfileId());
        tmcMsg.setTradeId(buildTradeId(message));
        tmcMsg.setCreateTime(new Date());
        return tmcMsg;
    }
}
