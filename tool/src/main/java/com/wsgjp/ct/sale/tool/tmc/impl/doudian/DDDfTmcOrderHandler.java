package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.controller.TmcConst;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class DDDfTmcOrderHandler extends DDNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DDDfTmcOrderHandler.class);
    private final EshopTmcUtils eshopTmcUtils;
    private final TmcNotifyProxy notifyProxy;

    private final EshopTmcRefundMsgService tmcRefundMsgService;
    public DDDfTmcOrderHandler(EshopTmcUtils eshopTmcUtils, TmcNotifyProxy notifyProxy, EshopTmcRefundMsgService tmcRefundMsgService) {
        this.eshopTmcUtils = eshopTmcUtils;
        this.notifyProxy = notifyProxy;
        this.tmcRefundMsgService = tmcRefundMsgService;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======抖店代发进入DDDfTmcOrderHandler.invoker方法======");
        if (StringUtils.isEmpty(invokeMessage.getMessage())) {
            LOGGER.error("抖店代发消息为空{}", JsonUtils.toJson(invokeMessage));
            return TmcConst.FAIL;
        }

        EshopNotifyChange change = buildEshopNotifyChange(invokeMessage);
        if (Objects.isNull(change)) {
            //当前未对接该类型的 tmc 消息
            LOGGER.info("账套ID{},转换成EshopNotifyChange为空", invokeMessage.getProfileId());
            return TmcConst.SUCCESS;
        }

        //这里发订单变更消息，系统会按单号下载
        SupportUtil.doOrderNotify(change.getOnlineShopId(), change, invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.DoudianDf.getCode());
        return TmcConst.SUCCESS;
    }

    /**
     * 获取EshopNotifyChange
     *
     * @return tmc 消息
     */
    public EshopNotifyChange buildEshopNotifyChange(InvokeMessageEntity invokeMessage) {
        DouDianTmcMessage tmcMessage = JsonUtils.toObject(invokeMessage.getMessage(), DouDianTmcMessage.class);
        DouDianDfTmcData data = JsonUtils.toObject(tmcMessage.getData(), DouDianDfTmcData.class);
        String tag = tmcMessage.getTag();
        DouDianTmcType ddTmcType = DouDianTmcType.getDouDianTmcTypeByTag(tag);
        if (null == ddTmcType) {
            return null;
        }
        EshopNotifyChange changeInfo = handlerMessageByType(data, ddTmcType.getTmcType());
        changeInfo.setContent(invokeMessage.getMessage());
        if (invokeMessage.getEshopId() != null) {
            changeInfo.setEshopId(invokeMessage.getEshopId());
        }
        return changeInfo;
    }

    /**
     * 根据类别创建EshopNotifyChange
     *
     * @param data    tmc消息data内容
     * @param tmcType tmc类型
     * @return EshopNotifyChange
     */
    public EshopNotifyChange handlerMessageByType(DouDianDfTmcData data, TMCType tmcType) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent("");
        change.setTradeOrderId(data.getDistrOrderId());
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(data.getShopId());
        return change;
    }



    @Override
    public String serviceName() {
        return "doudianDf.order";
    }

}
