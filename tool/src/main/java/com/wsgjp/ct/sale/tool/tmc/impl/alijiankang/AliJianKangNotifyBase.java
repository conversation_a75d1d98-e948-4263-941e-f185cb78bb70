package com.wsgjp.ct.sale.tool.tmc.impl.alijiankang;

import com.wsgjp.ct.sale.platform.utils.StringUtils;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public abstract class AliJianKangNotifyBase extends TaobaoNotifyBase implements MessageHandler {
    protected static final Logger LOGGER = LoggerFactory.getLogger(AliJianKangNotifyBase.class);


    public String getUnvalidSignResult(){
        return buildInvokeResult(false,"Illegal request","sign-check-failure");
    }
    protected String buildInvokeResult(Boolean success, String errMsg, String errCode) {
        success = null == success ? false : success;
        errCode = StringUtils.isEmpty(errCode) ? "" : errCode;
        errMsg = StringUtils.isEmpty(errMsg) ? "" : errMsg;
        String result = "{" +
                String.format("  \"success\":%s,\n", success.toString().toLowerCase()) +
                "   \"errorCode\":\"" + errCode + "\",\n" +
                "  \"errorMsg\":\"" + errMsg + "\"\n}";
        return result;
    }


    protected String buildModifyAddressInvokeResult(Boolean success, String errMsg, String errCode) {
        success = null == success ? false : success;
        errCode = StringUtils.isEmpty(errCode) ? "" : errCode;
        errMsg = StringUtils.isEmpty(errMsg) ? "" : errMsg;
        String result = "{" +
                String.format("  \"allow\":%s,\n", success.toString().toLowerCase()) +
                String.format("  \"success\":%s,\n", success.toString().toLowerCase()) +
                "   \"errorCode\":\"" + errCode + "\",\n" +
                "  \"errorMsg\":\"" + errMsg + "\"\n}";
        return result;
    }
}
