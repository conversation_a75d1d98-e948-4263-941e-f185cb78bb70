package com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac.newhipac;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HipacNewResponse {
    private HipacPush hipacPush;

    @JsonProperty("HipacPush")
    public HipacPush getHipacPush() { return hipacPush; }
    @JsonProperty("HipacPush")
    public void setHipacPush(HipacPush value) { this.hipacPush = value; }

    public static class HipacPush {
        private Supplier supplier;
        private Order order;
        private PayInfo payInfo;
        private Customer customer;
        private List<OrderItemList> orderItemList;

        @JsonProperty("Supplier")
        public Supplier getSupplier() { return supplier; }
        @JsonProperty("Supplier")
        public void setSupplier(Supplier value) { this.supplier = value; }

        @JsonProperty("Order")
        public Order getOrder() { return order; }
        @JsonProperty("Order")
        public void setOrder(Order value) { this.order = value; }

        @JsonProperty("PayInfo")
        public PayInfo getPayInfo() { return payInfo; }
        @JsonProperty("PayInfo")
        public void setPayInfo(PayInfo value) { this.payInfo = value; }

        @JsonProperty("Customer")
        public Customer getCustomer() { return customer; }
        @JsonProperty("Customer")
        public void setCustomer(Customer value) { this.customer = value; }

        @JsonProperty("OrderItemList")
        public List<OrderItemList> getOrderItemList() { return orderItemList; }
        @JsonProperty("OrderItemList")
        public void setOrderItemList(List<OrderItemList> value) { this.orderItemList = value; }
    }

    public static class Customer {
        private String custName;
        private String custIDNum;
        private String custPhone;
        private String custProvice;
        private String custCity;
        private String custArea;
        private String custAddress;
        private String customerName;
        private String customerPhone;
        private String customerIDNum;
        private String deliverName;
        private String deliverPhone;

        @JsonProperty("custName")
        public String getCustName() { return custName; }
        @JsonProperty("custName")
        public void setCustName(String value) { this.custName = value; }

        @JsonProperty("custIdNum")
        public String getCustIDNum() { return custIDNum; }
        @JsonProperty("custIdNum")
        public void setCustIDNum(String value) { this.custIDNum = value; }

        @JsonProperty("custPhone")
        public String getCustPhone() { return custPhone; }
        @JsonProperty("custPhone")
        public void setCustPhone(String value) { this.custPhone = value; }

        @JsonProperty("custProvice")
        public String getCustProvice() { return custProvice; }
        @JsonProperty("custProvice")
        public void setCustProvice(String value) { this.custProvice = value; }

        @JsonProperty("custCity")
        public String getCustCity() { return custCity; }
        @JsonProperty("custCity")
        public void setCustCity(String value) { this.custCity = value; }

        @JsonProperty("custArea")
        public String getCustArea() { return custArea; }
        @JsonProperty("custArea")
        public void setCustArea(String value) { this.custArea = value; }

        @JsonProperty("custAddress")
        public String getCustAddress() { return custAddress; }
        @JsonProperty("custAddress")
        public void setCustAddress(String value) { this.custAddress = value; }

        @JsonProperty("customerName")
        public String getCustomerName() { return customerName; }
        @JsonProperty("customerName")
        public void setCustomerName(String value) { this.customerName = value; }

        @JsonProperty("customerPhone")
        public String getCustomerPhone() { return customerPhone; }
        @JsonProperty("customerPhone")
        public void setCustomerPhone(String value) { this.customerPhone = value; }

        @JsonProperty("customerIdNum")
        public String getCustomerIDNum() { return customerIDNum; }
        @JsonProperty("customerIdNum")
        public void setCustomerIDNum(String value) { this.customerIDNum = value; }

        @JsonProperty("deliverName")
        public String getDeliverName() { return deliverName; }
        @JsonProperty("deliverName")
        public void setDeliverName(String value) { this.deliverName = value; }

        @JsonProperty("deliverPhone")
        public String getDeliverPhone() { return deliverPhone; }
        @JsonProperty("deliverPhone")
        public void setDeliverPhone(String value) { this.deliverPhone = value; }
    }

    public static class Order {
        private String orderNum;
        private String orderDate;
        private String totalOrderAmount;
        private String totalTaxAmount;
        private String totalPayAmount;
        private String logisticsAmount;

        @JsonProperty("orderNum")
        public String getOrderNum() { return orderNum; }
        @JsonProperty("orderNum")
        public void setOrderNum(String value) { this.orderNum = value; }

        @JsonProperty("orderDate")
        public String getOrderDate() { return orderDate; }
        @JsonProperty("orderDate")
        public void setOrderDate(String value) { this.orderDate = value; }

        @JsonProperty("totalOrderAmount")
        public String getTotalOrderAmount() { return totalOrderAmount; }
        @JsonProperty("totalOrderAmount")
        public void setTotalOrderAmount(String value) { this.totalOrderAmount = value; }

        @JsonProperty("totalTaxAmount")
        public String getTotalTaxAmount() { return totalTaxAmount; }
        @JsonProperty("totalTaxAmount")
        public void setTotalTaxAmount(String value) { this.totalTaxAmount = value; }

        @JsonProperty("totalPayAmount")
        public String getTotalPayAmount() { return totalPayAmount; }
        @JsonProperty("totalPayAmount")
        public void setTotalPayAmount(String value) { this.totalPayAmount = value; }

        @JsonProperty("logisticsAmount")
        public String getLogisticsAmount() { return logisticsAmount; }
        @JsonProperty("logisticsAmount")
        public void setLogisticsAmount(String value) { this.logisticsAmount = value; }
    }

    public static class OrderItemList {
        private String itemName;
        private String itemSupplyNo;
        private String itemPrice;
        private String itemQuantity;
        private String itemTotal;
        private String itemTotalTax;
        private String addTaxRate;
        private String exciseRate;
        private String tariffRate;
        private String specNme;
        private String specNum;
        private String itemSpecAmount;
        private String itemSpecTotalAmount;
        private String productDate;
        private String validStartDate;
        private String validEndDate;

        @JsonProperty("itemName")
        public String getItemName() { return itemName; }
        @JsonProperty("itemName")
        public void setItemName(String value) { this.itemName = value; }

        @JsonProperty("itemSupplyNo")
        public String getItemSupplyNo() { return itemSupplyNo; }
        @JsonProperty("itemSupplyNo")
        public void setItemSupplyNo(String value) { this.itemSupplyNo = value; }

        @JsonProperty("itemPrice")
        public String getItemPrice() { return itemPrice; }
        @JsonProperty("itemPrice")
        public void setItemPrice(String value) { this.itemPrice = value; }

        @JsonProperty("itemQuantity")
        public String getItemQuantity() { return itemQuantity; }
        @JsonProperty("itemQuantity")
        public void setItemQuantity(String value) { this.itemQuantity = value; }

        @JsonProperty("itemTotal")
        public String getItemTotal() { return itemTotal; }
        @JsonProperty("itemTotal")
        public void setItemTotal(String value) { this.itemTotal = value; }

        @JsonProperty("itemTotalTax")
        public String getItemTotalTax() { return itemTotalTax; }
        @JsonProperty("itemTotalTax")
        public void setItemTotalTax(String value) { this.itemTotalTax = value; }

        @JsonProperty("addTaxRate")
        public String getAddTaxRate() { return addTaxRate; }
        @JsonProperty("addTaxRate")
        public void setAddTaxRate(String value) { this.addTaxRate = value; }

        @JsonProperty("exciseRate")
        public String getExciseRate() { return exciseRate; }
        @JsonProperty("exciseRate")
        public void setExciseRate(String value) { this.exciseRate = value; }

        @JsonProperty("tariffRate")
        public String getTariffRate() { return tariffRate; }
        @JsonProperty("tariffRate")
        public void setTariffRate(String value) { this.tariffRate = value; }

        @JsonProperty("specNme")
        public String getSpecNme() { return specNme; }
        @JsonProperty("specNme")
        public void setSpecNme(String value) { this.specNme = value; }

        @JsonProperty("specNum")
        public String getSpecNum() { return specNum; }
        @JsonProperty("specNum")
        public void setSpecNum(String value) { this.specNum = value; }

        @JsonProperty("itemSpecAmount")
        public String getItemSpecAmount() { return itemSpecAmount; }
        @JsonProperty("itemSpecAmount")
        public void setItemSpecAmount(String value) { this.itemSpecAmount = value; }

        @JsonProperty("itemSpecTotalAmount")
        public String getItemSpecTotalAmount() { return itemSpecTotalAmount; }
        @JsonProperty("itemSpecTotalAmount")
        public void setItemSpecTotalAmount(String value) { this.itemSpecTotalAmount = value; }

        @JsonProperty("productDate")
        public String getProductDate() { return productDate; }
        @JsonProperty("productDate")
        public void setProductDate(String value) { this.productDate = value; }

        @JsonProperty("validStartDate")
        public String getValidStartDate() { return validStartDate; }
        @JsonProperty("validStartDate")
        public void setValidStartDate(String value) { this.validStartDate = value; }

        @JsonProperty("validEndDate")
        public String getValidEndDate() { return validEndDate; }
        @JsonProperty("validEndDate")
        public void setValidEndDate(String value) { this.validEndDate = value; }
    }

    public static class PayInfo {
        private String payNo;
        private String payType;
        private String payTime;
        private String payCompanyName;

        @JsonProperty("payNo")
        public String getPayNo() { return payNo; }
        @JsonProperty("payNo")
        public void setPayNo(String value) { this.payNo = value; }

        @JsonProperty("payType")
        public String getPayType() { return payType; }
        @JsonProperty("payType")
        public void setPayType(String value) { this.payType = value; }

        @JsonProperty("payTime")
        public String getPayTime() { return payTime; }
        @JsonProperty("payTime")
        public void setPayTime(String value) { this.payTime = value; }

        @JsonProperty("payCompanyName")
        public String getPayCompanyName() { return payCompanyName; }
        @JsonProperty("payCompanyName")
        public void setPayCompanyName(String value) { this.payCompanyName = value; }
    }



    public static class Supplier {
        private String supplierSenderID;

        @JsonProperty("supplierSenderID")
        public String getSupplierSenderID() { return supplierSenderID; }
        @JsonProperty("supplierSenderID")
        public void setSupplierSenderID(String value) { this.supplierSenderID = value; }
    }
}
