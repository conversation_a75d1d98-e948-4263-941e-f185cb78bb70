package com.wsgjp.ct.sale.tool.tmc.impl.pdd;

import cn.hutool.core.bean.BeanUtil;
import com.pdd.pop.sdk.message.model.Message;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderRefundStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.product.TmcProductMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.dto.refund.EshopTmcRefundMsgDto;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.pinduoduo.PinDuoDuoConfig;
import com.wsgjp.ct.sale.tool.common.log.EshopTmcMessageLog;
import com.wsgjp.ct.sale.tool.common.log.MqConsumeLog;
import com.wsgjp.ct.sale.tool.product.entity.ProductConst;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.PddGoodsMessage;
import com.wsgjp.ct.sale.tool.tmc.entity.PddOrderMessage;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.sale.tool.tmc.utils.TmcLogUtil;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.mq.SysMqSend;
import ngp.idgenerator.UId;
import ngp.mq.MqSendResult;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR> 拼多多承诺信息
 */
@Component
public class OrderPromiseHandler extends PddNotifyBase implements MessageHandler {
    private static final Logger sysLogger = LoggerFactory.getLogger(OrderPromiseHandler.class);
    protected static final List<String> REFUND_TOPICS = new ArrayList<>();

    static {
        REFUND_TOPICS.add(PddTmcMsgType.pdd_refund_RefundCreated.getMsgType());
        REFUND_TOPICS.add(PddTmcMsgType.pdd_refund_RefundAgreeAgreement.getMsgType());
        REFUND_TOPICS.add(PddTmcMsgType.pdd_refund_RefundClosed.getMsgType());
    }

    private final TmcNotifyProxy notifyProxy;

    private final PinDuoDuoConfig pddConfig;

    public OrderPromiseHandler(TmcNotifyProxy notifyProxy, PinDuoDuoConfig pddConfig) {
        this.notifyProxy = notifyProxy;
        this.pddConfig = pddConfig;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String message = invokeMessage.getMessage();
        if (StringUtils.isEmpty(message)) {
            sysLogger.info("拼多多消息为空");
            return "";
        }
        Message pddMsEntity = JsonUtils.toObject(message, Message.class);
        if (pddMsEntity == null) {
            sysLogger.info("拼多多消息为空");
            return "";
        }
        if (Objects.isNull(pddMsEntity.getType()) || StringUtils.isEmpty(pddMsEntity.getContent())) {
            return "";
        }
        PddTmcMsgType msgType = PddTmcMsgType.getPddTmcMsgTypeByMsgTypeName(pddMsEntity.getType());
        if (Objects.isNull(msgType)) {
            return "";
        }
        if (msgType.getTmcType().equals(TMCType.Ptype)) {
            doHandleGoodsMessage(invokeMessage, pddMsEntity);
            return ProductConst.SUCCESS;
        }

        EshopNotifyChange change = handleMessage(pddMsEntity, msgType, invokeMessage);
        //todo 写日志
        if (change.getType().equals(TMCType.CHANGE_ADDRESS)) {
            return doNotifyAddressChange(change);
        }
        if (change.getType().equals(TMCType.RefundOrder) && StringUtils.isNotEmpty(change.getTradeOrderId())) {
            // 因为订单下载完成之后会自动的去下载一次售后单，所以售后TMC直接走订单流程
            change.setType(TMCType.Order);
        }
        //todo 修改类的消息直接处理
        SupportUtil.doOrderNotifyByDelayWay(change.getOnlineShopId(), change, ShopType.PinDuoDuo.getCode(), pddConfig.getTmcDelayTime());
        return ProductConst.SUCCESS;
    }

    /**
     * 修改地址发送给消费者
     *
     * @param change 标准实体
     * @return 发送结果
     */
    private String doNotifyAddressChange(EshopNotifyChange change) {
        //修改地址request
        TmcInvokeRequest invokeRequest = buildTmcInvokeRequest(change);
        //订单地址变动：通知业务段
        TmcInvokeResponse invokeResponse = notifyProxy.execute(invokeRequest);
        String msg = "success";
        if (invokeResponse == null) {
            sysLogger.info("拼多多订单地址变动消息，通知业务端处理出错，response为null");
            msg = "error:消费者未成功处理返回null";
        } else if (invokeResponse.isError()) {
            sysLogger.info(String.format("拼多多订单地址变动消息，通知业务端处理。是否错误：%s, code: %s, msg: %s", invokeResponse.isError(), invokeResponse.getCode(), invokeResponse.getMessage()));
            msg = String.format(" code: %s, msg: %s", invokeResponse.getCode(), invokeResponse.getMessage());
        }
        return msg;
    }

    private void saveRefundToPlEshopNotifyChange(EshopNotifyChange change, Message msg, PddOrderMessage curRefundMessage) {
        try {
            EshopNotifyChange curNotifyChange = new EshopNotifyChange();
            BeanUtil.copyProperties(change, curNotifyChange);
            if (!StringUtils.equals(PddTmcMsgType.pdd_refund_RefundCreated.getMsgType(), msg.getType())
                    && !StringUtils.equals(PddTmcMsgType.pdd_refund_RefundClosed.getMsgType(), msg.getType())) {
                return;
            }
            //拼多多只处理退款创建、退款关闭消息
            TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
            curNotifyChange.setId(UId.newId());
            curNotifyChange.setContent(msg.getContent());
            curNotifyChange.setUpdateTime(DateUtils.getDate());
            TMCType tmcType;
            UpdateOrderRefundStateRequest request = new UpdateOrderRefundStateRequest();
            request.setTradeOrderId(curNotifyChange.getTradeOrderId());
            request.setShopId(curNotifyChange.getEshopId());
            RefundStatus refundStatus = buildRefundStatus(msg, curRefundMessage.getOperation());
            if (refundStatus == RefundStatus.CANCEL || refundStatus == RefundStatus.NONE) {
                request.setRefundState(ReturnState.NONE);
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CANCEL_BY_TMC);
                tmcType = TMCType.REFUND_STOP_CANCEL;
            } else if (refundStatus == RefundStatus.SUCCESS) {
                request.setRefundState(ReturnState.SUCCESS);
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CREATE_BY_TMC);
                tmcType = TMCType.REFUND_STOP;
            } else {
                request.setRefundState(ReturnState.REFUNDING);
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CREATE_BY_TMC);
                tmcType = TMCType.REFUND_STOP;
            }
            curNotifyChange.setType(tmcType);
            EshopSaleOrderService eshopSaleOrderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
            List<EshopNotifyChange> eshopNotifyChanges = tmcMapper.queryMessageChangeSorted(curNotifyChange.getProfileId(),
                    Collections.singletonList(curNotifyChange.getTradeOrderId()), curNotifyChange.getEshopId(), TMCType.REFUND_STOP.getCode());
            EshopNotifyChange latestNotifyChange = null;
            boolean duplicateMsg = false;
            long latestMsgTime = 0;
            long curMsgTime = curRefundMessage.getModified() != null ? curRefundMessage.getModified() : 0;
            if (CollectionUtils.isNotEmpty(eshopNotifyChanges)) {
                for (EshopNotifyChange eshopNotifyChange : eshopNotifyChanges) {
                    String content = eshopNotifyChange.getContent();
                    if (StringUtils.isEmpty(content)) {
                        continue;
                    }
                    PddOrderMessage oldRefundMessage = JsonUtils.toObject(content, PddOrderMessage.class);
                    if (oldRefundMessage == null || !StringUtils.equals(oldRefundMessage.getRefund_id(), curRefundMessage.getRefund_id())) {
                        continue;
                    }
                    //正常情况下一个售后单只会有一条售后类型消息
                    long msgTime = oldRefundMessage.getModified() != null ? oldRefundMessage.getModified() : 0;
                    if (msgTime >= curMsgTime) {
                        //消息重复。当前消息已经处理过
                        duplicateMsg = true;
                        break;
                    }
                    if (latestNotifyChange == null) {
                        latestNotifyChange = eshopNotifyChange;
                        latestMsgTime = msgTime;
                        continue;
                    }
                    if (latestMsgTime != 0 && latestMsgTime < msgTime) {
                        latestNotifyChange = eshopNotifyChange;
                        latestMsgTime = msgTime;
                    }
                }
            }
            if (duplicateMsg) {
                //消息重复。当前消息已经处理过
                return;
            }
            if (latestNotifyChange != null) {
                curNotifyChange.setId(latestNotifyChange.getId());
                tmcMapper.updateEshopNotifyChangeById(curNotifyChange);
            } else {
                tmcMapper.insertMessageChange(curNotifyChange);
            }
            eshopSaleOrderService.updateOrderRefundState(request);
        } catch (Exception ex) {
            if (ex.getMessage() != null && ex.getMessage().contains("订单尚未流入系统")) {
                return;
            }
            sysLogger.error("账套ID{},店铺ID{},保持售后消息到eshop_order_notify表失败，失败原因：{}.message:{}"
                    , change.getProfileId(), change.getEshopId(), ex.getMessage(), msg.getContent(), ex);
        }
    }

    protected void saveTmcRefundMsg(EshopNotifyChange change, Message msg, PddOrderMessage pddMsg) {
        if (!REFUND_TOPICS.contains(msg.getType())) {
            return;
        }
        String message = JsonUtils.toJson(msg);
        try {
            if (StringUtils.isEmpty(change.getTradeOrderId()) || StringUtils.isEmpty(change.getRefundOrderId())) {
                return;
            }
            RefundStatus refundStatus = buildRefundStatus(msg, pddMsg.getOperation());
            EshopTmcRefundMsgService tmcRefundMsgService = GetBeanUtil.getBean(EshopTmcRefundMsgService.class);
            EshopTmcRefundMsgDto refundMsgDto = new EshopTmcRefundMsgDto(change.getProfileId(), change.getEshopId(), ShopType.PinDuoDuo
                    , change.getTradeOrderId(), change.getRefundOrderId(), message, refundStatus);
            tmcRefundMsgService.saveOrUpdateTmcRefundMsg(refundMsgDto);
        } catch (Exception ex) {
            sysLogger.error("账套ID{},店铺ID{},保持售后消息失败，失败原因：{}.message:{}", change.getProfileId(), change.getEshopId(), ex.getMessage(), message, ex);
        }
    }

    private RefundStatus buildRefundStatus(Message msg, String operation) {
        RefundStatus refundStatus = RefundStatus.NONE;
        //退款成功1304； 维修完成1305；逾期未确认，维修成功1310；逾期未确认，维修成功1310；用户确认换货补寄完成1311；；用户逾期系统确认完成1313；
        List<String> refundSuccess = new ArrayList<>(Arrays.asList("1304", "1305", "1310", "1311", "1313"));
        if (msg.getType().equals(PddTmcMsgType.pdd_refund_RefundCreated.getMsgType())) {
            return RefundStatus.WAIT_SELLER_AGREE;
        }
        if (msg.getType().equals(PddTmcMsgType.pdd_refund_RefundClosed.getMsgType())) {
            if (refundSuccess.contains(operation)) {
                return RefundStatus.SUCCESS;
            } else {
                return RefundStatus.CANCEL;
            }
        }
        if (msg.getType().equals(PddTmcMsgType.pdd_refund_RefundAgreeAgreement.getMsgType())) {
            return RefundStatus.SELLER_AGREE;
        }
        return refundStatus;
    }

    /**
     * 构建发送给tmc消费者的消息
     *
     * @param msg 平台消息
     * @return 标准消费者的消息
     */
    private EshopNotifyChange handleMessage(Message msg, PddTmcMsgType msgType, InvokeMessageEntity invokeMessage) {
        PddOrderMessage pddMsg = JsonUtils.toObject(msg.getContent(), PddOrderMessage.class);
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(msg.getContent());
        change.setTradeOrderId(pddMsg.getTid());
        change.setId(UId.newId());
        change.setType(msgType.getTmcType());
        change.setOnlineShopId(pddMsg.getMall_id());
        change.setCreateTime(new Date());
        change.setEshopId(invokeMessage.getEshopId());
        change.setProfileId(invokeMessage.getProfileId());
        change.setRefundOrderId(pddMsg.getRefund_id());
        if (pddMsg.getModified() != null && pddMsg.getModified() > 0) {
            change.setDesiredTradeModifiedTime(new Date(pddMsg.getModified()));
        } else if (pddMsg.getOperation_time() != null && pddMsg.getOperation_time() > 0) {
            change.setDesiredTradeModifiedTime(new Date(pddMsg.getOperation_time()));
        }

        if (msgType.getTmcType().equals(TMCType.DIRECT_UPDATE_TRADE_STATUS)) {
            //pdd_trade_TradeSellerShip
            TradeStatus tradeStatus = TradeStatus.TRADE_FINISHED;
            if (PddTmcMsgType.pdd_trade_TradeSuccess.equals(msgType)) {
                tradeStatus = TradeStatus.WAIT_BUYER_CONFIRM_GOODS;
            }
            change.setTradeStatus(tradeStatus.toString());
        }
        saveTmcRefundMsg(change, msg, pddMsg);
        saveRefundToPlEshopNotifyChange(change, msg, pddMsg);
        return change;
    }

    private void doHandleGoodsMessage(InvokeMessageEntity invokeMessage, Message pddMsEntity) {
        PddGoodsMessage goodsMessage = JsonUtils.toObject(pddMsEntity.getContent(), PddGoodsMessage.class);
        TmcProductMessage tmcProductMessage = new TmcProductMessage();
        tmcProductMessage.setNumId(goodsMessage.getGoodsId());
        tmcProductMessage.setEshopId(invokeMessage.getEshopId());
        tmcProductMessage.setProfileId(invokeMessage.getProfileId());
        tmcProductMessage.setCreateTime(DateUtils.getDate());
        List<MqSendResult<TmcProductMessage>> sendResults = SysMqSend.send(Collections.singletonList(tmcProductMessage), ProductConst.TmcProductTopIcName);
        if (CollectionUtils.isEmpty(sendResults)) {
            return;
        }
        for (MqSendResult<TmcProductMessage> sendResult : sendResults) {
            String messageId = sendResult.getMessageId();
            EshopTmcMessageLog tmcMessageLog = TmcLogUtil.buildTmcLog(invokeMessage, pddMsEntity.getType(), messageId);
            tmcMessageLog.setTmcId(goodsMessage.getGoodsId());
            MqConsumeLog mqConsumeLog = TmcLogUtil.buildMqLog(tmcProductMessage, messageId, TMCType.Ptype);
            mqConsumeLog.setTmcId(goodsMessage.getGoodsId());
            LogService.add(tmcMessageLog);
            LogService.add(mqConsumeLog);
        }
    }


    /**
     * 修改地址的tmc消费者request
     *
     * @param notify 消费者需要的实体
     */
    private TmcInvokeRequest buildTmcInvokeRequest(EshopNotifyChange notify) {
        TmcInvokeRequest invokeRequest = new TmcInvokeRequest();
        invokeRequest.setEshopId(notify.getEshopId());
        invokeRequest.setProfileId(notify.getProfileId());
        invokeRequest.setMethod(TmcNotifyMethodEnum.MODIFY_ADDRESS_UPDATE);
        invokeRequest.setTradeId(notify.getTradeOrderId());
        invokeRequest.setMessage(notify.getContent());
        return invokeRequest;
    }

    @Override
    public String serviceName() {
        return "pddInvoker";
    }
}
