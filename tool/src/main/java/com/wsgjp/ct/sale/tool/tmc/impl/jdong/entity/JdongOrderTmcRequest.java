package com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity;

import com.wsgjp.ct.sale.platform.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class JdongOrderTmcRequest extends JdongTmcRequest {
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 揽件时间:pop_order_promise(pop订单时效)
     */
    private String pickDate;

    /**
     * 预计妥投时间:pop_order_promise(pop订单时效)
     */
    private String deliveredTime;

    /**
     * 京东全渠道的订单号
     */
    private String saleOrderId;
    /**
     * 售后服务单号
     */
    private String afsServiceId;

    //售后申请单号
    private String afsApplyId;


    public String getOrderId() {
        return orderId;
    }

    public String getTradeOrderId(){
        if (StringUtils.isNotBlank(orderId)){
            return orderId;
        }
        return saleOrderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPickDate() {
        return pickDate;
    }

    public void setPickDate(String pickDate) {
        this.pickDate = pickDate;
    }

    public String getDeliveredTime() {
        return deliveredTime;
    }

    public void setDeliveredTime(String deliveredTime) {
        this.deliveredTime = deliveredTime;
    }

    public String getSaleOrderId() {
        return saleOrderId;
    }

    public void setSaleOrderId(String saleOrderId) {
        this.saleOrderId = saleOrderId;
    }

    public String getAfsServiceId() {
        return afsServiceId;
    }

    public void setAfsServiceId(String afsServiceId) {
        this.afsServiceId = afsServiceId;
    }

    public String getAfsApplyId() {
        return afsApplyId;
    }

    public void setAfsApplyId(String afsApplyId) {
        this.afsApplyId = afsApplyId;
    }

    public String getRefundId() {
        if (StringUtils.isNotBlank(afsServiceId)){
            return afsServiceId;
        }
        return afsApplyId;
    }
}
