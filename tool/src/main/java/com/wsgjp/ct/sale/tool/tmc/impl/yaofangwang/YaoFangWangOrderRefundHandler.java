package com.wsgjp.ct.sale.tool.tmc.impl.yaofangwang;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.xiaohonghsu.entity.XiaoHongShuRes;
import com.wsgjp.ct.sale.tool.tmc.impl.yaofangwang.entity.OrderMsg;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;

@Component
public class YaoFangWangOrderRefundHandler extends YaoFangWangNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(YaoFangWangOrderRefundHandler.class);
    private final EshopTmcConfig config;
    public YaoFangWangOrderRefundHandler(EshopTmcConfig config) {
        this.config = config;
    }


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        XiaoHongShuRes res = new XiaoHongShuRes();
        OrderMsg orderMsg;
        try {
            orderMsg = JsonUtils.toObject(tmMessage, OrderMsg.class);
            if (orderMsg == null){
                throw new RuntimeException("json转实体失败");
            }
            OrderMsg.Data data = orderMsg.getData();
            if (data == null){
                throw new RuntimeException("获取订单或者售后单相关信息失败");
            }
            BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
            if (BigInteger.ZERO.equals(invokeMessage.getEshopId())){
                // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                EshopRegisterNotify notify = SupportUtil.buildNotify(data.getShopID(), ShopType.YaoFangWang.getCode());
                if (notify == null || notify.getId()==null|| notify.getId().equals(BigInteger.ZERO)){
                    LOGGER.error("药房网消息处理失败：eshopId拿取失败");
                    res.setSuccess(false);
                    res.setError_code(201);
                    res.setError_msg("店铺id寻找失败");
                    return JsonUtils.toJson(res);
                }
                eshopId=notify.getId();
            }else {
                eshopId = invokeMessage.getEshopId();
            }
            LOGGER.info("profileId:{},eshopId:{},店铺类型：{}",invokeMessage.getProfileId(),eshopId,shopTypeName);
            EshopNotifyChange change = handleMessageByType(data,tmMessage);
            change.setType(TMCType.Order);
            change.setId(UId.newId());
            SupportUtil.doOrderNotifyByDelayWay(data.getShopID(),change,invokeMessage.getShopType().getCode(),config.getTmcXHSSendDelayTime());
            res.setSuccess(true);
            res.setError_code(200);
            return JsonUtils.toJson(res);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成OrderRequest实体出错，错误信息：{}",shopTypeName,e.getMessage());
            res.setSuccess(false);
            res.setError_code(205);
            res.setError_msg("未知异常:" +  e.getMessage());
            return JsonUtils.toJson(res);
        }
    }

    private EshopNotifyChange handleMessageByType(OrderMsg.Data data,String tmMessage) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(tmMessage);
        // 目前只支持订单号下载
        change.setTradeOrderId(data.getOrderID());
        // 支持售后单号下载后使用
        change.setRefundOrderId(data.getAfterSaleId());
        change.setId(UId.newId());
        change.setType(TMCType.Order);
        change.setOnlineShopId(data.getShopID());
        change.setCreateTime(new Date());
        if (data.getOperatorTime() != null){
            change.setUpdateTime( new Date(data.getOperatorTime()));
        }
        return change;
    }

    @Override
    public String serviceName() {
        return "yaofangwangOrderAndRefund";
    }

}
