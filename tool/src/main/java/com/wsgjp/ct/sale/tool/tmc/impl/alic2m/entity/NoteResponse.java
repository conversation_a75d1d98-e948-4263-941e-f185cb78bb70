package com.wsgjp.ct.sale.tool.tmc.impl.alic2m.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class NoteResponse {
    private String memoType;
    private String supplierID;
    private String sourceTradeID;
    private String content;
    private String role;
    private String modifiedTime;

    @JsonProperty("memoType")
    public String getMemoType() { return memoType; }
    @JsonProperty("memoType")
    public void setMemoType(String value) { this.memoType = value; }

    @JsonProperty("supplierId")
    public String getSupplierID() { return supplierID; }
    @JsonProperty("supplierId")
    public void setSupplierID(String value) { this.supplierID = value; }

    @JsonProperty("sourceTradeId")
    public String getSourceTradeID() { return sourceTradeID; }
    @JsonProperty("sourceTradeId")
    public void setSourceTradeID(String value) { this.sourceTradeID = value; }

    @JsonProperty("content")
    public String getContent() { return content; }
    @JsonProperty("content")
    public void setContent(String value) { this.content = value; }

    @JsonProperty("role")
    public String getRole() { return role; }
    @JsonProperty("role")
    public void setRole(String value) { this.role = value; }

    @JsonProperty("modifiedTime")
    public String getModifiedTime() { return modifiedTime; }
    @JsonProperty("modifiedTime")
    public void setModifiedTime(String value) { this.modifiedTime = value; }
}