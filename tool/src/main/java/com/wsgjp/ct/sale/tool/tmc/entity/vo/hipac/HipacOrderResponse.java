package com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "HipacPush")
public class HipacOrderResponse {

    @XmlElement(name = "Head")
    public HipacHead head;
    @XmlElement(name = "Body")
    public HipacOrderBody orderResponse;

    public HipacHead getHead() {
        return head;
    }

    public void setHead(HipacHead head) {
        this.head = head;
    }

    public HipacOrderBody getOrderResponse() {
        return orderResponse;
    }

    public void setOrderResponse(HipacOrderBody orderResponse) {
        this.orderResponse = orderResponse;
    }
}
