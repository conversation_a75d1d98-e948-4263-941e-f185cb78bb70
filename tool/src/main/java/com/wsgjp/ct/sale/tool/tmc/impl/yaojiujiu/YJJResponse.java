package com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu;

import com.fasterxml.jackson.annotation.JsonProperty;

public class YJJResponse {
    private Boolean success;
    private Integer code;
    private String msg;
    private Object data;

    public YJJResponse() {
    }

    public YJJResponse(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        this.success = false;
    }

    @JsonProperty("success")
    public Boolean getSuccess() { return success; }
    @JsonProperty("success")
    public void setSuccess(Boolean value) { this.success = value; }

    @JsonProperty("code")
    public Integer getCode() { return code; }
    @JsonProperty("code")
    public void setCode(Integer value) { this.code = value; }

    @JsonProperty("msg")
    public String getMsg() { return msg; }
    @JsonProperty("msg")
    public void setMsg(String value) { this.msg = value; }

    @JsonProperty("data")
    public Object getData() { return data; }
    @JsonProperty("data")
    public void setData(Object value) { this.data = value; }
}