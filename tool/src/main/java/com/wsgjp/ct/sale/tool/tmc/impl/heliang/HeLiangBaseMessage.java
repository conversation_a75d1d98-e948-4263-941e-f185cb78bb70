package com.wsgjp.ct.sale.tool.tmc.impl.heliang;

import ngp.utils.HttpUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class HeLiangBaseMessage {
    private String sign;
    private HeLiangParam paramObj;
    private String param;

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public HeLiangParam getParamObj() {
        if (StringUtils.isNotBlank(param)) {
            paramObj = JsonUtils.toObject(HttpUtils.urlDecode(param), HeLiangParam.class);
        }
        return paramObj;
    }

    public void setParamObj(HeLiangParam paramObj) {
        this.paramObj = paramObj;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }
}
