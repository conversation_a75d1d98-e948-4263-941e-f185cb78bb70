package com.wsgjp.ct.sale.tool.bus;

import com.wsgjp.ct.sale.biz.api.profile.ProfileResponse;
import com.wsgjp.ct.sale.bus.center.BusDataCenter;
import com.wsgjp.ct.sale.bus.center.BusStarter;
import com.wsgjp.ct.sale.bus.entity.BusTaskInfo;
import com.wsgjp.ct.sale.bus.entity.LockParams;
import com.wsgjp.ct.sale.bus.entity.Task;
import com.wsgjp.ct.sale.bus.entity.TaskType;
import com.wsgjp.ct.sale.bus.utils.BusDataLockerImpl;
import com.wsgjp.ct.sale.tool.common.DatabaseFilterConfig;
import com.wsgjp.ct.sale.tool.logo.config.ToolCalculateConfig;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.mq.SysMqSend;
import ngp.mq.MqSendResult;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 计算型的轮训任务
 */
@Service
@ConditionalOnProperty(value = "sale-bus.enable", havingValue = "true")
public class BusConsumerInvoker extends AbstractBusConsumerInvoker<BusTaskInfo> {
    Logger logger = LoggerFactory.getLogger(BusConsumerInvoker.class);

    @Autowired
    private DatabaseFilterConfig databaseFilterConfig;
    public BusConsumerInvoker(ToolCalculateConfig toolConfig, BusStarter busStarter, BusDataCenter busDataCenter,BusDataLockerImpl busDataLocker) {
        super(toolConfig, busStarter, busDataCenter,busDataLocker);
    }

    @Override
    protected void invokeTraining(BusTaskInfo taskInfo) {
        logger.info("[{}]开始执行轮训消费计划,{}", name(),CurrentUser.getProfileId());
        BusStarter busStarter = getBusStarter();
        LockParams busLocker = busStarter.getBusTrainingLocker(CurrentUser.getProfileId());
        boolean lock = busDataLocker.lock(busLocker);
        if (!lock) {
            logger.info("[{}]锁定失败，结束执行轮训消费计划,{}", name(),CurrentUser.getProfileId());
            return;
        }
        try {
            // 指定任务类型采集执行，对过期任务进行执行
            busStarter.invokeTraining(busStarter.defaultTrainingConfigs());
            //关闭超期未执行的任务
            busStarter.closeAllErrorTasks();
        } catch (Exception e) {
            logger.error("[{}]执行轮训任务失败", name(), e);
        } finally {
            busDataLocker.release(busLocker);
        }
        LockParams splitDeliverBillLocker = busStarter.getSplitDeliverBillLocker(CurrentUser.getProfileId());
        boolean splitLock = busDataLocker.lock(splitDeliverBillLocker);
        if (!splitLock) {
            logger.info("[{}]锁定失败，结束推送拆分交易单计划(退货单据),{}", name(),CurrentUser.getProfileId());
            return;
        }
        try {
            String content = "拆分交易单";
            Task task = new Task(Md5Utils.md5(content), TaskType.SplitDeliverBill, new Date(), content);
            busStarter.sendBusOrExecute(Arrays.asList(task),CurrentUser.getProfileId());
        }
        catch (Exception ex)
        {
            logger.error("执行交易单拆分消息创建失败 {}", name(), ex);
        }
    }


    @Override
    public String name() {
        return "sale-bus";
    }

    @Override
    public List<BusTaskInfo> producer(String profileId) {
        //读取部署配置，该部署是否开启sale账套工具
        // 关闭自动生产工具
        boolean enabled = getToolConfig().isProducerEnabled();
        if (!enabled) {
            return Collections.emptyList();
        }
        if (databaseFilterConfig.ifFilterDb(CurrentUser.getServerId())){
            logger.error("账套被过滤{}",profileId);
            return Collections.emptyList();
        }
        logger.info("[{}]profileId【{}】向MQ发生消息", name(), profileId);
        ProfileResponse profileResponse = new ProfileResponse();
        profileResponse.setProfileId(new BigInteger(profileId));
        List<MqSendResult<ProfileResponse>> send = SysMqSend.send(Collections.singletonList(profileResponse), name());
        logger.debug("账套与消息,{},{}",profileId, JsonUtils.toJson(send));
        return Collections.emptyList();
    }
}
