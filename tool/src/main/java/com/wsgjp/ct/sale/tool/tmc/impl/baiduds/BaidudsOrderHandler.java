package com.wsgjp.ct.sale.tool.tmc.impl.baiduds;

import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.baiduds.entity.BaidudsTmcResponse;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.util.Date;
import java.util.Map;

@Component
public class BaidudsOrderHandler extends BaidudsNotifyBase{

    private static final Logger logger = LoggerFactory.getLogger(BaidudsOrderHandler.class);

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        //百度电商消息都有订单id 且售后消息也会通过更新订单更新售后单
        BaidudsTmcResponse response = new BaidudsTmcResponse();
        try {
            String message = invokeMessage.getMessage();
            Map body = JsonUtils.toObject(message, Map.class);
            String shopAccount = (body != null && body.containsKey("shopId"))
                    ? body.get("shopId").toString()
                    : "";
            if (StringUtils.isEmpty(shopAccount)) {
                logger.info("获取shopId失败", invokeMessage.getMessage());
                response.setErrno(1);
                response.setMsg("找不到eshopId");
                return JsonUtils.toJson(response);
            }
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(shopAccount, invokeMessage.getShopType().getCode());
            if(eshopRegister == null) {
                logger.info(String.format("百度电商找不到账套映射关系，传入的消息 : %s",invokeMessage.getMessage()));
                response.setErrno(1);
                response.setMsg("业务错误找不到店铺");
                return JsonUtils.toJson(response);
            }
            EshopNotifyChange change = new EshopNotifyChange();
            change.setProfileId(eshopRegister.getProfileId());
            change.setEshopId(eshopRegister.getId());
            change.setContent(invokeMessage.getMessage());
            change.setTradeOrderId( body.get("orderId").toString());
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(shopAccount);
            change.setCreateTime(new Date());
            SupportUtil.doNotify(change.getOnlineShopId(), change, invokeMessage.getShopType().getCode());
            response.setErrno(0);
        } catch (Exception e) {
            response.setErrno(1);
            response.setMsg(e.getMessage());
        }
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "baiduds.order";
    }
}
