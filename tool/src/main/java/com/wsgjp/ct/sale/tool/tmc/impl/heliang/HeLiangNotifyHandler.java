package com.wsgjp.ct.sale.tool.tmc.impl.heliang;

import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class HeLiangNotifyHandler extends HeLiangNotifyBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(HeLiangNotifyHandler.class);
    private final EshopTmcUtils eshopTmcUtils;

    public HeLiangNotifyHandler(EshopTmcUtils eshopTmcUtils) {
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        if (invokeMessage.getEshopId() == null) {
            return HeLiangUtils.buildResponse(HeLiangCode.FAIL);
        }
        EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), invokeMessage.getEshopId());
        if (Objects.isNull(eshopInfo)) {
            return HeLiangUtils.buildResponse(HeLiangCode.FAIL);
        }
        HeLiangBaseMessage heLiangMessage = JsonUtils.toObject(invokeMessage.getMessage(), HeLiangBaseMessage.class);
        if (heLiangMessage == null || heLiangMessage.getParamObj() == null || heLiangMessage.getParamObj().getMessageBody() == null) {
            return HeLiangUtils.buildResponse(HeLiangCode.FAIL);
        }
        MessageBody messageBody = heLiangMessage.getParamObj().getMessageBody();
        String appId = heLiangMessage.getParamObj().getAppId();
        if (StringUtils.isBlank(appId)) {
            LOGGER.error("账套ID:{},店铺ID：{},禾量onlineEshopId为空，原始报文：{}"
                    , invokeMessage.getProfileId(), invokeMessage.getEshopId(), invokeMessage.getMessage());
            return HeLiangUtils.buildResponse(HeLiangCode.SUCCESS);
        }
        if (StringUtils.isBlank(messageBody.getOutTradeNo())) {
            LOGGER.error("账套ID:{},店铺ID：{},onlineEshopId:{},禾量订单号为空，原始报文：{}"
                    , invokeMessage.getProfileId(), invokeMessage.getEshopId(), appId, invokeMessage.getMessage());
            return HeLiangUtils.buildResponse(HeLiangCode.FAIL);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setTradeOrderId(messageBody.getOutTradeNo());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(invokeMessage.getMessage());
        SupportUtil.sendMessage(eshopNotifyChange, eshopInfo);
        return HeLiangUtils.buildResponse(HeLiangCode.SUCCESS);
    }

    @Override
    public String serviceName() {
        return "heLiangInvoker";
    }
}
