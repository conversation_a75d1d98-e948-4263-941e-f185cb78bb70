package com.wsgjp.ct.sale.tool.bus;

import com.wsgjp.ct.sale.biz.jarvis.common.redis.LockerOperationEnum;
import com.wsgjp.ct.sale.biz.jarvis.common.redis.impl.CommonRedisBizLocker;
import com.wsgjp.ct.sale.bus.center.BusDataCenter;
import com.wsgjp.ct.sale.bus.center.BusStarter;
import com.wsgjp.ct.sale.bus.entity.BusTaskInfo;
import com.wsgjp.ct.sale.bus.entity.Task;
import com.wsgjp.ct.sale.bus.entity.TaskData;
import com.wsgjp.ct.sale.bus.entity.TaskType;
import com.wsgjp.ct.sale.bus.utils.BusDataLockerImpl;
import com.wsgjp.ct.sale.monitor.JarvisMonitorBuilder;
import com.wsgjp.ct.sale.monitor.entity.MonitorKeyConstant;
import com.wsgjp.ct.sale.tool.logo.config.ToolCalculateConfig;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.monitor.support.MeterType;
import ngp.service.component.job.BaseProfileJob;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Date;

//这里的AbstractBusConsumerInvoker 必须要有泛型。。不然消费消息的时候，获取父类泛型会报错
public abstract class AbstractBusConsumerInvoker<T extends BusTaskInfo> extends BaseProfileJob<T> {
    private final ToolCalculateConfig toolConfig;
    private final BusStarter busStarter;
    private final BusDataCenter busDataCenter;
    protected final BusDataLockerImpl busDataLocker;

    Logger logger = LoggerFactory.getLogger(AbstractBusConsumerInvoker.class);

    public AbstractBusConsumerInvoker(ToolCalculateConfig toolConfig, BusStarter busStarter, BusDataCenter busDataCenter, BusDataLockerImpl busDataLocker) {
        this.toolConfig = toolConfig;
        this.busStarter = busStarter;
        this.busDataCenter = busDataCenter;
        this.busDataLocker = busDataLocker;
    }

    /**
     * 测试资源原因，planTwo方案还在确认是否能够提交测试
     * @param messageId
     * @param createTimestamp
     * @param task
     * @return
     */
    public boolean planTwoExecute(String messageId, long createTimestamp, T task) {
        JarvisMonitorBuilder.NgpResource ngpResource = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_BUS_CUSTOMER, "type,系统轮询", MeterType.Summary, 1);
        ngpResource.start();
        try {
            Task busTask = MessageDecodeHelper.DecodeTask(task);
            String tags = getTags(busTask);
            ngpResource.updateTags(tags);
            if (busTask == null) {
                // 轮询
                invokeTraining(task);
            } else {
                // 执行指定任务
                busStarter.execute(new TaskData(busTask));
            }
            ngpResource.end(false);
        } catch (Exception exception) {
            ngpResource.end(true);
            logger.error("执行总线任务失败", exception);
            return false;
        }
        return true;
    }

    @Override
    public boolean execute(String messageId, long createTimestamp, T task) {
        if (null == task) {
            logger.debug("[{}]消费者传参为空", name());
            return true;
        }
        logger.info("开始执行总线任务{},{},{}", CurrentUser.getProfileId(), messageId, JsonUtils.toJson(task));
        CommonRedisBizLocker commonRedisBizLocker = new CommonRedisBizLocker(CurrentUser.getProfileId(), messageId, LockerOperationEnum.TOOL_BUG, 60);
        if (!commonRedisBizLocker.lockSuccess()) {
            logger.info("锁定失败，结束执行总线任务{},{}", CurrentUser.getProfileId(), messageId);
            return true;
        }
        try {
            //读取部署配置，该部署是否开启sale账套工具
            boolean enabled = toolConfig.isEnabled();
            if (!enabled) {
                return true;
            }
            JarvisMonitorBuilder.NgpResource ngpResourceW = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_BUS_CUSTOMER, "type,外围监控", MeterType.Summary, 1);
            ngpResourceW.start();
            try {
                if (StringUtils.isNotEmpty(task.getBusMessage())) {
                    busTask(messageId,task.getBusMessage());
                } else if (StringUtils.isNotEmpty(task.getAccountingMessage())) {
                    accountTask(messageId,task.getAccountingMessage());
                }else if (StringUtils.isNotEmpty(task.getWmsMessage())) {
                    wmsTask(messageId,task.getWmsMessage());
                } else {
                    //执行工具的produce发起的轮训任务
                    JarvisMonitorBuilder.NgpResource ngpResource = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_BUS_CUSTOMER, "type,系统轮询", MeterType.Summary, 1);
                    ngpResource.start();
                    try {
                        invokeTraining(task);
                        ngpResource.end(false);
                    } catch (Exception e) {
                        logger.error("执行总线轮询出错", e);
                        ngpResource.end(true);
                    }
                }
                ngpResourceW.end(false);
                logger.info("结束执行总线任务{}",ngpResourceW.getLimitTime());
                return true;
            } catch (RuntimeException exception) {
                ngpResourceW.end(true);
                logger.error("执行总线任务失败", exception);
                throw exception;
            }
        } finally {
            try {
                if (commonRedisBizLocker != null) {
                    commonRedisBizLocker.close();
                }
            } catch (Exception e) {
                logger.error("取消MQ分布式锁失败", e);
            }
            logger.info("结束执行总线任务{},{}", CurrentUser.getProfileId(), messageId);
        }
    }

    protected abstract void invokeTraining(T taskInfo);

    private void accountTask(String messageId,String accountingMessage) {
        //将消息解析提交总线备档，并执行
        Task task = new Task(Md5Utils.md5(accountingMessage), TaskType.SendedStockPostBill, new Date(), accountingMessage);
        task.setMqId(messageId);
        try {
            busDataCenter.reInsertBusData(Arrays.asList(task));
        } catch (Exception exception) {
            logger.error("[sale-bus]将结果初始化到副本失败", exception);
        }
        executorTask(new TaskData(task));
    }
    private void wmsTask(String messageId,String wmsMessage) {
        //将消息解析提交总线备档，并执行
        Task task = new Task(Md5Utils.md5(wmsMessage), TaskType.WmsSendMessage, new Date(), wmsMessage);
        task.setMqId(messageId);
        try {
            busDataCenter.reInsertBusData(Arrays.asList(task));
        } catch (Exception exception) {
            logger.error("[sale-bus]将结果初始化到副本失败", exception);
        }
        executorTask(new TaskData(task));
    }

    private TaskData busTask(String messageId,String busMessage) {
        logger.debug("[sale-bus]总线任务正在执行{},{}", CurrentUser.getProfileId(), busMessage);
        TaskData task = JsonUtils.toObject(busMessage, TaskData.class);
        // 有些任务需要重新写道数据库
        task.setMqId(messageId);
        try{
            busDataCenter.reInsertBusData(Arrays.asList(task));
        } catch (Exception exception) {
            logger.error("[sale-bus]将结果初始化到副本失败", exception);
        }
        executorTask(task);
        return task;
    }

    protected void executorTask(TaskData task) {
        String tag = getTags(task);
        JarvisMonitorBuilder.NgpResource ngpResource = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_BUS_CUSTOMER, tag, MeterType.Summary, 1);
        ngpResource.start();
        try {
            busStarter.execute(task);
            ngpResource.end(false);
        } catch (Exception e) {
            ngpResource.end(true);
            logger.error(String.format("[sale-bus]账套[%s]执行任务异常，错误信息：%s", CurrentUser.getProfileId(), e.getMessage()), e);
            throw e;
        }
    }

    private static String getTags(Task task) {
        if (task !=null && task.getTaskType() != null) {
            return String.format("type,%s", task.getTaskType().getName());
        }
        return "type,系统轮询";
    }

    protected ToolCalculateConfig getToolConfig() {
        return toolConfig;
    }

    protected BusStarter getBusStarter() {
        return busStarter;
    }

    protected BusDataCenter getBusDataCenter() {
        return busDataCenter;
    }
}
