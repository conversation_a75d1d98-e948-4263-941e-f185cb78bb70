package com.wsgjp.ct.sale.tool.tmc.impl.jdong;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.exception.PlatformInterfaceException;
import com.wsgjp.ct.sale.tool.product.entity.ProductConst;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.JdongImageAuditRequest;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.JdongProductAuditRequest;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.JdongTmcResponse;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.JdongTopic;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class JdongProductAuditHandler extends JdongNotifyBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(JdongProductAuditHandler.class);

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String tmcMessage = invokeMessage.getMessage();
        JdongTopic topic = JsonUtils.toObject(tmcMessage, JdongTopic.class);
        if (StringUtils.equals(topic.getTopic(), "vc_ware_apply_state_change")) {
            return productAudit(invokeMessage);
        } else {
            return imageAudit(invokeMessage);
        }
    }

    public String productAudit(InvokeMessageEntity invokeMessage) {
        JdongProductAuditRequest apiRequest;
        try {
            apiRequest = JsonUtils.toObject(invokeMessage.getMessage(), JdongProductAuditRequest.class);
            if (apiRequest == null) {
                throw new PlatformInterfaceException(String.format("平台接口返回报文数据异常,报文信息：%s", invokeMessage.getMessage()));
            }
        } catch (Exception ex) {
            LOGGER.error("京东tmMessage数据转换成JdongProductAuditRequest实体出错，错误信息：{}", ex.getMessage(), ex);
            return "";
        }
        return doAudit(apiRequest.getVenderCode(), apiRequest.getApplyId(), invokeMessage.getMessage(),ProductConst.PRODUCT_PUBLISH_AUDIT);
    }

    public String imageAudit(InvokeMessageEntity invokeMessage) {
        JdongImageAuditRequest apiRequest;
        try {
            apiRequest = JsonUtils.toObject(invokeMessage.getMessage(), JdongImageAuditRequest.class);
            if (apiRequest == null) {
                throw new PlatformInterfaceException(String.format("平台接口返回报文数据异常,报文信息：%s", invokeMessage.getMessage()));
            }
        } catch (Exception ex) {
            LOGGER.error("京东tmMessage数据转换成JdongProductAuditRequest实体出错，错误信息：{}", ex.getMessage(), ex);
            return "";
        }
        return doAudit(apiRequest.getVenderCode(), apiRequest.getBizId(), invokeMessage.getMessage(),ProductConst.PRODUCT_PUBLISH_AUDIT_IMAGE);
    }

    public String doAudit(String venderId, String applyId, String message,String productType) {
        EshopRegisterNotify notify = SupportUtil.buildNotify(venderId, ShopType.JdongVC.getCode());
        if (notify == null) {
            LOGGER.error("profileId:{},京东,供应商简码:{},查询店铺信息为空!tmMessage:{}", CurrentUser.getProfileId(), venderId, message);
            return buildResponse("501", "管家婆未找到对应店铺!");
        }
        ShopType shopType = ShopType.valueOf(notify.getType());
        EshopNotifyChange tmcMsg = new EshopNotifyChange();
        tmcMsg.setType(TMCType.Ptype);
        tmcMsg.setApplyId(applyId);
        tmcMsg.setOnlineShopId(venderId);
        tmcMsg.setContent(message);
        tmcMsg.setProductType(productType);
        SupportUtil.doProductNotify(venderId, tmcMsg, shopType.getCode());
        return buildResponse("0", "success");
    }

    @Override
    public String serviceName() {
        return "productAuditHandler";
    }

    protected String buildResponse(String code, String msg) {
        return JsonUtils.toJson(new JdongTmcResponse(code, msg));
    }
}
