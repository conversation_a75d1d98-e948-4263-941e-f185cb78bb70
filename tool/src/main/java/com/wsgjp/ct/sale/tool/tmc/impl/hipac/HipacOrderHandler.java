package com.wsgjp.ct.sale.tool.tmc.impl.hipac;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac.newhipac.HipacNewResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.math.BigInteger;
import java.text.ParseException;
import java.util.Date;
import java.util.Objects;

@Component
public class HipacOrderHandler extends HipacNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HipacOrderHandler.class);

    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;

    public HipacOrderHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======海拍客进入invoker方法======");
        String tmMessage = invokeMessage.getMessage();
        HipacNewResponse hipacNewResponse;
        try {
            hipacNewResponse = JsonUtils.toObject(tmMessage,HipacNewResponse.class);
        } catch (Exception e) {
            String errMsg = String.format("海拍客tmMessage数据转换成HipacOrderResponse实体出错，错误信息：%s", e.getMessage());
            return JsonUtils.toJson(new HipacTmcResponse(500, errMsg, "false"));
        }
        try {
            EshopRegisterNotify notify = SupportUtil.buildNotify(hipacNewResponse.getHipacPush().getSupplier().getSupplierSenderID(), 59);
            saveTmcOrderMsg(invokeMessage.getProfileId(), notify.getId(), tmMessage, hipacNewResponse.getHipacPush());
        } catch (Exception ex) {
            LOGGER.error("海拍客保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", invokeMessage.getProfileId(), invokeMessage.getEshopId(), tmMessage, ex.getMessage());
            return JsonUtils.toJson(new HipacTmcResponse(500, "海拍客订单数据保存数据库出错!", "false"));
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        String tradeId = hipacNewResponse.getHipacPush().getOrder().getOrderNum();
        eshopNotifyChange.setTradeOrderId(tradeId);
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(tmMessage);
        SupportUtil.doOrderNotify(hipacNewResponse.getHipacPush().getSupplier().getSupplierSenderID(), eshopNotifyChange, ShopType.Hipac.getCode());
        return JsonUtils.toJson(new HipacTmcResponse(200, "成功!", "true"));
    }

    private int saveTmcOrderMsg(BigInteger profileId, BigInteger eshopId, String tmMessage, HipacNewResponse.HipacPush apiOrder) {
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(profileId, eshopId, apiOrder.getOrder().getOrderNum());
        if (Objects.isNull(orderMsgEntity)) {
            orderMsgEntity = buildEshopTmcOrderMsgEntity(profileId, eshopId, tmMessage, apiOrder);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        } else {
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(BigInteger profileId, BigInteger eshopId, String tmMessage, HipacNewResponse.HipacPush apiOrder) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(profileId);
        tmcOrderMsgEntity.setEshopId(eshopId);
        tmcOrderMsgEntity.setShopType(ShopType.Hipac);
        tmcOrderMsgEntity.setTradeOrderId(apiOrder.getOrder().getOrderNum());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        try {
            Date date = DateUtils.parseDate(apiOrder.getOrder().getOrderDate(), "yyyy-mm-dd HH:mm:ss");
            tmcOrderMsgEntity.setCreateTime(date);
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (ParseException e) {
        }
        return tmcOrderMsgEntity;
    }

    @Override
    public String serviceName() {
        return "hipac.hsc.order.push";
    }

    public <T> T convertToJavaBean(String xml, Class<T> t) throws Exception {
        JAXBContext context = JAXBContext.newInstance(t);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        return (T) unmarshaller.unmarshal(new StringReader(xml));
    }
}
