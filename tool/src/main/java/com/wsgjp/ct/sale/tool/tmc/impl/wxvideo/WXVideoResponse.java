package com.wsgjp.ct.sale.tool.tmc.impl.wxvideo;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WXVideoResponse {
    private String ToUserName;
    private String FromUserName;
    private String CreateTime;
    private String MsgType;
    private String Event;
    private OrderInfo order_info;
    private FinderShopAftersaleStatusUpdate finderShopAftersaleStatusUpdate;
    @JsonProperty("finder_shop_aftersale_status_update")
    public FinderShopAftersaleStatusUpdate getFinderShopAftersaleStatusUpdate() { return finderShopAftersaleStatusUpdate; }
    @JsonProperty("finder_shop_aftersale_status_update")
    public void setFinderShopAftersaleStatusUpdate(FinderShopAftersaleStatusUpdate value) { this.finderShopAftersaleStatusUpdate = value; }

    public String getToUserName() {
        return ToUserName;
    }

    public void setToUserName(String toUserName) {
        ToUserName = toUserName;
    }

    public String getFromUserName() {
        return FromUserName;
    }

    public void setFromUserName(String fromUserName) {
        FromUserName = fromUserName;
    }

    public String getCreateTime() {
        return CreateTime;
    }

    public void setCreateTime(String createTime) {
        CreateTime = createTime;
    }

    public String getMsgType() {
        return MsgType;
    }

    public void setMsgType(String msgType) {
        MsgType = msgType;
    }

    public String getEvent() {
        return Event;
    }

    public void setEvent(String event) {
        Event = event;
    }

    public OrderInfo getOrder_info() {
        return order_info;
    }

    public void setOrder_info(OrderInfo order_info) {
        this.order_info = order_info;
    }

    public static class OrderInfo{
        private String order_id;
        private String type;

        public String getOrder_id() {
            return order_id;
        }

        public void setOrder_id(String order_id) {
            this.order_id = order_id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }

    public static class FinderShopAftersaleStatusUpdate {
        private String status;
        private String afterSaleOrderID;
        private String orderID;

        @JsonProperty("status")
        public String getStatus() { return status; }
        @JsonProperty("status")
        public void setStatus(String value) { this.status = value; }

        @JsonProperty("after_sale_order_id")
        public String getAfterSaleOrderID() { return afterSaleOrderID; }
        @JsonProperty("after_sale_order_id")
        public void setAfterSaleOrderID(String value) { this.afterSaleOrderID = value; }

        @JsonProperty("order_id")
        public String getOrderID() { return orderID; }
        @JsonProperty("order_id")
        public void setOrderID(String value) { this.orderID = value; }
    }
}
