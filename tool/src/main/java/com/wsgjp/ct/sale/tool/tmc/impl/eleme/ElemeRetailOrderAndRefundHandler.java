package com.wsgjp.ct.sale.tool.tmc.impl.eleme;

import com.alibaba.fastjson.JSON;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.refund.EshopTmcRefundMsgDto;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.elemeretail.ElemeRetailConfig;
import com.wsgjp.ct.sale.platform.factory.elemeretail.ElemeRetailRefund;
import com.wsgjp.ct.sale.platform.factory.elemeretail.entity.OrderMessageInfo;
import com.wsgjp.ct.sale.platform.sdk.entity.Etype;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopBaseInfoMapper;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.eleme.entity.ElemeRetailBaseRequestBody;
import com.wsgjp.ct.sale.tool.tmc.impl.eleme.entity.ElemeRetailBaseResponseBody;
import com.wsgjp.ct.sale.tool.tmc.impl.eleme.entity.ElemeRetailTmcBaseRequest;
import com.wsgjp.ct.sale.tool.tmc.impl.eleme.entity.ElemeRetailTmcBaseResponse;
import com.wsgjp.ct.sale.tool.tmc.impl.eleme.utils.ElemeRetailUtils;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.pubmessage.PubMessageCenterPip;
import com.wsgjp.ct.support.pubmessage.PubMessageCenterRequest;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 饿了么零售订单、售后TMC消息处理
 * 策略：无论订单还是售后都是发送订单变更消息给业务处理组，通过接口更新订单、售后单
 */
@Component
public class ElemeRetailOrderAndRefundHandler extends ElemeRetailNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ElemeRetailOrderAndRefundHandler.class);
    private final ElemeRetailConfig elemeRetailConfig;
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    private final EshopTmcUtils eshopTmcUtils;

    private final EshopTmcRefundMsgService tmcRefundMsgService;


    public ElemeRetailOrderAndRefundHandler(ElemeRetailConfig elemeRetailConfig, EshopTmcOrderMsgMapper tmcOrderMsgMapper, EshopTmcUtils eshopTmcUtils, EshopTmcRefundMsgService tmcRefundMsgService) {
        this.elemeRetailConfig = elemeRetailConfig;
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
        this.eshopTmcUtils = eshopTmcUtils;
        this.tmcRefundMsgService = tmcRefundMsgService;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        ElemeRetailTmcBaseRequest apiRequest;
        try {
            apiRequest = JsonUtils.toObject(tmMessage, ElemeRetailTmcBaseRequest.class);
            if (apiRequest != null && StringUtils.isNotBlank(apiRequest.getBody())) {
                apiRequest.setBodyEntity(JsonUtils.toObject(apiRequest.getBody(), ElemeRetailBaseRequestBody.class));
            } else {
                throw new RuntimeException(String.format("平台接口返回报文数据异常,报文信息：%s", tmMessage));
            }
        } catch (Exception ex) {
            LOGGER.error("{}tmMessage数据转换成ElemeRetailTmcBaseRequest实体出错，错误信息：{}", shopTypeName, ex.getMessage(), ex);
            return "";
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiRequest.getBodyEntity().getPlatform_shop_id(), invokeMessage.getShopType().getCode());
        if (Objects.isNull(eshopRegister)) {
            LOGGER.info("profileId:{},店铺类型:{},platform_shop_id:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, apiRequest.getBodyEntity().getPlatform_shop_id(), tmMessage);
            return buildResponse("管家婆未找到对应店铺!", apiRequest);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(apiRequest.getBodyEntity().getPlatform_shop_id());
        eshopNotifyChange.setTradeOrderId(apiRequest.getBodyEntity().getOrder_id());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(tmMessage);
        if ("order.deliveryStatus.push".equals(apiRequest.getCmd()) && "18".equals(apiRequest.getBodyEntity().getStatus())) {
            try {
                Thread.sleep(1500);
            } catch (InterruptedException ex) {
                LOGGER.info(ex.getMessage(), ex);
                Thread.currentThread().interrupt();
            }
        } else if (StringUtils.equals(apiRequest.getCmd(), "order.timeout.push")) {
            ShopType shopType = ShopType.valueOf(eshopRegister.getType());
            saveTmcOrderMsg(invokeMessage.getProfileId(), eshopRegister.getId(), shopType, apiRequest.getBodyEntity());
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopRegister.getId());
            pushMessage(apiRequest.getBodyEntity(), eshopInfo);
        }else if (StringUtils.equals(apiRequest.getCmd(), "order.reverse.push")){
            //售后消息
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopRegister.getId());
            saveTmcRefundMsg(eshopInfo,apiRequest.getBodyEntity(),tmMessage);
        }
        SupportUtil.doOrderNotify(apiRequest.getBodyEntity().getPlatform_shop_id(), eshopNotifyChange, invokeMessage.getShopType().getCode());
        return buildResponse("success", apiRequest);
    }

    protected void saveTmcRefundMsg(EshopInfo eshopInfo, ElemeRetailBaseRequestBody bodyEntity, String message) {
        try {
            RefundStatus refundStatus = ElemeRetailRefund.getRefundStatus(bodyEntity.getRefund_status());
            EshopTmcRefundMsgDto refundMsgDto = new EshopTmcRefundMsgDto(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), eshopInfo.getEshopType(),
                    bodyEntity.getOrder_id(), bodyEntity.getRefund_order_id(), message, refundStatus);
            tmcRefundMsgService.saveOrUpdateTmcRefundMsg(refundMsgDto);
        } catch (Exception ex) {
            LOGGER.error("账套ID{},店铺ID{},保持售后消息失败，失败原因：{}.message:{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), ex.getMessage(), message, ex);
        }
    }

    private int saveTmcOrderMsg(BigInteger profileId, BigInteger eshopId, ShopType shopType, ElemeRetailBaseRequestBody apiOrder) {
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(profileId, eshopId, apiOrder.getOrder_id());
        if (Objects.isNull(orderMsgEntity)) {
            orderMsgEntity = buildEshopTmcOrderMsgEntity(profileId, eshopId, shopType, apiOrder);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        } else {
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            OrderMessageInfo orderMsg = JsonUtils.toObject(orderMsgEntity.getMessage(), OrderMessageInfo.class);
            if (orderMsg == null) {
                orderMsg = new OrderMessageInfo();
            }
            orderMsg.setTradeId(apiOrder.getOrder_id());
            orderMsg.getTimeoutTypes().add(apiOrder.getTimeout_type());
            orderMsgEntity.setMessage(JsonUtils.toJson(orderMsg));
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private String buildResponse(String errMsg, ElemeRetailTmcBaseRequest apiRequest) {
        ElemeRetailTmcBaseResponse response = new ElemeRetailTmcBaseResponse();
        ElemeRetailBaseResponseBody body = new ElemeRetailBaseResponseBody();
        if (StringUtils.equals(errMsg, "success")) {
            body.setErrno(0);
            body.setError("success");
        } else {
            body.setErrno(500);
            body.setError(errMsg);
        }
        response.setBody(body);
        response.setCmd(apiRequest.getCmd());
        response.setTicket(UUID.randomUUID().toString().toUpperCase());
        response.setSource(apiRequest.getSource());
        response.setVersion(apiRequest.getVersion());
        response.setTimestamp(System.currentTimeMillis() / 1000);
        response.setSign(ElemeRetailUtils.signMethod(response, elemeRetailConfig.getAppSecret()));
        return JSON.toJSONString(response);
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(BigInteger profileId, BigInteger eshopId, ShopType shopType, ElemeRetailBaseRequestBody apiOrder) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(profileId);
        tmcOrderMsgEntity.setEshopId(eshopId);
        tmcOrderMsgEntity.setShopType(shopType);
        tmcOrderMsgEntity.setTradeOrderId(apiOrder.getOrder_id());
        OrderMessageInfo messageInfo = new OrderMessageInfo();
        messageInfo.setTradeId(apiOrder.getOrder_id());
        messageInfo.getTimeoutTypes().add(apiOrder.getTimeout_type());
        tmcOrderMsgEntity.setMessage(JsonUtils.toJson(messageInfo));
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setCreateTime(new Date());
        //平台没有返回更新时间，平台推送一次就更新一次
        tmcOrderMsgEntity.setUpdateTime(new Date());
        return tmcOrderMsgEntity;
    }

    private void pushMessage(ElemeRetailBaseRequestBody bodyEntity, EshopInfo eshopInfo) {
        //超时类型，枚举值：confirm_order=接单即将超时 refund_order=退款审核即将超时 pick_order=拣货即将超时
        //平台状态：1、待确认，5、订单已确认(订单已接单)，7、骑士已接单，8、骑士已取餐;，9、已完成，10、已取消
        String content;
        String title;
        if (StringUtils.equals(bodyEntity.getTimeout_type(), "confirm_order")) {
            title = "接单即将超时";
        } else if (StringUtils.equals(bodyEntity.getTimeout_type(), "refund_order")) {
            title = "线上售后处理即将超时";
        } else if (StringUtils.equals(bodyEntity.getTimeout_type(), "pick_order")) {
            title = "拣货即将超时";
        } else {
            return;
        }
        content = String.format("店铺【%s】的订单：%s，%s，请尽快处理。", eshopInfo.getFullname(), bodyEntity.getOrder_id(), title);
        pushMessage(content, title);
    }

    private void pushMessage(String content, String title) {
        try {
            PubMessageCenterRequest request = new PubMessageCenterRequest();
            List<BigInteger> etypeList = getetypeList(CurrentUser.getProfileId());
            request.setEventType("业务提醒");
            request.setSubjectType(PubMessageCenterRequest.PubMessageCenterSubjectType.deliveryTimeOrder.getKey());
            request.setContext(content);
            request.setEtypeIds(etypeList);
            request.setTitle(title);
            request.setUri("");
            PubMessageCenterPip.Util.push(request);
        } catch (Exception ex) {
            LOGGER.error("账套ID:{},发送消息铃铛失败。消息内容：{},失败原因:{}", CurrentUser.getProfileId(), content, ex.getMessage(), ex);
        }
    }

    private List<BigInteger> getetypeList(BigInteger profileId) {
        PlatformSdkEshopBaseInfoMapper baseInfoMapper = BeanUtils.getBean(PlatformSdkEshopBaseInfoMapper.class);
        List<Etype> etypeInfoList = baseInfoMapper.getEtypeList(profileId);

        List<BigInteger> etypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(etypeInfoList)) {
            etypeList = etypeInfoList.stream().map(Etype::getId).collect(Collectors.toList());
        }
        return etypeList;
    }

    @Override
    public String serviceName() {
        return "elemeRetailOrderAndRefundInvoker";
    }
}
