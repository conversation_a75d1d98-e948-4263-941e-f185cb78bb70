package com.wsgjp.ct.sale.tool.tmc.impl.yuanqi;

import bf.datasource.typehandler.CodeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum YuanQiTMCType implements CodeEnum {
    PurchaseOrderPaySuccess(0, "order.pay.succeed", "订单支付成功后通知", "yuanqisenlin.purchase.order"),
    PurchaseOrderOutSuccess(1, "order.out.succeed", "订单出库后通知", "yuanqisenlin.purchase.order"),
    PurchaseOrderCancel(2, "order.cancel", "订单取消后通知", "yuanqisenlin.purchase.order"),
    PurchaselogisticReceived(3, "logistics.received", "订单已接单", "yuanqisenlin.purchase.order"),
    PurchaselogisticTransport(4, "logistics.transport", "订单开始运输", "yuanqisenlin.purchase.order"),
    ProductUpdate(5, "product.update", "元气商品更新时通知", "yuanqisenlin.purchase.product"),
    ProductAdd(6, "product.erp.add", "新增商品", "yuanqisenlin.purchase.product"),
    XunDianOrderChange(7, "notify.orderinfochange", "巡店订单改变事件", "yuanqisenlin.xundian.order"),
    ProductBind(7, "product.erp.bind", "绑定商品", "yuanqisenlin.purchase.product"),
    ProductDelete(8, "product.delete", "删除商品", "yuanqisenlin.purchase.product"),
    ;
    private static final Logger logger = LoggerFactory.getLogger(YuanQiTMCType.class);

    private final int index;
    private final String name;
    private final String desc;
    private final String serviceName;

    YuanQiTMCType(int code, String name, String desc, String serviceName) {
        this.index = code;
        this.name = name;
        this.desc = desc;
        this.serviceName = serviceName;
    }

    @Override
    public int getCode() {
        return index;
    }

    @Override
    public String getName() {
        return name;
    }


    public String getServiceName() {
        return serviceName;
    }

    public static YuanQiTMCType getEnumTypeByIndex(int index) {
        YuanQiTMCType[] type = YuanQiTMCType.values();
        for (int i = 0; i < type.length; i++) {
            if (type[i].getCode() == index) {
                return type[i];
            }
        }
        return null;
    }

    public static YuanQiTMCType getEnumTypeByName(String name) {
        logger.info("元气消息getEnumTypeByName....");
        YuanQiTMCType[] type = YuanQiTMCType.values();
        logger.info("元气消息getEnumTypeByName---1111:type=" + (type == null ? "null" : type.length));
        for (int i = 0; i < type.length; i++) {
            logger.info("元气消息getServiceName-type-name:【{}】,type-serviceName:【{}】,param-name:【{}】", type[i].getName(), type[i].getServiceName(), name);
            if (type[i].getName().equals(name)) {
                return type[i];
            }
        }
        return null;
    }
}
