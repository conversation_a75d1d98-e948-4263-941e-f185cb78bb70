package com.wsgjp.ct.sale.tool.tmc.impl.meituands;

import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.impl.meituands.entity.MeiTuanDsBaseResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import ngp.utils.JsonUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 **/
public abstract class MeiTuanDsNotifyBase implements MessageHandler {

    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(true);
        return result;
    }

    public static String buildResponse(String msg) {
        MeiTuanDsBaseResponse response = new MeiTuanDsBaseResponse();
        response.setData(StringUtils.isEmpty(msg) ? "ok" : msg);
        return JsonUtils.toJson(response);
    }

}
