package com.wsgjp.ct.sale.tool.tmc.service;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopSaleOrderDownloadResponse;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcProductMapper;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.constant.SyncOrderConst;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.order.TmcBaseMessage;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.common.entity.product.TmcProductMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcNotifyChangeContent;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ConsumerType;
import com.wsgjp.ct.sale.common.utils.PlatformProfileMappingUtils;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.common.log.EshopTmcMessageLog;
import com.wsgjp.ct.sale.tool.common.log.MqConsumeLog;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.mapper.EshopInfoMapper;
import com.wsgjp.ct.sale.tool.tmc.utils.TmcLogUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.mq.SysMqSend;
import ngp.idgenerator.UId;
import ngp.mq.MqSendResult;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

public class SupportUtil {
    private static final Logger sysLogger = LoggerFactory.getLogger(SupportUtil.class);

    public static void doNotify(String shopAccount, EshopNotifyChange change, int shopType) {
        doOrderNotify(shopAccount, change, shopType);
    }


    public static void doProductNotify(String shopAccount, EshopNotifyChange change, int shopType) {
        try {
            sysLogger.info("*****************开始构建商品mq消息************************");
            TmcProductMessage message = getTmcProductMessage(shopAccount, change, shopType);
            EshopTmcConfig config = GetBeanUtil.getBean(EshopTmcConfig.class);
            SysMqSend.send(Collections.singletonList(message), config.getTmcProductMqTopic());
        } catch (Exception e) {
            sysLogger.error("元气发送商品mq消息报错" + e.getMessage());
        }
    }

    public static void doOrderNotify(String shopAccount, EshopNotifyChange change, int shopType) {
        boolean filterTmcShopTy = isFilterTmcShopTy(shopType, change.getType());
        if (filterTmcShopTy) {
            return;
        }
        change.setShopType(shopType);
        TmcOrderMessage message = getTmcOrderMessage(shopAccount, change, shopType);
        message.setShopCode(shopType);
        doSendMessage(message, change.getType());
    }

    public static boolean isFilterTmcShopTy(int shopType, TMCType type) {
        try {
            EshopTmcConfig eshopTmcConfig = BeanUtils.getBean(EshopTmcConfig.class);
            List<String> platformFilterTmcShopTyList = eshopTmcConfig.getPlatformFilterTmcShopTyList();
            if (CollectionUtils.isNotEmpty(platformFilterTmcShopTyList)) {
                for (String shopTy : platformFilterTmcShopTyList) {
                    if (String.valueOf(shopType).equals(shopTy)) {
                        sysLogger.info("店铺类型为：{}，不发送mq消息", shopTy);
                        return true;
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(eshopTmcConfig.getFilterTmcShopTypeByTmcType())) {
                String msgType = eshopTmcConfig.getFilterTmcShopTypeByTmcType().get(ShopType.valueOf(shopType).toString());
                if (StringUtils.isNotEmpty(msgType)) {
                    String[] split = msgType.split(",");
                    for (String s : split) {
                        if (s.equals(String.valueOf(type.getCode()))) {
                            return true;
                        }
                    }
                }
            }

        } catch (Exception e) {
            sysLogger.error("过滤tmc消息店铺类型为：{}，不发送mq消息失败，错误原因:{}", shopType, e.getMessage(), e);
        }
        return false;
    }

    public static void doOrderNotifyByDelayWay(String shopAccount, EshopNotifyChange change, int shopType, int delay) {
        boolean filterTmcShopTy = isFilterTmcShopTy(shopType, change.getType());
        if (filterTmcShopTy) {
            return;
        }
        change.setShopType(shopType);
        TmcOrderMessage message = getTmcOrderMessage(shopAccount, change, shopType);
        message.setShopCode(shopType);
        if (change.getType() == TMCType.DIRECT_UPDATE_ORDER || change.getType() == TMCType.DIRECT_UPDATE_TRADE_STATUS || change.getType() == TMCType.DIRECT_UPDATE_MEMO) {
            message.setTag(SyncOrderConst.DIRECT_UPDATE_ORDER);
        }
        doSendMessage(message, change.getType(), delay);
    }

    public static void doUpdateOrderStatusNotifyByDelayWay(String shopAccount, EshopNotifyChange change, int shopType, int delaySeconds) {
        // 王重阳那边执行修改订单状态又要33 , 34 ,35,所以pl_eshop_notify_change 存32，消费者那边也识别32，
        TmcOrderMessage message = buildTmcUpdateOrderMessage(shopAccount, change, shopType);
        message.setPlatformUpdateOrderJson(change.getContent());
        if (change.getType() == TMCType.DIRECT_UPDATE_ORDER) {
            message.setTag(SyncOrderConst.DIRECT_UPDATE_ORDER);
        }
        doSendMessage(message, change.getType(), delaySeconds);
    }

    private static TmcOrderMessage buildTmcUpdateOrderMessage(String shopAccount, EshopNotifyChange change, int shopType) {
        if (change.getEshopId() == null || change.getProfileId() == null) {
            EshopRegisterNotify notify = buildNotify(shopAccount, shopType);
            change.setId(UId.newId());
            change.setProfileId(notify.getProfileId());
            change.setEshopId(notify.getId());
            TmcNotifyChangeContent content = new TmcNotifyChangeContent();
            content.setConsumerType(buildConsumerType(change.getType()));
            if (change.getPubTime() == null) {
                change.setPubTime(DateUtils.getDate());
            }
            content.setPubTime(DateUtils.formatDate(change.getPubTime(), "yyyy-MM-dd HH:mm:ss"));
            content.setSellerFlag(change.getSellerFlag() != null ? change.getSellerFlag().getCode() : null);
            content.setSellerMemo(change.getSellerMemo());
            content.setBuyerMessage(change.getBuyerMessage());
            content.setTradeId(change.getTradeOrderId());
            content.setRefundId(change.getRefundOrderId());
            content.setTradeStatus(change.getOrderStatus() != null ? change.getOrderStatus().getCode() : null);
            content.setRefundStatus(change.getRefundStatus() != null ? change.getRefundStatus().getCode() : null);
            change.setContent(JsonUtils.toJson(content));
            change.setType(buildType(change.getType()));
        }
        return buildTmcOrderMessage(change);
    }

    private static ConsumerType buildConsumerType(TMCType type) {
        switch (type) {
            case DIRECT_UPDATE_MEMO:
                return ConsumerType.DIRECT_UPDATE_MEMO;
            case DIRECT_UPDATE_TRADE_STATUS:
                return ConsumerType.DIRECT_UPDATE_TRADE_STATUS;
            case DIRECT_UPDATE_REFUND_STATUS:
                return ConsumerType.DIRECT_UPDATE_REFUND_STATUS;
            default:
                return ConsumerType.NORMAL;
        }
    }

    private static TMCType buildType(TMCType type) {
        /**
         * 转换的原因接口EshopOrderFillTmcDirectInfoFeature，已经定义为DIRECT_UPDATE_ORDER 来作为判断是否要执行tmc或者手工下载判断是否取本次下载值更改
         */
        switch (type) {
            case DIRECT_UPDATE_REFUND_STATUS:
            case DIRECT_UPDATE_TRADE_STATUS:
            case DIRECT_UPDATE_MEMO:
                return TMCType.DIRECT_UPDATE_ORDER;
            default:
                return type;
        }
    }

    public static void sendMessage(EshopNotifyChange change, EshopInfo eshopInfo) {
        sendMessage(change, eshopInfo.getProfileId(), eshopInfo.getOtypeId());
    }

    public static void sendMessage(EshopNotifyChange change, BigInteger profileId, BigInteger eshopId) {
        change.setProfileId(profileId);
        change.setEshopId(eshopId);
        change.setId(UId.newId());
        change.setCreateTime(new Date());
        change.setUpdateTime(new Date());
        if (change.getUniqueId() == null) {
            change.setUniqueId(change.generateUniqueId());
        }
        saveNotifyChange(change);
        doWriteTmcMqLog(change, "");
        TmcOrderMessage message = buildMessage(change);
        doSendMessage(message, change.getType());
    }

    public static void saveNotifyChange(EshopNotifyChange change) {
        try {
            if (change.getUniqueId() == null) {
                change.setUniqueId(change.generateUniqueId());
            }
            //后续pl_eshop_notify_change 只用于存卖家备注、状态变更、发票等信息。
            if (change.getType() != TMCType.Order
                    && change.getType() != TMCType.RefundOrder
                    && change.getType() != TMCType.Ptype) {
                TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
                tmcMapper.insertMessageChange(change);
            }
        } catch (Exception ex) {
            sysLogger.error("账套ID:{},店铺ID:{},保存EshopNotifyChange消息异常,错误信息:{},change:{}",
                    change.getProfileId(), change.getEshopId(), ex.getMessage(), JsonUtils.toJson(change), ex);
        }
    }

    public static void doWriteTmcMqLog(InvokeMessageEntity invokeMessage,String tmcId, String method) {
        try {
            EshopTmcMessageLog tmcMessageLog = TmcLogUtil.buildTmcLog(invokeMessage, method, "");
            tmcMessageLog.setTmcId(tmcId);
            LogService.addNow(tmcMessageLog);
        } catch (Exception ex) {
            sysLogger.error("账套ID:{},店铺ID:{},保存tmc消息到pl_eshop_tmc_log表出错,错误信息:{},invokeMessage:{}",
                    invokeMessage.getProfileId(), invokeMessage.getEshopId(), ex.getMessage(), JsonUtils.toJson(invokeMessage), ex);
        }
    }

    public static void doWriteTmcMqLog(EshopNotifyChange notifyChange, String method) {
        try {
            EshopTmcMessageLog tmcMessageLog = TmcLogUtil.buildTmcLog(notifyChange, method, "");
            LogService.addNow(tmcMessageLog);
        } catch (Exception ex) {
            sysLogger.error("账套ID:{},店铺ID:{},保存tmc消息到pl_eshop_tmc_log表出错,错误信息:{},invokeMessage:{}",
                    notifyChange.getProfileId(), notifyChange.getEshopId(), ex.getMessage(), JsonUtils.toJson(notifyChange), ex);
        }
    }

    public static void doSendMessage(TmcOrderMessage message, TMCType type) {
        EshopTmcConfig config = GetBeanUtil.getBean(EshopTmcConfig.class);
        if (TMCType.RefundOrder == type) {
            message.setTag("tmcRefund");
        }
        sysLogger.info("发送消息到mq：{}", JsonUtils.toJson(message));
        String tmcMqTopic = config.getTmcMqTopic();
        try {
            if (CollectionUtils.isNotEmpty(config.getTmcSpecificTopicList())) {
                for (String shopCode : config.getTmcSpecificTopicList()) {
                    if (shopCode.equals(String.valueOf(message.getShopCode()))) {
                        tmcMqTopic = config.getTmcMqSpecialTopic();
                    }
                }
            }
        } catch (Exception e) {
            sysLogger.error("获取tmc特定topic报错：{}", e.getMessage(), e);
        }
        List<MqSendResult<TmcOrderMessage>> results = SysMqSend.send(Collections.singletonList(message), tmcMqTopic);
        insertMqConsumeLogByMqResults(results, message, type);

    }


    /**
     * @param message TmcOrderMessage
     * @param type    TMCType
     * @param delay   线上单位:ms
     */
    public static void doSendMessage(TmcOrderMessage message, TMCType type, int delay) {
        EshopTmcConfig config = GetBeanUtil.getBean(EshopTmcConfig.class);
        if (TMCType.RefundOrder == type) {
            message.setTag("tmcRefund");
        }
        sysLogger.info("发送消息到mq：{}", JsonUtils.toJson(message));
        String tmcMqTopic = config.getTmcMqTopic();
        try {
            if (CollectionUtils.isNotEmpty(config.getTmcSpecificTopicList())) {
                for (String shopCode : config.getTmcSpecificTopicList()) {
                    if (shopCode.equals(String.valueOf(message.getShopCode()))) {
                        tmcMqTopic = config.getTmcMqSpecialTopic();
                    }
                }
            }
        } catch (Exception e) {
            sysLogger.error("获取tmc特定topic报错：{}", e.getMessage(), e);
        }
        List<MqSendResult<TmcOrderMessage>> results = SysMqSend.sendDelay(Collections.singletonList(message), tmcMqTopic, delay);
        insertMqConsumeLogByMqResults(results, message, type);
    }

    public static <T extends TmcBaseMessage> void insertMqConsumeLogByMqResults(List<MqSendResult<T>> results, TmcOrderMessage message, TMCType type) {
        MqConsumeLog log = initMqConsumeLog(message);
        log.setBody(JsonUtils.toJson(message));
        log.setMsgType(type);
        log.setTmcId(message.getTradeId());
        if (CollectionUtils.isEmpty(results)) {
            log.setState(4);
            log.setErrorMsg("MQ发送发送失败，返回结果为空，原因未知");
            LogService.update(log);
            return;
        }
        for (MqSendResult<T> mqSendResult : results) {
            log.setMsgId(mqSendResult.getMessageId());
            if (!mqSendResult.isSuccess()) {
                log.setErrorMsg(String.format("MQ发送失败，失败原因：%s", mqSendResult.getErrorMsg()));
            }
            LogService.update(log);
        }
    }

    public static MqConsumeLog initMqConsumeLog(TmcBaseMessage message) {
        MqConsumeLog log = new MqConsumeLog();
        log.setId(message.getMessageId());
        log.setEshopId(message.getEshopId());
        log.setProfileId(message.getProfileId());
        log.setState(0);
        log.setEtypeId(BigInteger.ZERO);
        log.setConsumeTimes(1);
        log.setMsgReceiveTime(message.getCreateTime());
        return log;
    }
    public static TmcOrderMessage buildTmcOrderMessage(EshopNotifyChange change) {
        saveNotifyChange(change);
        doWriteTmcMqLog(change,"");
        return buildMessage(change);
    }

    public static TmcOrderMessage getTmcOrderMessage(String shopAccount, EshopNotifyChange change, int shopType) {
        if (change.getEshopId() == null || change.getProfileId() == null) {
            EshopRegisterNotify notify = buildNotify(shopAccount, shopType);
            change.setId(UId.newId());
            change.setProfileId(notify.getProfileId());
            change.setEshopId(notify.getId());
            change.setShopType(notify.getType());
        }
        return buildTmcOrderMessage(change);
    }

    private static TmcProductMessage getTmcProductMessage(String shopAccount, EshopNotifyChange change, int shopType) {
        EshopRegisterNotify notify = buildNotify(shopAccount, shopType);
        change.setProfileId(notify.getProfileId());
        change.setEshopId(notify.getId());
        TmcProductMapper tmcMapper = GetBeanUtil.getBean(TmcProductMapper.class);
        tmcMapper.insertProductMessageChange(change);
        return buildProductMessage(change);
    }

    public static EshopRegisterNotify buildNotify(String shopAccount, int shopType) {
        try {
            List<EshopRegisterNotify> notifyList = doBuildNotify(shopAccount, shopType);
            if (Objects.isNull(notifyList) || notifyList.size() == 0) {
                sysLogger.error("未找到订阅关系:{}", shopAccount);
                return null;
            }
            BigInteger profileId = CurrentUser.getProfileId();
            if (profileId != null && profileId.compareTo(BigInteger.ZERO) > 0) {
                List<EshopRegisterNotify> collect = notifyList.stream().filter(x -> x.getProfileId().compareTo(profileId) == 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    return collect.get(collect.size() - 1);
                }
            }
            return notifyList.get(notifyList.size() - 1);
        } catch (Exception ex) {
            sysLogger.error("获取账套信息错误：{}", ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }
    }

    private static List<EshopRegisterNotify> doBuildNotify(String shopAccount, int shopType) {
        return PlatformProfileMappingUtils.getRegisterNotifyList(shopAccount, ShopType.valueOf(shopType));
    }

    public static EshopRegisterNotify buildNotifies(String shopAccount, List<Integer> shopTypes) {
        try {
            List<EshopRegisterNotify> notifies = new ArrayList<>();
            BigInteger profileId = CurrentUser.getProfileId();
            for (Integer shopType : shopTypes) {
                List<EshopRegisterNotify> eshopRegisterNotifies = doBuildNotify(shopAccount, shopType);
                if (CollectionUtils.isNotEmpty(eshopRegisterNotifies)) {
                    notifies.addAll(eshopRegisterNotifies);
                }
            }
            if (CollectionUtils.isEmpty(notifies)) {
                sysLogger.error("未找到订阅关系:{}", shopAccount);
                return null;
            }
            if (profileId != null && profileId.compareTo(BigInteger.ZERO) > 0) {
                List<EshopRegisterNotify> filterNotifies = notifies.stream().filter(x -> x.getProfileId().compareTo(profileId) == 0).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterNotifies)) {
                    sysLogger.error("未找到订阅关系:{}", shopAccount);
                    return null;
                }
                return filterNotifies.get(filterNotifies.size() - 1);
            }
            return notifies.get(notifies.size() - 1);
        } catch (Exception ex) {
            sysLogger.error("获取账套信息错误：{}", ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }
    }

    public static TmcOrderMessage buildMessage(EshopNotifyChange change) {
        TmcOrderMessage message = new TmcOrderMessage();
        message.setTradeId(change.getTradeOrderId());
        message.setEshopId(change.getEshopId());
        message.setProfileId(change.getProfileId());
        message.setCreateTime(new Date());
        message.setTradeStatus(change.getTradeStatus());
        message.setNotifyChangeId(change.getId());
        message.setRefundId(change.getRefundOrderId());
        message.setDesiredTradeModifiedTime(change.getDesiredTradeModifiedTime());
        message.setOnlineShopId(change.getOnlineShopId());
        message.setPlatformMsgType(change.getPlatformMsgType());
        return message;
    }

    private static TmcProductMessage buildProductMessage(EshopNotifyChange change) {
        TmcProductMessage message = new TmcProductMessage();
        message.setEshopId(change.getEshopId());
        message.setProfileId(change.getProfileId());
        message.setCreateTime(new Date());
        message.setType(change.getProductType());
        message.setNumId(change.getNumId());
        message.setUnitName(change.getUnitName());
        message.setBarCode(change.getBarCode());
        message.setApplyId(change.getApplyId());
        message.setProductUniqueId(change.getProductUniqueId());
        message.setProductUnitCode(change.getProductUnitCode());
        message.setMappingType(change.getMappingType());
        return message;
    }

    public static EshopInfo getEshopByShopAccount(BigInteger profileId, String shopAccount) {
        sysLogger.info("开始获取TmcMapper");
        EshopInfoMapper tmcMapper = GetBeanUtil.getBean(EshopInfoMapper.class);
        sysLogger.info("开始保存tmc消息到数据库");
        return tmcMapper.queryEshopInfoByShopaccount(profileId, shopAccount);
    }

    public static void doModifyTmcMqConsumeState(TmcOrderMessage message, Date executeTime, EshopSaleOrderDownloadResponse response) {
        MqConsumeLog log = new MqConsumeLog();
        log.setId(message.getMessageId());
        log.setProfileId(message.getProfileId());
        log.setState(response.isSuccess() ? 1 : 2);
        log.setErrorMsg(response.getMessage());
        log.setConsumeStartTime(executeTime);
        log.setConsumeEndTime(DateUtils.getDate());
        LogService.update(log);
    }
}
