package com.wsgjp.ct.sale.tool.tmc.impl.alijiankang;

import com.wsgjp.ct.common.enums.core.enums.tmc.TmcNotifyResponseEnum;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcRefundMsgMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.AddressChangeMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.dto.AliJianKangOrderDto;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class AliJianKangModifyAddressHandler extends AliJianKangNotifyBase implements MessageHandler {
    private final EshopTmcRefundMsgMapper tmcRefundMsgMapper;
    private final TmcNotifyProxy notifyProxy;

    public AliJianKangModifyAddressHandler(EshopTmcRefundMsgMapper tmcRefundMsgMapper, TmcNotifyProxy notifyProxy) {
        this.tmcRefundMsgMapper = tmcRefundMsgMapper;
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        String tmMessage = invokeMessage.getMessage();
        AliJianKangOrderDto apiOrder;
        try {
            apiOrder = JsonUtils.toObject(tmMessage, AliJianKangOrderDto.class);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成AliJianKangOrderDto实体出错，错误信息：{},tmMessage:{}", shopTypeName, e.getMessage(), tmMessage);
            return buildModifyAddressInvokeResult(false, "isv系统转换消息失败", "ZFSCM_MODIFY_ADDRESS_CHECK_FAIL");
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiOrder.getSupplier_id(), invokeMessage.getShopType().getCode());
        if (Objects.isNull(eshopRegister)) {
            LOGGER.error("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, apiOrder.getSupplier_id(), tmMessage);
            return buildModifyAddressInvokeResult(false, "isv系统内未找到对应的注册账号信息", "ZFSCM_MODIFY_ADDRESS_CHECK_FAIL");
        }
        try {
            TmcInvokeRequest tmcInvokeRequest = new TmcInvokeRequest();
            AddressChangeMessage changeMessage = new AddressChangeMessage();
            tmcInvokeRequest.setTradeId(apiOrder.getMain_ship_order_id());
            tmcInvokeRequest.setMessage(JsonUtils.toJson(changeMessage));
            tmcInvokeRequest.setMethod(TmcNotifyMethodEnum.MODIFY_ADDRESS_NOTIFY);
            tmcInvokeRequest.setEshopId(eshopRegister.getId());
            tmcInvokeRequest.setShopType(invokeMessage.getShopType());
            tmcInvokeRequest.setProfileId(eshopRegister.getProfileId());
            TmcInvokeResponse resp = notifyProxy.execute(tmcInvokeRequest);
            boolean success = TmcNotifyResponseEnum.SUCCESS.getCode().equals(resp.getCode()) || TmcNotifyResponseEnum.SALE_ORDER_ENTER_ERP.getCode().equals(resp.getCode())
                    || TmcNotifyResponseEnum.SALE_ORDER_NO_ENTER_ERP.getCode().equals(resp.getCode()) || TmcNotifyResponseEnum.NO_SUBMIT.getCode().equals(resp.getCode())
                    || TmcNotifyResponseEnum.LOGISTICS_INVOICE_NOT_FIND.getCode().equals(resp.getCode());
            if (success) {
                return buildModifyAddressInvokeResult(true, "", "");
            } else {
                return buildModifyAddressInvokeResult(false, resp.getMessage(), "ZFSCM_MODIFY_ADDRESS_CHECK_FAIL");
            }
        } catch (Exception ex) {
            LOGGER.error("{}处理阿里健康修改地址消息失败,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", shopTypeName, eshopRegister.getProfileId(), eshopRegister.getId(), tmMessage, ex.getMessage(), ex);
            return buildModifyAddressInvokeResult(false, ex.getMessage(), "ZFSCM_MODIFY_ADDRESS_CHECK_FAIL");
        }
    }

    @Override
    public String serviceName() {
        return "qimen.alibaba.alihealth.shiporder.modifyaddress.check";
    }
}
