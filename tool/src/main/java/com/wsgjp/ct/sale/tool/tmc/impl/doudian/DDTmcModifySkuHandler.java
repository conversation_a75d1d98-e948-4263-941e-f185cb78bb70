package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.DDSPIModifySku;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.pdd.OrderPromiseHandler;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class DDTmcModifySkuHandler extends DDNotifyBase implements MessageHandler {

    private static final Logger sysLogger = LoggerFactory.getLogger(OrderPromiseHandler.class);

    private final TmcNotifyProxy notifyProxy;

    public DDTmcModifySkuHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String message = invokeMessage.getMessage();
        if(StringUtils.isEmpty(message)){
            sysLogger.info("抖店消息为空");
            //这里需要返回错误
            return JsonUtils.toJson(new GeneralResult(500L,"消息为空",null));
        }
        DDSPIModifySku ddspiModifySku = JsonUtils.toObject(message, DDSPIModifySku.class);
        if(ddspiModifySku == null){
            return JsonUtils.toJson(new GeneralResult(500L,"序列化错误",null));
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(ddspiModifySku.getShop_id(), invokeMessage.getShopType().getCode());
        TmcInvokeRequest invokeRequest = new TmcInvokeRequest();
        invokeRequest.setProfileId(invokeMessage.getProfileId());
        invokeRequest.setShopType(invokeMessage.getShopType());
        invokeRequest.setEshopId(eshopRegister.getId());
        invokeRequest.setTradeId(ddspiModifySku.getShop_order_id());
        invokeRequest.setMethod(TmcNotifyMethodEnum.MODIFY_SKU);
        invokeRequest.setMessage(invokeMessage.getMessage());
        TmcInvokeResponse resp = notifyProxy.execute(invokeRequest);
        try {
            EshopNotifyChange change = new EshopNotifyChange();
            change.setContent("");
            change.setTradeOrderId(ddspiModifySku.getShop_order_id());
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(ddspiModifySku.getShop_id());
            SupportUtil.doOrderNotify(change.getOnlineShopId(), change,  invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.Doudian.getCode());
        }catch (Exception e){

        }
        if(resp.isError()){
            return JsonUtils.toJson(new GeneralResult(200006,resp.getMessage(),null));
        }else{
            return JsonUtils.toJson(new GeneralResult(0L,"success",null));
        }

    }

    @Override
    public String serviceName() {
        return "doudian.modify.sku";
    }
}
