package com.wsgjp.ct.sale.tool.tmc.impl.meituands;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.meituands.entity.MeiTuanDsProductData;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.util.Date;
import java.util.Objects;

@Component
/**
 * <AUTHOR>
 **/
public class MeiTuanDsProductHandler extends MeiTuanDsNotifyBase{

    private static final Logger log = LoggerFactory.getLogger(MeiTuanDsProductHandler.class);

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        log.info("======{}进入invoker方法======", shopTypeName);
        String tmcMessage = invokeMessage.getMessage();
        MeiTuanDsProductData data;
        try {
            data = JsonUtils.toObject(tmcMessage, MeiTuanDsProductData.class);
        } catch (Exception e) {
            log.error(String.format("%stmc消息转换成美团电商商品实体失败",shopTypeName),e);
            return buildResponse(e.getMessage());
        }
        try {
            EshopRegisterNotify notify = SupportUtil.buildNotify(data.getPoiId(), invokeMessage.getShopType().getCode());
            if (Objects.isNull(notify)) {
                log.info(String.format("账套id:%s,店铺类型:%s,poiId:%s,查询店铺信息为空!tmcMessage:%s", invokeMessage.getProfileId(), shopTypeName, data.getPoiId(), tmcMessage));
                return buildResponse("管家婆未找到对应店铺!");
            }
            log.info(String.format("账套id:%s,网店id:%s,店铺类型：%s,tmcMessage:%s",notify.getProfileId(),notify.getId(),shopTypeName,tmcMessage));
            EshopNotifyChange change = new EshopNotifyChange();
            change.setContent(invokeMessage.getMessage());
            change.setNumId(String.valueOf(data.getPoiId()));
            change.setId(UId.newId());
            change.setType(TMCType.Ptype);
            change.setOnlineShopId(data.getPoiId());
            change.setCreateTime(new Date());
            SupportUtil.doNotify(change.getOnlineShopId(), change, ShopType.MeiTuan.getCode());
            return buildResponse("ok");
        } catch (Exception e) {
            String errMsg = e.getMessage();
            errMsg = StringUtils.isEmpty(errMsg) ? "tmc商品消息构建异常" : errMsg;
            log.error(String.format("账套id:%s,网店id:%s,店铺类型：%s,tmcMessage:%s",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName,tmcMessage),e);
            return buildResponse(errMsg);
        }
    }

    @Override
    public String serviceName() {
        return "productMessage";
    }

}
