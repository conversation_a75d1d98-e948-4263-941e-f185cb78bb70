package com.wsgjp.ct.sale.tool.tmc.impl.aikucun;

import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.AikucunTmcMessageResponse;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class AikucunTmcOrderHandler implements MessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(AikucunTmcOrderHandler.class);
    private static final String ACCESS_TOKEN = "accessToken";

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String message = invokeMessage.getMessage();
        AikucunTmcOrderChangeEntity entity;
        if (!invokeMessage.getParams().containsKey(ACCESS_TOKEN)) {
            return buildAikucunResponse(false, "C9001", "解析accessToken失败,无法匹配对应店铺");
        }
        try {
            entity = JsonUtils.toObject(message, AikucunTmcOrderChangeEntity.class);
        } catch (Exception ex) {
            logger.error("爱库存消息数据转换成OrderRequest实体出错，错误信息：{}", ex.getMessage(), ex);
            return buildAikucunResponse(false, "C9002", "系统异常");
        }


        if (entity == null || StringUtils.isEmpty(entity.getOrderNo())) {
            return buildAikucunResponse(false, "C9002", "系统异常");
        }


        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(invokeMessage.getMessage());
        change.setTradeOrderId(entity.getOrderNo());
        change.setId(UId.newId());
        change.setType(TMCType.Order);
        change.setOnlineShopId(invokeMessage.getParams().get(ACCESS_TOKEN));
        change.setCreateTime(new Date());
        try {
            SupportUtil.doNotify(change.getOnlineShopId(), change, invokeMessage.getShopType().getCode());
        } catch (Exception e) {
            logger.error("爱库存,调用业务处理订单消息失败,错误信息:{}", e.getMessage(), e);
            return buildAikucunResponse(false, "500", e.getMessage());
        }
        return buildAikucunResponse(true, "00000", "success");
    }

    private String buildAikucunResponse(boolean success, String code, String message) {
        AikucunTmcMessageResponse response = new AikucunTmcMessageResponse(success, code, message);
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "mengxiang_trade_orderCreate";
    }

    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        return null;
    }
}
