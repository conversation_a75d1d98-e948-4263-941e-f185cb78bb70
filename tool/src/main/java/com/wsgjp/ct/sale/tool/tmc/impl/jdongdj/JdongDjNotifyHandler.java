package com.wsgjp.ct.sale.tool.tmc.impl.jdongdj;

import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 */
@Component
public class JdongDjNotifyHandler extends JdongDjNotifyBase {
    private static final String API_EX = "api param exception!";
    private static final Logger LOGGER = LoggerFactory.getLogger(JdongDjNotifyHandler.class);
    private final EshopTmcUtils eshopTmcUtils;

    public JdongDjNotifyHandler(EshopTmcUtils eshopTmcUtils) {
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        if (invokeMessage.getEshopId() == null) {
            return JdongDjUtils.buildResponse(JdongDjErrorCode.Fail, "not query shop mapping!");
        }
        EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), invokeMessage.getEshopId());
        if (Objects.isNull(eshopInfo)) {
            return JdongDjUtils.buildResponse(JdongDjErrorCode.Fail, "not query shop info!");
        }
        JdongDjBaseMessage jdongDjMessage = JsonUtils.toObject(invokeMessage.getMessage(), JdongDjBaseMessage.class);
        if (jdongDjMessage == null || jdongDjMessage.getJdParamJson() == null) {
            return JdongDjUtils.buildResponse(JdongDjErrorCode.API_PARAM_EXCEPTION, API_EX);
        }
        if (StringUtils.isBlank(jdongDjMessage.getOnlineShopId())) {
            LOGGER.error("账套ID:{},店铺ID：{},京东秒送onlineEshopId为空，原始报文：{}"
                    , invokeMessage.getProfileId(), invokeMessage.getEshopId(), invokeMessage.getMessage());
            return JdongDjUtils.buildResponse(JdongDjErrorCode.API_PARAM_EXCEPTION, API_EX);
        }
        if (StringUtils.isBlank(jdongDjMessage.getTradeId())) {
            LOGGER.error("账套ID:{},店铺ID：{},onlineEshopId:{},京东秒送订单号为空，原始报文：{}"
                    , invokeMessage.getProfileId(), invokeMessage.getEshopId(), jdongDjMessage.getOnlineShopId(), invokeMessage.getMessage());
            return JdongDjUtils.buildResponse(JdongDjErrorCode.API_PARAM_EXCEPTION, API_EX);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setTradeOrderId(jdongDjMessage.getTradeId());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(invokeMessage.getMessage());
        SupportUtil.sendMessage(eshopNotifyChange, eshopInfo);
        return JdongDjUtils.buildResponse(JdongDjErrorCode.SUCCESS, "success");
    }

    @Override
    public String serviceName() {
        return "jdongdjInvoker";
    }
}
