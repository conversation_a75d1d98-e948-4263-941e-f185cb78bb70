package com.wsgjp.ct.sale.tool.tmc.impl.weimobec.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class WeiMobMessage {
    private String id;
    private String topic;
    private String event;
    @JsonProperty("public_account_id")
    private String publicAccountId;
    private String bosId;
    //微盟1.0的消息体
    private String msg_body;
    //微盟2.0的消息体

    private String msgBody;

    private WeiMobMessageBody msgBodyObj;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getPublicAccountId() {
        return publicAccountId;
    }

    public void setPublicAccountId(String publicAccountId) {
        this.publicAccountId = publicAccountId;
    }

    public String getMsgBody() {
        return msgBody;
    }

    public void setMsgBody(String msgBody) {
        this.msgBody = msgBody;
    }

    public WeiMobMessageBody getMsgBodyObj() {
        if (msgBodyObj == null) {
            if (StringUtils.isNotBlank(msg_body)) {
                msgBodyObj = JsonUtils.toObject(msg_body, WeiMobMessageBody.class);
            } else if (StringUtils.isNotBlank(msgBody)) {
                msgBodyObj = JsonUtils.toObject(msgBody, WeiMobMessageBody.class);
            }
        }
        return msgBodyObj;
    }

    public void setMsgBodyObj(WeiMobMessageBody msgBodyObj) {
        this.msgBodyObj = msgBodyObj;
    }

    public String getBosId() {
        return bosId;
    }

    public void setBosId(String bosId) {
        this.bosId = bosId;
    }

    public String getMsg_body() {
        return msg_body;
    }

    public void setMsg_body(String msg_body) {
        this.msg_body = msg_body;
    }
}
