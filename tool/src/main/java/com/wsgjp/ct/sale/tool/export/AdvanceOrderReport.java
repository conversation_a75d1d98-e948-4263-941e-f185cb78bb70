package com.wsgjp.ct.sale.tool.export;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.ngp.export.core.config.NgpExportConfig;
import com.wsgjp.ct.ngp.export.core.entity.ExportTask;
import com.wsgjp.ct.ngp.export.core.entity.ReportHeader;
import com.wsgjp.ct.ngp.export.sdk.BaseReport;
import com.wsgjp.ct.ngp.export.sdk.api.ExportServiceCenterApi;
import com.wsgjp.ct.ngp.export.sdk.consts.DecryptConst;
import com.wsgjp.ct.ngp.export.sdk.service.NgpExportEshopService;
import com.wsgjp.ct.sale.biz.eshoporder.config.ExportConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.RefundConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderPlaform;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderPlatformExtend;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryAdvanceOrderParameter;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopSaleOrderExtendMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopAdvanceSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.service.receiver.EshopBuyerService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopOrderEshopRefundService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR> ql
 * @date 2020/7/06 14:17
 */
@Service
@ConditionalOnProperty(value = "eshoporder-tool-export.enabled", havingValue = "true")
public class AdvanceOrderReport extends BaseReport<QueryAdvanceOrderParameter, EshopAdvanceOrderEntity> {
    private final EshopAdvanceSaleOrderService advanceSaleOrderService;
    private final EshopSaleOrderExtendMapper eshopSaleOrderExtendMapper;
    private final EshopOrderEshopRefundService refundService;
    private final EshopBuyerService buyerService;
    private final ExportConfig exportEonfig;
    private static final Logger logger = LoggerFactory.getLogger(AdvanceOrderReport.class);

    protected AdvanceOrderReport(ExportServiceCenterApi exportServiceCenterApi, NgpExportConfig config, EshopAdvanceSaleOrderService advanceSaleOrderService, EshopSaleOrderExtendMapper eshopSaleOrderExtendMapper, EshopOrderEshopRefundService refundService, NgpExportEshopService ngpExportEshopService, EshopBuyerService buyerService, ExportConfig exportEonfig) {
        super(exportServiceCenterApi, config, ngpExportEshopService);
        this.advanceSaleOrderService = advanceSaleOrderService;
        this.eshopSaleOrderExtendMapper = eshopSaleOrderExtendMapper;
        this.refundService = refundService;
        this.buyerService = buyerService;
        this.exportEonfig = exportEonfig;
    }

    @Override
    public Class<QueryAdvanceOrderParameter> getQueryParamsType() {
        return QueryAdvanceOrderParameter.class;
    }

    @Override
    public Class<EshopAdvanceOrderEntity> getResultType() {
        return EshopAdvanceOrderEntity.class;
    }

    @Override
    public boolean isNeedSum() {
        return true;
    }

    @Override
    public String[] getSumField() {
        String[] getSumField = new String[11];
        getSumField[0] = "disedTaxedTotal";
        getSumField[1] = "taxTotal";
        getSumField[2] = "sellerPreferentialTotal";
        getSumField[3] = "platformPreferentialTotal";
        getSumField[4] = "customerPayment";
        getSumField[5] = "buyerFreightFee";
        getSumField[6] = "serviceFee";
        getSumField[7] = "buyerTradeTotal";
        getSumField[8] = "buyerPaidTotal";
        getSumField[9] = "buyerUnpaidTotal";
        getSumField[10] = "disTaxedTotalCaption";
        getSumField[11] = "platform.anchorPtypePreferentialTotal";
        getSumField[12] = "platform.anchorOrderPreferentialTotal";
        getSumField[13] = "platform.platformPtypeSubsidyTotal";
        getSumField[14] = "platform.platformOrderSubsidyTotal";
        return getSumField;
    }

    @Override
    public String getJobName() {
        return "advance-order";
    }

    @Override
    protected boolean needPlatformDecrypt() {
        return false;
    }

    @Override
    public void onComplete(ExportTask task, QueryAdvanceOrderParameter queryParams, List<EshopAdvanceOrderEntity> dataList) {
        super.onComplete(task, queryParams, dataList);
        aboutExportExtend(dataList);
    }

    public void aboutExportExtend(List<EshopAdvanceOrderEntity> dataList) {
        try {
            for (EshopAdvanceOrderEntity en : dataList) {
                EshopAdvanceOrderPlaform eshopSaleOrderExtendEntity = eshopSaleOrderExtendMapper.queryAdvanceByProfileAndVchcode(en.getProfileId(), en.getVchcode());
                if (null != eshopSaleOrderExtendEntity) {
                    eshopSaleOrderExtendMapper.updateAdvanceExportCountByProfileAndVchcode(en.getProfileId(), en.getVchcode(), eshopSaleOrderExtendEntity.getExportCount() + 1);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<EshopAdvanceOrderEntity> export(QueryAdvanceOrderParameter queryOrderParameter, ReportHeader reportHeader, String s, long l) throws Exception {
        int pageSize = 30000;
        PageRequest<QueryAdvanceOrderParameter> request = new PageRequest<>();
        request.setPageIndex(0);
        request.setPageSize(pageSize);
        request.setQueryParams(queryOrderParameter);
        List<EshopAdvanceOrderEntity> resultList = null;
        int exported = 0;
        PageResponse<EshopAdvanceOrderEntity> response = new PageResponse<>();
        do {
            List<EshopAdvanceOrderEntity> entityList = doExportDataQuery(request);
            response.setTotal(entityList == null ? 0 : entityList.size());
            response.setPageIndex(request.getPageIndex());
            response.setPageSize(request.getPageSize());
            response.setList(entityList);
            if (resultList == null) {
                if (response.getTotal() >= pageSize) {
                    resultList = new ArrayList<>(pageSize);
                } else {
                    resultList = response.getList();
                }
            } else {
                resultList.addAll(response.getList());
            }
            exported += response.getList().size();
            updateProgress(exported, (int) response.getTotal());
        }
        while (!response.getList().isEmpty() && exported < response.getTotal());

        return resultList;
    }

    @Override
    public Map<String, String> JdEncryptSensitiveFieldMapping() {
        StringJoiner joiner = new StringJoiner(",");
        joiner.add(DecryptConst.RECEIVER_ACCOUNT);
        joiner.add(DecryptConst.RECEIVER_NAME);
        joiner.add(DecryptConst.RECEIVER_MOBILE);
        joiner.add(DecryptConst.RECEIVER_TELEPHONE);
        joiner.add(DecryptConst.RECEIVER_PROVINCE);
        joiner.add(DecryptConst.RECEIVER_CITY);
        joiner.add(DecryptConst.RECEIVER_DISTRICT);
        joiner.add(DecryptConst.RECEIVER_ADDRESS);
        Map<String,String> map = new HashMap<String, String>();
        map.put("eshopBuyer.fullBuyerInfo", joiner.toString());
        map.put("eshopBuyer.customerShopAccount", DecryptConst.RECEIVER_ACCOUNT);
        map.put("extend.platformExtend.selfPickUpInfo.fullBuyerInfo", joiner.toString());
        map.put("extend.platformExtend.realBuyer.fullBuyerInfo", joiner.toString());
        return map;
    }

    private List<EshopAdvanceOrderEntity> doExportDataQuery(PageRequest<QueryAdvanceOrderParameter> request) {
        List<EshopAdvanceOrderEntity> entityList = new ArrayList<>();
        PageResponse<EshopAdvanceOrderEntity> response = advanceSaleOrderService.queryAdvanceOrders(request);
        if (response == null || CollectionUtils.isEmpty(response.getList())) {
            return entityList;
        }
        entityList = response.getList();
        entityList.forEach(e->e.getPlatform().getPlatformExtend().setPeriodFrequency(e.getPlatform().getPlatformExtend().getPeriodFrequency()));
        CommonUtil.transOrderTagForReportForAdvance(entityList);
        DoSaleOrderBantchDecrypt(entityList);
        return entityList;
    }

    @Override
    public Integer getExportCount() {
        return exportEonfig.getExportCount();
    }

    @Override
    public boolean useNewExport() {
        return true;
    }

    @Override
    public List<EshopAdvanceOrderEntity> limitExport(PageRequest<QueryAdvanceOrderParameter> queryParams, ReportHeader header, String messageId, long timespan) {
        logger.info(String.format("advanceorderlimitExport profileId:%s messageId:%s  params:%s header:%s", CurrentUser.getProfileId(), messageId
                , JsonUtils.toJson(queryParams), JsonUtils.toJson(header)));
        return this.doExportDataQuery(queryParams);
    }

    private void DoSaleOrderBantchDecrypt(List<EshopAdvanceOrderEntity> advanceEntityList) {
        if (null == advanceEntityList || advanceEntityList.isEmpty()) {
            return ;
        }
        List<BigInteger> buyeridList = new ArrayList<>();
        List<BigInteger> pickUpidList = new ArrayList<>();
        List<BigInteger> realBuyeridList = new ArrayList<>();
        advanceEntityList.forEach(o->{
            o.getEshopBuyer().setFullBuyerInfo(o.getEshopBuyer().getFullBuyerInfo());
            if (null == o.getPlatform().getPlatformExtend()){
                EshopAdvanceOrderPlatformExtend platformExtend = new EshopAdvanceOrderPlatformExtend();
                platformExtend.setSelfPickUpInfo(new EshopBuyer());
                platformExtend.setRealBuyer(new EshopBuyer());
                o.getPlatform().setPlatformExtend(platformExtend);
            }
            if (null == o.getPlatform().getPlatformExtend().getSelfPickUpInfo()){
                o.getPlatform().getPlatformExtend().setSelfPickUpInfo(new EshopBuyer());
            }
            if (null == o.getPlatform().getPlatformExtend().getRealBuyer()){
                o.getPlatform().getPlatformExtend().setRealBuyer(new EshopBuyer());
            }
            o.getPlatform().getPlatformExtend().getSelfPickUpInfo().setFullBuyerInfo(o.getPlatform().getPlatformExtend().getSelfPickUpInfo().getFullBuyerInfo());
            o.getPlatform().getPlatformExtend().getRealBuyer().setFullBuyerInfo(o.getPlatform().getPlatformExtend().getRealBuyer().getFullBuyerInfo());
            o.setExportState(o.getPlatform().getExportCount() == 0 ? RefundConstantEnum.NO_EXPORT.getName() : RefundConstantEnum.EXPORTED.getName());
            //系统加密，允许导出解密
            if (o.getEshopBuyer().getDi().contains("SYS:")){
                buyeridList.add(o.getBuyerId());
            }
            if (o.getPlatform().getPlatformExtend().getSelfPickUpInfo().getDi().contains("SYS:")){
                pickUpidList.add(o.getPlatform().getPlatformExtend().getPickUpAddressId());
            }
            if (o.getPlatform().getPlatformExtend().getRealBuyer().getDi().contains("SYS:")){
                realBuyeridList.add(o.getPlatform().getPlatformExtend().getRealBuyerId());
            }
            String platform = o.getShopType().name().toLowerCase();
            Boolean needDecryptByPlatform = exportEonfig.getNeedDecryptPlatform().get(platform);
            //平台加密需要读取配置
            if (needDecryptByPlatform != null && needDecryptByPlatform) {
                buyeridList.add(o.getBuyerId());
            }
        });
        if (CollectionUtils.isNotEmpty(buyeridList)){
            Map<BigInteger, EshopBuyer> buyerMap = buyerService.DoPageBatchDecrypt(buyeridList.stream().distinct().collect(Collectors.toList()));
            if(buyerMap != null && !buyerMap.isEmpty()) {
                for (EshopAdvanceOrderEntity order : advanceEntityList) {
                    BigInteger buyerId = order.getBuyerId();
                    if (!buyerMap.containsKey(buyerId)) {
                        continue;
                    }
                    EshopBuyer eshopBuyer = buyerMap.get(buyerId);
                    order.setEshopBuyer(eshopBuyer);
                    order.setExportState(order.getPlatform().getExportCount() == 0 ?
                            RefundConstantEnum.NO_EXPORT.getName() : RefundConstantEnum.EXPORTED.getName());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(pickUpidList)){
            Map<BigInteger, EshopBuyer> pickUpMap = buyerService.DoPageBatchDecrypt(pickUpidList.stream().distinct().collect(Collectors.toList()));
            if(pickUpMap != null && !pickUpMap.isEmpty()) {
                for (EshopAdvanceOrderEntity order : advanceEntityList) {
                    BigInteger pickUpAddressId = order.getPlatform().getPlatformExtend().getPickUpAddressId();
                    if (!pickUpMap.containsKey(pickUpAddressId)) {
                        continue;
                    }
                    EshopBuyer pickUpInfo = pickUpMap.get(pickUpAddressId);
                    order.getPlatform().getPlatformExtend().setSelfPickUpInfo(pickUpInfo);
                    order.setExportState(order.getPlatform().getExportCount()== 0 ?
                            RefundConstantEnum.NO_EXPORT.getName() : RefundConstantEnum.EXPORTED.getName());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(realBuyeridList)){
            Map<BigInteger, EshopBuyer> realBuyerMap = buyerService.DoPageBatchDecrypt(realBuyeridList.stream().distinct().collect(Collectors.toList()));
            if(realBuyerMap != null && !realBuyerMap.isEmpty()) {
                for (EshopAdvanceOrderEntity order : advanceEntityList) {
                    BigInteger realBuyerId = order.getPlatform().getPlatformExtend().getRealBuyerId();
                    if (!realBuyerMap.containsKey(realBuyerId)) {
                        continue;
                    }
                    EshopBuyer realBuyer = realBuyerMap.get(realBuyerId);
                    order.getPlatform().getPlatformExtend().setRealBuyer(realBuyer);
                    order.setExportState(order.getPlatform().getExportCount()== 0 ?
                            RefundConstantEnum.NO_EXPORT.getName() : RefundConstantEnum.EXPORTED.getName());
                }
            }
        }
    }

}
