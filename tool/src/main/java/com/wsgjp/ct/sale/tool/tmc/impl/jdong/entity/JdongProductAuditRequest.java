package com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity;

public class JdongProductAuditRequest extends JdongTmcRequest {
    /**
     * 供应商简码
     */
    private String venderCode;
    private String applyId;
    private String wareId;
    private String wareName;
    /**
     * 采销审核状态名称
     */
    private String applyStateName;
    /**
     * 采销审核状态码
     */
    private String applyStateCode;
    /**
     * 采销/经理审批时间
     */
    private String applyApproveTime;
    /**
     * 申请单驳回原因
     */
    private String applyReason;
    /**
     * 合规审核状态名称
     */
    private String keeperStateName;
    /**
     * 合规审核状态码
     */
    private String keeperStateCode;
    /**
     * 合规审批时间
     */
    private String keeperApproveTime;
    /**
     * 合规驳回原因
     */
    private String keeperReason;

    public String getVenderCode() {
        return venderCode;
    }

    public void setVenderCode(String venderCode) {
        this.venderCode = venderCode;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getWareId() {
        return wareId;
    }

    public void setWareId(String wareId) {
        this.wareId = wareId;
    }

    public String getWareName() {
        return wareName;
    }

    public void setWareName(String wareName) {
        this.wareName = wareName;
    }

    public String getApplyStateName() {
        return applyStateName;
    }

    public void setApplyStateName(String applyStateName) {
        this.applyStateName = applyStateName;
    }

    public String getApplyStateCode() {
        return applyStateCode;
    }

    public void setApplyStateCode(String applyStateCode) {
        this.applyStateCode = applyStateCode;
    }

    public String getApplyApproveTime() {
        return applyApproveTime;
    }

    public void setApplyApproveTime(String applyApproveTime) {
        this.applyApproveTime = applyApproveTime;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public String getKeeperStateName() {
        return keeperStateName;
    }

    public void setKeeperStateName(String keeperStateName) {
        this.keeperStateName = keeperStateName;
    }

    public String getKeeperStateCode() {
        return keeperStateCode;
    }

    public void setKeeperStateCode(String keeperStateCode) {
        this.keeperStateCode = keeperStateCode;
    }

    public String getKeeperApproveTime() {
        return keeperApproveTime;
    }

    public void setKeeperApproveTime(String keeperApproveTime) {
        this.keeperApproveTime = keeperApproveTime;
    }

    public String getKeeperReason() {
        return keeperReason;
    }

    public void setKeeperReason(String keeperReason) {
        this.keeperReason = keeperReason;
    }
}
