package com.wsgjp.ct.sale.tool.tmc.impl.jdong;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.exception.PlatformInterfaceException;
import com.wsgjp.ct.sale.platform.factory.jdong.JdongConfig;
import com.wsgjp.ct.sale.platform.factory.jdongQQD.JdongQQDConfig;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.JdongOrderTmcRequest;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.JdongTmcRequest;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.TmcTopic;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.redis.factory.CacheType;
import ngp.idgenerator.UId;
import ngp.redis.RedisPoolFactory;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class JdongOrderAndRefundHandler extends JdongNotifyBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(JdongOrderAndRefundHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    private final JdongConfig jdongConfig;
    private final JdongQQDConfig jdongQQDConfig;

    public JdongOrderAndRefundHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper, JdongConfig jdongConfig, JdongQQDConfig jdongQQDConfig) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
        this.jdongConfig = jdongConfig;
        this.jdongQQDConfig = jdongQQDConfig;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String tmMessage = invokeMessage.getMessage();
        JdongOrderTmcRequest apiRequest;
        try {
            apiRequest = JsonUtils.toObject(tmMessage, JdongOrderTmcRequest.class);
            if (apiRequest == null) {
                throw new PlatformInterfaceException(String.format("平台接口返回报文数据异常,报文信息：%s", tmMessage));
            }
        } catch (Exception ex) {
            LOGGER.error("京东tmMessage数据转换成JdongTmcRequest实体出错，错误信息：{}", ex.getMessage(), ex);
            return "";
        }
        String venderId = getVenderId(apiRequest);
        if (StringUtils.isBlank(venderId)) {
            LOGGER.info("profileId:{},京东,platform_shop_id(venderId)解析为空!tmMessage:{}", invokeMessage.getProfileId(), tmMessage);
            return buildResponse("0", "success");
        }
        EshopRegisterNotify eshopRegister;
        if (invokeMessage.getShopType() != null) {
            eshopRegister = SupportUtil.buildNotify(venderId, invokeMessage.getShopType().getCode());
        } else {
            List<Integer> shopTypes = Arrays.asList(ShopType.JDong.getCode(), ShopType.JdongVC.getCode(),
                    ShopType.JdongZGB.getCode(), ShopType.JdongFBP.getCode(), ShopType.JdongQQD.getCode());
            eshopRegister = SupportUtil.buildNotifies(venderId, shopTypes);
        }
        if (eshopRegister == null) {
            LOGGER.error("profileId:{},京东,platform_shop_id(venderId):{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), venderId, tmMessage);
            return buildResponse("501", "管家婆未找到对应店铺!");
        }
        boolean hasOrderPromiseMsg = StringUtils.isNotBlank(apiRequest.getPickDate()) || StringUtils.isNotBlank(apiRequest.getDeliveredTime());
        ShopType shopType = ShopType.valueOf(eshopRegister.getType());
        if (hasOrderPromiseMsg) {
            saveTmcOrderMsg(invokeMessage.getProfileId(), eshopRegister.getId(), shopType, tmMessage, apiRequest);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setEshopId(eshopRegister.getId());
        eshopNotifyChange.setOnlineShopId(venderId);
        eshopNotifyChange.setTradeOrderId(apiRequest.getTradeOrderId());
        eshopNotifyChange.setChangeBizId(apiRequest.getTradeOrderId());
        if (StringUtils.equals(jdongConfig.getModifySellerMemoTopic(), apiRequest.getTopic())) {
            eshopNotifyChange.setType(TMCType.MODIFY_SELLER_MEMO);
        } else if (StringUtils.equals(jdongConfig.getOrderSubsidyReviewStatusTopic(), apiRequest.getTopic())) {
            eshopNotifyChange.setType(TMCType.ORDER_SUBSIDY_REVIEW_STATUS);
        } else {
            //京东售后消息
            if (StringUtils.equals(jdongConfig.getAfsStepResultJosTopic(), apiRequest.getTopic())) {
                eshopNotifyChange.setChangeBizId(apiRequest.getRefundId());
            }
            eshopNotifyChange.setType(TMCType.Order);
        }
        //京东消息生成唯一键时不用content。防止数据太多了
        eshopNotifyChange.setUniqueId(eshopNotifyChange.generateUniqueId());
        eshopNotifyChange.setContent(tmMessage);
        String uniqueKey = Md5Utils.md5(String.format("%s%s", eshopNotifyChange.getEshopId(), eshopNotifyChange.getContent()));
        boolean getLockSuccess = doGetLocker(uniqueKey);
        if (getLockSuccess) {
            //修改延时为配置
            SupportUtil.doOrderNotifyByDelayWay(venderId, eshopNotifyChange, shopType.getCode(), jdongConfig.getTmcDelayTime());
        }
        return buildResponse("0", "success");
    }


    private boolean doGetLocker(String uniqueKey) {
        try {
            RedisPoolFactory redisPoolFactory = BeanUtils.getBean(RedisPoolFactory.class);
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            long time = DateUtils.getDate().getTime();
            String formatDate = String.valueOf(time);
            Boolean ifAbsent = template.opsForValue().setIfAbsent(uniqueKey, formatDate, jdongConfig.getTmcUniqLockTime(), TimeUnit.SECONDS);
            if (ifAbsent == null) {
                return true;
            }
            return ifAbsent;
        } catch (Exception ex) {
            LOGGER.error("京东tmc消息幂等锁：{}", ex.getMessage(), ex);
        }
        return true;
    }

    private String getVenderId(JdongTmcRequest request) {
        if (StringUtils.equals(request.getTopic(), TmcTopic.AFS_StepResult_JOS)) {
            return request.getBuId();
        }
        if (jdongQQDConfig != null && jdongQQDConfig.getTenantIdTopicList().contains(request.getTopic())) {
            return request.getTenantId();
        }
        if (StringUtils.isNotBlank(request.getVenderId())) {
            return request.getVenderId();
        } else if (StringUtils.isNotBlank(request.getSellerId())) {
            return request.getSellerId();
        } else if (StringUtils.isNotBlank(request.getVender())) {
            return request.getVender();
        }
        return "";
    }

    private int saveTmcOrderMsg(BigInteger profileId, BigInteger eshopId, ShopType shopType, String tmMessage, JdongOrderTmcRequest apiOrder) {
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(profileId, eshopId, apiOrder.getTradeOrderId());
        if (Objects.isNull(orderMsgEntity)) {
            orderMsgEntity = buildEshopTmcOrderMsgEntity(profileId, eshopId, shopType, tmMessage, apiOrder);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        } else {
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(BigInteger profileId, BigInteger eshopId, ShopType shopType, String tmMessage, JdongOrderTmcRequest apiOrder) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(profileId);
        tmcOrderMsgEntity.setEshopId(eshopId);
        tmcOrderMsgEntity.setShopType(shopType);
        tmcOrderMsgEntity.setTradeOrderId(apiOrder.getTradeOrderId());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setCreateTime(new Date());
        //平台没有返回更新时间，平台推送一次就更新一次
        tmcOrderMsgEntity.setUpdateTime(new Date());
        return tmcOrderMsgEntity;
    }


    @Override
    public String serviceName() {
        return "jdongOrderAndRefundInvoker";
    }
}
