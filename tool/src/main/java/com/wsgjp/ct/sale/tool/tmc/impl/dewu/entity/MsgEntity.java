package com.wsgjp.ct.sale.tool.tmc.impl.dewu.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class MsgEntity {
    private String orderNo;
    private Long orderType;
    private Long originMsgID;
    private String appKey;
    private String openID;

    private String subOrderNo;

    public String getSubOrderNo() {
        return subOrderNo;
    }

    public void setSubOrderNo(String subOrderNo) {
        this.subOrderNo = subOrderNo;
    }

    @JsonProperty("orderNo")
    public String getOrderNo() { return orderNo; }
    @JsonProperty("orderNo")
    public void setOrderNo(String value) { this.orderNo = value; }

    @JsonProperty("orderType")
    public Long getOrderType() { return orderType; }
    @JsonProperty("orderType")
    public void setOrderType(Long value) { this.orderType = value; }

    @JsonProperty("originMsgId")
    public Long getOriginMsgID() { return originMsgID; }
    @JsonProperty("originMsgId")
    public void setOriginMsgID(Long value) { this.originMsgID = value; }

    @JsonProperty("appKey")
    public String getAppKey() { return appKey; }
    @JsonProperty("appKey")
    public void setAppKey(String value) { this.appKey = value; }

    @JsonProperty("openId")
    public String getOpenID() { return openID; }
    @JsonProperty("openId")
    public void setOpenID(String value) { this.openID = value; }
}