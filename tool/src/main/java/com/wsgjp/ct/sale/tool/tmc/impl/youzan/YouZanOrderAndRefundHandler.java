package com.wsgjp.ct.sale.tool.tmc.impl.youzan;

import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderByTmcRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ConsumerType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.factory.youzanretail.entity.DeliveryOrder;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.youzan.entity.MessageBody;
import com.wsgjp.ct.sale.tool.tmc.impl.youzan.entity.YouZanTmcMessage;
import com.wsgjp.ct.sale.tool.tmc.impl.youzan.entity.YouZanTmcResponse;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.CollectionUtils;
import ngp.utils.HttpUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 有赞微商城、有赞零售订单、售后单TMC消息处理
 */
@Component
public class YouZanOrderAndRefundHandler extends YouZanNotifyBase {

    private final EshopSaleOrderService saleOrderService;
    private static final Logger LOGGER = LoggerFactory.getLogger(YouZanOrderAndRefundHandler.class);

    public YouZanOrderAndRefundHandler(EshopSaleOrderService saleOrderService) {
        this.saleOrderService = saleOrderService;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        String tmMessage = invokeMessage.getMessage();
        YouZanTmcMessage apiRequest;
        try {
            apiRequest = JsonUtils.toObject(tmMessage, YouZanTmcMessage.class);
            if (StringUtils.isNotBlank(apiRequest.getMsg())) {
                apiRequest.setMsgBody(JsonUtils.toObject(HttpUtils.urlDecode(apiRequest.getMsg()), MessageBody.class));
            } else if (CollectionUtils.isNotEmpty(apiRequest.getSource_delivery_orders())){
                apiRequest.setMsgBody(JsonUtils.toObject(tmMessage, MessageBody.class));
                //改派消息
            }else {
                throw new RuntimeException("平台发送消息异常!");
            }
        } catch (Exception ex) {
            LOGGER.error("{}tmMessage数据转换成YouZanTmcMessage实体出错，错误信息：{}", shopTypeName, ex.getMessage(), ex);
            return buildResponse(new YouZanTmcResponse(500L, ex.getMessage()));
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiRequest.getOnlineShopId(), invokeMessage.getShopType().getCode());
        if (Objects.isNull(eshopRegister)) {
            LOGGER.error("profileId:{},店铺类型:{},platform_shop_id:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, apiRequest.getOnlineShopId(), tmMessage);
            return buildResponse(new YouZanTmcResponse(201L, "管家婆未找到对应店铺!"));
        }
        if (StringUtils.isBlank(apiRequest.getId()) && (apiRequest.getMsgBody() == null || StringUtils.isBlank(apiRequest.getMsgBody().getTid()))) {
            LOGGER.error("有赞消息订单号为空！profileId:{},eshopId:{},店铺类型：{},tmMessage:{}", eshopRegister.getProfileId(), eshopRegister.getId(), shopTypeName, tmMessage);
            return buildResponse(new YouZanTmcResponse(0L, "success"));
        }
        String tradeId;
        if (apiRequest.getMsgBody() != null && StringUtils.isNotBlank(apiRequest.getMsgBody().getTid())) {
            tradeId = apiRequest.getMsgBody().getTid();
        } else {
            tradeId = apiRequest.getId();
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(apiRequest.getOnlineShopId());
        eshopNotifyChange.setTradeOrderId(tradeId);
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(tmMessage);
        //改派消息
        if (StringUtils.equals(apiRequest.getType(), "youzan_retail_DeliveryOrderChangeDispatcher") ||
                (apiRequest.getMsgBody() != null && CollectionUtils.isNotEmpty(apiRequest.getSource_delivery_orders()))) {
            eshopNotifyChange.setType(TMCType.ORDER_DISPATCHER);
            List<DeliveryOrder> sourceDeliveryOrders = apiRequest.getSource_delivery_orders();
            List<String> tradeIds = sourceDeliveryOrders.stream().filter(x -> StringUtils.equals(x.getInvalid(), "1")).map(DeliveryOrder::getDelivery_order_no).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tradeIds)) {
                for (String tradeOrderId : tradeIds) {
                    UpdateOrderByTmcRequest request = new UpdateOrderByTmcRequest();
                    try {
                        request.setTradeOrderId(tradeOrderId);
                        request.setTradeStatus(TradeStatus.ALL_CLOSED);
                        request.setShopId(eshopRegister.getId());
                        request.setConsumerType(ConsumerType.DIRECT_UPDATE_TRADE_STATUS);
                        saleOrderService.updateOrderByTmcRequest(request);
                    } catch (Exception ex) {
                        LOGGER.error("有赞消息修改订单{}改派状态出错！profileId:{},eshopId:{},店铺类型：{},request:{},tmMessage:{},错误信息：{}",
                                eshopRegister.getProfileId(), tradeOrderId, eshopRegister.getId(), shopTypeName,
                                JsonUtils.toJson(request), tmMessage, ex.getMessage(), ex);
                    }
                }
            }
        }
        SupportUtil.doOrderNotify(apiRequest.getOnlineShopId(), eshopNotifyChange, invokeMessage.getShopType().getCode());
        return buildResponse(new YouZanTmcResponse(0L, "success"));
    }

    private String buildResponse(YouZanTmcResponse response) {
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "youZanOrderAndRefundHandler";
    }
}
