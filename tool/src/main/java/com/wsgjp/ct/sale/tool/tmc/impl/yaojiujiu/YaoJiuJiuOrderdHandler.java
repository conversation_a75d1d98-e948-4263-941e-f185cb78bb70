package com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu;

import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.constant.PlatformTmcType;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu.entity.Order;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

@Component
public class YaoJiuJiuOrderdHandler extends YaoJjiuJiuNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(YaoJiuJiuOrderdHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    public YaoJiuJiuOrderdHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        YJJResponse resp = new YJJResponse();
        if (invokeMessage.getEshopId() == null || invokeMessage.getEshopId().compareTo(BigInteger.ZERO) == 0){
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(invokeMessage.getOnlineEshopId(), invokeMessage.getShopType().getCode());
            invokeMessage.setEshopId(eshopRegister.getId());
        }
        Order apiOrder;
        try {
            apiOrder = JsonUtils.toObject(tmMessage, Order.class);
            if (apiOrder == null){
                throw new RuntimeException("药九九转换实体为空");
            }
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成Order实体出错，错误信息：{}",shopTypeName,e.getMessage(),e);
            resp.setCode(203);
            resp.setSuccess(false);
            resp.setMsg("json转换报错!");
            return JsonUtils.toJson(resp);
        }
        try{
            saveTmcOrderMsg(invokeMessage,tmMessage,apiOrder);
        }catch (Exception ex){
            LOGGER.error("{}保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,invokeMessage.getProfileId(),invokeMessage.getEshopId(),tmMessage,ex.getMessage(),ex);
            resp.setCode(203);
            resp.setSuccess(false);
            resp.setMsg("订单保存数据库出错!");
            return JsonUtils.toJson(resp);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(invokeMessage.getOnlineEshopId());
        eshopNotifyChange.setTradeOrderId(apiOrder.getOrderCode());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(tmMessage);
        eshopNotifyChange.setPlatformMsgType(PlatformTmcType.ORDER_MSG);
        SupportUtil.doOrderNotify(invokeMessage.getOnlineEshopId(),eshopNotifyChange,invokeMessage.getShopType().getCode());
        resp.setCode(200);
        resp.setSuccess(true);
        resp.setMsg("成功");
        return JsonUtils.toJson(resp);

    }

    public int saveTmcOrderMsg(InvokeMessageEntity invokeMessage, String tmMessage, Order orderRequest){
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(invokeMessage.getProfileId(),invokeMessage.getEshopId(),orderRequest.getOrderCode());
        if (Objects.isNull(orderMsgEntity)){
            orderMsgEntity = buildEshopTmcOrderMsgEntity(invokeMessage,tmMessage,orderRequest);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        }else {
            //orderMsgEntity.setStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(InvokeMessageEntity invokeMessage, String tmMessage, Order orderRequest) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(invokeMessage.getProfileId());
        tmcOrderMsgEntity.setEshopId(invokeMessage.getEshopId());
        tmcOrderMsgEntity.setShopType(invokeMessage.getShopType());
        tmcOrderMsgEntity.setTradeOrderId(orderRequest.getOrderCode());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        //默认值已付款
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        try {
            if (orderRequest.getOrderTime() != null){
                Date date = new Date(orderRequest.getOrderTime());
                tmcOrderMsgEntity.setCreateTime(date);
                //平台没有返回更新时间，平台推送一次就更新一次
            }
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (Exception e) {
            LOGGER.error("药九九转换时间戳失败:" + e.getMessage(), e);
        }
        return tmcOrderMsgEntity;
    }


    @Override
    public String serviceName() {
        return "yjjOrder";
    }

}
