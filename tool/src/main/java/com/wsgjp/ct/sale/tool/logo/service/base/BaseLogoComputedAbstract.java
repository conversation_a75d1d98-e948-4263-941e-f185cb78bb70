package com.wsgjp.ct.sale.tool.logo.service.base;

import com.wsgjp.ct.sale.biz.common.entity.LogoExceptionState;
import com.wsgjp.ct.sale.sdk.logo.state.LogoExceptionStatusEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoSourceTypeEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoUsePhaseEnum;
import com.wsgjp.ct.sale.tool.logo.util.LogoUtil;

import java.util.List;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-03-14 16:58
 */
public abstract class BaseLogoComputedAbstract<T extends BaseLogoEntity> implements BaseLogoComputed<T> {

    @Override
    public void computed(List<LogoExceptionState> result, List<T> logoDataList, LogoSourceTypeEnum sourceType) {
        for (T logoData : logoDataList) {
            //确定该订单在那些界面显示
            List<LogoUsePhaseEnum> logoUsePhaseList = getLogoUsePhase(logoData);
            for (LogoUsePhaseEnum logoUsePhase : logoUsePhaseList) {
                //拿到当前界面的徽标状态列表
                List<LogoExceptionStatusEnum> logoStateList = getLogoStateList(logoUsePhase);
                //遍历需要计算的异常状态
                for (LogoExceptionStatusEnum logoState : logoStateList) {
                    //设置当前计算哪个界面的徽标
                    logoData.setLogoUsePhase(logoUsePhase);
                    //判断该订单是否处于该异常状态
                    boolean verdictResult = verdict(logoData, logoState);
                    //如果处于该异常状态则构建结果
                    if (verdictResult) {
                        LogoUtil.buildNewStateList(result, logoData.getProfileId(), logoData.getVchcode(), logoState, sourceType, logoUsePhase,logoData.getTradeCreateTime());
                    }
                }
            }

        }
    }

    //判断该订单是否处于该异常状态，交给子类去完成
    public abstract boolean verdict(T logoData, LogoExceptionStatusEnum logoState);

}
