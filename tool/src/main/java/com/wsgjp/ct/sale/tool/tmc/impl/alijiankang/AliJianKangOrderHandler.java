package com.wsgjp.ct.sale.tool.tmc.impl.alijiankang;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.dto.AliJianKangOrderDto;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class AliJianKangOrderHandler extends AliJianKangNotifyBase implements MessageHandler {
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    public AliJianKangOrderHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String tmMessage = invokeMessage.getMessage();
        String shopTypeName = invokeMessage.getShopType().getName();
        AliJianKangOrderDto apiOrder;
        try {
            apiOrder = JsonUtils.toObject(invokeMessage.getMessage(), AliJianKangOrderDto.class);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成AliJianKangOrderDto实体出错，错误信息：{}", shopTypeName, e.getMessage());
            return buildInvokeResult(false, "isv系统转换消息失败", "201");
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiOrder.getSupplier_id(), invokeMessage.getShopType().getCode());
        if (Objects.isNull(eshopRegister)) {
            return buildInvokeResult(false, "isv系统内未找到对应的注册账号信息", "201");
        }
        try{
            saveTmcOrderMsg(eshopRegister,tmMessage,apiOrder);
        }catch (Exception ex){
            return buildInvokeResult(false, "isv系统内保存消息失败", "201");
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(apiOrder.getSupplier_id());
        eshopNotifyChange.setTradeOrderId(apiOrder.getMain_ship_order_id());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(tmMessage);
        SupportUtil.doOrderNotify(apiOrder.getSupplier_id(),eshopNotifyChange,invokeMessage.getShopType().getCode());
        return buildInvokeResult(true, "成功", "0");
    }


    private int saveTmcOrderMsg(EshopRegisterNotify eshopRegister, String tmMessage, AliJianKangOrderDto AliJianKangOrderDto) {
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(eshopRegister.getProfileId(), eshopRegister.getId(), AliJianKangOrderDto.getMain_ship_order_id());
        if (null == orderMsgEntity) {
            orderMsgEntity = buildEshopTmcOrderMsgEntity(eshopRegister, tmMessage, AliJianKangOrderDto);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        } else {
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }


    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(EshopRegisterNotify eshopRegister, String tmMessage, AliJianKangOrderDto AliJianKangOrderDto) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopRegister.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopRegister.getId());
        tmcOrderMsgEntity.setShopType(ShopType.valueOf(eshopRegister.getType()));
        tmcOrderMsgEntity.setTradeOrderId(AliJianKangOrderDto.getMain_ship_order_id());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        //默认值已付款
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        if(StringUtils.isEmpty(AliJianKangOrderDto.getCreate_order_time())){
            return tmcOrderMsgEntity;
        }
        try {
            Date date = DateUtils.parseDate(AliJianKangOrderDto.getCreate_order_time(), "yyyy-mm-dd HH:mm:ss");
            tmcOrderMsgEntity.setCreateTime(date);
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (Exception e) {

        }
        return tmcOrderMsgEntity;
    }


    @Override
    public String serviceName() {
        return "qimen.alibaba.alihealth.shiporder.push";
    }
}
