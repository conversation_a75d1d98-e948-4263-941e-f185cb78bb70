package com.wsgjp.ct.sale.tool.tmc.impl.hema;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderRefundStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.factory.hema.entity.HeMaCancelOrderEntity;
import com.wsgjp.ct.sale.platform.factory.hema.entity.HeMaConstants;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.hema.entity.HemaTopicEnum;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.HttpUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class HeMaNotifyHandler extends HeMaNotifyBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(HeMaNotifyHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    private final EshopTmcUtils eshopTmcUtils;

    public HeMaNotifyHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper, EshopTmcUtils eshopTmcUtils) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        if (invokeMessage.getEshopId() == null) {
            return HeMaUtils.buildResponse("not query shop mapping!", "501", "", false);
        }
        HeMaBaseMessage heMaBaseMessage = JsonUtils.toObject(invokeMessage.getMessage(), HeMaBaseMessage.class);
        if (heMaBaseMessage == null) {
            return HeMaUtils.buildResponse("消息体为空", "500", "", false);
        }
        EshopInfo eshopInfo;
        try {
            eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), invokeMessage.getEshopId());
            if (Objects.isNull(eshopInfo)) {
                return HeMaUtils.buildResponse("not query shop info!", "501", "", false);
            }
            saveTmcOrderMsg(invokeMessage.getProfileId(), eshopInfo.getOtypeId(), invokeMessage.getMessage(), heMaBaseMessage);
            saveRefundToPlEshopNotifyChange(invokeMessage);
        } catch (Exception ex) {
            LOGGER.error("盒马保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", invokeMessage.getProfileId(), invokeMessage.getEshopId(),
                    invokeMessage.getMessage(), ex.getMessage(), ex);
            return HeMaUtils.buildResponse(ex.getMessage(), "501", heMaBaseMessage.getRequestId(), false);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setTradeOrderId(heMaBaseMessage.getFulfillOrderId());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(invokeMessage.getMessage());
        SupportUtil.sendMessage(eshopNotifyChange, eshopInfo);
        return HeMaUtils.buildResponse("SUCCESS", "0", heMaBaseMessage.getRequestId(), true);
    }

    private void saveRefundToPlEshopNotifyChange(InvokeMessageEntity invokeMessage) {
        try {
            HeMaCancelOrderEntity curRefundMessage = JsonUtils.toObject(invokeMessage.getMessage(), HeMaCancelOrderEntity.class);
            if (!StringUtils.equals(curRefundMessage.getRouteType(), HemaTopicEnum.FulFillOrder_cancelMainOrder.getTopic()) &&
                    !StringUtils.equals(curRefundMessage.getRouteType(), HemaTopicEnum.FulFillOrder_cancelSubOrder.getTopic())) {
                return;
            }
            EshopNotifyChange curNotifyChange = new EshopNotifyChange();
            curNotifyChange.setId(UId.newId());
            curNotifyChange.setUpdateTime(new Date());
            curNotifyChange.setProfileId(invokeMessage.getProfileId());
            curNotifyChange.setEshopId(invokeMessage.getEshopId());
            curNotifyChange.setContent(invokeMessage.getMessage());
            curNotifyChange.setTradeOrderId(curRefundMessage.getFulfillOrderId());
            curNotifyChange.setType(TMCType.REFUND_STOP);
            UpdateOrderRefundStateRequest request = new UpdateOrderRefundStateRequest();
            request.setTradeOrderId(curNotifyChange.getTradeOrderId());
            request.setShopId(curNotifyChange.getEshopId());
            if (StringUtils.equals(curRefundMessage.getRouteType(), HemaTopicEnum.FulFillOrder_cancelSubOrder.getTopic())){
                request.setRefundState(ReturnState.SECTION_REFUNDING);
            }else{
                request.setRefundState(ReturnState.REFUNDING);
            }
            request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CREATE_BY_TMC);
            request.setOidList(curRefundMessage.getFulfillSubOrderIds());
            EshopSaleOrderService eshopSaleOrderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
            TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
            List<EshopNotifyChange> eshopNotifyChanges = tmcMapper.queryMessageChangeSorted(curNotifyChange.getProfileId(),
                    Collections.singletonList(curNotifyChange.getTradeOrderId()), curNotifyChange.getEshopId(), TMCType.REFUND_STOP.getCode());
            HashSet<String> oldFulfillSubOrderIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(eshopNotifyChanges)) {
                for (EshopNotifyChange eshopNotifyChange : eshopNotifyChanges) {
                    String content = eshopNotifyChange.getContent();
                    if (StringUtils.isEmpty(content)) {
                        continue;
                    }
                    HeMaCancelOrderEntity oldRefundMessage = JsonUtils.toObject(content, HeMaCancelOrderEntity.class);
                    if (oldRefundMessage == null) {
                        continue;
                    }
                    //已经存在整单取消消息。说明消息重复过滤
                    if (StringUtils.equals(oldRefundMessage.getRouteType(), HemaTopicEnum.FulFillOrder_cancelMainOrder.getTopic())) {
                        return;
                    }
                    oldFulfillSubOrderIds.addAll(oldRefundMessage.getFulfillSubOrderIds());
                }
            }
            //如果新消息是部分取消，并且oid都取消过了，说明消息重复过滤
            if (StringUtils.equals(curRefundMessage.getRouteType(), HemaTopicEnum.FulFillOrder_cancelSubOrder.getTopic())
                    && oldFulfillSubOrderIds.containsAll(curRefundMessage.getFulfillSubOrderIds())) {
                return;
            }
            tmcMapper.insertMessageChange(curNotifyChange);
            eshopSaleOrderService.updateOrderRefundState(request);
        } catch (Exception ex) {
            if (ex.getMessage() != null && ex.getMessage().contains("订单尚未流入系统")) {
                return;
            }
            LOGGER.error("保存退款消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",
                    invokeMessage.getProfileId(), invokeMessage.getEshopId(), invokeMessage.getMessage(), ex.getMessage(), ex);
        }
    }


    private int saveTmcOrderMsg(BigInteger profileId, BigInteger eshopId, String tmMessage, HeMaBaseMessage heMaBaseMessage) {
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(profileId, eshopId, heMaBaseMessage.getFulfillOrderId());
        if (orderMsgEntity == null) {
            orderMsgEntity = buildEshopTmcOrderMsgEntity(profileId, eshopId, HttpUtils.urlEncode(tmMessage), heMaBaseMessage);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        } else {
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            //将下发订单消息和取消订单消息用&拼接起来
            orderMsgEntity.setMessage(String.format("%s%s%s", orderMsgEntity.getMessage(), HeMaConstants.SPLIT_CHARACTER, HttpUtils.urlEncode(tmMessage)));
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(BigInteger profileId, BigInteger eshopId, String tmMessage, HeMaBaseMessage heMaBaseMessage) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(profileId);
        tmcOrderMsgEntity.setEshopId(eshopId);
        tmcOrderMsgEntity.setShopType(ShopType.HeMa);
        tmcOrderMsgEntity.setTradeOrderId(heMaBaseMessage.getFulfillOrderId());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setCreateTime(new Date());
        //平台没有返回更新时间，平台推送一次就更新一次
        tmcOrderMsgEntity.setUpdateTime(new Date());
        return tmcOrderMsgEntity;
    }

    @Override
    public String serviceName() {
        return "hemaInvoker";
    }
}
