package com.wsgjp.ct.sale.tool.tmc.impl.kuaishou.entity;

import com.wsgjp.ct.sale.common.enums.TMCType;
import ngp.utils.ArrayUtils;
import ngp.utils.StringUtils;

/**
 * 拼多多tmc消息类型
 */

public enum KuaiShouTmcMsgType {
    /**
     * 修改交易收货地址消息:发货前，卖家修改交易的收货地址，会产生此消息
     */
    Order("kwaishop_order_addOrder", TMCType.Order),
    /**
     * 订单旗帜备注新增及变更消息=>订单旗帜备注的修改和新增会触发此消息发送注：仅修改旗帜颜色也会触发消息发送，此备注指商家备注note，并非买家留言remark
     */
    OrderNote("kwaishop_order_addNote", TMCType.TradeMemoModified),
    /**
     * 订单地址变更消息=>订单地址信息有变更时会触发消息发送，包括商家变更了订单地址、买家申请了修改订单地址信息并审核通过后会发送消息
     */
    OrderAddress("kwaishop_order_addressChange", TMCType.Order),

    /**
     * 订单商品规格属性变更时触发消息发送，商品规格属性需要买家同意后由商家变更，变更成功时发送消息
     */
    OrderProductSkuChange("kwaishop_order_productSkuChange", TMCType.Order),

    /**
     * 订单风控状态解除消息
     */
    OrderRiskStatusChange("kwaishop_order_riskStatusChange", TMCType.Order),

    /**
     * 订单状态变更为已支付时触发消息发送，订单状态字段status=30
     */
    OrderPaySuccess("kwaishop_order_paySuccess", TMCType.Order),

    /**
     * 订单状态变更为已发货时触发消息发送，订单状态字段status=40
     */
    OrderDeliver("kwaishop_order_delivering", TMCType.Order),

    /**
     * 订单已收货消息，订单状态字段status=50
     */
    OrderDeliverSuccess("kwaishop_order_deliverySuccess", TMCType.Order),

    /**
     * 订单状态变更为交易成功时触发消息发送，状态字段status=70
     */
    OrderSuccess("kwaishop_order_orderSuccess", TMCType.Order),
    /**
     * 订单状态变更为交易失败时触发消息发送，订单状态字段status=80
     */
    OrderFail("kwaishop_order_orderFail", TMCType.Order),
    /**
     * 订单费用变更消息
     */
    OrderTotalFeeChange("kwaishop_order_totalFeeChange", TMCType.Order),
    /**
     * 订单发货时效变更消息
     */
    OrderDeliverEffect("kwaishop_order_deliveryEffect", TMCType.Order),
    /**
     * 买家修改订单地址消息
     * 1.订单支付后发货前，买家可申请修改订单地址信息，详见《用户自助修改地址》
     * 2.买家申请修改订单地址时、商家审批通过时、商家审批拒绝时，均会触发消息发送
     * 3.监听到消息后，商家不需要全量处理，按照《订单收货地址变更场景》流程处理
     */
    OrderAddressUpdateAudit("kwaishop_order_addressUpdateAudit", TMCType.CHANGE_ADDRESS),
    /**
     * 订单履约拦截解除消息
     * 订单从不可履约发货的情况转为可进行履约发货时触发消息发送，如拼团订单拼团成功、风控审核态订单审核通过等
     */
    OrderEnableDelivery("kwaishop_order_enableDelivery", TMCType.Order),

    /**
     * 快分销订单变更消息
     */
    DistributeOrderChange("kwaishop_distribute_order_change_notify", TMCType.Order),

    /**
     * 售后单新增消息
     */
    AddRefund("kwaishop_aftersales_addRefund", TMCType.Order),
    /**
     * 售后单有更新时触发消息发送 ，包括状态、金额、协商记录等所有变更
     */
    RefundUpdateRefund("kwaishop_aftersales_updateRefund", TMCType.Order),
    /**
     * 订单履约拦截发货消息
     */
    HoldDelivery("kwaishop_order_holdDelivery", TMCType.Order),
    /**
     * 订单履约拦截解除消息，订单从不可履约发货的情况转为可进行履约发货时触发消息发送，如拼团订单拼团成功、风控审核态订单审核通过等
     */
    EnableDelivery("kwaishop_order_enableDelivery", TMCType.Order);


    private final String event;
    /**
     * 对应ngp TMC业务类型
     */
    private final TMCType tmcType;

    KuaiShouTmcMsgType(String event, TMCType tmcType) {
        this.event = event;
        this.tmcType = tmcType;
    }

    public String getEvent() {
        return event;
    }

    public TMCType getTmcType() {
        return tmcType;
    }

    public static KuaiShouTmcMsgType getKuaiShouTmcMsgTypeByEvent(String flag) {
        KuaiShouTmcMsgType[] msgTypes = KuaiShouTmcMsgType.values();
        if (ArrayUtils.isEmpty(msgTypes) || StringUtils.isEmpty(flag)) {
            return null;
        }
        for (KuaiShouTmcMsgType msgType : msgTypes) {
            if (msgType.getEvent().equals(flag)) {
                return msgType;
            }
        }
        return null;
    }
}
