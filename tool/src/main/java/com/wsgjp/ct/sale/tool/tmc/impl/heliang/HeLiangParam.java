package com.wsgjp.ct.sale.tool.tmc.impl.heliang;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class HeLiangParam {
    @JsonProperty("appId")
    private String appId;
    @JsonProperty("message_body")
    private MessageBody messageBody;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public MessageBody getMessageBody() {
        return messageBody;
    }

    public void setMessageBody(MessageBody messageBody) {
        this.messageBody = messageBody;
    }
}
