package com.wsgjp.ct.sale.tool.logo.entity;

import java.math.BigInteger;

/**
 * <AUTHOR> csh
 * @create 2023-06-16 11:00
 */
public class ToolLockEntity {
    private BigInteger profileId;
    private long threadId;
    private String key;
    private int lockSeconds;
    private String errMsg;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public long getThreadId() {
        return threadId;
    }

    public void setThreadId(long threadId) {
        this.threadId = threadId;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getLockSeconds() {
        return lockSeconds;
    }

    public void setLockSeconds(int lockSeconds) {
        this.lockSeconds = lockSeconds;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
