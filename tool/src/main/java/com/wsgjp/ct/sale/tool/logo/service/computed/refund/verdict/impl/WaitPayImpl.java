package com.wsgjp.ct.sale.tool.logo.service.computed.refund.verdict.impl;

import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundAuditStatus;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundDeleteStateEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundPayStatus;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.enums.RefundTypeEnum;
import com.wsgjp.ct.sale.sdk.logo.state.LogoExceptionStatusEnum;
import com.wsgjp.ct.sale.tool.logo.service.computed.refund.entity.LogoEntityByRefund;
import com.wsgjp.ct.sale.tool.logo.service.computed.refund.verdict.LogoVerdictByRefundAbstract;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-03-15 17:41
 * 无发货仓库
 */
@Service
public class WaitPayImpl extends LogoVerdictByRefundAbstract {

    @Override
    public LogoExceptionStatusEnum getLogoState() {
        return LogoExceptionStatusEnum.WAIT_PAY;
    }

    @Override
    public boolean computed(LogoEntityByRefund logoData) {
        return (RefundTypeEnum.MONEY_ONLY.equals(logoData.getRefundTypeEnum())
                || RefundTypeEnum.MONEY_GOODS.equals(logoData.getRefundTypeEnum())
                || RefundTypeEnum.REFUND_ONLY_PART_MONEY.equals(logoData.getRefundTypeEnum())
                || RefundTypeEnum.REFUND_FEE_AFTER_SALE.equals(logoData.getRefundTypeEnum()))
                && !RefundDeleteStateEnum.Deleted.equals(logoData.getDeleted())
                && !RefundStatus.SELLER_REFUSE.equals(logoData.getRefundState())
                && !RefundStatus.CANCEL.equals(logoData.getRefundState())
                && RefundPayStatus.WAIT_PAY.equals(logoData.getPayState())
                && RefundAuditStatus.AUDITED.equals(logoData.getConfirmState())
                ;
    }
}
