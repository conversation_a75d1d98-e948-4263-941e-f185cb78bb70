package com.wsgjp.ct.sale.tool.logo.service.computed.eshoporder.verdict.impl;

import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.sdk.logo.state.LogoExceptionStatusEnum;
import com.wsgjp.ct.sale.tool.logo.service.computed.eshoporder.entity.LogoEntityByOrder;
import com.wsgjp.ct.sale.tool.logo.service.computed.eshoporder.verdict.LogoVerdictByOrderAbstract;
import org.springframework.stereotype.Service;

import java.math.BigInteger;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-03-15 17:41
 * 无发货仓库
 */
@Service
public class NoStockImpl extends LogoVerdictByOrderAbstract {
    @Override
    public LogoExceptionStatusEnum getLogoState() {
        return LogoExceptionStatusEnum.NO_STOCK;
    }

    @Override
    public boolean computed(LogoEntityByOrder logoData) {
        return BigInteger.ZERO.compareTo(logoData.getKtypeId()) == 0 &&
                logoData.isOrderDeliverRequired() &&
                !TradeStatus.ALL_CLOSED.equals(logoData.getLocalTradeState()) && !logoData.isDeleted();
    }
}
