package com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark.entitys.spi.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import ngp.utils.JsonUtils;

public class AuthMessage {
    private Long msgType;
    private String msg;

    @JsonProperty("msg_type")
    public Long getMsgType() { return msgType; }
    @JsonProperty("msg_type")
    public void setMsgType(Long value) { this.msgType = value; }

    @JsonProperty("msg")
    public Msg getMsg() { return
            JsonUtils.toObject(msg,Msg.class); }
    @JsonProperty("msg")
    public void setMsg(String value) { this.msg = value; }
}
