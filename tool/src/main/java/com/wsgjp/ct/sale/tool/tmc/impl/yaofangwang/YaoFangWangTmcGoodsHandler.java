package com.wsgjp.ct.sale.tool.tmc.impl.yaofangwang;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.constant.SyncProductConst;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.product.TmcProductMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.tool.common.log.EshopTmcMessageLog;
import com.wsgjp.ct.sale.tool.common.log.MqConsumeLog;
import com.wsgjp.ct.sale.tool.product.entity.ProductConst;
import com.wsgjp.ct.sale.tool.tmc.controller.TmcConst;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.yaofangwang.entity.ProductMsg;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.sale.tool.tmc.utils.TmcLogUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.mq.SysMqSend;
import ngp.mq.MqSendResult;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Collections;
import java.util.List;


@Component
public class YaoFangWangTmcGoodsHandler extends YaoFangWangNotifyBase implements MessageHandler {


    private static final Logger LOGGER = LoggerFactory.getLogger(YaoFangWangTmcGoodsHandler.class);


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        try {
            ProductMsg tmcMessage = JsonUtils.toObject(invokeMessage.getMessage(), ProductMsg.class);
            if (tmcMessage == null || tmcMessage.getData() == null || CollectionUtils.isEmpty(tmcMessage.getData().getDataList())) {
                LOGGER.error("{},解析消息失败",JsonUtils.toJson(invokeMessage));
                return TmcConst.FAIL;
            }
            List<ProductMsg.DataList> dataList = tmcMessage.getData().getDataList();
            for (ProductMsg.DataList msg : dataList) {
                TmcProductMessage tmcProductMessage=new TmcProductMessage();
                tmcProductMessage.setNumId(msg.getMedicineCode());
                BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
                if (BigInteger.ZERO.equals(invokeMessage.getEshopId())){
                    // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                    EshopRegisterNotify notify = SupportUtil.buildNotify(String.valueOf(tmcMessage.getData().getShopID()), ShopType.YaoFangWang.getCode());
                    if (notify == null || notify.getId()==null|| notify.getId().equals(BigInteger.ZERO)){
                        throw new RuntimeException("获取eshopId失败");
                    }
                    eshopId=notify.getId();
                }else {
                    eshopId = invokeMessage.getEshopId();
                }
                tmcProductMessage.setEshopId(eshopId);
                tmcProductMessage.setProfileId(CurrentUser.getProfileId());
                tmcProductMessage.setCreateTime(DateUtils.getDate());
                tmcProductMessage.setType(SyncProductConst.TMC_PRODUCT);
                List<MqSendResult<TmcProductMessage>> sendResults = SysMqSend.send(Collections.singletonList(tmcProductMessage), ProductConst.TmcProductTopIcName);
                if (CollectionUtils.isEmpty(sendResults)) {
                    LOGGER.error("{},发送消息失败,sendMq返回结果为空",JsonUtils.toJson(invokeMessage));
                    return TmcConst.FAIL;
                }
                boolean sendSuccess = true;
                for (MqSendResult<TmcProductMessage> sendResult : sendResults){
                    if(!sendResult.isSuccess()){
                        sendSuccess = false;
                        continue;
                    }
                    String messageId = sendResult.getMessageId();
                    EshopTmcMessageLog tmcMessageLog = TmcLogUtil.buildTmcLog(invokeMessage, "商品相关消息", messageId);
                    tmcMessageLog.setTmcId(msg.getMedicineCode());
                    MqConsumeLog mqConsumeLog = TmcLogUtil.buildMqLog(tmcProductMessage, messageId, TMCType.Ptype);
                    mqConsumeLog.setTmcId(msg.getMedicineCode());
                    LogService.add(tmcMessageLog);
                    LogService.add(mqConsumeLog);
                }
                if(!sendSuccess){
                    return TmcConst.FAIL;
                }
            }
            return TmcConst.SUCCESS;

        }catch (Exception ex){
            return TmcConst.FAIL;
        }
    }



    @Override
    public String serviceName() {
        return "yaofangWangProduct";
    }
}
