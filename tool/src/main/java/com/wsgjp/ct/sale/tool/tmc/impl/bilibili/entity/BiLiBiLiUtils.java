package com.wsgjp.ct.sale.tool.tmc.impl.bilibili.entity;

import utils.JsonUtils;

/**
 * <AUTHOR>
 */
public class BiLiBiLiUtils {
    public static String buildResponse(String data) {
        BiLiBiLiResponse response = new BiLiBiLiResponse();
        response.setData(data);
        return JsonUtils.toJson(response);
    }

    static class BiLiBiLiResponse {
        private String data;

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }
    }
}
