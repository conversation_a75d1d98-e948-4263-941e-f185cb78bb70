package com.wsgjp.ct.sale.tool.tmc.impl.bilibili.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class MessageContent {
    @JsonProperty("shop_id")
    private String shopId;
    /**
     * 订单消息的订单号
     */
    @JsonProperty("p_id")
    private String pId;
    /**
     * 售后消息中的订单号
     */
    @JsonProperty("order_id")
    private String orderId;
    /**
     * 售后消息中的售后单号
     */
    @JsonProperty("after_sale_id")
    private String afterSaleId;

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getAfterSaleId() {
        return afterSaleId;
    }

    public void setAfterSaleId(String afterSaleId) {
        this.afterSaleId = afterSaleId;
    }
}
