package com.wsgjp.ct.sale.tool.logo.service.base;

import com.wsgjp.ct.sale.sdk.logo.state.LogoUsePhaseEnum;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-03-14 16:54
 */
public class BaseLogoEntity {
    private BigInteger profileId;
    /**
     * 用于计算的 主键id  如：任务单id,交易单id,原始订单id,预售单id
     */
    private BigInteger vchcode;

    private LogoUsePhaseEnum logoUsePhase;
    private Date tradeCreateTime;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    /**
     * 用于计算的 主键id  如：任务单id,交易单id,原始订单id,预售单id
     * @return
     */
    public BigInteger getVchcode() {
        return vchcode;
    }

    /**
     * 用于计算的 主键id  如：任务单id,交易单id,原始订单id,预售单id
     * @param vchcode
     */
    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public LogoUsePhaseEnum getLogoUsePhase() {
        return logoUsePhase;
    }

    public void setLogoUsePhase(LogoUsePhaseEnum logoUsePhase) {
        this.logoUsePhase = logoUsePhase;
    }

    public Date getTradeCreateTime() {
        return tradeCreateTime;
    }

    public void setTradeCreateTime(Date tradeCreateTime) {
        this.tradeCreateTime = tradeCreateTime;
    }
}
