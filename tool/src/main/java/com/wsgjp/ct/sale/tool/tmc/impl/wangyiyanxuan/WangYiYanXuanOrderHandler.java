package com.wsgjp.ct.sale.tool.tmc.impl.wangyiyanxuan;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.wangyiyanxuan.response.CallbackResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import wangyiyanxuansdk.entity.Order;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class WangYiYanXuanOrderHandler extends WangYiYanXuanNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(WangYiYanXuanOrderHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    public WangYiYanXuanOrderHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        List<Order> apiOrder;
        try {
            apiOrder = JsonUtils.toList(tmMessage, Order.class);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成OrderRequest实体出错，错误信息：{}",shopTypeName,e.getMessage());
            return "{\n" +
                    "    \"code\":\"401\",\n" +
                    "    \"errorString\":\"发生异常了\"\n" +
                    "}";
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(invokeMessage.getParams().get("appKey"), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",invokeMessage.getProfileId(),shopTypeName,invokeMessage.getParams().get("appKey"),tmMessage);
            return "{\n" +
                    "    \"code\":\"401\",\n" +
                    "    \"errorString\":\"发生异常了\"\n" +
                    "}";
        }
        LOGGER.info("profileId:{},eshopId:{},店铺类型：{}",eshopRegister.getProfileId(),eshopRegister.getId(),shopTypeName);

        if (CollectionUtils.isEmpty(apiOrder)){
            return "{\n" +
                    "    \"code\":\"401\",\n" +
                    "    \"errorString\":\"发生异常了\"\n" +
                    "}";
        }
        List<CallbackResponse.Data> data = new ArrayList<>();
        for (Order order : apiOrder) {
            try{
                saveTmcOrderMsg(eshopRegister,JsonUtils.toJson(order),order);
            }catch (Exception ex){
                LOGGER.error("{}保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,eshopRegister.getProfileId(),eshopRegister.getId(),tmMessage,ex.getMessage(),ex);
                return "{\n" +
                        "    \"code\":\"401\",\n" +
                        "    \"errorString\":\"发生异常了\"\n" +
                        "}";
            }
            EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
            eshopNotifyChange.setOnlineShopId(invokeMessage.getParams().get("appKey"));
            eshopNotifyChange.setTradeOrderId(order.getOutNumber());
            eshopNotifyChange.setType(TMCType.Order);
            eshopNotifyChange.setContent(JsonUtils.toJson(order));
            SupportUtil.doOrderNotify(invokeMessage.getParams().get("appKey"),eshopNotifyChange,invokeMessage.getShopType().getCode());
            CallbackResponse.Data data1 = new CallbackResponse.Data();
            data1.setOutNumber(order.getOutNumber());
            data.add(data1);
        }
        CallbackResponse response = new CallbackResponse();
        response.setCode("200");
        response.setErrorString("");
        response.setData(data);
        return JsonUtils.toJson(response);
    }

    public int saveTmcOrderMsg(EshopRegisterNotify eshopRegister, String tmMessage, Order orderRequest){
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(eshopRegister.getProfileId(),eshopRegister.getId(),orderRequest.getOutNumber());
        if (Objects.isNull(orderMsgEntity)){
            orderMsgEntity = buildEshopTmcOrderMsgEntity(eshopRegister,tmMessage,orderRequest);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        }else {
            //orderMsgEntity.setStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(EshopRegisterNotify eshopRegister, String tmMessage, Order orderRequest) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopRegister.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopRegister.getId());
        tmcOrderMsgEntity.setShopType(ShopType.valueOf(eshopRegister.getType()));
        tmcOrderMsgEntity.setTradeOrderId(orderRequest.getOutNumber());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        //默认值已付款
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        try {
            Date date = DateUtils.parse(orderRequest.getPayTimeStr());
            tmcOrderMsgEntity.setCreateTime(date);
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (Exception e) {

        }
        return tmcOrderMsgEntity;
    }

    public <T> T convertToJavaBean(String xml, Class<T> t) throws Exception {
        T obj = null;
        JAXBContext context = JAXBContext.newInstance(t);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        obj = (T) unmarshaller.unmarshal(new StringReader(xml));
        return obj;
    }

    @Override
    public String serviceName() {
        return "yx.supplier.push.order.create";
    }
}
