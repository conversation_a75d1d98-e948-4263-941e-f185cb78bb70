package com.wsgjp.ct.sale.tool.tmc.impl.yuanqi;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.YQPurchaseMessage;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class YQPurchaseProductHandler extends YuanQiNotifyBase implements MessageHandler {

    private static final Logger sysLogger = LoggerFactory.getLogger(YQPurchaseProductHandler.class);
    private static final String NumIdKey ="productNumber";
    private static final String GoodsUnitKye="saleUnitName";
    private static final String Barcode="barCode";
    private static final String YQproductUniqueId="productUniqueId";
    private static final String YQproductUnitCode="productUnitCode";
    private static final String YQMappingType="mappingType";
    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        try {
            String message = invokeMessage.getMessage();
            sysLogger.info("**************接收到元气数据"+message+"***************");
            YQPurchaseMessage yqOrder = getYQMsg(message);
            Map<String, String> bodyMap = YQUtils.getBodyMsgMap(JsonUtils.toJson(yqOrder.getBody()));
            checkMsg(!bodyMap.containsKey(NumIdKey),"元气商品消息没有商品编码");
            checkMsg(!bodyMap.containsKey(GoodsUnitKye),"元气商品消息没有销售单位名称");
            checkMsg(!bodyMap.containsKey(Barcode),"元气商品消息没有barCode");

            String numId = bodyMap.get(NumIdKey);
            String unitName = bodyMap.get(GoodsUnitKye);
            String barCode = bodyMap.get(Barcode);
            String mappingType = bodyMap.get(YQMappingType);
            String productUniqueId = bodyMap.get(YQproductUniqueId);
            String productUnitCode = bodyMap.get(YQproductUnitCode);
            String shopAccount = yqOrder.getMerchantNumber();
            EshopNotifyChange tmcMsg = new EshopNotifyChange();
            tmcMsg.setType(TMCType.Ptype);
            tmcMsg.setOnlineShopId(shopAccount);
            tmcMsg.setNumId(numId);
            tmcMsg.setUnitName(unitName);
            tmcMsg.setBarCode(barCode);
            tmcMsg.setMappingType(mappingType);
            tmcMsg.setProductUniqueId(productUniqueId);
            tmcMsg.setProductUnitCode(productUnitCode);
            tmcMsg.setProductType(yqOrder.getType());
            tmcMsg.setContent(message);
            SupportUtil.doProductNotify(shopAccount, tmcMsg, ShopType.YuanQiSenLin.getCode());
            return "success";
        }catch (Exception ex){
            sysLogger.error(ex.getMessage());
            ex.printStackTrace();
            return ex.getMessage();
        }
    }

    @Override
    public String serviceName() {
        return "yuanqisenlin.purchase.product";
    }




}
