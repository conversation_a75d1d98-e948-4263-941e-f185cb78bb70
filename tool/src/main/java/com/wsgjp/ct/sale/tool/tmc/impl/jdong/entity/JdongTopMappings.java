package com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity;

import ngp.utils.StringUtils;

import java.util.HashMap;

public class JdongTopMappings {
    private static final HashMap<String, String> TOPIC_MAP = new HashMap<>();

    static {
        TOPIC_MAP.put("jos_gms_spu_unbind", "jdongProductHandler");
        TOPIC_MAP.put("vc_pic_apply_state_change", "productAuditHandler");
        TOPIC_MAP.put("vc_ware_apply_state_change", "productAuditHandler");
    }

    public static String getHandlerMethod(String topic) {
        if (StringUtils.isEmpty(topic)) {
            return "jdongOrderAndRefundInvoker";
        }
        return TOPIC_MAP.getOrDefault(topic, "jdongOrderAndRefundInvoker");
    }
}
