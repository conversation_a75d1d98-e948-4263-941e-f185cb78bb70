package com.wsgjp.ct.sale.tool.tmc.boot;

import com.wsgjp.ct.sale.tool.tmc.producer.BaseMessageProducer;
import com.wsgjp.ct.sale.tool.tmc.service.ProducerUtil;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(value = "eshoporder-tool-tmc.enabled", havingValue = "true")
public class NotifyRunner implements CommandLineRunner {

    private final ProducerUtil producerUtil;

    public NotifyRunner(ProducerUtil producerUtil) {
        this.producerUtil = producerUtil;
    }

    @Override
    public void run(String... args) {
        List<BaseMessageProducer> producerList = producerUtil.getProducerList();
        producerList.forEach(BaseMessageProducer::start);
    }
}
