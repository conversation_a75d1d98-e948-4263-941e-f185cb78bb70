package com.wsgjp.ct.sale.tool.tmc.impl.qimen;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.BifrostEshopRefundMapper;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcProductMsgMapper;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcRefundMsgMapper;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.CloseOrderRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.CloseOrderResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.EshopOrderMiddleGroundService;
import com.wsgjp.ct.sale.common.constant.SyncOrderConst;
import com.wsgjp.ct.sale.common.constant.SyncProductConst;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.common.entity.product.TmcProductMessage;
import com.wsgjp.ct.sale.common.enums.QmMessageType;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcProductMsgEntity;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcRefundMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.entities.SimpleRefundEntity;
import com.wsgjp.ct.sale.platform.entity.entities.qm.QmGoodsEntity;
import com.wsgjp.ct.sale.platform.entity.entities.qm.QmGoodsItem;
import com.wsgjp.ct.sale.platform.entity.entities.qm.QmOrderEntity;
import com.wsgjp.ct.sale.platform.entity.entities.qm.QmRefundEntity;
import com.wsgjp.ct.sale.platform.exception.PlatformInterfaceException;
import com.wsgjp.ct.sale.platform.factory.hema.entity.HeMaConstants;
import com.wsgjp.ct.sale.platform.factory.qimendx.sdk.order.QiMenCancelOrder;
import com.wsgjp.ct.sale.platform.factory.qimendx.sdk.order.QiMenOrder;
import com.wsgjp.ct.sale.platform.factory.qimendx.sdk.order.QiMenReturnOrder;
import com.wsgjp.ct.sale.platform.factory.qimendx.sdk.product.QiMenProductInfo;
import com.wsgjp.ct.sale.tool.common.log.MqConsumeLog;
import com.wsgjp.ct.sale.tool.tmc.entity.qimen.QmRestParam;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.mq.SysMqSend;
import ngp.idgenerator.UId;
import ngp.mq.MqSendResult;
import ngp.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class QiMenService {
    private static final Logger logger = LoggerFactory.getLogger(QiMenService.class);
    private final EshopTmcOrderMsgMapper tmcMsgMapper;
    private final EshopTmcRefundMsgMapper tmcRefundMsgMapper;
    private final EshopTmcProductMsgMapper tmcProductMsgMapper;
    private final BifrostEshopRefundMapper eshopRefundMapper;
    private final EshopTmcConfig config;
    private final EshopOrderMiddleGroundService eshopOrderMiddleGroundService;
    private static final String QM_MESSAGE_TYPE = "qmMessageType";
    private static final Map<String, String> characters = new HashMap<>();

    static {
        characters.put("&lt;", "<");
        characters.put("&gt;", ">");
    }

    public QiMenService(EshopTmcOrderMsgMapper tmcMsgMapper, EshopTmcRefundMsgMapper tmcRefundMsgMapper, EshopTmcProductMsgMapper tmcProductMsgMapper, BifrostEshopRefundMapper eshopRefundMapper, EshopTmcConfig config, EshopOrderMiddleGroundService eshopOrderMiddleGroundService) {
        this.tmcMsgMapper = tmcMsgMapper;
        this.tmcRefundMsgMapper = tmcRefundMsgMapper;
        this.tmcProductMsgMapper = tmcProductMsgMapper;
        this.eshopRefundMapper = eshopRefundMapper;
        this.config = config;
        this.eshopOrderMiddleGroundService = eshopOrderMiddleGroundService;
    }

    public void doSaveTmcData(QmRestParam param, QmMessageType qmType, EshopInfo eshopInfo) {
        switch (qmType) {
            case ORDER:
                doSaveQmDataToOrder(param, eshopInfo);
                break;
            case REFUND:
                doSaveQmDataToRefund(param, eshopInfo);
                break;
            case GOODS:
                doSaveQmDataToGoods(param, eshopInfo);
                break;
            case ORDER_CANCEL:
                doSaveQmDataToOrderOrRefundCancel(param, eshopInfo);
                break;
            default:
                break;
        }
    }

    /**
     * 处理json数据中 < 和 > 符号被转义
     */
    private String unescapeXml(String xmlStr) {
        if (StringUtils.isBlank(xmlStr)) {
            return xmlStr;
        }
        String unescapeStr = xmlStr;
        for (Map.Entry<String, String> entry : characters.entrySet()) {
            unescapeStr = unescapeStr.replaceAll(entry.getKey(), entry.getValue());
        }
        return unescapeStr;
    }

    private String qmDataToJson(QmRestParam param, Class clazz) {
        //处理xml字符串被转义
        String unescapedXml = unescapeXml(param.getQmData());
        Object object = XmlUtils.toObject(unescapedXml, clazz);
        if (object == null) {
            throw new PlatformInterfaceException("xml解析成对象失败！");
        }
        String jsonData = JsonUtils.toJson(object);
        if (StringUtils.isEmpty(jsonData)) {
            throw new PlatformInterfaceException("xml解析失败！");
        }
        return jsonData;
    }

    private void doSaveQmDataToRefund(QmRestParam param, EshopInfo eshopInfo) {
        try {
            String qmData = qmDataToJson(param, QiMenReturnOrder.class);
            QmRefundEntity apiRefund = JsonUtils.toObject(qmData, QmRefundEntity.class);
            if (apiRefund == null) {
                throw new PlatformInterfaceException("售后消息解析失败");
            }
            Map<String, Object> qmDataMap = JsonUtils.toHashMap(qmData);
            qmDataMap.put(QM_MESSAGE_TYPE, QmMessageType.REFUND.getCode());
            String tmcMessage = JsonUtils.toJson(qmDataMap);
            EshopTmcRefundMsgEntity refundMsgEntity = tmcRefundMsgMapper.queryTmcRefundMsgByRefundId(eshopInfo.getProfileId(),
                    eshopInfo.getOtypeId(), apiRefund.getReturnOrder().getReturnOrderCode());
            if (refundMsgEntity == null) {
                refundMsgEntity = buildEshopTmcRefundMsgEntity(eshopInfo, HttpUtils.urlEncode(tmcMessage), apiRefund);
                tmcRefundMsgMapper.insertTmcRefundMsg(refundMsgEntity);
            } else {
                refundMsgEntity.setMsgUpdateTime(new Date());
                //平台没有返回更新时间，平台推送一次就更新一次
                refundMsgEntity.setUpdateTime(new Date());
                //售后消息只存最新的一份。存在到整个报文头部，和订单取消消息用 & 符合分割。订单取消消息存多份
                String[] msgSplit = refundMsgEntity.getMessage().split(HeMaConstants.SPLIT_CHARACTER);
                msgSplit[0] = HttpUtils.urlEncode(tmcMessage);
                refundMsgEntity.setMessage(StringUtils.join(msgSplit, HeMaConstants.SPLIT_CHARACTER));
                tmcRefundMsgMapper.updateTmcRefundMsg(refundMsgEntity);
            }
            doSendOrderMsg(apiRefund.getReturnOrder().getPreDeliveryOrderCode(), qmData, eshopInfo, TMCType.RefundOrder);
        } catch (Exception ex) {
            logger.error("账套ID:{},店铺ID:{},tmcMessage:{},售后消息处理出错,错误信息：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), param.getQmData(), ex.getMessage(), ex);
            throw new PlatformInterfaceException(ex.getMessage(), ex);
        }
    }

    private void doSaveQmDataToOrder(QmRestParam param, EshopInfo eshopInfo) {
        try {
            String qmData = qmDataToJson(param, QiMenOrder.class);
            QmOrderEntity apiOrder = JsonUtils.toObject(qmData, QmOrderEntity.class);
            if (apiOrder == null) {
                throw new PlatformInterfaceException("订单消息解析失败");
            }
            Map<String, Object> qmDataMap = JsonUtils.toHashMap(qmData);
            qmDataMap.put(QM_MESSAGE_TYPE, QmMessageType.ORDER.getCode());
            String tmcMessage = JsonUtils.toJson(qmDataMap);
            String tradeId = apiOrder.getTradeId();
            EshopTmcOrderMsgEntity orderMsgEntity = tmcMsgMapper.queryTmcOrderMsgByTradeId(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), tradeId);
            if (orderMsgEntity == null) {
                orderMsgEntity = buildEshopTmcOrderMsgEntity(eshopInfo, HttpUtils.urlEncode(tmcMessage), apiOrder);
                tmcMsgMapper.insertTmcOrderMsg(orderMsgEntity);
            } else {
                logger.error("账套ID:{},店铺ID:{},tmcMessage:{},该订单已存在,订单接收失败!", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), param.getQmData());
                return;
            }
            doSendOrderMsg(tradeId, tmcMessage, eshopInfo, TMCType.Order);
        } catch (Exception ex) {
            logger.error("账套ID:{},店铺ID:{},tmcMessage:{},订单消息处理出错,错误信息：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), param.getQmData(), ex.getMessage(), ex);
            throw new PlatformInterfaceException(ex.getMessage(), ex);
        }
    }

    private void doSaveQmDataToOrderOrRefundCancel(QmRestParam param, EshopInfo eshopInfo) {
        String qmData = qmDataToJson(param, QiMenCancelOrder.class);
        QiMenCancelOrder cancelOrder = JsonUtils.toObject(qmData, QiMenCancelOrder.class);
        if (cancelOrder == null) {
            throw new PlatformInterfaceException("取消订单或售后单消息解析失败");
        }
        Map<String, Object> qmDataMap = JsonUtils.toHashMap(qmData);
        qmDataMap.put(QM_MESSAGE_TYPE, QmMessageType.ORDER_CANCEL.getCode());
        //THRK=退货入库  售后单
        if (StringUtils.equals(cancelOrder.getOrderType(), "THRK")) {
            doSaveQmDataToRefundCancel(qmDataMap, cancelOrder.getOrderCode(), eshopInfo);
        } else {
            doSaveQmDataToOrderCancel(qmDataMap, cancelOrder.getOrderCode(), eshopInfo);
        }
    }

    private void doSaveQmDataToRefundCancel(Map<String, Object> qmDataMap, String refundId, EshopInfo eshopInfo) {
        String tmcMessage = JsonUtils.toJson(qmDataMap);
        try {
            EshopTmcRefundMsgEntity refundMsgEntity = tmcRefundMsgMapper.queryTmcRefundMsgByRefundId(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), refundId);
            //售后单都没有下载到
            if (refundMsgEntity == null) {
                return;
            }
            SimpleRefundEntity refund = eshopRefundMapper.getRefundByRefundId(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), refundId);
            if (refund == null) {
                throw new PlatformInterfaceException("ERP未查询到售后单，售后单取消失败!");
            }
            //3=已入库
            if (StringUtils.equals(refund.getReceiveState(), "3")) {
                throw new PlatformInterfaceException("已完成收货入库，售后单取消失败!");
            }
            refundMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            refundMsgEntity.setUpdateTime(new Date());
            //将下发售后消息和取消售后消息用&拼接起来
            refundMsgEntity.setMessage(String.format("%s%s%s", refundMsgEntity.getMessage(), HeMaConstants.SPLIT_CHARACTER, HttpUtils.urlEncode(tmcMessage)));
            tmcMsgMapper.updateTmcRefundMsg(refundMsgEntity);
            doSendOrderMsg(refundMsgEntity.getTradeOrderId(), tmcMessage, eshopInfo, TMCType.RefundOrder);
        } catch (Exception ex) {
            logger.error("账套ID:{},店铺ID:{},tmcMessage:{},取消售后单消息处理出错,错误信息：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), tmcMessage, ex.getMessage(), ex);
            throw new PlatformInterfaceException(ex.getMessage(), ex);
        }
    }


    private void doSaveQmDataToOrderCancel(Map<String, Object> qmDataMap, String tradeId, EshopInfo eshopInfo) {
        String tmcMessage = JsonUtils.toJson(qmDataMap);
        try {
            EshopTmcOrderMsgEntity orderMsgEntity = tmcMsgMapper.queryTmcOrderMsgByTradeId(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), tradeId);
            if (orderMsgEntity == null) {
                return;
            }
            List<CloseOrderRequest> requests = new ArrayList<>();
            CloseOrderRequest request = new CloseOrderRequest();
            request.setEshopId(eshopInfo.getOtypeId());
            request.setTradeOrderId(tradeId);
            requests.add(request);
            List<CloseOrderResponse> closeOrderResponses = eshopOrderMiddleGroundService.closeOrder(requests);
            if (CollectionUtils.isEmpty(closeOrderResponses)) {
                throw new PlatformInterfaceException("关闭订单失败！调用closeOrder响应为空");
            }
            CloseOrderResponse closeOrderResponse = closeOrderResponses.get(0);
            if (!closeOrderResponse.isSuccess()) {
                throw new PlatformInterfaceException(closeOrderResponse.getMsg());
            }
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            //将下发订单消息和取消订单消息用&拼接起来
            orderMsgEntity.setMessage(String.format("%s%s%s", orderMsgEntity.getMessage(), HeMaConstants.SPLIT_CHARACTER, HttpUtils.urlEncode(tmcMessage)));
            tmcMsgMapper.updateTmcOrderMsg(orderMsgEntity);
            doSendOrderMsg(tradeId, tmcMessage, eshopInfo, TMCType.Order);
        } catch (Exception ex) {
            logger.error("账套ID:{},店铺ID:{},tmcMessage:{},取消订单消息处理出错,错误信息：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), tmcMessage, ex.getMessage(), ex);
            throw new PlatformInterfaceException(ex.getMessage(), ex);
        }
    }


    private void doSaveQmDataToGoods(QmRestParam param, EshopInfo eshopInfo) {
        try {
            String qmData = qmDataToJson(param, QiMenProductInfo.class);
            QmGoodsEntity apiProduct = JsonUtils.toObject(qmData, QmGoodsEntity.class);
            if (apiProduct == null) {
                throw new PlatformInterfaceException("商品消息解析失败");
            }
            QmGoodsItem item = apiProduct.getItem();
            EshopTmcProductMsgEntity productMsgEntity = tmcProductMsgMapper.queryTmcProductMsgByNumId(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), item.getItemCode());
            if (productMsgEntity == null) {
                productMsgEntity = buildEshopTmcProductMsgEntity(eshopInfo, qmData, apiProduct);
                tmcProductMsgMapper.insertTmcProductMsg(productMsgEntity);
            } else {
                productMsgEntity.setMsgUpdateTime(DateUtils.parse(item.getUpdateTime()));
                productMsgEntity.setUpdateTime(new Date());
                productMsgEntity.setMessage(qmData);
                tmcProductMsgMapper.updateTmcProductMsg(productMsgEntity);
            }
            doSendProductMsg(item.getItemCode(), qmData, eshopInfo);
        } catch (Exception ex) {
            logger.error("账套ID:{},店铺ID:{},tmcMessage:{},商品消息处理出错,错误信息：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), param.getQmData(), ex.getMessage(), ex);
            throw new PlatformInterfaceException(ex.getMessage(), ex);
        }
    }

    private void doSendOrderMsg(String tradeId, String qmData, EshopInfo eshopInfo, TMCType type) {
        try {
            EshopNotifyChange change = new EshopNotifyChange();
            change.setProfileId(eshopInfo.getProfileId());
            change.setEshopId(eshopInfo.getOtypeId());
            change.setTradeOrderId(tradeId);
            change.setId(UId.newId());
            change.setContent(qmData);
            change.setType(type);
            change.setShopType(ShopType.QiMenSupplier.getCode());
            SupportUtil.saveNotifyChange(change);
            SupportUtil.doWriteTmcMqLog(change, TMCType.Order.getName());
            TmcOrderMessage message = SupportUtil.buildMessage(change);
            List<MqSendResult<TmcOrderMessage>> send = SysMqSend.send(Collections.singletonList(message), SyncOrderConst.TmcTopIcName);
            //SupportUtil.insertNotifyChangeErrorByMqResults(send);
            SupportUtil.insertMqConsumeLogByMqResults(send, message, type);
        } catch (Exception ex) {
            logger.error("账套ID:{},店铺ID:{},tmcMessage:{},发送消息队列出错,错误信息：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), qmData, ex.getMessage(), ex);
        }
    }

    private void doSendProductMsg(String itemCode, String qmData, EshopInfo eshopInfo) {
        try {
            EshopNotifyChange change = new EshopNotifyChange();
            change.setProfileId(eshopInfo.getProfileId());
            change.setEshopId(eshopInfo.getOtypeId());
            change.setTradeOrderId(itemCode);
            change.setId(UId.newId());
            change.setContent(qmData);
            change.setType(TMCType.Ptype);
            change.setShopType(ShopType.QiMenSupplier.getCode());
            SupportUtil.saveNotifyChange(change);
            SupportUtil.doWriteTmcMqLog(change, TMCType.Ptype.getName());
            TmcProductMessage message = new TmcProductMessage();
            message.setType(SyncProductConst.TMC_PRODUCT);
            message.setEshopId(eshopInfo.getOtypeId());
            message.setProfileId(eshopInfo.getProfileId());
            message.setCreateTime(new Date());
            message.setNumId(itemCode);
            message.setNotifyChangeId(change.getId());
            List<MqSendResult<TmcProductMessage>> send = SysMqSend.send(Collections.singletonList(message), config.getTmcProductMqTopic());
            logProductMqLog(send, message);
        } catch (Exception ex) {
            logger.error("账套ID:{},店铺ID:{},tmcMessage:{},发送消息队列出错,错误信息：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), qmData, ex.getMessage(), ex);
        }
    }

    private void logProductMqLog(List<MqSendResult<TmcProductMessage>> send, TmcProductMessage message) {
        MqConsumeLog log = SupportUtil.initMqConsumeLog(message);
        log.setBody(JsonUtils.toJson(message));
        log.setMsgType(TMCType.Ptype);
        log.setTmcId(message.getNumId());
        if (CollectionUtils.isEmpty(send)) {
            log.setState(4);
            log.setErrorMsg("MQ发送发送失败，返回结果为空，原因未知");
            LogService.addNow(log);
            return;
        }
        for (MqSendResult<TmcProductMessage> mqSendResult : send) {
            log.setMsgId(mqSendResult.getMessageId());
            if (!mqSendResult.isSuccess()) {
                log.setErrorMsg(String.format("MQ发送失败，失败原因：%s", mqSendResult.getErrorMsg()));
            }
            LogService.update(log);
        }
    }

    private EshopTmcProductMsgEntity buildEshopTmcProductMsgEntity(EshopInfo eshopInfo, String qmData, QmGoodsEntity apiProduct) {
        EshopTmcProductMsgEntity tmcOrderMsgEntity = new EshopTmcProductMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopInfo.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopInfo.getOtypeId());
        tmcOrderMsgEntity.setShopType(ShopType.QiMenSupplier);
        tmcOrderMsgEntity.setPlatformNumId(apiProduct.getItem().getItemCode());
        tmcOrderMsgEntity.setMessage(qmData);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setMsgCreateTime(DateUtils.parse(apiProduct.getItem().getCreateTime()));
        tmcOrderMsgEntity.setMsgUpdateTime(DateUtils.parse(apiProduct.getItem().getUpdateTime()));
        tmcOrderMsgEntity.setCreateTime(new Date());
        tmcOrderMsgEntity.setUpdateTime(new Date());
        return tmcOrderMsgEntity;
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(EshopInfo eshopInfo, String tmMessage, QmOrderEntity order) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopInfo.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopInfo.getOtypeId());
        tmcOrderMsgEntity.setShopType(ShopType.QiMenSupplier);
        tmcOrderMsgEntity.setTradeOrderId(order.getTradeId());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setMsgCreateTime(DateUtils.parse(order.getCreateTime()));
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setCreateTime(DateUtils.parse(order.getCreateTime()));
        //平台没有返回更新时间，平台推送一次就更新一次
        tmcOrderMsgEntity.setUpdateTime(new Date());
        return tmcOrderMsgEntity;
    }

    private EshopTmcRefundMsgEntity buildEshopTmcRefundMsgEntity(EshopInfo eshopInfo, String tmMessage, QmRefundEntity apiRefund) {
        EshopTmcRefundMsgEntity tmcRefundMsgEntity = new EshopTmcRefundMsgEntity();
        tmcRefundMsgEntity.setId(UId.newId());
        tmcRefundMsgEntity.setProfileId(eshopInfo.getProfileId());
        tmcRefundMsgEntity.setEshopId(eshopInfo.getOtypeId());
        tmcRefundMsgEntity.setShopType(ShopType.QiMenSupplier);
        tmcRefundMsgEntity.setTradeOrderId(apiRefund.getReturnOrder().getPreDeliveryOrderCode());
        tmcRefundMsgEntity.setRefundOrderId(apiRefund.getReturnOrder().getReturnOrderCode());
        tmcRefundMsgEntity.setMessage(tmMessage);
        tmcRefundMsgEntity.setMsgStatus(0);
        tmcRefundMsgEntity.setMsgCreateTime(new Date());
        tmcRefundMsgEntity.setMsgUpdateTime(new Date());
        tmcRefundMsgEntity.setCreateTime(new Date());
        return tmcRefundMsgEntity;
    }
}
