package com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class TokenInfo {
    /**
     * access_token信息
     */
    @JsonProperty("access_token")
    private String accessToken;
    /**
     * access_token信息
     */
    @JsonProperty("refresh_token")
    private String refreshToken;
    /**
     * access_token的过期时间，正数代表剩余有效时间，负数代表已失效时间
     */
    @JsonProperty("expires_in")
    private String expiresIn;
    /**
     * refresh_token的过期时间，正数代表剩余有效时间，负数代表已失效时间
     */
    @JsonProperty("re_expires_in")
    private String reExpiresIn;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(String expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getReExpiresIn() {
        return reExpiresIn;
    }

    public void setReExpiresIn(String reExpiresIn) {
        this.reExpiresIn = reExpiresIn;
    }
}
