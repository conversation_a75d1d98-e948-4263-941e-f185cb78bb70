package com.wsgjp.ct.sale.tool.tmc.impl.taobaomaicai;

import com.taobao.api.internal.spi.CheckResult;
import com.taobao.api.internal.spi.SpiUtils;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.entity.TaobaomaicaiApiResult;
import com.wsgjp.ct.sale.tool.tmc.entity.TaotaomaicaiResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import ngp.utils.JsonUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public abstract class TaobaomaicaiNotifyBase implements MessageHandler {
    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        try {
            CheckResult checkResult = SpiUtils.checkSign(request, eshopTmcConfig.getTaobaomaicaiAppSecret());
            CheckSignResult signResult = new CheckSignResult();
            signResult.setBody(checkResult.getRequestBody());
            signResult.setSuccess(checkResult.isSuccess());
            return signResult;
        } catch (IOException e) {
            CheckSignResult signResult = new CheckSignResult();
            signResult.setSuccess(false);
            return signResult;
        }
    }

    protected String buildTaobaomaicaiResponse(boolean isSuccess, String returnCode, String returnMsg, boolean retry) {
        TaotaomaicaiResponse response = new TaotaomaicaiResponse();
        TaobaomaicaiApiResult apiResult = new TaobaomaicaiApiResult();
        apiResult.setSuccess(isSuccess);
        apiResult.setReturnCode(returnCode);
        apiResult.setReturnMsg(returnMsg);
        response.setApiResult(apiResult);
        return JsonUtils.toJson(response);
    }
}
