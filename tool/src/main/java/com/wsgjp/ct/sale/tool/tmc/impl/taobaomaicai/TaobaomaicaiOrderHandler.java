package com.wsgjp.ct.sale.tool.tmc.impl.taobaomaicai;

import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.taobaomaicai.entity.TaobaomaicaiOrderStatusChangeRequest;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Component
public class TaobaomaicaiOrderHandler extends TaobaomaicaiNotifyBase implements MessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(TaobaomaicaiOrderHandler.class);

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        logger.info("======进入淘宝买菜TaobaomaicaiOrderHandler.invoker方法======");
        String tbMessage = invokeMessage.getMessage();
        TaobaomaicaiOrderStatusChangeRequest request;
        try {
            request = JsonUtils.toObject(tbMessage, TaobaomaicaiOrderStatusChangeRequest.class);
        } catch (Exception ex) {
            logger.error("淘宝买菜消息数据转换成OrderRequest实体出错，错误信息：{}", ex.getMessage());
            return buildTaobaomaicaiResponse(false, "201", "系统异常", false);

        }


        if (request == null ||request.getOrderMessage()==null || StringUtils.isEmpty(request.getOrderMessage().getBizOrderId())) {
            return buildTaobaomaicaiResponse(false, "201", "系统异常", false);
        }
//        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(request.getSupplierNo(), invokeMessage.getShopType().getCode());
//
//        logger.info("淘宝买菜订单变更消息获取账套关系,invokeMessage:{},result:{}", JsonUtils.toJson(invokeMessage),
//                eshopRegister == null ? "null" : JsonUtils.toJson(eshopRegister));
//
//        if(eshopRegister==null || eshopRegister.getProfileId()==null || eshopRegister.getProfileId().compareTo(BigInteger.ZERO)==0)
//        {
//            return buildXmlResponse(false, "202", "根据供应商编码找不到有效的店铺", false);
//        }

        EshopNotifyChange change = new EshopNotifyChange();
//        change.setProfileId(eshopRegister.getProfileId());
//        change.setEshopId(eshopRegister.getId());
        change.setContent(invokeMessage.getMessage());
        change.setTradeOrderId(request.getOrderMessage().getBizOrderId());
        change.setId(UId.newId());
        change.setType(TMCType.Order);
        change.setOnlineShopId(request.getOrderMessage().getSupplierNo());
        change.setCreateTime(new Date());
        try {
            SupportUtil.doNotify(change.getOnlineShopId(), change, invokeMessage.getShopType().getCode());
        } catch (Exception e) {
            logger.error("淘宝买菜,调用业务处理订单消息失败,错误信息:{}", e.getMessage(), e);
            return buildTaobaomaicaiResponse(true,"500" , e.getMessage(), false);
        }
        return buildTaobaomaicaiResponse(true, "0", "success", false);
    }


    @Override
    public String serviceName() {
        return "qimen.alibaba.tbmc.order.status.change";
    }

}
