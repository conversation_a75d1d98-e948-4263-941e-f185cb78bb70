package com.wsgjp.ct.sale.tool.tmc.controller;

import com.wsgjp.ct.sale.platform.utils.StringUtils;

public enum JdongTmcMessageEnums {
    jos_gms_spu_unbind("jdongProductHandler"),
    order_OmnicOrderStatusChange("jdongQQDOrderHandler"),
    order_omincWaybillState("jdongQQDOrderHandler");
    private final String tmcHandler;

    JdongTmcMessageEnums(String tmcHandler) {
        this.tmcHandler = tmcHandler;
    }

    public String getTmcHandler() {
        return tmcHandler;
    }

    public static String getJdongTmcHandler(String topic) {
        if (StringUtils.isEmpty(topic)) {
            return "jdongOrderAndRefundInvoker";
        }
        for (JdongTmcMessageEnums tmcType : JdongTmcMessageEnums.values()) {
            if (topic.equals(tmcType.toString())) {
                return tmcType.getTmcHandler();
            }
        }
        return "jdongOrderAndRefundInvoker";
    }
}
