package com.wsgjp.ct.sale.tool.logo.service.computed.refund.entity;


import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.enums.RefundTypeEnum;
import com.wsgjp.ct.sale.tool.logo.service.base.BaseLogoEntity;

import java.math.BigInteger;
import java.util.Date;


/**
 * <AUTHOR> zsh
 */
public class LogoEntityByRefund extends BaseLogoEntity {
    // ID
    private BigInteger id;
    // 售后类型
    private RefundTypeEnum refundTypeEnum;
    // 售后状态
    private RefundStatus refundState;
    // 退货物流公司
    private String freightName;
    // 退货物流单号
    private String freightNo;
    // 删除状态
    private RefundDeleteStateEnum deleted;
    // 创建方式
    private RefundCreateTypeEnum createType;
    // 审核状态
    private RefundAuditStatus confirmState;
    // 入库状态
    private RefundReceiveStatus receiveState;
    // 退款支付状态
    private RefundPayStatus payState;
    // 0不支持 1支持
    private int agRefund = 0;

    private RefundTypeEnum platformRefundTypeEnum;

    public RefundTypeEnum getPlatformRefundTypeEnum() {
        return platformRefundTypeEnum;
    }

    public void setPlatformRefundTypeEnum(RefundTypeEnum platformRefundTypeEnum) {
        this.platformRefundTypeEnum = platformRefundTypeEnum;
    }

    // 售后平台创建时间
    private Date refundCreateTime;

    public Date getRefundCreateTime() {
        return refundCreateTime;
    }

    public void setRefundCreateTime(Date refundCreateTime) {
        this.refundCreateTime = refundCreateTime;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public int getAgRefund() {
        return agRefund;
    }

    public void setAgRefund(int agRefund) {
        this.agRefund = agRefund;
    }

    public RefundTypeEnum getRefundTypeEnum() {
        return refundTypeEnum;
    }

    public void setRefundTypeEnum(RefundTypeEnum refundTypeEnum) {
        this.refundTypeEnum = refundTypeEnum;
    }

    public RefundStatus getRefundState() {
        return refundState;
    }

    public void setRefundState(RefundStatus refundState) {
        this.refundState = refundState;
    }

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getFreightNo() {
        return freightNo;
    }

    public void setFreightNo(String freightNo) {
        this.freightNo = freightNo;
    }

    public RefundDeleteStateEnum getDeleted() {
        return deleted;
    }

    public void setDeleted(RefundDeleteStateEnum deleted) {
        this.deleted = deleted;
    }

    public RefundCreateTypeEnum getCreateType() {
        return createType;
    }

    public void setCreateType(RefundCreateTypeEnum createType) {
        this.createType = createType;
    }

    public RefundAuditStatus getConfirmState() {
        return confirmState;
    }

    public void setConfirmState(RefundAuditStatus confirmState) {
        this.confirmState = confirmState;
    }

    public RefundReceiveStatus getReceiveState() {
        return receiveState;
    }

    public void setReceiveState(RefundReceiveStatus receiveState) {
        this.receiveState = receiveState;
    }

    public RefundPayStatus getPayState() {
        return payState;
    }

    public void setPayState(RefundPayStatus payState) {
        this.payState = payState;
    }
}
