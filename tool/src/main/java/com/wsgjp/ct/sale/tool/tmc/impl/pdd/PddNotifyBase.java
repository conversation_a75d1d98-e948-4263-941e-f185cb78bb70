package com.wsgjp.ct.sale.tool.tmc.impl.pdd;

import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;

import javax.servlet.http.HttpServletRequest;

public abstract class PddNotifyBase implements MessageHandler {
    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        //拼多多消息是长连接方式推送过来的，无需签名验证
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(true);
        return result;
    }
}
