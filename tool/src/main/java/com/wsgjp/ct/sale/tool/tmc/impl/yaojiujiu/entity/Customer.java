package com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class Customer {
    private String b2BAccounts;
    private String custName;
    private String custType;
    private String taxPayerNumber;
    private String provinceCode;
    private String cityCode;
    private String areaCode;
    private String provinceName;
    private String cityName;
    private String areaName;
    private String businessScopeCode;
    private String custAddress;
    private String companyMan;
    private String companyManMobile;
    private String trusteeName;
    private String trusteeIDNumber;
    private String trusteeValidityStart;
    private String trusteeValidityEnd;
    private Integer trusteeIsLongterm;
    private String invoiceOpenBank;
    private String invoiceBankAccount;
    private String receiveAddressID;
    private String receiveLinkMan;
    private String receiveLinkPhone;
    private String receiveProvinceCode;
    private String receiveProvinceName;
    private String receiveCityCode;
    private String receiveCityName;
    private String receiveAreaCode;
    private String receiveAreaName;
    private String receiveAddressDetail;
    private List<B2BLicenceDet> b2BLicenceDet;

    @JsonProperty("b2bAccounts")
    public String getB2BAccounts() {
        return b2BAccounts;
    }

    @JsonProperty("b2bAccounts")
    public void setB2BAccounts(String value) {
        this.b2BAccounts = value;
    }

    @JsonProperty("custName")
    public String getCustName() {
        return custName;
    }

    @JsonProperty("custName")
    public void setCustName(String value) {
        this.custName = value;
    }

    @JsonProperty("custType")
    public String getCustType() {
        return custType;
    }

    @JsonProperty("custType")
    public void setCustType(String value) {
        this.custType = value;
    }

    @JsonProperty("taxPayerNumber")
    public String getTaxPayerNumber() {
        return taxPayerNumber;
    }

    @JsonProperty("taxPayerNumber")
    public void setTaxPayerNumber(String value) {
        this.taxPayerNumber = value;
    }

    @JsonProperty("provinceCode")
    public String getProvinceCode() {
        return provinceCode;
    }

    @JsonProperty("provinceCode")
    public void setProvinceCode(String value) {
        this.provinceCode = value;
    }

    @JsonProperty("cityCode")
    public String getCityCode() {
        return cityCode;
    }

    @JsonProperty("cityCode")
    public void setCityCode(String value) {
        this.cityCode = value;
    }

    @JsonProperty("areaCode")
    public String getAreaCode() {
        return areaCode;
    }

    @JsonProperty("areaCode")
    public void setAreaCode(String value) {
        this.areaCode = value;
    }

    @JsonProperty("provinceName")
    public String getProvinceName() {
        return provinceName;
    }

    @JsonProperty("provinceName")
    public void setProvinceName(String value) {
        this.provinceName = value;
    }

    @JsonProperty("cityName")
    public String getCityName() {
        return cityName;
    }

    @JsonProperty("cityName")
    public void setCityName(String value) {
        this.cityName = value;
    }

    @JsonProperty("areaName")
    public String getAreaName() {
        return areaName;
    }

    @JsonProperty("areaName")
    public void setAreaName(String value) {
        this.areaName = value;
    }

    @JsonProperty("businessScopeCode")
    public String getBusinessScopeCode() {
        return businessScopeCode;
    }

    @JsonProperty("businessScopeCode")
    public void setBusinessScopeCode(String value) {
        this.businessScopeCode = value;
    }

    @JsonProperty("custAddress")
    public String getCustAddress() {
        return custAddress;
    }

    @JsonProperty("custAddress")
    public void setCustAddress(String value) {
        this.custAddress = value;
    }

    @JsonProperty("companyMan")
    public String getCompanyMan() {
        return companyMan;
    }

    @JsonProperty("companyMan")
    public void setCompanyMan(String value) {
        this.companyMan = value;
    }

    @JsonProperty("companyManMobile")
    public String getCompanyManMobile() {
        return companyManMobile;
    }

    @JsonProperty("companyManMobile")
    public void setCompanyManMobile(String value) {
        this.companyManMobile = value;
    }

    @JsonProperty("trusteeName")
    public String getTrusteeName() {
        return trusteeName;
    }

    @JsonProperty("trusteeName")
    public void setTrusteeName(String value) {
        this.trusteeName = value;
    }

    @JsonProperty("trusteeIdNumber")
    public String getTrusteeIDNumber() {
        return trusteeIDNumber;
    }

    @JsonProperty("trusteeIdNumber")
    public void setTrusteeIDNumber(String value) {
        this.trusteeIDNumber = value;
    }

    @JsonProperty("trusteeValidityStart")
    public String getTrusteeValidityStart() {
        return trusteeValidityStart;
    }

    @JsonProperty("trusteeValidityStart")
    public void setTrusteeValidityStart(String value) {
        this.trusteeValidityStart = value;
    }

    @JsonProperty("trusteeValidityEnd")
    public String getTrusteeValidityEnd() {
        return trusteeValidityEnd;
    }

    @JsonProperty("trusteeValidityEnd")
    public void setTrusteeValidityEnd(String value) {
        this.trusteeValidityEnd = value;
    }

    @JsonProperty("trusteeIsLongterm")
    public Integer getTrusteeIsLongterm() {
        return trusteeIsLongterm;
    }

    @JsonProperty("trusteeIsLongterm")
    public void setTrusteeIsLongterm(Integer value) {
        this.trusteeIsLongterm = value;
    }

    @JsonProperty("invoiceOpenBank")
    public String getInvoiceOpenBank() {
        return invoiceOpenBank;
    }

    @JsonProperty("invoiceOpenBank")
    public void setInvoiceOpenBank(String value) {
        this.invoiceOpenBank = value;
    }

    @JsonProperty("invoiceBankAccount")
    public String getInvoiceBankAccount() {
        return invoiceBankAccount;
    }

    @JsonProperty("invoiceBankAccount")
    public void setInvoiceBankAccount(String value) {
        this.invoiceBankAccount = value;
    }

    @JsonProperty("receiveAddressId")
    public String getReceiveAddressID() {
        return receiveAddressID;
    }

    @JsonProperty("receiveAddressId")
    public void setReceiveAddressID(String value) {
        this.receiveAddressID = value;
    }

    @JsonProperty("receiveLinkMan")
    public String getReceiveLinkMan() {
        return receiveLinkMan;
    }

    @JsonProperty("receiveLinkMan")
    public void setReceiveLinkMan(String value) {
        this.receiveLinkMan = value;
    }

    @JsonProperty("receiveLinkPhone")
    public String getReceiveLinkPhone() {
        return receiveLinkPhone;
    }

    @JsonProperty("receiveLinkPhone")
    public void setReceiveLinkPhone(String value) {
        this.receiveLinkPhone = value;
    }

    @JsonProperty("receiveProvinceCode")
    public String getReceiveProvinceCode() {
        return receiveProvinceCode;
    }

    @JsonProperty("receiveProvinceCode")
    public void setReceiveProvinceCode(String value) {
        this.receiveProvinceCode = value;
    }

    @JsonProperty("receiveProvinceName")
    public String getReceiveProvinceName() {
        return receiveProvinceName;
    }

    @JsonProperty("receiveProvinceName")
    public void setReceiveProvinceName(String value) {
        this.receiveProvinceName = value;
    }

    @JsonProperty("receiveCityCode")
    public String getReceiveCityCode() {
        return receiveCityCode;
    }

    @JsonProperty("receiveCityCode")
    public void setReceiveCityCode(String value) {
        this.receiveCityCode = value;
    }

    @JsonProperty("receiveCityName")
    public String getReceiveCityName() {
        return receiveCityName;
    }

    @JsonProperty("receiveCityName")
    public void setReceiveCityName(String value) {
        this.receiveCityName = value;
    }

    @JsonProperty("receiveAreaCode")
    public String getReceiveAreaCode() {
        return receiveAreaCode;
    }

    @JsonProperty("receiveAreaCode")
    public void setReceiveAreaCode(String value) {
        this.receiveAreaCode = value;
    }

    @JsonProperty("receiveAreaName")
    public String getReceiveAreaName() {
        return receiveAreaName;
    }

    @JsonProperty("receiveAreaName")
    public void setReceiveAreaName(String value) {
        this.receiveAreaName = value;
    }

    @JsonProperty("receiveAddressDetail")
    public String getReceiveAddressDetail() {
        return receiveAddressDetail;
    }

    @JsonProperty("receiveAddressDetail")
    public void setReceiveAddressDetail(String value) {
        this.receiveAddressDetail = value;
    }

    @JsonProperty("b2bLicenceDet")
    public List<B2BLicenceDet> getB2BLicenceDet() {
        return b2BLicenceDet;
    }

    @JsonProperty("b2bLicenceDet")
    public void setB2BLicenceDet(List<B2BLicenceDet> value) {
        this.b2BLicenceDet = value;
    }


    public static class B2BLicenceDet {
        private Long issuingDate;
        private String licenceID;
        private String licenceName;
        private String licencePicURL;
        private String licenseNo;
        private Long expiryDate;
        private String isEffective;

        @JsonProperty("issuingDate")
        public Long getIssuingDate() {
            return issuingDate;
        }

        @JsonProperty("issuingDate")
        public void setIssuingDate(Long value) {
            this.issuingDate = value;
        }

        @JsonProperty("licenceID")
        public String getLicenceID() {
            return licenceID;
        }

        @JsonProperty("licenceID")
        public void setLicenceID(String value) {
            this.licenceID = value;
        }

        @JsonProperty("licenceName")
        public String getLicenceName() {
            return licenceName;
        }

        @JsonProperty("licenceName")
        public void setLicenceName(String value) {
            this.licenceName = value;
        }

        @JsonProperty("licencePicUrl")
        public String getLicencePicURL() {
            return licencePicURL;
        }

        @JsonProperty("licencePicUrl")
        public void setLicencePicURL(String value) {
            this.licencePicURL = value;
        }

        @JsonProperty("licenseNo")
        public String getLicenseNo() {
            return licenseNo;
        }

        @JsonProperty("licenseNo")
        public void setLicenseNo(String value) {
            this.licenseNo = value;
        }

        @JsonProperty("expiryDate")
        public Long getExpiryDate() {
            return expiryDate;
        }

        @JsonProperty("expiryDate")
        public void setExpiryDate(Long value) {
            this.expiryDate = value;
        }

        @JsonProperty("isEffective")
        public String getIsEffective() {
            return isEffective;
        }

        @JsonProperty("isEffective")
        public void setIsEffective(String value) {
            this.isEffective = value;
        }
    }
}