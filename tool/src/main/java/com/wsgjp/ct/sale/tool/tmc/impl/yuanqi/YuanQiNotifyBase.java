package com.wsgjp.ct.sale.tool.tmc.impl.yuanqi;

import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.entity.YQPurchaseMessage;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;

public abstract class YuanQiNotifyBase implements MessageHandler {
    private static final Logger sysLogger = LoggerFactory.getLogger(YuanQiNotifyBase.class);
    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(true);
        return result;
    }


    protected void checkMsg(boolean notValid, String msg) {
        if (notValid) {
            throw new RuntimeException(msg);
        }
    }

    protected YQPurchaseMessage getYQMsg(String message) {
        try {
            checkMsg(StringUtils.isEmpty(message), "元气消息为空");
            YQPurchaseMessage yqOrder = JsonUtils.toObject(message, YQPurchaseMessage.class);
            checkMsg(yqOrder == null, "元气消息为空");
            checkMsg(yqOrder.getBody() == null, "元气body内容为空");
            return yqOrder;
        }
        catch (Exception ex){
            sysLogger.error("反序列化消息为实体报错：{}，消息为：{}",ex.getMessage(),message);
            throw new RuntimeException(ex.getMessage());
        }
    }
}
