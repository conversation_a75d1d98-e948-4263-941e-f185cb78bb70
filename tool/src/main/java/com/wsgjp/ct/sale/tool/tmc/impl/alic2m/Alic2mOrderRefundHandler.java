package com.wsgjp.ct.sale.tool.tmc.impl.alic2m;

import com.wsgjp.ct.sale.biz.eshoporder.entity.log.SystemLog;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.sdk.entity.Etype;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopBaseInfoMapper;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibaba.AlibabaNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.impl.alic2m.entity.Alic2mEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alic2m.entity.Alic2mRes;
import com.wsgjp.ct.sale.tool.tmc.impl.alic2m.entity.Alic2mResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import ngp.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class Alic2mOrderRefundHandler extends AlibabaNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(Alic2mOrderRefundHandler.class);
    private final EshopTmcUtils eshopTmcUtils;
    private final TmcNotifyProxy notifyProxy;
    public Alic2mOrderRefundHandler(EshopTmcUtils eshopTmcUtils, TmcNotifyProxy notifyProxy) {
        this.eshopTmcUtils = eshopTmcUtils;
        this.notifyProxy = notifyProxy;
    }

    /**
     * 订单消息相关
     */

    private final String ASCP_ASCP_ORDER_ANNOUNCE_SENDGOODS = "ASCP_ASCP_ORDER_ANNOUNCE_SENDGOODS";
    private final String ASCP_ASCP_ORDER_COMFIRM_RECEIVEGOODS = "ASCP_ASCP_ORDER_COMFIRM_RECEIVEGOODS";

    private final String ASCP_ASCP_ORDER_PAY = "ASCP_ASCP_ORDER_PAY";

    private final String ASCP_ASCP_ORDER_SUCCESS = "ASCP_ASCP_ORDER_SUCCESS";

    /**
     * 售后单消息相关
     */
    private final String ASCP_ASCP_ORDER_BUYER_REFUND_APPLY_IN_SALES = "ASCP_ASCP_ORDER_BUYER_REFUND_APPLY_IN_SALES";
    private final String ASCP_ASCP_ORDER_BUYER_REFUND_CLOSED_IN_SALES = "ASCP_ASCP_ORDER_BUYER_REFUND_CLOSED_IN_SALES";

    private final String ASCP_ASCP_ORDER_BUYER_REFUND_SUCCESS = "ASCP_ASCP_ORDER_BUYER_REFUND_SUCCESS";

    /**
     * 订单备注相关
     */
    private final String ORDER_TGC_ORDER_SELLER_MEMO = "ORDER_TGC_ORDER_SELLER_MEMO";


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
//        try {
//            String encode = PlatformEventSecurityUtil.encode("{\n" +
//                    "    \"eventId\":\"12312\",\n" +
//                    "    \"bizId\":2232700121995010,\n" +
//                    "    \"openId\":\"f198e2b6591c7e0314bf5724b80aaca5\",\n" +
//                    "    \"event\":\"kwaishop_order_statusChange\",\n" +
//                    "    \"info\":{\n" +
//                    "        \"oid\":2232700121995010,\n" +
//                    "        \"sellerId\":123,\n" +
//                    "        \"openId\":\"f198e2b6591c7e0314bf5724b80aaca5\",\n" +
//                    "        \"status\":30,\n" +
//                    "        \"beforeStatus\":80,\n" +
//                    "        \"updateTime\":1669617309000\n" +
//                    "    },\n" +
//                    "    \"createTime\":1669617309000\n" +
//                    "}", "6bKH1VD4Td7sCgTF6FdJJw==");
//            System.out.println(encode);
//        }catch (Exception e){
//
//        }

        Alic2mResponse response;
        Alic2mRes res = new Alic2mRes();
        try {
            response = JsonUtils.toObject(tmMessage, Alic2mResponse.class);
            Alic2mEntity entity = response.getData();
            if (entity == null) {
                res.setSuccess(false);
                res.setError_code(204);
                res.setError_msg("Json转实体失败");
                return JsonUtils.toJson(res);
            }
            BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
            if (BigInteger.ZERO.equals(invokeMessage.getEshopId())){
                // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                EshopRegisterNotify notify = SupportUtil.buildNotify(entity.getSupplierID(), 82);
                if (notify == null || notify.getId()==null|| notify.getId().equals(BigInteger.ZERO)){
                    LOGGER.error("淘工厂消息处理失败：eshopId拿取失败");
                    res.setSuccess(false);
                    res.setError_code(201);
                    res.setError_msg("店铺id寻找失败");
                    return JsonUtils.toJson(res);
                }
                eshopId=notify.getId();
            }else {
                eshopId = invokeMessage.getEshopId();
            }
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
            if(Objects.isNull(eshopInfo)){
                LOGGER.info("profileId:{},eshopId:{},店铺类型:{},淘工厂查询店铺信息为空!",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName);
                res.setSuccess(false);
                res.setError_code(202);
                res.setError_msg("店铺信息获取失败");
                return JsonUtils.toJson(res);
            }

            if(ORDER_TGC_ORDER_SELLER_MEMO.equals(response.getType())){
                // 判断只处理订单备注
                if (!"SALE_ORDER".equals(entity.getMemoType())){
                    res.setSuccess(true);
                    return JsonUtils.toJson(res);
                }
                EshopNotifyChange change = handleMessage(response,entity,entity.getSourceTradeID());
                SupportUtil.doOrderNotify(entity.getSupplierID(),change,eshopInfo.getEshopType().getCode());
                res.setSuccess(true);
                return JsonUtils.toJson(res);
            }
            // 由于消息接口给的是数组
            if (CollectionUtils.isNotEmpty(entity.getBizOrderID())){
                for (String orderId : entity.getBizOrderID()) {
                    EshopNotifyChange change = handleMessage(response,entity,orderId);
                    SupportUtil.doOrderNotify(entity.getSupplierID(),change,eshopInfo.getEshopType().getCode());
                }
                res.setSuccess(true);
                return JsonUtils.toJson(res);
            }
            res.setSuccess(false);
            res.setError_code(204);
            res.setError_msg("订单号为空");
            return JsonUtils.toJson(res);
        } catch (Exception e) {
            LOGGER.error("{}未知错误，错误信息：{}",shopTypeName,e.getMessage());
            res.setSuccess(false);
            res.setError_code(205);
            res.setError_msg("未知异常:" +  e.getMessage());
            return JsonUtils.toJson(res);
        }

    }
    //账号改密码（ACCOUNT_PASSWORD_MODIFIED）、订购到期（ORDER_EXPIRED）、子账号被停用（SUB_ACCOUNT_INACTIVE）、子账号被删除（SUB_ACCOUNT_DELETED）、账号被安全处罚（ACCOUNT_PUBLISHED）、单用户对接应用授权账号被修改（BOND_ACCOUNT_MODIFIED）、手动取消账号授权（MANUAL_CANCEL）
    private String getMsg(String operation) {
        switch (operation){
            case "ACCOUNT_PASSWORD_MODIFIED":return "账号改密码";
            case "ORDER_EXPIRED":return "订购到期";
            case "SUB_ACCOUNT_INACTIVE":return "子账号被停用";
            case "SUB_ACCOUNT_DELETED":return "子账号被删除";
            case "ACCOUNT_PUBLISHED":return "账号被安全处罚";
            case "BOND_ACCOUNT_MODIFIED":return "单用户对接应用授权账号被修改";
            case "MANUAL_CANCEL":return "手动取消账号授权";
        }
        return "未知原因";
    }
    private SystemLog buildEshopConfigLog(String logMsg) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        SystemLog baseInfoLog = new SystemLog();
        baseInfoLog.setProfileId(profileId);
        baseInfoLog.setEtypeId(employeeId);
        baseInfoLog.setId(UId.newId());
        baseInfoLog.setBody(logMsg);
        baseInfoLog.setEfullname("工具");
        baseInfoLog.setLogTime(DateUtils.getDate());
        baseInfoLog.setIp(IpUtils.getLocalHostIp());
        return baseInfoLog;
    }
    private List<BigInteger> getetypeList(BigInteger profileId) {
        PlatformSdkEshopBaseInfoMapper baseInfoMapper = BeanUtils.getBean(PlatformSdkEshopBaseInfoMapper.class);
        List<Etype> etypeInfoList = baseInfoMapper.getEtypeList(profileId);

        List<BigInteger> etypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(etypeInfoList)) {
            etypeList = etypeInfoList.stream().map(Etype::getId).collect(Collectors.toList());
        }
        return etypeList;
    }

    private EshopNotifyChange handleMessage(Alic2mResponse msg,Alic2mEntity order,String orderId) {
        if (msg == null || StringUtils.isEmpty(msg.getType())) {
            return null;
        }
        EshopNotifyChange changeInfo;
        switch (msg.getType()) {
            case ASCP_ASCP_ORDER_BUYER_REFUND_SUCCESS:
            case ASCP_ASCP_ORDER_BUYER_REFUND_CLOSED_IN_SALES:
            case ASCP_ASCP_ORDER_BUYER_REFUND_APPLY_IN_SALES:
            case ASCP_ASCP_ORDER_SUCCESS:
            case ASCP_ASCP_ORDER_PAY:
            case ASCP_ASCP_ORDER_COMFIRM_RECEIVEGOODS:
            case ASCP_ASCP_ORDER_ANNOUNCE_SENDGOODS:
                changeInfo = handleMessageByType(msg,TMCType.Order,order,orderId);
                break;
            case ORDER_TGC_ORDER_SELLER_MEMO:
                changeInfo = handleMessageByType(msg,TMCType.SERIES_FIELD_CHANGE,order,orderId);
                break;
            default:
                throw new RuntimeException("erp未接入此类型消息处理:"+msg.getType());
        }
        return changeInfo;
    }

    private EshopNotifyChange handleMessageByType(Alic2mResponse msg, TMCType tmcType, Alic2mEntity order, String orderId) {
        if (TMCType.AUTHORIZATION_CANCEL == tmcType){
            EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
            eshopNotifyChange.setOnlineShopId(msg.getUserInfo());
            eshopNotifyChange.setTradeOrderId(StringUtils.isEmpty(order.getSupplierID()) ? "" : order.getSupplierID());
            eshopNotifyChange.setType(TMCType.AUTHORIZATION_CANCEL);
            eshopNotifyChange.setContent(JsonUtils.toJson(msg));
            return eshopNotifyChange;
        }
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(JsonUtils.toJson(msg.getData()));
        // 目前只支持订单号下载
        change.setTradeOrderId(orderId);
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(order.getSupplierID());
        change.setCreateTime(new Date());
        return change;
    }



    @Override
    public String serviceName() {
        return "alic2mInvoker";
    }

}
