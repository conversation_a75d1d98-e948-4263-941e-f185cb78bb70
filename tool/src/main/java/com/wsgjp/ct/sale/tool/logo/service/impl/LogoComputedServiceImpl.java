package com.wsgjp.ct.sale.tool.logo.service.impl;

import com.wsgjp.ct.sale.biz.common.entity.LogoExceptionChange;
import com.wsgjp.ct.sale.biz.common.entity.LogoExceptionState;
import com.wsgjp.ct.sale.sdk.logo.state.LogoSourceTypeEnum;
import com.wsgjp.ct.sale.tool.logo.config.LogoCalculateConfig;
import com.wsgjp.ct.sale.tool.logo.service.LogoComputedService;
import com.wsgjp.ct.sale.tool.logo.service.base.BaseLogoComputedAbstract;
import com.wsgjp.ct.sale.tool.logo.service.base.LogoFactory;
import com.wsgjp.ct.sale.tool.logo.util.LogoUtil;
import ngp.utils.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-03-15 10:14
 */
@Service
public class LogoComputedServiceImpl implements LogoComputedService {

    private LogoFactory logoFactory;
    Logger logger = LoggerFactory.getLogger(LogoComputedServiceImpl.class);

    public LogoComputedServiceImpl(LogoFactory logoFactory) {
        this.logoFactory = logoFactory;
    }

    @Override
    public void computed(List<LogoExceptionState> result, BigInteger profileId, List<LogoExceptionChange> changeList) {
        if (CollectionUtils.isEmpty(changeList)) {
            return;
        }
        Map<LogoSourceTypeEnum, List<LogoExceptionChange>> sourceTypeMap = changeList.stream().collect(Collectors.groupingBy(LogoExceptionChange::getSourceType));
        for (Map.Entry<LogoSourceTypeEnum, List<LogoExceptionChange>> entry : sourceTypeMap.entrySet()) {
//            LogoSourceTypeEnum sourceType = entry.getKey();
//            if (LogoSourceTypeEnum.transmit.size() == 0) {
//                logger.error("LogoSourceTypeEnum.transmit为空");
//                synchronized (LogoSourceTypeEnum.transmit) {
//                    LogoSourceTypeEnum.transmit.put(LogoSourceTypeEnum.DELIVER_BILL, new LogoSourceTypeEnum[]{LogoSourceTypeEnum.TASK_BILL});
//                }
//            }
//            LogoSourceTypeEnum[] logoSourceTypeEnums = LogoSourceTypeEnum.transmit.get(sourceType);
//            ArrayList<LogoSourceTypeEnum> arrayList = new ArrayList<>();
//            arrayList.add(sourceType);
//            if (logoSourceTypeEnums != null) {
//                arrayList.addAll(CollectionUtils.newArrayList(logoSourceTypeEnums));
//            }
//            for (LogoSourceTypeEnum logoSourceTypeEnum : arrayList) {
//                try {
//                    doComputed(result, profileId, entry, logoSourceTypeEnum);
//                } catch (Exception exception) {
//                    logger.error("执行徽标计算失败,终止计算", exception);
//                    throw exception;
//                }
//            }
            try {
                doComputed(result, profileId, entry, entry.getKey());
            } catch (Exception exception) {
                logger.error("执行徽标计算失败,终止计算", exception);
                throw exception;
            }
        }
    }

    private void doComputed(List<LogoExceptionState> result, BigInteger profileId, Map.Entry<LogoSourceTypeEnum, List<LogoExceptionChange>> entry, LogoSourceTypeEnum sourceType) {
        BaseLogoComputedAbstract computed = logoFactory.getComputed(sourceType);
        if (null!=computed) {
            List<LogoExceptionChange> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                //去重
                List<BigInteger> billIds = value.stream().map(LogoExceptionChange::getVchcode).distinct().collect(Collectors.toList());
                List logoDataList = computed.getLogoData(profileId, billIds);
                LogoSourceTypeEnum sourceTypeWriteState = computed.getSourceType();
                if (CollectionUtils.isNotEmpty(logoDataList) && null!=sourceTypeWriteState) {
                    computed.computed(result,logoDataList,sourceTypeWriteState);
                }
            }
        }
    }
    @Override
    public void buildChangeListByTdBatch(List<LogoExceptionChange> changeAllList, BigInteger profileId, int pageIndex, int pageCount, Date startTime, Date endTime, LogoCalculateConfig config){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LogoSourceTypeEnum[] values = config.getLogoSourceTypeEnums()==null||config.getLogoSourceTypeEnums().length==0 ? LogoSourceTypeEnum.values() :config.getLogoSourceTypeEnums();
        for (LogoSourceTypeEnum sourceType : values) {
            if (!sourceType.isAuto()) {
                BaseLogoComputedAbstract computed = logoFactory.getComputed(sourceType);
                if (null!=computed) {
                    List<BigInteger> allVchcode;
                    allVchcode = computed.getAllVchcodeBatchDay(profileId,pageIndex,pageCount,formatter.format(startTime),formatter.format(endTime));
                    if (CollectionUtils.isNotEmpty(allVchcode)) {
                        LogoUtil.buildReQueueList(changeAllList, profileId, allVchcode, computed.getSourceType());
                    }
                }
            }
        }
    }

    @Override
    public void buildChangeListByTdBatch(List<LogoExceptionChange> changeAllList, BigInteger profileId, int pageIndex, int pageCount,
                                         boolean computeAllDayNumEnabled, int computeAllDayNum, Date computeAllEndDate) {
        for (LogoSourceTypeEnum sourceType : LogoSourceTypeEnum.values()) {
            if (!sourceType.isAuto()) {
                BaseLogoComputedAbstract computed = logoFactory.getComputed(sourceType);
                if (null!=computed) {
                    List<BigInteger> allVchcode;
                    if (computeAllDayNumEnabled) {
                        Date endDay = getEndDay(computeAllEndDate);
                        String startTime = buildStartTime(computeAllDayNum,endDay);
                        String endTime = buildEndTime(endDay);
                        allVchcode = computed.getAllVchcodeBatchDay(profileId,pageIndex,pageCount,startTime,endTime);
                    }else{
                        allVchcode = computed.getAllVchcodeBatch(profileId,pageIndex,pageCount);
                    }
                    if (CollectionUtils.isNotEmpty(allVchcode)) {
                        LogoUtil.buildNewChangeList(changeAllList, profileId, allVchcode, sourceType);
                    }
                }
            }
        }
    }

    private Date getEndDay(Date computeAllEndDate) {
        Calendar calendar = Calendar.getInstance();

        Date endDay = null;
        if (null==computeAllEndDate) {
            // 获取当前日期
            endDay = calendar.getTime();
        }else{
            endDay = computeAllEndDate;
        }

        return endDay;
    }

    private String buildEndTime(Date endDay) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(endDay);
    }

    private String buildStartTime(int computeAllDayNum, Date endDay){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endDay);
        calendar.add(Calendar.DAY_OF_MONTH, -(computeAllDayNum-1));
        Date startDay = calendar.getTime();

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(startDay);
    }

    @Override
    public void buildReQueueListByState(List<LogoExceptionChange> changeAllList, BigInteger profileId, List<LogoExceptionState> exceptionStateList) {
        if (CollectionUtils.isEmpty(exceptionStateList)) {
            return;
        }

        Map<LogoSourceTypeEnum, List<LogoExceptionState>> map = exceptionStateList.stream().collect(Collectors.groupingBy(LogoExceptionState::getSourceType));
        for (Map.Entry<LogoSourceTypeEnum, List<LogoExceptionState>> entry : map.entrySet()) {
            LogoSourceTypeEnum sourceType = entry.getKey();
            BaseLogoComputedAbstract computed = logoFactory.getComputed(sourceType);
            if (null!=computed) {
//                LogoSourceTypeEnum toolComputeSourceType = computed.getSourceTypeWriteState();
                if (null != computed.getSourceType()) {
                    List<LogoExceptionState> allVchcodeList = entry.getValue();
                    if (CollectionUtils.isNotEmpty(allVchcodeList)) {
                        List<BigInteger> allVchcode = allVchcodeList.stream().map(LogoExceptionState::getVchcode).distinct().collect(Collectors.toList());
                        LogoUtil.buildReQueueList(changeAllList, profileId, allVchcode, computed.getSourceType());
                    }
                }
            }
        }

    }
}
