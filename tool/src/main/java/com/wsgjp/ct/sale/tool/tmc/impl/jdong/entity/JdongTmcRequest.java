package com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity;

/**
 * <AUTHOR>
 */
public class JdongTmcRequest {

    /**
     * 商家编号(商家ID)
     */
    private String venderId;
    /**
     * 商家编号(商家ID)
     * 售后消息中的商家ID
     */
    private String sellerId;
    /**
     * 商家编号(商家ID)
     * pop_order_promise(pop订单时效)消息中的商家ID
     */
    private String vender;
    /**
     * 商家ID或供应商简码(AFS_StepResult_JOS 售后服务单全流程消息通知)
     */
    private String buId;
    /**
     * 京东全渠道消息的商家ID
     * 租户ID
     */
    private String tenantId;

    private String topic;

    public String getVenderId() {
        return venderId;
    }

    public void setVenderId(String venderId) {
        this.venderId = venderId;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getVender() {
        return vender;
    }

    public void setVender(String vender) {
        this.vender = vender;
    }

    public String getBuId() {
        return buId;
    }

    public void setBuId(String buId) {
        this.buId = buId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}
