package com.wsgjp.ct.sale.tool.tmc.impl.bilibili.entity;

import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class BiLiBiLiMessage {
    private String event;
    private String timestamp;
    private MessageContent content;

    public String getTradeId() {
        if (content != null) {
            if (StringUtils.isNotBlank(content.getpId())) {
                return content.getpId();
            } else {
                return content.getOrderId();
            }
        }
        return null;
    }

    public String getShopId() {
        if (content != null) {
            return content.getShopId();
        }
        return null;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public MessageContent getContent() {
        return content;
    }

    public void setContent(MessageContent content) {
        this.content = content;
    }
}
