package com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity;

/**
 * 门店相关信息
 * <AUTHOR>
 */
public class PoiInfo {
    /**
     * APP方门店id，传商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
     */
    private String appPoiCode;
    /**
     * 美团门店名称
     */
    private String poiName;
    /**
     * 美团门店id
     */
    private Long wmPoiId;
    /**
     * 门店上线状态：0-下线；1-上线；2-上单中；3-审核通过可上线。
     */
    private int poiStatus;


    public String getAppPoiCode() {
        return appPoiCode;
    }

    public void setAppPoiCode(String appPoiCode) {
        this.appPoiCode = appPoiCode;
    }

    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }

    public Long getWmPoiId() {
        return wmPoiId;
    }

    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    public int getPoiStatus() {
        return poiStatus;
    }

    public void setPoiStatus(int poiStatus) {
        this.poiStatus = poiStatus;
    }
}
