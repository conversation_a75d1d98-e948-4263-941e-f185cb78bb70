package com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl.entity.order;



import java.math.BigDecimal;

public class Request {
    private String sellerNick;
    private String supplierName;
    private int orderSource;
    private String supplierId;
    private String extraContent;
    private String orderCreateTime;
    private ReceiverInfo receiverInfo;
    private Orderitems orderItems;
    private BigDecimal postFee;
    private String bizOrderCode;
    private String tmsServiceName;
    private String klTradeId;
    private String buyerMessage;
    private String sellerId;
    private String itemsValue;
    private String postMode;
    private String storeName;
    private DeliverRequirement deliverRequirement;
    private String tcOrderId;
    private String tcSubOrderId;
    private String storeCode;


    public String getTcOrderId() {
        return tcOrderId;
    }

    public void setTcOrderId(String tcOrderId) {
        this.tcOrderId = tcOrderId;
    }

    public String getTcSubOrderId() {
        return tcSubOrderId;
    }

    public void setTcSubOrderId(String tcSubOrderId) {
        this.tcSubOrderId = tcSubOrderId;
    }

    public Orderitems getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(Orderitems orderItems) {
        this.orderItems = orderItems;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public int getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(int orderSource) {
        this.orderSource = orderSource;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getExtraContent() {
        return extraContent;
    }

    public void setExtraContent(String extraContent) {
        this.extraContent = extraContent;
    }

    public String getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(String orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public ReceiverInfo getReceiverInfo() {
        return receiverInfo;
    }

    public void setReceiverInfo(ReceiverInfo receiverInfo) {
        this.receiverInfo = receiverInfo;
    }

    public BigDecimal getPostFee() {
        return postFee;
    }

    public void setPostFee(BigDecimal postFee) {
        this.postFee = postFee;
    }

    public String getBizOrderCode() {
        return bizOrderCode;
    }

    public void setBizOrderCode(String bizOrderCode) {
        this.bizOrderCode = bizOrderCode;
    }

    public String getTmsServiceName() {
        return tmsServiceName;
    }

    public void setTmsServiceName(String tmsServiceName) {
        this.tmsServiceName = tmsServiceName;
    }

    public String getKlTradeId() {
        return klTradeId;
    }

    public void setKlTradeId(String klTradeId) {
        this.klTradeId = klTradeId;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getItemsValue() {
        return itemsValue;
    }

    public void setItemsValue(String itemsValue) {
        this.itemsValue = itemsValue;
    }

    public String getPostMode() {
        return postMode;
    }

    public void setPostMode(String postMode) {
        this.postMode = postMode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public DeliverRequirement getDeliverRequirement() {
        return deliverRequirement;
    }

    public void setDeliverRequirement(DeliverRequirement deliverRequirement) {
        this.deliverRequirement = deliverRequirement;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }
}
