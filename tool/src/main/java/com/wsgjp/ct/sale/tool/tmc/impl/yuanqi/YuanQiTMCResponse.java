package com.wsgjp.ct.sale.tool.tmc.impl.yuanqi;

import com.wsgjp.ct.sale.platform.utils.StringUtils;

public class YuanQiTMCResponse {
    public YuanQiTMCResponse(int error, String message) {
        this.error = error;
        this.message = message;
    }

    public YuanQiTMCResponse() {
        this.error = error;
        this.message = message;
    }

    /**
     * 0=成功，其他为失败
     */
    private int error;
    private String message;

    public int getError() {
        return error;
    }

    public void setError(int error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isError() {
        String success = "success";
        int successCode = 0;
        if (StringUtils.isEmpty(message)) {
            return false;
        } else if (!StringUtils.isEmpty(message) && success.equals(message)) {
            return false;
        } else if (successCode == error) {
            return false;
        }
        return true;
    }
}
