package com.wsgjp.ct.sale.tool.tmc.impl.jdongdj;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import ngp.utils.JsonUtils;

/**
 * <AUTHOR>
 */
public class JdongDjBaseMessage {
    private String onlineShopId;
    @JsonProperty("jd_param_json")
    private String jdParamJson;

    private JdParamJson jdParamJsonObj;

    public String getTradeId() {
        jdParamJsonObj = getJdParamJsonObj();
        if (jdParamJsonObj == null) {
            return null;
        }
        if (StringUtils.isNotBlank(jdParamJsonObj.getBillId())) {
            return jdParamJsonObj.getBillId();
        }
        if (StringUtils.isNotBlank(jdParamJsonObj.getOrderId())) {
            return jdParamJsonObj.getOrderId();
        }
        return null;
    }

    public String getOnlineShopId() {
        return onlineShopId;
    }

    public void setOnlineShopId(String onlineShopId) {
        this.onlineShopId = onlineShopId;
    }

    public String getJdParamJson() {
        return jdParamJson;
    }

    public void setJdParamJson(String jdParamJson) {
        this.jdParamJson = jdParamJson;
    }

    public JdParamJson getJdParamJsonObj() {
        if (jdParamJsonObj == null){
            jdParamJsonObj = JsonUtils.toObject(jdParamJson, JdParamJson.class);
        }
        return jdParamJsonObj;
    }

    public void setJdParamJsonObj(JdParamJson jdParamJsonObj) {
        this.jdParamJsonObj = jdParamJsonObj;
    }
}
