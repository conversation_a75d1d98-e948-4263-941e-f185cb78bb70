package com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl.entity.order.OrderEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl.entity.order.Request;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.util.Date;
import java.util.Objects;

@Component
public class AlibabaGylOrderHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlibabaGylOrderHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    private final TmcEshopNotifyChangeMapper tmcMapper;
    public AlibabaGylOrderHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper, TmcEshopNotifyChangeMapper tmcMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
        this.tmcMapper = tmcMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        OrderEntity orderEntity;
        try {
            orderEntity = JsonUtils.toObject(tmMessage, OrderEntity.class);
            if (orderEntity == null || orderEntity.getRequest() == null){
                throw new RuntimeException("阿里供应链转换实体为空");
            }
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成OrderRequest实体出错，错误信息：{}",shopTypeName,e.getMessage());
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        Request apiOrder = orderEntity.getRequest();
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiOrder.getSupplierId(), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",invokeMessage.getProfileId(),shopTypeName,apiOrder.getSupplierId(),tmMessage);
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        LOGGER.info("profileId:{},eshopId:{},店铺类型：{}",eshopRegister.getProfileId(),eshopRegister.getId(),shopTypeName);
        try{
            saveTmcOrderMsg(eshopRegister,tmMessage,apiOrder);
            saveTmcNotifyChange(eshopRegister,apiOrder);
        }catch (Exception ex){
            LOGGER.error("{}保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,eshopRegister.getProfileId(),eshopRegister.getId(),tmMessage,ex.getMessage(),ex);
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(apiOrder.getSupplierId());
        eshopNotifyChange.setTradeOrderId(apiOrder.getBizOrderCode());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(tmMessage);
        SupportUtil.doOrderNotify(apiOrder.getSupplierId(),eshopNotifyChange,invokeMessage.getShopType().getCode());
        AlibabaGylResponse response = new AlibabaGylResponse(true,"0","成功",false);
        return JsonUtils.toJson(response);
    }

    private int saveTmcNotifyChange(EshopRegisterNotify eshopRegister, Request apiOrder) {
        try {
            EshopNotifyChange notifyChange = tmcMapper.queryMessageChangeByTradeId(eshopRegister.getProfileId(), eshopRegister.getId(), apiOrder.getTcOrderId() + "_" + apiOrder.getTcSubOrderId());
            if (Objects.isNull(notifyChange)){
                notifyChange = buildEshopNotifyChangeMsgEntity(eshopRegister,apiOrder);
                return tmcMapper.insertMessageChange(notifyChange);
            }else {
                //orderMsgEntity.setStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
                //平台没有返回更新时间，平台推送一次就更新一次
                notifyChange.setUpdateTime(new Date());
                notifyChange.setContent(apiOrder.getBizOrderCode());
                return tmcMapper.updateEshopNotifyChange(notifyChange);
            }
        } catch (Exception e) {
            LOGGER.error("账单和订单关系保存notify_change表失败：profileid:{},店铺id:{}，错误信息:{}",eshopRegister.getProfileId(),eshopRegister.getId(),e.getMessage(), e);
        }
        return 0;
    }

    private EshopNotifyChange buildEshopNotifyChangeMsgEntity(EshopRegisterNotify eshopRegister, Request apiOrder) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setId(UId.newId());
        change.setProfileId(eshopRegister.getProfileId());
        change.setEshopId(eshopRegister.getId());
        change.setTradeOrderId(apiOrder.getTcOrderId() + "_" + apiOrder.getTcSubOrderId());
        change.setContent(apiOrder.getBizOrderCode());
        change.setCreateTime(new Date());
        change.setUpdateTime(new Date());
        change.setStatus(99);
        return change;
    }

    public int saveTmcOrderMsg(EshopRegisterNotify eshopRegister, String tmMessage, Request orderRequest){
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(eshopRegister.getProfileId(),eshopRegister.getId(),orderRequest.getBizOrderCode());
        if (Objects.isNull(orderMsgEntity)){
            orderMsgEntity = buildEshopTmcOrderMsgEntity(eshopRegister,tmMessage,orderRequest);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        }else {
            //orderMsgEntity.setStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(EshopRegisterNotify eshopRegister, String tmMessage, Request orderRequest) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopRegister.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopRegister.getId());
        tmcOrderMsgEntity.setShopType(ShopType.valueOf(eshopRegister.getType()));
        tmcOrderMsgEntity.setTradeOrderId(orderRequest.getBizOrderCode());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        //默认值已付款
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        try {
            Date date = DateUtils.parseDate(orderRequest.getOrderCreateTime(), "yyyy-mm-dd HH:mm:ss");
            tmcOrderMsgEntity.setCreateTime(date);
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (Exception e) {

        }
        return tmcOrderMsgEntity;
    }

    @Override
    public String serviceName() {
        return "odc.alibaba.ascp.uop.consignorder.notify";
    }
}
