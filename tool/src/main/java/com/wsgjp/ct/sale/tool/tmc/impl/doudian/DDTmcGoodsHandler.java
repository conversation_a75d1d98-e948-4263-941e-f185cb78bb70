package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.wsgjp.ct.sale.common.constant.SyncProductConst;
import com.wsgjp.ct.sale.common.entity.product.TmcProductMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.tool.common.log.EshopTmcMessageLog;
import com.wsgjp.ct.sale.tool.common.log.MqConsumeLog;
import com.wsgjp.ct.sale.tool.product.entity.ProductConst;
import com.wsgjp.ct.sale.tool.tmc.controller.TmcConst;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.utils.TmcLogUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.mq.SysMqSend;
import ngp.mq.MqSendResult;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 2024/8/20 11:18
 */
@Component
public class DDTmcGoodsHandler extends DDNotifyBase implements MessageHandler {


    private static final Logger LOGGER = LoggerFactory.getLogger(DDTmcGoodsHandler.class);


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        try {
            DouDianTmcMessage tmcMessage = JsonUtils.toObject(invokeMessage.getMessage(), DouDianTmcMessage.class);
            DouDianGoodsDto data = JsonUtils.toObject(tmcMessage.getData(), DouDianGoodsDto.class);
            TmcProductMessage tmcProductMessage=new TmcProductMessage();
            tmcProductMessage.setNumId(data.getProductId());
            tmcProductMessage.setEshopId(invokeMessage.getEshopId());
            tmcProductMessage.setProfileId(CurrentUser.getProfileId());
            tmcProductMessage.setCreateTime(DateUtils.getDate());
            tmcProductMessage.setType(SyncProductConst.TMC_PRODUCT);
            List<MqSendResult<TmcProductMessage>> sendResults = SysMqSend.send(Collections.singletonList(tmcProductMessage), ProductConst.TmcProductTopIcName);
            if (CollectionUtils.isEmpty(sendResults)) {
                LOGGER.error("{},发送消息失败,sendMq返回结果为空",JsonUtils.toJson(invokeMessage));
                return TmcConst.FAIL;
            }
            boolean sendSuccess = true;
            for (MqSendResult<TmcProductMessage> sendResult : sendResults){
                if(!sendResult.isSuccess()){
                    sendSuccess = false;
                    continue;
                }
                String messageId = sendResult.getMessageId();
                EshopTmcMessageLog tmcMessageLog = TmcLogUtil.buildTmcLog(invokeMessage, tmcMessage.getTag(), messageId);
                tmcMessageLog.setTmcId(data.getProductId());
                MqConsumeLog mqConsumeLog = TmcLogUtil.buildMqLog(tmcProductMessage, messageId, TMCType.Ptype);
                mqConsumeLog.setTmcId(data.getProductId());
                LogService.add(tmcMessageLog);
                LogService.add(mqConsumeLog);
            }
            if(!sendSuccess){
                return TmcConst.FAIL;
            }
            return TmcConst.SUCCESS;
        }catch (Exception ex){
            return TmcConst.FAIL;
        }
    }



    @Override
    public String serviceName() {
        return TmcConst.DOU_DIAN_GOODS_HANDLER_NAME;
    }
}
