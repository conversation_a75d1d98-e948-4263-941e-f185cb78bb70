package com.wsgjp.ct.sale.tool.tmc.impl.weimobec;

import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.dto.refund.EshopTmcRefundMsgDto;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.impl.weimobec.entity.WeiMobMessage;
import com.wsgjp.ct.sale.tool.tmc.impl.weimobec.entity.WeiMobMessageBody;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;

public abstract class WeiMobEcNotifyBase implements MessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(WeiMobEcNotifyBase.class);

    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(true);
        return result;
    }

    protected void saveTmcRefundMsg(EshopInfo eshopInfo, WeiMobMessage weiMobMessage, String tmcMessage) {
        if (!StringUtils.equals(weiMobMessage.getTopic(), "ec_rights") && !StringUtils.equals(weiMobMessage.getTopic(), "weimob_shop.rights")) {
            return;
        }
        try {
            WeiMobMessageBody msgBody = weiMobMessage.getMsgBodyObj();
            RefundStatus refundStatus = buildRefundStatus(weiMobMessage);
            if (RefundStatus.NONE == refundStatus) {
                return;
            }
            EshopTmcRefundMsgService tmcRefundMsgService = GetBeanUtil.getBean(EshopTmcRefundMsgService.class);
            EshopTmcRefundMsgDto refundMsgDto = new EshopTmcRefundMsgDto(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), eshopInfo.getEshopType()
                    , msgBody.getOrderNo(), msgBody.getRightsId(), tmcMessage, refundStatus);
            tmcRefundMsgService.saveOrUpdateTmcRefundMsg(refundMsgDto);
        } catch (Exception ex) {
            logger.error("账套ID{},店铺ID{},保持售后消息失败，失败原因：{}.message:{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), ex.getMessage(), tmcMessage, ex);
        }
    }

     abstract RefundStatus buildRefundStatus(WeiMobMessage weiMobMessage);
}
