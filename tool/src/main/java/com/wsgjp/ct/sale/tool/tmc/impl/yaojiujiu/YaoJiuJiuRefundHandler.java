package com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu;

import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcRefundMsgMapper;
import com.wsgjp.ct.sale.common.constant.PlatformTmcType;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcRefundMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu.entity.OrderRefund;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

@Component
public class YaoJiuJiuRefundHandler extends YaoJjiuJiuNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(YaoJiuJiuRefundHandler.class);
    private final EshopTmcRefundMsgMapper tmcRefundMsgMapper;

    public YaoJiuJiuRefundHandler(EshopTmcRefundMsgMapper tmcRefundMsgMapper) {
        this.tmcRefundMsgMapper = tmcRefundMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        YJJResponse resp = new YJJResponse();
        if (invokeMessage.getEshopId() == null || invokeMessage.getEshopId().compareTo(BigInteger.ZERO) == 0){
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(invokeMessage.getOnlineEshopId(), invokeMessage.getShopType().getCode());
            invokeMessage.setEshopId(eshopRegister.getId());
        }
        OrderRefund apiRefund;
        try {
            apiRefund = JsonUtils.toObject(tmMessage, OrderRefund.class);
            if (apiRefund == null){
                throw new RuntimeException("药九九转换实体为空");
            }
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成OrderRefund实体出错，错误信息：{}",shopTypeName,e.getMessage(),e);
            resp.setCode(203);
            resp.setSuccess(false);
            resp.setMsg("json转换报错!");
            return JsonUtils.toJson(resp);
        }
        try {
            saveTmcRefundMsg(invokeMessage, tmMessage, apiRefund);
        } catch (Exception ex) {
            LOGGER.error("{}保存TMC售后消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,invokeMessage.getProfileId(),invokeMessage.getEshopId(),tmMessage,ex.getMessage(),ex);
            resp.setCode(203);
            resp.setSuccess(false);
            resp.setMsg("售后单保存数据库出错!");
            return JsonUtils.toJson(resp);
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setProfileId(invokeMessage.getProfileId());
        eshopNotifyChange.setEshopId(invokeMessage.getEshopId());
        eshopNotifyChange.setTradeOrderId(apiRefund.getOrderCode());
        eshopNotifyChange.setRefundOrderId(apiRefund.getReturnNo());
        eshopNotifyChange.setContent("");
        eshopNotifyChange.setId(UId.newId());
        eshopNotifyChange.setOnlineShopId(invokeMessage.getOnlineEshopId());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setPlatformMsgType(PlatformTmcType.REFUND_MSG);
        SupportUtil.doOrderNotify(invokeMessage.getOnlineEshopId(), eshopNotifyChange, invokeMessage.getShopType().getCode());
        resp.setCode(200);
        resp.setSuccess(true);
        resp.setMsg("成功");
        return JsonUtils.toJson(resp);
    }

    public int saveTmcRefundMsg(InvokeMessageEntity invokeMessage, String tmMessage, OrderRefund apiRefund) {
        //bizOrderCode 售后单号
        EshopTmcRefundMsgEntity refundMsgEntity = tmcRefundMsgMapper.queryTmcRefundMsgByRefundId(invokeMessage.getProfileId(), invokeMessage.getEshopId(), apiRefund.getOrderCode());
        if (Objects.isNull(refundMsgEntity)) {
            refundMsgEntity = buildEshopTmcRefundMsgEntity(invokeMessage, tmMessage, apiRefund);
            return tmcRefundMsgMapper.insertTmcRefundMsg(refundMsgEntity);
        } else {
            //todo  refundMsgEntity.setMsgStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            refundMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            refundMsgEntity.setUpdateTime(new Date());
            refundMsgEntity.setMessage(tmMessage);
            return tmcRefundMsgMapper.updateTmcRefundMsg(refundMsgEntity);
        }
    }

    private EshopTmcRefundMsgEntity buildEshopTmcRefundMsgEntity(InvokeMessageEntity invokeMessage, String tmMessage, OrderRefund apiRefund) {
        EshopTmcRefundMsgEntity tmcRefundMsgEntity = new EshopTmcRefundMsgEntity();
        tmcRefundMsgEntity.setId(UId.newId());
        tmcRefundMsgEntity.setProfileId(invokeMessage.getProfileId());
        tmcRefundMsgEntity.setEshopId(invokeMessage.getEshopId());
        tmcRefundMsgEntity.setShopType(invokeMessage.getShopType());
        tmcRefundMsgEntity.setTradeOrderId(apiRefund.getOrderCode());
        tmcRefundMsgEntity.setRefundOrderId(apiRefund.getReturnNo());
        tmcRefundMsgEntity.setMessage(tmMessage);
        tmcRefundMsgEntity.setMsgStatus(0);
        tmcRefundMsgEntity.setMsgCreateTime(new Date());
        tmcRefundMsgEntity.setMsgUpdateTime(new Date());
        tmcRefundMsgEntity.setCreateTime(new Date());
        return tmcRefundMsgEntity;
    }

    @Override
    public String serviceName() {
        return "yjjOrderRefund";
    }
}
