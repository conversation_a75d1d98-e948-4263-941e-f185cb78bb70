package com.wsgjp.ct.sale.tool.tmc.impl.yaojiujiu.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.List;

public class OrderRefund {
    private Long platformID;
    private String thirdCustCode;
    private String returnItemTime;
    private String returnReason;
    private String returnInstruction;
    private List<String> imgList;
    private Long itemStoreID;
    private String batchNo;
    private String ckdCode;
    private String returnNo;
    private String orderCode;
    private String prodNo;
    private BigDecimal returnPrice;
    private BigDecimal applyReturnNumber;

    @JsonProperty("platformId")
    public Long getPlatformID() { return platformID; }
    @JsonProperty("platformId")
    public void setPlatformID(Long value) { this.platformID = value; }

    @JsonProperty("thirdCustCode")
    public String getThirdCustCode() { return thirdCustCode; }
    @JsonProperty("thirdCustCode")
    public void setThirdCustCode(String value) { this.thirdCustCode = value; }

    @JsonProperty("returnItemTime")
    public String getReturnItemTime() { return returnItemTime; }
    @JsonProperty("returnItemTime")
    public void setReturnItemTime(String value) { this.returnItemTime = value; }

    @JsonProperty("returnReason")
    public String getReturnReason() { return returnReason; }
    @JsonProperty("returnReason")
    public void setReturnReason(String value) { this.returnReason = value; }

    @JsonProperty("returnInstruction")
    public String getReturnInstruction() { return returnInstruction; }
    @JsonProperty("returnInstruction")
    public void setReturnInstruction(String value) { this.returnInstruction = value; }

    @JsonProperty("imgList")
    public List<String> getImgList() { return imgList; }
    @JsonProperty("imgList")
    public void setImgList(List<String> value) { this.imgList = value; }

    @JsonProperty("itemStoreId")
    public Long getItemStoreID() { return itemStoreID; }
    @JsonProperty("itemStoreId")
    public void setItemStoreID(Long value) { this.itemStoreID = value; }

    @JsonProperty("batchNo")
    public String getBatchNo() { return batchNo; }
    @JsonProperty("batchNo")
    public void setBatchNo(String value) { this.batchNo = value; }

    @JsonProperty("ckdCode")
    public String getCkdCode() { return ckdCode; }
    @JsonProperty("ckdCode")
    public void setCkdCode(String value) { this.ckdCode = value; }

    @JsonProperty("returnNo")
    public String getReturnNo() { return returnNo; }
    @JsonProperty("returnNo")
    public void setReturnNo(String value) { this.returnNo = value; }

    @JsonProperty("orderCode")
    public String getOrderCode() { return orderCode; }
    @JsonProperty("orderCode")
    public void setOrderCode(String value) { this.orderCode = value; }

    @JsonProperty("prodNo")
    public String getProdNo() { return prodNo; }
    @JsonProperty("prodNo")
    public void setProdNo(String value) { this.prodNo = value; }

    @JsonProperty("returnPrice")
    public BigDecimal getReturnPrice() { return returnPrice; }
    @JsonProperty("returnPrice")
    public void setReturnPrice(BigDecimal value) { this.returnPrice = value; }

    @JsonProperty("applyReturnNumber")
    public BigDecimal getApplyReturnNumber() { return applyReturnNumber; }
    @JsonProperty("applyReturnNumber")
    public void setApplyReturnNumber(BigDecimal value) { this.applyReturnNumber = value; }
}