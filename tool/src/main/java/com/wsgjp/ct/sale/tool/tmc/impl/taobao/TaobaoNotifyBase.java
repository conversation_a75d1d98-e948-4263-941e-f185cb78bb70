package com.wsgjp.ct.sale.tool.tmc.impl.taobao;

import com.taobao.api.internal.spi.CheckResult;
import com.taobao.api.internal.spi.SpiUtils;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;

import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.IOException;
import java.io.StringReader;

public abstract class TaobaoNotifyBase implements MessageHandler {
    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        try {
            CheckResult checkResult = SpiUtils.checkSign(request, eshopTmcConfig.getTaobaoAppSecret());
            CheckSignResult signResult = new CheckSignResult();
            signResult.setBody(checkResult.getRequestBody());
            signResult.setSuccess(checkResult.isSuccess());
            return signResult;
        } catch (IOException e) {
            throw new RuntimeException("淘宝验签失败," + e.getMessage());
        }
    }


    protected  <T> T convertToJavaBean(String xml, Class<T> t) throws Exception {
        T obj = null;
        JAXBContext context = JAXBContext.newInstance(t);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        obj = (T) unmarshaller.unmarshal(new StringReader(xml));
        return obj;
    }
}
