package com.wsgjp.ct.sale.tool.bus;

import com.wsgjp.ct.sale.bus.entity.BusTaskInfo;
import com.wsgjp.ct.sale.bus.entity.Task;
import com.wsgjp.ct.sale.bus.entity.TaskData;
import com.wsgjp.ct.sale.bus.entity.TaskType;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class MessageDecodeHelper {
    static Logger logger = LoggerFactory.getLogger(MessageDecodeHelper.class);
    public static Task DecodeTask(BusTaskInfo task) {
        if (StringUtils.isNotEmpty(task.getBusMessage())) {
            return busTask(task.getBusMessage());
        } else if (StringUtils.isNotEmpty(task.getAccountingMessage())) {
            return accountTask(task.getAccountingMessage());
        }
        //系统轮询是没有参数的
        return null;
    }
    private static Task accountTask(String accountingMessage) {
        //将消息解析提交总线备档，并执行
        Task task = new Task(Md5Utils.md5(accountingMessage), TaskType.SendedStockPostBill, new Date(), accountingMessage);
        return task;
    }
    private static Task busTask(String busMessage) {
        logger.debug("[sale-bus]总线任务正在执行{},{}", CurrentUser.getProfileId(), busMessage);
        TaskData task = JsonUtils.toObject(busMessage, TaskData.class);
        return task;
    }
}
