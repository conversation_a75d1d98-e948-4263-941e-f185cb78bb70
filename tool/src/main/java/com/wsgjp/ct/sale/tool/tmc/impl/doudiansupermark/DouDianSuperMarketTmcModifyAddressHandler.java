package com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark;

import com.doudian.open.spi.yunc_wms_updateOutboundAddress.param.ReceiverInfo;
import com.doudian.open.spi.yunc_wms_updateOutboundAddress.param.YuncWmsUpdateOutboundAddressParam;
import com.doudian.open.utils.JsonUtil;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.dto.DDAddressDto;
import com.wsgjp.ct.sale.tool.tmc.impl.doudian.DDNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.impl.pdd.OrderPromiseHandler;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.sis.client.common.CurrentUser;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class DouDianSuperMarketTmcModifyAddressHandler extends DDNotifyBase implements MessageHandler {

    private static final Logger sysLogger = LoggerFactory.getLogger(OrderPromiseHandler.class);

    private final TmcNotifyProxy notifyProxy;

    public DouDianSuperMarketTmcModifyAddressHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String message = invokeMessage.getMessage();
        if(StringUtils.isEmpty(message)){
            sysLogger.info("抖店消息为空");
            //这里需要返回错误
            return JsonUtils.toJson(new GeneralResult(500L,"消息为空",null));
        }
        YuncWmsUpdateOutboundAddressParam ddspiModifyAddress = JsonUtil.fromJson(message, YuncWmsUpdateOutboundAddressParam.class);
        if(ddspiModifyAddress == null){
            return JsonUtils.toJson(new GeneralResult(500L,"序列化地址信息错误",null));
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(ddspiModifyAddress.getWarehouseCode(), invokeMessage.getShopType().getCode());
        TmcInvokeRequest request = new TmcInvokeRequest();
        request.setMethod(TmcNotifyMethodEnum.MODIFY_ADDRESS_UPDATE);
        request.setTradeId(ddspiModifyAddress.getOutboundOrderNo());
        request.setEshopId(eshopRegister.getId());
        request.setProfileId(CurrentUser.getProfileId());
        request.setShopType(ShopType.Doudian);
        buildRequest(request,ddspiModifyAddress);
        TmcInvokeResponse response = notifyProxy.execute(request);
        if("0".equals(response.getCode()) || "200".equals(response.getCode()) || "203".equals(response.getCode()) || "202".equals(response.getCode())){
            return JsonUtils.toJson(new GeneralResult(0L,"success",null));
        }else{
            long code = Long.parseLong(response.getCode());
            return JsonUtils.toJson(new GeneralResult(code,response.getMessage(),null));
        }

    }

    @Override
    public String serviceName() {
        return "yunc.wms.updateOutboundAddress";
    }


    private void buildRequest(TmcInvokeRequest request,YuncWmsUpdateOutboundAddressParam ddspiModifyAddress){
        ReceiverInfo toReceiverInfo = ddspiModifyAddress.getReceiverInfo();
        DDAddressDto ddAddressDto = new DDAddressDto();
        ddAddressDto.setPostReceiver(StringUtils.isNotEmpty(toReceiverInfo.getEncryptName()) ? toReceiverInfo.getEncryptName() : toReceiverInfo.getName());
        ddAddressDto.setPostTel(StringUtils.isNotEmpty(toReceiverInfo.getEncryptMobile()) ? toReceiverInfo.getEncryptMobile() : toReceiverInfo.getMobile());
        ddAddressDto.setProvince(toReceiverInfo.getProvince());
        ddAddressDto.setCity(toReceiverInfo.getCity());
        ddAddressDto.setStreet(toReceiverInfo.getStreet());
        ddAddressDto.setAddressDetail(StringUtils.isNotEmpty(toReceiverInfo.getEncryptDetail()) ? toReceiverInfo.getEncryptDetail() : toReceiverInfo.getDetail());
        String json = JsonUtils.toJson(ddAddressDto);
        request.setMessage(json);
    }
}
