package com.wsgjp.ct.sale.tool.tmc.impl.taobao;

import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.vo.TaoBaoResultVO;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * 催发货
 */
@Component
public class DeliverGoodsHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeliverGoodsHandler.class);

    private final TmcNotifyProxy notifyProxy;

    public DeliverGoodsHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        Map<String, String> params = invokeMessage.getParams();

        TaoBaoResultVO response = new TaoBaoResultVO();
        TaoBaoResultVO.ResultInfo resultDO = new TaoBaoResultVO.ResultInfo();

        TmcInvokeRequest invokeRequest = new TmcInvokeRequest();
        invokeRequest.setProfileId(invokeMessage.getProfileId());
        invokeRequest.setEshopId(invokeMessage.getEshopId());
        invokeRequest.setTradeId(params.get("order_id"));
        invokeRequest.setMethod(TmcNotifyMethodEnum.EXPEDITE_DELIVERY);
        invokeRequest.setMessage(invokeMessage.getMessage());

        TmcInvokeResponse resp = notifyProxy.execute(invokeRequest);

        if (!resp.isError()) {
            resultDO.setData("亲，已经安排提前发货");
            resultDO.setSuccess(true);
            resultDO.setErrorCode(resp.getMessage());
            resultDO.setErrorMsg("");
            LOGGER.info("订单{}通过店小蜜催发货，优先发货。返回成功", invokeRequest.getTradeId());
        } else {
            resultDO.setErrorCode("999");
            resultDO.setErrorMsg(resp.getMessage());
            resultDO.setSuccess(false);
        }
        response.setResult(resultDO);
        return JsonUtils.toJson(response);
    }


    @Override
    public String serviceName() {
        return "invokedxm.delivergoods";
    }
}
