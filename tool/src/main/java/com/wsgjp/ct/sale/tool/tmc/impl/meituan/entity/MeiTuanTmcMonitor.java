package com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity;

import com.wsgjp.ct.sale.monitor.bifrost.entity.BifrostApiMonitorInfo;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.utils.TmcMonitor;
import ngp.utils.StringUtils;

/**
 * <AUTHOR>
 */
public class MeiTuanTmcMonitor {
    public static void recordMeiTuanMonitor(String msg, String serviceName, InvokeMessageEntity invokeMessage, long startTime) {
        long endTime = System.currentTimeMillis();
        boolean success = StringUtils.equals("ok", msg);
        BifrostApiMonitorInfo monitorInfo = TmcMonitor.getApiMonitorInfo(serviceName, success, msg, "500");
        TmcMonitor.recordApiMonitor(monitorInfo, invokeMessage.getShopType(), endTime - startTime, !success);
    }
}
