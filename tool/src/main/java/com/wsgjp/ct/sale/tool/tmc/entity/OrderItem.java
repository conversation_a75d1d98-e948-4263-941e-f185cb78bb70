package com.wsgjp.ct.sale.tool.tmc.entity;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "orderItem")
public class OrderItem {
    private double quantity;
    private BigDecimal itemAmount;
    private String scItemName;
    private String subTradeOrderId;
    private String subOrderCode;
    private String outerId;
    private String scItemId;
    private String tradeOrerId;
    private String barCode;

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getItemAmount() {
        return itemAmount;
    }

    public void setItemAmount(BigDecimal itemAmount) {
        this.itemAmount = itemAmount;
    }

    public String getScItemName() {
        return scItemName;
    }

    public void setScItemName(String scItemName) {
        this.scItemName = scItemName;
    }

    public String getSubTradeOrderId() {
        return subTradeOrderId;
    }

    public void setSubTradeOrderId(String subTradeOrderId) {
        this.subTradeOrderId = subTradeOrderId;
    }

    public String getSubOrderCode() {
        return subOrderCode;
    }

    public void setSubOrderCode(String subOrderCode) {
        this.subOrderCode = subOrderCode;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getScItemId() {
        return scItemId;
    }

    public void setScItemId(String scItemId) {
        this.scItemId = scItemId;
    }

    public String getTradeOrerId() {
        return tradeOrerId;
    }

    public void setTradeOrerId(String tradeOrerId) {
        this.tradeOrerId = tradeOrerId;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }
}
