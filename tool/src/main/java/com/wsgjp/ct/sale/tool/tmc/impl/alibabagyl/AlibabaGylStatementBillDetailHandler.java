package com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl;

import com.alibaba.dchain.api.request.SettlementStatementbillDetailNotifyRequest;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
public class AlibabaGylStatementBillDetailHandler extends TaobaoNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(AlibabaGylStatementBillDetailHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    public AlibabaGylStatementBillDetailHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        SettlementStatementbillDetailNotifyRequest orderEntity;
        try {
            orderEntity = JsonUtils.toObject(tmMessage, SettlementStatementbillDetailNotifyRequest.class);
            if (orderEntity == null || CollectionUtils.isEmpty(orderEntity.getDetailList())){
                throw new RuntimeException("阿里供应链转换账单实体为空");
            }
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成SettlementStatementbillDetailNotifyRequest实体出错，错误信息：{}",shopTypeName,e.getMessage());
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        List<SettlementStatementbillDetailNotifyRequest.DetailList> detailList = orderEntity.getDetailList();
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(String.valueOf(detailList.get(0).getSupplierId()), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",invokeMessage.getProfileId(),shopTypeName,orderEntity.getDetailList().get(0).getSupplierId(),tmMessage);
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        LOGGER.info("profileId:{},eshopId:{},店铺类型：{}",eshopRegister.getProfileId(),eshopRegister.getId(),shopTypeName);

        try{
            // 为什么没考虑批量插入，因为要查询是否存在，需要更新
            for (SettlementStatementbillDetailNotifyRequest.DetailList list : detailList) {
                saveTmcOrderMsg(eshopRegister,JsonUtils.toJson(list),list);
            }
        }catch (Exception ex){
            LOGGER.error("{}保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,eshopRegister.getProfileId(),eshopRegister.getId(),tmMessage,ex.getMessage(),ex);
            AlibabaGylResponse response = new AlibabaGylResponse(false,"201","系统异常",false);
            return JsonUtils.toJson(response);
        }
        AlibabaGylResponse response = new AlibabaGylResponse(true,"0","成功",false);
        return JsonUtils.toJson(response);
    }

    public int saveTmcOrderMsg(EshopRegisterNotify eshopRegister, String tmMessage, SettlementStatementbillDetailNotifyRequest.DetailList orderRequest){
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(eshopRegister.getProfileId(),eshopRegister.getId(),orderRequest.getStatementBillCode());
        if (Objects.isNull(orderMsgEntity)){
            orderMsgEntity = buildEshopTmcOrderMsgEntity(eshopRegister,tmMessage,orderRequest);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        }else {
            //orderMsgEntity.setStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(EshopRegisterNotify eshopRegister, String tmMessage, SettlementStatementbillDetailNotifyRequest.DetailList orderRequest) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopRegister.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopRegister.getId());
        tmcOrderMsgEntity.setShopType(ShopType.valueOf(eshopRegister.getType()));
        tmcOrderMsgEntity.setTradeOrderId(orderRequest.getStatementBillCode());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(99);
        //默认值已付款
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.All);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        try {
            if (StringUtils.isNotEmpty(orderRequest.getBillCreateTime())){
                tmcOrderMsgEntity.setCreateTime(new Date(Date.parse(orderRequest.getBillCreateTime())));
            }
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (Exception e) {
            tmcOrderMsgEntity.setCreateTime(new Date());
            tmcOrderMsgEntity.setUpdateTime(new Date());
        }
        return tmcOrderMsgEntity;
    }

    @Override
    public String serviceName() {
        return "alibaba.dchain.settlement.statementbill.detail.notify";
    }
}
