package com.wsgjp.ct.sale.tool.tmc.mapper;

import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
@Component
public interface EshopProductSkuMappingMapper {
    List<EshopProductSkuMapping> queryEshopProductSkuMappingByNumId(@Param("profileId") BigInteger profileId,
                                                                    @Param("eshopId") BigInteger eshopId,
                                                                    @Param("platformNumId") String platformNumId);
}
