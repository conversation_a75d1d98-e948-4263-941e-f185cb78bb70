package com.wsgjp.ct.sale.tool.tmc.impl.taobao;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.common.enums.core.enums.tmc.TmcNotifyResponseEnum;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.tmc.BuyerSpecifyLogisticsMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.vo.TaoBaoResultVO;
import ngp.utils.JsonUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Map;

/**
 * 指定物流
 * <AUTHOR>
 */
@Component
public class SpecifyLogisticsHandler extends TaobaoNotifyBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(SpecifyLogisticsHandler.class);
    private final TmcNotifyProxy notifyProxy;

    public SpecifyLogisticsHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        TmcInvokeRequest invokeRequest = buildTmcInvokeRequest(invokeMessage);

        TmcInvokeResponse invokeResp = notifyProxy.execute(invokeRequest);

        TaoBaoResultVO resp = new TaoBaoResultVO();
        TaoBaoResultVO.ResultInfo result = new TaoBaoResultVO.ResultInfo();

        if (invokeResp.isError()) {
            result.setSuccess(true);
            result.setErrorMsg("指定物流公司已经更新");
        } else {
            if (TmcNotifyResponseEnum.SALE_ORDER_NO_ENTER_ERP.getCode().equals(invokeResp.getCode())) {
                result.setErrorMsg("订单还未处理发货，我们会尽快处理发货，若特殊要求请联系人工客服。");
            } else if (TmcNotifyResponseEnum.LOGISTICS_AUDIT_SPLIT_MERGE.getCode().equals(invokeResp.getCode())) {
                result.setErrorMsg("订单已经在拣货打包，请联系人工客服处理");
            } else {
                result.setErrorMsg(invokeResp.getMessage());
            }
        }
        resp.setResult(result);
        return JsonUtils.toJson(resp);
    }

    @NotNull
    private TmcInvokeRequest buildTmcInvokeRequest(InvokeMessageEntity invokeMessage) {
        BigInteger eshopId = invokeMessage.getEshopId();
        Map<String, String> params = invokeMessage.getParams();
        TmcInvokeRequest invokeRequest = new TmcInvokeRequest();

        BuyerSpecifyLogisticsMessage logisticsMessage = new BuyerSpecifyLogisticsMessage();
        logisticsMessage.setFreightCode("");
        logisticsMessage.setShopType(ShopType.TaoBao);
        Map<String, Object> bodyMap = JsonUtils.toHashMap(invokeMessage.getMessage());
        logisticsMessage.setFreightCode((String) bodyMap.get("cap"));
        logisticsMessage.setOrderId(params.get("order_id"));
        logisticsMessage.setSellerId(params.get("seller_id"));
        logisticsMessage.setSellerNick(params.get("seller_nick"));

        invokeRequest.setEshopId(eshopId);
        invokeRequest.setTradeId(params.get("order_id"));
        invokeRequest.setMessage(JsonUtils.toJson(logisticsMessage));
        invokeRequest.setMethod(TmcNotifyMethodEnum.SPECIFY_LOGISTICS);
        return invokeRequest;
    }

    @Override
    public String serviceName() {
        return "invokedxm.express";
    }
}
