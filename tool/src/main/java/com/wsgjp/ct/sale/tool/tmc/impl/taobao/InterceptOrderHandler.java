package com.wsgjp.ct.sale.tool.tmc.impl.taobao;

import com.qimencloud.api.sceneqimen.request.TaobaoQianniuCloudkefuOrderSelfInterceptRequest;
import com.qimencloud.api.sceneqimen.response.TaobaoQianniuCloudkefuOrderSelfInterceptResponse;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class InterceptOrderHandler extends TaobaoNotifyBase implements MessageHandler {
    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        TaobaoQianniuCloudkefuOrderSelfInterceptResponse response = new TaobaoQianniuCloudkefuOrderSelfInterceptResponse();
        TaobaoQianniuCloudkefuOrderSelfInterceptResponse.ResultDO resultDO = new TaobaoQianniuCloudkefuOrderSelfInterceptResponse.ResultDO();
        try {
            TaobaoQianniuCloudkefuOrderSelfInterceptRequest request = JsonUtils.toObject(invokeMessage.getMessage(), TaobaoQianniuCloudkefuOrderSelfInterceptRequest.class);
            EshopNotifyChange change = new EshopNotifyChange();
            change.setContent("");
            change.setTradeOrderId(request.getBizOrder());
            change.setId(UId.newId());
            change.setType(TMCType.INTERCEPT_ORDER);
            SupportUtil.doNotify(request.getSellerNick(), change, ShopType.TaoBao.getCode());
            resultDO.setErrorMsg("");
            resultDO.setErrorCode("");
            resultDO.setSuccess(true);
            response.setResult(resultDO);
        } catch (RuntimeException ex) {
            resultDO.setErrorCode("999");
            resultDO.setErrorMsg(ex.getMessage());
            resultDO.setSuccess(false);
            response.setResult(resultDO);
        }
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "qimen.taobao.qianniu.cloudkefu.order.self.intercept";
    }
}
