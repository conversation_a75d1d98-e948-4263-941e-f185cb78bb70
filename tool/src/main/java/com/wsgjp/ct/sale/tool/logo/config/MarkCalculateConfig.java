package com.wsgjp.ct.sale.tool.logo.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> csh
 * @create 2023-06-16 10:33
 */
@Configuration
@ConfigurationProperties(prefix = "sale-mark-calculate")
public class MarkCalculateConfig {
    /**
     * 是否开启计算标记，默认开启（部署级别）
     */
    private boolean enabled = false;
    /**
     * 分页条数，默认1000
     */
    private int queryCount = 2000;
    /**
     * 测试环境配置，只计算指定账套的标记
     */
    private String profileIds;
    /**
     * Redis锁持续几秒
     * 默认30分钟
     */
    private int calculateProfileLockSeconds = 30 * 60;
    /**
     * 最大循环次数，防止死循环
     */
    private int maxLoopCount = 100;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getQueryCount() {
        return queryCount;
    }

    public void setQueryCount(int queryCount) {
        this.queryCount = queryCount;
    }

    public String getProfileIds() {
        return profileIds;
    }

    public void setProfileIds(String profileIds) {
        this.profileIds = profileIds;
    }

    public int getCalculateProfileLockSeconds() {
        return calculateProfileLockSeconds;
    }

    public void setCalculateProfileLockSeconds(int calculateProfileLockSeconds) {
        this.calculateProfileLockSeconds = calculateProfileLockSeconds;
    }

    public int getMaxLoopCount() {
        return maxLoopCount;
    }

    public void setMaxLoopCount(int maxLoopCount) {
        this.maxLoopCount = maxLoopCount;
    }
}
