package com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class HipacHead {

    @XmlElement(name = "appKey")
    public String shopAccount;

    @XmlElement(name = "service")
    public String apiMethod;

    @XmlElement(name = "sendID")
    public String sendId;

    @XmlElement(name = "sign")
    public String sign;

    @XmlElement(name = "supplierSenderID")
    public String supplierSenderID;

    public String getShopAccount() {
        return shopAccount;
    }

    public void setShopAccount(String shopAccount) {
        this.shopAccount = shopAccount;
    }

    public String getApiMethod() {
        return apiMethod;
    }

    public void setApiMethod(String apiMethod) {
        this.apiMethod = apiMethod;
    }

    public String getSendId() {
        return sendId;
    }

    public void setSendId(String sendId) {
        this.sendId = sendId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getSupplierSenderID() {
        return supplierSenderID;
    }

    public void setSupplierSenderID(String supplierSenderID) {
        this.supplierSenderID = supplierSenderID;
    }
}
