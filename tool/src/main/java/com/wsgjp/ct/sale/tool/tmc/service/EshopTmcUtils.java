package com.wsgjp.ct.sale.tool.tmc.service;

import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.tool.tmc.mapper.EshopInfoMapper;
import org.springframework.stereotype.Service;

import java.math.BigInteger;

@Service
public class EshopTmcUtils {
    private final EshopInfoMapper mapper;

    public EshopTmcUtils(EshopInfoMapper mapper) {
        this.mapper = mapper;
    }

    public EshopInfo queryEshopInfo(BigInteger profileId, BigInteger eshopId){
        return mapper.queryEshopInfo(profileId,eshopId);
    }

    public EshopInfo queryEshopInfoByShopaccount(BigInteger profileId, String shopaccount){
        return mapper.queryEshopInfoByShopaccount(profileId,shopaccount);
    }
}
