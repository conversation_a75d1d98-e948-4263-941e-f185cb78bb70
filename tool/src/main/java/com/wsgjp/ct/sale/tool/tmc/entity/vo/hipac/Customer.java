package com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class Customer {

    @XmlElement(name = "custName")
    public String custName;

    @XmlElement(name = "custIdNum")
    public String custIdNum;

    @XmlElement(name = "custPhone")
    public String custPhone;

    @XmlElement(name = "custProvice")
    public String custProvice;

    @XmlElement(name = "custCity")
    public String custCity;

    @XmlElement(name = "custArea")
    public String custArea;

    @XmlElement(name = "custAddress")
    public String custAddress;

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustIdNum() {
        return custIdNum;
    }

    public void setCustIdNum(String custIdNum) {
        this.custIdNum = custIdNum;
    }

    public String getCustPhone() {
        return custPhone;
    }

    public void setCustPhone(String custPhone) {
        this.custPhone = custPhone;
    }

    public String getCustProvice() {
        return custProvice;
    }

    public void setCustProvice(String custProvice) {
        this.custProvice = custProvice;
    }

    public String getCustCity() {
        return custCity;
    }

    public void setCustCity(String custCity) {
        this.custCity = custCity;
    }

    public String getCustArea() {
        return custArea;
    }

    public void setCustArea(String custArea) {
        this.custArea = custArea;
    }

    public String getCustAddress() {
        return custAddress;
    }

    public void setCustAddress(String custAddress) {
        this.custAddress = custAddress;
    }
}
