package com.wsgjp.ct.sale.tool.tmc.impl.doudian.douDianAuthorization;

import com.wsgjp.ct.sale.biz.bifrost.mapper.BifrostEshopMapper;
import com.wsgjp.ct.sale.biz.bifrost.service.EshopTmcRefundMsgService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.BaseInfoLog;
import com.wsgjp.ct.sale.biz.eshoporder.impl.auth.AuthManager;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.platform.dto.token.EshopAuthInfo;
import com.wsgjp.ct.sale.platform.factory.alibaba.AlibabaConfig;
import com.wsgjp.ct.sale.platform.sdk.entity.Etype;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopBaseInfoMapper;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.doudian.douDianAuthorization.entity.*;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.pubmessage.PubMessageCenterPip;
import com.wsgjp.ct.support.pubmessage.PubMessageCenterRequest;
import ngp.idgenerator.UId;
import ngp.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class DouDianAuthorizationHandler extends DouDianAuthorizationNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DouDianAuthorizationHandler.class);
    private final EshopTmcUtils eshopTmcUtils;
    private final BifrostEshopMapper eshopMapper;

    public DouDianAuthorizationHandler(EshopTmcUtils eshopTmcUtils, EshopTmcRefundMsgService tmcRefundMsgService, AlibabaConfig alibabaConfig, BifrostEshopMapper eshopMapper) {
        this.eshopTmcUtils = eshopTmcUtils;
        this.eshopMapper = eshopMapper;
    }


    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        MsgRequest response;
        MsgResponse res = new MsgResponse();
        try {
            response = JsonUtils.toObject(tmMessage, MsgRequest.class);
            String entity = response.getMsg();
            if (StringUtils.isEmpty(entity)) {
                res.setCode(-1);
                res.setMsg("转json失败");
                return JsonUtils.toJson(res);
            }
            MsgOrderInfo msgOrderInfo = null;
            MsgAuthInfo msgAuthInfo = null;
            PushAfterSaleInfoVO PushAfterSaleInfoVO = null;
            if (response.getMsg_type() == 1) {
                msgOrderInfo = JsonUtils.toObject(entity, MsgOrderInfo.class);
                if (msgOrderInfo == null || msgOrderInfo.getOrder_info() == null) {
                    res.setCode(-1);
                    res.setMsg("转json失败");
                    return JsonUtils.toJson(res);
                }
                // 订购消息暂时不处理
                return JsonUtils.toJson(res);
            }
            if (response.getMsg_type() == 2) {
                msgAuthInfo = JsonUtils.toObject(entity, MsgAuthInfo.class);
                if (msgAuthInfo == null || msgAuthInfo.getAction_type() == null) {
                    res.setCode(-1);
                    res.setMsg("转json失败");
                    return JsonUtils.toJson(res);
                }
                BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
                if (BigInteger.ZERO.equals(invokeMessage.getEshopId())) {
                    // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                    EshopRegisterNotify notify = SupportUtil.buildNotify(msgAuthInfo.getShop_id(), 52);
                    if (notify == null || notify.getId() == null || notify.getId().equals(BigInteger.ZERO)) {
                        LOGGER.error("抖店授权过期消息处理失败：eshopId拿取失败");
                        res.setCode(-1);
                        res.setMsg("店铺id寻找失败");
                        return JsonUtils.toJson(res);
                    }
                    eshopId = notify.getId();
                } else {
                    eshopId = invokeMessage.getEshopId();
                }
                if (msgAuthInfo.getAction_type() == 1) {
                    // 授权消息
                    try {
                        EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
                        EshopAuthInfo authInfo = new EshopAuthInfo();
                        authInfo.setToken("");
                        authInfo.setExpiresIn(new Date());
                        if (eshopInfo != null){
                            authInfo.setRefreshToken(eshopInfo.getRefreshToken());
                        }
                        authInfo.setProfileId(eshopInfo.getProfileId());
                        authInfo.setEshopId(eshopId);
                        authInfo.setHasTokenExpired(true);
                        eshopMapper.updateEshopToken(authInfo);
                        String logging = "抖店平台推送消息提示授权已经关闭了，请尽快前往抖店后台重新打开授权开关,然后在ERP中重新进行一次授权！";
                        if (StringUtils.isNotEmpty(logging)) {
                            BaseInfoLog baseInfoLog = buildEshopConfigLog(logging);
                            baseInfoLog.setEfullname("工具");
                            baseInfoLog.setObjectId(eshopInfo.getOtypeId());
                            baseInfoLog.setObjectType("otype");
                            LogService.add(baseInfoLog);
                        }
                    } catch (Exception e) {
                        LOGGER.error("tmc抖店店铺授权过期修改数据库失败：eshopId:{}", eshopId);
                    }

                    return JsonUtils.toJson(res);
                }
                if (msgAuthInfo.getAction_type() == 3) {
                    AuthManager authManager = GetBeanUtil.getBean(AuthManager.class);
                    boolean refreshToken = authManager.doRefreshToken(invokeMessage.getProfileId(), eshopId);
                    if (!refreshToken) {
                        EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
                        try {
                            EshopAuthInfo authInfo = new EshopAuthInfo();
                            authInfo.setToken("");
                            authInfo.setExpiresIn(new Date());
                            if (eshopInfo != null){
                                authInfo.setRefreshToken(eshopInfo.getRefreshToken());
                            }
                            eshopMapper.updateEshopToken(authInfo);
                        } catch (Exception e) {
                            LOGGER.error("tmc抖店店铺授权过期修改数据库失败：eshopId:{}", eshopId);
                        }

                        if (Objects.isNull(eshopInfo)) {
                            LOGGER.info("profileId:{},eshopId:{},店铺类型:{},抖店查询店铺信息为空!", invokeMessage.getProfileId(), invokeMessage.getEshopId(), shopTypeName);
                            res.setCode(-1);
                            res.setMsg("店铺信息获取失败");
                            return JsonUtils.toJson(res);
                        }
                        PubMessageCenterRequest request = new PubMessageCenterRequest();
                        List<BigInteger> etypeList = getetypeList(CurrentUser.getProfileId());
                        request.setEventType("业务提醒");
                        request.setSubjectType("pubMsgEshopAuthorization");
                        String msg = "平台推送消息提示授权提前过期了，请尽快在erp进行重新授权!!!";
                        request.setContext("【" + eshopInfo.getFullname() + "】已经授权过期" + "，请尽快重新授权!导致过期原因:" + msg);
                        request.setEtypeIds(etypeList);
                        request.setTitle("网店授权");
                        String url = "sale/eshoporder/eshop/EShopList.gspx";
                        request.setUri(url);
                        PubMessageCenterPip.Util.push(request);
                    }
                    return JsonUtils.toJson(res);
                }

            }
            if (response.getMsg_type() == 3) {
                PushAfterSaleInfoVO = JsonUtils.toObject(entity, PushAfterSaleInfoVO.class);
                if (PushAfterSaleInfoVO == null) {
                    res.setCode(-1);
                    res.setMsg("转json失败");
                    return JsonUtils.toJson(res);
                }
                // 订购退款消息暂时不处理
                return JsonUtils.toJson(res);
            }


        } catch (Exception e) {
            res.setCode(-1);
            res.setMsg("未知原因:" + e.getMessage());
            return JsonUtils.toJson(res);
        }
        return JsonUtils.toJson(res);
    }
    private List<BigInteger> getetypeList(BigInteger profileId) {
        PlatformSdkEshopBaseInfoMapper baseInfoMapper = BeanUtils.getBean(PlatformSdkEshopBaseInfoMapper.class);
        List<Etype> etypeInfoList = baseInfoMapper.getEtypeList(profileId);

        List<BigInteger> etypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(etypeInfoList)) {
            etypeList = etypeInfoList.stream().map(Etype::getId).collect(Collectors.toList());
        }
        return etypeList;
    }
    private BaseInfoLog buildEshopConfigLog(String logMsg) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        BaseInfoLog baseInfoLog = new BaseInfoLog();
        baseInfoLog.setProfileId(profileId);
        baseInfoLog.setEtypeId(employeeId);
        baseInfoLog.setId(UId.newId());
        baseInfoLog.setBody(logMsg);
        baseInfoLog.setEfullname("工具");
        baseInfoLog.setLogTime(DateUtils.getDate());
        baseInfoLog.setIp(IpUtils.getLocalHostIp());
        return baseInfoLog;
    }
    @Override
    public String serviceName() {
        return "douDianAuthorization";
    }

}
