package com.wsgjp.ct.sale.tool.tmc.impl.xianyu;

import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.dto.XianYuOrderMessageDto;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
public class XianYuOrderHandler implements MessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(XianYuOrderHandler.class);

    private final TmcNotifyProxy notifyProxy;

    public XianYuOrderHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        XianYuTmcResponse response = new XianYuTmcResponse();
        try {
            String appId = (invokeMessage.getParams() != null && invokeMessage.getParams().containsKey("appid"))
                    ? invokeMessage.getParams().get("appid")
                    : "";
            if (StringUtils.isEmpty(appId)) {
                logger.error("获取appid失败{}", invokeMessage.getParams() == null ? "" : ngp.utils.JsonUtils.toJson(invokeMessage.getParams()));
                response.setResult("false");
                response.setResult("获取appid失败");
                return ngp.utils.JsonUtils.toJson(response);
            }
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(appId, invokeMessage.getShopType().getCode());
            XianYuOrderMessageDto xianYuOrderMessage =
                    JsonUtils.toObject(invokeMessage.getMessage(), XianYuOrderMessageDto.class);
            EshopNotifyChange change = new EshopNotifyChange();
            change.setProfileId(eshopRegister.getProfileId());
            change.setEshopId(eshopRegister.getId());
            change.setContent(invokeMessage.getMessage());
            change.setTradeOrderId(xianYuOrderMessage.getOrder_no());
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(appId);
            change.setCreateTime(new Date());
            SupportUtil.doNotify(change.getOnlineShopId(), change, invokeMessage.getShopType().getCode());
            response.setResult("success");
            response.setMsg("成功");
        } catch (Exception e) {
            response.setResult("failed");
            response.setMsg(e.getMessage());
        }
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "invokeXianYu/orderReceive";
    }

    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(true);
        return result;
    }
}
