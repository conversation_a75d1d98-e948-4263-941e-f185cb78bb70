package com.wsgjp.ct.sale.tool.tmc.impl.alibabagyl.entity.refund.RefundEntity;

import com.wsgjp.ct.sale.platform.entity.entities.TmallSuperMarketOrderReceiverInfo;
import com.wsgjp.ct.sale.platform.entity.entities.TmallSuperMarketOrderSenderInfo;

public class TmallSuperMarketRefundEntity {
    private String supplierId;
    private String supplierName;
    private String bizOrderCode;
    private OrderRefundItem orderItems;
    private TmallSuperMarketOrderReceiverInfo receiverInfo;
    private TmallSuperMarketOrderSenderInfo senderInfo;
    private String outBizId;
    private String storeCode;
    private String tmsServiceCode;
    private String tmsOrderCode;
    private String forwardOrderCode;
    private String customerId;

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getBizOrderCode() {
        return bizOrderCode;
    }

    public void setBizOrderCode(String bizOrderCode) {
        this.bizOrderCode = bizOrderCode;
    }

    public OrderRefundItem getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(OrderRefundItem orderItems) {
        this.orderItems = orderItems;
    }

    public TmallSuperMarketOrderReceiverInfo getReceiverInfo() {
        return receiverInfo;
    }

    public void setReceiverInfo(TmallSuperMarketOrderReceiverInfo receiverInfo) {
        this.receiverInfo = receiverInfo;
    }

    public TmallSuperMarketOrderSenderInfo getSenderInfo() {
        return senderInfo;
    }

    public void setSenderInfo(TmallSuperMarketOrderSenderInfo senderInfo) {
        this.senderInfo = senderInfo;
    }

    public String getOutBizId() {
        return outBizId;
    }

    public void setOutBizId(String outBizId) {
        this.outBizId = outBizId;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getTmsServiceCode() {
        return tmsServiceCode;
    }

    public void setTmsServiceCode(String tmsServiceCode) {
        this.tmsServiceCode = tmsServiceCode;
    }

    public String getTmsOrderCode() {
        return tmsOrderCode;
    }

    public void setTmsOrderCode(String tmsOrderCode) {
        this.tmsOrderCode = tmsOrderCode;
    }

    public String getForwardOrderCode() {
        return forwardOrderCode;
    }

    public void setForwardOrderCode(String forwardOrderCode) {
        this.forwardOrderCode = forwardOrderCode;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}