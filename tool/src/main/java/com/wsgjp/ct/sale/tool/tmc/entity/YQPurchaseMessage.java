package com.wsgjp.ct.sale.tool.tmc.entity;

public class YQPurchaseMessage {
    /**
     * 接入appkey
     */
    private String appId;
    /**
     * 客户编码=》对应shopaccount
     */
    private String merchantNumber;
    /**
     * 经销商再erp系统中的唯一编码
     */
    private String merchantErpId;
    /**
     * 客户名称
     */
    private String merchantName;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 事件类型：tmc事件名称，或者说是method名称
     */
    private String type;
    /**
     * 推送事件唯一id, 32位, 失败重试时, pushUniqueId 相同
     */
    private String pushUniqueId;
    /**
     * body，每个事件body内容不一样
     */
    private Object body;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMerchantNumber() {
        return merchantNumber;
    }

    public void setMerchantNumber(String merchantNumber) {
        this.merchantNumber = merchantNumber;
    }

    public String getMerchantErpId() {
        return merchantErpId;
    }

    public void setMerchantErpId(String merchantErpId) {
        this.merchantErpId = merchantErpId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPushUniqueId() {
        return pushUniqueId;
    }

    public void setPushUniqueId(String pushUniqueId) {
        this.pushUniqueId = pushUniqueId;
    }

    public Object getBody() {
        return body;
    }

    public void setBody(Object body) {
        this.body = body;
    }
}
