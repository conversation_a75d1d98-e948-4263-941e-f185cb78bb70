package com.wsgjp.ct.sale.tool.tmc.impl.weimobec;


import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.exception.PlatformInterfaceException;
import com.wsgjp.ct.sale.platform.factory.weimobec.builder.RefundBuilder2;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.weimobec.entity.WeiMobEcCode;
import com.wsgjp.ct.sale.tool.tmc.impl.weimobec.entity.WeiMobEcTmcResponse;
import com.wsgjp.ct.sale.tool.tmc.impl.weimobec.entity.WeiMobMessage;
import com.wsgjp.ct.sale.tool.tmc.impl.weimobec.entity.WeiMobMessageBody;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.util.Date;

@Component
public class WeiMobEcOrderV2Handler extends WeiMobEcNotifyBase {

    private static final Logger logger = LoggerFactory.getLogger(WeiMobEcOrderV2Handler.class);
    private final EshopTmcUtils eshopTmcUtils;

    public WeiMobEcOrderV2Handler(EshopTmcUtils eshopTmcUtils) {
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        //微盟消息都有订单id 且售后消息也会通过更新订单更新售后单
        WeiMobEcTmcResponse response = new WeiMobEcTmcResponse();
        WeiMobEcCode code = new WeiMobEcCode();
        try {
            String message = invokeMessage.getMessage();
            WeiMobMessage weiMobMessage = JsonUtils.toObject(message, WeiMobMessage.class);
            if (weiMobMessage == null) {
                logger.error("微盟解析消息数据失败:{}", invokeMessage.getMessage());
                code.setErrcode("1");
                code.setErrmsg("消息体解析为空");
                response.setCode(code);
                return JsonUtils.toJson(response);
            }
            String shopAccount = weiMobMessage.getBosId();
            if (StringUtils.isEmpty(shopAccount)) {
                logger.error("微盟获取shopId失败{}", invokeMessage.getMessage());
                code.setErrcode("1");
                code.setErrmsg("找不到eshopId");
                response.setCode(code);
                return JsonUtils.toJson(response);
            }
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), invokeMessage.getEshopId());
            if (eshopInfo == null) {
                logger.error(String.format("微盟找不到账套映射关系，传入的消息 : %s", invokeMessage.getMessage()));
                code.setErrcode("1");
                code.setErrmsg("业务错误找不到店铺");
                response.setCode(code);
                return JsonUtils.toJson(response);
            }
            EshopNotifyChange change = new EshopNotifyChange();
            change.setProfileId(eshopInfo.getProfileId());
            change.setEshopId(eshopInfo.getOtypeId());
            change.setContent(invokeMessage.getMessage());
            //获取订单id
            WeiMobMessageBody msgBody = weiMobMessage.getMsgBodyObj();
            if (msgBody == null) {
                throw new PlatformInterfaceException("消息的msg_body为空!");
            }
            saveTmcRefundMsg(eshopInfo, weiMobMessage, invokeMessage.getMessage());
            change.setTradeOrderId(msgBody.getOrderNo());
            change.setId(UId.newId());
            change.setType(TMCType.Order);
            change.setOnlineShopId(shopAccount);
            change.setCreateTime(new Date());
            SupportUtil.sendMessage(change, eshopInfo);
            code.setErrcode("0");
            code.setErrmsg("success");
            response.setCode(code);
        } catch (Exception e) {
            logger.error("微盟处理tmc消息失败,账套ID:{},店铺ID:{},tmcMessage:{},错误信息：{}",
                    invokeMessage.getProfileId(), invokeMessage.getEshopId(), invokeMessage.getMessage(), e.getMessage(), e);
            code.setErrcode("1");
            code.setErrmsg(e.getMessage());
            response.setCode(code);
        }
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "weiMobEcOrderV2";
    }

    @Override
    RefundStatus buildRefundStatus(WeiMobMessage weiMobMessage) {
        if (StringUtils.isBlank(weiMobMessage.getEvent())) {
            return RefundStatus.NONE;
        }
        if (StringUtils.equals(weiMobMessage.getEvent(), "create")) {
            return RefundStatus.WAIT_SELLER_AGREE;
        }
        if (StringUtils.equals(weiMobMessage.getEvent(), "statusUpdate")) {
            if (weiMobMessage.getMsgBodyObj().getStatus() == null) {
                return RefundStatus.NONE;
            }
            return RefundBuilder2.buildRefundStatus(weiMobMessage.getMsgBodyObj().getStatus());
        }
        return RefundStatus.NONE;
    }
}
