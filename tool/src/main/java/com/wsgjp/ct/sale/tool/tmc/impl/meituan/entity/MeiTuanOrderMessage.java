package com.wsgjp.ct.sale.tool.tmc.impl.meituan.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import ngp.utils.HttpUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;

import java.util.List;

/**
 * 订单消息
 *
 * <AUTHOR>
 */
public class MeiTuanOrderMessage {
    /**
     * 订单号，数据库中请用bigint(20)存储此字段
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 订单展示ID，与用户端、商家端订单详情中展示的订单号码一致。数据库中请用bigint(20)存储此字段
     */
    @JsonProperty("wm_order_id_view")
    private String orderIdView;

    /**
     * APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。
     */
    @JsonProperty("app_poi_code")
    private String appPoiCode;

    /**
     * 推送自配订单配送状态消息不回返回order_id
     * 订单展示ID
     */
    @JsonProperty("order_view_id")
    private String orderViewId;
    /**
     * 配送异常原因
     */
    @JsonProperty("exception_reason")
    private String exceptionReason;

    @JsonProperty("refund_id")
    private String refundId;
    @JsonProperty("status")
    private String status;

    @JsonProperty("refund_status")
    private String refundStatus;

    @JsonProperty("notify_type")
    private String notifyType;

    @JsonProperty("res_type")
    private String resType;
    @JsonProperty("food")
    private String food;
    private List<GoodInfo> foodList;
    @JsonProperty("timestamp")
    private String timestamp;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderIdView() {
        return orderIdView;
    }

    public void setOrderIdView(String orderIdView) {
        this.orderIdView = orderIdView;
    }

    public String getAppPoiCode() {
        return appPoiCode;
    }

    public void setAppPoiCode(String appPoiCode) {
        this.appPoiCode = appPoiCode;
    }

    public String getOrderViewId() {
        return orderViewId;
    }

    public void setOrderViewId(String orderViewId) {
        this.orderViewId = orderViewId;
    }

    public String getTradeId() {
        if (StringUtils.isNotBlank(this.orderId)) {
            return this.orderId;
        }
        return this.orderViewId;
    }

    public String getExceptionReason() {
        return exceptionReason;
    }

    public void setExceptionReason(String exceptionReason) {
        this.exceptionReason = exceptionReason;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(String notifyType) {
        this.notifyType = notifyType;
    }

    public String getResType() {
        return resType;
    }

    public void setResType(String resType) {
        this.resType = resType;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getFood() {
        return food;
    }

    public void setFood(String food) {
        this.food = food;
    }

    public List<GoodInfo> getFoodList() {
        if (StringUtils.isNotBlank(this.food)) {
            foodList = JsonUtils.toList(HttpUtils.urlDecode(this.food), GoodInfo.class);
        }
        return foodList;
    }

    public void setFoodList(List<GoodInfo> foodList) {
        this.foodList = foodList;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }
}
