package com.wsgjp.ct.sale.tool.tmc.impl.xiaohonghsu.entity;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class OrderEntity {
    private String orderId;
    private Integer orderStatus;
    private Long updateTime;
    private String openAddressId;
    private String operator;
    private String sellerMarkNote;
    private String sellerRemarkContent;
    private Long promiseLastShipTime;

    /**
     * 售后的参数
     */
    private String returnsId;
    private Integer returnType;
    private Integer request_from;
    private BigDecimal refundFee;

    public String getSellerRemarkContent() {
        return sellerRemarkContent;
    }

    public void setSellerRemarkContent(String sellerRemarkContent) {
        this.sellerRemarkContent = sellerRemarkContent;
    }

    public String getReturnsId() {
        return returnsId;
    }

    public void setReturnsId(String returnsId) {
        this.returnsId = returnsId;
    }

    public Integer getReturnType() {
        return returnType;
    }

    public void setReturnType(Integer returnType) {
        this.returnType = returnType;
    }

    public Integer getRequest_from() {
        return request_from;
    }

    public void setRequest_from(Integer request_from) {
        this.request_from = request_from;
    }

    public BigDecimal getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getOpenAddressId() {
        return openAddressId;
    }

    public void setOpenAddressId(String openAddressId) {
        this.openAddressId = openAddressId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getSellerMarkNote() {
        return sellerMarkNote;
    }

    public void setSellerMarkNote(String sellerMarkNote) {
        this.sellerMarkNote = sellerMarkNote;
    }

    public Long getPromiseLastShipTime() {
        return promiseLastShipTime;
    }

    public void setPromiseLastShipTime(Long promiseLastShipTime) {
        this.promiseLastShipTime = promiseLastShipTime;
    }
}
