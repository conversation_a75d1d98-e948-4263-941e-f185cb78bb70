package com.wsgjp.ct.sale.tool.tmc.impl.wxvideo;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillPostState;
import com.wsgjp.ct.bill.core.handle.entity.enums.DeliverProcessTypeEnum;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.BifrostEshopMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderEshopRefundMapper;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.entity.request.order.AuditOrderReceiverRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.AuditOrderReceiverResponse;
import com.wsgjp.ct.sale.platform.enums.LocalProcessStatus;
import com.wsgjp.ct.sale.platform.exception.BusinessSupportException;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.order.EshopAuditCancelOrderReceiverFeature;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AuditCancelOrderService {
    private static final Logger logger = LoggerFactory.getLogger(AuditCancelOrderService.class);

    public AuditOrderReceiverResponse  auditCancelOrder(String tradeId, BigInteger profileId,BigInteger eshopId, ShopType shopType){
        EshopOrderEntity entity = new EshopOrderEntity();
        entity.setTradeId(tradeId);
        entity.setProfileId(profileId);
        entity.setEshopId(eshopId);
        LocalProcessStatus status = checkDeliverStatus(entity);
        AuditOrderReceiverRequest request = new AuditOrderReceiverRequest();
        request.setTradeId(tradeId);
        request.setStatus(status);
        request.setShopType(shopType);
        request.setShopId(eshopId);
        return auditCancelOrder(request);
    }

    public AuditOrderReceiverResponse auditCancelOrder(AuditOrderReceiverRequest request) {
        try {
            BifrostEshopMapper eshopMapper = BeanUtils.getBean(BifrostEshopMapper.class);
            EshopInfo eshopInfo = eshopMapper.getEshopInfoByShopId(com.wsgjp.ct.sis.client.common.CurrentUser.getProfileId(), request.getShopId());
            EshopSystemParams systemParams = com.wsgjp.ct.sale.biz.bifrost.util.CommonUtil.toSystemParams(eshopInfo);
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), systemParams);
            EshopAuditCancelOrderReceiverFeature feature = factory.getFeature(EshopAuditCancelOrderReceiverFeature.class);
            if (feature == null) {
                String msg = String.format("%s不支持取消订单回告平台！", request.getSystemParams().getShopType().getName());
                throw new BusinessSupportException(msg);
            }
            return feature.auditOrderReceiver(request);
        } catch (Exception ex) {
            logger.error("取消订单失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            AuditOrderReceiverResponse response = new AuditOrderReceiverResponse();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
            return response;
        }
    }
    public LocalProcessStatus checkDeliverStatus(EshopOrderEntity apiOrder) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger otypeId = apiOrder.getEshopId();
        String tradeId = apiOrder.getTradeId();
        //判断交易单的类型，是否需要走仓储
        //如果不走仓储
        //仓储是否打印物流单
        EshopOrderEshopRefundMapper refundMapper = BeanUtils.getBean(EshopOrderEshopRefundMapper.class);
        List<BillDeliverDTO> deliverList = refundMapper.queryDeliverByTradeId(profileId, otypeId, tradeId);
        if (CollectionUtils.isEmpty(deliverList)) {
            return LocalProcessStatus.WAIT_AUDIT;
        }

        for (BillDeliverDTO billDeliverDTO : deliverList) {
            //todo:DeliverProcessTypeEnum.NONE 32245
            if (billDeliverDTO.getDeliverProcessType().equals(DeliverProcessTypeEnum.NONE)) {
                return LocalProcessStatus.WAIT_AUDIT;
            }
            BillPostState postState = billDeliverDTO.getPostState();
            if (postState.getState() > 500) {
                return LocalProcessStatus.SENDED;
            }
            //todo:modifyNONE 32245 wf
            if (billDeliverDTO.getDeliverProcessType().equals(DeliverProcessTypeEnum.NONE_POST)) {
                return LocalProcessStatus.WAIT_AUDIT;
            }
            if (billDeliverDTO.getDeliverProcessType().equals(DeliverProcessTypeEnum.CLOUD_WAREHOUSE_DELIVERY)) {
                return getDeliverStatusWithWms(billDeliverDTO, refundMapper);
            }
            return getDeliverStatusWithPost(billDeliverDTO, refundMapper);
        }
        return LocalProcessStatus.WAIT_AUDIT;
    }

    private LocalProcessStatus getDeliverStatusWithWms(BillDeliverDTO billDeliverDTO, EshopOrderEshopRefundMapper refundMapper) {
        boolean sendState = refundMapper.queryWmsSendState(billDeliverDTO.getProfileId(), billDeliverDTO.getVchcode());
        if (sendState) {
            return LocalProcessStatus.WAIT_SEND;
        } else {
            return LocalProcessStatus.WAIT_AUDIT;
        }
    }

    private LocalProcessStatus getDeliverStatusWithPost(BillDeliverDTO billDeliverDTO, EshopOrderEshopRefundMapper refundMapper) {
        List<Boolean> list = refundMapper.queryFrightPrintState(billDeliverDTO.getProfileId(), billDeliverDTO.getVchcode());
        if (CollectionUtils.isEmpty(list)) {
            return LocalProcessStatus.WAIT_AUDIT;
        }
        boolean match = list.stream().anyMatch(x -> x);
        if (match) {
            return LocalProcessStatus.WAIT_SEND;
        } else {
            return LocalProcessStatus.WAIT_AUDIT;
        }
    }
}
