package com.wsgjp.ct.sale.tool.tmc.impl.xiaohonghsu;

import cn.hutool.core.bean.BeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderRefundStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.AddressChangeMessage;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.xiaohonghsu.entity.OrderEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.xiaohonghsu.entity.XiaoHongShuRes;
import com.wsgjp.ct.sale.tool.tmc.impl.xiaohonghsu.entity.XiaoHongShuResponse;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
public class XiaoHongShuOrderRefundHandler extends XiaoHongshuNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(XiaoHongShuOrderRefundHandler.class);
    private final EshopTmcUtils eshopTmcUtils;
    private final EshopTmcConfig config;

    public XiaoHongShuOrderRefundHandler(EshopTmcUtils eshopTmcUtils, EshopTmcConfig config) {
        this.eshopTmcUtils = eshopTmcUtils;
        this.config = config;
    }

    /**
     * 订单消息相关
     */
    private final String OrderDeliverTimeChange = "msg_fulfillment_delivery_time_change";
    private final String OrderRemark = "msg_fulfillment_seller_remark_change";
    private final String OrderAddress = "msg_fulfillment_receiver_change";
    private final String OrderStatusChange = "msg_fulfillment_status_change";


    /**
     * 售后单消息相关
     */
    private final String Refund = "msg_after_sale_create";
    private final String RefundStatusChange = "msg_after_sale_transfer";
    private final String RefundFinished = "msg_after_sale_refund_finished";
    private final String RefundClosed = "msg_after_sale_closed";

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
//        try {
//            String encode = PlatformEventSecurityUtil.encode("{\n" +
//                    "    \"eventId\":\"12312\",\n" +
//                    "    \"bizId\":2232700121995010,\n" +
//                    "    \"openId\":\"f198e2b6591c7e0314bf5724b80aaca5\",\n" +
//                    "    \"event\":\"kwaishop_order_statusChange\",\n" +
//                    "    \"info\":{\n" +
//                    "        \"oid\":2232700121995010,\n" +
//                    "        \"sellerId\":123,\n" +
//                    "        \"openId\":\"f198e2b6591c7e0314bf5724b80aaca5\",\n" +
//                    "        \"status\":30,\n" +
//                    "        \"beforeStatus\":80,\n" +
//                    "        \"updateTime\":1669617309000\n" +
//                    "    },\n" +
//                    "    \"createTime\":1669617309000\n" +
//                    "}", "6bKH1VD4Td7sCgTF6FdJJw==");
//            System.out.println(encode);
//        }catch (Exception e){
//
//        }

        List<XiaoHongShuResponse> apiOrder;
        XiaoHongShuRes res = new XiaoHongShuRes();
        try {
            apiOrder = JsonUtils.toList(tmMessage, XiaoHongShuResponse.class);
            if (CollectionUtils.isNotEmpty(apiOrder)) {
                for (XiaoHongShuResponse response : apiOrder) {
                    String data = response.getData();
                    OrderEntity entity = JsonUtils.toObject(data, OrderEntity.class);
                    if (entity == null) {
                        res.setSuccess(false);
                        res.setError_code(204);
                        res.setError_msg("Json转实体失败");
                        return JsonUtils.toJson(res);
                    }
                    BigInteger eshopId;
        /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
                    if (BigInteger.ZERO.equals(invokeMessage.getEshopId())) {
                        // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                        EshopRegisterNotify notify = SupportUtil.buildNotify(response.getSellerId(), 40);
                        if (notify == null || notify.getId() == null || notify.getId().equals(BigInteger.ZERO)) {
                            LOGGER.error("小红书消息处理失败：eshopId拿取失败");
                            res.setSuccess(false);
                            res.setError_code(201);
                            res.setError_msg("店铺id寻找失败");
                            return JsonUtils.toJson(res);
                        }
                        eshopId = notify.getId();
                    } else {
                        eshopId = invokeMessage.getEshopId();
                    }
                    EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
                    if (Objects.isNull(eshopInfo)) {
                        LOGGER.error("profileId:{},eshopId:{},店铺类型:{},小红书查询店铺信息为空!", invokeMessage.getProfileId(), invokeMessage.getEshopId(), shopTypeName);
                        res.setSuccess(false);
                        res.setError_code(202);
                        res.setError_msg("店铺信息获取失败");
                        return JsonUtils.toJson(res);
                    }
                    EshopNotifyChange change = handleMessage(response, entity);
//                    try {
//                        SupportUtil.doUpdateOrderStatusNotifyByDelayWay(response.getSellerId(),change,eshopInfo.getEshopType().getCode(),config.getTmcXHSSendDelayTime());
//                    } catch (Exception e) {
//                        LOGGER.error("小红书直接修改订单交易状态失败" + e.getMessage(),e);
//                    }
                    change.setType(TMCType.Order);
                    change.setId(UId.newId());
                    change.setEshopId(eshopId);
                    change.setProfileId(invokeMessage.getProfileId());
                    saveRefundToPlEshopNotifyChange(change, invokeMessage, response);
                    SupportUtil.doOrderNotifyByDelayWay(response.getSellerId(), change, eshopInfo.getEshopType().getCode(), config.getTmcXHSSendDelayTime());
                }
                res.setSuccess(true);
                res.setError_code(200);
                return JsonUtils.toJson(res);
            }
            res.setSuccess(false);
            res.setError_code(204);
            res.setError_msg("Json转实体为null");
            return JsonUtils.toJson(res);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成OrderRequest实体出错，错误信息：{}", shopTypeName, e.getMessage());
            res.setSuccess(false);
            res.setError_code(205);
            res.setError_msg("未知异常:" + e.getMessage());
            return JsonUtils.toJson(res);
        }
    }

    private void saveRefundToPlEshopNotifyChange(EshopNotifyChange notifyChange, InvokeMessageEntity invokeMessage, XiaoHongShuResponse messageObj) {
        try {
            if (StringUtils.isBlank(invokeMessage.getMessage()) || notifyChange == null || messageObj == null) {
                return;
            }
            if (!StringUtils.equals(messageObj.getMsgTag(), Refund) &&
                    !StringUtils.equals(messageObj.getMsgTag(), RefundFinished) &&
                    !StringUtils.equals(messageObj.getMsgTag(), RefundClosed)) {
                return;
            }
            OrderEntity curRefundMessage = JsonUtils.toObject(messageObj.getData(), OrderEntity.class);
            if (curRefundMessage == null) {
                return;
            }
            EshopNotifyChange curNotifyChange = new EshopNotifyChange();
            BeanUtil.copyProperties(notifyChange, curNotifyChange);
            curNotifyChange.setId(UId.newId());
            curNotifyChange.setUpdateTime(DateUtils.getDate());
            curNotifyChange.setContent(messageObj.getData());
            TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
            TMCType tmcType;
            UpdateOrderRefundStateRequest request = new UpdateOrderRefundStateRequest();
            request.setTradeOrderId(curNotifyChange.getTradeOrderId());
            request.setShopId(curNotifyChange.getEshopId());
            if (StringUtils.equals(messageObj.getMsgTag(), RefundClosed)) {
                tmcType = TMCType.REFUND_STOP_CANCEL;
                request.setRefundState(ReturnState.NONE);
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CANCEL_BY_TMC);
            } else {
                tmcType = TMCType.REFUND_STOP;
                if (StringUtils.equals(messageObj.getMsgTag(), RefundFinished)){
                    request.setRefundState(ReturnState.SUCCESS);
                }else{
                    request.setRefundState(ReturnState.REFUNDING);
                }
                request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CREATE_BY_TMC);
            }
            curNotifyChange.setType(tmcType);
            EshopSaleOrderService eshopSaleOrderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
            List<EshopNotifyChange> eshopNotifyChanges = tmcMapper.queryMessageChangeSorted(curNotifyChange.getProfileId(),
                    Collections.singletonList(curNotifyChange.getTradeOrderId()), curNotifyChange.getEshopId(), TMCType.REFUND_STOP.getCode());
            EshopNotifyChange latestNotifyChange = null;
            boolean duplicateMsg = false;
            long latestMsgTime = 0;
            long curMsgTime = curRefundMessage.getUpdateTime() != null ? curRefundMessage.getUpdateTime() : 0;
            if (CollectionUtils.isNotEmpty(eshopNotifyChanges)) {
                for (EshopNotifyChange eshopNotifyChange : eshopNotifyChanges) {
                    String content = eshopNotifyChange.getContent();
                    if (StringUtils.isEmpty(content)) {
                        continue;
                    }
                    OrderEntity oldRefundMessage = JsonUtils.toObject(content, OrderEntity.class);
                    if (oldRefundMessage == null || !StringUtils.equals(oldRefundMessage.getReturnsId(), curRefundMessage.getReturnsId())) {
                        continue;
                    }
                    long msgTime = oldRefundMessage.getUpdateTime() != null ? oldRefundMessage.getUpdateTime() : 0;
                    if (msgTime >= curMsgTime) {
                        //消息重复。当前消息已经处理过
                        duplicateMsg = true;
                        break;
                    }
                    if (latestNotifyChange == null || latestMsgTime != 0 && latestMsgTime < msgTime) {
                        latestNotifyChange = eshopNotifyChange;
                        latestMsgTime = msgTime;
                    }
                }
            }
            if (duplicateMsg) {
                return;
            }
            if (latestNotifyChange != null) {
                curNotifyChange.setId(latestNotifyChange.getId());
                tmcMapper.updateEshopNotifyChangeById(curNotifyChange);
            } else {
                tmcMapper.insertMessageChange(curNotifyChange);
            }
            eshopSaleOrderService.updateOrderRefundState(request);
        } catch (Exception ex) {
            if (ex.getMessage() != null && ex.getMessage().contains("订单尚未流入系统")) {
                return;
            }
            LOGGER.error("账套ID{},店铺ID{},保持售后消息到eshop_order_notify表失败，失败原因：{}.message:{}"
                    , notifyChange.getProfileId(), notifyChange.getEshopId(), ex.getMessage(), invokeMessage.getMessage(), ex);
        }
    }


    private EshopNotifyChange handleMessage(XiaoHongShuResponse msg, OrderEntity order) {
        if (msg == null || StringUtils.isEmpty(msg.getMsgTag())) {
            return null;
        }
        EshopNotifyChange changeInfo;
        switch (msg.getMsgTag()) {
            case OrderStatusChange:
                changeInfo = handleMessageByType(msg, TMCType.DIRECT_UPDATE_TRADE_STATUS, order);
                break;
            case OrderRemark:
                changeInfo = handleMessageByType(msg, TMCType.DIRECT_UPDATE_MEMO, order);
                break;
            case OrderAddress:
            case OrderDeliverTimeChange:
                changeInfo = handleMessageByType(msg, TMCType.Order, order);
                break;
            case Refund:
            case RefundStatusChange:
            case RefundFinished:
            case RefundClosed:
                changeInfo = handleMessageByType(msg, TMCType.RefundOrder, order);
                break;
            default:
                changeInfo = null;
        }
        return changeInfo;
    }

    private EshopNotifyChange handleMessageByType(XiaoHongShuResponse msg, TMCType tmcType, OrderEntity order) {
        EshopNotifyChange change = new EshopNotifyChange();
        if (tmcType == TMCType.DIRECT_UPDATE_MEMO || tmcType == TMCType.DIRECT_UPDATE_TRADE_STATUS) {
            change.setPubTime(order.getUpdateTime() != null ? new Date(order.getUpdateTime()) : DateUtils.getDate());
            change.setTradeOrderId(order.getOrderId());
            change.setId(UId.newId());
            change.setType(tmcType);
            change.setOnlineShopId(msg.getSellerId());
            change.setCreateTime(new Date());
            change.setUpdateTime(order.getUpdateTime() != null ? new Date(order.getUpdateTime()) : DateUtils.getDate());
            if (order.getOrderStatus() != null) {
                change.setOrderStatus(buildOrderStatus(order.getOrderStatus()));
            }
            change.setSellerMemo(order.getSellerRemarkContent());
            return change;
        }

        change.setContent(msg.getData());
        // 目前只支持订单号下载
        change.setTradeOrderId(order.getOrderId());
        // 支持售后单号下载后使用
        if (tmcType == TMCType.RefundOrder) {
            change.setRefundOrderId(order.getReturnsId());
        }
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(msg.getSellerId());
        change.setCreateTime(new Date());
        if (order.getUpdateTime() != null) {
            change.setUpdateTime(new Date(order.getUpdateTime()));
        }
        return change;
    }

    private TradeStatus buildOrderStatus(Integer packageStatus) {
        if (packageStatus == 1) {
            return TradeStatus.WAIT_BUYER_PAY;
        }
        if (packageStatus == 2) {
            return TradeStatus.WAIT_SELLER_SEND_GOODS;
        }
        if (packageStatus == 3) {
            return TradeStatus.WAIT_SELLER_SEND_GOODS;
        }
        if (packageStatus == 4) {
            return TradeStatus.WAIT_SELLER_SEND_GOODS;
        }
        if (packageStatus == 5) {
            return TradeStatus.SELLER_CONSIGNED_PART;
        }
        if (packageStatus == 6) {
            return TradeStatus.WAIT_BUYER_CONFIRM_GOODS;
        }
        if (packageStatus == 7) {
            return TradeStatus.TRADE_FINISHED;
        }
        if (packageStatus == 8) {
            return TradeStatus.ALL_CLOSED;
        }
        if (packageStatus == 9) {
            return TradeStatus.ALL_CLOSED;
        }
        if (packageStatus == 10) {
            return TradeStatus.WAIT_BUYER_CONFIRM_GOODS;
        }
        return null;
    }


    private TmcInvokeRequest buildUpdateAddressRequest(OrderEntity msg) {
        TmcInvokeRequest request = new TmcInvokeRequest();
        AddressChangeMessage changeMessage = new AddressChangeMessage();
        request.setTradeId(msg.getOrderId());
        request.setMessage(JsonUtils.toJson(changeMessage));
        request.setMethod(TmcNotifyMethodEnum.MODIFY_ADDRESS_NOTIFY);
        return request;
    }

    @Override
    public String serviceName() {
        return "xiaohongshuInvoker";
    }

}
