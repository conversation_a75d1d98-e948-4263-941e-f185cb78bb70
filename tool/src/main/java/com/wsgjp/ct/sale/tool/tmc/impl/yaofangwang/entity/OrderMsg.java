package com.wsgjp.ct.sale.tool.tmc.impl.yaofangwang.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OrderMsg {
    private Integer type;
    private Data data;

    @JsonProperty("type")
    public Integer getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(Integer value) {
        this.type = value;
    }

    @JsonProperty("data")
    public Data getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(Data value) {
        this.data = value;
    }

    public static class Data {
        private String orderID;
        private Long orderType;
        private String shopID;
        private Long orderPrescription;
        private Long status;
        private Long operatorTime;
        private String afterSaleId;

        public String getAfterSaleId() {
            return afterSaleId;
        }

        public void setAfterSaleId(String afterSaleId) {
            this.afterSaleId = afterSaleId;
        }

        @JsonProperty("orderId")
        public String getOrderID() {
            return orderID;
        }

        @JsonProperty("orderId")
        public void setOrderID(String value) {
            this.orderID = value;
        }

        @JsonProperty("orderType")
        public Long getOrderType() {
            return orderType;
        }

        @JsonProperty("orderType")
        public void setOrderType(Long value) {
            this.orderType = value;
        }

        @JsonProperty("shopId")
        public String getShopID() {
            return shopID;
        }

        @JsonProperty("shopId")
        public void setShopID(String value) {
            this.shopID = value;
        }

        @JsonProperty("orderPrescription")
        public Long getOrderPrescription() {
            return orderPrescription;
        }

        @JsonProperty("orderPrescription")
        public void setOrderPrescription(Long value) {
            this.orderPrescription = value;
        }

        @JsonProperty("status")
        public Long getStatus() {
            return status;
        }

        @JsonProperty("status")
        public void setStatus(Long value) {
            this.status = value;
        }

        @JsonProperty("operatorTime")
        public Long getOperatorTime() {
            return operatorTime;
        }

        @JsonProperty("operatorTime")
        public void setOperatorTime(Long value) {
            this.operatorTime = value;
        }
    }
}