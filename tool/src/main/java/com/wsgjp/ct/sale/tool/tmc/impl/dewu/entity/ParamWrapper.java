package com.wsgjp.ct.sale.tool.tmc.impl.dewu.entity;


/**
 * <AUTHOR>
 */
public class ParamWrapper {

    /**
     * 消息唯一id
     */
    private String uuid;

    /**
     * 加密后消息体
     */
    private String msg;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 消息类型名称
     */
    private String name;

    private MsgEntity msgEntity;

    public MsgEntity getMsgEntity() {
        return msgEntity;
    }

    public void setMsgEntity(MsgEntity msgEntity) {
        this.msgEntity = msgEntity;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}