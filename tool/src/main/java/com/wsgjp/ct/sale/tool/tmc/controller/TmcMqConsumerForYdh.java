package com.wsgjp.ct.sale.tool.tmc.controller;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.common.BaseInfoCacheUtil;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundDownloadTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.DownloadType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EShopRefundDownloadTask;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopSaleOrderDownloadTask;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopRefundDownloadResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopSaleOrderDownloadResponse;
import com.wsgjp.ct.sale.biz.eshoporder.impl.order.EshopSaleOrderDownloadPreciseImpl;
import com.wsgjp.ct.sale.biz.eshoporder.impl.refund.EshopRefundDownloaderPreciseImpl;
import com.wsgjp.ct.sale.common.constant.SyncOrderConst;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMsgByToken;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import ngp.idgenerator.UId;
import ngp.service.component.job.Consumer;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2023/11/27 10:59
 */
@Service
@ConditionalOnProperty(value = "eshoporder-tool-order.enabled", havingValue = "true")
public class TmcMqConsumerForYdh implements Consumer<TmcOrderMsgByToken> {

    private static final Logger logger = LoggerFactory.getLogger(TmcMqConsumerForYdh.class);
    private final BaseInfoCacheUtil cacheUtil;
    private final MonitorService monitorService;
    private final EshopRefundDownloaderPreciseImpl refundImpl;
    private final EshopTmcOrderMsgMapper mapper;

    public TmcMqConsumerForYdh(BaseInfoCacheUtil cacheUtil, MonitorService monitorService, EshopRefundDownloaderPreciseImpl refundImpl, EshopTmcOrderMsgMapper mapper) {
        this.cacheUtil = cacheUtil;
        this.monitorService = monitorService;
        this.refundImpl = refundImpl;
        this.mapper = mapper;
    }

    @Override
    public boolean executeTag(String s, String s1, long l, TmcOrderMsgByToken message) {
        BigInteger profileId = message.getProfileId();
        try {
            message.setMessageId(s);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            EshopTmcOrderMsgEntity tmcOrderMsg = initEshopTmcOrderMsgEntity(message);
            mapper.insertTmcOrderMsg(tmcOrderMsg);
            long delay = DateUtils.getDate().getTime() - message.getModifyTime().getTime();
            monitorService.recordSum(MonitorTypeEnum.PL_BS_ORDER_DOWNLOAD_TMC_QPS.getTopic(), ShopType.MiddleGroundDingHuo, 1);
            monitorService.recordTP(MonitorTypeEnum.PL_BS_ORDER_DOWNLOAD_TMC_TP_DELAY.getTopic(), ShopType.MiddleGroundDingHuo, delay);
            if (StringUtils.isEmpty(message.getToken())) {
                throw new RuntimeException("云订货TMC消息实体没有token，无法消费：" + JsonUtils.toJson(message));
            }
            EshopInfo eshopInfo = doGetEshopByToken(profileId, message.getToken());
            EshopSaleOrderDownloadTask task = new EshopSaleOrderDownloadTask();
            task.setTaskId(s);
            task.setEshopInfo(eshopInfo);
            task.setOtypeId(eshopInfo.getOtypeId());
            task.setFilterStr(message.getTradeId());
            task.setDownloadType(DownloadType.TMC_BY_TRADE_ID);
            EshopSaleOrderDownloadPreciseImpl downloader = EshopSaleOrderDownloadPreciseImpl.getInstance();
            EshopSaleOrderDownloadResponse response = downloader.download(task);
            doDownloadRefund(eshopInfo, message);
            long time = stopWatch.getTime();
            monitorService.recordTP(MonitorTypeEnum.PL_BS_ORDER_DOWNLOAD_TMC_USE_TIME.getTopic(), ShopType.MiddleGroundDingHuo, time);
            tmcOrderMsg.setEshopId(eshopInfo.getOtypeId());
            tmcOrderMsg.setMsgUpdateTime(DateUtils.getDate());
            tmcOrderMsg.setMsgStatus(1);
            tmcOrderMsg.setUpdateTime(DateUtils.getDate());
            mapper.updateTmcOrderMsg(tmcOrderMsg);
            if (!response.isSuccess()) {
                throw new RuntimeException(response.getMessage());
            }
        } catch (Exception ex) {
            logger.error("账套ID【{}】消息报文：{}，处理云订货TMC消息时报错：{}", profileId, JsonUtils.toJson(message), ex.getMessage(), ex);
        }
        return true;
    }

    private EshopTmcOrderMsgEntity initEshopTmcOrderMsgEntity(TmcOrderMsgByToken message) {
        EshopTmcOrderMsgEntity msgEntity = new EshopTmcOrderMsgEntity();
        msgEntity.setId(UId.newId());
        msgEntity.setTradeOrderId(message.getTradeId());
        msgEntity.setRefundOrderId(message.getRefundId());
        msgEntity.setMsgCreateTime(DateUtils.getDate());
        msgEntity.setMsgUpdateTime(DateUtils.getDate());
        msgEntity.setMsgConsumptionTime(DateUtils.getDate());
        msgEntity.setTradeStatus(TradeStatus.ABNORMAL);
        msgEntity.setShopType(ShopType.MiddleGroundDingHuo);
        msgEntity.setEshopId(BigInteger.ZERO);
        msgEntity.setProfileId(message.getProfileId());
        msgEntity.setMessage(JsonUtils.toJson(message));
        msgEntity.setMsgStatus(0);
        msgEntity.setCreateTime(DateUtils.getDate());
        //平台没有返回更新时间，平台推送一次就更新一次
        msgEntity.setUpdateTime(DateUtils.getDate());
        return msgEntity;
    }

    private void doDownloadRefund(EshopInfo eshopInfo, TmcOrderMsgByToken message) {
        if (StringUtils.isEmpty(message.getRefundId())) {
            return;
        }
        List<EshopOrderEntity> apiOrders = new ArrayList<>();
        EshopOrderEntity entity = new EshopOrderEntity();
        entity.setProfileId(eshopInfo.getProfileId());
        entity.setEshopId(eshopInfo.getOtypeId());
        entity.setRefundId(message.getRefundId());
        entity.setTradeId(message.getTradeId());
        apiOrders.add(entity);
        EShopRefundDownloadTask task = new EShopRefundDownloadTask();
        task.setApiOrders(apiOrders);
        task.setRefundId(message.getRefundId());
        task.setTradeId(message.getTradeId());
        task.setEshopInfo(eshopInfo);
        task.setOtypeId(eshopInfo.getOtypeId());
        task.setDownloadType(RefundDownloadTypeEnum.REFUND_TMC);
        EshopRefundDownloadResponse download = refundImpl.download(task, null);
        if (!download.isSuccess()) {
            logger.error("消息：{}。TMC下载售后单失败:{}", JsonUtils.toJson(message), download.getMessage());
        }
    }

    private EshopInfo doGetEshopByToken(BigInteger profileId, String token) {
        Otype otype = cacheUtil.getOtypeByToken(profileId, token);
        if (otype == null) {
            throw new RuntimeException("根据token无法查询到本地网店信息，请确认授权是否过期！");
        }
        return otype.getEshopInfo();
    }

    @Override
    public String getJobName() {
        return SyncOrderConst.TMC_MQ_PRODUCE_TOPIC;
    }
}
