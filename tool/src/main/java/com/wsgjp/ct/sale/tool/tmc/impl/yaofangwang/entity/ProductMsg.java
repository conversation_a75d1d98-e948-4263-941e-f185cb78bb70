package com.wsgjp.ct.sale.tool.tmc.impl.yaofangwang.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ProductMsg {
    private Integer type;
    private Data data;

    @JsonProperty("type")
    public Integer getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(Integer value) {
        this.type = value;
    }

    @JsonProperty("data")
    public Data getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(Data value) {
        this.data = value;
    }


    public static class Data {
        private Long shopID;
        private String traceID;
        private List<DataList> dataList;
        private String crateTime;

        @JsonProperty("shopId")
        public Long getShopID() {
            return shopID;
        }

        @JsonProperty("shopId")
        public void setShopID(Long value) {
            this.shopID = value;
        }

        @JsonProperty("traceId")
        public String getTraceID() {
            return traceID;
        }

        @JsonProperty("traceId")
        public void setTraceID(String value) {
            this.traceID = value;
        }

        @JsonProperty("dataList")
        public List<DataList> getDataList() {
            return dataList;
        }

        @JsonProperty("dataList")
        public void setDataList(List<DataList> value) {
            this.dataList = value;
        }

        @JsonProperty("crateTime")
        public String getCrateTime() {
            return crateTime;
        }

        @JsonProperty("crateTime")
        public void setCrateTime(String value) {
            this.crateTime = value;
        }
    }


    public static class DataList {
        private String medicineCode;
        private String create;
        private String match;
        private String message;

        @JsonProperty("medicineCode")
        public String getMedicineCode() {
            return medicineCode;
        }

        @JsonProperty("medicineCode")
        public void setMedicineCode(String value) {
            this.medicineCode = value;
        }

        @JsonProperty("create")
        public String getCreate() {
            return create;
        }

        @JsonProperty("create")
        public void setCreate(String value) {
            this.create = value;
        }

        @JsonProperty("match")
        public String getMatch() {
            return match;
        }

        @JsonProperty("match")
        public void setMatch(String value) {
            this.match = value;
        }

        @JsonProperty("message")
        public String getMessage() {
            return message;
        }

        @JsonProperty("message")
        public void setMessage(String value) {
            this.message = value;
        }
    }
}