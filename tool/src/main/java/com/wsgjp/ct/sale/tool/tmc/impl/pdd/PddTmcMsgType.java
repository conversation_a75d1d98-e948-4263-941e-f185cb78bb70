package com.wsgjp.ct.sale.tool.tmc.impl.pdd;

import com.wsgjp.ct.sale.common.enums.TMCType;
import ngp.utils.ArrayUtils;
import ngp.utils.StringUtils;

/**
 * 拼多多tmc消息类型
 */

public enum PddTmcMsgType {
    /**
     * 修改交易收货地址消息:发货前，卖家修改交易的收货地址，会产生此消息
     */
    pdd_trade_TradeLogisticsAddressChanged("pdd_trade_TradeLogisticsAddressChanged", TMCType.Order),

    /**
     * 交易成功消息:买家确认收货和订单自动收货后，会产生此消息
     */
    pdd_trade_TradeSuccess("pdd_trade_TradeSuccess", TMCType.DIRECT_UPDATE_TRADE_STATUS),

    /**
     * 卖家发货消息:当通过api完成发货操作时，会产生此消息； 当通过页面完成发货操作时，会产生此消息;
     */
    pdd_trade_TradeSellerShip("pdd_trade_TradeSellerShip", TMCType.DIRECT_UPDATE_TRADE_STATUS),

    /**
     * 交易确认消息:普通订单创建成功后，会触发此消息。定金订单付完尾款后，会产生此消息。
     */
    pdd_trade_TradeConfirmed("pdd_trade_TradeConfirmed", TMCType.Order),

    /**
     * 交易备注修改消息:在交易创建后，卖家修改交易备注，会产生此消息
     */
    pdd_trade_TradeMemoModified("pdd_trade_TradeMemoModified", TMCType.Order),

    /**
     * 订单服务承诺消息
     */
    pdd_chat_OrderPromise("pdd_chat_OrderPromise", TMCType.PDD_Promise),

    /**
     * 风控状态变更:订单消息增加“风控状态变更”消息，当订单风控状态变更时，触发消息提醒
     */
    pdd_trade_TradeRiskChanged("pdd_trade_TradeRiskChanged", TMCType.Order),

    /**
     * 买家备注修改消息:在交易创建后，买家修改交易备注，会产生此消息
     */
    pdd_trade_BuyerMemoModified("pdd_trade_BuyerMemoModified", TMCType.Order),

    /**
     * 跨境全托管发货单交易确认消息:跨境全托管发货单交易确认消息
     */
    pdd_fulfillment_TradeConfirmed("pdd_fulfillment_TradeConfirmed", TMCType.Order),

    /**
     * 同意退款协议消息:商家/系统/平台客服 同意退款时触发此消息，商家/系统/平台客服 同意退货时触发此消息
     */
    pdd_refund_RefundAgreeAgreement("pdd_refund_RefundAgreeAgreement", TMCType.RefundOrder),

    /**
     * 退款创建消息:消费者/系统/平台客服 创建售后单时，会触发此消息。
     */
    pdd_refund_RefundCreated("pdd_refund_RefundCreated", TMCType.RefundOrder),

    /**
     * 买家修改退款协议消息
     * 消费者/平台客服 修改售后单时，触发此消息；消费者 重新申请售后单时，触发此消息。
     */
    pdd_refund_RefundBuyerModifyAgreement("pdd_refund_RefundBuyerModifyAgreement", TMCType.RefundOrder),

    /**
     * 买家退货给卖家消息
     * 消费者/平台客服在填写/修改退货信息给商家时，会触发此消息；卖家在页面同意买家拒收商品后，系统检测到发货物流轨迹有拒收的时候，会触发此消息。
     */
    pdd_refund_RefundBuyerReturnGoods("pdd_refund_RefundBuyerReturnGoods", TMCType.RefundOrder),

    /**
     * 发表退款留言消息
     * 消费者/平台客服在售后发表留言时，会触发此消息。
     */
    pdd_refund_RefundCreateMessage("pdd_refund_RefundCreateMessage", TMCType.RefundOrder),

    /**
     * 售后单关闭消息
     * 售后单关闭时，推送对应消息
     */
    pdd_refund_RefundClosed("pdd_refund_RefundClosed", TMCType.RefundOrder),

    pdd_goods_GoodsOffShelf("pdd_goods_GoodsOffShelf", TMCType.Ptype),
    pdd_goods_GoodsDelete("pdd_goods_GoodsDelete", TMCType.Ptype),
    pdd_goods_GoodsOnShelf("pdd_goods_GoodsOnShelf", TMCType.Ptype),
    pdd_goods_GoodsUpdate("pdd_goods_GoodsUpdate", TMCType.Ptype),
    pdd_goods_GoodsAdd("pdd_goods_GoodsAdd", TMCType.Ptype),
    ;

    private final String msgType;
    /**
     * 对应ngp TMC业务类型
     */
    private final TMCType tmcType;

    PddTmcMsgType(String msgType, TMCType tmcType) {
        this.msgType = msgType;
        this.tmcType = tmcType;
    }

    public String getMsgType() {
        return msgType;
    }

    public TMCType getTmcType() {
        return tmcType;
    }

    public static PddTmcMsgType getPddTmcMsgTypeByMsgTypeName(String flag) {
        PddTmcMsgType[] msgTypes = PddTmcMsgType.values();
        if (ArrayUtils.isEmpty(msgTypes) || StringUtils.isEmpty(flag)) {
            return null;
        }
        for (PddTmcMsgType msgType : msgTypes) {
            if(msgType.getMsgType().equals(flag)){
                return  msgType;
            }
        }
        return null;
    }
}
