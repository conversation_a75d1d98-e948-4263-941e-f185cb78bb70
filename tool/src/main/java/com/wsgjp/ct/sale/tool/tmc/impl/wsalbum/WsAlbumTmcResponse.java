package com.wsgjp.ct.sale.tool.tmc.impl.wsalbum;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class WsAlbumTmcResponse {
    private String errcode;
    private String errmsg;
    private String result;

    @JsonProperty("errcode")
    public String getErrcode() {
        return errcode;
    }

    @JsonProperty("errcode")
    public void setErrcode(String errcode) {
        this.errcode = errcode;
    }

    @JsonProperty("errmsg")
    public String getErrmsg() {
        return errmsg;
    }

    @JsonProperty("errmsg")
    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    @JsonProperty("result")
    public String getResult() {
        return result;
    }

    @JsonProperty("result")
    public void setResult(String result) {
        this.result = result;
    }
}
