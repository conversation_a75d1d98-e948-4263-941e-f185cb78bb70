package com.wsgjp.ct.sale.tool.tmc.utils;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.bifrost.entity.BifrostApiMonitorInfo;
import com.wsgjp.ct.sale.monitor.bifrost.entity.BifrostApiMonitorTypeEnum;
import com.wsgjp.ct.sale.platform.constraint.PlatformConstants;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class TmcMonitor {

    private static final Logger LOGGER = LoggerFactory.getLogger(TmcMonitor.class);

    public static void monitor(BifrostApiMonitorInfo apiMonitorInfo, String result, InvokeMessageEntity args, long executionTime, boolean hasEx) {
        if (Objects.isNull(args) || Objects.isNull(args.getShopType())) {
            return;
        }
        ShopType shopType = args.getShopType();
        if (Objects.isNull(apiMonitorInfo)) {
            throw new RuntimeException(String.format("%s平台构建监控数据异常,返回的监控信息为空!", shopType.getName()));
        }
        try {
            if (StringUtils.isEmpty(result)) {
                result = "null";
            }
            String apiName = StringUtils.isEmpty(apiMonitorInfo.getApiName()) ? "" : apiMonitorInfo.getApiName();
            if (hasEx) {
                LOGGER.info("平台名称：{},tmc handler：{},请求参数：{},异常信息：{}", shopType.getName(), apiName, JsonUtils.toJson(args), apiMonitorInfo.getMessage());
            } else {
                LOGGER.info("平台名称：{},tmc handler：{},请求参数：{},响应参数：{},错误信息：{}", shopType.getName(), apiName, JsonUtils.toJson(args), result, apiMonitorInfo.getMessage());
            }

            recordLogMonitor(apiMonitorInfo, shopType, executionTime, hasEx);
        } catch (Exception ex) {
            LOGGER.error("%平台构建监控数据异常=》平台名称：{},tmc handler：{},请求参数：{},响应参数：{},错误信息：{}", shopType.getName(), apiMonitorInfo.getApiName(), JsonUtils.toJson(args), result, apiMonitorInfo.getMessage());
        }
    }


    private static void recordLogMonitor(BifrostApiMonitorInfo apiMonitorInfo, ShopType shopType, long executionTime, boolean hasEx) {
        if (!apiMonitorInfo.getLogApiList().contains(apiMonitorInfo.getApiName())) {
            return;
        }
        MonitorService monitorService = BeanUtils.getBean(MonitorService.class);
        monitorService.recordSum(BifrostApiMonitorTypeEnum.PL_INTERFACE_YQ_TP_TIME.getTopic(), shopType, "serviceName", apiMonitorInfo.getApiName(), executionTime);
        monitorService.recordSum(BifrostApiMonitorTypeEnum.PL_INTERFACE_YQ_REQ_ERROR_TOTAL_COUNT.getTopic(), shopType, "serviceName", apiMonitorInfo.getApiName(), 1);
        if (hasEx || !apiMonitorInfo.getSuccess()) {
            monitorService.recordSum(BifrostApiMonitorTypeEnum.PL_INTERFACE_YQ_REQ_ERROR_INFO_TOTAL_COUNT.getTopic(), shopType, "serviceName", apiMonitorInfo.getApiName(), 1);
        }
    }

    public static BifrostApiMonitorInfo getApiMonitorInfo(String serviceName, boolean isSuccess, String errMsg, String errCode) {
        BifrostApiMonitorInfo monitor = new BifrostApiMonitorInfo();
        monitor.setStatusCode(errCode);
        monitor.setMessage(errMsg);
        monitor.setSuccess(isSuccess);
        monitor.setApiName(serviceName);
        monitor.addLogApi(serviceName);
        return monitor;
    }


    public static void recordApiMonitor(BifrostApiMonitorInfo apiMonitorInfo, ShopType shopType, long executionTime, boolean hasEx) {
        try {
            MonitorService monitorService = BeanUtils.getBean(MonitorService.class);
            String apiName = apiMonitorInfo.getApiName();
            String applicationName = apiMonitorInfo.getApplicationName();
            String topic = BifrostApiMonitorTypeEnum.PL_INTERFACE_PLATFORM_API_REQ_TOTAL_COUNT.getTopic();
            String errorTopic = BifrostApiMonitorTypeEnum.PL_INTERFACE_PLATFORM_API_REQ_ERROR_TOTAL_COUNT.getTopic();
            String delayTopic = BifrostApiMonitorTypeEnum.PL_INTERFACE_PLATFORM_API_TP_TIME.getTopic();
            Tags tags = Tags.of(Tag.of("shopType", shopType.getName())).and(Tag.of(PlatformConstants.MONITOR_API_NAME, apiName));
            //记录API接口TP 响应时间
            monitorService.recordSum(delayTopic, shopType, PlatformConstants.MONITOR_API_NAME, apiName, executionTime);
            if(StringUtils.isNotEmpty(applicationName)){
                tags.and(Tag.of(PlatformConstants.MONITOR_APP_NAME, applicationName));
                monitorService.recordSum(topic,tags, 1);
                if (hasEx || !apiMonitorInfo.getSuccess()) {
                    monitorService.recordSum(errorTopic, tags, 1);
                }
                return;
            }
            //记录API接口请求总次数
            monitorService.recordSum(topic, tags, 1);
            if (hasEx || !apiMonitorInfo.getSuccess()) {
                //记录API接口请求异常总数
                monitorService.recordSum(errorTopic, tags, 1);
            }
        } catch (Throwable ex) {
            LOGGER.error("记录API请求日志监控报错：{}", ex.getMessage(), ex);
        }
    }
}
