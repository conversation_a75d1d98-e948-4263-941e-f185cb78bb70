package com.wsgjp.ct.sale.tool.tmc.impl.tmall;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.constant.PlatformTmcType;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.OrderRequest;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class TmSuperMarketOrderHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(TmSuperMarketOrderHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    public TmSuperMarketOrderHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        OrderRequest apiOrder;
        try {
            apiOrder = convertToJavaBean(tmMessage, OrderRequest.class);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成OrderRequest实体出错，错误信息：{}",shopTypeName,e.getMessage());
            return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<response>\n" +
                    "  <success>false</success>\n" +
                    "  <errorCode>201</errorCode>\n" +
                    "  <errorMsg>系统异常</errorMsg>\n" +
                    "  <retry>false</retry>\n" +
                    "</response>";
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiOrder.getSupplierId(), invokeMessage.getShopType().getCode());
        if(Objects.isNull(eshopRegister)){
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}",invokeMessage.getProfileId(),shopTypeName,apiOrder.getSupplierId(),tmMessage);
            return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<response>\n" +
                    "  <success>false</success>\n" +
                    "  <errorCode>201</errorCode>\n" +
                    "  <errorMsg>系统异常</errorMsg>\n" +
                    "  <retry>false</retry>\n" +
                    "</response>";
        }
        LOGGER.info("profileId:{},eshopId:{},店铺类型：{}",eshopRegister.getProfileId(),eshopRegister.getId(),shopTypeName);
        try{
            saveTmcOrderMsg(eshopRegister,tmMessage,apiOrder);
        }catch (Exception ex){
            LOGGER.error("{}保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,eshopRegister.getProfileId(),eshopRegister.getId(),tmMessage,ex.getMessage(),ex);
            return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<response>\n" +
                    "  <success>false</success>\n" +
                    "  <errorCode>201</errorCode>\n" +
                    "  <errorMsg>系统异常</errorMsg>\n" +
                    "  <retry>false</retry>\n" +
                    "</response>";
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setOnlineShopId(apiOrder.getSupplierId());
        eshopNotifyChange.setTradeOrderId(apiOrder.getBizOrderCode());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(tmMessage);
        eshopNotifyChange.setPlatformMsgType(PlatformTmcType.ORDER_MSG);
        SupportUtil.doOrderNotify(apiOrder.getSupplierId(),eshopNotifyChange,invokeMessage.getShopType().getCode());
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<response>\n" +
                "  <success>true</success>\n" +
                "  <errorCode>0</errorCode>\n" +
                "  <errorMsg>成功</errorMsg>\n" +
                "  <retry>false</retry>\n" +
                "</response>";
    }

    public int saveTmcOrderMsg(EshopRegisterNotify eshopRegister, String tmMessage, OrderRequest orderRequest){
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(eshopRegister.getProfileId(),eshopRegister.getId(),orderRequest.getBizOrderCode());
        if (Objects.isNull(orderMsgEntity)){
            orderMsgEntity = buildEshopTmcOrderMsgEntity(eshopRegister,tmMessage,orderRequest);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        }else {
            //orderMsgEntity.setStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(EshopRegisterNotify eshopRegister, String tmMessage, OrderRequest orderRequest) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(eshopRegister.getProfileId());
        tmcOrderMsgEntity.setEshopId(eshopRegister.getId());
        tmcOrderMsgEntity.setShopType(ShopType.valueOf(eshopRegister.getType()));
        tmcOrderMsgEntity.setTradeOrderId(orderRequest.getBizOrderCode());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        //默认值已付款
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        try {
            Date date = DateUtils.parseDate(orderRequest.getOrderCreateTime(), "yyyy-mm-dd HH:mm:ss");
            tmcOrderMsgEntity.setCreateTime(date);
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (Exception e) {

        }
        return tmcOrderMsgEntity;
    }

    @Override
    public String serviceName() {
        return "qimen.alibaba.ascp.uop.consignorder.notify";
    }
}
