package com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark;

import com.doudian.open.spi.yunc_wms_outbound_create.param.YuncWmsOutboundCreateParam;
import com.doudian.open.utils.JsonUtil;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.profile.ProfileApi;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcOrderMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

@Component
public class DouDianSupermarkOrderHandler extends DDSNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DouDianSupermarkOrderHandler.class);
    private final ProfileApi profileApi;
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;

    public DouDianSupermarkOrderHandler(ProfileApi profileApi, EshopTmcOrderMsgMapper tmcOrderMsgMapper) {
        this.profileApi = profileApi;
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======抖店超市进入invoker方法======");
        String tmMessage = invokeMessage.getMessage();
        YuncWmsOutboundCreateParam yuncWmsInboundCreateParam;
        try {
            yuncWmsInboundCreateParam = JsonUtil.fromJson(tmMessage, YuncWmsOutboundCreateParam.class);
        } catch (Exception e) {
            String errMsg = String.format("抖店超市tmMessage数据转换成yuncWmsInboundCreateParam实体出错，错误信息：%s", e.getMessage());
            return JsonUtils.toJson(new GeneralResult(500L,"序列化失败",null));
        }
        try {
            EshopRegisterNotify notify = SupportUtil.buildNotify(yuncWmsInboundCreateParam.getWarehouseCode(), 148);
            saveTmcOrderMsg(invokeMessage.getProfileId(), notify.getId(), tmMessage, yuncWmsInboundCreateParam);
        } catch (Exception ex) {
            LOGGER.error("错误",ex);
            LOGGER.error("抖店超市保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", invokeMessage.getProfileId(), invokeMessage.getEshopId(), tmMessage, ex.getMessage());
            return JsonUtils.toJson(new GeneralResult(500, "抖店超市订单数据保存数据库出错!", null));
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        String tradeId = yuncWmsInboundCreateParam.getOutboundOrderNo();
        eshopNotifyChange.setTradeOrderId(tradeId);
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setContent(tmMessage);
        SupportUtil.doOrderNotify(yuncWmsInboundCreateParam.getWarehouseCode(), eshopNotifyChange, ShopType.DouDianSupermarket.getCode());
        return JsonUtils.toJson(new GeneralResult(0L,"success",null));
    }

    private int saveTmcOrderMsg(BigInteger profileId, BigInteger eshopId, String tmMessage, YuncWmsOutboundCreateParam apiOrder) {
        EshopTmcOrderMsgEntity orderMsgEntity = tmcOrderMsgMapper.queryTmcOrderMsgByTradeId(profileId, eshopId, apiOrder.getOutboundOrderNo());
        if (Objects.isNull(orderMsgEntity)) {
            orderMsgEntity = buildEshopTmcOrderMsgEntity(profileId, eshopId, tmMessage, apiOrder);
            return tmcOrderMsgMapper.insertTmcOrderMsg(orderMsgEntity);
        } else {
            orderMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            orderMsgEntity.setUpdateTime(new Date());
            orderMsgEntity.setMessage(tmMessage);
            return tmcOrderMsgMapper.updateTmcOrderMsg(orderMsgEntity);
        }
    }

    private EshopTmcOrderMsgEntity buildEshopTmcOrderMsgEntity(BigInteger profileId, BigInteger eshopId, String tmMessage, YuncWmsOutboundCreateParam apiOrder) {
        EshopTmcOrderMsgEntity tmcOrderMsgEntity = new EshopTmcOrderMsgEntity();
        tmcOrderMsgEntity.setId(UId.newId());
        tmcOrderMsgEntity.setProfileId(profileId);
        tmcOrderMsgEntity.setEshopId(eshopId);
        tmcOrderMsgEntity.setShopType(ShopType.DouDianSupermarket);
        tmcOrderMsgEntity.setTradeOrderId(apiOrder.getOutboundOrderNo());
        tmcOrderMsgEntity.setMessage(tmMessage);
        tmcOrderMsgEntity.setMsgStatus(0);
        tmcOrderMsgEntity.setMsgCreateTime(new Date());
        tmcOrderMsgEntity.setMsgUpdateTime(new Date());
        tmcOrderMsgEntity.setTradeStatus(TradeStatus.WAIT_SELLER_SEND_GOODS);
        try {
            Date date = null;
            if (apiOrder.getOrderTime() != null){
                date = new Date(apiOrder.getOrderTime() * 1000);
            }else {
                date = new Date();
            }
            tmcOrderMsgEntity.setCreateTime(date);
            //平台没有返回更新时间，平台推送一次就更新一次
            tmcOrderMsgEntity.setUpdateTime(new Date());
        } catch (Exception e) {
        }
        return tmcOrderMsgEntity;
    }

    @Override
    public String serviceName() {
        return "yunc.wms.outbound.create";
    }
}
