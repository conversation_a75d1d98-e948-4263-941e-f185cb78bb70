package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DDTmcRefundHandler extends DDNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(DDTmcRefundHandler.class);

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.debug("======抖店进入DDTmcRefundHandler.invoker方法======");
        String message = invokeMessage.getMessage();
        if (StringUtils.isEmpty(message)) {
            LOGGER.warn("抖店消息为空");
            return JsonUtils.toJson(new GeneralResult(500L, "消息为空", null));
        }
        LOGGER.debug("账套ID{},doudianTmcMessage:{}", invokeMessage.getProfileId(), message);
        EshopNotifyChange change = buildEshopNotifyChange(message);
        if (Objects.isNull(change)) {
            //当前未对接该类型的 tmc 消息
            LOGGER.info("账套ID{},转换成EshopNotifyChange为空", invokeMessage.getProfileId());
            return JsonUtils.toJson(new GeneralResult(0L, "success", null));
        }
        SupportUtil.doOrderNotify(change.getOnlineShopId(), change, invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.Doudian.getCode());
        return JsonUtils.toJson(new GeneralResult(0L, "success", null));
    }

    private EshopNotifyChange buildEshopNotifyChange(String message) {
        Map<String, Object> ddMessage = JsonUtils.toHashMap(message);
        Map<String, Object> data = JsonUtils.toHashMap(JsonUtils.toJson(ddMessage.get("data")));
        String tag = (String) ddMessage.get("tag");
        EshopNotifyChange changeInfo;
        switch (Integer.parseInt(tag)) {
            //买家发起售后申请消息
            case 200:
                //买家修改售后申请消息
            case 208:
                //售后关闭消息
            case 207:
                //退款成功消息
            case 206:
                //拒绝退款消息
            case 204:
                //买家退货给卖家消息
            case 203:
                //同意退货申请消息
            case 202:
                //同意退款消息
            case 201:
                //同意补寄申请
            case 227:
                changeInfo = handlerMessageByType(data, TMCType.Order);
                break;
            default:
                changeInfo = null;
                break;
        }
        return changeInfo;
    }

    private EshopNotifyChange handlerMessageByType(Map<String, Object> data, TMCType tmcType) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(JsonUtils.toJson(data));
        Object pId = data.get("p_id");
        Object shopId = data.get("shop_id");
        change.setTradeOrderId(pId.toString());
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(shopId.toString());
        return change;
    }

    @Override
    public String serviceName() {
        return "doudian.refund";
    }
}
