package com.wsgjp.ct.sale.tool.tmc.impl.hipac;

public class HipacTmcResponse {
    private Integer code;
    private String message;
    private String success;
    private String data;

    public HipacTmcResponse() {
    }

    public HipacTmcResponse(String message, String success) {
        this.message = message;
        this.success = success;
    }

    public HipacTmcResponse(Integer code, String message, String success) {
        this.code = code;
        this.message = message;
        this.success = success;
    }

    public HipacTmcResponse(Integer code, String message, String success, String data) {
        this.code = code;
        this.message = message;
        this.success = success;
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
