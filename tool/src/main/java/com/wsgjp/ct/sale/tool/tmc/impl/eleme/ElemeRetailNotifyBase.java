package com.wsgjp.ct.sale.tool.tmc.impl.eleme;

import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.entity.CheckSignResult;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */

public abstract class ElemeRetailNotifyBase implements MessageHandler {

    @Override
    public CheckSignResult checkSign(HttpServletRequest request, EshopTmcConfig eshopTmcConfig) {
        CheckSignResult result = new CheckSignResult();
        result.setSuccess(true);
        return result;
    }
}
