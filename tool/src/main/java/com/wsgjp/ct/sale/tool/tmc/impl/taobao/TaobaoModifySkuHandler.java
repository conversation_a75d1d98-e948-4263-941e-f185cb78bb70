package com.wsgjp.ct.sale.tool.tmc.impl.taobao;

import com.qimencloud.api.sceneqimen.request.TaobaoOpenModifyskuRequest;
import com.qimencloud.api.sceneqimen.response.TaobaoOpenModifyskuResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

/**
 * <AUTHOR>
 */
@Component
public class TaobaoModifySkuHandler extends TaobaoNotifyBase implements MessageHandler {
    private final TmcNotifyProxy notifyProxy;

    public TaobaoModifySkuHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        TaobaoOpenModifyskuResponse response = new TaobaoOpenModifyskuResponse();
        try {
            TaobaoOpenModifyskuRequest modifySkuParams = JsonUtils.toObject(invokeMessage.getMessage(), TaobaoOpenModifyskuRequest.class);
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(modifySkuParams.getSellerNick(), invokeMessage.getShopType().getCode());
            TmcInvokeRequest invokeRequest = new TmcInvokeRequest();
            invokeRequest.setProfileId(invokeMessage.getProfileId());
            invokeRequest.setShopType(invokeMessage.getShopType());
            invokeRequest.setEshopId(eshopRegister.getId());
            invokeRequest.setTradeId(modifySkuParams.getBizOrderId());
            invokeRequest.setMethod(TmcNotifyMethodEnum.MODIFY_SKU);
            invokeRequest.setMessage(invokeMessage.getMessage());
            TmcInvokeResponse resp = notifyProxy.execute(invokeRequest);
            response.setSuccess(!resp.isError());
            response.setErrorCode(resp.isError() ? "1007" : "0");
            response.setMessage(resp.isError() ? "仓库已接单不支持改SKU" : "");
        } catch (Exception e) {
            response.setSuccess(false);
            response.setErrorMsg(e.getMessage());
        }
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "taobaoModifySkuInvoker";
    }
}
