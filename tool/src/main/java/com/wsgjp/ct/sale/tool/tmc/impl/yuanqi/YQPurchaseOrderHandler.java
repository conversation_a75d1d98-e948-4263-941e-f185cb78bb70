package com.wsgjp.ct.sale.tool.tmc.impl.yuanqi;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.purchase.PurchaseOrderDownloadResult;
import com.wsgjp.ct.sale.common.constant.SyncOrderConst;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.YQPurchaseMessage;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class YQPurchaseOrderHandler extends YuanQiNotifyBase implements MessageHandler {

    private static final Logger sysLogger = LoggerFactory.getLogger(YQPurchaseOrderHandler.class);
    private static final String TradeIdKey="orderNumber";
    private static final String TradeStatusKey="orderStatus";
    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {

        try {
            sysLogger.info("元气采购消息：{}", JsonUtils.toJson(invokeMessage));
            String message = invokeMessage.getMessage();
            sysLogger.info("元气采购消息message字段：{}", message);
            YQPurchaseMessage yqOrder = getYQMsg(message);
            sysLogger.info("元气采购反序列消息为对象：{}", yqOrder==null);
            sysLogger.info("元气采购message中body字段：{}",JsonUtils.toJson(yqOrder.getBody()));
            Map<String, String> bodyMap = YQUtils.getBodyMsgMap(JsonUtils.toJson(yqOrder.getBody()));
            sysLogger.info("元气采购消息bodyMap：{}",bodyMap==null ?"null值":JsonUtils.toJson(bodyMap));
            if (Objects.isNull(bodyMap)){
                YuanQiTMCResponse response = new YuanQiTMCResponse(500, "发序列化错误");
                return JsonUtils.toJson(response);
            }
            checkMsg(!bodyMap.containsKey(TradeIdKey),"元气订单消息没有订单号");
            checkMsg(!bodyMap.containsKey(TradeStatusKey),"元气订单消息没有订单状态");
            String tradeId = bodyMap.get(TradeIdKey);
            String tradeStatus = bodyMap.get(TradeStatusKey);
            String shopAccount = yqOrder.getMerchantNumber();
            EshopNotifyChange tmcMsg = new EshopNotifyChange();
            tmcMsg.setType(TMCType.Order);
            tmcMsg.setTradeOrderId(tradeId);
            tmcMsg.setTradeStatus(tradeStatus);
            tmcMsg.setOnlineShopId(shopAccount);
            tmcMsg.setContent(message);
            tmcMsg.setEshopId(invokeMessage.getEshopId());
            tmcMsg.setProfileId(CurrentUser.getProfileId());
            tmcMsg.setSubType(2);
            tmcMsg.setShopType(ShopType.YuanQiSenLin.getCode());
            YQOrderDownloadManager download = new YQOrderDownloadManager();
            TmcOrderMessage tmcMessage = SupportUtil.buildTmcOrderMessage(tmcMsg);
            tmcMessage.setTag(SyncOrderConst.TmcPurchaseOrdersTag);
            PurchaseOrderDownloadResult result = download.doYuanQiOrderSync(tmcMessage);
            return JsonUtils.toJson(result);
            //sysLogger.info("元气采购消息开始推送消息到mq");
            //SupportUtil.doTagMqNotify(shopAccount, tmcMsg, ShopType.YuanQiSenLin.getCode(), SyncOrderConst.TmcPurchaseOrdersTag);
            //return "success";
        }catch (Exception ex){
            sysLogger.info(ex.getMessage());
            return ex.getMessage();
        }
    }

    @Override
    public String serviceName() {
        return "yuanqisenlin.purchase.order";
    }
}
