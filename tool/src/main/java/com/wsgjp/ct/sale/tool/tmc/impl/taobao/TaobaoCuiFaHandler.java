package com.wsgjp.ct.sale.tool.tmc.impl.taobao;

import com.qimencloud.api.sceneqimen.request.TaobaoOpenSellerBizLogisticTimeGetRequest;
import com.qimencloud.api.sceneqimen.response.TaobaoOpenSellerBizLogisticTimeGetResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

/**
 * <AUTHOR>
 */
@Component
public class TaobaoCuiFaHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(TaobaoCuiFaHandler.class);
    private final TmcNotifyProxy notifyProxy;

    public TaobaoCuiFaHandler(TmcNotifyProxy notifyProxy) {
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        TaobaoOpenSellerBizLogisticTimeGetResponse response = new TaobaoOpenSellerBizLogisticTimeGetResponse();
        try {
            TaobaoOpenSellerBizLogisticTimeGetRequest cuiFaMessage =
                    JsonUtils.toObject(invokeMessage.getMessage(), TaobaoOpenSellerBizLogisticTimeGetRequest.class);
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(cuiFaMessage.getSellerNick(), invokeMessage.getShopType().getCode());

            TmcInvokeRequest invokeRequest = new TmcInvokeRequest();
            invokeRequest.setProfileId(invokeMessage.getProfileId());
            invokeRequest.setTradeId(cuiFaMessage.getMainOrderId());
            invokeRequest.setMethod(TmcNotifyMethodEnum.EXPEDITE_DELIVERY);
            invokeRequest.setMessage(invokeMessage.getMessage());
            invokeRequest.setEshopId(eshopRegister.getId());
            TmcInvokeResponse resp = notifyProxy.execute(invokeRequest);
            response.setRet(!resp.isError());
            response.setRetCode(resp.isError() ? resp.getCode() : "0");
            response.setRetMsg(resp.isError() ? resp.getMessage() : "");
            TaobaoOpenSellerBizLogisticTimeGetResponse.LogisticResult result = new TaobaoOpenSellerBizLogisticTimeGetResponse.LogisticResult();
            result.setMainOrderId(cuiFaMessage.getMainOrderId());
            result.setSellerNick(cuiFaMessage.getSellerNick());
            result.setProcessCode(40L);
            response.setLogisticResult(result);
        } catch (Exception e) {
            response.setRet(false);
            response.setRetMsg(e.getMessage());
        }
        return JsonUtils.toJson(response);
    }

    @Override
    public String serviceName() {
        return "taobao.open.seller.biz.logistic.time.get";
    }
}
