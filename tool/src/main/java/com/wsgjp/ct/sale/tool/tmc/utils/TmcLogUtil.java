package com.wsgjp.ct.sale.tool.tmc.utils;

import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.common.entity.product.TmcProductMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.common.log.EshopTmcMessageLog;
import com.wsgjp.ct.sale.tool.common.log.MqConsumeLog;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;

import java.math.BigInteger;

/**
 * <AUTHOR> 2024/8/20 16:11
 */
public class TmcLogUtil {

    public static EshopTmcMessageLog buildTmcLog(EshopNotifyChange notifyChange, String method, String messageId) {
        EshopTmcMessageLog tmcLog = new EshopTmcMessageLog();
        tmcLog.setId(UId.newId());
        tmcLog.setProfileId(notifyChange.getProfileId());
        tmcLog.setEshopId(notifyChange.getEshopId());
        tmcLog.setEtypeId(BigInteger.ZERO);
        tmcLog.setTmcInfo(notifyChange.getContent());
        tmcLog.setReturnInfo("");
        tmcLog.setStatus(0);
        tmcLog.setErrorMsg("");
        tmcLog.setEshopType(notifyChange.getShopType() != null ? notifyChange.getShopType() : -1);
        tmcLog.setMethod(StringUtils.isNotBlank(method) ? method : "");
        tmcLog.setMsgId(StringUtils.isNotBlank(messageId) ? messageId : "");
        tmcLog.setTmcId(notifyChange.getTradeOrderId() != null ? notifyChange.getTradeOrderId() : "");
        return tmcLog;
    }

    public static EshopTmcMessageLog buildTmcLog(InvokeMessageEntity invokeMessage, String method, String messageId) {
        EshopTmcMessageLog tmcLog = new EshopTmcMessageLog();
        tmcLog.setId(UId.newId());
        tmcLog.setProfileId(invokeMessage.getProfileId());
        tmcLog.setEshopId(invokeMessage.getEshopId());
        tmcLog.setEtypeId(BigInteger.ZERO);
        tmcLog.setTmcInfo(invokeMessage.getMessage());
        tmcLog.setReturnInfo("");
        tmcLog.setStatus(0);
        tmcLog.setErrorMsg("");
        tmcLog.setEshopType(invokeMessage.getShopType() != null ?invokeMessage.getShopType().getCode() : -1);
        tmcLog.setMethod(StringUtils.isNotBlank(method) ? method : "");
        tmcLog.setMsgId(StringUtils.isNotBlank(messageId) ? messageId : "");
        return tmcLog;
    }


    public static MqConsumeLog buildMqLog(TmcProductMessage message, String messageId, TMCType tmcType) {
        MqConsumeLog log = new MqConsumeLog();
        log.setId(UId.newId());
        log.setState(0);
        log.setConsumeTimes(1);
        log.setEtypeId(BigInteger.ZERO);
        log.setMsgType(tmcType);
        log.setProfileId(message.getProfileId());
        log.setMsgId(messageId);
        log.setBody(JsonUtils.toJson(message));
        log.setEshopId(message.getEshopId());
        log.setMsgReceiveTime(DateUtils.getDate());
        return log;
    }
    public static MqConsumeLog buildMqLog(TmcOrderMessage message, String messageId, TMCType tmcType){
        MqConsumeLog log = new MqConsumeLog();
        log.setId(UId.newId());
        log.setState(0);
        log.setConsumeTimes(1);
        log.setEtypeId(BigInteger.ZERO);
        log.setMsgType(tmcType);
        log.setProfileId(message.getProfileId());
        log.setMsgId(messageId);
        log.setBody(JsonUtils.toJson(message));
        log.setEshopId(message.getEshopId());
        log.setMsgReceiveTime(DateUtils.getDate());
        return log;
    }

}
