package com.wsgjp.ct.sale.tool.tmc.mapper;

import com.wsgjp.ct.sale.common.entity.EshopInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

@Mapper
@Repository
@Component
public interface EshopInfoMapper {
     EshopInfo queryEshopInfo(@Param("profileId") BigInteger profileId,
                              @Param("eshopId") BigInteger eshopId);
     EshopInfo queryEshopInfoByShopaccount(@Param("profileId") BigInteger profileId,
                                           @Param("shopaccount") String shopaccount);
}
