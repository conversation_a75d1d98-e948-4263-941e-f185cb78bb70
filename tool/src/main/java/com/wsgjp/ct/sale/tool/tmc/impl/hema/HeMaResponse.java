package com.wsgjp.ct.sale.tool.tmc.impl.hema;

/**
 * <AUTHOR>
 */
public class HeMaResponse {
    protected String code;
    private String msg;
    private String requestId;
    private boolean success;
    private long timestamp;
    private Object bizData;

    public HeMaResponse() {
    }

    public HeMaResponse(String msg, String requestId, boolean success) {
        this.msg = msg;
        this.requestId = requestId;
        this.success = success;
        this.timestamp = System.currentTimeMillis();
    }

    public HeMaResponse(String msg,String code, String requestId, boolean success) {
        this.msg = msg;
        this.requestId = requestId;
        this.code = code;
        this.success = success;
        this.timestamp = System.currentTimeMillis();
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public Object getBizData() {
        return bizData;
    }

    public void setBizData(Object bizData) {
        this.bizData = bizData;
    }
}
