package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.BifrostEshopMapper;
import com.wsgjp.ct.sale.biz.bifrost.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyBase;
import com.wsgjp.ct.sale.biz.eshoporder.service.notify.TmcNotifyProxy;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeRequest;
import com.wsgjp.ct.sale.common.entity.tmc.TmcInvokeResponse;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.TmcNotifyMethodEnum;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.request.order.ModifyOrderAdressRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.ModifyOrderAdressResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.order.EshopOrderModifyAddressFeature;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.dto.DDAddressDto;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.sis.client.common.CurrentUser;
import ngp.idgenerator.UId;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Component
public class DDTmcOrderHandler extends DDNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DDTmcOrderHandler.class);
    private final TmcNotifyBase notifyBase;
    private final TmcNotifyProxy notifyProxy;
    public DDTmcOrderHandler(TmcNotifyBase notifyBase,TmcNotifyProxy notifyProxy) {
        this.notifyBase = notifyBase;
        this.notifyProxy = notifyProxy;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        LOGGER.info("======抖店进入DDTmcOrderHandler.invoker方法======");
        String message = invokeMessage.getMessage();
        if(StringUtils.isEmpty(message)){
            LOGGER.info("抖店消息为空");
            return JsonUtils.toJson(new GeneralResult(500L,"消息为空",null));
        }

        EshopNotifyChange change = buildEshopNotifyChange(message,invokeMessage);
        if (TMCType.SELFHELPMODIFLYADDRESS == change.getType()){
            Map<String, Object> ddMessage = JsonUtils.toHashMap(message);
            Map<String, Object> data = JsonUtils.toHashMap(JsonUtils.toJson(ddMessage.get("data")));
            EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(change.getOnlineShopId(), invokeMessage.getShopType().getCode());
            if ("1001".equals(data.get("task_type"))){
                TmcInvokeRequest request = new TmcInvokeRequest();
                request.setMethod(TmcNotifyMethodEnum.MODIFY_ADDRESS_UPDATE);
                request.setTradeId(change.getTradeOrderId());
                request.setEshopId(eshopRegister.getId());
                request.setProfileId(eshopRegister.getProfileId());
                buildRequest(request,data);
                TmcInvokeResponse response = notifyProxy.execute(request);
                if(response.getCode().equals("0") || response.getCode().equals("200")|| "203".equals(response.getCode()) || "202".equals(response.getCode())){
                    ModifyOrderAdressRequest req = new ModifyOrderAdressRequest();
                    req.setTradeId(change.getTradeOrderId());
                    req.setApproved(0L);
                    modifyAdress(eshopRegister, req);
                    return JsonUtils.toJson(new GeneralResult(0L,"success",null));
                }else{
                    long code = Long.parseLong(response.getCode());
                    ModifyOrderAdressRequest req = new ModifyOrderAdressRequest();
                    req.setTradeId(change.getTradeOrderId());
                    req.setApproved(1003L);
                    modifyAdress(eshopRegister, req);
                    return JsonUtils.toJson(new GeneralResult(code,response.getMessage(),null));
                }
            }else {
                ModifyOrderAdressRequest req = new ModifyOrderAdressRequest();
                req.setTradeId(change.getTradeOrderId());
                req.setApproved(0L);
                Map<String, Object> receiverMsg = JsonUtils.toHashMap(JsonUtils.toJson(data.get("receiver_msg")));
                Map<String, Object> postReceiverMsg = JsonUtils.toHashMap(JsonUtils.toJson(data.get("post_receiver_msg")));
                if (receiverMsg.get("name") == null || postReceiverMsg.get("name") == null || !receiverMsg.get("name").toString().equals(postReceiverMsg.get("name").toString())){
                    req.setApproved(300005L);
                    modifyAdress(eshopRegister, req);
                    return JsonUtils.toJson(new GeneralResult(0L,"success",null));
                }
                if (receiverMsg.get("tel") == null || postReceiverMsg.get("tel") == null || !receiverMsg.get("tel").toString().equals(postReceiverMsg.get("tel").toString())){
                    req.setApproved(300005L);
                    modifyAdress(eshopRegister, req);
                    return JsonUtils.toJson(new GeneralResult(0L,"success",null));
                }
                Map<String, Object> addr = JsonUtils.toHashMap(JsonUtils.toJson(receiverMsg.get("addr")));
                Map<String, Object> postAddr = JsonUtils.toHashMap(JsonUtils.toJson(postReceiverMsg.get("addr")));
                Map<String, Object> province = JsonUtils.toHashMap(JsonUtils.toJson(addr.get("province")));
                Map<String, Object> postProvince = JsonUtils.toHashMap(JsonUtils.toJson(postAddr.get("province")));
                if (province.get("name") == null || postProvince.get("name") == null || !province.get("name").toString().equals(postProvince.get("name").toString())){
                    req.setApproved(300001L);
                    modifyAdress(eshopRegister, req);
                    return JsonUtils.toJson(new GeneralResult(0L,"success",null));
                }
                Map<String, Object> city = JsonUtils.toHashMap(JsonUtils.toJson(addr.get("city")));
                Map<String, Object> postCity = JsonUtils.toHashMap(JsonUtils.toJson(postAddr.get("city")));
                if (city.get("name") == null || postCity.get("name") == null || !city.get("name").toString().equals(postCity.get("name").toString())){
                    req.setApproved(300003L);
                    modifyAdress(eshopRegister, req);
                    return JsonUtils.toJson(new GeneralResult(0L,"success",null));
                }
                Map<String, Object> town = JsonUtils.toHashMap(JsonUtils.toJson(addr.get("town")));
                Map<String, Object> postTown = JsonUtils.toHashMap(JsonUtils.toJson(postAddr.get("town")));
                if (town.get("name") == null || postTown.get("name") == null || !town.get("name").toString().equals(postTown.get("name").toString())){
                    req.setApproved(300004L);
                    modifyAdress(eshopRegister, req);
                    return JsonUtils.toJson(new GeneralResult(0L,"success",null));
                }
//                Map<String, Object> street = JsonUtils.toHashMap(JsonUtils.toJson(addr.get("street")));
//                if (street.get("name") != null){
//                    ddAddressDto.setStreet(street.get("name").toString());
//                }
//                if(addr.get("encrypt_detail") != null){
//                    ddAddressDto.setAddressDetail(addr.get("encrypt_detail").toString());
//                }
                modifyAdress(eshopRegister, req);
                return JsonUtils.toJson(new GeneralResult(0L,"success",null));
            }

        }
        if (Objects.isNull(change)){
            //当前未对接该类型的 tmc 消息
            LOGGER.info("账套ID{},转换成EshopNotifyChange为空",invokeMessage.getProfileId());
            return JsonUtils.toJson(new GeneralResult(0L,"success",null));
        }

        //这里发订单变更消息，系统会按单号下载
        SupportUtil.doOrderNotify(change.getOnlineShopId(), change,  invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.Doudian.getCode());
        return JsonUtils.toJson(new GeneralResult(0L,"success",null));
    }

    private void modifyAdress(EshopRegisterNotify eshopRegister, ModifyOrderAdressRequest req) {
        BifrostEshopMapper eshopMapper = GetBeanUtil.getBean(BifrostEshopMapper.class);
        EshopInfo eshopInfo = eshopMapper.getEshopInfoByOtypeId(CurrentUser.getProfileId(), eshopRegister.getId());
        EshopSystemParams systemParams = CommonUtil.toSystemParams(eshopInfo);
        ShopType shopType = systemParams.getShopType();
        EshopFactory factory = EshopFactoryManager.create(shopType, systemParams);
        EshopOrderModifyAddressFeature modifyAddressFeature = factory.getFeature(EshopOrderModifyAddressFeature.class);
        ModifyOrderAdressResponse response1 = modifyAddressFeature.modifyOrderAdress(req);
    }

    private void buildRequest(TmcInvokeRequest request, Map<String, Object> data) {
        Map<String, Object> receiverMsg = JsonUtils.toHashMap(JsonUtils.toJson(data.get("receiver_msg")));
        DDAddressDto ddAddressDto = new DDAddressDto();
        if (receiverMsg.get("name") != null){
            ddAddressDto.setPostReceiver(receiverMsg.get("name").toString());
        }
        if (receiverMsg.get("tel") != null){
            ddAddressDto.setPostTel(receiverMsg.get("tel").toString());
        }
        Map<String, Object> addr = JsonUtils.toHashMap(JsonUtils.toJson(receiverMsg.get("addr")));
        Map<String, Object> province = JsonUtils.toHashMap(JsonUtils.toJson(addr.get("province")));
        if (province.get("name") != null){
            ddAddressDto.setProvince(province.get("name").toString());
        }
        Map<String, Object> city = JsonUtils.toHashMap(JsonUtils.toJson(addr.get("city")));
        if (city.get("name") != null){
            ddAddressDto.setCity(city.get("name").toString());
        }
        Map<String, Object> town = JsonUtils.toHashMap(JsonUtils.toJson(addr.get("town")));
        if (town.get("name") != null){
            ddAddressDto.setTown(town.get("name").toString());
        }
        Map<String, Object> street = JsonUtils.toHashMap(JsonUtils.toJson(addr.get("street")));
        if (street.get("name") != null){
            ddAddressDto.setStreet(street.get("name").toString());
        }
        if(addr.get("encrypt_detail") != null){
            ddAddressDto.setAddressDetail(addr.get("encrypt_detail").toString());
        }
        String json = JsonUtils.toJson(ddAddressDto);
        request.setMessage(json);
    }

    @Override
    public String serviceName() {
        return "doudian.order";
    }


    /**
     * 获取EshopNotifyChange
     * @param message
     * @return
     */
    public EshopNotifyChange buildEshopNotifyChange(String message,InvokeMessageEntity invokeMessage){
        Map<String, Object> ddMessage = JsonUtils.toHashMap(message);
        Map<String, Object> data = JsonUtils.toHashMap(ddMessage.get("data").toString());
        String tag = (String) ddMessage.get("tag");
        EshopNotifyChange changeInfo;
        switch (Integer.valueOf(tag)){
            //订单创建消息
            case 100:
                //订单支付/确认消息
            case 101:
                //发货物流信息变更消息
            case 104:
                //买家收货信息变更消息
            case 105:
                //订单取消消息
            case 106:
                //订单金额修改消息
            case 109:
                //订单已支付待处理
            case 110:
                changeInfo = handlerMessageByType(data,TMCType.Order);
                break;
                //买家收货信息变更申请消息
            case 111:
                changeInfo = handlerMessageByType(data,TMCType.SELFHELPMODIFLYADDRESS);
                break;
            case 112:
            case 148:
                changeInfo = handlerMessageByType(data,TMCType.Order);
                break;
            //卖家发货消息
            case 102:
                //卖家部分发货消息
            case 108:
                changeInfo = handlerMessageByType(data,TMCType.CHANGE_ADDRESS);
                break;
            //交易完成消息
            case 103:
                changeInfo = handlerMessageByType(data,TMCType.TradeSuccess);
                break;
            //卖家添加备注消息
            case 113:
                changeInfo = handlerMessageByType(data,TMCType.TradeMemoModified);
                break;
            case 10003:
                changeInfo = handlerMessageFirstSend(data,invokeMessage,TMCType.Order);
                break;
            default:
                changeInfo = null;
        }
        return changeInfo;
    }

    private EshopNotifyChange handlerMessageFirstSend(Map<String, Object> data,InvokeMessageEntity invokeMessage, TMCType tmcType) {
        Object pId = data.get("shop_order_id");
        Object shopId = data.get("shop_id");
        //先将数据插入change表
        EshopNotifyChange eshopSaleOrderChangeInfo = new EshopNotifyChange();
        eshopSaleOrderChangeInfo.setId(UId.newId());
        eshopSaleOrderChangeInfo.setProfileId(invokeMessage.getProfileId());
        eshopSaleOrderChangeInfo.setTradeOrderId(pId.toString());
        if (null ==invokeMessage.getEshopId() || BigInteger.ZERO.equals(invokeMessage.getEshopId())){
            // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
            EshopRegisterNotify notify = SupportUtil.buildNotify(shopId.toString(),  invokeMessage.getShopType() != null ? invokeMessage.getShopType().getCode() : ShopType.Doudian.getCode());
            eshopSaleOrderChangeInfo.setEshopId(notify.getId());
        }else {
            eshopSaleOrderChangeInfo.setEshopId(invokeMessage.getEshopId());
        }
        eshopSaleOrderChangeInfo.setContent(invokeMessage.getMessage());
        eshopSaleOrderChangeInfo.setType(TMCType.YOUXIAN_SEND);
        eshopSaleOrderChangeInfo.setCreateTime(new Date());
        notifyBase.insertMessageChange(Collections.singletonList(eshopSaleOrderChangeInfo));
        //在构建消息
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent(invokeMessage.getMessage());
        change.setTradeOrderId(pId.toString());
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(shopId.toString());
        return change;
    }

    /**
     * 根据类别创建EshopNotifyChange
     * @param data
     * @param tmcType
     * @return
     */
    public EshopNotifyChange handlerMessageByType(Map<String,Object> data, TMCType tmcType){
        EshopNotifyChange change = new EshopNotifyChange();
        change.setContent("");
        Object pId = data.get("p_id");
        Object shopId = data.get("shop_id");
        change.setTradeOrderId(pId.toString());
        change.setId(UId.newId());
        change.setType(tmcType);
        change.setOnlineShopId(shopId.toString());
        return change;
    }
}
