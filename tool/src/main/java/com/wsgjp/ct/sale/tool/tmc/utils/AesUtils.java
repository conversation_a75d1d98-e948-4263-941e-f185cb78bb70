package com.wsgjp.ct.sale.tool.tmc.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.util.Base64;

/**
 * AES加解密工具
 * <p>
 * 密钥(密码)长度只能为16, 24, 32 位长度
 * <p>
 * 加密过程: 加密-Base64编码
 * 解密过程: Base64解码-解密
 */

public class AesUtils {

    // 加密模式 AES/ECB/PKCS5Padding
    private static final String TRANS_FORMATION = "AES/ECB/PKCS5Padding";

    // 加密算法
    private static final String ALGORITHM = "AES";

    // 字符编解码
    private static final String CHARSET_NAME = "UTF-8";


    /**
     * AES 解密
     *
     * @param value base64编码后的加密字符串
     * @return base64解码后的原始字符串
     * @throws Exception 解码异常
     */
    public static String decrypt(String password, String value) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANS_FORMATION);

        SecretKey secretKey = new SecretKeySpec(password.getBytes(), ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);

        byte[] content = Base64.getDecoder().decode(value.getBytes(Charset.forName(CHARSET_NAME)));
        byte[] encrypted = cipher.doFinal(content);
        // 如果出现中文乱码，尝试指定utf8编码
        // return new String(encrypted, "utf8");
        return new String(encrypted);
    }
}
