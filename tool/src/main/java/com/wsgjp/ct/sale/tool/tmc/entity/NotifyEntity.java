package com.wsgjp.ct.sale.tool.tmc.entity;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.common.enums.TMCType;

import java.math.BigInteger;

public class NotifyEntity {
    public BigInteger profileId;
    public BigInteger eshopId;
    public String shopAccount;
    public String content;
    public ShopType shopType;
    public TMCType messageType;
    public BigInteger notifyId;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public String getShopAccount() {
        return shopAccount;
    }

    public void setShopAccount(String shopAccount) {
        this.shopAccount = shopAccount;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public TMCType getMessageType() {
        return messageType;
    }

    public void setMessageType(TMCType messageType) {
        this.messageType = messageType;
    }

    public BigInteger getNotifyId() {
        return notifyId;
    }

    public void setNotifyId(BigInteger notifyId) {
        this.notifyId = notifyId;
    }
}
