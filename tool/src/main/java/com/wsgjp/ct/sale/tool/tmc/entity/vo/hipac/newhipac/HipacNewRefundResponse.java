package com.wsgjp.ct.sale.tool.tmc.entity.vo.hipac.newhipac;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class HipacNewRefundResponse {
    private Supplier supplier;
    private Order order;

    @JsonProperty("Supplier")
    public Supplier getSupplier() { return supplier; }
    @JsonProperty("Supplier")
    public void setSupplier(Supplier value) { this.supplier = value; }

    @JsonProperty("Order")
    public Order getOrder() { return order; }
    @JsonProperty("Order")
    public void setOrder(Order value) { this.order = value; }

    public static class Order {
        private String orderNum;
        private String refundNo;
        private String refundStatus;
        private String itemName;
        private String itemSupplyNo;
        private String features;
        private String refundAmount;
        private String logisticsAmount;
        private String logisticsName;
        private String logisticsNo;
        private String shopName;
        private String refundReason;
        private String itemQuantity;
        private String totalAmount;

        @JsonProperty("orderNum")
        public String getOrderNum() { return orderNum; }
        @JsonProperty("orderNum")
        public void setOrderNum(String value) { this.orderNum = value; }

        @JsonProperty("refundNo")
        public String getRefundNo() { return refundNo; }
        @JsonProperty("refundNo")
        public void setRefundNo(String value) { this.refundNo = value; }

        @JsonProperty("refundStatus")
        public String getRefundStatus() { return refundStatus; }
        @JsonProperty("refundStatus")
        public void setRefundStatus(String value) { this.refundStatus = value; }

        @JsonProperty("itemName")
        public String getItemName() { return itemName; }
        @JsonProperty("itemName")
        public void setItemName(String value) { this.itemName = value; }

        @JsonProperty("itemSupplyNo")
        public String getItemSupplyNo() { return itemSupplyNo; }
        @JsonProperty("itemSupplyNo")
        public void setItemSupplyNo(String value) { this.itemSupplyNo = value; }

        @JsonProperty("features")
        public String getFeatures() { return features; }
        @JsonProperty("features")
        public void setFeatures(String value) { this.features = value; }

        @JsonProperty("refundAmount")
        public String getRefundAmount() { return refundAmount; }
        @JsonProperty("refundAmount")
        public void setRefundAmount(String value) { this.refundAmount = value; }

        @JsonProperty("logisticsAmount")
        public String getLogisticsAmount() { return logisticsAmount; }
        @JsonProperty("logisticsAmount")
        public void setLogisticsAmount(String value) { this.logisticsAmount = value; }

        @JsonProperty("logisticsName")
        public String getLogisticsName() { return logisticsName; }
        @JsonProperty("logisticsName")
        public void setLogisticsName(String value) { this.logisticsName = value; }

        @JsonProperty("logisticsNo")
        public String getLogisticsNo() { return logisticsNo; }
        @JsonProperty("logisticsNo")
        public void setLogisticsNo(String value) { this.logisticsNo = value; }

        @JsonProperty("shopName")
        public String getShopName() { return shopName; }
        @JsonProperty("shopName")
        public void setShopName(String value) { this.shopName = value; }

        @JsonProperty("refundReason")
        public String getRefundReason() { return refundReason; }
        @JsonProperty("refundReason")
        public void setRefundReason(String value) { this.refundReason = value; }

        @JsonProperty("itemQuantity")
        public String getItemQuantity() { return itemQuantity; }
        @JsonProperty("itemQuantity")
        public void setItemQuantity(String value) { this.itemQuantity = value; }

        @JsonProperty("totalAmount")
        public String getTotalAmount() { return totalAmount; }
        @JsonProperty("totalAmount")
        public void setTotalAmount(String value) { this.totalAmount = value; }
    }


    public static class Supplier {
        private String supplierSenderID;

        @JsonProperty("supplierSenderID")
        public String getSupplierSenderID() { return supplierSenderID; }
        @JsonProperty("supplierSenderID")
        public void setSupplierSenderID(String value) { this.supplierSenderID = value; }
    }

}
