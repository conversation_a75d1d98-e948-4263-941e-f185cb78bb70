package com.wsgjp.ct.sale.tool.logo.util;

import com.wsgjp.ct.sale.tool.logo.entity.ToolLockEntity;
import com.wsgjp.ct.support.redis.factory.CacheType;
import ngp.redis.RedisPoolFactory;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> csh
 * @create 2023-06-16 10:51
 */
@Service
public class ToolLockUtil {

    private final static Logger logger = LoggerFactory.getLogger(ToolLockUtil.class);
    private final RedisPoolFactory redisPoolFactory;

    public ToolLockUtil(RedisPoolFactory redisPoolFactory) {
        this.redisPoolFactory = redisPoolFactory;
    }

    public boolean getLock(ToolLockEntity entity) {
        String key = String.format("%s_%s", entity.getKey(), entity.getProfileId());
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String value = template.opsForValue().get(key);
        if (StringUtils.isNotEmpty(value)) {
            return false;
        }
        try {
            //默认30分钟，线程结束的时候自动释放
            int lockSeconds = entity.getLockSeconds();
            Boolean ifAbsent = template.opsForValue().setIfAbsent(key, String.valueOf(entity.getProfileId()), lockSeconds, TimeUnit.SECONDS);
            return ifAbsent != null && ifAbsent;
        } catch (Exception ex) {
            String errMsg = String.format("[%s-锁]profileId【%s】Thread【%s】,报错：%s | %s",
                    entity.getErrMsg(), entity.getProfileId(), entity.getThreadId(), ex.getMessage(), ex.getCause().getMessage());
            logger.error(errMsg, ex);
            return false;
        }
    }

    public void disposeLock(ToolLockEntity entity) {
        String key = String.format("%s_%s", entity.getKey(), entity.getProfileId());
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        Boolean delete = template.delete(key);
        if (null==delete || !delete) {
            logger.error("[{}-锁]profileId【{}】Thread【{}】,释放Redis锁【{}】失败",
                    entity.getErrMsg(), entity.getProfileId(), entity.getThreadId(), key);
        }
    }

}
