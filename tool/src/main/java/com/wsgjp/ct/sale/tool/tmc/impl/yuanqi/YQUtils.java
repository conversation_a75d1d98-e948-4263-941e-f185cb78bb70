package com.wsgjp.ct.sale.tool.tmc.impl.yuanqi;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.purchase.PurchaseOrderDownloadResult;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.utils.JsonUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import yaunqisdk.utils.YuanQiUtil;

import java.math.BigInteger;
import java.util.*;

public class YQUtils {

    private static String BodyKey = "body";
    private static String SignKey = "sign";
    private static String MethodKey = "type";
    private static String Method = "method";
    private static String Token = "token";
    private static String ShopAccount = "merchantNumber";
    private static final Logger logger = LoggerFactory.getLogger(YQUtils.class);

    /**
     * 检查签名
     *
     * @param params
     * @param config
     * @return
     */
    public static boolean checkSign(Map<String, String> params, EshopTmcConfig config) {

        String signStr = getSignString(params);
        String md5Sign = DigestUtils.md5Hex(signStr);
        String shaSign = YuanQiUtil.SHA256(config.getYuanqiAppSecret(), md5Sign);
        String signParam = params.get(SignKey);
        if (!StringUtils.isEmpty(signParam) && shaSign.equals(signParam)) {
            return true;
        }
        return false;
    }

    public static BigInteger getProfileId(Map<String, String> params) {
        try {
            String token = params.get(Token);
            if (StringUtils.isEmpty(token)) {
                return null;
            }
            String shopAccount = params.get(ShopAccount);
            EshopRegisterNotify notify = SupportUtil.buildNotify(shopAccount, ShopType.YuanQiSenLin.getCode());
            if (notify == null) {
                return null;
            }
            return notify.getProfileId();
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * 获取body消息体的map对象
     *
     * @param bodyMsg
     * @return
     */
    public static Map<String, String> getBodyMsgMap(String bodyMsg) {
        Map<String, Object> map = JsonUtils.toHashMap(bodyMsg);
        Map<String, String> msgMap = new HashMap<>();
        for (String key : map.keySet()) {
            Object value = map.get(key);
            if (BodyKey.equals(key)) {
                if (value == null || StringUtils.isEmpty(value.toString())) {
                    msgMap.put(key, "");
                    continue;
                }
                msgMap.put(key, JsonUtils.toJson(value));
                continue;
            }
            if (null == value) {
                msgMap.put(key, null);
                continue;
            }
            msgMap.put(key, value.toString());
        }
        return msgMap;
    }

    /**
     * 通过事件类型匹配handler的servicename
     *
     * @param map
     * @return
     */
    public static String getServiceName(Map<String, String> map) {
        try {
            boolean hasMethodKey = map.containsKey(MethodKey) || map.containsKey(Method);
            logger.info("元气消息getServiceName-hasMethodKey:{}", hasMethodKey);
            if (null == map || map.size() == 0 || !hasMethodKey) {
                return null;
            }
            String method = map.get(Method) != null && "notify.orderinfochange".equals(map.get(Method)) ? map.get(Method) : map.get(MethodKey);
            logger.info("元气消息getServiceName-method:{}", method);
            if (StringUtils.isEmpty(method)) {
                return null;
            }
            YuanQiTMCType type = YuanQiTMCType.getEnumTypeByName(method);
            logger.info("元气消息getServiceName-type:{}", type);
            if (type == null) {
                return null;
            }
            return type.getServiceName();
        }
        catch (Exception ex){
            logger.error("getServiceName-exception:"+ex.getMessage(),ex);
            throw ex;
        }

    }

    /**
     * 获取签名串
     *
     * @param params
     * @return
     */
    private static String getSignString(Map<String, String> params) {
        StringBuilder stringBuilder = new StringBuilder();
        List<Map.Entry<String, String>> list = new ArrayList<Map.Entry<String, String>>(params.entrySet());
        //对map进行排序
        Collections.sort(list, new Comparator<Map.Entry<String, String>>() {
            @Override
            public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                return o1.getKey().compareTo(o2.getKey());
            }
        });
        //拼接字符串
        for (Map.Entry<String, String> entry : list) {
            if (SignKey.equals(entry.getKey())) {
                continue;
            }
            //过滤空值
            if (!BodyKey.equals(entry.getKey()) && (entry.getValue() == null || StringUtils.isEmpty(entry.getValue().toString()))) {
                continue;
            } else if (BodyKey.equals(entry.getKey()) && (entry.getValue() == null || StringUtils.isEmpty(entry.getValue().toString()))) {
                continue;
            }
            String value;
            if (BodyKey.equals(entry.getKey())) {
                value = JsonUtils.toJson(entry.getValue());
            } else {
                value = entry.getValue().toString();
            }
            stringBuilder.append(entry.getKey())
                    .append("=")
                    .append(value)
                    .append("&");

        }
        return stringBuilder.substring(0, stringBuilder.length() - 1);
    }


    public static void buildResponse(String serviceName, YuanQiTMCResponse response) {
        if (!YuanQiTMCType.PurchaseOrderPaySuccess.getServiceName().equals(serviceName)) {
            return;
        }
        if (StringUtils.isEmpty(response.getMessage())) {
            return;
        }
        PurchaseOrderDownloadResult result = JsonUtils.toObject(response.getMessage(), PurchaseOrderDownloadResult.class);
        int errorCode = result.isSuccess() ? 0 : 500;
        if (null != result.getUnRelationItems() && result.getUnRelationItems().size() > 0) {
            errorCode = 100001;
        }
        response.setError(errorCode);
    }

}
