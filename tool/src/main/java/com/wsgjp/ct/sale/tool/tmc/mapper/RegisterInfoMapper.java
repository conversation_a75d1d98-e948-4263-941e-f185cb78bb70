package com.wsgjp.ct.sale.tool.tmc.mapper;

import com.wsgjp.ct.sale.tool.tmc.entity.RegisterInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
@Component
public interface RegisterInfoMapper {
    List<RegisterInfoEntity> getRegisterInfoByMallId(@Param("shopaccount") String shopaccount,
                                                     @Param("shoptypes") List<String> shoptypes);
}
