package com.wsgjp.ct.sale.tool.tmc.impl.doudian;

import com.wsgjp.ct.sale.common.enums.TMCType;
import ngp.utils.ArrayUtils;
import ngp.utils.StringUtils;

public enum DouDianTmcType {
    doudian_trade_TradeCreate( 100, TMCType.Order,"订单创建消息"),
    doudian_trade_TradePaid( 101,TMCType.Order,"订单支付/确认消息"),
    doudian_trade_TradePending( 110,TMCType.Order,"订单已支付待处理"),
    doudian_trade_TradePartlySellerShip(108 ,TMCType.Order,"卖家部分发货消息"),
    doudian_trade_TradeSellerShip( 102,TMCType.Order,"卖家发货消息"),
    doudian_trade_TradeCanceled(106 ,TMCType.Order,"订单取消消息"),
    doudian_trade_TradeSuccess( 103,TMCType.Order,"交易完成消息"),
    doudian_trade_TradeLogisticsChanged( 104,TMCType.Order,"发货物流信息变更消息"),
    doudian_trade_TradeAmountChanged( 109,TMCType.Order,"订单金额修改消息"),
    doudian_trade_TradeMemoModify(113 ,TMCType.Order,"卖家添加备注消息"),
    doudian_trade_TradeAppointmentV2( 126,TMCType.Order,"订单发货时效变更"),
    doudian_order_orderTagChange( 142,TMCType.Order,"订单打标变更消息"),
    doudian_trade_TradeResourcechange( 148,TMCType.Order,"订单时效变更消息"),
    doudian_order_JadeQualityChangeType( 141,TMCType.Order,"订单玉石质检状态变更消息"),
    doudian_logistics_orderTagPush( 10003,TMCType.Order,"订单标记推送"),
    doudian_order_orderSourceTag( 140,TMCType.Order,"订单来源打标消息"),

    doudian_trade_TradeAddressChangeApplied(111 ,TMCType.Order,"买家收货信息变更申请消息"),
    doudian_trade_TradeAddressChanged(105 ,TMCType.Order,"买家收货信息变更消息"),

    doudian_refund_ExchangeConfirmed(211,TMCType.RefundOrder,"卖家收到买家换货包裹，确认换货并二次发货消息"),
//    doudian_refund_ArbitrateDiscussUpload(218,TMCType.RefundOrder,"协商期上传凭证消息"),
//    doudian_refund_ArbitrateServiceIntervene(217,TMCType.RefundOrder,"仲裁客服介入消息"),
//    doudian_refund_ArbitrateCancelled(215,TMCType.RefundOrder,"买家取消仲裁消息"),
//    doudian_refund_ArbitrateAudited(216,TMCType.RefundOrder,"客服仲裁结果消息"),
//    doudian_refund_ArbitrateSubmitted(214,TMCType.RefundOrder,"商家上传仲裁凭证消息"),
//    doudian_refund_ArbitrateSubmitting(213,TMCType.RefundOrder,"客服要求商家上传凭证消息"),
//    doudian_refund_ArbitrateApplied(212,TMCType.RefundOrder,"买家发起客服仲裁消息"),
    doudian_refund_RefundCreated(200,TMCType.RefundOrder,"买家发起售后申请消息"),
    doudian_refund_RefundModified(208,TMCType.RefundOrder,"买家修改售后申请消息"),
    doudian_refund_ExpirationChange(209,TMCType.RefundOrder,"售后超时时长变更消息"),
    doudian_refund_RefundClosed(207,TMCType.RefundOrder,"售后关闭消息"),
    doudian_refund_RefundSuccess(206,TMCType.RefundOrder,"退款成功消息"),
    doudian_refund_ReturnApplyRefused(205,TMCType.RefundOrder,"拒绝退货申请消息"),
    doudian_refund_RefundRefused(204,TMCType.RefundOrder,"拒绝退款消息"),
    doudian_refund_BuyerReturnGoods(203,TMCType.RefundOrder,"买家退货给卖家消息"),
    doudian_refund_ReturnApplyAgreed(202,TMCType.RefundOrder,"同意退货申请消息"),
    doudian_Refund_SpecialRefund(224,TMCType.RefundOrder,"特殊权益售后消息"),
    doudian_Refund_SpecialRefundSuccess(225,TMCType.RefundOrder,"特殊权益售后退款成功消息"),
    doudian_refund_AuditAgreeResend(227,TMCType.RefundOrder,"同意补寄申请"),
    doudian_refund_ResendFillLogistics(226,TMCType.RefundOrder,"补寄商家发货"),
    doudian_invoice_InvoiceApply(139,TMCType.Invoice_Apply,"用户申请开票消息"),
    doudian_iopTrade_Distribution(501,TMCType.Order,"商家订单分配消息"),
    doudian_iopTrade_StatusReturn(505,TMCType.Order,"代打运单回传状态变更消息"),
    doudian_iopTrade_UpdateRemark(500,TMCType.Order,"商家修改备注消息"),
    doudian_iopTrade_UpdateReceiver(504,TMCType.Order,"修改收件人信息消息"),
    doudian_iopTrade_DistributionCancel(503,TMCType.Order,"商家取消分配订单消息")
    ;

    private final Integer tag;
    private final TMCType tmcType;
    private final String desc;

    public Integer getTag() {
        return tag;
    }

    public TMCType getTmcType() {
        return tmcType;
    }

    public String getDesc() {
        return desc;
    }

    DouDianTmcType(int tag, TMCType tmcType, String desc){
        this.tag=tag;
        this.tmcType = tmcType;
        this.desc = desc;
    }

    public static DouDianTmcType getDouDianTmcTypeByTag(String flag) {
        DouDianTmcType[] msgTypes = DouDianTmcType.values();
        if (ArrayUtils.isEmpty(msgTypes) || StringUtils.isEmpty(flag)) {
            return null;
        }
        for (DouDianTmcType msgType : msgTypes) {
            if(msgType.getTag().toString().equals(flag)){
                return  msgType;
            }
        }
        return null;
    }

}
