package com.wsgjp.ct.sale.tool.tmc.impl.youzan.entity;

import com.wsgjp.ct.sale.platform.factory.youzanretail.entity.DeliveryOrder;
import com.wsgjp.ct.sale.platform.utils.StringUtils;

import java.util.List;

public class YouZanTmcMessage {
    /**
     * 订单所属店铺的店铺id
     */
    private String kdt_id;
    /**
     *有赞连锁总店店铺ID
     */
    private String root_kdt_id;

    /**
     * 店铺名称
     */
    private String kdt_name;
    /**
     * 消息
     */
    private String msg;
    /**
     * 消息实体
     */
    private MessageBody msgBody;
    /**
     * 消息ID
     */
    private String msg_id;
    private String sign;
    private Long version;
    /**
     * 消息业务类型（topic）
     */
    private String type;
    /**
     * 业务消息的标识
     * 对于订单售后消息就是tid(订单号)
     */
    private String id;

    /**
     * 改派消息使用
     */
    private List<DeliveryOrder> source_delivery_orders;
    private DeliveryOrder target_delivery_order;

    public String getKdt_id() {
        return kdt_id;
    }

    public void setKdt_id(String kdt_id) {
        this.kdt_id = kdt_id;
    }

    public String getKdt_name() {
        return kdt_name;
    }

    public void setKdt_name(String kdt_name) {
        this.kdt_name = kdt_name;
    }

    public String getMsg_id() {
        return msg_id;
    }

    public void setMsg_id(String msg_id) {
        this.msg_id = msg_id;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public MessageBody getMsgBody() {
        return msgBody;
    }

    public void setMsgBody(MessageBody msgBody) {
        this.msgBody = msgBody;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<DeliveryOrder> getSource_delivery_orders() {
        return source_delivery_orders;
    }

    public void setSource_delivery_orders(List<DeliveryOrder> source_delivery_orders) {
        this.source_delivery_orders = source_delivery_orders;
    }

    public DeliveryOrder getTarget_delivery_order() {
        return target_delivery_order;
    }

    public void setTarget_delivery_order(DeliveryOrder target_delivery_order) {
        this.target_delivery_order = target_delivery_order;
    }

    public String getRoot_kdt_id() {
        return root_kdt_id;
    }

    public void setRoot_kdt_id(String root_kdt_id) {
        this.root_kdt_id = root_kdt_id;
    }

    public String getOnlineShopId(){
        if (StringUtils.isNotBlank(this.root_kdt_id)){
            return this.root_kdt_id;
        }
        return this.kdt_id;
    }
}


