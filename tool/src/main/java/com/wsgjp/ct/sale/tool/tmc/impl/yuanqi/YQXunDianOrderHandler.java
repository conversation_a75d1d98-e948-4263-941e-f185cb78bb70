package com.wsgjp.ct.sale.tool.tmc.impl.yuanqi;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.entity.purchase.PurchaseOrderDownloadResult;
import com.wsgjp.ct.sale.common.constant.SyncOrderConst;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.YQXunDianMessage;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 元气巡店
 * 所有的巡店订单事件处理  记录订单id 然后调用订单详情接口
 */
@Component
public class YQXunDianOrderHandler extends YuanQiNotifyBase implements MessageHandler {

    private static final Logger sysLogger = LoggerFactory.getLogger(YQXunDianOrderHandler.class);
    private static final String TRADEID = "orderId";

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        try {
            String message = invokeMessage.getMessage();
            YQXunDianMessage yqOrder = getYQMsgs(message);
            checkMsg(yqOrder.getOrderId() == null,"元气订单消息没有订单号");
            //经销商编码
            String shopAccount = yqOrder.getMerchantNumber();
            EshopNotifyChange tmcMsg = new EshopNotifyChange();
            tmcMsg.setType(TMCType.Order);
            tmcMsg.setTradeOrderId(yqOrder.getOrderId());
            tmcMsg.setContent(message);
            tmcMsg.setOnlineShopId(shopAccount);
            tmcMsg.setEshopId(invokeMessage.getEshopId());
            tmcMsg.setProfileId(CurrentUser.getProfileId());
            tmcMsg.setSubType(1);
            tmcMsg.setShopType(ShopType.YuanQiSenLin.getCode());
            TmcOrderMessage tmcMessage = SupportUtil.buildTmcOrderMessage(tmcMsg);
            tmcMessage.setTag(SyncOrderConst.TmcSalesOrderTag);
            ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.API_ORDER_DOWNLOAD_THREAD_NAME);
            threadPool.executeAsync(invoker -> {
                YQOrderDownloadManager download = new YQOrderDownloadManager();
                PurchaseOrderDownloadResult result = download.doYuanQiOrderSync(tmcMessage);
            }, null);
             return "success";
            //return result.isSuccess() ? "success" : JsonUtils.toJson(result);
        }catch (Exception ex){
            sysLogger.info(ex.getMessage());
            return ex.getMessage();
        }
    }

    public YQXunDianMessage getYQMsgs(String message) {
        try {
            checkMsg(StringUtils.isEmpty(message), "元气消息为空");
            YQXunDianMessage yqOrder = JsonUtils.toObject(message, YQXunDianMessage.class);
            checkMsg(yqOrder == null, "元气消息为空");
            return yqOrder;
        }
        catch (Exception ex){
            sysLogger.error("反序列化消息为实体报错：{}，消息为：{}",ex.getMessage(),message);
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public String serviceName() {
        return "yuanqisenlin.xundian.order";
    }

}
