package com.wsgjp.ct.sale.tool.tmc.impl.tmall;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcRefundMsgMapper;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.UpdateOrderRefundStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.constant.PlatformTmcType;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.eshoporder.UpdateOrderRefundType;
import com.wsgjp.ct.sale.platform.dto.tmc.EshopTmcRefundMsgEntity;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.entities.NotifyRefundItem;
import com.wsgjp.ct.sale.platform.entity.entities.TmallSuperMarketRefundEntity;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.taobao.TaobaoNotifyBase;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.StringUtils;
import ngp.utils.XmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class TmSupperMarketRefundHandler extends TaobaoNotifyBase implements MessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(TmSupperMarketRefundHandler.class);
    private final EshopTmcRefundMsgMapper tmcRefundMsgMapper;

    public TmSupperMarketRefundHandler(EshopTmcRefundMsgMapper tmcRefundMsgMapper) {
        this.tmcRefundMsgMapper = tmcRefundMsgMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======", shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        TmallSuperMarketRefundEntity apiRefund;
        try {
            apiRefund = XmlUtils.toObject(tmMessage, TmallSuperMarketRefundEntity.class);
        } catch (Exception e) {
            LOGGER.error("{}tmMessage数据转换成TmallSuperMarketRefundEntity实体出错，错误信息：{},tmMessage:{}", shopTypeName, e.getMessage(), tmMessage);
            return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<response>\n" +
                    "  <success>false</success>\n" +
                    "  <errorCode>201</errorCode>\n" +
                    "  <errorMsg>系统异常</errorMsg>\n" +
                    "  <retry>false</retry>\n" +
                    "</response>";
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiRefund.getSupplierId(), invokeMessage.getShopType().getCode());
        if (Objects.isNull(eshopRegister)) {
            LOGGER.info("profileId:{},店铺类型:{},supplierId:{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), shopTypeName, apiRefund.getSupplierId(), tmMessage);
            return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<response>\n" +
                    "  <success>false</success>\n" +
                    "  <errorCode>201</errorCode>\n" +
                    "  <errorMsg>系统异常</errorMsg>\n" +
                    "  <retry>false</retry>\n" +
                    "</response>";
        }
        LOGGER.info("profileId:{},eshopId:{},店铺类型：{}", eshopRegister.getProfileId(), eshopRegister.getId(), eshopRegister.getType());
        try {
            saveTmcRefundMsg(eshopRegister, tmMessage, apiRefund);
            saveRefundToPlEshopNotifyChange(eshopRegister, tmMessage, apiRefund);
        } catch (Exception ex) {
            LOGGER.error("{}保存TMC订单消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}", shopTypeName, eshopRegister.getProfileId(), eshopRegister.getId(), tmMessage, ex.getMessage(), ex);
            return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<response>\n" +
                    "  <success>false</success>\n" +
                    "  <errorCode>201</errorCode>\n" +
                    "  <errorMsg>系统异常</errorMsg>\n" +
                    "  <retry>false</retry>\n" +
                    "</response>";
        }
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setProfileId(eshopRegister.getProfileId());
        eshopNotifyChange.setEshopId(eshopRegister.getId());
        eshopNotifyChange.setTradeOrderId(apiRefund.getForwardOrderCode());
        eshopNotifyChange.setRefundOrderId(apiRefund.getBizOrderCode());
        eshopNotifyChange.setContent("");
        eshopNotifyChange.setId(UId.newId());
        eshopNotifyChange.setOnlineShopId(apiRefund.getSupplierId());
        eshopNotifyChange.setType(TMCType.Order);
        eshopNotifyChange.setPlatformMsgType(PlatformTmcType.REFUND_MSG);
        SupportUtil.doOrderNotify(apiRefund.getSupplierId(), eshopNotifyChange, invokeMessage.getShopType().getCode());
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<response>\n" +
                "  <success>true</success>\n" +
                "  <errorCode>0</errorCode>\n" +
                "  <errorMsg>成功</errorMsg>\n" +
                "  <retry>false</retry>\n" +
                "</response>";
    }

    private void saveRefundToPlEshopNotifyChange(EshopRegisterNotify eshopRegister, String tmMessage, TmallSuperMarketRefundEntity curRefundMessage) {
        try {
            EshopNotifyChange curNotifyChange = new EshopNotifyChange();
            curNotifyChange.setProfileId(eshopRegister.getProfileId());
            curNotifyChange.setEshopId(eshopRegister.getId());
            curNotifyChange.setTradeOrderId(curRefundMessage.getForwardOrderCode());
            curNotifyChange.setId(UId.newId());
            curNotifyChange.setContent(tmMessage);
            curNotifyChange.setType(TMCType.REFUND_STOP);
            curNotifyChange.setUpdateTime(DateUtils.getDate());
            UpdateOrderRefundStateRequest request = new UpdateOrderRefundStateRequest();
            request.setTradeOrderId(curNotifyChange.getTradeOrderId());
            request.setShopId(curNotifyChange.getEshopId());
            request.setRefundState(ReturnState.REFUNDING);
            request.setUpdateOrderRefundType(UpdateOrderRefundType.REFUND_CREATE_BY_TMC);
            if (CollectionUtils.isNotEmpty(curRefundMessage.getOrderItems())) {
                List<String> oidList = curRefundMessage.getOrderItems().stream().map(NotifyRefundItem::getSubOrderCode).collect(Collectors.toList());
                request.setOidList(oidList);
            }
            EshopSaleOrderService eshopSaleOrderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
            TmcEshopNotifyChangeMapper tmcMapper = GetBeanUtil.getBean(TmcEshopNotifyChangeMapper.class);
            List<EshopNotifyChange> eshopNotifyChanges = tmcMapper.queryMessageChangeSorted(curNotifyChange.getProfileId(),
                    Collections.singletonList(curNotifyChange.getTradeOrderId()), curNotifyChange.getEshopId(), TMCType.REFUND_STOP.getCode());
            if (CollectionUtils.isNotEmpty(eshopNotifyChanges)) {
                for (EshopNotifyChange eshopNotifyChange : eshopNotifyChanges) {
                    String content = eshopNotifyChange.getContent();
                    if (StringUtils.isEmpty(content)) {
                        continue;
                    }
                    TmallSuperMarketRefundEntity oldRefundMessage = null;
                    try {
                        oldRefundMessage = XmlUtils.toObject(tmMessage, TmallSuperMarketRefundEntity.class);
                    } catch (Exception ex) {
                    }
                    if (oldRefundMessage == null) {
                        continue;
                    }
                    //说明退款入库单已经推送过了。重复过滤
                    if (StringUtils.equals(oldRefundMessage.getBizOrderCode(), curRefundMessage.getBizOrderCode())) {
                        return;
                    }
                }
            }
            tmcMapper.insertMessageChange(curNotifyChange);
            eshopSaleOrderService.updateOrderRefundState(request);
        } catch (Exception ex) {
            if (ex.getMessage() != null && ex.getMessage().contains("订单尚未流入系统")) {
                return;
            }
            LOGGER.error("保存退款消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",
                    eshopRegister.getProfileId(), eshopRegister.getId(), tmMessage, ex.getMessage(), ex);
        }
    }

    public int saveTmcRefundMsg(EshopRegisterNotify eshopRegister, String tmMessage, TmallSuperMarketRefundEntity apiRefund) {
        //bizOrderCode 售后单号
        EshopTmcRefundMsgEntity refundMsgEntity = tmcRefundMsgMapper.queryTmcRefundMsgByRefundId(eshopRegister.getProfileId(), eshopRegister.getId(), apiRefund.getBizOrderCode());
        if (Objects.isNull(refundMsgEntity)) {
            refundMsgEntity = buildEshopTmcRefundMsgEntity(eshopRegister, tmMessage, apiRefund);
            return tmcRefundMsgMapper.insertTmcRefundMsg(refundMsgEntity);
        } else {
            //todo  refundMsgEntity.setMsgStatus(0); 处理状态（0：未处理，1：处理成功，2：处理失败）
            refundMsgEntity.setMsgUpdateTime(new Date());
            //平台没有返回更新时间，平台推送一次就更新一次
            refundMsgEntity.setUpdateTime(new Date());
            refundMsgEntity.setMessage(tmMessage);
            return tmcRefundMsgMapper.updateTmcRefundMsg(refundMsgEntity);
        }
    }

    private EshopTmcRefundMsgEntity buildEshopTmcRefundMsgEntity(EshopRegisterNotify eshopRegister, String tmMessage, TmallSuperMarketRefundEntity apiRefund) {
        EshopTmcRefundMsgEntity tmcRefundMsgEntity = new EshopTmcRefundMsgEntity();
        tmcRefundMsgEntity.setId(UId.newId());
        tmcRefundMsgEntity.setProfileId(eshopRegister.getProfileId());
        tmcRefundMsgEntity.setEshopId(eshopRegister.getId());
        tmcRefundMsgEntity.setShopType(ShopType.valueOf(eshopRegister.getType()));
        tmcRefundMsgEntity.setTradeOrderId(apiRefund.getForwardOrderCode());
        tmcRefundMsgEntity.setRefundOrderId(apiRefund.getBizOrderCode());
        tmcRefundMsgEntity.setMessage(tmMessage);
        tmcRefundMsgEntity.setMsgStatus(0);
        tmcRefundMsgEntity.setMsgCreateTime(new Date());
        tmcRefundMsgEntity.setMsgUpdateTime(new Date());
        tmcRefundMsgEntity.setCreateTime(new Date());
        return tmcRefundMsgEntity;
    }

    @Override
    public String serviceName() {
        return "qimen.alibaba.ascp.uop.reverseorder.instorage.notify";
    }
}
