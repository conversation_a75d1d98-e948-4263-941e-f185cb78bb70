package com.wsgjp.ct.sale.tool.tmc.impl.kuaishou.entity;

import com.wsgjp.ct.sale.platform.factory.kuaishou.entity.Info;

/**
 * <AUTHOR>
 */
public class KuaiShouResponse {
    private String eventId;
    private Long bizId;
    private Long userId;
    private String openId;
    private String appKey;
    private String event;
    private String info;
    private Long createTime;
    private boolean test;

    private Info messageBody;


    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public boolean isTest() {
        return test;
    }

    public void setTest(boolean test) {
        this.test = test;
    }

    public Info getMessageBody() {
        return messageBody;
    }

    public void setMessageBody(Info messageBody) {
        this.messageBody = messageBody;
    }
}
