package com.wsgjp.ct.sale.tool.logo.config;

import com.wsgjp.ct.sale.sdk.logo.state.LogoSourceTypeEnum;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "sale-logo-calculate")
public class LogoCalculateConfig {
    /**
     * 账套ID缓存时间(分钟)
     */
    private int profileIdCacheMinutes = 30;
    /**
     * 是否开启徽标，默认开启（部署级别）
     */
    private boolean enabled = true;
    /**
     * 是否开启全量计算徽标，默认开启（部署级别）
     */
    private boolean computeAllEnabled = true;
    /**
     * 全量计算徽标的天数，默认最近3天
     */
    private int computeAllDayNum = 3;
    /**
     * 全量计算徽标的结束日期，默认不配置则为今天
     */
    private String computeAllEndDate = "";
    /**
     * 控制全量计算徽标的天数-默认为true
     */
    private boolean computeAllDayNumEnabled = true;
    /**
     * 全量计算徽标的查询分页条数，默认1000
     */
    private int computeAllBillNumber = 1000;

    /**
     * 全量计算徽标的查询指定重算数据来源，默认为空，所有来源
     */
    private LogoSourceTypeEnum[] logoSourceTypeEnums;

    /**
     * 全量计算徽标的查询占用表分页条数，默认10000
     */
    private int computeAllStateNumber = 10000;

    /**
     * 全量计算徽标的查询总条数，默认100000
     */
    private int computeAllStateCount = 100000;


    /**
     * 全量计算徽标的查询总state表的最后修改天数
     */
    private int computeAllStateLastDays = 7;

    /**
     * 是否计算 处理失败 的订单，默认开启
     */
    private boolean process = true;
    /**
     * 查询change表的limit数量，默认3000
     */
    private int billNumber = 3000;
    /**
     * 正常队列
     * Redis锁持续几秒
     * 默认30分钟
     */
    private int calculateProfileLockSeconds = 2 * 60;


    /**
     * 重算
     * Redis锁持续几秒
     * 默认3小时
     */
    private int calculateProfileAllLockSeconds = 60 * 60 *3;

    /**
     * 账套重算写入change表的区间时间段-开始。默认从0点开始
     */
    private int intervalStartTime = 23;
    /**
     * 账套全量计算区间结束的小时点。默认4点结束
     */
    private int intervalEndTime = 3;
    /**
     * 测试环境配置，只计算指定账套的徽标
     */
    private String profileIds;

    /**
     * 重算频率，默认间隔18小时
     */
    private int resetComputeRate = 18;

    public int getResetComputeRate() {
        return resetComputeRate;
    }

    public void setResetComputeRate(int resetComputeRate) {
        this.resetComputeRate = resetComputeRate;
    }

    public LogoSourceTypeEnum[] getLogoSourceTypeEnums() {
        return logoSourceTypeEnums;
    }

    public void setLogoSourceTypeEnums(LogoSourceTypeEnum[] logoSourceTypeEnums) {
        this.logoSourceTypeEnums = logoSourceTypeEnums;
    }

    public int getComputeAllStateNumber() {
        return computeAllStateNumber;
    }

    public void setComputeAllStateNumber(int computeAllStateNumber) {
        this.computeAllStateNumber = computeAllStateNumber;
    }

    public int getCalculateProfileAllLockSeconds() {
        return calculateProfileAllLockSeconds;
    }

    public void setCalculateProfileAllLockSeconds(int calculateProfileAllLockSeconds) {
        this.calculateProfileAllLockSeconds = calculateProfileAllLockSeconds;
    }

    public int getComputeAllStateLastDays() {
        return computeAllStateLastDays;
    }

    public void setComputeAllStateLastDays(int computeAllStateLastDays) {
        this.computeAllStateLastDays = computeAllStateLastDays;
    }

    public int getComputeAllStateCount() {
        return computeAllStateCount;
    }

    public void setComputeAllStateCount(int computeAllStateCount) {
        this.computeAllStateCount = computeAllStateCount;
    }

    public String getProfileIds() {
        return profileIds;
    }

    public void setProfileIds(String profileIds) {
        this.profileIds = profileIds;
    }

    public int getIntervalStartTime() {
        return intervalStartTime;
    }

    public void setIntervalStartTime(int intervalStartTime) {
        this.intervalStartTime = intervalStartTime;
    }

    public int getIntervalEndTime() {
        return intervalEndTime;
    }

    public void setIntervalEndTime(int intervalEndTime) {
        this.intervalEndTime = intervalEndTime;
    }

    public int getCalculateProfileLockSeconds() {
        return calculateProfileLockSeconds;
    }

    public void setCalculateProfileLockSeconds(int calculateProfileLockSeconds) {
        this.calculateProfileLockSeconds = calculateProfileLockSeconds;
    }

    public int getProfileIdCacheMinutes() {
        return profileIdCacheMinutes;
    }

    public void setProfileIdCacheMinutes(int profileIdCacheMinutes) {
        this.profileIdCacheMinutes = profileIdCacheMinutes;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isProcess() {
        return process;
    }

    public void setProcess(boolean process) {
        this.process = process;
    }

    public int getBillNumber() {
        return billNumber;
    }

    public void setBillNumber(int billNumber) {
        this.billNumber = billNumber;
    }

    public boolean isComputeAllEnabled() {
        return computeAllEnabled;
    }

    public void setComputeAllEnabled(boolean computeAllEnabled) {
        this.computeAllEnabled = computeAllEnabled;
    }

    public int getComputeAllDayNum() {
        return computeAllDayNum;
    }

    public void setComputeAllDayNum(int computeAllDayNum) {
        this.computeAllDayNum = computeAllDayNum;
    }

    public int getComputeAllBillNumber() {
        return computeAllBillNumber;
    }

    public void setComputeAllBillNumber(int computeAllBillNumber) {
        this.computeAllBillNumber = computeAllBillNumber;
    }

    public boolean isComputeAllDayNumEnabled() {
        return computeAllDayNumEnabled;
    }

    public void setComputeAllDayNumEnabled(boolean computeAllDayNumEnabled) {
        this.computeAllDayNumEnabled = computeAllDayNumEnabled;
    }

    public String getComputeAllEndDate() {
        return computeAllEndDate;
    }

    public void setComputeAllEndDate(String computeAllEndDate) {
        this.computeAllEndDate = computeAllEndDate;
    }
}
