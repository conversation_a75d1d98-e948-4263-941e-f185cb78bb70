package com.wsgjp.ct.sale.tool.tmc.impl.jdong;

import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductMappingSaver;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.platform.exception.PlatformInterfaceException;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.jdong.entity.JdongProductTmcRequest;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class JdongProductHandler extends JdongNotifyBase {
    private static final Logger LOGGER = LoggerFactory.getLogger(JdongProductHandler.class);
    private final EshopProductMapper eshopProductMapper;
    private final EshopProductMappingSaver mappingSaver;

    public JdongProductHandler(EshopProductMapper eshopProductMapper, EshopProductMappingSaver mappingSaver) {
        this.eshopProductMapper = eshopProductMapper;
        this.mappingSaver = mappingSaver;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String tmcMessage = invokeMessage.getMessage();
        JdongProductTmcRequest apiRequest;
        try {
            apiRequest = JsonUtils.toObject(tmcMessage, JdongProductTmcRequest.class);
            if (apiRequest == null) {
                throw new PlatformInterfaceException(String.format("平台接口返回报文数据异常,报文信息：%s", tmcMessage));
            }
        } catch (Exception ex) {
            LOGGER.error("京东tmMessage数据转换成JdongProductTmcRequest实体出错，错误信息：{}", ex.getMessage(), ex);
            return "";
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotifies(apiRequest.getVenderId(), shopTypes);
        if (eshopRegister == null) {
            LOGGER.error("profileId:{},京东,platform_shop_id(venderId):{},查询店铺信息为空!tmMessage:{}", invokeMessage.getProfileId(), apiRequest.getVenderId(), tmcMessage);
            return buildResponse("501", "管家婆未找到对应店铺!");
        }
        try {
            updateProductInfo(eshopRegister.getProfileId(), eshopRegister.getId(), apiRequest);
        } catch (Exception ex) {
            LOGGER.error("更新商品信息报错,错误信息:{},tmcMessage:{}", ex.getMessage(), tmcMessage, ex);
            return "";
        }
        return buildResponse("0", "success");
    }

    private void updateProductInfo(BigInteger profileId, BigInteger eshopId, JdongProductTmcRequest apiRequest) {
        if (apiRequest.getSourceProductId() == null) {
            return;
        }
        if (StringUtils.isBlank(apiRequest.getTargetProductIdAndSkuIdsMap())) {
            return;
        }
        HashMap productMap = JsonUtils.toObject(apiRequest.getTargetProductIdAndSkuIdsMap(), HashMap.class);
        if (productMap == null) {
            return;
        }
        List<EshopProductSkuMapping> oldProductSkuMappings = eshopProductMapper.queryProductSkuMappingByNumids(profileId, eshopId,
                Collections.singletonList(String.valueOf(apiRequest.getSourceProductId())), 0);
        if (CollectionUtils.isEmpty(oldProductSkuMappings)) {
            return;
        }
        //只处理手工对应商品
        List<EshopProductSkuMapping> oldFilterProductSkuMappings = oldProductSkuMappings.stream().filter(x -> x.getMappingType().equals(MappingType.NOMARL)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oldFilterProductSkuMappings)) {
            return;
        }
        List<EshopProductSkuMapping> newProductSkuMappings = new ArrayList<>();
        productMap.forEach((productId, skuIdList) -> {
            //如果商品ID未变更不处理
            if (StringUtils.equals(String.valueOf(apiRequest.getSourceProductId()), (String) productId)) {
                return;
            }
            for (EshopProductSkuMapping oldProductSkuMapping : oldFilterProductSkuMappings) {
                if (skuIdList != null) {
                    List<String> skuIds = ((List<Object>) skuIdList).stream().map(String::valueOf).collect(Collectors.toList());
                    if (skuIds.contains(oldProductSkuMapping.getPlatformSkuId())) {
                        EshopProductSkuMapping newProductSkuMapping = new EshopProductSkuMapping();
                        BeanUtils.copyProperties(oldProductSkuMapping, newProductSkuMapping);
                        newProductSkuMapping.setUniqueId(null);
                        newProductSkuMapping.setId(UId.newId());
                        newProductSkuMapping.setPlatformNumId(String.valueOf(productId));
                        newProductSkuMappings.add(newProductSkuMapping);
                    }
                }
            }
        });
        if (CollectionUtils.isNotEmpty(newProductSkuMappings)) {
            doUpdateProductInfo(newProductSkuMappings);
        }
    }

    private void doUpdateProductInfo(List<EshopProductSkuMapping> newProductSkuMappings) {
        //保存平台的mapping+skuexpand就行了
        mappingSaver.saveOnlyEshopProductSkuMapping(newProductSkuMappings);
    }

    @Override
    public String serviceName() {
        return "jdongProductHandler";
    }
}
