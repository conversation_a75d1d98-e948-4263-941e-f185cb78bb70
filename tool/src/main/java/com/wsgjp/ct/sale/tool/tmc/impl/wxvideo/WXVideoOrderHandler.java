package com.wsgjp.ct.sale.tool.tmc.impl.wxvideo;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.eshoporder.dao.EshopSaleOrderDao;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.common.enums.eshoporder.OrderOpreateType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.response.order.AuditOrderReceiverResponse;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.wxvideo.entity.VideoMsg;
import com.wsgjp.ct.sale.tool.tmc.impl.wxvideo.weixin.mp.aes.WXBizMsgCrypt;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import ngp.utils.XmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import utils.JsonUtils;

import java.math.BigInteger;
import java.util.*;

@Component
public class WXVideoOrderHandler extends WXVideoNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(WXVideoOrderHandler.class);
    private final EshopTmcOrderMsgMapper tmcOrderMsgMapper;
    private final EshopTmcUtils eshopTmcUtils;
    private final EshopTmcConfig config;
    private final TmcEshopNotifyChangeMapper tmcMapper;
    public WXVideoOrderHandler(EshopTmcOrderMsgMapper tmcOrderMsgMapper, EshopTmcUtils eshopTmcUtils, EshopTmcConfig config, TmcEshopNotifyChangeMapper tmcMapper) {
        this.tmcOrderMsgMapper = tmcOrderMsgMapper;
        this.eshopTmcUtils = eshopTmcUtils;
        this.config = config;
        this.tmcMapper = tmcMapper;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String shopTypeName = invokeMessage.getShopType().getName();
        LOGGER.info("======{}进入invoker方法======",shopTypeName);
        String tmMessage = invokeMessage.getMessage();
        Map<String, String> params = invokeMessage.getParams();
        String timestamp = params.get("timestamp");
        String nonce = params.get("nonce");
        BigInteger eshopId = null;
        EshopInfo eshopInfo = null;
        try {
            WXVideoResponse response = null;
            if (!config.isWxVideoIsJson()){
//            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
//            dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
//            dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
//            dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
//            dbf.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
//            dbf.setXIncludeAware(false);
//            dbf.setExpandEntityReferences(false);

//            DocumentBuilder db = dbf.newDocumentBuilder();
//            StringReader sr = new StringReader(tmMessage);
//            InputSource is = new InputSource(sr);
//            Document document = db.parse(is);

//            Element root = document.getDocumentElement();
//            NodeList nodelist1 = root.getElementsByTagName("Encrypt");
//            NodeList nodelist2 = root.getElementsByTagName("MsgSignature");
//
//            String encrypt = nodelist1.item(0).getTextContent();
//            String msgSignature = nodelist2.item(0).getTextContent();
//
//            String format = "<xml><ToUserName><![CDATA[toUser]]></ToUserName><Encrypt><![CDATA[%1$s]]></Encrypt></xml>";
//            String fromXML = String.format(format, encrypt);
                //
                // 公众平台发送消息给第三方，第三方处理
                //
                // 第三方收到公众号平台发送的消息
                Map map1 = XmlUtils.toObject(tmMessage, Map.class);
                Object toUserName = map1.get("ToUserName");
                if (BigInteger.ZERO.equals(invokeMessage.getEshopId())){
                    // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                    EshopRegisterNotify notify = SupportUtil.buildNotify(toUserName.toString(), 115);
                    if (notify == null || notify.getId()==null|| notify.getId().equals(BigInteger.ZERO)){
                        LOGGER.error("微信视频号消息处理失败：eshopId拿取失败");
                        return "false";
                    }
                    eshopId=notify.getId();
                }else {
                    eshopId = invokeMessage.getEshopId();
                }
                eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
                if(Objects.isNull(eshopInfo)){
                    LOGGER.error("profileId:{},eshopId:{},店铺类型:{},微信视频号查询店铺信息为空!",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName);
                    return "false";
                }
                WXBizMsgCrypt pc = new WXBizMsgCrypt(config.getWxVideoToken(), config.getWxVideoEncodingAesKey(), config.getWxVideoAppId());
                String result2 = pc.decryptMsg(params.get("msg_signature"), timestamp, nonce, tmMessage);
                Map map = XmlUtils.toObject(result2, Map.class);
                response = JsonUtils.toObject(JsonUtils.toJson(map), WXVideoResponse.class);
//            WXVideoResponse response = XmlUtils.toObject(result2, WXVideoResponse.class);
            }else {
                VideoMsg videoMsg = JsonUtils.toObject(tmMessage, VideoMsg.class);
                /*
            由于毕方没做过，直接走的NGP路由那边没提供eshopId过来，所以需要自己拿取
         */
                if (BigInteger.ZERO.equals(invokeMessage.getEshopId())){
                    // 如果拿不到检查下CurrentUser.getProductId()是多少和platform_code_profile_mapping表的product是否一样
                    EshopRegisterNotify notify = SupportUtil.buildNotify(videoMsg.getToUserName(), 115);
                    if (notify == null || notify.getId()==null|| notify.getId().equals(BigInteger.ZERO)){
                        LOGGER.error("微信视频号消息处理失败：eshopId拿取失败");
                        return "false";
                    }
                    eshopId=notify.getId();
                }else {
                    eshopId = invokeMessage.getEshopId();
                }
                eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopId);
                if(Objects.isNull(eshopInfo)){
                    LOGGER.error("profileId:{},eshopId:{},店铺类型:{},微信视频号查询店铺信息为空!",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName);
                    return "false";
                }
                WXBizMsgCrypt pc = new WXBizMsgCrypt(config.getWxVideoToken(), config.getWxVideoEncodingAesKey(), config.getWxVideoAppId());
                String decrypt = pc.decrypt(videoMsg.getEncrypt());
                response = JsonUtils.toObject(decrypt, WXVideoResponse.class);
            }


            if (response == null) {
                return "false";
            }
            LOGGER.info("profileId:{},eshopId:{},店铺类型：{}",invokeMessage.getProfileId(),invokeMessage.getEshopId(),shopTypeName);
            try {
                if ("channels_ec_order_ext_info_update".equals(response.getEvent()) && "4".equals(response.getOrder_info().getType())){
                    AuditCancelOrderService service = new AuditCancelOrderService();
                    AuditOrderReceiverResponse auditOrderReceiverResponse = service.auditCancelOrder(response.getOrder_info().getOrder_id(), invokeMessage.getProfileId(), eshopId, ShopType.WeChatVideoShop);
                    EshopSaleOrderDao orderDao = BeanUtils.getBean(EshopSaleOrderDao.class);
                    EshopSaleOrderEntity order = orderDao.getSaleOrderByTradeId(eshopId, response.getOrder_info().getOrder_id());
                    if (order != null && auditOrderReceiverResponse.getAccept() != null){
                        if (auditOrderReceiverResponse.getAccept() && auditOrderReceiverResponse.getSuccess()){
                            SysLogUtil.add(SysLogUtil.buildLog(order, OrderOpreateType.AUDIT_BUYER_MODIFY_ADDRESS, "买家申请自动修改收货地址。允许修改，返回改地址成功。"));
                        }
                        if (!auditOrderReceiverResponse.getAccept() && auditOrderReceiverResponse.getSuccess()){
                            SysLogUtil.add(SysLogUtil.buildLog(order, OrderOpreateType.AUDIT_BUYER_MODIFY_ADDRESS, "买家申请自动修改收货地址。不允许修改，返回改地址失败。"));
                        }
                    }

                }
            }catch (Exception e){
                LOGGER.error("账套ID:" +invokeMessage.getProfileId() + "订单号:" + "视频号确认修改地址报错" + e.getMessage());
            }
            if (response.getOrder_info() == null || StringUtils.isEmpty(response.getOrder_info().getOrder_id())){
                LOGGER.error("账套ID:" +invokeMessage.getProfileId() + "视频号消息推送订单号为空,event:" + response.getEvent());
                return "false";
            }
            if ("channels_ec_order_pay".equals(response.getEvent())){
                try{
                    saveTmcNotifyChange(invokeMessage,response.getOrder_info());
                }catch (Exception ex){
                    LOGGER.error("{}保存视频号预处理TMC消息到数据库出错,profileId:{},eshopId:{},tmMessage:{},错误信息：{}",shopTypeName,invokeMessage.getProfileId(),invokeMessage.getEshopId(),tmMessage,ex.getMessage(),ex);
                }
            }

            if ("channels_ec_aftersale_update".equals(response.getEvent())  || "channels_ec_present_aftersale_update".equals(response.getEvent())){
                WXVideoResponse.FinderShopAftersaleStatusUpdate finderShopAftersaleStatusUpdate = response.getFinderShopAftersaleStatusUpdate();
                if (finderShopAftersaleStatusUpdate != null){
                    EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
                    eshopNotifyChange.setOnlineShopId(response.getToUserName());
                    eshopNotifyChange.setTradeOrderId(response.getFinderShopAftersaleStatusUpdate().getOrderID());
                    eshopNotifyChange.setType(TMCType.Order);
                    eshopNotifyChange.setContent(tmMessage);
                    SupportUtil.doOrderNotify(response.getToUserName(),eshopNotifyChange,eshopInfo.getEshopType().getCode());
                    return "success";
                }
            }

            EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
            eshopNotifyChange.setOnlineShopId(response.getToUserName());
            eshopNotifyChange.setTradeOrderId(response.getOrder_info().getOrder_id());
            eshopNotifyChange.setType(TMCType.Order);
            eshopNotifyChange.setContent(tmMessage);
            SupportUtil.doOrderNotify(response.getToUserName(),eshopNotifyChange,eshopInfo.getEshopType().getCode());
            return "success";
        } catch (Exception e) {
            LOGGER.error("{}未知错误，错误信息：{}",shopTypeName,e.getMessage(),e);
            return "Failed";
        }
    }

//    private void updateTmcNotifyChange(InvokeMessageEntity invokeMessage, WXVideoResponse.OrderInfo orderInfo) {
//        try {
//            List<EshopNotifyChange> changes = tmcMapper.queryMessageChangeSorted(invokeMessage.getProfileId(), Collections.singletonList(orderInfo.getOrder_id()), invokeMessage.getEshopId(), TMCType.CUSTOMER_INFORMATION.getCode());
//            if (CollectionUtils.isNotEmpty(changes)){
//                for (EshopNotifyChange change : changes) {
//                    change.setUpdateTime(new Date());
//                    change.setContent(invokeMessage.getMessage());
//                    change.setType(TMCType.ALLOW_SHIPMENT);
//                    tmcMapper.updateEshopNotifyChange(change);
//                }
//
//            }
//        } catch (Exception e) {
//            LOGGER.error("账单和订单关系保存notify_change表失败：profileid:{},店铺id:{}，错误信息:{}",invokeMessage.getProfileId(),invokeMessage.getEshopId(),e.getMessage(), e);
//        }
//    }


    private int saveTmcNotifyChange(InvokeMessageEntity invokeMessage, WXVideoResponse.OrderInfo orderInfo) {
        try {
            List<EshopNotifyChange> changes = tmcMapper.queryMessageChangeSorted(invokeMessage.getProfileId(), Collections.singletonList(orderInfo.getOrder_id()), invokeMessage.getEshopId(), TMCType.CUSTOMER_INFORMATION.getCode());
            if (CollectionUtils.isEmpty(changes)){
                EshopNotifyChange change = buildEshopNotifyChangeMsgEntity(invokeMessage, orderInfo);
                return tmcMapper.insertMessageChange(change);
            }
        } catch (Exception e) {
            LOGGER.error("账单和订单关系保存notify_change表失败：profileid:{},店铺id:{}，错误信息:{}",invokeMessage.getProfileId(),invokeMessage.getEshopId(),e.getMessage(), e);
        }
        return 0;
    }

    private EshopNotifyChange buildEshopNotifyChangeMsgEntity(InvokeMessageEntity invokeMessage, WXVideoResponse.OrderInfo orderInfo) {
        EshopNotifyChange change = new EshopNotifyChange();
        change.setId(UId.newId());
        change.setProfileId(invokeMessage.getProfileId());
        change.setEshopId(invokeMessage.getEshopId());
        change.setTradeOrderId(orderInfo.getOrder_id());
        change.setContent(invokeMessage.getMessage());
        change.setCreateTime(new Date());
        change.setUpdateTime(new Date());
        change.setType(TMCType.PAID_ORDER_PREPROCESSING);
        return change;
    }


    @Override
    public String serviceName() {
        return "WXVideoOrder";
    }

}
