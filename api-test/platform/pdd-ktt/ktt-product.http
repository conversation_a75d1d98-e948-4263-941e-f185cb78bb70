
### 快团团全量下载商品

POST http://localhost:10089/{{service-name}}/product/download
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "beginTime": "2022-02-17T09:03:15.913Z",
  "endTime": "2022-02-17T09:03:15.913Z",
  "sellerCid": "",
  "shopId": 532108643672272551,
  "shopType": "PinDuoDuoKtt",
  "status": [
    "All"
  ]
}
> {%
 client.test("Request executed successfully", function () {
     client.assert(response.status === 200, "Response status is not 200")
 })
 %}
### 快团团 增量下载商品

POST http://localhost:10089/{{service-name}}/product/downloadByIncrementally
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "beginTime": "2021-02-01 09:03:15.913",
  "endTime": "2022-02-17 09:03:15.913",
  "sellerCid": "",
  "shopId": 532108643672272551,
  "shopType": "PinDuoDuoKtt",
  "status": "All"
}

###