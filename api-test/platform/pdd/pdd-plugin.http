###拼多多 定义网店新增（编辑）页面的展示内容
POST http://localhost:10089/{{service-name}}/plugin/getFiledInfo
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "shopType": "PinDuoDuo",
  "shopId": "542663637471346277"
}

###拼多多 定义网店页面的显示标签，订购，授权等
POST http://localhost:10089/{{service-name}}/plugin/getEshopTag
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "shopType": "PinDuoDuo",
  "shopId": "542663637471346277"
}

###拼多多 下载商品支持的状态(UI展示的顺序按照列表顺序展示)
POST http://localhost:10089/{{service-name}}/plugin/getProductDownloadSupport
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "shopType": "PinDuoDuo",
  "shopId": "542663637471346277"
}

###拼多多 下载订单支持的状态(UI展示的顺序按照列表顺序展示)
POST http://localhost:10089/{{service-name}}/plugin/getOrderDownloadSupport
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "shopType": "PinDuoDuo",
  "shopId": "542663637471346277"
}


###拼多多 商品对应规则列表
POST http://localhost:10089/{{service-name}}/plugin/getSupportMappingTypeList
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "shopType": "PinDuoDuo",
  "shopId": "542663637471346277"
}

###拼多多 检查授权是否过期的方式
POST http://localhost:10089/{{service-name}}/plugin/getCheckAuthType
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "shopType": "PinDuoDuo",
  "shopId": "542663637471346277"
}

###拼多多 获取支持的业务配置
POST http://localhost:10089/{{service-name}}/plugin/getEshopBusinessConfigList
Accept: application/json
ngp-router: {{ngp-router}}
Content-Type: application/json

{
  "shopType": "PinDuoDuo",
  "shopId": "542663637471346277"
}


