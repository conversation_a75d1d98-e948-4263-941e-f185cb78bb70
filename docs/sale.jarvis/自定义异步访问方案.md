## 自定义异步访问方案
### 目标
* 通过快捷的配置和少量的数据存储采集编辑，快速增加异步查询方案
## 组件
### 拦截器
* 负责完成接口访问的拦截、任务管理、接口数据返回切换的自适应

```java

@Aspect
@Component
public class DefinedAsyncProcessNormalAspect extends AbstractAsyncProcess {
    /**
     * 基于注解 DefinedAsyncTaskProcess 
     */
    @Pointcut("@annotation(processNormal)")
    public void source(DefinedAsyncTaskProcess processNormal) {
        
    }

    @Around("source(processNormal)")
    public Object doAround(ProceedingJoinPoint joinPoint, DefinedAsyncTaskProcess processNormal) throws Throwable {
        /**
         1. 检查提交内容中是否包含自定义异步线程的参数内容 processId
         2. 创建任务、并启动异步线程
         3. 根据任务的状态返回统一的临时结果 DefinedAsyncTask
         4. 当异步线程执行完成时标记任务执行完成 
         5. 前端任务检查
            前端会一直访问改接口：
                当任务状态为已完成时，调度注解中的托管加载器DefinedAsyncTaskResultLoader，加载需要返回的结果集
         5. 整个流程终止
        */        
    }
}

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DefinedAsyncTaskProcess {
    String threadPoolName();
    DefinedAsyncQueryTypeEnum queryType();
    Class<? extends DefinedAsyncTaskResultLoader> taskClass();
    public static class ThreadPoolNames {
        public static final String EShopSaleStatistic = "jarvis-deliverbill-report-eshopsalestatistic";
    }
}

public class DefinedAsyncTask implements DefinedAsyncTaskLinkInfo  {
    /*
            临时结果：基于PageResponse 的返回接口可用
            自定义返回内容还需要扩展自定义操作相关的接口
     */
    
}

/**
 * 接口为拦截器与后续的抽象对象操作提供接口化的标准参数读写
 */
public interface DefinedAsyncTaskLinkInfo {
    /**
     * 前端提供的任务id，请再后台获取一个cuid，该标识会写入到数据库中
     * @return
     */
    BigInteger getProcessId();

    /**
     * 系统内部的任务id，当任务创建时 通过set方法写到该对象上
     * 对象会携带数据到业务内部，主要用户检查任务是否还可以继续
     * @return
     */
    BigInteger getSysQueryTaskId();
    void setSysQueryTaskId(BigInteger taskId);
}
```
### 任务与任务结果管理
```java
/**
 * 为外部提供任务操作的封装
 */
public interface DefinedAsyncTaskService {


    /**
     * 自定义的异常，在业务中需要单独拦截，该异常是终止程序继续运营的异常
     * 请不要抛出给最外层业务，会导致外层没有拦截而设置任务执行失败
     */
    public static class ContinueException extends  Exception {
        public ContinueException(String msg) {
            super(msg);
        }
    }

}

/**
 * 为外部提供任务结果的封装
 */
public interface DefinedAsyncTaskResultService {
    
}
```
### 辅助插件
```java
/**
 * 分页管理器组合接口
 * 1. 负责重写分页管理器 initPage、readPage 并利用这两个步骤对数据结果进行采集
 * 2. 利用组合插件，对业务部分进行扩展，例如：检查是否可以继续
 */
public interface PageHelperComponentService {
    
}
/**
 * 默认封装类，不支持注入 自己new一下
 * 默认调用原始的PageDevice方法
 */
public class DefaultPageHelperServiceImpl implements PageHelperComponentService {
    
}


@Component
public class PageHelperDefinedSyncTaskComponentServiceImpl implements PageHelperComponentService {


    private DefinedAsyncTaskResultService definedAsyncTaskResultService;
    private DefinedAsyncTaskService definedAsyncTaskService;

    public PageHelperDefinedSyncTaskComponentServiceImpl(DefinedAsyncTaskResultService definedAsyncTaskResultService,
                                                         DefinedAsyncTaskService definedAsyncTaskService) {
        this.definedAsyncTaskResultService = definedAsyncTaskResultService;
        this.definedAsyncTaskService = definedAsyncTaskService;
    }

    /**
     * 通过对该方法的装饰，拦截操作，只处理排序的字段转换
     * @param request
     * @param linkInfo
     * @param <T>
     */
    @Override
    public <T> void initPage(PageRequest<T> request,DefinedAsyncTaskLinkInfo linkInfo) {

    }

    /**
     * 通过对该方法的装饰，拦截数据结果，并写入到数据库中
     * @param linkInfo
     * @param result
     * @param <K>
     * @return
     */
    @Override
    public <K> PageResponse<K> readPage(DefinedAsyncTaskLinkInfo linkInfo, List<K> result) {
        
    }

    /**
     * 检查任务是否继续，可能任务已取消，如此可以减少程序不必要的允许时间
     * @param linkInfo
     */
    @Override
    public void checkContinue(DefinedAsyncTaskLinkInfo linkInfo) throws DefinedAsyncTaskService.ContinueException {
        
    }
}

```

## 使用方法
### 增加自定义数据结果加载器
```java
/**
 * 准备一个实现DefinedAsyncTaskResultLoader接口的自定义加载器
 */ 
public class EShopSaleStatisticsServiceImpl implements DefinedAsyncTaskResultLoader<PageRequest<EShopSaleStatisticsQueryParams>,PageResponse<EShopSaleStatistics>> {
    @Autowired
    private DefinedAsyncTaskResultService definedAsyncTaskResultService;
    @Autowired
    private DefinedAsyncTaskService definedAsyncTaskService;
    /**
     * 从异步任务结果中加载数据
     *
     * @param request
     * @return
     */
    @Override
    public PageResponse<EShopSaleStatistics> getDefinedAsyncResult(PageRequest<EShopSaleStatisticsQueryParams> request) {
        if (null == request || null == request.getQueryParams()) {
            return PageDevice.readPage(new ArrayList<>());
        }
        DefinedAsyncTask task = definedAsyncTaskService.getTaskById(request.getQueryParams().getSysQueryTaskId());
        final List<DefinedAsyncTaskResult> datas = definedAsyncTaskResultService.list(task);
        List<EShopSaleStatistics> result = new ArrayList<>();
        if (datas == null || datas.size() == 0) {
            // 没有数据 直接返回
            return PageDevice.readPage(result);
        }
        // 对数据进行反序列化组装结果
        for (DefinedAsyncTaskResult data : datas) {
            EShopSaleStatistics eShopSaleStatistics = JsonUtils.toObject(data.getJsonData(), EShopSaleStatistics.class);
            result.add(eShopSaleStatistics);
        }
        return PageDevice.readPage(result);
    }
}

/**
 * 使查询对象继承自DefinedAsyncTaskLinkInfo
 */
public class EShopSaleStatisticsQueryParams extends QueryParams implements DefinedAsyncTaskLinkInfo {
}

/**
 * 对场景进行添加
 */
public enum DefinedAsyncQueryTypeEnum implements CodeEnum {
    EShopSaleStatistic(1,"网店运营统计"),
    ;

    DefinedAsyncQueryTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;
    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}

/**
 * 将注入器添加到注解上
 * 并使用PageHelperDefinedSyncTaskComponentServiceImpl 替换PageDevice的操作方案
 * 当然这个你也可以自行编辑
 */
public class EShopSaleStatisticsController {

    @Autowired
    private EShopSaleStatisticsService esService;
    @Autowired
    private PageHelperDefinedSyncTaskComponentServiceImpl pageHelperDefinedSyncTaskComponentService;

    @DefinedAsyncTaskProcess(threadPoolName = DefinedAsyncTaskProcess.ThreadPoolNames.EShopSaleStatistic,
            queryType = DefinedAsyncQueryTypeEnum.EShopSaleStatistic,
            loadClass = EShopSaleStatisticsServiceImpl.class)
    public PageResponse<EShopSaleStatistics> getEShopSaleStatistics (@RequestBody PageRequest<EShopSaleStatisticsQueryParams> esQueryParams) {
        return esService.getEShopSaleStatistics(esQueryParams, pageHelperDefinedSyncTaskComponentService);
    }
}

@Service
public class EShopSaleStatisticsServiceImpl implements EShopSaleStatisticsService, DefinedAsyncTaskResultLoader<PageRequest<EShopSaleStatisticsQueryParams>,PageResponse<EShopSaleStatistics>> {

    /**
     * 主动拦截 异常DefinedAsyncTaskServiceImpl.ContinueException，
     * @param esQueryParams
     * @param pageHelperComponentService
     * @return
     */
    @Override
    public PageResponse<EShopSaleStatistics> getEShopSaleStatistics(PageRequest<EShopSaleStatisticsQueryParams> esQueryParams, PageHelperComponentService pageHelperComponentService) {
        try {
            return this.doGetEShopSaleStatistics(esQueryParams, pageHelperComponentService);
        } catch (DefinedAsyncTaskServiceImpl.ContinueException e) {
            // 不能继续的异常消息，不用处理，正常退出
            return PageDevice.readPage(new ArrayList<>());
        }
    }

    /**
     * 在方法中增加代码调整
     * @param esQueryParams
     * @param pageHelperComponentService
     * @return
     * @throws DefinedAsyncTaskService.ContinueException
     */    
    private PageResponse<EShopSaleStatistics> doGetEShopSaleStatistics(PageRequest<EShopSaleStatisticsQueryParams> esQueryParams, PageHelperComponentService pageHelperComponentService) throws DefinedAsyncTaskServiceImpl.ContinueException {
        /**
         * 使用新的组合对象替换pagehelper的操作
         */
        pageHelperComponentService.initPage(esQueryParams);
        // 调用检查是否需要继续，针对大量数据的操作
        pageHelperComponentService.checkContinue(esQueryParams.getQueryParams());
        // 调用读取数据将内存数据刷入到需要去的地方
        return pageHelperComponentService.readPage(queryParams, result);
    }

```
