

### 问题排查
* 数据采集SQL语句
```mysql
select
	tba.id,
	tba.profile_id,
	tba.expected_time,
	tba.execute_time,
	tba.task_state,
	tba.execute_count,
	tba.source_app,
	tba.identity_label,
	tba.task_type,
	tba.bus_content,
	tba.bus_result,
	tba.user_app,
	tba.create_time,
	tba.update_time,
	data.ids
from
	(
	select
		group_concat(tba.id order by tba.execute_time asc) ids,
		SUBSTRING_INDEX(GROUP_CONCAT(tba.id order by tba.execute_time desc), ',', 1) id,
		tba.profile_id
	from
		td_bus_data tba
	where
		tba.profile_id = 831263959557435393
		and tba.task_type in ( 7 , 2 , 6 , 3 , 5 , 4 , 1 )
		and tba.expected_time <= now()
		and tba.execute_count <= 3
		and tba.task_state in ( 0 , 3 )
	group by
		tba.task_type,
		tba.identity_label
	order by
		max(tba.execute_time) asc
	limit 2000 ) data
left join td_bus_data tba on
	tba.profile_id = data.profile_id
	and tba.id = data.id
where
	data.profile_id = 831263959557435393
```
* 检查账套是否会自动生产任务：测试环境
```mysql
SELECT profile.profileid,dep.deployname  FROM `company`cmp
        JOIN PROFILE ON profile.companyid = cmp.companyid AND profile.productId=cmp.productId
        LEFT JOIN deploy dep ON dep.`id` =cmp.deployid
        WHERE dep.deployname='master' and cmp.expiredate>now() and profile.profilestate=0
        and profile.profileid in(820006409142837248,822534037687017473,853672154821148673)
        
```
* 统计还没有执行的任务和账套
```mysql
select count(1),profile_id from td_bus_data tbd 
where task_state  = 0 and expected_time < now() 
group by profile_id 
```
* 特殊情况
  1. 账套没有加载到数据    
  检查数据拉去SQL语句是否能够正常读取到数据
  2. 数据加载到了，但是没有执行
  检查update SQL是否出现了SQL语句错误
  3. 始终没有消费
  检查日志是否包含该账套的生产记录，补充company\profile两个标的deployid设置正确