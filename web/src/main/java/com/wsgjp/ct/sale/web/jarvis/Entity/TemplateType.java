package com.wsgjp.ct.sale.web.jarvis.Entity;

public enum TemplateType {
    //
    GENERAL(0, "普通面单"),
    //
    LOGISTICS_COMPANY(1, "物流公司热敏"),
    //
    CAINIAO_PRINT(2, "菜鸟电子面单"),
    //
    JD_ALPHA(3, "京东无界面单"),
    //
    PDD_PRINT(4, "拼多多电子面单"),
    //
    VIP_JITX_PRINT(5, "唯品会JITX电子面单");

    private int type;
    private String name;

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    TemplateType(int type, String name) {
        this.type = type;
        this.name = name;
    }
}