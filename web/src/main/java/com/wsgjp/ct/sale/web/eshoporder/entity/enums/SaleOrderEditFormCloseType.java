package com.wsgjp.ct.sale.web.eshoporder.entity.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2020/3/6 0006 16:44
 */
public enum  SaleOrderEditFormCloseType implements CodeEnum {
    /**
     * 手工开单页面关闭之后的操作类型
     */
    ASK(0,"询问"),
    CLOSE(1,"关闭"),
    GOTO_LIST_FORM(2,"跳转原始订单");

    private int code;
    private String name;
    SaleOrderEditFormCloseType(int code, String name){
        this.code=code;
        this.name=name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
