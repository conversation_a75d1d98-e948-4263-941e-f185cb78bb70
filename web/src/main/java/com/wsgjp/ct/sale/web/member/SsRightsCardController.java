package com.wsgjp.ct.sale.web.member;

import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.common.CustomResult;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.member.model.dto.rights.SsRightsCardIdListRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.rights.VipIdsAndRightsIds;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCardTemplate;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCardTemplateDetail;
import com.wsgjp.ct.sale.biz.member.model.entity.rights.MemberEquityValue;
import com.wsgjp.ct.sale.biz.member.model.vo.rights.SsRightsCard;
import com.wsgjp.ct.sale.biz.member.service.ISsRightsCardService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipRightsCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;

@Api(tags = "权益相关")
@RequestMapping("${app.id}/member/rightsCard")
@RestController
public class SsRightsCardController {

    @Autowired
    private ISsRightsCardService service;

    @Autowired
    private ISsVipRightsCardService vipRightsCardService;


    @ApiOperation(value = "权益卡列表")
    @PostMapping(value = "/getRightsCardList")
    @WebLogs
    PageInfo<SsCardTemplate> getRightsCardList(@RequestBody SsCardTemplate requestParam) throws ParseException {
        return service.getRightsCardList(requestParam);
    }

    @ApiOperation(value = "权益卡管理列表")
    @PostMapping(value = "/getRightsCardManagerList")
    @PermissionCheck(key = PermissionShopSale.MEMBER_RIGHTSMANAGER_VIEW)
    PageInfo<SsCardTemplate> getRightsCardManagerList(@RequestBody SsCardTemplate requestParam) throws ParseException {
        return service.getRightsCardManagerList(requestParam);
    }

    @ApiOperation(value = "获取权益卡权益")
    @PostMapping(value = "/getRightsCardRightsConfig")
    PageInfo<MemberEquityValue> getRightsCardRightsConfig(@RequestBody SsRightsCard requestParam) throws ParseException {
        return service.getRightsCardRightsConfig(requestParam);
    }

    @ApiOperation(value = "获取权益卡权益价值")
    @PostMapping(value = "/getRightsCardEquityValue")
    PageInfo<SsCardTemplateDetail> getRightsCardEquityValue(@RequestBody SsCardTemplate requestParam) throws ParseException {
        return service.getRightsCardEquityValue(requestParam);
    }

    @ApiOperation(value = "停用一条或者多条权益卡")
    @PostMapping(value = "/stopOrDeleteRightsCard")
    CustomResult stopOrDeleteRights(@RequestBody SsRightsCardIdListRequest requestParam) throws ParseException {
        return service.stopOrDeleteRights(requestParam);
    }

    @ApiOperation(value = "删除会员的权益卡")
    @PostMapping(value = "/deleteRightsCard")
    public void deleteRightsCard(@RequestBody VipIdsAndRightsIds ids) {
        vipRightsCardService.deleteRightsCard(ids);
    }


}
