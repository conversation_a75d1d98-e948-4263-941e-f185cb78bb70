package com.wsgjp.ct.sale.web.wx.auth;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.wsgjp.ct.sale.biz.wx.properties.NgpWxOpenConfigProperties;
import com.wsgjp.ct.sale.biz.wx.service.NgpWxOpenService;
import com.wsgjp.ct.support.context.CurrentUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import ngp.starter.web.annotation.NotWrapper;
import ngp.utils.Base64Utils;
import ngp.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.math.BigInteger;


/**
 * 微信开放平台主动调用controller
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/${app.id}/wxopen/auth")
@Slf4j
@AllArgsConstructor
public class WechatOpenAuthController {

    private final NgpWxOpenService wxOpenService;

    private final NgpWxOpenConfigProperties wxOpenConfigProperties;

    /**
     * 授权成功回调url
     */
    private static final String AUTH_CALLBACK_URI = "/sale/wxopen/auth/callback?profileId=";
    /**
     * 开始授权页面{参数: profileId、微信授权地址url}
     */
    private static final String GO_AUTH_PAGE = "/sale/member/pages/auth/goAuth.html?platformUrl={}&callbackUrl={}";
    /**
     * 授权成功页面{参数: profileId、授权结果错误信息}
     */
    private static final String AUTH_CALLBACK_PAGE = "/sale/member/pages/auth/authCallback.html?profileId={}&auth_error_info={}";


    /**
     * 开放平台消息通知回调
     *
     * @param requestBody
     * @param timestamp
     * @param nonce
     * @param signature
     * @param encType
     * @param msgSignature
     * @return
     * @throws Exception
     */
    @PostMapping("/notify/receive_ticket")
    @NotWrapper
    @ResponseBody
    public Object receiveTicket(@RequestBody(required = false) String requestBody, @RequestParam("timestamp") String timestamp,
                                @RequestParam("nonce") String nonce, @RequestParam("signature") String signature,
                                @RequestParam(name = "encrypt_type", required = false) String encType,
                                @RequestParam(name = "msg_signature", required = false) String msgSignature) throws Exception {
        checkSign(encType, signature, timestamp, nonce);
        // 解密消息
        WxOpenXmlMessage inMessage = WxOpenXmlMessage.fromEncryptedXml(requestBody,
                wxOpenService.getWxOpenConfigStorage(), timestamp, nonce, msgSignature);
        // 处理消息并返回
        return wxOpenService.getWxOpenComponentService().route(inMessage);
    }

    /**
     * 公众号/小程序 消息回调通知
     *
     * @param requestBody
     * @param appId
     * @param signature
     * @param timestamp
     * @param nonce
     * @param openid
     * @param encType
     * @param msgSignature
     * @return
     */
    @PostMapping("/notify/callback/{appId}")
    @NotWrapper
    @ResponseBody
    public String callback(@RequestBody(required = false) String requestBody,
                           @PathVariable("appId") String appId,
                           @RequestParam("signature") String signature,
                           @RequestParam("timestamp") String timestamp,
                           @RequestParam("nonce") String nonce,
                           @RequestParam("encrypt_type") String encType,
                           @RequestParam("msg_signature") String msgSignature) {
        log.info("进入微信公众号事件回调：" + requestBody);
        checkSign(encType, signature, timestamp, nonce);
        // 解密消息
        WxMpXmlMessage inMessage = WxOpenXmlMessage.fromEncryptedMpXml(requestBody,
                wxOpenService.getWxOpenConfigStorage(), timestamp, nonce, msgSignature);
        // 处理消息，若内部有返回则返回，无返回则直接返回空字符串
        WxMpXmlOutMessage outMessage = wxOpenService.getWxOpenMessageRouter().route(inMessage, appId);
        if (outMessage != null) {
            return WxOpenXmlMessage.wxMpOutXmlMessageToEncryptedXml(outMessage,wxOpenService.getWxOpenConfigStorage());
        }
        return "";
    }

    /**
     * 校验签名
     *
     * @param encType
     * @param signature
     * @param timestamp
     * @param nonce
     */
    private void checkSign(String encType, String signature, String timestamp, String nonce) {
        if (!StringUtils.equalsIgnoreCase("aes", encType)
                || !wxOpenService.getWxOpenComponentService().checkSignature(timestamp, nonce, signature)) {
            throw new IllegalArgumentException("Invalid signature: " + signature);
        }
    }


    /**
     * 获取预授权码
     *
     * @throws Exception
     */
    @ResponseBody
    @PostMapping("/getAuthUrl")
    public String getAuthUrl(@RequestBody String hostUrl) throws Exception {
        // 拼接回调地址  域名 + 回调uri
        BigInteger profileId = CurrentUser.getProfileId();
        String authDomain = wxOpenConfigProperties.getAuthDomain();
        // 真正的授权成功回调url
        String callbackUrl = hostUrl + AUTH_CALLBACK_URI + profileId.toString();
        String goAuthPage = authDomain + GO_AUTH_PAGE.substring(0,GO_AUTH_PAGE.indexOf("?"));
        String wxAuthUrl = "";
        log.info(JsonUtils.toJson("获取授权地址，当前信息：" + JsonUtils.toJson(wxOpenService.getWxOpenConfigStorage())));
        try {
            wxAuthUrl = wxOpenService.getWxOpenComponentService().getPreAuthUrl(goAuthPage);
        } catch (WxErrorException e) {
            throw new RuntimeException(e.getError().getErrorMsg());
        }
        return authDomain + StrUtil.format(GO_AUTH_PAGE, Base64Utils.toBase64String(wxAuthUrl), Base64Utils.toBase64String(callbackUrl));
    }

    /**
     * 授权成功回调地址【此请求地址在ngp网关中单独做了处理，携带了profileId参数会自动封装ngpRouter，即代表登录用户在访问】
     *
     * @param authorizationCode
     * @throws Exception
     */
    @GetMapping("/callback")
    public ModelAndView callback(@RequestParam("auth_code") String authorizationCode) throws Exception {
        String errorMsg = "";
        // 调用微信开放平台获取授权信息
        try {
            WxOpenQueryAuthResult queryAuthResult = wxOpenService.getWxOpenComponentService().getQueryAuth(authorizationCode);
            log.debug("收到授权回调,授权信息:{}", JSONUtil.toJsonStr(queryAuthResult));
            // 存储授权信息
            wxOpenService.getNgpWxMapService().auth(queryAuthResult);
        } catch (Exception e) {
            errorMsg = URLUtil.encode(e.getMessage());
        }
        // 打开授权成功的界面
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setView(new RedirectView(StrUtil.format(AUTH_CALLBACK_PAGE, CurrentUser.getProfileId(), errorMsg), true, false));
        return modelAndView;
    }

}
