package com.wsgjp.ct.sale.web.shopsale;

import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.PlatformInfoQueryParam;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.YunLiTemplate;
import com.wsgjp.ct.sale.biz.shopsale.service.WmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: sale
 * @author: tanglan
 * @create: 2023/10/29
 * @description: 三方运力
 **/
@Api(value = "${app.id}/shopsale/yunli", tags = {"三方配送"})
@RestController
@RequestMapping("${app.id}/shopsale/yunli")
public class YunliController {
    @Autowired
    private WmsService wmsService;

    @ApiOperation(value = "获取运力列表")
    @PostMapping("/getPlatformTemplateList")
    public List<YunLiTemplate> getPlatformTemplateList(@RequestBody Map param) {
        return wmsService.getPlatformTemplateList(param);
    }

    @ApiOperation(value = "保存运力")
    @PostMapping("/doSaveYunLiTemplate")
    void doSaveYunLiTemplate(@RequestBody YunLiTemplate param) {
        wmsService.doSaveYunLiTemplate(param);
    }

    @ApiOperation(value = "确认运力")
    @PostMapping("/doSubmitYunLiTemplate")
    void doSubmitYunLiTemplate(@RequestBody PlatformInfoQueryParam param) {
        wmsService.doSubmitYunLiTemplate(param);
    }


    @ApiOperation(value = "取消运力")
    @PostMapping("/cancelYunLi")
    void cancelYunLi(@RequestBody List<PlatformInfoQueryParam> param) {
        wmsService.cancelYunLi(param);
    }

    @ApiOperation(value = "查询运力状态")
    @PostMapping("/queryYunLiStatus")
    void queryYunLiStatus(@RequestBody PlatformInfoQueryParam param) {
        wmsService.queryYunLiStatus(param);
    }

    @ApiOperation(value = "追加保存小费")
    @PostMapping("/addYunLiPrice")
    void addYunLiPrice(@RequestBody List<PlatformInfoQueryParam> param) {
        wmsService.addYunLiPrice(param);
    }

    @ApiOperation(value = "查询取消原因")
    @PostMapping("/queryYunLiCancelReason")
    Map<Integer, String> queryYunLiCancelReason() {
        return wmsService.queryYunLiCancelReason(new ArrayList());
    }


}
