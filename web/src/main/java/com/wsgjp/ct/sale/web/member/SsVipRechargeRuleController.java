package com.wsgjp.ct.sale.web.member;

import com.wsgjp.ct.sale.biz.member.model.dto.recharge.RechargeGivingVo;
import com.wsgjp.ct.sale.biz.member.service.ISsVipRechargeRuleService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @program: member
 * @description:
 * @author: lxg
 * @create: 2022-04-08 16:30
 */
@ApiModel("会员充值规则相关")
@RequestMapping("${app.id}/member/vipRechargeRule")
@RestController
public class SsVipRechargeRuleController {

    @Autowired
    private ISsVipRechargeRuleService rechargeRule;


    @PostMapping("/getRechargeRule")
    @ApiOperation(value = "获取充值赠送规则")
    public RechargeGivingVo getRechargeRule(){
      return rechargeRule.getRechargeRule(CurrentUser.getProfileId());
    }


}
