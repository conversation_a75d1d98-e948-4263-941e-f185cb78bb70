package com.wsgjp.ct.sale.web.member;

import bf.datasource.page.PageRequest;
import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.page.PageSummary;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.model.dto.recharge.*;
import com.wsgjp.ct.sale.biz.member.service.ISsVipRechargeRecordService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipRechargeService;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PayResultInfo;
import com.wsgjp.ct.sis.client.common.CurrentUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;


@ApiModel("会员充值活动")
@RequestMapping("${app.id}/member/vipRecharge")
@RestController
public class SsVipRechargeController {

    @Autowired
    private ISsVipRechargeService rechargeService;

    @Autowired
    private ISsVipRechargeRecordService rechargeRecordService;

    @PostMapping("/getRecharge")
    @ApiOperation(value = "获取充值赠送规则")
    public RechargeDTO getRecharge(@RequestBody BigInteger rechargeId) {
        return rechargeService.getRechargeById(rechargeId);
    }

    @PostMapping("/saveOrUpdateRecharge")
    @ApiOperation(value = "新增或修改充值活动")
    public void saveOrUpdateRecharge(@RequestBody RechargeDTO dto) throws Exception {
        rechargeService.saveOrUpdateRecharge(dto);
    }

    @PostMapping("/getRechargeList")
    @ApiOperation(value = "充值规则列表")
    @WebLogs
    public PageInfo<RechargeDTO> getRechargeList(@RequestBody PageRequest<GetRechargeListRequestDTO> request) {
        return rechargeService.getRechargeListWithLevelNamesAndOtypeNames(request);
    }

    @PostMapping("/changeRechargeStoped")
    @ApiOperation(value = "启用/停用")
    public void changeRechargeStoped(@RequestBody BigInteger rechargeId) {
        rechargeService.changeRechargeStoped(rechargeId);
    }

    @PostMapping("/recharge")
    @ApiOperation(value = "会员充值")
    @WebLogs
    public String recharge(@RequestBody VipRechargeDTO request) {
        return rechargeService.recharge(request);
    }

    @PostMapping("/submitRechargeBill")
    @ApiOperation(value = "会员充值生成收款单-草稿")
    @WebLogs
    public void submitRechargeBill(@RequestBody VipRechargeDTO request) {
        rechargeService.submitRechargeBill(request);
    }

    @PostMapping("/batchRecharge")
    @ApiOperation(value = "批量会员充值")
    @WebLogs
    public void batchRecharge(@RequestBody VipBatchRechargeDTO request) {
        rechargeService.batchRecharge(request);
    }

    @PostMapping("/rechargeRefund")
    @ApiOperation(value = "充值退款")
    public void rechargeRefund(@RequestBody RechargeRefundDTO request) {
        request.setCreateEtypeId(CurrentUser.getEmployeeId());
        BigInteger vchcode = rechargeService.rechargeRefund(request);
        rechargeService.postBill(vchcode, "", "会员储值退款单据核算失败");
    }

    @ApiOperation(value = "根据会员信息获取可用的充值活动")
    @PostMapping("/getRechargeListByVip")
    @WebLogs
    public List<RechargeDTO> getRechargeListByVip(@RequestBody GetRechargeByVipDTO request) {
        return rechargeService.getRechargeListByVip(request);
    }

    @ApiOperation(value = "获取会员充值记录")
    @PostMapping("/getRechargeRecordList")
    @WebLogs
    public PageInfo<RechargeRecordDTO> getRechargeRecordList(@RequestBody GetRechargeRecordRequestDTO request) {
        return rechargeRecordService.getRechargeRecordList(request);
    }

    @ApiOperation(value = "获取会员充值记录详情")
    @PostMapping("/getRechargeRecordDetail")
    @WebLogs
    public RechargeRecordDetailDTO getRechargeRecordDetail(@RequestBody BigInteger recordId) {
        return rechargeRecordService.getRechargeRecordDetail(recordId);
    }

    @ApiOperation(value = "会员储值作废")
    @PostMapping("/invalidateRechargeRecord")
    @WebLogs
    public void invalidateRechargeRecord(@RequestBody InvalidateRechargeRequestDTO request) {
        rechargeService.invalidateRechargeRecord(request);
    }

    @ApiOperation(value = "储值记录-PC")
    @PostMapping("/getRechargeRecordReport")
    @WebLogs
    public PageInfo<RechargeRecordReport> getRechargeRecordReport(@RequestBody RechargeRecordRequest request) {
        return rechargeRecordService.getRechargeRecordReport(request);
    }

    @ApiOperation(value = "储值记录合计-PC")
    @PostMapping("/getRechargeRecordReportSum")
    @WebLogs
    public PageSummary getRechargeRecordReportSum(@RequestBody RechargeRecordRequest request) {
        return rechargeRecordService.getRechargeRecordReportSum(request);
    }

    @ApiOperation(value = "会员储值聚合支付")
    @PostMapping("/rechargePayOrderAndSaveBill")
    @WebLogs
    public PayResultInfo rechargePayOrderAndSaveBill(@RequestBody VipRechargePaymentRequest request) {
        return rechargeService.rechargePayOrderAndSaveBill(request);
    }

    @ApiOperation(value = "会员储值支付确认处理", notes = "用于POS端轮询或手动确认收款时调用，处理资产变动和单据核算")
    @PostMapping("/confirmRechargePayment")
    @WebLogs
    public void confirmRechargePayment(@RequestBody VipRechargePaymentRequest request) {
        rechargeService.confirmRechargePayment(request);
    }


}
