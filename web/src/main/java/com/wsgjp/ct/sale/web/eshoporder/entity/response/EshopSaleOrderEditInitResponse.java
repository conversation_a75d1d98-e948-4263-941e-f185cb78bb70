package com.wsgjp.ct.sale.web.eshoporder.entity.response;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EnumState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.SaleOrderPriceControl;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EnumStateService;
import com.wsgjp.ct.sale.web.eshoporder.entity.enums.PageMode;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.IndustryConfig;
import com.wsgjp.ct.support.global.entity.SysGlobalConfig;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/12 0012 16:57
 */
public class EshopSaleOrderEditInitResponse {
    public EshopSaleOrderEditInitResponse(PageMode mode){
        IndustryConfig industryConfig = GlobalConfig.get(IndustryConfig.class);
        SysGlobalConfig globalConfig = GlobalConfig.get(SysGlobalConfig.class);
        enabledProps = industryConfig.isEnabledProps();
        enabledBatch = industryConfig.isEnabledBatch();
        enabledTax = globalConfig.isEnabledTax();
        this.mode=mode;
        this.flags=EnumStateService.getFlags();
        this.payType=EnumStateService.getEnumState("payType");
    }

    private SaleOrderPriceControl priceControl;
    private final Boolean enabledBatch;
    private final Boolean enabledProps;
    private Boolean enabledTax;
    private EshopSaleOrderEntity saleOrderEntity;
    private String title;
    private final PageMode mode;
    private Boolean hasPostBill;
    private List<EnumState> flags;
    private List<EnumState> payType;
    private BigInteger employeeId;

    public BigInteger getEmployeeId() {
        return CurrentUser.getEmployeeId();
    }

    public void setEmployeeId(BigInteger employeeId) {
        this.employeeId = employeeId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public PageMode getMode() {
        return mode;
    }

    private List<EnumState> deliverTypeList;

    public List<EnumState> getDeliverTypeList() {
        return EnumStateService.getEnumState("deliverTypeSource");
    }

    public Boolean getEnabledProps() {
        return enabledProps;
    }

    public Boolean getEnabledBatch() {
        return enabledBatch;
    }

    public EshopSaleOrderEntity getSaleOrderEntity() {
        return saleOrderEntity;
    }

    public void setSaleOrderEntity(EshopSaleOrderEntity saleOrderEntity) {
        this.saleOrderEntity = saleOrderEntity;
    }

    public Boolean getEnabledTax() {
        return enabledTax;
    }

    public void setEnabledTax(Boolean enabledTax) {
        this.enabledTax = enabledTax;
    }

    public SaleOrderPriceControl getPriceControl() {
        return priceControl;
    }

    public void setPriceControl(SaleOrderPriceControl priceControl) {
        this.priceControl = priceControl;
    }

    public Boolean getHasPostBill() {
        if(hasPostBill==null){
            return false;
        }
        return hasPostBill;
    }

    public void setHasPostBill(Boolean hasPostBill) {
        this.hasPostBill = hasPostBill;
    }

    public List<EnumState> getFlags() {
        return flags;
    }

    public void setFlags(List<EnumState> flags) {
        this.flags = flags;
    }

    public List<EnumState> getPayType() {
        return payType;
    }

    public void setPayType(List<EnumState> payType) {
        this.payType = payType;
    }
}
