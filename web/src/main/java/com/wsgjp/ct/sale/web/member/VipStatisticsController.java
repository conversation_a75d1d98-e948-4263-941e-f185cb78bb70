package com.wsgjp.ct.sale.web.member;

import bf.datasource.page.PageRequest;
import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.sale.biz.member.model.dto.statistics.GetAssertsChangeRecordRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.statistics.GetScoreTrendRequest;
import com.wsgjp.ct.sale.biz.member.model.entity.statistics.*;
import com.wsgjp.ct.sale.biz.member.service.VipStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "会员统计")
@RestController
@RequestMapping("${app.id}/member/vipStatistics")
public class VipStatisticsController {

    @Autowired
    private VipStatisticsService vipStatisticsService;

    @ApiOperation("获取储值变动记录")
    @PostMapping("/getStoredValueChangeRecord")
    public PageInfo<AssertsChangeRecord> getStoredValueChangeRecord(@RequestBody PageRequest<GetAssertsChangeRecordRequest> request) {
        return vipStatisticsService.getStoredValueChangeRecord(request);
    }

    @ApiOperation("获取积分变动记录")
    @PostMapping("/getScoreChangeRecord")
    public PageInfo<AssertsChangeRecord> getScoreChangeRecord(@RequestBody PageRequest<GetAssertsChangeRecordRequest> request) {
        return vipStatisticsService.getScoreChangeRecord(request);
    }

    @ApiOperation("获取积分分析数据概览")
    @PostMapping("/getScoreDataOverview")
    public ScoreDataOverview getScoreDataOverview() {
        return vipStatisticsService.getScoreDataOverview();
    }

    @ApiOperation("获取积分趋势")
    @PostMapping("/getScoreTrend")
    public List<ScoreTrend> getScoreTrend(@RequestBody GetScoreTrendRequest request) {
        return vipStatisticsService.getScoreTrend(request);
    }

    @ApiOperation("获取可用积分分布")
    @PostMapping("/getAvailableScoreDistribution")
    public List<AvailableScoreDistribution> getAvailableScoreDistribution() {
        return vipStatisticsService.getAvailableScoreDistribution();
    }

    @ApiOperation("获取积分累计获取分布")
    @PostMapping("/getAccumulateScoreDistribution")
    public List<ScoreDistribution> getAccumulateScoreDistribution() {
        return vipStatisticsService.getAccumulateScoreDistribution();
    }

    @ApiOperation("获取积分累计消耗分布")
    @PostMapping("/getPayScoreCountDistribution")
    public List<ScoreDistribution> getPayScoreCountDistribution() {
        return vipStatisticsService.getPayScoreCountDistribution();
    }

    @ApiOperation("获取会员等级积分分布")
    @PostMapping("/getLevelScoreDistribution")
    public List<LevelScoreDistribution> getLevelScoreDistribution() {
        return vipStatisticsService.getLevelScoreDistribution();
    }

    @ApiOperation("获取当前储值状况")
    @PostMapping("/getCurrentStoredValueDistribution")
    public CurrentStoredValueDistribution getCurrentStoredValueDistribution() {
        return vipStatisticsService.getCurrentStoredValueDistribution();
    }

    @ApiOperation("获取储值数据概览")
    @PostMapping("/getStoredValueDataOverview")
    public StoredValueDataOverview getStoredValueDataOverview(@RequestBody GetAssertsChangeRecordRequest params) {
        return vipStatisticsService.getStoredValueDataOverview(params);
    }

    @ApiOperation("获取储值趋势")
    @PostMapping("/getStoredValueTrend")
    public List<StoredValueTrend> getStoredValueTrend(@RequestBody GetAssertsChangeRecordRequest params) {
        return vipStatisticsService.getStoredValueTrend(params);
    }

    @ApiOperation("获取单次储值金额分布")
    @PostMapping("/getSingleStoredValueDistribution")
    public List<StoredValueDistribution> getSingleStoredValueDistribution(@RequestBody GetAssertsChangeRecordRequest params) {
        return vipStatisticsService.getSingleStoredValueDistribution(params);
    }

    @ApiOperation("获取累计储值金额分布")
    @PostMapping("/getAccumulateTotalDistribution")
    public List<StoredValueDistribution> getAccumulateTotalDistribution(@RequestBody GetAssertsChangeRecordRequest params) {
        return vipStatisticsService.getAccumulateTotalDistribution(params);
    }

    @ApiOperation("获取充值次数分布")
    @PostMapping("/getRechargeCountDistribution")
    public List<StoredValueDistribution> getRechargeCountDistribution(@RequestBody GetAssertsChangeRecordRequest params) {
        return vipStatisticsService.getRechargeCountDistribution(params);
    }
}
