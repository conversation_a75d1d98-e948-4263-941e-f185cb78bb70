package com.wsgjp.ct.sale.web.shopsale;


import bf.datasource.page.PageRequest;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.cashbox.CashBoxPaymentDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.cashbox.CashBoxPaymentStatisticsDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.cashbox.CashBoxResponsePage;
import com.wsgjp.ct.sale.biz.shopsale.service.CashBoxService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ngp.monitor.annontaion.NgpResource;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 钱箱存取
 */
@Api(value = "${app.id}/shopsale/cashbox", tags = "钱箱存取相关")
@RestController
@RequestMapping("${app.id}/shopsale/cashbox")
public class CashBoxController {

    private final CashBoxService cashBoxService;

    public CashBoxController(CashBoxService cashBoxService) {
        this.cashBoxService = cashBoxService;
    }

    @ApiOperation(value = "插入一条钱箱存取数据")
    @ApiParam(name = "payment", value = "请求参数", required = true)
    @NgpResource(name = "shopsale.insertPayment", tagStrings = "'tagA,'+0")
    @PostMapping("/insertPayment")
    @WebLogs
    public boolean insertPayment(@RequestBody CashBoxPaymentDTO payment) {
        return cashBoxService.insertPayment(payment);
    }


    @ApiOperation(value = "查询某个时段内钱箱的存取统计")
    @ApiParam(name = "paymentDTO", value = "查询参数", required = true)
    @GetMapping("/getPaymentStatistics")
    public CashBoxPaymentStatisticsDTO getPaymentStatistics(@RequestBody CashBoxPaymentDTO paymentDTO) {
        PageRequest<CashBoxPaymentDTO> pageRequest = new PageRequest<>();
        paymentDTO.setProfileId(CurrentUser.getProfileId());
        pageRequest.setQueryParams(paymentDTO);
        pageRequest.setPageIndex(paymentDTO.getPage());
        pageRequest.setPageSize(paymentDTO.getPageSize());
        return cashBoxService.getPaymentStatistics(pageRequest);
    }

    @ApiOperation(value = "分页查询钱箱收支记录表")
    @ApiParam(name = "paymentDTO", value = "查询参数", required = true)
    @PostMapping("/getCashBoxRecordList")
    @NgpResource(name = "shopsale.getCashBoxRecordList", tagStrings = "'tagA,'+0")
    @PermissionCheck(key = PermissionShopSale.SHOPSALE_CASHBOXRECORD_VIEW)
    public CashBoxResponsePage getCashBoxRecordList(@RequestBody PageRequest<CashBoxPaymentDTO> paymentDTO) {
        CashBoxPaymentDTO cashBoxPaymentDTO = paymentDTO.getQueryParams();
        cashBoxPaymentDTO.setProfileId(CurrentUser.getProfileId());
        return cashBoxService.getCashBoxRecordList(paymentDTO);
    }


    @ApiOperation(value = "查询钱箱收支记录表合计")
    @PostMapping("/getCashBoxRecordList/count")
    public Map getCashBoxRecordListSummary(@RequestBody CashBoxPaymentDTO paymentDTO) {
        return cashBoxService.getCashBoxRecordListCount(paymentDTO);
    }

}
