package com.wsgjp.ct.sale.web.eshoporder.entity.response;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EnumState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;

import java.util.List;

/**
 * <AUTHOR>
 * @date 8/6/2021 上午 9:50
 */
public class StockSyncPageInitData {
	private List<EnumState> ruleTypeList;
	private List<Stock> ktypeSource;
	private List<Integer> warehouseShopTypes;
	private List<EnumState> stockSceneTypeList;
	private List<EnumState> syncQtyTargetTypeList;
	private List<EnumState> stockMinLimitTypeList;
	private List<Integer> syncTimeTypes;


	public List<EnumState> getRuleTypeList() {
		return ruleTypeList;
	}

	public void setRuleTypeList(List<EnumState> ruleTypeList) {
		this.ruleTypeList = ruleTypeList;
	}

	public List<Stock> getKtypeSource() {
		return ktypeSource;
	}

	public void setKtypeSource(List<Stock> ktypeSource) {
		this.ktypeSource = ktypeSource;
	}

	public List<Integer> getWarehouseShopTypes() {
		return warehouseShopTypes;
	}

	public void setWarehouseShopTypes(List<Integer> warehouseShopTypes) {
		this.warehouseShopTypes = warehouseShopTypes;
	}

	public List<EnumState> getStockSceneTypeList() {
		return stockSceneTypeList;
	}

	public void setStockSceneTypeList(List<EnumState> stockSceneTypeList) {
		this.stockSceneTypeList = stockSceneTypeList;
	}

	public List<EnumState> getSyncQtyTargetTypeList() {
		return syncQtyTargetTypeList;
	}

	public void setSyncQtyTargetTypeList(List<EnumState> syncQtyTargetTypeList) {
		this.syncQtyTargetTypeList = syncQtyTargetTypeList;
	}
	public List<EnumState> getStockMinLimitTypeList() {
		return stockMinLimitTypeList;
	}

	public void setStockMinLimitTypeList(List<EnumState> stockMinLimitTypeList) {
		this.stockMinLimitTypeList = stockMinLimitTypeList;
	}

	public List<Integer> getSyncTimeTypes() {
		return syncTimeTypes;
	}

	public void setSyncTimeTypes(List<Integer> syncTimeTypes) {
		this.syncTimeTypes = syncTimeTypes;
	}
}
