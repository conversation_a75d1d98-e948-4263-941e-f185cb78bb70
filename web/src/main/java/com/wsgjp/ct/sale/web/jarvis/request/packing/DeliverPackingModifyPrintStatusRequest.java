package com.wsgjp.ct.sale.web.jarvis.request.packing;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DeliverPackingModifyPrintStatusRequest {
    private List<BigInteger> packingIds;
    private List<BigInteger> vchcodes;

    private int type;
    private boolean status;
    private boolean updateBill;

    public boolean isUpdateBill() {
        return updateBill;
    }

    public void setUpdateBill(boolean updateBill) {
        this.updateBill = updateBill;
    }

    public List<BigInteger> getPackingIds() {
        return packingIds;
    }

    public void setPackingIds(List<BigInteger> packingIds) {
        this.packingIds = packingIds;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public List<BigInteger> getVchcodes() {
        return vchcodes;
    }

    public void setVchcodes(List<BigInteger> vchcodes) {
        this.vchcodes = vchcodes;
    }
}
