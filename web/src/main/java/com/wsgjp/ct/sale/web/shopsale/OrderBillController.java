package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.bill.model.entity.BillLoadRequest;
import com.wsgjp.ct.sale.biz.jarvis.common.BillLog;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.*;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.log.SaleBillLogQueryParams;
import com.wsgjp.ct.sale.biz.shopsale.service.OrderBillService;
import com.wsgjp.ct.support.log.service.LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(value = "${app.id}/shopsale/orderbill", tags = {"订单类单据"})
@RestController
@RequestMapping("${app.id}/shopsale/orderbill")

public class OrderBillController {
    OrderBillService billService;

    OrderBillController(OrderBillService billService) {
        this.billService = billService;
    }


    @ApiOperation(value = "加载订单/拉取新订单")
    @PostMapping("/getbill")
    @WebLogs
    public OrderBillDTO getBill(@RequestBody BillLoadRequest requestParams) {
        return billService.getOrderBill(requestParams);
    }

    @ApiOperation(value = "提交订单")
    @PostMapping("/submitBill")
    @WebLogs
    public BillSaveResultDTO submitBill(@RequestBody OrderBillDTO requestParams) {
        return billService.submitBill(requestParams);
    }

    @ApiOperation(value = "取单")
    @PostMapping("/orderBillCore/list")
    public PageResponse<Map> orderBillCoreList(@RequestBody PageRequest<Map> requestParams) {
        return billService.orderBillCoreList(requestParams);
    }

    @ApiOperation(value = "删除订单")
    @PostMapping("/delete")
    @WebLogs
    public OperationBillResponse deleteOrderBill(@RequestBody DeleteOrderBillRequest requestParams) {
        return billService.deleteOrderBill(requestParams);
    }


    /**
     * {
     * "pageIndex": 1,
     * "pageSize": 20,
     * "queryParams": {
     * "startTime": "2022-12-01 00:00:00",
     * "endTime": "2022-12-06 00:00:00",
     * "billNumber": "001",//非必填
     * "vchtypes": [//非必填
     * 2100,
     * 2000
     * ]
     * }
     * }
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取待出入库 已出入库 核酸完成的所有单据 pos端使用")
    @PostMapping("/getAllBillList")
    @WebLogs
    public PageResponse<BillCoreALLDTO> getAllBillList(@RequestBody PageRequest<OrderListRequestDTO> request) {
        return billService.getAllBillList(request);
    }

    @ApiOperation(value = "查看单据日志列表")
    @PostMapping(value = "/loadBillLogList")
    public PageResponse<BillLog> loadBillLogList(@RequestBody PageRequest<SaleBillLogQueryParams> request) {
        PageResponse<BillLog> query = LogService.query(request);
        return query;
    }

    @ApiOperation(value = "获取调拨订单")
    @PostMapping(value = "/getTransferOrderList")
    @WebLogs
    public PageResponse<TransferOrderDTO> getTransferOrderList(@RequestBody PageRequest<TransferOrderListRequestDTO> request) {
        return billService.getTransferOrderList(request);
    }

    @ApiOperation(value = "校验订单是否满足生单条件(明细生单)")
    @PostMapping("/checkBillByDetail")
    @WebLogs
    public void checkBillByDetail(@RequestBody CheckDetailBillRequest checkBillRequest) {
        billService.checkBillByDetail(checkBillRequest);
    }

    @ApiOperation(value = "勾选订单明细生成单据")
    @PostMapping("/selectDetailCreateBill")
    @WebLogs
    public CreteBillResponse selectDetailCreateBill(@RequestBody SelectDetailCreateBillRequest request) {
        return billService.selectDetailCreateBill(request);
    }

}