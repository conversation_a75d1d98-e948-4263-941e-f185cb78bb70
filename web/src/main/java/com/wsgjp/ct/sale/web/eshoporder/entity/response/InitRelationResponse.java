package com.wsgjp.ct.sale.web.eshoporder.entity.response;

/**
 * <AUTHOR>
 * @date 20/8/2020 下午 1:41
 */
public class InitRelationResponse {

	private Boolean enabledProps;
	private String hashKeys;

	/**
	 *账套是否开启 如果网店商品相关的所有订单，都是退款的(整单所有明细)、取消、交易关闭，则该网店商品不需要插入快速对应
	 */
	private boolean insertUnRelationByNormalOrder;

	public boolean getInsertUnRelationByNormalOrder() {
		return insertUnRelationByNormalOrder;
	}

	public void setInsertUnRelationByNormalOrder(boolean insertUnRelationByNormalOrder) {
		this.insertUnRelationByNormalOrder = insertUnRelationByNormalOrder;
	}

	public Boolean getEnabledProps() {
		return enabledProps;
	}

	public void setEnabledProps(Boolean enabledProps) {
		this.enabledProps = enabledProps;
	}

	public String getHashKeys() {
		return hashKeys;
	}

	public void setHashKeys(String hashKeys) {
		this.hashKeys = hashKeys;
	}
}
