package com.wsgjp.ct.sale.web.jarvis.response.packing;

import com.wsgjp.ct.sale.biz.jarvis.dto.Dtype;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DeliverPackingBillSelectInitResponse {
    private List<Employee> employees;
    private List<Stock> ktypeList;
    private List<Freight> freightList;
    private List<Organization> organizationList;
    private List<Btype> btypeList;
    private List<Dtype> dtypeList;

    public List<Employee> getEmployees() {
        return employees;
    }

    public void setEmployees(List<Employee> employees) {
        this.employees = employees;
    }

    public List<Stock> getKtypeList() {
        return ktypeList;
    }

    public void setKtypeList(List<Stock> ktypeList) {
        this.ktypeList = ktypeList;
    }

    public List<Freight> getFreightList() {
        return freightList;
    }

    public void setFreightList(List<Freight> freightList) {
        this.freightList = freightList;
    }

    public List<Organization> getOrganizationList() {
        return organizationList;
    }

    public void setOrganizationList(List<Organization> organizationList) {
        this.organizationList = organizationList;
    }

    public List<Btype> getBtypeList() {
        return btypeList;
    }

    public void setBtypeList(List<Btype> btypeList) {
        this.btypeList = btypeList;
    }

    public List<Dtype> getDtypeList() {
        return dtypeList;
    }

    public void setDtypeList(List<Dtype> dtypeList) {
        this.dtypeList = dtypeList;
    }
}
