package com.wsgjp.ct.sale.web.wx;


import com.wsgjp.ct.sale.biz.wx.entity.SendWxMpTemplateMessageDto;
import com.wsgjp.ct.sale.biz.wx.entity.SsWxVipCardTemplate;
import com.wsgjp.ct.sale.biz.wx.entity.WxCard;
import com.wsgjp.ct.sale.biz.wx.entity.WxMpAuthStatus;
import com.wsgjp.ct.sale.biz.wx.mapper.OpsCpaMapper;
import com.wsgjp.ct.sale.biz.wx.service.NgpWxOpenService;
import com.wsgjp.ct.sale.biz.wx.utils.AssertUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ngp.starter.web.exception.RestException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@RestController
@Slf4j
@RequestMapping(value = "/${app.id}/wxopen/mpCtl")
@Api(tags = "微信开放平台公众号接口")
public class WechatOpenMpController {

    private final NgpWxOpenService wxOpenService;

    private final OpsCpaMapper opsCpaMapper;

    @ApiOperation(value = "公众号授权状态")
    @PostMapping("/getAuthInfo")
    public WxMpAuthStatus getAuthorizerInfo() throws Exception {
        return wxOpenService.getNgpWxMapService().getMpAuthStatus();
    }

    @ApiOperation(value = "取消公众号授权")
    @PostMapping(value = "/unAuth")
    public void unAuth() {
        wxOpenService.unMpAuthorize(null);
    }


    @ApiOperation(value = "设置固定行业")
    @PostMapping(value = "/setIndustry")
    public void setIndustry() throws Exception {
        wxOpenService.getNgpWxMapService().setIndustry();
    }


    @ApiOperation(value = "获取绑定客户微信二维码")
    @PostMapping("/getBindCustomerWxQrCode")
    public String getBindCustomerWxQrCode(@RequestBody QrCodeRequestDto requestDto) throws Exception {
        if (requestDto == null || requestDto.getCpaId() == null) {
            throw new RestException("产品账号id不能为空");
        }
        BigInteger customerId = opsCpaMapper.getCustomerIdById(requestDto.getCpaId(), CurrentUser.getProfileId());
        AssertUtil.nullTrw(customerId, "客户不存在");
        return wxOpenService.getNgpWxMapService().getBindCustomerWxQrCode(customerId);
    }


    @ApiOperation(value = "清空绑定关系并生成微信二维码")
    @PostMapping(value = "/resetAndGetBindWxQrCode")
    public String resetAndGetBindWxQrCode(@RequestBody QrCodeRequestDto requestDto) throws Exception {
        if (requestDto == null || requestDto.getCpaId() == null) {
            throw new RestException("产品账号id不能为空");
        }
        BigInteger customerId = opsCpaMapper.getCustomerIdById(requestDto.getCpaId(), CurrentUser.getProfileId());
        AssertUtil.nullTrw(customerId, "客户不存在");
        return wxOpenService.getNgpWxMapService().resetAndGetBindWxQrCode(customerId);
    }

    @ApiOperation(value = "上传微信会员卡logo")
    @PostMapping(value = "/uploadLogo")
    public String UploadLogo(MultipartFile logo) throws Exception {
        return wxOpenService.getNgpWxMapService().mediaImgUpload(logo);
    }

    @ApiOperation(value = "创建微信会员卡模板")
    @PostMapping(value = "/createTemplate")
    public void createTemplate(@RequestBody SsWxVipCardTemplate template) throws Exception {
        wxOpenService.getNgpWxMapService().createTemplate(template);
    }

    @ApiOperation(value = "修改微信会员卡模板")
    @PostMapping(value = "/updateTemplate")
    public void updateTemplate(@RequestBody SsWxVipCardTemplate template) throws Exception {
        wxOpenService.getNgpWxMapService().updateTemplate(template);
    }

    @ApiOperation(value = "删除微信会员卡模板")
    @PostMapping(value = "/deleteTemplate")
    public void deleteTemplate(@RequestBody SsWxVipCardTemplate template) throws Exception {
        wxOpenService.getNgpWxMapService().deleteTemplate(template);
    }

    @ApiOperation(value = "获取微信会员卡模板二维码")
    @PostMapping(value = "/createQrcode")
    public String createQrcode(@RequestBody String wxCardId) throws Exception {
        return wxOpenService.getNgpWxMapService().createQrcode(wxCardId);
    }

    @ApiOperation(value = "获取单个卡券信息")
    @PostMapping(value = "/getCard")
    public WxCard getCard(@RequestBody String wxCardId) throws Exception {
        return wxOpenService.getNgpWxMapService().getCard(wxCardId);
    }

    @ApiOperation(value = "刷新卡券审核状态")
    @PostMapping(value = "/refreshAuditState")
    public SsWxVipCardTemplate refreshAuditState(@RequestBody String wxCardId) throws Exception {
        return wxOpenService.getNgpWxMapService().refreshAuditState(wxCardId);
    }

    @RequestMapping(value = "/test", method = RequestMethod.POST)
    public void test(@RequestBody SendWxMpTemplateMessageDto dto) throws Exception {

        wxOpenService.getNgpWxMapService().sendTemplateMsg(dto);
    }


    static class QrCodeRequestDto {

        @ApiModelProperty(value = "产品账号id")
        private BigInteger cpaId;

        public BigInteger getCpaId() {
            return cpaId;
        }

        public void setCpaId(BigInteger cpaId) {
            this.cpaId = cpaId;
        }
    }
}
