package com.wsgjp.ct.sale.web.member;

import cn.hutool.core.collection.CollUtil;
import com.wsgjp.ct.pm.enums.SysProfileModelKeyEnum;
import com.wsgjp.ct.pm.service.SysProfileModelService;
import com.wsgjp.ct.sale.biz.member.common.MemberSmsVariableEnum;
import com.wsgjp.ct.sale.biz.member.mapper.SsVipMapper;
import com.wsgjp.ct.sale.biz.member.model.dto.sms.*;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.MemberSmsTemplateParam;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.PosBuyer;
import com.wsgjp.ct.sale.biz.shopsale.service.PosSisClientService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "短信发送接口")
@RestController
@RequestMapping("${app.id}/member/sms")
public class SmsSendController {

    private static final Logger logger = LoggerFactory.getLogger(SmsSendController.class);

    @Autowired
    private SsVipMapper ssVipMapper;

    @Autowired
    private PosSisClientService sisClientService;

    @ApiOperation("是否可用,新增模版的时候，场景下拉列表是否可见， 对接方自行实现")
    @PostMapping("/isAvailable")
    public Boolean isAvailable() {
        // 先看云零售的资产，等会员资产独立后再改
        return SysProfileModelService.checkCurrentEnabledByKeyEnum(SysProfileModelKeyEnum.StoreFunc);
    }

    @ApiOperation("获取模板变量列表")
    @PostMapping("/loadVariable")
    public List<SmsVariableNameDto> loadVariable() {
        return Arrays.stream(MemberSmsVariableEnum.values())
                .map(s -> new SmsVariableNameDto(s.getVariableParam(), s.getVariable(), s.getVariableName(), s.isCustomerVariable()))
                .collect(Collectors.toList());
    }

    @ApiOperation("获取变量值")
    @PostMapping("/getVariableValue")
    public List<GetVariableValueResult> getVariableValue(@RequestBody GetVariableValueParam param) {
        if (param == null || CollUtil.isEmpty(param.getVariableList()) || CollUtil.isEmpty(param.getResourceIds())) {
            return new ArrayList<>();
        }
        // 查出这些资源id的所有会员信息
        List<MemberSmsTemplateParam> memberSmsTemplateParams = ssVipMapper.getMemberSmsTemplateParams(CurrentUser.getProfileId(), param.getResourceIds());
        // 会员手机号解密处理
        // 如果param中要手机号参数再解密
        if (param.getVariableList().contains("vipPhone")) {
            Map<BigInteger, String> buyerPhoneMap = Optional.ofNullable(sisClientService.batchDecryptBuyers(memberSmsTemplateParams.stream().map(MemberSmsTemplateParam::getBuyerId).collect(Collectors.toList())))
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            PosBuyer::getBuyerId,
                            PosBuyer::getCustomerReceiverPhone,
                            // 处理重复key的情况，如果key重复，用后者
                            (existing, replacement) -> replacement));

            memberSmsTemplateParams.forEach(p ->
                    Optional.ofNullable(buyerPhoneMap.get(p.getBuyerId()))
                            .ifPresent(p::setVipPhone)
            );
        }
        return memberSmsTemplateParams.stream()
                .map(member -> {
                    List<GetVariableValueResult.VariableValueDto> dtoList = param.getVariableList().stream()
                            .map(variable -> MemberSmsVariableEnum.fromVariable(variable)
                                    .map(enumItem -> new GetVariableValueResult.VariableValueDto(
                                            enumItem.getVariableParam(),
                                            enumItem.getValue(member)
                                    ))
                                    .orElse(null)
                            )
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    return new GetVariableValueResult(member.getVipId(), dtoList);
                })
                .collect(Collectors.toList());
    }

    @ApiOperation("通过对应的资源Id批量 获取接受短信的手机号码")
    @PostMapping("/getReceivePhone")
    public List<ResourceIdPhoneDto> getReceivePhone(@RequestBody GetReceivePhoneParam param) {
        List<ResourceIdPhoneDto> phoneByVipIds = ssVipMapper.getPhoneByVipIds(CurrentUser.getProfileId(), param.getResourceIds());
        // 手机号解密
        Map<BigInteger, String> buyerPhoneMap = Optional.ofNullable(sisClientService.batchDecryptBuyers(phoneByVipIds.stream().map(ResourceIdPhoneDto::getBuyerId).collect(Collectors.toList())))
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        PosBuyer::getBuyerId,
                        PosBuyer::getCustomerReceiverPhone,
                        // 处理重复key的情况，如果key重复，用后者
                        (existing, replacement) -> replacement));

        phoneByVipIds.forEach(p ->
                Optional.ofNullable(buyerPhoneMap.get(p.getBuyerId()))
                        .ifPresent(p::setReceiverPhone)
        );
        return phoneByVipIds;
    }
}
