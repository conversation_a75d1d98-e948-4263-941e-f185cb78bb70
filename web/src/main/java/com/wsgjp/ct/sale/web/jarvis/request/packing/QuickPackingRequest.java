package com.wsgjp.ct.sale.web.jarvis.request.packing;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class QuickPackingRequest {
    private BigInteger vchcode;
    private String batchNo;
    private BigInteger etypeId;
    private String etypeName;
    private boolean batch;

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BigInteger getEtypeId() {
        return etypeId;
    }

    public void setEtypeId(BigInteger etypeId) {
        this.etypeId = etypeId;
    }

    public boolean isBatch() {
        return batch;
    }

    public void setBatch(boolean batch) {
        this.batch = batch;
    }

    public String getEtypeName() {
        return etypeName;
    }

    public void setEtypeName(String etypeName) {
        this.etypeName = etypeName;
    }
}
