package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.open.JarvisOpenApi;
import com.wsgjp.ct.sale.biz.jarvis.open.dto.FeedBackDeliverRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> chenSiHu
 * @create 2023-04-11 17:34
 */
@RestController
@RequestMapping("/${app.id}/jarvis/platform")
public class PlatformController {

    private JarvisOpenApi jarvisOpenApi;

    public PlatformController(JarvisOpenApi jarvisOpenApi) {
        this.jarvisOpenApi = jarvisOpenApi;
    }

    @PostMapping("/feedBackDeliver")
    public void feedBackDeliver(@RequestBody List<FeedBackDeliverRequest> list) {
        jarvisOpenApi.feedBackDeliver(list);
    }
}
