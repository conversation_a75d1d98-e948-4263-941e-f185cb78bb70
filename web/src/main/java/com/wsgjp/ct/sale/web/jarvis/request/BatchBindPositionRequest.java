package com.wsgjp.ct.sale.web.jarvis.request;

import com.wsgjp.ct.sale.biz.jarvis.entity.PtypePositionRelation;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BatchBindPositionRequest {

    private BigInteger skuId;
    private BigInteger ktypeId;
    private List<PtypePositionRelation> relations;

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public List<PtypePositionRelation> getRelations() {
        return relations;
    }

    public void setRelations(List<PtypePositionRelation> relations) {
        this.relations = relations;
    }
}
