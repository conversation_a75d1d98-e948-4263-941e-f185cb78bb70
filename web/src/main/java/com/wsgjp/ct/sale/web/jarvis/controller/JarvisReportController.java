package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.jarvis.dto.PlatformProductSaleDetailDto;
import com.wsgjp.ct.sale.biz.jarvis.dto.PtypeSaleStatisticsDto;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.PlatformProductSaleDetailReportQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.PtypeSaleStatisticsRequest;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.JarvisReportService;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.text.ParseException;
import java.util.Collections;
import java.util.List;

@RestController
@Api(value = "jarvis 相关统计报表")
@RequestMapping("/${app.id}/jarvis/report")
public class JarvisReportController {

    @Autowired
    private JarvisReportService jarvisReportService;
    @Autowired
    private BaseInfoService baseInfoService;

    @ApiOperation(value = "网店商品销售统计")
    @PostMapping("/queryPlatformProductSaleDetailReportList")
    public PageResponse<PlatformProductSaleDetailDto> queryPlatformProductSaleDetailReportList(@RequestBody PageRequest<PlatformProductSaleDetailReportQueryParams> queryParams) {
        PubSystemLogService.saveInfo("进入【网店商品销售统计】");
        BigInteger profileId = CurrentUser.getProfileId();
        PlatformProductSaleDetailReportQueryParams queryParams1 = queryParams.getQueryParams();
        List<Organization> eshops = baseInfoService.getEshopOrganizationsLimit(profileId, CurrentUser.getEmployeeId());
        if (eshops.stream().noneMatch(e -> e.getId().equals(queryParams1.getOtypeId()))) {
            return new PageResponse<>(0, 0, 0, Collections.emptyList());
        }
        return jarvisReportService.queryPlatformProductSaleDetailReportList(queryParams);
    }


    @ApiOperation(value = "商品销售对比")
    @PostMapping("/ptypeSaleStatistics")
    public List<PtypeSaleStatisticsDto> ptypeSaleStatistics(@RequestBody PageRequest<PtypeSaleStatisticsRequest> queryParams) throws ParseException {
        return jarvisReportService.ptypeSaleStatistics(queryParams);
    }
}
