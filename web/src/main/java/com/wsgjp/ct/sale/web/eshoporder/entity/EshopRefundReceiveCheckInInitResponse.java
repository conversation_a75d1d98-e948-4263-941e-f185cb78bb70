package com.wsgjp.ct.sale.web.eshoporder.entity;

import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderSysDataConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Stock;
import com.wsgjp.ct.sale.web.eshoporder.entity.enums.PageMode;
import com.wsgjp.ct.sale.web.eshoporder.entity.enums.SaleOrderEditFormCloseType;
import com.wsgjp.ct.support.global.GlobalConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
public class EshopRefundReceiveCheckInInitResponse extends CommonInitResponse {

  public EshopRefundReceiveCheckInInitResponse(PageMode mode) {
    EshopOrderSysDataConfig sysConfig = GlobalConfig.get(EshopOrderSysDataConfig.class);
    int code = sysConfig.getEditFormCloseType();
    closeType = SaleOrderEditFormCloseType.values()[code];
    this.mode = mode;
  }

  private EshopRefundReceiveCheckInEntity checkInEntity;
  private String title;
  private PageMode mode;
  private SaleOrderEditFormCloseType closeType;
  private boolean enableGoodsCheckin=true;
  private boolean strictSno;
  private boolean autoStockInAfterAudited = false;
  private List<Stock> ktypeList;
  private List<Etype> etypeList;

  public void setMode(PageMode mode) {
    this.mode = mode;
  }

  public boolean getAutoStockInAfterAudited() {
    return autoStockInAfterAudited;
  }

  public void setAutoStockInAfterAudited(boolean autoStockInAfterAudited) {
    this.autoStockInAfterAudited = autoStockInAfterAudited;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public PageMode getMode() {
    return mode;
  }


  public SaleOrderEditFormCloseType getCloseType() {
    return closeType;
  }

  public void setCloseType(SaleOrderEditFormCloseType closeType) {
    this.closeType = closeType;
  }

  public EshopRefundReceiveCheckInEntity getCheckInEntity() {
    return checkInEntity;
  }

  public void setCheckInEntity(EshopRefundReceiveCheckInEntity checkInEntity) {
    this.checkInEntity = checkInEntity;
  }
  public boolean isEnableGoodsCheckin() {
    return enableGoodsCheckin;
  }

  public void setEnableGoodsCheckin(boolean enableGoodsCheckin) {
    this.enableGoodsCheckin = enableGoodsCheckin;
  }

  public void setStrictSno(boolean strictSno) {
    this.strictSno = strictSno;
  }

  public boolean isStrictSno() {
    return strictSno;
  }

  public List<Stock> getKtypeList() {
    return ktypeList;
  }

  public void setKtypeList(List<Stock> ktypeList) {
    this.ktypeList = ktypeList;
  }

  public List<Etype> getEtypeList() {
    return etypeList;
  }

  public void setEtypeList(List<Etype> etypeList) {
    this.etypeList = etypeList;
  }
}
