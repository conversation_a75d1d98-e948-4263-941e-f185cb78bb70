package com.wsgjp.ct.sale.web.eshoporder.aftersale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopRefundService;
import com.wsgjp.ct.sale.biz.eshoporder.api.EshopOrderAutoToolApi;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderToolConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.manage.refund.RefundCheckInMergeManage;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderEshopRefundMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopRefundReceiveCheckInMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.AfterSaleCheckinService;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.AfterSalePageService;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.AfterSaleService;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.RefundBatchOperateResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.balance.BalanceService;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.RefundManager;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.impl.NewRefundServiceImpl;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.UpdateRefundDutyRequest;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.request.AfterSaleToPayRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.request.AfterSaleListQueryRequest;
import io.swagger.annotations.Api;
import ngp.redis.RedisPoolFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "售后管理")
@RequestMapping("${app.id}/eshoporder/aftersale")
@RestController
public class AfrerSaleController {
    private final AfterSalePageService refundService;
    private final EshopService eshopService;
    private final RefundManager manager;
    private final RefundCheckInMergeManage refundCheckInMergeManage;
    private final BalanceService balanceService;
    private final NewRefundServiceImpl newRefundService;
    private final EshopOrderEshopRefundMapper refundMapper;
    private final EshopRefundReceiveCheckInMapper eshopRefundReceiveCheckInMapper;
    private final EshopOrderAutoToolApi ehopOrderAutoToolApi;
    private final EshopOrderToolConfig config;
    private final EshopOrderBaseInfoService baseInfoService;
    private final RedisPoolFactory factory;
    private final BifrostEshopRefundService bifrostEshopRefundService;
    private final AfterSaleCheckinService afterSaleCheckinService;


    private static final Logger logger = LoggerFactory.getLogger(AfrerSaleController.class);
    private final AfterSaleService afterSaleService;

    public AfrerSaleController(AfterSalePageService refundService, EshopService eshopService,
                               RefundManager manager, RefundCheckInMergeManage refundCheckInMergeManage,
                               BalanceService balanceService, NewRefundServiceImpl newRefundService,
                               EshopOrderEshopRefundMapper refundMapper,
                               EshopRefundReceiveCheckInMapper eshopRefundReceiveCheckInMapper,
                               EshopOrderAutoToolApi ehopOrderAutoToolApi, EshopOrderToolConfig config,
                               EshopOrderBaseInfoService baseInfoService, RedisPoolFactory factory,
                               BifrostEshopRefundService bifrostEshopRefundService, AfterSaleCheckinService afterSaleCheckinService, AfterSaleService afterSaleService) {
        this.refundService = refundService;
        this.eshopService = eshopService;
        this.manager = manager;
        this.refundCheckInMergeManage = refundCheckInMergeManage;
        this.balanceService = balanceService;
        this.newRefundService = newRefundService;
        this.refundMapper = refundMapper;
        this.eshopRefundReceiveCheckInMapper = eshopRefundReceiveCheckInMapper;
        this.ehopOrderAutoToolApi = ehopOrderAutoToolApi;
        this.config = config;
        this.baseInfoService = baseInfoService;
        this.factory = factory;
        this.bifrostEshopRefundService = bifrostEshopRefundService;
        this.afterSaleCheckinService = afterSaleCheckinService;
        this.afterSaleService = afterSaleService;
    }

    /**
     * 查询售后单列表
     */
    @PostMapping(value = "/queryAfterSaleList")
    @PermissionCheck(key = PermissionSysConst.ESHOP_REFUND_VIEW)
    public PageResponse<EshopRefundEntity> queryAfterSaleList(@RequestBody PageRequest<AfterSaleListQueryRequest> params) {
        return refundService.queryAfterSaleList(params, true);
    }

    /**
     * 查询售后单数量
     */
    @PostMapping(value = "/queryAfterSaleListCount")
    @PermissionCheck(key = PermissionSysConst.ESHOP_REFUND_VIEW)
    public int queryAfterSaleListCount(@RequestBody PageRequest<AfterSaleListQueryRequest> params) {
        return refundService.queryAfterSaleListCount(params);
    }

    @PostMapping("batchEditRefund")
    public BaseResponse updateRefundDuty(@RequestBody UpdateRefundDutyRequest request) {
//        return refundService.updateRefundDuty(request);
        return null;
    }


    /**
     * 系统入库
     */
    @PostMapping(value = "/afterSaleToCheckin")
    public RefundBatchOperateResponse afterSaleToCheckin(@RequestBody List<EshopRefundEntity> refunds) {
        return afterSaleCheckinService.doCheckin(refunds);
    }

    /**
     * 系统退款
     */
    @PostMapping(value = "/afterSaleToPay")
    public RefundBatchOperateResponse afterSaleToPay(@RequestBody AfterSaleToPayRequest request) {
        return afterSaleService.afterSaleToPay(request);
    }

}
