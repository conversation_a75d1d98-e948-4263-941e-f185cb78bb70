package com.wsgjp.ct.sale.web.shopsale;

import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.BillSaveResultDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsBillDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.promotion.ExecutePromotionResultDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionInfo;
import com.wsgjp.ct.sale.biz.shopsale.service.PromotionBtypeDTO;
import com.wsgjp.ct.sale.biz.shopsale.service.PromotionExecuteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.annontaion.NgpResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "促销执行类")
@RequestMapping("${app.id}/shopsale/promotionExecute")
@RestController
public class PromotionExecuteController {
    @Autowired
    PromotionExecuteService promotionService;

    @ApiOperation(value = "验证使用的促销是否有效")
    @PostMapping(value = "/getValidPromotionId")
    @NgpResource(name = "shopsale.getValidPromotionId", tagStrings = "'tagA,'+0")
    @WebLogs
    List<BigInteger> getValidPromotionId(@RequestBody List<BigInteger> promotionIds) {
        return promotionService.getValidPromotionId(promotionIds);
    }

    @ApiOperation(value = "查询完成促销信息列表")
    @PostMapping(value = "/getFullPromotionList")
    @WebLogs
    List<PromotionInfo> getFullPromotionList(@RequestBody BigInteger otypeId) {
        return promotionService.getFullPromotionList(otypeId);
    }

    @ApiOperation(value = "查询促销信息列表(jxc)")
    @PostMapping(value = "/getFullPromotionListWithIds")
    @WebLogs
    List<PromotionInfo> getFullPromotionListWithIds(@RequestBody List<BigInteger> ids) {
        return promotionService.getFullPromotionListWithIds(ids);
    }

    @ApiOperation(value = "进销存执行促销")
    @PostMapping(value = "/executePromotion")
    public ExecutePromotionResultDTO executePromotion(@RequestBody GoodsBillDTO bill) {
        return promotionService.executePromotion(bill);
    }

    @ApiOperation(value = "促销验证")
    @PostMapping(value = "/checkPromotion")
    BillSaveResultDTO checkPromotion(@RequestBody GoodsBillDTO bill) {
        return promotionService.checkPromotion(bill);
    }

    @ApiOperation(value = "根据往来单位id获取促销列表")
    @PostMapping(value = "/getPromotionListByBtypeId")
    public List<PromotionInfo> getPromotionListByBtypeId(@RequestBody PromotionBtypeDTO bill) {
        return promotionService.getPromotionListByBtypeId(bill);
    }
}
