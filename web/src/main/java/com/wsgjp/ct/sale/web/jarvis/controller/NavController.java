package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.jarvis.config.NavProcessStatus;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverCountResult;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverSyncBillCountResult;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Employee;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Stock;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.BillDeliverSpecialQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.biz.jarvis.state.DeliverProcessStatusEnum;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.redis.factory.CacheType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.redis.RedisPoolFactory;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import ngp.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@Api("导航首页显示数据")
@RequestMapping("/${app.id}/jarvis/nav")
public class NavController {

    private DeliverService deliverService;
    private BaseInfoService baseInfoService;
    private Logger logger =  LoggerFactory.getLogger(NavController.class);
    private final RedisPoolFactory factory;
    private static final String NAV_REDIS_TITLE = "NAV";
    private static final String NAV_REDIS_SEC0NDS_KEY = "nav_redis_seconds";
    private static final String NAV_DATA_BY_LOGO="javis.config.DataByLogo";

    public NavController(DeliverService deliverService, BaseInfoService baseInfoService, RedisPoolFactory factory) {
        this.deliverService = deliverService;
        this.baseInfoService = baseInfoService;
        this.factory=factory;
    }


    @PostMapping("getNavProcessStatus")
    @ApiOperation(value = "获取可自定义流程状态", notes = "获取可自定义流程状态")
    public NavProcessStatus getNavProcessStatus() {
        NavProcessStatus config = (NavProcessStatus) GlobalConfig.get(NavProcessStatus.class, "jarvis.nav");
        return config;
    }
    @PostMapping("getSpecialQueryPayNotSendCount")
    @ApiOperation(value = "获取特殊查询待审核数量", notes = "获取特殊查询待审核数量")
    public DeliverCountResult getSpecialQueryPayNotSendCount(@RequestBody BillDeliverSpecialQueryParams specialQueryParams) {
        List<Stock> stockList = getStockList();
        stockList = stockList == null ? new ArrayList<>() : stockList;
        List<BigInteger> stockIds = stockList.stream().map(Stock::getId).collect(Collectors.toList());

        List<Organization> organizationList = getOrganizationList();
        organizationList = organizationList == null ? new ArrayList<>() : organizationList;
        List<BigInteger> otypeIds = organizationList.stream().map(Organization::getId).collect(Collectors.toList());

        List<Employee> employeeList = getEmployeeList();
        employeeList = employeeList == null ? new ArrayList<>() : employeeList;
        List<BigInteger> etypeIds = employeeList.stream().map(Employee::getId).collect(Collectors.toList());

        DeliverCountResult countResult = new DeliverCountResult();
        DeliverCountResult auditCount = this.deliverService.getSpecialQueryPayNotSendCount(CurrentUser.getProfileId(), stockIds, otypeIds, etypeIds,specialQueryParams);
        countResult.setAuditCount(auditCount.getAuditCount());
        countResult.setAuditMinTime(auditCount.getAuditMinTime());
        countResult.setAuditMaxTime(auditCount.getAuditMaxTime());
        return countResult;
    }

    @PostMapping("getNavBillInfo")
    @ApiOperation(value = "获取导航首页显示数据", notes = "获取导航首页显示数据")
    public DeliverCountResult getNavBillInfo(@RequestBody NavRequest request) {
        DeliverCountResult result=new DeliverCountResult();
        if(null==request){
            return result;
        }
        String key = getRequestRedisKey(request);
        StringRedisTemplate biz = factory.getTemplate(CacheType.BIZ.getKey());
        ValueOperations<String, String> redis = biz.opsForValue();
        if(request.getHandleRefresh()){
            result= deliverCountResultRedisSet(request, redis, key);
            return result;
        }
        String redisValue=redis.get(key);
        if(StringUtils.isEmpty(redisValue)){
            result= deliverCountResultRedisSet(request, redis, key);
            return result;
        }else{
            try{
                result= JsonUtils.toObject(redisValue,DeliverCountResult.class);
            }catch (Exception e){
                result= deliverCountResultRedisSet(request, redis, key);
                logger.error("NAV JsonUtils.toObject",e);
                return result;
            }
            if(null==result){
                result= deliverCountResultRedisSet(request, redis, key);
            }
            return result;
        }
    }

    private DeliverCountResult deliverCountResultRedisSet(NavRequest request, ValueOperations<String, String> redis, String key) {
        DeliverCountResult result=getNavBillInfoCore(request);
        String navRedisSecondsStr= GlobalConfig.get(NAV_REDIS_SEC0NDS_KEY);
        long navRedisSeconds=300;
        if(StringUtils.isNotEmpty(navRedisSecondsStr)){
            try{
                navRedisSeconds=Long.parseLong(navRedisSecondsStr);
            }catch (Exception exx){}
        }
        try{
            redis.set(key, JsonUtils.toJson(result), navRedisSeconds, TimeUnit.SECONDS);
        }catch (Exception e){
            logger.error("NAV redis set",e);
        }
        return result;
    }

    @NotNull
    private static String getRequestRedisKey(NavRequest request) {
        String requestMd5= Md5Utils.md5(JsonUtils.toJson(request));
        String key = MessageFormat.format("{0}:{1}:{2}:{3}",NAV_REDIS_TITLE, CurrentUser.getProfileId(), CurrentUser.getEmployeeId(),requestMd5);
        return key;
    }

    private DeliverCountResult getNavBillInfoCore(NavRequest request) {
        List<Stock> stockList = getStockList();
        stockList = stockList == null ? new ArrayList<>() : stockList;
        List<BigInteger> stockIds = stockList.stream().map(Stock::getId).collect(Collectors.toList());

        List<Organization> organizationList = getOrganizationList();
        organizationList = organizationList == null ? new ArrayList<>() : organizationList;
        List<BigInteger> otypeIds = organizationList.stream().map(Organization::getId).collect(Collectors.toList());

        List<Employee> employeeList = getEmployeeList();
        employeeList = employeeList == null ? new ArrayList<>() : employeeList;
        List<BigInteger> etypeIds = employeeList.stream().map(Employee::getId).collect(Collectors.toList());

        DeliverCountResult countResult = new DeliverCountResult();
        Date beginTime = DateUtils.getBeginDate(DateUtils.addDays(DateUtils.getDate(), -29));
        Date endTime = DateUtils.getEndDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat( " yyyy-MM-dd HH:mm:ss");
        String beginTimeString = simpleDateFormat.format(beginTime);
        String endTimeString = simpleDateFormat.format(endTime);

        String navDateByLogoStr= GlobalConfig.get(NAV_DATA_BY_LOGO);
        boolean dataByLogo=false;
        try{
            if(StringUtils.isNotEmpty(navDateByLogoStr)){
                dataByLogo=Boolean.parseBoolean(navDateByLogoStr);
            }
        }catch (Exception ex){
            logger.error("dataByLogo format",ex);
        }

        int queryType = request.queryType;
        if (queryType == 0 || queryType == 1) {
            //【待审核】
            DeliverCountResult auditCount = this.deliverService.getAuditCount(CurrentUser.getProfileId(), stockIds, otypeIds, etypeIds, beginTime, endTime,dataByLogo);
            countResult.setAuditCount(auditCount.getAuditCount());
            countResult.setAuditMinTime(auditCount.getAuditMinTime());
            countResult.setAuditMaxTime(auditCount.getAuditMaxTime());
            countResult.setAuditUrl("/sale/jarvis/DeliverBill/common/DeliverBillGuideForm.gspx?caption=订单审核&tag=AUDIT&closeType=url&timeType=0&beginTime=" + beginTimeString + "&endTime=" + endTimeString + "&jumpType=audit");
        }

        if (queryType == 0 || queryType == 2) {
            //【待同步】
            DeliverCountResult waitSyncCount = this.deliverService.getWaitSyncCount(CurrentUser.getProfileId(), stockIds, otypeIds, etypeIds, beginTime, endTime,dataByLogo);
            countResult.setWaitSyncCount(waitSyncCount.getWaitSyncCount());
            countResult.setWaitSyncMinTime(waitSyncCount.getWaitSyncMinTime());
            countResult.setWaitSyncMaxTime(waitSyncCount.getWaitSyncMaxTime());
            countResult.setWaitSyncUrl("/sale/jarvis/DeliverBill/common/DeliverBillGuideForm.gspx?caption=订单查询&tag=QUERY&closeType=url&timeType=0&beginTime=" + beginTimeString + "&endTime=" + endTimeString + "&jumpType=waitSync");
        }

        if (queryType == 0 || queryType == 3) {
            //【待出库】
            DeliverCountResult waitOutCount = this.deliverService.getWaitOutCount(CurrentUser.getProfileId(), stockIds, otypeIds, etypeIds, beginTime, endTime,dataByLogo);
            countResult.setWaitOutCount(waitOutCount.getWaitOutCount());
            countResult.setWaitOutMinTime(waitOutCount.getWaitOutMinTime());
            countResult.setWaitOutMaxTime(waitOutCount.getWaitOutMaxTime());
            countResult.setWaitOutUrl("/sale/jarvis/DeliverBill/common/DeliverBillGuideForm.gspx?caption=订单查询&tag=QUERY&closeType=url&timeType=0&beginTime=" + beginTimeString + "&endTime=" + endTimeString + "&jumpType=notStockOut");
        }

        if (queryType == 0 || queryType == 4) {
            //【异常单】
            DeliverCountResult abnormalCount = this.deliverService.getAbnormalCount(CurrentUser.getProfileId(), stockIds, otypeIds, etypeIds, beginTime, endTime,dataByLogo);
            countResult.setAbnormalCount(abnormalCount.getAbnormalCount());
            countResult.setAbnormalMinTime(abnormalCount.getAbnormalMinTime());
            countResult.setAbnormalMaxTime(abnormalCount.getAbnormalMaxTime());
            countResult.setAbnormalUrl("/sale/jarvis/DeliverBill/common/DeliverBillGuideForm.gspx?caption=订单查询&tag=QUERY&closeType=url&timeType=0&beginTime=" + beginTimeString + "&endTime=" + endTimeString + "&jumpType=exceptionAll");
        }

        if (queryType == 0 || queryType == 5) {
            //【发货预警】
            DeliverCountResult aboutOverTimeCount = this.deliverService.getAboutOverTimeCount(CurrentUser.getProfileId(), stockIds, otypeIds, etypeIds, beginTime, endTime,dataByLogo);
            countResult.setAboutOverTimeCount(aboutOverTimeCount.getAboutOverTimeCount());
            countResult.setAboutOverTimeMinTime(aboutOverTimeCount.getAboutOverTimeMinTime());
            countResult.setAboutOverTimeMaxTime(aboutOverTimeCount.getAboutOverTimeMaxTime());
            countResult.setAboutOverTimeUrl("/sale/jarvis/DeliverBill/common/DeliverBillGuideForm.gspx?caption=订单查询&tag=QUERY&closeType=url&timeType=0&beginTime=" + beginTimeString + "&endTime=" + endTimeString + "&jumpType=aboutOverTime");
        }

        if (queryType == 0 || queryType == 6) {
            //【发货超时】
            DeliverCountResult overTimeCount = this.deliverService.getOverTimeCount(CurrentUser.getProfileId(), stockIds, otypeIds, etypeIds, beginTime, endTime,dataByLogo);
            countResult.setOverTimeCount(overTimeCount.getOverTimeCount());
            countResult.setOverTimeMinTime(overTimeCount.getOverTimeMinTime());
            countResult.setOverTimeMaxTime(overTimeCount.getOverTimeMaxTime());
            countResult.setOverTimeUrl("/sale/jarvis/DeliverBill/common/DeliverBillGuideForm.gspx?caption=订单查询&tag=QUERY&closeType=url&timeType=0&beginTime=" + beginTimeString + "&endTime=" + endTimeString + "&jumpType=DeliveryTimeout");
        }

        if (queryType == 0 || queryType == 7) {
            //【同步失败】
            DeliverCountResult syncFailCount = this.deliverService.getSyncFailCount(CurrentUser.getProfileId(), stockIds, otypeIds, etypeIds, beginTime, endTime,dataByLogo);
            countResult.setSyncFailCount(syncFailCount.getSyncFailCount());
            countResult.setSyncFailMinTime(syncFailCount.getSyncFailMinTime());
            countResult.setSyncFailMaxTime(syncFailCount.getSyncFailMaxTime());
            countResult.setSyncFailUrl("/sale/jarvis/DeliverBill/common/DeliverBillGuideForm.gspx?caption=订单查询&tag=QUERY&closeType=url&timeType=0&beginTime=" + beginTimeString + "&endTime=" + endTimeString + "&jumpType=syncFail");
        }

        return countResult;
    }
    @PostMapping("getSyncBillHint")
    @ApiOperation(value = "获取首页右上角同步数据", notes = "获取首页右上角同步数据")
    public DeliverSyncBillCountResult getSyncBillHint(@RequestBody NavRequest request) {
        DeliverSyncBillCountResult result;
        result = deliverService.getSyncBillHint(CurrentUser.getProfileId());
        return result;
    }

    @PostMapping("getSyncBillHintNumbers")
    @ApiOperation(value = "获取首页右上角同步数据的跳转订单", notes = "获取首页右上角同步数据的跳转订单")
    public DeliverSyncBillCountResult getSyncBillHintNumbers(@RequestBody NavRequest request) {
        return deliverService.getSyncBillHintNumbers(CurrentUser.getProfileId(), request.getSyncBillType());
    }

    public List<Stock> getStockList() {
        if (!PermissionValiateService.isAdmin() && PermissionValiateService.isKtypeLimited()) {
            List<Stock> result= baseInfoService.getKTypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            Stock zero=new Stock();
            zero.setId(BigInteger.ZERO);
            result.add(zero);
            return result;
        } else {
            return null;
        }
    }


    public List<Employee> getEmployeeList() {
        if (!PermissionValiateService.isAdmin() && PermissionValiateService.isEtypeLimited()) {
            List<Employee> result= baseInfoService.getETypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            Employee zero=new Employee();
            zero.setId(BigInteger.ZERO);
            result.add(zero);
            Employee user=new Employee();
            user.setId(CurrentUser.getEmployeeId());
            result.add(user);
            return result;
        } else {
            return null;
        }
    }


    public List<Organization> getOrganizationList() {
        if (!PermissionValiateService.isAdmin() && PermissionValiateService.isOtypeLimited()) {
            List<Organization> result=baseInfoService.getEshopOrganizationsLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            Organization zero=new Organization();
            zero.setId(BigInteger.ZERO);
            result.add(zero);
            return result;
        } else {
            return null;
        }
    }
    public static class NavRequest implements Serializable {
        private DeliverProcessStatusEnum status;
        private Boolean enabled;

        /**
         * 0 - 查询所有状态订单单据数量
         * 1 - 查询待审核、待出库、异常单据数量
         * 2 - 查询异常、超时、同步失败单据数量
         **/
        private int queryType;
        /**
         * 1 已打印60分钟未发货未同步 2 已发货未同步 3 同步失败 4 全部异常订单
         */
        private int syncBillType;
        private Boolean handleRefresh;

        public int getQueryType() {
            return queryType;
        }

        public void setQueryType(int queryType) {
            this.queryType = queryType;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public DeliverProcessStatusEnum getStatus() {
            return status;
        }

        public void setStatus(DeliverProcessStatusEnum status) {
            this.status = status;
        }

        public Boolean getHandleRefresh() {
            return handleRefresh;
        }

        public void setHandleRefresh(Boolean handleRefresh) {
            this.handleRefresh = handleRefresh;
        }

        public int getSyncBillType() {
            return syncBillType;
        }

        public void setSyncBillType(int syncBillType) {
            this.syncBillType = syncBillType;
        }
    }
}
