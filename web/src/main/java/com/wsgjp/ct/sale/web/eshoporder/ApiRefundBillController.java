package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundFreight;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.WriteBackRefundStateParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.WriteBackRefundStateResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryRefundParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.WmsNotifyRefundParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopOrderEshopRefundService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.callback.EshopRefundBillCore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright © 2021 章鱼侠. All rights reserved.
 *
 * <AUTHOR> by hyq
 * @version 1.0
 * @date 2021/6/22
 * @description:
 */
@Api(tags = "售后单据状态调整对外提供的接口相关")
@RequestMapping("${app.id}/eshoporder/refundApi")
@RestController
public class ApiRefundBillController {

    private static final Logger logger = LoggerFactory.getLogger(ApiRefundBillController.class);

    @Autowired
    EshopRefundBillCore eshopRefundBillCore;
    @Autowired
    private EshopOrderEshopRefundService refundService;


    @PostMapping("/writeBackRefundState")
    @ApiOperation("回写订单状态")
    public WriteBackRefundStateResponse writeBackRefundState(@RequestBody WriteBackRefundStateParameter parameter) {
        long s = System.currentTimeMillis();
        WriteBackRefundStateResponse writeBackRefundStateResponse = eshopRefundBillCore.writeBackRefundState(parameter);
        logger.info("单据ID【" + parameter.getVchcode() + "】,账套ID【" + parameter.getProfileId() + "】执行时间：" + (System.currentTimeMillis() - s));
        return writeBackRefundStateResponse;
    }

    /**
     * WMS新增售后单
     */
    @PostMapping(value = "/saveRefund")
    @ApiOperation("WMS新增售后单")
    public BaseResponse saveRefund(@RequestBody EshopRefundEntity refund) {
        return refundService.saveRefundForWMS(refund);
    }

    /**
     * WMS查询售后单列表
     */
    @PostMapping(value = "/queryRefundList")
    @ApiOperation("WMS查询售后单列表")
    public PageResponse<EshopRefundEntity> queryRefundList(@RequestBody PageRequest<QueryRefundParameter> parameter) {
        return refundService.queryRefundListNew(parameter, false, true);
    }

    /**
     * WMS修改售后单售后状态
     * */
    @PostMapping(value = "/updateRefundState")
    @ApiOperation("WMS修改售后单售后状态")
    public BaseResponse updateRefundState(@RequestBody EshopRefundEntity refund) {
        return refundService.updateRefundStateForWms(refund);
    }

    /**
     * WMS回告售后单修改批次等信息
     * */
    @PostMapping(value = "/wmsNotifyRefund")
    @ApiOperation("WMS回告售后单修改批次等信息")
    public BaseResponse wmsNotifyRefund(@RequestBody PageRequest<WmsNotifyRefundParameter> parameter) {
        return refundService.wmsNotifyRefund(parameter);
    }


    /**
     * WMS修改售后单物流信息
     * */
    @PostMapping(value = "/updateRefundFreight")
    @ApiOperation("WMS修改售后单物流信息")
    public BaseResponse updateRefundFreight(@RequestBody EshopRefundEntity refund) {
        return refundService.updateRefundFreight(refund);
    }

}
