package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.entity.template.TemplateAndFreightDAO;
import com.wsgjp.ct.sale.biz.jarvis.entity.template.TemplatePrinterConfigDAO;
import com.wsgjp.ct.sale.biz.jarvis.service.template.TemplatePrinterConfigService;
import com.wsgjp.ct.sale.biz.jarvis.service.template.TemplateService;
import com.wsgjp.ct.sale.biz.jarvis.state.TemplateTypeForQueryEnum;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api("模板打印机配置服务接口")
@RequestMapping("/${app.id}/jarvis/TemplatePrinterConfig")
public class TemplatePrinterConfigController {

    private TemplatePrinterConfigService configService;
    private TemplateService templateService;

    public TemplatePrinterConfigController(TemplatePrinterConfigService configService, TemplateService templateService) {
        this.configService = configService;
        this.templateService = templateService;
    }

    @ApiOperation(value = "批量添加配置", notes = "（）")
    @PostMapping("batchAddConfig")
    public void batchAddConfig(@RequestBody List<TemplatePrinterConfigDAO> configDAOList) {
        this.configService.batchAddConfig(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), configDAOList);
    }

    @ApiOperation(value = "获取配置", notes = "（）")
    @PostMapping("getConfig")
    List<TemplatePrinterConfigDAO> getConfigs(@RequestBody GetConfigParam getConfigParam) {
        return this.configService.getConfig(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), getConfigParam.getTemplateType());
    }

    @ApiOperation(value = "获取模板信息", notes = "（）")
    @PostMapping("getTemplateInfoById")
    TemplateAndFreightDAO getTemplateInfoById(@RequestBody TemplatePrinterConfigDAO templatePrinterConfigDAO) {
        return templateService.getTemplateById(CurrentUser.getProfileId(), templatePrinterConfigDAO.getTemplateId());
    }

    public static class GetConfigParam implements Serializable {
        private TemplateTypeForQueryEnum templateType;

        public TemplateTypeForQueryEnum getTemplateType() {
            return templateType;
        }

        public void setTemplateType(TemplateTypeForQueryEnum templateType) {
            this.templateType = templateType;
        }
    }
}
