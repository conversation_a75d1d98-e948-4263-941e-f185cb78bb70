package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.entity.check.*;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverSplitService;
import com.wsgjp.ct.sale.biz.jarvis.service.check.NewCheckService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: gj
 * @Date: 2020-04-27
 */
@RestController
@Api(description = "发货单主表接口")
@RequestMapping("/${app.id}/jarvis/deliverBillCheck")
public class DeliverBillCheckController {
    private NewCheckService newCheckService;
    private BillDeliverSplitService billDeliverSplitService;

    public DeliverBillCheckController(NewCheckService newCheckService,
                                      BillDeliverSplitService billDeliverSplitService) {
        this.newCheckService = newCheckService;
        this.billDeliverSplitService = billDeliverSplitService;
    }

    @ApiOperation(value = "订单检查", notes = "多个地方调用,使用任务单ID")
    @PostMapping("check")
    public BillDeliverCheckResult check(@RequestBody NewDeliverCheckRequest request) {
        // 去除request中重复的vchCode
        Set<BigInteger> flagSet = new HashSet<>();
//        if(request.getCheckType().equals(NewBillDeliverCheckTypeEnum.RE_POST_FAILED_BILL_FOR_ACCOUNT)){
//            request.setBills(request.getBills().stream().filter(b -> flagSet.add(b.getVchcode())).collect(Collectors.toList()));
//            return newCheckService.Check(request.getCheckType(), request.getBills());
//        }else {
        request.setBills(request.getBills().stream().filter(b -> flagSet.add(b.getWarehouseTaskId())).collect(Collectors.toList()));
        return newCheckService.Check(request.getCheckType(), request.getBills());
//        }
    }

    @ApiOperation(value = "自动审核订单检查", notes = "自动审核订单检查")
    @PostMapping("autoAuditCheck")
    public BillDeliverCheckResult autoAuditCheck(@RequestBody List<DeliverCheckRequest.CheckDeliverBill> checkBills) {
        return newCheckService.autoAuditCheck(checkBills);
    }

    @ApiOperation(value = "还原订单检查", notes = "还原订单检查")
    @PostMapping("resetCheck")
    public BillResetCheckResult resetCheck(@RequestBody List<BigInteger> warehouseTaskIds) {
        List<DeliverCheckRequest.CheckDeliverBill> checkBills = new ArrayList<>();
        for (BigInteger warehouseTaskId : warehouseTaskIds) {
            DeliverCheckRequest.CheckDeliverBill bill = new DeliverCheckRequest.CheckDeliverBill();
            bill.setWarehouseTaskId(warehouseTaskId);
            checkBills.add(bill);
        }
        return newCheckService.resetCheck(checkBills);
    }

    @ApiOperation(value = "改单据信息检查", notes = "改单据信息检查")
    @PostMapping("batchModifyDeliverCheck")
    public BillDeliverCheckResult batchModifyDeliverCheck(@RequestBody ModifyDeliverCheckRequest modifyDeliverCheckRequest) {
        return newCheckService.batchModifyDeliverCheck(modifyDeliverCheckRequest);
    }

    @ApiOperation(value = "改单据信息检查", notes = "改单据信息检查")
    @PostMapping("batchModifyDeliverKtypeCheck")
    public BillDeliverCheckResult batchModifyDeliverKtypeCheck(@RequestBody ModifyDeliverCheckRequest modifyDeliverCheckRequest) {
        return newCheckService.batchModifyDeliverKtypeCheck(modifyDeliverCheckRequest);
    }

    @ApiOperation(value = "取消订单拆分检查", notes = "仓储使用")
    @PostMapping("checkCancelSplit")
    public BillResetCheckResult check(@RequestBody List<BigInteger> warehouseTaskIds) {
        return billDeliverSplitService.checkCancel(CurrentUser.getProfileId(), warehouseTaskIds);
    }

    @ApiOperation(value = "联系买家-检查", notes = "联系买家-检查")
    @PostMapping("contactBuyerCheck")
    public boolean contactBuyerCheck(@RequestBody BigInteger warehouseTaskIds) {
        return newCheckService.contactBuyerCheck(warehouseTaskIds);
    }
}
