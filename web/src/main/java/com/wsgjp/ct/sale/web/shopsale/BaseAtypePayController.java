package com.wsgjp.ct.sale.web.shopsale;

import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseAtypePay;
import com.wsgjp.ct.sale.biz.shopsale.service.BaseAtypePayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;

@Api(tags = {"收支账户"})
@RestController
@RequestMapping("${app.id}/shopsale/baseAtypePay")
public class BaseAtypePayController {

    @Autowired
    private BaseAtypePayService baseAtypePayService;

    @ApiOperation("获取收支账户支付绑定关系")
    @PostMapping("/getByPaywayId")
    public BaseAtypePay getByPaywayId(@RequestBody BigInteger paywayId) {
        return baseAtypePayService.getByPaywayId(paywayId);
    }

    @ApiOperation("绑定聚合支付")
    @PostMapping("/saveBaseAtypePay")
    public void saveBaseAtypePay(@RequestBody BaseAtypePay pay) {
        baseAtypePayService.saveBaseAtypePay(pay);
    }

    @ApiOperation("解绑聚合支付")
    @PostMapping("/deleteBaseAtypePay")
    public void deleteBaseAtypePay(@RequestBody BigInteger id) {
        baseAtypePayService.deleteBaseAtypePay(id);
    }

    @ApiOperation(value="授权信息",notes ="是否存在历史授权信息（现金银行绑定）")
    @PostMapping("/checkExistByAuthHistory")
    public boolean checkExistByAuthHistory(@Validated @RequestBody BaseAtypePay atypePayDto){
        return baseAtypePayService.checkExistByAuthHistory(atypePayDto.getOpenKey(),atypePayDto.getOpenId());
    }
}
