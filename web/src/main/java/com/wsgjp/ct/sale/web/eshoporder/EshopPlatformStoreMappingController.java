package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import bf.datasource.page.Sort;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.EshopPlatformStoreMappingInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.RefreshOnlineStoreParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.PlatformStoreImportErrorData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopPlatformStoreMappingService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.common.processlogger.impl.ProcessLoggerImpl;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import ngp.idgenerator.UId;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "全渠道门店对应")
@RequestMapping("${app.id}/eshoporder/eshopplatformstoremapping")
@RestController
public class EshopPlatformStoreMappingController {
    private final EshopPlatformStoreMappingService storeMappingService;

    public EshopPlatformStoreMappingController(EshopPlatformStoreMappingService storeMappingService) {
        this.storeMappingService = storeMappingService;
    }

    @PostMapping("/queryListByEshopId")
    public PageResponse<EshopPlatformStoreMapping> queryListByEshopId(@RequestBody PageRequest<EshopPlatformStoreMappingInDTO> pageParameter) {
        EshopPlatformStoreMappingInDTO queryParams = pageParameter.getQueryParams();
        if (pageParameter.getSorts() == null) {
            List<Sort> sorts = new ArrayList<>();
            sorts.add(new Sort("eshopId", true));
            pageParameter.setSorts(sorts);
        }
        CommonUtil.initLimited(queryParams);
        PageDevice.initPage(pageParameter);
        return PageDevice.readPage(storeMappingService.queryListByEshopId(queryParams));
    }

    @PostMapping("/refreshPlatformStore")
    public BaseResponse refreshPlatformStore(@RequestBody EshopPlatformStoreMappingInDTO inDTO) {
        if (inDTO != null) {
            inDTO.setType(0);
        }
        return storeMappingService.refreshPlatformStore(inDTO);
    }

    @PostMapping("/refreshOnlineStore")
    public String refreshOnlineStore(@RequestBody RefreshOnlineStoreParam params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        params.setTaskId(taskId);
        params.setProcessLogger(processLogger);
        processLogger.appendMsg("接到请求，正在处理");
        ThreadPool test = ThreadPoolFactory.build("refresh-ptype");
        test.executeAsync(invoker -> {
            storeMappingService.refreshOnlineStore(params);
        }, "刷新网店仓库对应");
        return taskId;
    }


    @PostMapping("/addStore")
    public BaseResponse addStore(@RequestBody EshopPlatformStoreMapping inDTO) {
        if (inDTO != null) {
            inDTO.setType(0);
        }
        // 如果是新页面固定是66
        if (inDTO.isPlatformStoreNew()){
            inDTO.setType(66);
        }
        return storeMappingService.savePlatformStore(inDTO);
    }

    @PostMapping("/platformStoreEdit")
    public BaseResponse platformStoreEdit(@RequestBody EshopPlatformStoreMapping inDTO) {
        return storeMappingService.platformStoreEdit(inDTO);
    }
    @PostMapping("/doDelete")
    public BaseResponse doDelete(@RequestBody EshopPlatformStoreMapping inDTO) {
        return storeMappingService.doDelete(inDTO);
    }

    @PostMapping("/doBatchDelete")
    public BaseResponse doBatchDelete(@RequestBody List<EshopPlatformStoreMapping> inDTOs) {
        return storeMappingService.doBatchDelete(inDTOs);
    }

    @PostMapping("/getBaseKtypeListByProfileId")
    public List<Stock> getBaseKtypeListByProfileId() {
        return storeMappingService.getBaseKtypeListByProfileId();
    }

    @PostMapping("/getBaseKtypeListByProfileIdForRefund")
    public List<Stock> getBaseKtypeListByProfileIdForRefund() {
        return storeMappingService.getBaseKtypeListByProfileIdForRefund();
    }

    @PostMapping("/saveFile")
    public List<PlatformStoreImportErrorData> uploadFile(MultipartFile importFile, BigInteger eshopId, String eshopName,
                                                         BigInteger ktypeId) {
        return storeMappingService.uploadFile(importFile, eshopId, eshopName, ktypeId,null);
    }

    @PostMapping("/saveFileNew")
    public List<PlatformStoreImportErrorData> uploadFileNew(MultipartFile importFile, BigInteger eshopId, String eshopName,
                                                            BigInteger ktypeId,String taskId) {
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        processLogger.appendMsg("接到请求，正在处理");

        return storeMappingService.uploadFileNew(importFile, eshopId, eshopName, ktypeId, 99);
    }

    @PostMapping("/updateById")
    public BaseResponse updateById(@RequestBody EshopPlatformStoreMappingInDTO inDTO) {
        return storeMappingService.updateById(inDTO);
    }

    @GetMapping(value = "/addSysLog")
    public void addSysLog() {
        PubSystemLogService.saveInfo("进入全渠道门店对应");
    }
}
