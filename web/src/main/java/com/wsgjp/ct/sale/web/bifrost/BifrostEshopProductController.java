package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopProductService;
import com.wsgjp.ct.sale.platform.entity.request.plugin.GetPlatProductUrlRequest;
import com.wsgjp.ct.sale.platform.entity.request.product.*;
import com.wsgjp.ct.sale.platform.entity.response.product.ProductCategoryResponse;
import com.wsgjp.ct.sale.platform.entity.response.product.ProductDownloadResponse;
import com.wsgjp.ct.sale.platform.entity.response.product.ProductModifyResponse;
import com.wsgjp.ct.sale.platform.entity.response.product.ProductShelfResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "网店商品相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/product")
public class BifrostEshopProductController {
    private final BifrostEshopProductService productService;

    public BifrostEshopProductController(BifrostEshopProductService productService) {
        this.productService = productService;
    }

    @ApiOperation("下载商品分类列表")
    @PostMapping("downloadCategory")
    public ProductCategoryResponse downloadCategory(@RequestBody DownloadProductCategoryRequest request) {
        return productService.downloadCategoryList(request);
    }

    @ApiOperation("全量下载商品列表")
    @PostMapping("download")
    public ProductDownloadResponse downloadProducts(@RequestBody DownloadProductRequest request) {
        return productService.getProducts(request);
    }

    @ApiOperation("通过Id下载商品列表")
    @PostMapping("downloadByNumIds")
    public ProductDownloadResponse downloadByNumIds(@RequestBody DownloadProductByIdRequest request) {
        return productService.getProductByNumId(request);
    }

    @ApiOperation("增量下载商品列表")
    @PostMapping("downloadByIncrementally")
    public ProductDownloadResponse downloadByIncrementally(@RequestBody DownloadProductIncreaseRequest request) {
        return productService.downloadProductIncrementally(request);
    }

    @ApiOperation("修改商品商家编码")
    @PostMapping("modifyXcode")
    public ProductModifyResponse modifyXcode(@RequestBody ModifyXcodeRequest request) {
        return productService.modifyProductXcode(request);
    }

    @ApiOperation("商品上下架")
    @PostMapping("shelfOnOrOff")
    public ProductShelfResponse productShelfOnOrOff(@RequestBody ProductShelfRequest request) {
        return productService.productShelfOnOrOff(request);
    }

    @ApiOperation("获取商品地址")
    @PostMapping("getPlatformProductUrl")
    public String getPlatformProductUrl(@RequestBody GetPlatProductUrlRequest request) {
        return productService.getPlatformProductUrl(request);
    }

    @ApiOperation("获取商品地址")
    @PostMapping("downloadProductsByXcode")
    public ProductDownloadResponse downloadProductsByXcode(@RequestBody DownloadProductXcodeRequest request) {
        return productService.downloadProductsByXcode(request);
    }
}
