package com.wsgjp.ct.sale.web.jarvis.controller;

/**
 * @Description TODO
 * @Date 2020-05-09 16:23
 * @Created by lingxue
 */

import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.api.response.BillPostResponse;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.DeliverInvoiceStateUpdateDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.DeliversInvoiceStateUpdateDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.BaseProcessRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.BillInvoice;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.jarvis.service.BillInvoiceService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillExStatusAsyncHandleHelper;
import com.wsgjp.ct.sale.biz.jarvis.utils.RedisProcessMsgUtils;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.NeedProcessMsgBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessFaceBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessMessageMemory;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessResponse;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.sale.web.jarvis.response.CommonResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.JsonUtils;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RestController
@Api(description = "发票信息")
@RequestMapping("/${app.id}/jarvis/invoice")
public class BillInvoiceController {
    private static org.slf4j.Logger logger = LoggerFactory.getLogger(BillInvoiceController.class);
    private BillInvoiceService invoiceService;
    private BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper;

    public BillInvoiceController(BillInvoiceService invoiceService,
                                 BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper) {
        this.invoiceService = invoiceService;
        this.billExStatusAsyncHandleHelper = billExStatusAsyncHandleHelper;
    }

    @ApiOperation(value = "批量修改发票信息", notes = "修改发票信息")
    @PostMapping("modifyInvoiceBatchForProcessMsg")
    @NeedProcessMsgBatchAd(threadPoolName=NeedProcessMsg.ProcessName.defaultProcessName)
    public ProcessResponse<StrategyProcessLog> modifyInvoiceBatchForProcessMsg(@RequestBody ModifyInvoiceRequest modifyInvoiceRequest) {
        ProcessResponse<StrategyProcessLog> response = new ProcessResponse<StrategyProcessLog>();
        //初始化Redis
        RedisProcessMessage processMessage = new ProcessMessageMemory(modifyInvoiceRequest,modifyInvoiceRequest.getProcessId());
        RedisProcessMessage.MsgLogger log = processMessage.getMsgLogger();
        List<BillPostResponse> result = new ArrayList<>();
        log.appendMsg("开始执行...");
        try {
            response.setCode("200");
            result = invoiceService.addOrModifyBatch(modifyInvoiceRequest.getWarehouseTaskIds(),
                    modifyInvoiceRequest.getBillInvoice(),
                    true,
                    false);
        } catch (Exception e) {
            String msg = String.format("修改发票信息失败 %s", e.getMessage());
            response.setCode("-1");
            response.setMsg(msg);
            log.appendMsg(msg);
            logger.error(msg, e);
            RedisProcessMsgUtils.resetLoggerError(log,true);
        } finally {
//            billExStatusAsyncHandleHelper.logoComprehensiveSceneByFace(ErrorFace.看我看我别忘了改("记得改这个参数"),Arrays.asList(ModifyScene.BillOtherModify),false);
        }
        long failCount = result.stream().filter(r->!r.isSuccess()).count();
        RedisProcessMsgUtils.appendEndMessage(log, "执行完毕", modifyInvoiceRequest.getWarehouseTaskIds().size(), (int)(modifyInvoiceRequest.getWarehouseTaskIds().size()-failCount));
        return ProcessResponse.result(StrategyProcessLog.class,processMessage);
    }

    @ApiOperation(value = "修改发票信息-订单审核", notes = "修改发票信息")
    @PostMapping("modifyAUDIT")
    @PermissionCheck(key = PermissionSysConst.SEND_DELIVER_MODIFY_INVOICE)
    public BaseResponse modifyAUDIT(@RequestBody BillInvoice invoice) {
        try {
            return CommonResponse.success(invoiceService.addOrModify(CurrentUser.getProfileId(),invoice,true,false));
        } catch (Exception e) {
            logger.error("修改发票信息失败", e);
            return CommonResponse.fail(String.format("修改发票信息失败 %s", e.getMessage()));
        }
    }

    @ApiOperation(value = "修改发票信息-订单查询", notes = "修改发票信息")
    @PostMapping("modifyQUERY")
    @PermissionCheck(key = PermissionSysConst.SEND_DELIVER_MODIFY_INVOICE)
    public BaseResponse modifySENDQUERY(@RequestBody BillInvoice invoice) {
        try {
            return CommonResponse.success(invoiceService.addOrModify(CurrentUser.getProfileId(),invoice,true,false));
        } catch (Exception e) {
            logger.error("修改发票信息失败", e);
            return CommonResponse.fail(String.format("修改发票信息失败 %s", e.getMessage()));
        }
    }

    @ApiOperation(value = "修改", notes = "修改发票信息")
    @PostMapping("modifyNOSENDQUERY")
    @PermissionCheck(key = PermissionSysConst.NO_SEND_DELIVER_MODIFY_INVOICE)
    public BaseResponse modifyNOSENDQUERY(@RequestBody BillInvoice invoice) {
        try {
            return CommonResponse.success(invoiceService.addOrModify(CurrentUser.getProfileId(),invoice,true,false));
        } catch (Exception e) {
            logger.error("修改发票信息失败", e);
            return CommonResponse.fail(String.format("修改发票信息失败 %s", e.getMessage()));
        }
    }

    @ApiOperation(value = "修改", notes = "修改发票信息")
    @PostMapping("modifyPRINT")
    @PermissionCheck(key = PermissionSysConst.DELIVER_PRINT_MODIFY_INVOICE)
    public BaseResponse modifyPRINT(@RequestBody BillInvoice invoice) {
        try {
            return CommonResponse.success(invoiceService.addOrModify(CurrentUser.getProfileId(),invoice,true,false));
        } catch (Exception e) {
            logger.error("修改发票信息失败", e);
            return CommonResponse.fail(String.format("修改发票信息失败 %s", e.getMessage()));
        }
    }

    @ApiOperation(value = "修改开票状态-订单审核/查询界面修改", notes = "修改发票信息")
    @PostMapping("modifyInvoiceState")
    public void modifyInvoiceState(@RequestBody DeliverInvoiceStateUpdateDTO request){
        logger.error("modifyInvoiceState-修改开票状态接口:"+ JsonUtils.toJson(request));
        this.invoiceService.modifyInvoiceState(CurrentUser.getProfileId(),
                Collections.singletonList(request.getVchcode()),
                request.getInvoiceState().getCode());
    }

    @ApiOperation(value = "批量修改开票状态-进销存接口回调", notes = "批量修改发票信息")
    @PostMapping("modifyBatchInvoiceState")
    public void modifyInvoiceState(@RequestBody DeliversInvoiceStateUpdateDTO request){
        logger.error("modifyBatchInvoiceState-修改开票状态接口:"+ JsonUtils.toJson(request));
        this.invoiceService.modifyInvoiceState(CurrentUser.getProfileId(),
                request.getVchcodes(),
                request.getInvoiceState().getCode());
    }

    @ApiOperation(value = "上传发票", notes = "批量上传发票")
    @PostMapping("uploadBatchInvoice")
    public BaseResponse uploadBatchInvoice(@RequestBody DeliversInvoiceStateUpdateDTO request){
        try {
            return CommonResponse.success(invoiceService.uploadEshopInvoice(request.getWarehouseTaskIds()));
        } catch (Exception e) {
            logger.error("上传发票失败", e);
            return CommonResponse.fail(String.format("上传失败 %s", e.getMessage()));
        }
    }
}

class ModifyInvoiceRequest extends BaseProcessRequest  implements ProcessFaceBatchAd {
    private List<BigInteger> vchcodes;
    private List<BigInteger> warehouseTaskIds;
    private BillInvoice billInvoice;
    private Boolean notProcess;

    public List<BigInteger> getVchcodes() {
        return vchcodes;
    }

    public void setVchcodes(List<BigInteger> vchcodes) {
        this.vchcodes = vchcodes;
    }

    public BillInvoice getBillInvoice() {
        return billInvoice;
    }

    public void setBillInvoice(BillInvoice billInvoice) {
        this.billInvoice = billInvoice;
    }

    public List<BigInteger> getWarehouseTaskIds() {
        return warehouseTaskIds;
    }

    public void setWarehouseTaskIds(List<BigInteger> warehouseTaskIds) {
        this.warehouseTaskIds = warehouseTaskIds;
    }

    @Override
    public Boolean getNotProcess() {
        return notProcess;
    }

    public void setNotProcess(Boolean notProcess) {
        this.notProcess = notProcess;
    }
}
