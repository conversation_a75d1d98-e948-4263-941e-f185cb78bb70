package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import com.wsgjp.ct.common.enums.core.enums.BillRemindEnum;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.DeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.DeliverPrintBatchConfigDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.DeliverPrintBatchQueryParamDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.PrintBatchCheckResultDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverPrintBatchDAO;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Employee;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Stock;
import com.wsgjp.ct.sale.biz.jarvis.entity.templatecenter.DeliverFreightInfoDTO;
import com.wsgjp.ct.sale.biz.jarvis.mapper.BillDeliverDetailMapper;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.CommonService;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverBillMainSubPrintCommonService;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.biz.jarvis.service.printbatch.FreightPrintService;
import com.wsgjp.ct.sale.biz.jarvis.service.printbatch.PrintBatchExecuterService;
import com.wsgjp.ct.sale.biz.jarvis.service.printbatch.PrintBatchService;
import com.wsgjp.ct.sale.biz.jarvis.state.AssignStateEnum;
import com.wsgjp.ct.sale.biz.jarvis.state.FreightSyncStateEnum;
import com.wsgjp.ct.sale.biz.jarvis.state.RefundStateEnum;
import com.wsgjp.ct.sale.biz.jarvis.utils.BitOperExtensions;
import com.wsgjp.ct.sale.biz.jarvis.utils.CaptchaUtil;
import com.wsgjp.ct.sale.common.enums.TradeStateEnum;
import com.wsgjp.ct.sale.web.jarvis.Entity.ConfirmCodeParam;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@Api("订单打印")
@RequestMapping("/${app.id}/jarvis/deliverBillPrint")
public class DeliverBillPrintController {

    private DeliverBillMainSubPrintCommonService deliverBillMainSubPrintCommonService;
    private PrintBatchService printBatchService;
    private PrintBatchExecuterService printBatchExecuterService;
    private DeliverService deliverService;
    private BaseInfoService baseInfoService;
    private CommonService commonService;
    private FreightPrintService freightPrintService;
    private BillDeliverDetailMapper billDeliverDetailMapper;

    public DeliverBillPrintController(DeliverBillMainSubPrintCommonService deliverBillMainSubPrintCommonService,
                                      PrintBatchService printBatchService,
                                      PrintBatchExecuterService printBatchExecuterService,
                                      DeliverService deliverService,
                                      BaseInfoService baseInfoService,
                                      CommonService commonService,
                                      FreightPrintService freightPrintService,
                                      BillDeliverDetailMapper billDeliverDetailMapper) {
        this.deliverBillMainSubPrintCommonService = deliverBillMainSubPrintCommonService;
        this.printBatchService = printBatchService;
        this.printBatchExecuterService = printBatchExecuterService;
        this.deliverService = deliverService;
        this.baseInfoService = baseInfoService;
        this.commonService = commonService;
        this.freightPrintService = freightPrintService;
        this.billDeliverDetailMapper = billDeliverDetailMapper;
    }

//    @ApiOperation(value = "获取波次列表信息", notes = "获取波次列表信息")
//    @PostMapping("getDeliverPrintBatchList")
//    PageResponse<DeliverPrintBatchDAO> getDeliverPrintBatchList(@RequestBody PageRequest<DeliverPrintBatchQueryParamDTO> pageRequest) {
//        buildBaseInfoLimit(pageRequest);
//        return this.printBatchService.getDeliverPrintBatchDaoForQuery(pageRequest);
//    }

    private void buildBaseInfoLimit(PageRequest<DeliverPrintBatchQueryParamDTO> request) {
        List<Stock> stocks = new ArrayList<>();
        List<Employee> employees = new ArrayList<>();
        List<Organization> eshops = new ArrayList<>();
        if (request.getQueryParams().getEtypeId() == null||request.getQueryParams().getEtypeId().size()==0) {
            if (!PermissionValiateService.isAdmin() && PermissionValiateService.isEtypeLimited()) {
                employees = baseInfoService.getETypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            } else {
                employees = baseInfoService.getAllETypes(CurrentUser.getProfileId());
            }
            List<BigInteger> ids = employees.stream().map(e -> e.getId()).collect(Collectors.toList());
            ids.add(CurrentUser.getEmployeeId());
            ids.add(BigInteger.ZERO);
            request.getQueryParams().setEtypeId(ids);
        }
        if (request.getQueryParams().getKtypeIds() == null||request.getQueryParams().getKtypeIds().size()==0) {
            if (!PermissionValiateService.isAdmin() && PermissionValiateService.isKtypeLimited()) {
                stocks = baseInfoService.getKTypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            } else {
                stocks = baseInfoService.getAllKTypes(CurrentUser.getProfileId());
            }
            List<BigInteger> ids = stocks.stream().map(e -> e.getId()).collect(Collectors.toList());
            ids.add(BigInteger.ZERO);
            request.getQueryParams().setKtypeIds(stocks.stream().map(s -> s.getId()).collect(Collectors.toList()));
        }
        if (request.getQueryParams().getOtypeIds() == null||request.getQueryParams().getOtypeIds().size() == 0) {
            if (!PermissionValiateService.isAdmin() && PermissionValiateService.isOtypeLimited()) {
                eshops = baseInfoService.getEshopOrganizationsLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            } else {
                eshops = baseInfoService.getAllEshopOrganizations(CurrentUser.getProfileId());
            }
            List<BigInteger> ids = eshops.stream().map(e -> e.getId()).collect(Collectors.toList());
            ids.add(BigInteger.ZERO);
            request.getQueryParams().setOtypeIds(eshops.stream().map(e -> e.getId()).collect(Collectors.toList()));
        }
    }

//    @ApiOperation(value = "获取波次列表信息", notes = "获取波次列表信息")
//    @PostMapping("getPrintBatchDeliver")
//    List<PrintBatchDeliverDTO> getPrintBatchDeliver(@RequestBody List<BigInteger> batchIds) {
//        List<PrintBatchDeliverDTO> deliverListByBatchId = this.printBatchService.getDeliverListByBatchId(CurrentUser.getProfileId(), batchIds);
//
//        List<BillDeliverDTO> billDeliverDTOs = new ArrayList<>();
//        deliverListByBatchId.forEach(bill -> {
//            String FreightBillNoShow = deliverService.buildFreightNumber(bill.getFreightInfos());
//            bill.setFreightBillNoShow(FreightBillNoShow);
//            BillDeliverDTO billDeliverDTO = new BillDeliverDTO();
//            billDeliverDTO.setVchcode(bill.getVchcode());
//            billDeliverDTO.setProfileId(CurrentUser.getProfileId());
//            billDeliverDTOs.add(billDeliverDTO);
//            BillDeliverState billDeliverState = new BillDeliverState();
//            billDeliverDTO.setState(billDeliverState);
//            billDeliverDTO.setTradeOrderId(bill.getTradeOrderId());
//        });
//
//        deliverService.bindBillDeliverMark(billDeliverDTOs, HandleMarkTypeEnum.SERVICE);//TODO-优化 查询波次信息的方法，已经搬移到wms了，可以去掉
//
//        billDeliverDTOs.forEach(bd -> {
//            PrintBatchDeliverDTO printBatchDeliverDTO = deliverListByBatchId.stream().filter(p -> p.getVchcode().equals(bd.getVchcode())).findFirst().get();
//            printBatchDeliverDTO.setPresetMarkShow(bd.getPresetMarkShow());
//            printBatchDeliverDTO.setBillDeliverMark(bd.getBillDeliverMark());
//        });
//        // build detail information
//        BigInteger profileId = CurrentUser.getProfileId();
//        List<BillDeliverDetailDTO> allDetails = billDeliverDetailMapper.getMainDetailInfoByVchcodes(
//                profileId, deliverListByBatchId.stream().map(PrintBatchDeliverDTO::getVchcode).collect(Collectors.toList())
//        );
//        deliverService.buildDetailInfoForPrintBatch(profileId, deliverListByBatchId, allDetails, true);
//
//        return deliverListByBatchId;
//    }






    @ApiOperation(value = "波次配货完成检查", notes = "波次配货完成检查")
    @PostMapping("getPrintBatchCheckResults")
    public HashMap<String,List<BigInteger>> getPrintBatchCheckResults(@RequestBody List<BigInteger> printBatchIds) {

        HashMap<String,List<BigInteger>> resultMap=new HashMap<>();
        resultMap.put("ERROR",new ArrayList<>());
        resultMap.put("WARN",new ArrayList<>());
        resultMap.put("NORMAL",new ArrayList<>());
        for (BigInteger printBatchId : printBatchIds) {
            List<PrintBatchCheckResultDTO> printBatchCheckResults = this.printBatchService.getPrintBatchCheckResults(CurrentUser.getProfileId(), Arrays.asList(printBatchId));
            List<PrintBatchCheckResultDTO> errorList = printBatchCheckResults.stream().filter(p -> (p.getLockType() > 0 || p.getTradeState().equals(TradeStateEnum.ALL_CLOSED))).collect(Collectors.toList());
            if (errorList.size() > 0) {
                resultMap.get("ERROR").add(printBatchId);
                continue;
            }

            List<PrintBatchCheckResultDTO> continueCheckList = printBatchCheckResults.stream().filter(p -> p.getLockType() == 0 && !p.getTradeState().equals(TradeStateEnum.ALL_CLOSED)).collect(Collectors.toList());

            boolean flag = false;
            for (PrintBatchCheckResultDTO resultDTO : continueCheckList) {
                if(flag){
                    break;
                }
                // 同步失败
                boolean syncFail = resultDTO.getFreightSyncState().equals(FreightSyncStateEnum.SYNC_FAIL);
                BigInteger presetMark = resultDTO.getPresetMark();
                // 缺货订单
                boolean isLack = BitOperExtensions.isContain(presetMark, BillRemindEnum.LACK.getValue());
                // 未知订单
                boolean abnormal = resultDTO.getTradeState().equals(TradeStateEnum.ABNORMAL);
                RefundStateEnum refundState = resultDTO.getRefundState();
                // 部份退款中
                boolean refundingPart = refundState.equals(RefundStateEnum.REFUNDING_PART);
                // 部分退款成功
                boolean finishPart = refundState.equals(RefundStateEnum.FINISH_PART);
                // 全部退款中
                boolean refunding = refundState.equals(RefundStateEnum.REFUNDING);
                // 全部退款成功
                boolean finish = refundState.equals(RefundStateEnum.FINISH);

                if (syncFail || isLack || abnormal || refundingPart || finishPart || refunding || finish) {
                    flag = true;
                }
            }
            if(flag){
                resultMap.get("WARN").add(printBatchId);
            }else{
                resultMap.get("NORMAL").add(printBatchId);
            }
        }

        return resultMap;
    }
    @ApiOperation(value = "波次配货完成检查", notes = "波次配货完成检查")
    @PostMapping("getPrintBatchCheckResultsForAssign")
    public String getPrintBatchCheckResultsForAssign(@RequestBody List<BigInteger> printBatchIds) {
        List<PrintBatchCheckResultDTO> printBatchCheckResults = this.printBatchService.getPrintBatchCheckResults(CurrentUser.getProfileId(), printBatchIds);
        List<PrintBatchCheckResultDTO> batchErrorList = printBatchCheckResults.stream().filter(p -> (p.getAssignState()==AssignStateEnum.ASSIGN_FINISH || p.getAssignState()==AssignStateEnum.ASSIGN_HOLD ||p.getAssignState()==AssignStateEnum.ASSIGN_PART)).collect(Collectors.toList());
        if (batchErrorList.size() > 0) {
            return "BATCHERROR";
        }
        List<PrintBatchCheckResultDTO> errorList = printBatchCheckResults.stream().filter(p -> (p.getLockType() > 0 || p.getTradeState().equals(TradeStateEnum.ALL_CLOSED))).collect(Collectors.toList());
        if (errorList.size() > 0) {
            return "ERROR";
        }

        List<PrintBatchCheckResultDTO> continueCheckList = printBatchCheckResults.stream().filter(p -> p.getLockType() == 0 && !p.getTradeState().equals(TradeStateEnum.ALL_CLOSED)).collect(Collectors.toList());

        for (PrintBatchCheckResultDTO resultDTO : continueCheckList) {
            // 同步失败
            boolean syncFail = resultDTO.getFreightSyncState().equals(FreightSyncStateEnum.SYNC_FAIL);
            BigInteger presetMark = resultDTO.getPresetMark();
            // 缺货订单
            boolean isLack = BitOperExtensions.isContain(presetMark, BillRemindEnum.LACK.getValue());
            // 未知订单
            boolean abnormal = resultDTO.getTradeState().equals(TradeStateEnum.ABNORMAL);
            RefundStateEnum refundState = resultDTO.getRefundState();
            // 部份退款中
            boolean refundingPart = refundState.equals(RefundStateEnum.REFUNDING_PART);
            // 部分退款成功
            boolean finishPart = refundState.equals(RefundStateEnum.FINISH_PART);
            // 全部退款中
            boolean refunding = refundState.equals(RefundStateEnum.REFUNDING);
            // 全部退款成功
            boolean finish = refundState.equals(RefundStateEnum.FINISH);

            if (syncFail || isLack || abnormal || refundingPart || finishPart || refunding || finish) {
                return "WARN";
            }
        }

        return "NORMAL";
    }


    @ApiOperation(value = "通过波次id获取发货单", notes = "通过波次id获取发货单")
    @PostMapping("getDeliverListByBatchId")
    public List<DeliverDTO> getDeliverListByBatchId(@RequestBody List<BigInteger> printBatchIds) {
        return this.printBatchService.getDeliverDTOByPrintBatchId(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), printBatchIds);
    }


    @ApiOperation(value = "获取默认打印机配置的模板", notes = "获取默认打印机配置的模板")
    @PostMapping("getAssignPrinterName")
    public String getAssignPrinterName(@RequestBody BigInteger templateId) {
        return deliverBillMainSubPrintCommonService.getAssignPrinterName(templateId);
    }


    @ApiOperation(value = "获取订单打印物流单数据", notes = "获取订单打印物流单数据")
    @PostMapping("getLogisticPrintInfos")
    public List<DeliverFreightInfoDTO> getLogisticPrintInfos(@RequestBody List<BigInteger> vchcodes) {
        return deliverBillMainSubPrintCommonService.getLogisticPrintInfos(vchcodes);

    }

    @ApiOperation(value = "获取随机数", notes = "获取随机数")
    @PostMapping("getRandomUid")
    public BigInteger getRandomUid() {
        return UId.newId();

    }

    @ApiOperation(value = "获取验证码图片", notes = "获取验证码图片")
    @GetMapping("getConfirmCode")
    public void getConfirmCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        byte[] captchaChallengeAsJpeg = null;
        HashMap<String, BufferedImage> data = CaptchaUtil.genRandomCodeImage();
        String uid = request.getParameter("uid");
        String cookieName = MessageFormat.format("print_repeat_form_uid_{0}", uid);
        String code = data.entrySet().iterator().next().getKey();

        String key = commonService.saveCaptcha(code);
        Cookie cookie = new Cookie(cookieName, key);
        cookie.setMaxAge(60);
        cookie.setPath("/");
        response.addCookie(cookie);

        BufferedImage challenge = data.entrySet().iterator().next().getValue();
        ByteArrayOutputStream jpegOutputStream = new ByteArrayOutputStream();
        ImageIO.write(challenge, "jpg", jpegOutputStream);
        captchaChallengeAsJpeg = jpegOutputStream.toByteArray();
        response.setHeader("Cache-Control", "no-store");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setContentType("image/jpeg");
        ServletOutputStream responseOutputStream = response.getOutputStream();
        responseOutputStream.write(captchaChallengeAsJpeg);
        responseOutputStream.flush();
        responseOutputStream.close();
    }

    @ApiOperation(value = "验证随机数", notes = "验证随机数")
    @PostMapping("checkConfirmCode")
    public boolean checkConfirmCode(@RequestBody ConfirmCodeParam param) {
        return commonService.checkCaptcha(param.getConfirmKey(), param.getConfirmCode());
    }



    @ApiOperation(value = "修改波次物流信息", notes = "修改波次物流信息")
    @PostMapping("/updatePrintBatchFreightBtypeId")
    public void updatePrintBatchFreightBtypeId(@RequestBody DeliverPrintBatchDAO request) {
        printBatchService.updatePrintBatchFreightBtypeId(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), request);
    }

    public static class ProgressMessage implements Serializable {
        private Boolean finish;
        private String msg;
        private String data;

        public ProgressMessage(Boolean finish, String msg) {
            this.finish = finish;
            this.msg = msg;
        }

        public ProgressMessage(Boolean finish, String msg, String data) {
            this.finish = finish;
            this.msg = msg;
            this.data = data;
        }

        public Boolean getFinish() {
            return finish;
        }

        public void setFinish(Boolean finish) {
            this.finish = finish;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }
    }

    public static class CreatePrintBatchParam implements Serializable {
        private DeliverPrintBatchConfigDTO configDTO;
        private List<BigInteger> vchcodes;
        private List<List<BigInteger>> groupVchcodes;
        private String processId;

        public DeliverPrintBatchConfigDTO getConfigDTO() {
            return configDTO;
        }

        public void setConfigDTO(DeliverPrintBatchConfigDTO configDTO) {
            this.configDTO = configDTO;
        }

        public List<BigInteger> getVchcodes() {
            return vchcodes;
        }

        public void setVchcodes(List<BigInteger> vchcodes) {
            this.vchcodes = vchcodes;
        }

        public String getProcessId() {
            return processId;
        }

        public CreatePrintBatchParam setProcessId(String processId) {
            this.processId = processId;
            return this;
        }

        public List<List<BigInteger>> getGroupVchcodes() {
            return groupVchcodes;
        }

        public void setGroupVchcodes(List<List<BigInteger>> groupVchcodes) {
            this.groupVchcodes = groupVchcodes;
        }
    }


}
