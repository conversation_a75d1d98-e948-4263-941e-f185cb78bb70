package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.BaseProcessRequest;
import com.wsgjp.ct.sale.platform.enums.RefundTypeEnum;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2023/4/14 10:17
 * @Description: 批量新增售后单请求参数
 */
public class BatchAddRefundRequest extends BaseProcessRequest implements Serializable {

    private List<EshopRefundEntity> refundList;
    private RefundTypeEnum refundTypeEnum;

    public RefundTypeEnum getRefundTypeEnum() {
        return refundTypeEnum;
    }

    public void setRefundTypeEnum(RefundTypeEnum refundTypeEnum) {
        this.refundTypeEnum = refundTypeEnum;
    }

    public List<EshopRefundEntity> getRefundList() {
        return refundList;
    }

    public void setRefundList(List<EshopRefundEntity> refundList) {
        this.refundList = refundList;
    }
}
