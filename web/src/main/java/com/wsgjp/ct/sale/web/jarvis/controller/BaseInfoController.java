package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.baseinfo.core.dao.entity.Deliveryinfo;
import com.wsgjp.ct.bill.core.handle.entity.enums.Vchtypes;
import com.wsgjp.ct.common.enums.core.enums.FeatureEnum;
import com.wsgjp.ct.common.enums.core.enums.FullLinkStatusEnum;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.analysiscloud.util.Functions;
import com.wsgjp.ct.sale.biz.api.baseinfo.BaseInfoApi;
import com.wsgjp.ct.sale.biz.api.eshoporder.EShopOrderApi;
import com.wsgjp.ct.sale.biz.bifrost.mapper.BifrostEshopMapper;
import com.wsgjp.ct.sale.biz.bifrost.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.service.broadcast.EshopBroadcastService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.jarvis.common.HashMapCreater;
import com.wsgjp.ct.sale.biz.jarvis.config.MainConfig;
import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.dto.PlatformQualityInfo;
import com.wsgjp.ct.sale.biz.jarvis.dto.PlatformQualityOrg;
import com.wsgjp.ct.sale.biz.jarvis.dto.PtypePositionRelationDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.JResponse;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverMark;
import com.wsgjp.ct.sale.biz.jarvis.entity.PtypePosition;
import com.wsgjp.ct.sale.biz.jarvis.entity.PtypePositionRelationLog;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.*;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.DeliverInfoRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.PtypePositionRelationLogQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.PtypePositionRelationQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.entity.template.TemplateAndFreightDAO;
import com.wsgjp.ct.sale.biz.jarvis.entity.template.TemplateDAO;
import com.wsgjp.ct.sale.biz.jarvis.jarvisStatusList.GetDeliverBillStatusFace;
import com.wsgjp.ct.sale.biz.jarvis.jarvisStatusList.StatusContainer;
import com.wsgjp.ct.sale.biz.jarvis.service.*;
import com.wsgjp.ct.sale.biz.jarvis.service.freight.FreightLimitService;
import com.wsgjp.ct.sale.biz.jarvis.service.nav.NavStockService;
import com.wsgjp.ct.sale.biz.jarvis.service.template.TemplateManagementService;
import com.wsgjp.ct.sale.biz.jarvis.service.template.TemplateService;
import com.wsgjp.ct.sale.biz.jarvis.state.TemplateCodeEnum;
import com.wsgjp.ct.sale.biz.jarvis.state.TemplateTypeForQueryEnum;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillDeliverUtils;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.order.AgencyListItem;
import com.wsgjp.ct.sale.platform.dto.order.SubmitTemplate;
import com.wsgjp.ct.sale.platform.dto.order.WarehouseList;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.order.EshopGetSubmitTemplateFeature;
import com.wsgjp.ct.sale.platform.feature.order.EshopGetWarehouseAndAgencyListFeature;
import com.wsgjp.ct.sale.sdk.reason.entity.BillDeliverReasonDTO;
import com.wsgjp.ct.sale.sdk.reason.enums.ReasonStopTypeEnum;
import com.wsgjp.ct.sale.sdk.reason.enums.ReasonTypeEnum;
import com.wsgjp.ct.sale.web.jarvis.Entity.RetailHomeConfig;
import com.wsgjp.ct.sale.web.jarvis.request.*;
import com.wsgjp.ct.sale.web.jarvis.response.MapEntry;
import com.wsgjp.ct.sale.web.jarvis.response.QueryPtypePositionRelationImportProgressResponse;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.utils.RedisBizUtils;
import com.wsgjp.ct.support.utils.WebUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述 基本信息
 *
 * <AUTHOR>
 * @create 2019-12-27
 * @since 1.0.0
 */
@RestController
@Api(description = "订单查询基本信息")
@RequestMapping("/${app.id}/jarvis/baseInfo")
public class BaseInfoController {

    private static final Logger logger = LoggerFactory.getLogger(BaseInfoController.class);
    private static List<ShopType> onlineBuyerEshopType = new ArrayList<>();
    private MainConfig mainConfig;
    private BaseInfoService baseInfoService;

    private BaseInfoApi baseInfoApi;
    private EShopOrderApi eShopOrderApi;
    private PtypePositionRelationService positionRelationService;

    private TemplateService templateService;
    private CustomerQueryService customerQueryService;
    private NavStockService navStockService;
    private FreightLimitService freightLimitService;
    private BillDeliverMarkService deliverMarkService;
    private DeliverBillReasonService deliverBillReasonService;
    private DeliverBillLiveBroadcastService deliverBillLiveBroadcastService;
    private TemplateManagementService templateManagementService;
    private EshopBroadcastService eshopBroadcastService;
    private DeliverSpecialQueryService deliverSpecialQueryService;
    public BaseInfoController(BaseInfoService baseInfoService, BaseInfoApi baseInfoApi, TemplateService templateService,
                              PtypePositionRelationService positionRelationService, CustomerQueryService customerQueryService,
                              NavStockService navStockService, FreightLimitService freightLimitService,
                              BillDeliverMarkService deliverMarkService, EShopOrderApi eShopOrderApi, DeliverBillReasonService deliverBillReasonService,
                              DeliverBillLiveBroadcastService deliverBillLiveBroadcastService,
                              TemplateManagementService templateManagementService, EshopBroadcastService eshopBroadcastService,
                              MainConfig mainConfig,DeliverSpecialQueryService deliverSpecialQueryService) {
        this.baseInfoService = baseInfoService;
        this.baseInfoApi = baseInfoApi;
        this.positionRelationService = positionRelationService;

        this.templateService = templateService;
        this.customerQueryService = customerQueryService;
        this.navStockService = navStockService;
        this.freightLimitService = freightLimitService;
        this.deliverMarkService = deliverMarkService;
        this.eShopOrderApi = eShopOrderApi;
        this.deliverBillReasonService = deliverBillReasonService;
        this.deliverBillLiveBroadcastService = deliverBillLiveBroadcastService;
        this.templateManagementService = templateManagementService;
        this.eshopBroadcastService = eshopBroadcastService;
        this.mainConfig = mainConfig;
        this.deliverSpecialQueryService=deliverSpecialQueryService;
    }



    @RequestMapping("isOpenWMSConfig")
    public boolean isOpenWMSConfig() {
        return baseInfoService.isOpenWMSConfig();
    }

    @RequestMapping("mainConfig")
    public RetailHomeConfig mainConfig(){
        RetailHomeConfig config = new RetailHomeConfig();
        config.setEnable(this.mainConfig.isEnable());
        return config;
    }


    @RequestMapping("getOnlineBuyerEshopType")
    public void getOnlineBuyerEshopType() {
        List<Integer> result = this.eShopOrderApi.getShopTypeByFeatureName(FeatureEnum.MODIFY_RECEIVER.getFeature()).getData();
        if (result == null || result.size() <= 0) {
            return;
        }
        for (Integer value : result) {
            onlineBuyerEshopType.add(ShopType.valueOf(value));
        }
    }

    @ApiOperation(value = "获取指定类型的地址列表", notes = "（）")
    @PostMapping("deliveryInfos")
    public PageResponse<Deliveryinfo> deliveryInfos(@RequestBody PageRequest<DeliverInfoRequest> request) {
        return baseInfoService.deliveryInfos(request);
    }
    @ApiOperation(value = "保存地址", notes = "（）")
    @PostMapping("deliveryInfoSave")
    public Deliveryinfo deliveryInfoSave(@RequestBody Deliveryinfo request) {
        return baseInfoService.saveDeliveryInfo(request);
    }




    @ApiOperation(value = "获取属性", notes = "（）")
    @PostMapping("getProps")
    public List<Prop> getPropList() {
        List<Prop> result = baseInfoApi.listProps().getData().getList();
        return result;
    }

    @ApiOperation(value = "获取网店", notes = "（）")
    @PostMapping("getEShops")
    public List<Organization> getEShopList() throws Exception {
        List<Organization> eshops = baseInfoService.getEShopList(CurrentUser.getProfileId());
        return eshops;
    }

    @ApiOperation(value = "获取物流公司", notes = "（）")
    @PostMapping("getFreights")
    public List<Freight> getFreightList() throws Exception {
        return baseInfoService.getFregihtList(CurrentUser.getProfileId());
    }
    @ApiOperation(value = "获取包含停用物流公司", notes = "（）")
    @PostMapping("getAllFregihtList")
    public List<Freight> getAllFregihtList() throws Exception {
        List<Freight> result=  baseInfoService.getAllFregihtList(CurrentUser.getProfileId());
        if(CollectionUtils.isNotEmpty(result)){
            result.forEach(p -> {
                if (p.isStoped()) {
                    p.setFullName(String.format("(停用)%s", p.getFullName()));
                }
            });
        }
        return result;
    }
    // 目前不获取往来单位数据源，前端展示直接关联表展示往来单位名称。如果要添加往来单位查询使用基本信息组提供的往来单位控件
    @ApiOperation(value = "获取往来单位", notes = "（）")
    @PostMapping("getBtypes")
    public List<Btype> getBtypeList() {
        return baseInfoService.getBtypeList();
    }

    @ApiOperation(value = "获取仓库", notes = "（）")
    @PostMapping("getStocks")
    public List<Stock> getStockList() {
        return baseInfoService.getKTypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
    }

    @ApiOperation(value = "获取非停用仓库", notes = "（）")
    @PostMapping("getStockListNotStop")
    public List<Stock> getStockListNotStop() {
        return baseInfoService.getKTypesLimitNotStop(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
    }

    @ApiOperation(value = "获取职员", notes = "（）")
    @PostMapping("getEmployees")
    public List<Employee> getEmployeeList() {
        return baseInfoService.getETypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
    }

    @ApiOperation(value = "获取销售机构", notes = "（）")
    @PostMapping("getOrganizations")
    public List<Organization> getOrganizationList() {
        return baseInfoService.getEshopOrganizationsLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
    }

    @ApiOperation(value = "获取物流公司", notes = "（）")
    @PostMapping("getFreightTemplateList")
    public List<TemplateDAO> getFreightTemplateList() {
        return this.templateService.getTemplatesByNotStop(CurrentUser.getProfileId());

    }

    @ApiOperation(value = "获取简化流程", notes = "（）")
    @PostMapping("getSimpleProcess")
    public boolean getSimpleProcess() {
        return BillDeliverUtils.isOpenSimpleProcess();
    }


    public static class GetDeliverBillStatusParam implements Serializable, GetDeliverBillStatusFace {
        private String operationType;
        private String pageName;
        private TemplateTypeForQueryEnum templateType;
        private TemplateCodeEnum templateCode;
        private Functions.ActionOne<HashMap<String, Object>> action;

        public String getOperationType() {
            return operationType;
        }

        public void setOperationType(String operationType) {
            this.operationType = operationType;
        }

        public String getPageName() {
            return pageName;
        }

        public void setPageName(String pageName) {
            this.pageName = pageName;
        }

        public TemplateTypeForQueryEnum getTemplateType() {
            return templateType;
        }

        public void setTemplateType(TemplateTypeForQueryEnum templateType) {
            this.templateType = templateType;
        }

        public TemplateCodeEnum getTemplateCode() {
            return templateCode;
        }

        @Override
        public void initYourData(HashMap<String, Object> result) throws Exception {
            if (action != null){
                action.invoke(result);
            }
        }

        public void setTemplateCode(TemplateCodeEnum templateCode) {
            this.templateCode = templateCode;
        }
    }

    @ApiOperation(value = "获取状态值", notes = "（）")
    @PostMapping("getStatus")
    public HashMap<String, Object> getDeliverBillStatus(@RequestBody GetDeliverBillStatusParam param) throws Exception {
        HashMap<String, Object> result = HashMapCreater.create(45);
        param.action = stringObjectHashMap -> {
            try{
                result.put("flags", getFlag());
            }catch (Exception exception) {
                throw new RuntimeException(exception);
            }
        };
        // 找 pageName 对应的 bean
        StatusContainer.initStatus(param.getPageName(), result, param);
        if ("eshopSendDetails".equalsIgnoreCase(param.getPageName())) {
            PubSystemLogService.saveInfo("进入【网店发货明细表】");
            //商品标签
            List<PtypeLabel> ptypeLabels = baseInfoService.getPtypeLabels(CurrentUser.getProfileId());
            result.put("ptypeLabels", ptypeLabels);
        }
        if ("eshopSendBills".equalsIgnoreCase(param.getPageName())) {
            PubSystemLogService.saveInfo("进入【网店发货统计】");
        }

        if ("DistributionGrossProfitStatistics".equalsIgnoreCase(param.getPageName())) {
            PubSystemLogService.saveInfo("进入【分销毛利统计】");
        }

        if ("DistributionGrossProfitStatisticsDetail".equalsIgnoreCase(param.getPageName())) {
            PubSystemLogService.saveInfo("进入【分销毛利统计明细】");
        }

        return result;
    }
    private void addFullLinkStatusForSearch(HashMap result){
        if(CollectionUtils.isEmpty(result)){
            result = HashMapCreater.create(45);
        }
        List<DillDeliverState> fullLinkStatus=baseInfoService.getDeliverBillStatus(FullLinkStatusEnum.class);
        result.put("fullLinkStatus", fullLinkStatus);
        List<DillDeliverState>  fullLinkStatusForSearch=new ArrayList<>();
        for (int i = 0; i < fullLinkStatus.size(); i++) {
            DillDeliverState status = fullLinkStatus.get(i);
            DillDeliverState newStatus=new DillDeliverState();
            newStatus.setDescription(status.getDescription());
            if(status.getCode().equals(120)||status.getCode().equals(121) || status.getCode().equals(170)){
                // 部分财务核算的选项合并
                continue;
            }else {
                newStatus.setKey(status.getCode().toString());
            }
            fullLinkStatusForSearch.add(newStatus);
        }
//        fullLinkStatus.forEach(status->{
//            DillDeliverState newStatus=new DillDeliverState();
//            newStatus.setDescription(status.getDescription());
//            if(status.getCode().equals(100)){
//                newStatus.setKey("100,120,121,");
//            }else if(status.getCode().equals(120)||status.getCode().equals(121)){
//                return;
//            }else {
//                newStatus.setKey(status.getCode().toString());
//            }
//            fullLinkStatusForSearch.add(newStatus);
//        });
        result.put("fullLinkStatusForSearch", fullLinkStatusForSearch);
    }
    /**
     * 获取-单据类型
     */
    private List<DillDeliverState> getBillType(){
        List<DillDeliverState> list = new ArrayList<>();
        list.add(buildBillType(Vchtypes.SaleBill));
        list.add(buildBillType(Vchtypes.SingleOutStockBill));
        list.add(buildBillType(Vchtypes.BuyBackBill));
        return list;
    }
    private DillDeliverState buildBillType(Vchtypes obj){
        DillDeliverState state = new DillDeliverState();
        state.setCode(obj.getVchtype());
        state.setKey(obj.name());
        state.setDescription(obj.getFullname());
        return state;
    }

    private List<DillDeliverState> getDeliverStates() throws Exception {
        return Collections.emptyList();
    }

//    private List<Supplier> getAllSupplierList(BigInteger profileId) {
//        return baseInfoService.getAllSupplierList(profileId);
//    }
//    private List<Supplier> getLimitSupplierList(BigInteger profileId,BigInteger employeeId) {
//        return baseInfoService.getLimitSupplierList(profileId, employeeId);
//    }

    private List<Employee> listEmplyees(List<Employee> employees) {
        Optional<Employee> detailOptional = employees.stream().filter(item -> item.getId().equals(CurrentUser.getEmployeeId())).findFirst();
        if (detailOptional.isPresent()) {
            Employee employee = detailOptional.get();
            employees.remove(employee);
            employees.add(0, employee);
        }
        return employees;
    }

    private boolean isOpenSortField() {
        return true;
    }

    @ApiOperation(value = "获取订单查询条件", notes = "（）")
    @PostMapping("getQueryFilterType")
    public List<DillDeliverState> getQueryFilterType(@RequestParam String operationType) throws Exception {
        return baseInfoService.getQueryFilterType(operationType);
    }
    @ApiOperation(value = "获取网店发货明细表查询条件", notes = "（）")
    @PostMapping("getQueryFilterTypeForSendDetail")
    public List<DillDeliverState> getQueryFilterTypeForEshopSend(@RequestParam String operationType) throws Exception {
        return baseInfoService.getQueryFilterTypeForSendDetail(operationType);
    }

    @ApiOperation(value = "获取订单标记", notes = "（）")
    @PostMapping("getDeliverMark")
    public List<DeliverMark> getDeliverMark() throws Exception {
        return deliverMarkService.getAllDeliverMark(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "获取订单标记", notes = "（）")
    @PostMapping("getBaseUserMark")
    public List<DeliverMark> getBaseUserMark() throws Exception {
        return deliverMarkService.getBaseUserMark(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "获取系统提醒", notes = "（）")
    @PostMapping("getBaseOrderMark")
    public List<DeliverMark> getBaseOrderMark() throws Exception {
        return deliverMarkService.getBaseOrderMark();
    }

    @ApiOperation(value = "获取平台标记", notes = "（）")
    @PostMapping("getPlatformDeliverMark")
    public List<DeliverMark> getPlatformDeliverMark() throws Exception {
        return deliverMarkService.getPlatformDeliverMark(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "加载旗帜", notes = "（）")
    @PostMapping("getFlag")
    public List<MapEntry> getFlag() throws Exception {
        List<MapEntry> flags = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            MapEntry flag = new MapEntry(i, "/sale/jarvis/skins/img/flag/flag" + i + ".png");
            flags.add(flag);
        }
        return flags;
    }

//    @ApiOperation(value = "获取物流公司", notes = "（）")
//    @PostMapping("listFreights")
//    public List<FreightInfoDTO> listFreights(@RequestBody BaseInfoGetRequest<FreightInfoParams> FreightInfoParams) {
//        //FreightInfoParams.getQueryParams().setBtypeType("onlyfreight");
//        List<FreightInfoDTO> result = baseInfoApi.listFreights(FreightInfoParams).getData().getList();
//        return result;
//    }

    @ApiOperation(value = "获取发货单样式", notes = "（）")
    @PostMapping("getDeliverTemplates")
    public List<TemplateDAO> getDeliverTemplates() {
        return this.templateService.getDeliverTemplate(CurrentUser.getProfileId(), "deliverySendDetail");
    }

    @ApiOperation(value = "获取商品货位关系绑定", notes = "（）")
    @PostMapping("listPtypePositionRelations")
    public PageResponse<PtypePositionRelationDTO> listPtypePositionRelations(@RequestBody PageRequest<PtypePositionRelationQueryParams> request) {
        return positionRelationService.listPtypePositionRelations(request);
    }

    @ApiOperation(value = "获取商品货位关系绑定日志", notes = "（）")
    @PostMapping("listPtypePositionRelationLogs")
    public PageResponse<PtypePositionRelationLog> listPtypePositionRelationLogs(@RequestBody PageRequest<PtypePositionRelationLogQueryParams> request) {
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        if (!CurrentUser.isAdmin()) {
            request.getQueryParams().setEtypeId(CurrentUser.getEmployeeId());
        }
        return LogService.query(request);
    }

    @ApiOperation(value = "获取当前操作员", notes = "（）")
    @PostMapping("getCurrentEmployee")
    public BigInteger getCurrentEmployee() {
        return CurrentUser.getEmployeeId();
    }

    @ApiOperation(value = "扫描绑定商品货位", notes = "（）")
    @PostMapping("ptypePositionBind")
    public JResponse ptypePositionBind(@RequestBody PtypePositionBindRequest request) {
        return positionRelationService.bind(
                request.getPtypeId(), request.getSkuId(), request.getKtypeId(),
                request.getPosition(), request.isForce()
        );
    }

    @ApiOperation(value = "界面批量绑定货位")
    @PostMapping("/ptypePosition/batchBind")
    public JResponse batchBindPtypePosition(@RequestBody BatchBindPositionRequest batchBindPositionRequest) {
        return positionRelationService.batchBind(
                CurrentUser.getProfileId(), batchBindPositionRequest.getSkuId(),
                batchBindPositionRequest.getKtypeId(), batchBindPositionRequest.getRelations()
        );
    }

    @ApiOperation(value = "设置默认货位")
    @PostMapping("/ptypePosition/bindDefaultPosition")
    public JResponse bindDefaultPosition(@RequestBody DefaultPositionRequest defaultPositionRequest) {
        return positionRelationService.bindDefaultPosition(
                defaultPositionRequest.getRelationId(),
                defaultPositionRequest.getKtypeId(),
                defaultPositionRequest.getDefaultPositionPresent()
        );
    }

    @ApiOperation(value = "解除绑定商品货位", notes = "（）")
    @PostMapping("unbindPtypePositionRelations")
    public JResponse unbindPtypePositionRelations(@RequestBody UnbindPositionRequest unbindPositionRequest) {
        return positionRelationService.unbindPosition(unbindPositionRequest.getRelationIds(), unbindPositionRequest.getKtypeId());
    }

    @ApiOperation(value = "根据关键字获取商品列表", notes = "（）")
    @GetMapping("/searchPtypes")
    public JResponse searchPtypes(String keyword, BigInteger ktypeId) {
        return positionRelationService.searchPtypes(keyword, ktypeId);
    }

    @ApiOperation(value = "获取货位Id", notes = "（）")
    @GetMapping("getPositionId")
    public PtypePosition getPositionId(String positionNo, BigInteger ktypeId) {
        return positionRelationService.getPositionId(ktypeId, positionNo);
    }

    @ApiOperation(value = "获取商品和货位关系")
    @PostMapping("/fetchPositions")
    public List<PtypePositionRelationDTO> fetchPtypePositionRelations(@RequestBody PtypePositionRelationRequest relationRequest) {
        return positionRelationService.listSkuRelations(
                CurrentUser.getProfileId(), relationRequest.getKtypeId(), relationRequest.getSkuIds()
        );
    }

    @ApiOperation(value = "下载商品货位关系导入模板", notes = "（）")
    @GetMapping("downloadExcelTemplate")
    public void downloadExcelTemplate(String name) {
        try {
            String filename = String.format("classpath*:**/excelTemplate/%s", name);
            ResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();
            Resource[] templateFiles = resourceResolver.getResources(filename);
            if (templateFiles.length <= 0) {
                throw new RuntimeException("未找到对应模板，请确认文件名称是否正确");
            }
            for (Resource resource : templateFiles) {
                try (InputStream inputStream = resource.getInputStream()) {
                    WebUtils.download(name, inputStream);
                    break;
                }
            }

        } catch (Exception e) {
            logger.error("下载模板出错：" + e.getMessage(), e);
            throw new RuntimeException("下载模板出错，请联系管理员", e);
        }

    }

    @ApiOperation(value = "商品货位关系绑定模板下载", notes = "（）")
    @GetMapping("getPtypePositionRelationTemplateDownloadUrl")
    public String getPtypePositionRelationTemplateDownloadUrl(BigInteger ktypeId) {
        return positionRelationService.fetchTemplateDownloadUrl(ktypeId);
    }

    @ApiOperation(value = "批量导入商品货位关系", notes = "（）")
    @PostMapping("importPtypePositionRelation")
    public String importPtypePositionRelation(@RequestParam("file") MultipartFile file) {
        return positionRelationService.importPtypePositionRelations(file);
    }

    @ApiOperation(value = "查询商品货位导入进度", notes = "（）")
    @GetMapping("queryPtypePositionRelationImportProgress")
    public QueryPtypePositionRelationImportProgressResponse queryPtypePositionRelationImportProgress(BigInteger messageId) {
        String url = RedisBizUtils.get("ptypeRelationError:" + messageId);
        QueryPtypePositionRelationImportProgressResponse response = new QueryPtypePositionRelationImportProgressResponse();
        if (url != null) {
            response.setFinished(true);
            response.setErrored(StringUtils.isNotEmpty(url));
            response.setUrl(url);
        } else {
            response.setFinished(false);
            response.setErrored(false);
        }
        return response;
    }

    @GetMapping("entryPtypePositionRelation")
    @ApiOperation(value = "增加进入商品货位绑定界面日志", notes = "（）")
    public boolean entryPtypePositionRelation() {
        PubSystemLogService.saveInfo("进入商品货位绑定");
        return true;
    }

    @ApiOperation(value = "查询物流模板", notes = "（）")
    @PostMapping("getSupportTemplates")
    public List<TemplateAndFreightDAO> getSupportTemplates(@RequestBody FreightLimitRequest request) {
        List<TemplateAndFreightDAO> templates = this.templateService.getAllTemplates(CurrentUser.getProfileId());
        List<TemplateAndFreightDAO> validTemplates = new ArrayList<>();
        if (request.getEshopIds() == null || request.getEshopIds().size() == 0) {
            return templates;
        }
        List<Organization> eshops = baseInfoService.getAllEshopsByIds(CurrentUser.getProfileId(), request.getEshopIds());
        if (eshops == null || eshops.size() == 0) {
            return templates;
        }
        List<BigInteger> markCodeList = request.getMarkCodeList();
        for (TemplateAndFreightDAO template : templates) {
            template.setOriginalTemplateName(template.getTemplateName());
            template.setTemplateName(template.getTemplateName());
            for (Organization eshop : eshops) {
                if (!freightLimitService.isSupport(eshop.getEshopType(), template.getFreightTemplateType(), template.getUserCode())) {
                    template.setEnable(false);
                    break;
                }
                if(CollectionUtils.isNotEmpty(markCodeList)){
                    List<BigInteger> caiNiaoList = markCodeList.stream()
                            .filter(m -> m.compareTo(new BigInteger("200047")) == 0)
                            .collect(Collectors.toList());
                    //模板枚举：FreightTemplateTypeEnum
                    if (CollectionUtils.isNotEmpty(caiNiaoList) && template.getFreightTemplateType()!=0) {
                        template.setEnable(false);
                        break;
                    }
                }

            }
            validTemplates.add(template);
        }
        return validTemplates;
    }

    @ApiOperation(value = "查询网店", notes = "（）")
    @PostMapping("getSupportEShops")
    public List<Organization> getSupportEShops(@RequestBody FreightLimitRequest request) {
        List<Organization> eshops = baseInfoService.getAllEshopOrganizations(CurrentUser.getProfileId());
        List<Organization> validEshops = new ArrayList<>();
        if (request.getTemplateIds() == null || request.getTemplateIds().size() == 0) {
            return eshops;
        }
        List<TemplateAndFreightDAO> templates = this.templateService.getAllTemplatesByIds(CurrentUser.getProfileId(), request.getTemplateIds());
        if (templates == null || templates.size() == 0) {
            return eshops;
        }
        for (Organization eshop : eshops) {
            boolean isSupport = true;
            for (TemplateAndFreightDAO template : templates) {
                if (!freightLimitService.isSupport(eshop.getEshopType(), template.getFreightTemplateType(), template.getUserCode())) {
                    eshop.setFullname(String.format("%s（%s）", eshop.getFullname(), ShopType.valueOf(eshop.getEshopType()).getName()));
                    eshop.setEnable(false);
                    break;
                }
            }
            validEshops.add(eshop);
        }
        return validEshops;
    }


    public static class FreightLimitRequest {
        private List<BigInteger> eshopIds;
        private List<BigInteger> templateIds;

        private List<BigInteger> markCodeList;

        public List<BigInteger> getMarkCodeList() {
            return markCodeList;
        }

        public void setMarkCodeList(List<BigInteger> markCodeList) {
            this.markCodeList = markCodeList;
        }

        public List<BigInteger> getEshopIds() {
            return eshopIds;
        }

        public void setEshopIds(List<BigInteger> eshopIds) {
            this.eshopIds = eshopIds;
        }

        public List<BigInteger> getTemplateIds() {
            return templateIds;
        }

        public void setTemplateIds(List<BigInteger> templateIds) {
            this.templateIds = templateIds;
        }
    }
    @ApiOperation(value = "交易单操作原因表", notes = "（）")
    @PostMapping("getBillDeliverReason")
    public Map<ReasonTypeEnum, List<BillDeliverReasonDTO>> getBillDeliverReason()
    {
        List<BillDeliverReasonDTO> billDeliverReasonDTOS = deliverBillReasonService.queryReason(null, ReasonStopTypeEnum.NORMAL);
        return billDeliverReasonDTOS.stream().
                collect(Collectors.groupingBy(BillDeliverReasonDTO::getReasonType));
    }

    @ApiOperation(value = "交易单操作原因表", notes = "（）")
    @PostMapping("initReasonDataSource")
    public HashMap initReasonDataSource() {
        HashMap result = HashMapCreater.create(2);
        //截停说明、驳回原因
        Map<ReasonTypeEnum, List<BillDeliverReasonDTO>> billDeliverReason = getBillDeliverReason();
        List<BillDeliverReasonDTO> billDeliverReasonDTOS = billDeliverReason.get(ReasonTypeEnum.AUTO_LOCK);
        if (CollectionUtils.isNotEmpty(billDeliverReasonDTOS)) {
            List<BillDeliverReasonDTO> lock = billDeliverReason.getOrDefault(ReasonTypeEnum.LOCK, new ArrayList<>());
            lock.addAll(billDeliverReasonDTOS);
            billDeliverReason.put(ReasonTypeEnum.LOCK, lock);
        }
        result.put("lockReason", billDeliverReason.getOrDefault(ReasonTypeEnum.LOCK, Collections.emptyList()));
        result.put("returnReason", billDeliverReason.getOrDefault(ReasonTypeEnum.RETURN, Collections.emptyList()));
        return result;
    }

    @ApiOperation(value = "获取一个uid", notes = "（）")
    @PostMapping("uid")
    public BigInteger uid() {
        return UId.newId();
    }

    @ApiOperation(value = "获取抖店质检机构", notes = "（）")
    @PostMapping("getPlatformQualityOrg")
    public List<PlatformQualityOrg> getPlatformQualityOrg(@RequestBody BigInteger eshopId) {
        BifrostEshopMapper eshopMapper = GetBeanUtil.getBean(BifrostEshopMapper.class);
        EshopInfo eshopInfo = eshopMapper.getEshopInfoByOtypeId(CurrentUser.getProfileId(), eshopId);
        EshopSystemParams systemParams = CommonUtil.toSystemParams(eshopInfo);
        ShopType shopType = systemParams.getShopType();
        EshopFactory factory = EshopFactoryManager.create(shopType, systemParams);
        EshopGetWarehouseAndAgencyListFeature feature = factory.getFeature(EshopGetWarehouseAndAgencyListFeature.class);
        List<WarehouseList> qualities = feature.getWarehouseAndAgencyList();
        List<PlatformQualityOrg> result = new ArrayList<>();
        for (WarehouseList quality:qualities) {
            if(CollectionUtils.isEmpty(quality.getAgencyListItems()))
            {
                continue;
            }
            for (AgencyListItem agency:quality.getAgencyListItems()) {
                PlatformQualityOrg qualityQrg = new PlatformQualityOrg();
                qualityQrg.setPlatformQualityWarehouseCode(quality.getWarehouseCode());
                qualityQrg.setPlatformQualityWarehouseName(quality.getWarehouseName());
                qualityQrg.setReceiverInfo(quality.getReceiverInfo());
                qualityQrg.setPlatformQualityOrgId(agency.getAgencyId().toString());
                qualityQrg.setPlatformQualityOrgName(agency.getAgencyName());
                result.add(qualityQrg);
            }
        }
        return result;
    }
    @ApiOperation(value = "获取质检机构", notes = "（）")
    @PostMapping("getPlatformQuality")
    public PlatformQualityInfo getPlatformQuality(@RequestBody BigInteger eshopId) {
        return getPlatformQualityCore(eshopId,false);
    }
    private PlatformQualityInfo getPlatformQualityCore(BigInteger eshopId,boolean isTwice) {
        PlatformQualityInfo result=new PlatformQualityInfo();
        BifrostEshopMapper eshopMapper = GetBeanUtil.getBean(BifrostEshopMapper.class);
        EshopInfo eshopInfo = eshopMapper.getEshopInfoByOtypeId(CurrentUser.getProfileId(), eshopId);
        EshopSystemParams systemParams = CommonUtil.toSystemParams(eshopInfo);
        ShopType shopType = systemParams.getShopType();
        EshopFactory factory = EshopFactoryManager.create(shopType, systemParams);
        EshopGetWarehouseAndAgencyListFeature feature = factory.getFeature(EshopGetWarehouseAndAgencyListFeature.class);
        try{
            List<WarehouseList> qualities = feature.getWarehouseAndAgencyList();
            result.setWarehouseList(qualities);
        }catch (Exception e){
            result.setWarehouseList(new ArrayList<>());
        }
        try {
            EshopGetSubmitTemplateFeature btypeFeature = factory.getFeature(EshopGetSubmitTemplateFeature.class);
            SubmitTemplate submitTemplate = btypeFeature.getSubmitTemplateList(isTwice);
            result.setDeliveryList(null!=submitTemplate&&null!=submitTemplate.getDeliveryList()&& !submitTemplate.getDeliveryList().isEmpty() ?submitTemplate.getDeliveryList():new ArrayList<>());
            if(CollectionUtils.isNotEmpty(submitTemplate.getInspectOrgList())){
                for (WarehouseList warehouseItem:result.getWarehouseList()
                ) {
                    warehouseItem.setAgencyListItems(new ArrayList<>());
                    for(SubmitTemplate.InspectOrgList inspectOrgList:submitTemplate.getInspectOrgList()){
                        AgencyListItem agencyListItem=new AgencyListItem();
                        agencyListItem.setAgencyId(inspectOrgList.getID());
                        agencyListItem.setAgencyName(inspectOrgList.getName());
                        agencyListItem.setAgencyShortName(inspectOrgList.getName());
                        warehouseItem.getAgencyListItems().add(agencyListItem);
                    }
                }
            }
        }catch (Exception ex){
            result.setDeliveryList(new ArrayList<>());
        }
//        rebuildTestDeliverList(result,shopType);
        return result;
    }
    @ApiOperation(value = "获取质检机构", notes = "（）")
    @PostMapping("getTwicePlatformQuality")
    public PlatformQualityInfo getTwicePlatformQuality(@RequestBody BigInteger eshopId) {
        return getPlatformQualityCore(eshopId,true);
    }
    private void rebuildTestDeliverList(PlatformQualityInfo result,ShopType shopType){
        result.setDeliveryList(new ArrayList<>());
        SubmitTemplate.DeliveryList delivery1=new SubmitTemplate.DeliveryList();
        delivery1.setID("STO");
        delivery1.setName("申通快递");
        delivery1.setDeliveryProducts(new ArrayList<>());
        SubmitTemplate.DeliveryProduct deliveryProduct1=new SubmitTemplate.DeliveryProduct();
        deliveryProduct1.setID("1");
        deliveryProduct1.setName("申通快递");
        deliveryProduct1.setEnableInsure(2L);
        deliveryProduct1.setInsureTypeList(new ArrayList<>());
        delivery1.getDeliveryProducts().add(deliveryProduct1);
        result.getDeliveryList().add(delivery1);
        SubmitTemplate.DeliveryList delivery2=new SubmitTemplate.DeliveryList();
        delivery2.setID("SF");
        delivery2.setName("顺丰速运");
        delivery2.setDeliveryProducts(new ArrayList<>());
        SubmitTemplate.DeliveryProduct deliveryProduct2=new SubmitTemplate.DeliveryProduct();
        deliveryProduct2.setID("2");
        deliveryProduct2.setName("顺丰标快");
        deliveryProduct2.setEnableInsure(1L);
        deliveryProduct2.setInsureTypeList(new ArrayList<>());
        SubmitTemplate.InsureTypeList insureType2=new SubmitTemplate.InsureTypeList();
        insureType2.setID("INSURE");
        insureType2.setName("基础保");
        insureType2.setUpperLimitType(1L);
        insureType2.setUpperLimitAmount(BigDecimal.valueOf(1000));
        deliveryProduct2.getInsureTypeList().add(insureType2);
        delivery2.getDeliveryProducts().add(deliveryProduct2);
        SubmitTemplate.DeliveryProduct deliveryProduct3=new SubmitTemplate.DeliveryProduct();
        deliveryProduct3.setID("247");
        deliveryProduct3.setName("顺丰电商标快");
        deliveryProduct3.setEnableInsure(2L);
        deliveryProduct3.setInsureTypeList(new ArrayList<>());
        delivery2.getDeliveryProducts().add(deliveryProduct3);
        result.getDeliveryList().add(delivery2);
    }
}


