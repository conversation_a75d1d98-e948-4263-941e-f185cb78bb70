package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopDistributorService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.middleground.BtypeEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.RefreshBtypeParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BusinessResponse;
import com.wsgjp.ct.sale.biz.eshoporder.exception.DistributorException;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.middleground.EshopMiddleGroundService;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.dto.distributor.DistributorInfo;
import com.wsgjp.ct.sale.platform.entity.request.distributor.DistributorRequest;
import com.wsgjp.ct.sale.platform.entity.response.distributor.DistributorResponse;
import com.wsgjp.ct.sale.platform.enums.BusinessResolveType;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.distributor.EshopDistributorFeature;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022-07-21
 * @Description
 */
@Api(value = "/business", tags = "供应链中台分销商相关")
@RequestMapping("${app.id}/eshoporder/business")
@RestController
public class DistributorController {
    private final Logger logger = LoggerFactory.getLogger(DistributorController.class);
    private final EshopService eshopService;
    private final EshopMiddleGroundService eshopMiddleGroundService;
    private final BifrostEshopDistributorService bifrostEshopDistributorService;

    public DistributorController(EshopService eshopService, EshopMiddleGroundService eshopMiddleGroundService, BifrostEshopDistributorService bifrostEshopDistributorService) {
        this.eshopService = eshopService;
        this.eshopMiddleGroundService = eshopMiddleGroundService;
        this.bifrostEshopDistributorService = bifrostEshopDistributorService;
    }

    /**
     * 分销商同步至中台
     * @param type:
     * @return com.wsgjp.ct.sale.biz.eshoporder.entity.response.BusinessResponse
     */
    @ApiOperation(value = "同步分销商至中台")
    @RequestMapping("/resolveBusiness")
    public BusinessResponse resolveBusiness(BusinessResolveType type){
        BusinessResponse resp = resp = new BusinessResponse();
        try {
            // 获取分销商信息并构建
            List<DistributorInfo> btypeList = getBtypeInfoList(type.getCode());
            // 筛选配置订货商城店铺
            List<EshopInfo> eshopList = getEnableMiddleGroundEshops();

            DistributorResponse apiResp =new DistributorResponse();
            // type-同步至中台
            for(EshopInfo eshopInfo : eshopList){
                // 构建接口请求
                DistributorRequest distributorRequest = eshopMiddleGroundService.buildDistributorRequest(eshopInfo);
                List<DistributorInfo> needUploadBtypeList=btypeList.stream().filter(x->null==eshopInfo.getLastUploadBusinessTime()||x.getUpdateTime().after(eshopInfo.getLastUploadBusinessTime())).collect(Collectors.toList());
                if (null==needUploadBtypeList||needUploadBtypeList.size()==0){
                    apiResp.setCode(200);
                    apiResp.setMessage("没有需要上传的分销商-updatetime");
                }
                distributorRequest.setDistributorInfos(needUploadBtypeList);
                logger.info(String.format("【profileId：%s】，同步至供应链中台请求：%s", CurrentUser.getProfileId(), JsonUtils.toJson(distributorRequest)));
                apiResp = syncToMidlleGround(type, distributorRequest);
                logger.info(String.format("【profileId：%s】，同步至供应链中台请求结果：%s", CurrentUser.getProfileId(), JsonUtils.toJson(apiResp)));
                if(!apiResp.getSuccess()){
                    resp.setSuccess(false);
                    resp.setMessage(null == apiResp.getMessage() ? "接口无报错信息返回" : apiResp.getMessage());
                }
                else
                {
                    // 同步最新更新时间
                    eshopInfo.setLastUploadBusinessTime(new Date());
                    eshopService.updateEShopBusinessTime(eshopInfo);
                }
            }
        } catch (DistributorException e) {
            logger.error(String.format("【profileId：】%s, 同步至中台出错：%s，出错原因：%s", CurrentUser.getProfileId(), e.getErrMsg(), e.getMessage()));
            resp.setSuccess(false);
            resp.setMessage(String.format("报错原因：%s， 报错信息：%s", e.getErrMsg(), e.getMessage()));
            return resp;
        }
        resp.setSuccess(true);
        return resp;
    }

    /**
     * 刷新客户/供货商
     * @param params:
     * @return String taskId
     *
     */
    @ApiOperation(value = "刷新客户/供货商")
    @RequestMapping("/refreshPlatformBtype")
    public String refreshPlatformBtype(@RequestBody RefreshBtypeParams params){
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        params.setTaskId(taskId);
        params.setProcessLogger(processLogger);
        processLogger.appendMsg("接到请求，正在处理");
        ThreadPool test = ThreadPoolFactory.build("refresh-ptype");
        test.executeAsync(invoker -> {
            eshopMiddleGroundService.refreshPlatformBtype(params);
        }, "刷新客户/供应商");
        return taskId;
    }


    /**
     * 获取分销商信息
     * @return void
     */
    private List<DistributorInfo> getBtypeInfoList(int type) {
        try {
            List<BtypeEntity> btypeEntities = eshopMiddleGroundService.queryBtypeList(type);
            if(CollectionUtils.isEmpty(btypeEntities)){
                throw new DistributorException(500, "该账套无分销商信息");
            }

            // 构建DistributorInfo
            List<DistributorInfo> distributorInfos = eshopMiddleGroundService.buildDistributorInfo(btypeEntities);
            if(CollectionUtils.isEmpty(distributorInfos)){
                throw new DistributorException(500, "没有需要上传的分销商信息");
            }
            return distributorInfos;
        } catch (DistributorException e) {
            throw new DistributorException(e.getErrCode(), e.getErrMsg());
        }
    }


    /**
     * 查询订货商城
     * @return com.wsgjp.ct.sale.common.entity.EshopInfo
     */
    private List<EshopInfo> getEnableMiddleGroundEshops(){
        List<EshopInfo> newList = null;
        try {
            newList = new ArrayList<>();
            List<EshopInfo> allEshopList = eshopService.getAllEshopList();
//            List<EshopInfo> mShopList = allEshopList.stream().filter(eshopInfo -> eshopInfo.getEshopType().equals(ShopType.MiddleGround)).collect(Collectors.toList());
            // 是否开启订货商城
            for(EshopInfo eshopInfo : allEshopList){
                DistributorRequest distributorRequest = eshopMiddleGroundService.buildDistributorRequest(eshopInfo);
                EshopFactory factory = null;
                try {
                    factory = EshopFactoryManager.create(distributorRequest.getShopType(), distributorRequest.getSystemParams());
                } catch (RuntimeException e) {
                    logger.info(String.format("创建Factory报错：%s", e.getMessage()));
                }
                if(null != factory){
                    EshopDistributorFeature feature = factory.getFeature(EshopDistributorFeature.class);
                    if (feature == null) {
                        logger.info(String.format("【profileId：】%s, %s不支持上传经销商！", CurrentUser.getProfileId(), distributorRequest.getSystemParams().getShopType().getName()));
                        continue;
                    }
                    newList.add(eshopInfo);
                }
            }
        } catch (RuntimeException e) {
            throw new DistributorException(500, "筛选已授权订货商城店铺出错,【profileId】：" + CurrentUser.getProfileId());
        }
        return newList;
    }

    /**
     * 同步至中台
     * @param type:
     * @param req:
     * @return void
     */
    private DistributorResponse syncToMidlleGround(BusinessResolveType type, DistributorRequest req){
        int code = null == type ? 2 : type.getCode();
        DistributorResponse distributorResponse = new DistributorResponse();
        switch (code){
            // 新增
            case 1:
            case 2:
                distributorResponse = bifrostEshopDistributorService.distributorUpload(req);
                break;
            case 3:
                distributorResponse = bifrostEshopDistributorService.distributorDelete(req);
                break;
            default:
                break;
        }
        return distributorResponse;
    }


}
