package com.wsgjp.ct.sale.web.jarvis.Entity;


import java.util.List;

public class SettingDefine {
    private String fieldName;
    private String field;
    private ControllerType controllerType = ControllerType.TextEdit;
    private List<FieldSource> sources;
    private int order;

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public List<FieldSource> getSources() {
        return sources;
    }

    public void setSources(List<FieldSource> sources) {
        this.sources = sources;
    }

    @Override
    public String toString() {
        return String.format("SettingDefine{fieldName='%s', field='%s', sources=%s}", fieldName, field, sources);
    }

    public ControllerType getControllerType() {
        return controllerType;
    }

    public void setControllerType(ControllerType controllerType) {
        this.controllerType = controllerType;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }
}

