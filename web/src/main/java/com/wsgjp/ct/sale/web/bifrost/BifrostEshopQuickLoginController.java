package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.entity.request.QuickLoginRequest;
import com.wsgjp.ct.sale.biz.bifrost.entity.request.QuickLoginUpdateEShopInfoRequest;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopQuickLoginService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "网店授权相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/quick/login")
public class BifrostEshopQuickLoginController {

    @Autowired
    private BifrostEshopQuickLoginService quickLoginService;

    /**
     * 更新保存店铺授权信息
     */
    @PostMapping("/updateEshopAuthInfo")
    public void updateEshopAuthInfo(@RequestBody QuickLoginUpdateEShopInfoRequest request){
        quickLoginService.updateEshopInfo(request,true);
    }

    /**
     * 添加店铺或者更新店铺授权信息
     * @param request
     */
    @PostMapping("/addOrUpdateEshopInfo")
    public void addOrUpdateEshopInfo(@RequestBody QuickLoginUpdateEShopInfoRequest request){
        quickLoginService.updateEshopInfo(request,false);
    }
}
