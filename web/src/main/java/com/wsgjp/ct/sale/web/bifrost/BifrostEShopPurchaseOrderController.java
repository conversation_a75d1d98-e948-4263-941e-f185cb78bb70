package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopPurchaseOrderService;
import com.wsgjp.ct.sale.platform.entity.request.purchase.PurchaseOrderDetailRequest;
import com.wsgjp.ct.sale.platform.entity.request.purchase.PurchaseOrderSignRequest;
import com.wsgjp.ct.sale.platform.entity.response.purchase.PurchaseOrderDownloadResponse;
import com.wsgjp.ct.sale.platform.entity.response.purchase.PurchaseOrderSignResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 采购单相关接口
 * @author: lj
 * @create: 2022-03-11
 **/
@Api(tags = "网店采购单相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/purchaseOrder")
public class BifrostEShopPurchaseOrderController {
    private final BifrostEshopPurchaseOrderService eshopPurchaseOrderService;

    public BifrostEShopPurchaseOrderController(BifrostEshopPurchaseOrderService eshopPurchaseOrderService) {
        this.eshopPurchaseOrderService = eshopPurchaseOrderService;
    }

    /**
     * 根据订单号查询采购单详情
     *
     * @param request
     * @return
     */
    @ApiOperation("根据订单号查询采购单详情")
    @PostMapping("downloadPurchaseOrders")
    public PurchaseOrderDownloadResponse downloadPurchaseOrders(@RequestBody PurchaseOrderDetailRequest request) {
        return eshopPurchaseOrderService.downloadPurchaseOrders(request);
    }

    /**
     * 采购订单签收
     *
     * @param request PurchaseOrderSignRequest
     * @return
     */
    @ApiOperation("采购订单签收")
    @PostMapping("signForPurchaseOrder")
    public PurchaseOrderSignResponse signForPurchaseOrder(@RequestBody PurchaseOrderSignRequest request) {
        return eshopPurchaseOrderService.signForPurchaseOrder(request);
    }
}
