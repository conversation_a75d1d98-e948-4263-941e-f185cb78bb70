package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.response.staff.QueryStaffInfosResponse;
import com.wsgjp.ct.sale.platform.sdk.service.EshopStaffDownloadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "职员/平台职员相关接口")
@RestController
@RequestMapping("${app.id}/bifrost/staff")
public class BifrostEshopStaffDownloadController {

    private final EshopStaffDownloadService staffDownloadService;

    public BifrostEshopStaffDownloadController(EshopStaffDownloadService staffDownloadService) {
        this.staffDownloadService = staffDownloadService;
    }

    @ApiOperation("获取平台职员列表信息")
    @PostMapping("/queryStaffInfos")
    public QueryStaffInfosResponse queryStaffInfos(@RequestBody BaseRequest request) {
        try {
            return staffDownloadService.queryStaffInfos(request);
        } catch (Exception ex) {
            QueryStaffInfosResponse response = new QueryStaffInfosResponse();
            response.setSuccess(false);
            response.setCode(500);
            response.setMessage(ex.getMessage());
            return response;
        }
    }
}
