package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopStockService;
import com.wsgjp.ct.sale.biz.bifrost.service.impl.BifrostEshopStockServiceImpl;
import com.wsgjp.ct.sale.platform.entity.request.product.ModifyStockQtyRequest;
import com.wsgjp.ct.sale.platform.entity.response.product.ProductModifyStockResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "网店库存相关接口")
@RestController
@RequestMapping("${app.id}/bifrost/stock")
public class BifrostEshopStockController {
    private final BifrostEshopStockService stockService;

    public BifrostEshopStockController(BifrostEshopStockServiceImpl stockService) {
        this.stockService = stockService;
    }

    @ApiOperation("修改商品库存数量(普通库存同步、分仓库存同步、阶梯库存同步)")
    @PostMapping("modify/qty")
    public ProductModifyStockResponse modifyQty(@RequestBody ModifyStockQtyRequest request) {
        return stockService.modifyProductStockQty(request);
    }
}
