package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.OperationEnum;
import com.wsgjp.ct.sale.common.log.PlatformBizKeyPointFeedback;
import com.wsgjp.ct.sale.platform.entity.entities.DouDianSdkInfo;
import com.wsgjp.ct.sale.platform.entity.request.uploadlog.BatchLog;
import com.wsgjp.ct.sale.platform.entity.request.uploadlog.SingleLog;
import com.wsgjp.ct.sale.platform.entity.request.uploadlog.SqlLog;
import com.wsgjp.ct.sale.platform.entity.request.uploadlog.TransferLog;
import com.wsgjp.ct.sale.platform.factory.doudian.DoudianConfig;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Api(tags = "日志上传相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost")
public class BifrostEshopLogUploadController {
    private final PlatformBizKeyPointFeedback platformBizKeyPointFeedback;
    private final DoudianConfig doudianConfig;
    private final EshopService eshopService;

    public BifrostEshopLogUploadController(PlatformBizKeyPointFeedback platformBizKeyPointFeedback, DoudianConfig doudianConfig, EshopService eshopService) {
        this.platformBizKeyPointFeedback = platformBizKeyPointFeedback;
        this.doudianConfig = doudianConfig;
        this.eshopService = eshopService;
    }

    @ApiOperation("批量上传订单日志")
    @PostMapping("uploadBatchLog")
    public void uploadLog(@RequestBody BatchLog batchLog) {
        platformBizKeyPointFeedback.feedback(batchLog.getOrders(), typeOf(batchLog.getCode()));
    }
    @ApiOperation("单个上传订单日志")
    @PostMapping("singleUploadLog")
    public void uploadLog(@RequestBody SingleLog singleLog) {
        platformBizKeyPointFeedback.feedback(singleLog.getEshopId(), singleLog.getTradeId() , typeOf(singleLog.getCode()));
    }
    @ApiOperation("sql操作日志上传")
    @PostMapping("sqlUploadLog")
    public void uploadBatchLog(@RequestBody SqlLog sqlLog) {
        platformBizKeyPointFeedback.sql(sqlLog.getEshopId(),sqlLog.getSql());
    }
    @ApiOperation("推送第三方订单日志")
    @PostMapping("transferLog")
    public void transferLog(@RequestBody TransferLog transferLog) {
        platformBizKeyPointFeedback.transfer(transferLog.getOrders(),transferLog.getUrl(),transferLog.getFileMd5());
    }

    @ApiOperation("初始化抖店SDK")
    @PostMapping("initDouDianSdk")
    public DouDianSdkInfo initDouDianSdk(HttpServletResponse response) {
        try {
            List<EshopInfo> eshopList = eshopService.getAllEshopList();
            boolean hasDouDianEshop = eshopList.stream().anyMatch(x -> 52 == x.getEshopType().getPlatformType());
            if (!hasDouDianEshop) {
                return null;
            }
            DouDianSdkInfo sdkInfo = new DouDianSdkInfo();
            sdkInfo.setAppKey(doudianConfig.getAppKey());
            sdkInfo.setAccountId(CurrentUser.getEmployeeId().toString());
            sdkInfo.setUrls(doudianConfig.getUrls());
            response.addHeader("Access-Control-Allow-Headers","doudian-event-id");
            response.addHeader("Access-Control-Max-Age","3600");
            return sdkInfo;
        } catch (Exception e) {
            return null;
        }
    }

    public static OperationEnum typeOf(int type) {
        for (OperationEnum oerationEnum : OperationEnum.values()) {
            if (oerationEnum.getCode() == type) {
                return oerationEnum;
            }
        }
        throw new RuntimeException("没有找到对应的枚举");
    }

}
