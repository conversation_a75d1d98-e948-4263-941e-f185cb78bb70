package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.service.printbatch.PrintBatchNewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * <AUTHOR> 2022-04-20 16:06
 */
@RestController
@Api("波次配货")
@RequestMapping("/${app.id}/jarvis/printBatchNew")
public class PrintBatchNewController {
    private PrintBatchNewService printBatchNewService;

    public  PrintBatchNewController(PrintBatchNewService printBatchNewService){
        this.printBatchNewService = printBatchNewService;
    }

    @ApiOperation(value = "获取状态值", notes = "（）")
    @PostMapping("init")
    public HashMap init() throws Exception{
        return printBatchNewService.init();
    }

}
