package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.request.BatchBaseInfoCheckRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.BaseInfoCheckResponse;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.StockCheckResponse;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoInUseCheckService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.ApiOperation;
import ngp.utils.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-17
 **/

@RestController
@RequestMapping("/${app.id}/jarvis/baseInfoCheck")
public class JarvisBaseInfoCheckController {
    private BaseInfoInUseCheckService checkService;

    public JarvisBaseInfoCheckController(BaseInfoInUseCheckService checkService) {
        this.checkService = checkService;
    }

    @ApiOperation(value = "基本信息删除或者停用时检查")
    @PostMapping("deleteOrStopBaseInfoCheck")
    public List<BaseInfoCheckResponse> deleteOrStopBaseInfoCheck(@RequestBody BatchBaseInfoCheckRequest request) {
        List<BaseInfoCheckResponse> response = new ArrayList<>();
        if (request.getIds() == null) {
            return response;
        }
        request.setProfileId(CurrentUser.getProfileId());
        for (BigInteger id : request.getIds()) {
            BaseInfoCheckResponse item = new BaseInfoCheckResponse();
            item.setSuccess(true);
            item.setId(id);
            request.setId(id);
            try {
                String ret = checkService.checkInUseToAll(request);
                if (!StringUtils.isEmpty(ret)) {
                    item.setSuccess(false);
                    item.setMsg(ret);
                }
            } catch (Exception ex) {
                item.setSuccess(false);
                item.setMsg(String.format("执行异常，%s", ex.getMessage()));
            }
            response.add(item);
        }

        return response;
    }

    @PostMapping("CheckStopOrDeletedStock")
    public StockCheckResponse CheckStopOrDeletedStock(@RequestBody BatchBaseInfoCheckRequest request) {
        return checkService.CheckStopOrDeletedStock(request);
    }

}
