package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.common.enums.core.utils.EnumUtil;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopAuthService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopPluginService;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopSysConfig;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.EshopPlatformStoreMappingInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.GetPlatformTypeInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.EshopShowTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.AutoPickKtypePageInfo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopAddressMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.PlatformBranchEshopResult;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.BaseInfoCheckRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopDefaultConfigRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryEShopParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.QueryStockRuleParameter;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.checkQuote.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopBusinessService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopPlatformStoreMappingService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.StockUtil;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BaseStore;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.MallType;
import com.wsgjp.ct.sale.common.processlogger.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformEshopSupport;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformEshoptypeSupport;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformSupport;
import com.wsgjp.ct.sale.platform.dto.eshop.ShopTypeSupport;
import com.wsgjp.ct.sale.platform.dto.plugin.EshopBusinessConfig;
import com.wsgjp.ct.sale.platform.dto.plugin.EshopTagInfo;
import com.wsgjp.ct.sale.platform.dto.shop.OnlineShopEntity;
import com.wsgjp.ct.sale.platform.dto.token.EshopAuthInfo;
import com.wsgjp.ct.sale.platform.entity.request.auth.EshopState;
import com.wsgjp.ct.sale.platform.entity.request.plugin.CommonRequest;
import com.wsgjp.ct.sale.platform.entity.request.store.GetOnlineShopRequest;
import com.wsgjp.ct.sale.platform.entity.response.shop.GetOnlineShopResponse;
import com.wsgjp.ct.sale.platform.enums.CheckAuthType;
import com.wsgjp.ct.sale.platform.enums.EshopBusinessConfigTypeEnum;
import com.wsgjp.ct.sale.platform.enums.FeatureEnum;
import com.wsgjp.ct.sale.platform.feature.customer.EshopCustomerCallbackFeature;
import com.wsgjp.ct.sale.platform.feature.invoice.EshopInvoiceFeature;
import com.wsgjp.ct.sale.platform.feature.plugin.EshopPluginFeature;
import com.wsgjp.ct.sale.platform.feature.product.EshopProductShelfFeature;
import com.wsgjp.ct.sale.platform.feature.staff.EshopQueryStaffInfosFeature;
import com.wsgjp.ct.sale.platform.sdk.util.PlatformCommonUtil;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.wsgjp.ct.support.context.CurrentUser.getProfileId;

/**
 * <AUTHOR>
 */
@Api(tags = "网店基本信息相关")
@RequestMapping("${app.id}/eshoporder/eshop")
@RestController
public class EshopController {

    private static final Logger logger = LoggerFactory.getLogger(EshopController.class);
    private final EshopService service;
    private final EshopPlatformStoreMappingService storeSvc;
    private final EshopBusinessService eshopBusinessService;
    private final BifrostEshopOrderService bifrostEshopOrderService;
    private final BifrostEshopPluginService eshopPluginService;
    private final EshopOrderBaseInfoService eshopOrderBaseInfoService;

    private final SaleBizConfig saleBizConfig;
    private final BifrostEshopAuthService bifrostEshopAuthService;
    private final HashMap<Integer, BaseCheckQuoteService> map = new HashMap<>();

    public EshopController(EshopService service, EshopPlatformStoreMappingService storeSvc, EshopBusinessService eshopBusinessService, BifrostEshopPluginService eshopPluginService,
                           EshopCheckQuoteService eshopCheckQuoteService, KtypeCheckQuoteService ktypeCheckQuoteService,
                           PtypeCheckQuoteService ptypeCheckQuoteService, EtypeCheckQuoteService etypeCheckQuoteService,
                           ComboCheckQuoteService comboCheckQuoteService, BtypeCheckQuoteService btypeCheckQuoteService,
                           BifrostEshopOrderService bifrostEshopOrderService, BifrostEshopAuthService bifrostEshopAuthService,
                           SaleBizConfig saleBizConfig, EshopOrderBaseInfoService eshopOrderBaseInfoService) {
        this.service = service;
        this.storeSvc = storeSvc;
        this.eshopBusinessService = eshopBusinessService;
        this.eshopPluginService = eshopPluginService;
        this.bifrostEshopOrderService = bifrostEshopOrderService;
        this.bifrostEshopAuthService = bifrostEshopAuthService;
        this.saleBizConfig = saleBizConfig;
        this.eshopOrderBaseInfoService=eshopOrderBaseInfoService;
        this.map.put(1, ktypeCheckQuoteService);
        this.map.put(2, eshopCheckQuoteService);
        this.map.put(4, ptypeCheckQuoteService);
        this.map.put(6, etypeCheckQuoteService);
        this.map.put(8, comboCheckQuoteService);
        this.map.put(9, btypeCheckQuoteService);
    }

    @PostMapping(value = "/getShopTypeFiles")
    public List<PlatformEshoptypeSupport> getShopTypeFiles(@RequestBody EShopPageInfo pageInfo) {
        return service.getPlatformTypeFiles(pageInfo);
    }

    @PostMapping(value = "/getEshopPageResponse")
    public EshopPageResponse getEshopPageResponse(@RequestBody QueryEShopParameter parameter) {
        EshopPageResponse response = service.getEshopPageResponse(parameter);
        ShopType eshopType = response.getPageInfo().getEshopType();
        response.setProductId(RouteThreadLocal.getRoute().getProductId());
        List<PlatformSupport> platformSupportList = EshopUtils.getSupportPlatformTypes();
        ArrayList<PlatformSupport> platformSupportNewList = platformSupportList.stream()
                .collect(Collectors.collectingAndThen(Collectors
                                .toCollection(() -> new TreeSet<>(Comparator.comparing(PlatformSupport::getPlatformType))),
                        ArrayList::new));
        response.setPlatformSupport(platformSupportNewList);
        response.setEshopTypeSupport(EshopUtils.getSupportEshopTypes());
        boolean splitSupported = EshopUtils.checkUsingSendingShopType(eshopType);
        response.setAllowSplitSendOrder(splitSupported);
        response.setOldVersion(service.isOldVersion());
        return response;
    }

    @PostMapping(value = "/getBranchEshop")
    public List<PlatformBranchEshopResult> getBranchEshop(@RequestBody QueryEShopParameter parameter) {
        EShopPageInfo pageInfo = new EShopPageInfo();
        pageInfo.setEshopType(parameter.getShopType());
        GetOnlineShopResponse getOnlineShopResponse = new GetOnlineShopResponse();
        if (parameter.getOtypeId() == null) {
            EshopAuthInfo eshopAuthInfoFromRdsNew = eshopBusinessService.getEshopAuthInfoFromRdsNew(pageInfo, false);
            if (null == eshopAuthInfoFromRdsNew) {
                throw new RuntimeException("拉取子网店时候，必须先授权。");
            }
            GetOnlineShopRequest request = new GetOnlineShopRequest();
            request.setShopType(parameter.getShopType());
            request.setToken(eshopAuthInfoFromRdsNew.getToken());
            EshopSystemParams req = new EshopSystemParams();
            req.setShopType(parameter.getShopType());
            req.setToken(eshopAuthInfoFromRdsNew.getToken());
            req.setRefreshToken(eshopAuthInfoFromRdsNew.getRefreshToken());
            req.setReExpiresIn(eshopAuthInfoFromRdsNew.getReExpiresIn());
            req.setR1ExpiresIn(eshopAuthInfoFromRdsNew.getR1ExpireIn());
            request.setSystemParams(req);
            getOnlineShopResponse = bifrostEshopOrderService.queryOnlineShops(request);

        } else {
            GetOnlineShopRequest request = new GetOnlineShopRequest();
            request.setShopType(parameter.getShopType());
            request.setShopId(parameter.getOtypeId());
            getOnlineShopResponse = bifrostEshopOrderService.queryOnlineShops(request);
        }
        List<PlatformBranchEshopResult> results = new ArrayList<>();
        List<PlatformBranchEshopResult> brachEshopByGroupId = service.getBrachEshopByGroupId(String.valueOf(parameter.getOtypeId()));
        if (getOnlineShopResponse.getSuccess()) {
            List<OnlineShopEntity> onlineShops = getOnlineShopResponse.getOnlineShops();
            if (CollectionUtils.isNotEmpty(onlineShops)) {
                for (OnlineShopEntity en : onlineShops) {
                    Optional<PlatformBranchEshopResult> first = brachEshopByGroupId.stream().filter(b -> en.getShopId().equals(b.getEshopAccount()) && en.getShopName().equals(b.getFullname())).findFirst();
                    boolean isExist = service.getEshopInfoByFullNameAndShopAccount(en);
                    PlatformBranchEshopResult res = new PlatformBranchEshopResult();
                    res.setEshopAccount(en.getShopId());
                    res.setEnabled(isExist ? true : false);
                    res.setFullname(en.getShopName());
                    res.setOtypeId(first.isPresent() ? first.get().getOtypeId() : BigInteger.ZERO);
                    results.add(res);
                }
            }
        }
        return results;
    }

    @PostMapping(value = "/getParTypeList")
    public List<Otype> getParTypeList() {
        List<Otype> response = service.getOrgParTypeList(CurrentUser.getProfileId());
        return response;
    }

    /**
     * 对外获取网店平台类型
     * 传值-100 获取所有 传值-9999获取排除其他外所有
     *
     * @return
     */
    @PostMapping(value = "/getPlatformType")
    public List<ShopTypeSupport> getPlatformTypeSupport(@RequestBody GetPlatformTypeInDTO inDTO) {
        if (inDTO == null || inDTO.getState() == null) {
            throw new RuntimeException("入参为空");
        }
        return EshopUtils.getShopTypeSupportByPlatformTypeIsOther(inDTO.getState());
    }

    @GetMapping(value = "/getAuthInfo")
    public EshopAuthInfo getAuthInfo(int shopType) {
        EShopPageInfo pageInfo = new EShopPageInfo();
        pageInfo.setEshopType(ShopType.valueOf(shopType));
        EshopAuthInfo eshopAuthInfoFromRdsNew = eshopBusinessService.getEshopAuthInfoFromRdsNew(pageInfo, false);
        return eshopAuthInfoFromRdsNew;
    }

    /**
     * 内部获取网店平台类型
     *
     * @return
     */
    @GetMapping(value = "/getPlatformType/{type}")
    public List<PlatformSupport> getPlatformTypeSupport(@PathVariable int type) {
        List<PlatformSupport> platformSupportList = EshopUtils.getSupportPlatformTypes();
        if (type == 1) {
            List<PlatformSupport> newSupport = new ArrayList<>();
            for (PlatformSupport item : platformSupportList) {
                if (9999 == item.getPlatformType()) {
                    continue;
                }
                newSupport.add(item);
            }
            platformSupportList = newSupport;
        }
        return platformSupportList.stream()
                .collect(Collectors.collectingAndThen(Collectors
                                .toCollection(() -> new TreeSet<>(Comparator.comparing(PlatformSupport::getPlatformType))),
                        ArrayList::new));
    }

    /**
     * 获取当前账套用户支持获取类目的平台的平台类型列表
     */
    @PostMapping(value = "/getCurrentUserSupportGetCategoryPlatformTypes")
    public List<PlatformSupport> getCurrentUserSupportGetCategoryPlatformTypes() {
        return service.getCurrentUserSupportGetCategoryPlatformTypes();
    }


    @PostMapping(value = "/getEshopInfoByEshopType")
    public EshopPageResponse getEshopInfoByEshopType(@RequestBody QueryEShopParameter parameter) {
        return service.getEshopPageResponse(parameter);
    }

    @PostMapping(value = "/doAuth")
    public BaseResponse doAuth(@RequestBody QueryEShopParameter parameter) {
        return service.doAuthoriza(parameter);
    }

    @PostMapping(value = "/doAuthNew")
    public BaseResponse doAuthNew(@RequestBody QueryEShopParameter parameter) {
        return service.doAuthorizaNew(parameter);
    }

    @PostMapping(value = "/authorizeCheck")
    public BaseResponse authorizeCheck(@RequestBody QueryEShopParameter parameter) throws Exception {
        return service.authCheck(parameter);
    }

    @PostMapping(value = "/authorizeCheckNew")
    public BaseResponse authorizeCheckNew(@RequestBody QueryEShopParameter parameter) throws Exception {
        return service.authorizeCheckNew(parameter);
    }


    @PostMapping(value = "/authorizeCheckByShoptype")
    public BaseResponse authorizeCheckByShoptype(@RequestBody QueryEShopParameter parameter) throws Exception {
        return service.authorizeCheckByShoptype(parameter);
    }

    @PostMapping(value = "/saveEshopConfig")
    public BaseResponse saveEshopConfig(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        try {
            response = eshopBusinessService.saveEshopConfig(eshopInfo);
            response = eshopBusinessService.saveEshopInfo(eshopInfo);
            eshopBusinessService.saveOtyoe(eshopInfo);
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/saveEshopConfigByWhere")
    public List<EshopInfo> saveEshopConfigByWhere(@RequestBody EShopPageInfo eshopInfo) {
        List<EshopInfo> eshopInfos = new ArrayList<>();
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        eshopBusinessService.saveEshopConfigByWhere(eshopInfo);
        BaseResponse response = new BaseResponse();
        try {
            List<PlatformBranchEshopResult> eshopBranch = eshopInfo.getEshopBranch();
            for (PlatformBranchEshopResult en : eshopBranch) {
                EshopInfo info = new EshopInfo();
                info.setFullname(en.getFullname());
                info.setEshopAccount(en.getEshopAccount());
                info.setOtypeId(en.getOtypeId());
                eshopInfos.add(info);
            }
            return eshopInfos;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return eshopInfos;
    }


    @PostMapping(value = "/doSaveBuyer")
    public BigInteger doSaveBuyer(@RequestBody EshopBuyer eshopBuyer) {
        return eshopBusinessService.doSaveBuyer(eshopBuyer);
    }

    @PostMapping(value = "/saveEshopDefaultConfig")
    public BaseResponse saveEshopDefaultConfig(@RequestBody EshopDefaultConfigRequest config) {
        BaseResponse response = new BaseResponse();
        try {
            response = eshopBusinessService.saveEshopDefaultConfig(config);
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @GetMapping(value = "/getEshopDefaultConfig")
    public EshopDefaultConfigRequest getEshopDefaultConfig(String key) {
        return eshopBusinessService.getEshopDefaultConfig(key);
    }

    @PostMapping(value = "/checkOpenEshopBtypeUploadConfig")
    public BaseResponse checkOpenEshopBtypeUploadConfig(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        try {
            //保存对应关系之后，回告平台
            Otype otype = service.getOtypeById(eshopInfo.getOtypeId());
            if (otype == null) {
                response.setSuccess(false);
                response.setMessage("未找到网店信息！无法处理！");
            }
            boolean callBackSupport = EshopUtils.isFeatureSupported(EshopCustomerCallbackFeature.class, otype.getShopType());
            response.setSuccess(callBackSupport);
            if (!callBackSupport) {
                response.setMessage("该平台暂时不支持回告往来单位信息！");
            }
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/getAgEnable")
    public boolean getAgEnable(@RequestBody EShopPageInfo eshopInfo) {
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(eshopInfo.getEshopType());
        List<EshopBusinessConfig> eshopBusinessConfigList = eshopPluginService.getEshopBusinessConfigList(commonRequest);
        if (CollectionUtils.isNotEmpty(eshopBusinessConfigList)) {
            for (EshopBusinessConfig eshopBusinessConfigEntity : eshopBusinessConfigList) {
                if (EshopBusinessConfigTypeEnum.AG.equals(eshopBusinessConfigEntity.getBusinessConfigType())) {
                    return true;
                }
            }
        }
        return false;
    }

    @PostMapping(value = "/processRefundOnline")
    public boolean processRefundOnline(@RequestBody EShopPageInfo eshopInfo) {
        boolean processRefundOnlineSta = service.processRefundOnline(eshopInfo);
        return processRefundOnlineSta;
    }

    @PostMapping(value = "/getAllowAoXiangEshop")
    public BaseResponse getAllowAoXiangEshop(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        response.setSuccess(false);
        List<ShopType> warehouseBizFeedbackSupportedList = PlatformCommonUtil.getWarehouseBizFeedbackSupportedList();
        for (ShopType shopType : warehouseBizFeedbackSupportedList) {
            if (shopType.getCode() == eshopInfo.getEshopType().getCode()) {
                response.setSuccess(true);
                break;
            }
        }
        return response;
    }

    @PostMapping(value = "/checkOpenEshopInvoiceConfig")
    public BaseResponse checkOpenEshopInvoiceConfig(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        try {
            //保存对应关系之后，回告平台
            Otype otype = service.getOtypeById(eshopInfo.getOtypeId());
            if (otype == null) {
                response.setSuccess(false);
                response.setMessage("未找到网店信息！无法处理！");
            }
            boolean callBackSupport = EshopUtils.isFeatureSupported(EshopInvoiceFeature.class, otype.getShopType());
            response.setSuccess(callBackSupport);
            if (!callBackSupport) {
                response.setMessage("该平台暂时不支持开启回传发票！");
            }
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/checkDownloadImportOrder")
    public BaseResponse checkDownloadImportOrder(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        try {
            Otype otype = service.getOtypeById(eshopInfo.getOtypeId());
            if (otype == null) {
                response.setSuccess(false);
                response.setMessage("未找到网店信息！无法处理！");
            }
            boolean callBackSupport = EshopUtils.isFeatureSupported(EshopInvoiceFeature.class, otype.getShopType());
            response.setSuccess(callBackSupport);
            if (!callBackSupport) {
                response.setMessage("该平台暂时不支持开启下载平台的线下导入订单！");
            }
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/needCheckShopAccountByShoptype")
    public boolean needCheckShopAccountByShoptype(@RequestBody EShopPageInfo eshopInfo) {
        return service.needCheckShopAccountByShoptype(eshopInfo.getEshopType());
    }

    @PostMapping(value = "/checkSupportAutoShelfOnOrOff")
    public BaseResponse checkSupportAutoShelfOnOrOff(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        try {
            //保存对应关系之后，回告平台
            Otype otype = service.getOtypeById(eshopInfo.getOtypeId());
            if (otype == null) {
                response.setSuccess(false);
                response.setMessage("未找到网店信息！无法处理！");
            }
            boolean callBackSupport = EshopUtils.isFeatureSupported(EshopProductShelfFeature.class, otype.getShopType());
            if (callBackSupport) {
                if (otype.getShopType() == ShopType.WeiMobEC) {
                    EshopConfig eshopConfig = otype.getEshopConfig();
                    if (eshopConfig != null && "2".equals(eshopConfig.getPlatformEshopSnType())) {
                        callBackSupport = false;
                    }
                }
            }
            response.setSuccess(callBackSupport);
            if (!callBackSupport) {
                response.setMessage("该平台暂时不支持开启自动上下架！");
            }
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/checkSupportAutoCreateBtype")
    public BaseResponse checkSupportAutoCreateBtype(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        try {
            //保存对应关系之后，回告平台
            Otype otype = service.getOtypeById(eshopInfo.getOtypeId());
            if (otype == null) {
                response.setSuccess(false);
                response.setMessage("未找到网店信息！无法处理！");
                return response;
            }
            HashMap<String, Boolean> autoCreateBtypeAndPlaintextPlatformDistributorName = saleBizConfig.getAutoCreateBtypeAndPlaintextPlatformDistributorName();
            Boolean enable = autoCreateBtypeAndPlaintextPlatformDistributorName.get(otype.getShopType().name().toLowerCase());
            response.setSuccess(null != enable);
            if (!response.isSuccess()) {
                response.setMessage("该平台暂时不支持开启自动创建往来单位！");
            }
            return response;
        } catch (Exception ex) {
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/saveEshopForUpdate")
    public BaseResponse saveEshopForUpdate(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        List<BigInteger> otypeIds = eshopInfo.getOtypeIds();
        try {
            for (BigInteger otypeId : otypeIds) {
                eshopInfo.setOtypeId(otypeId);
                QueryEShopParameter parameter = new QueryEShopParameter();
                parameter.setProfileId(CurrentUser.getProfileId());
                parameter.setEshopId(otypeId);
                EshopInfo getEshopInfoByOrgId = service.getEshopInfoById(parameter);
                eshopInfo.setEshopType(getEshopInfoByOrgId.getEshopType());
                eshopInfo.setEshopAccount(getEshopInfoByOrgId.getEshopAccount());
                eshopInfo.setDeliverDuration(eshopInfo.getDeliverDuration());
                eshopInfo.setOtypeFullname(getEshopInfoByOrgId.getFullname());
                eshopInfo.setMallType(MallType.getMallType(getEshopInfoByOrgId.getMallType()));
                eshopInfo.setSubscribeLogistics(eshopInfo.isSubscribeLogistics() == null ? getEshopInfoByOrgId.isSubscribeLogistics() : eshopInfo.isSubscribeLogistics());
                response = eshopBusinessService.saveEshopInfo(eshopInfo);
                eshopBusinessService.saveOtyoe(eshopInfo);
            }
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/checkBranchEshopIsRepeat")
    public String checkBranchEshopIsRepeat(@RequestBody List<PlatformBranchEshopResult> eshopBranch) {
        return eshopBusinessService.checkBranchEshopIsRepeat(eshopBranch);
    }

    @PostMapping(value = "/saveEshop")
    public BaseResponse saveEshop(@RequestBody EShopPageInfo eshopInfo) {
        String groupId = UId.newId().toString();
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        try {
            List<PlatformBranchEshopResult> eshopBranch = eshopInfo.getEshopBranch();
            eshopInfo.setMainEshop(!eshopBranch.isEmpty() || eshopInfo.getPlatformauthType() == 1);
            eshopInfo.setGroupId(groupId);
            response = eshopBusinessService.saveEshopInfo(eshopInfo);
            if (CollectionUtils.isNotEmpty(eshopBranch)) {
                if (response.isSuccess()) {
                    eshopInfo.setMainEshop(false);
                    eshopBusinessService.saveBranchEshopInfo(eshopInfo);
                }
            }
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/saveEshopNew")
    public BaseResponse saveEshopNew(@RequestBody EShopPageInfo eshopInfo) {
        eshopInfo.setProfileId(CurrentUser.getProfileId());
        BaseResponse response = new BaseResponse();
        try {
            response = eshopBusinessService.saveEshopInfoNew(eshopInfo);
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }


    @PostMapping(value = "/getEshopDeleted")
    public boolean getEshopDeleted(@RequestBody BigInteger eshopId) {
        BigInteger profileId = getProfileId();
        EshopInfo eshop = service.getEshopInfoById(profileId, eshopId);
        return eshop == null || eshop.isDeleted();
    }

    @PostMapping(value = "/checkDuplicateOtype")
    public boolean checkDuplicateOtype(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = getProfileId();
        eshopInfo.setProfileId(profileId);
        return service.checkOtypeDuplicate(eshopInfo);
    }

    @PostMapping(value = "/checkOtypeBeforeSave")
    public String checkOtypeBeforeSave(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = getProfileId();
        eshopInfo.setProfileId(profileId);
        String message = "";
        boolean isDuplicate = service.checkOtypeDuplicate(eshopInfo);
        if (isDuplicate) {
            return "已存在相同名称的网店，请重新填写网店名称！";
        }
        if (eshopBusinessService.IsCooperationShopType(eshopInfo.getEshopType()) && !eshopBusinessService.IsCooperationProfileSupport(eshopInfo.getEshopType())) {
            return "该网店类型暂未全面开放，如果您需要使用，可以联系服务顾问开通使用.";
        }
        return "";
    }

    @PostMapping(value = "/getEShopTypeByPlatformType")
    public List<PlatformEshopSupport> getEShopTypeByPlatformType(@RequestBody EShopPageInfo pageInfo) {
        int platform = pageInfo.getEshopSalePlatform();
        List<PlatformEshopSupport> list = EshopUtils.getSupportEShopType(platform);
        for (PlatformEshopSupport item : list) {
            if (item.getType() == 9999) {
                item.setShowAppKey(EshopShowTypeEnum.NO_SHOW_AUTH.getCode());
                continue;
            }
            boolean featureSupported = EshopUtils.isFeatureSupported(EshopPluginFeature.class, ShopType.valueOf(item.getType()));
            EshopState state = new EshopState();
            state.setShopType(pageInfo.getEshopType());
            EshopTagInfo eshopTag = eshopPluginService.getEshopTag(state);
            if (featureSupported && eshopTag != null) {
                item.setAuthType(eshopTag.getAuthType().getCode());
                if (!eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
                    item.setShowAppKey(EshopShowTypeEnum.NO_SHOW_AUTH.getCode());
                    continue;
                }
                if (!eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && eshopTag.isShowAppKey()) {
                    item.setShowAppKey(EshopShowTypeEnum.SHOW_APPKEY_NO_AUTH.getCode());
                    continue;
                }
                if (eshopTag.isShowAuth() && eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
                    item.setShowAppKey(EshopShowTypeEnum.SHOW_ORDER_AND_AUTH.getCode());
                    continue;
                }
                if (eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && eshopTag.isShowAppKey()) {
                    item.setShowAppKey(EshopShowTypeEnum.SHOW_APPKEY_AND_AUTH.getCode());
                    continue;

                }
                if (eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
                    item.setShowAppKey(EshopShowTypeEnum.SHOW_AUTH_NO_ORDER.getCode());

                }
            }
//            }
        }
        return list;
    }


    @PostMapping(value = "/getEShopTypeByPlatformTypeIsOther")
    public List<PlatformEshopSupport> getEShopTypeByPlatformTypeIsOther(@RequestBody EShopPageInfo pageInfo) {
        int platform = pageInfo.getEshopSalePlatform();
        List<PlatformEshopSupport> list = EshopUtils.getSupportEShopTypeByPlatformTypeIsOther(platform);
        ListIterator<PlatformEshopSupport> iterator = list.listIterator();
        while (iterator.hasNext()) {
            PlatformEshopSupport item = iterator.next();
            if (item.getType() == 9999) {
                item.setShowAppKey(EshopShowTypeEnum.NO_SHOW_AUTH.getCode());
                continue;
            }
            if (pageInfo.getMode() == 1) {
                if (item.getType() == 890) {
                    iterator.remove();
                    continue;
                }
            }
            try {
                EshopState state = new EshopState();
                state.setShopType(ShopType.valueOf(item.getType()));
                boolean featureSupported = EshopUtils.isFeatureSupported(EshopPluginFeature.class, ShopType.valueOf(item.getType()));
                EshopTagInfo eshopTag = eshopPluginService.getEshopTag(state);
                if (featureSupported) {
                    if (eshopTag != null) {
                        item.setAuthType(eshopTag.getAuthType().getCode());
                        if (!eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
                            item.setShowAppKey(EshopShowTypeEnum.NO_SHOW_AUTH.getCode());
                            continue;
                        }
                        if (!eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && eshopTag.isShowAppKey()) {
                            item.setShowAppKey(EshopShowTypeEnum.SHOW_APPKEY_NO_AUTH.getCode());
                            continue;
                        }
                        if (eshopTag.isShowAuth() && eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
                            item.setShowAppKey(EshopShowTypeEnum.SHOW_ORDER_AND_AUTH.getCode());
                            continue;
                        }
                        if (eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && eshopTag.isShowAppKey()) {
                            item.setShowAppKey(EshopShowTypeEnum.SHOW_APPKEY_AND_AUTH.getCode());
                            continue;

                        }
                        if (eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
                            item.setShowAppKey(EshopShowTypeEnum.SHOW_AUTH_NO_ORDER.getCode());
                        }
                        if (eshopTag.isShowAuth() && eshopTag.isShowOrderLink() && eshopTag.isShowAppKey()) {
                            item.setShowAppKey(EshopShowTypeEnum.SHOW_ALL.getCode());
                            continue;
                        }

                    }
                } else {
                    iterator.remove();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return list;
    }

    @PostMapping(value = "/getAuthUrl")
    public String getAuthUrl(@RequestBody QueryEShopParameter parameter) {
        try {
            return service.getAuthUrl(parameter);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }

    }

    @PostMapping(value = "/saveShopAccounttoRedis")
    public String saveShopAccounttoRedis(@RequestBody QueryEShopParameter parameter) {
        try {
            return service.saveShopAccounttoRedis(parameter);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }

    }

    @PostMapping(value = "/getRefundUrl")
    public String getRefundUrl(@RequestBody Otype otype) {
        try {
            return service.getRefundUrl(otype);
        } catch (Exception ex) {
            String errMsg = String.format("账套%s,网店%s获取退款链接失败,错误信息:%s", CurrentUser.getProfileId(), otype.getFullname(), ex.getMessage());
            throw new RuntimeException(errMsg, ex);
        }
    }


    @PostMapping(value = "/getBtypeByName")
    public BigInteger getBtypeByName(@RequestBody String paymentName) {
        try {
            BigInteger profileId = getProfileId();
            Btype btype = service.getBtypeByName(profileId, paymentName);
            if (btype == null) {
                return null;
            }
            return btype.getId();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    @GetMapping(value = "/getBtypeUserCode")
    public String getBtypeUserCode() {
        try {
            return UId.newId().toString();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 获取网店下拉框
     *
     * @param queryEShopParameter
     * @return
     */
    @PostMapping(value = "/getEshopInfos")
    public List<EshopInfo> getEshopInfos(@RequestBody QueryEShopParameter queryEShopParameter) {
        return service.getEshopByShopType(queryEShopParameter);
    }

    @GetMapping(value = "/getUrl/{shoptype}")
    public String getUrl(@PathVariable Integer shoptype) {
        return service.getUrl(shoptype);
    }

    @GetMapping(value = "/getAuthCheckResult/{shoptype}")
    public EshopAuthInfo getAuthCheckResult(@PathVariable Integer shoptype) {
        return eshopBusinessService.getAuthCheckResult(shoptype);
    }

    @PostMapping(value = "/getOrganizationById")
    public Otype getOrganizationById(@RequestBody QueryEShopParameter parameter) {
        return service.getOrganizationById(parameter);
    }

    @GetMapping(value = "/getEshopStoreTree/{eshopId}")
    public List<EshopStoreTreeData> getEshopStoreTreeData(@PathVariable BigInteger eshopId) {
        return storeSvc.queryEshopStoreTreeData(eshopId);
    }

    @GetMapping(value = "/getEshopStoreForWarehouseStock/{eshopId}")
    public List<EshopStoreTreeData> getEshopStoreForWarehouseStock(@PathVariable BigInteger eshopId) {
        return storeSvc.queryEshopStoreForWarehouseStock(eshopId);
    }

    @PostMapping(value = "/checkBtypeIn")
    public boolean checkBtypeIn(@RequestBody QueryEShopParameter parameter) {
        return service.checkBtypeIn(parameter);
    }

    @GetMapping(value = "/addEshopLog")
    public void addEshopLog() {
        PubSystemLogService.saveInfo("进入网店");
    }

    /**
     * 基本信息检查请求，检查基本信息是否有引用，能否被删除
     * <p>
     * 总：* 1-仓库 * 2-网店 * 3-物流公司 * 4-商品 * 5-sku * 6-职员 * 7-自定义标记 * 8-套餐  * 9-往来单位
     * <p>
     * 通过接口检查：* 1-仓库 * 2-网店（otype） * 3-物流公司 * 4-商品 ** 6-职员  8-套餐  * 9-往来单位
     * 基本信息组查库检查： 5-sku *
     *
     * @param baseInfoCheckRequest 基本信息检查请求
     * @return {@link List}<{@link BaseInfoCheckResponses}>
     */
    @PostMapping(value = "/baseInfoCheckRequest")
    public List<BaseInfoCheckResponses> baseInfoCheckRequest(@RequestBody BaseInfoCheckRequest baseInfoCheckRequest) {
        baseInfoCheckRequest.setProfileId(CurrentUser.getProfileId());
        BaseCheckQuoteService baseCheckQuoteService = map.get(baseInfoCheckRequest.getCheckType());
        //默认可以删除/停用
        if (null == baseCheckQuoteService || baseInfoCheckRequest.getOperationType() == 2) {
            List<BaseInfoCheckResponses> baseInfoCheckResponseList = new ArrayList<>();
            List<BigInteger> ids = baseInfoCheckRequest.getIds();
            for (BigInteger id : ids) {
                BaseInfoCheckResponses baseInfoCheckResponse = new BaseInfoCheckResponses();
                baseInfoCheckResponse.setMsg("");
                baseInfoCheckResponse.setSuccess(true);
                baseInfoCheckResponse.setId(id);
                baseInfoCheckResponseList.add(baseInfoCheckResponse);
            }
            return baseInfoCheckResponseList;
        }
        return baseCheckQuoteService.checkHasQuote(baseInfoCheckRequest);
    }

    @PostMapping(value = "/getEshopByPlatformTypes")
    public List<EshopInfo> getEshopByPlatformTypes(@RequestBody QueryEShopParameter queryEShopParameter) {
        if (queryEShopParameter == null) {
            queryEShopParameter = new QueryEShopParameter();
            queryEShopParameter.setQueryVirtual(true);
        }
        CommonUtil.initLimited(queryEShopParameter);
        List<Integer> ocategorys = queryEShopParameter.getOcategorys();
        ocategorys.add(0);
        ocategorys.add(1);
        ocategorys.add(2);
        //查询虚拟
        if (queryEShopParameter.isQueryVirtual()) {
            ocategorys.add(3);
        }
        queryEShopParameter.setOcategorys(ocategorys);
        List<EshopInfo> eshopByShopTypes = service.getEshopByShopTypes(queryEShopParameter);
        if (queryEShopParameter.isQueryFenXiao()) {
            return eshopByShopTypes;
        } else {
            return eshopByShopTypes.stream().filter(e -> !ShopType.MiddleGroundSupplier.equals(e.getEshopType())).collect(Collectors.toList());

        }
    }

    @PostMapping(value = "/getTradeStatusByEshopIds")
    public List<DropDownPluginEntity> getTradeStatusByEshopIds(@RequestBody List<BigInteger> eshopIds) {
        return service.getTradeStatusList(CurrentUser.getProfileId(), eshopIds);
    }

    @RequestMapping(value = "/getEshopOfSupportQueryStaff", method = RequestMethod.POST)
    @ApiOperation("查询支持下载员工信息的网店")
    public List<EshopInfo> getEshopOfSupportQueryStaff() {
        QueryEShopParameter queryParam = new QueryEShopParameter();
        List<ShopType> shopTypes = EshopUtils.listFeatureSupportedShopTypes(EshopQueryStaffInfosFeature.class.getSimpleName());
        if (CollectionUtils.isEmpty(shopTypes)) {
            return new ArrayList<>();
        }
        queryParam.setProfileId(CurrentUser.getProfileId());
        List<Integer> shopTypeIds = shopTypes.stream().map(ShopType::getCode).collect(Collectors.toList());
        queryParam.setShopTypes(shopTypeIds);
        CommonUtil.initLimited(queryParam);
        return service.getEshopByShopTypes(queryParam);
    }

    @PostMapping("queryJdongEshopAnyEncryptAuth")
    public String queryJdongEshopAnyEncryptAuth() {
        return service.queryJdongEshopAnyEncryptAuth();
    }

    @PostMapping("/checkAuth")
    public boolean checkAuth(@RequestBody QueryStockRuleParameter parameter) {
        EshopInfo eshopInfo = service.getEshopInfoById(parameter.getProfileId(), parameter.getEshopId());
        if (eshopInfo == null) {
            return false;
        }
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(eshopInfo.getEshopType());
        if (eshopPluginService.getCheckAuthType(commonRequest).equals(CheckAuthType.TOKEN) && !StringUtils.isEmpty(eshopInfo.getToken())) {
            return true;
        }
        if (eshopPluginService.getCheckAuthType(commonRequest).equals(CheckAuthType.APP_KEY) && !StringUtils.isEmpty(eshopInfo.getAppKey())) {
            return true;
        }
        if (eshopPluginService.getCheckAuthType(commonRequest).equals(CheckAuthType.TOKEN_EXPIRE)) {
            int compareTo = eshopInfo.getTokenExpireIn().compareTo(DateUtils.getDate());
            return compareTo >= 0;
        }
        return false;
    }

    @PostMapping("/getFirstOtype")
    public EshopInfo getFirstOtype(@RequestBody QueryEShopParameter parameter) {
        CommonUtil.initLimited(parameter);
        return service.getFirstOtype(parameter);
    }

    @PostMapping("/getEshopById")
    public EshopInfo getEshopById(@RequestBody QueryEShopParameter parameter) {
        return service.getEshopInfoById(parameter);
    }

    @GetMapping("/getShopTypeByFeatureName/{featureName}")
    public List<ShopType> getShopTypeByFeatureName(@PathVariable String featureName) throws NoSuchMethodException {
        FeatureEnum feature = EnumUtil.valueOf(FeatureEnum.class, featureName,
                FeatureEnum.class.getMethod("getFeature"));
        return EshopUtils.listFeatureSupportedShopTypes(feature.getFeature());
    }

    @GetMapping("/getShopTypeCodeByFeatureName/{featureName}")
    public List<Integer> getShopTypeCodeByFeatureName(@PathVariable String featureName) throws NoSuchMethodException {
        FeatureEnum feature = EnumUtil.valueOf(FeatureEnum.class, featureName,
                FeatureEnum.class.getMethod("getFeature"));
        List<ShopType> shopTypeList = EshopUtils.listFeatureSupportedShopTypes(feature.getFeature());
        return StockUtil.doBuildSupportShopTypes(shopTypeList);
    }

    @PostMapping(value = "/deleteAuthInfoRedis")
    public BaseResponse deleteAuthInfoRedis(@RequestBody EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        try {
            response.setSuccess(service.deleteAuthInfoRedis(eshopInfo));
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/getMeituanBrachEshop")
    public List<PlatformBranchEshopResult> getMeituanBrachEshop() {
        return service.getMeituanBrachEshop();
    }


    @GetMapping(value = "/getBrachEshopByGroupId/{groupId}")
    public List<PlatformBranchEshopResult> getBrachEshopByGroupId(@PathVariable String groupId) {
        return service.getBrachEshopByGroupId(groupId);
    }


    @PostMapping(value = "/saveOtype")
    public BaseResponse saveOtype(@RequestBody BaseStore baseStore) {
        BaseResponse response = service.saveOtype(baseStore);
        return response;
    }

    @PostMapping(value = "/doCheckIndependentAccounting")
    public BaseResponse doCheckIndependentAccounting(@RequestBody EShopPageInfo eshopInfo) {
        return service.checkIndependentAccounting(eshopInfo);
    }

    @PostMapping(value = "/addEshopAddressMapping")
    public void addEshopAddressMapping(@RequestBody EshopAddressMapping addressMapping) {
        service.addEshopAddressMapping(addressMapping);
    }

    @PostMapping(value = "/registerTmc")
    public BaseResponse registerTmc(@RequestBody EShopPageInfo eshopInfo) {
        return service.registerTmc(eshopInfo);
    }

    @PostMapping(value = "/baseInfoChangeNotify")
    public void baseInfoChangeNotify(@RequestBody String info) {
        service.baseInfoChangeNotify(info);
    }

    @PostMapping("/getAutoPickKtypeInfo")
    public AutoPickKtypePageInfo getAutoPickKtypeInfo(@RequestBody EShopPageInfo eshopInfo) {

        AutoPickKtypePageInfo result = eshopBusinessService.getEshopAutoPickKtypeConfig(eshopInfo.getOtypeId(), eshopInfo.getEshopType());
        EshopPlatformStoreMappingInDTO request = new EshopPlatformStoreMappingInDTO();
        request.setEshopId(eshopInfo.getOtypeId());
        request.setCorrespondFlag(Integer.valueOf(2));
        result.setPlatformKtypeList(storeSvc.queryListByEshopId(request));
        return result;
    }

    @PostMapping("/importBranchEshop")
    public String importBranchEshop(MultipartFile importFile) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        ThreadPool test = ThreadPoolFactory.build("branch_eshop_import");
        test.executeAsync(invoker -> {
            try {
                InputStream inputStream = importFile.getInputStream();
                ByteArrayOutputStream byteArrayOutputStream = cloneInputStream(inputStream);
                BufferedInputStream buffInputStream = new BufferedInputStream(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));
                service.importBranchEshop(buffInputStream, processLogger);
            } catch (Exception ex) {
                processLogger.appendMsg(ex.getMessage());
            } finally {
                processLogger.appendMsg("导入结束");
                processLogger.doFinish();
            }
        }, "");

        return taskId;
    }

    private static ByteArrayOutputStream cloneInputStream(InputStream input) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = input.read(buffer)) > -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
            return baos;
        } catch (IOException e) {
            LoggerFactory.getLogger(PtypeReleationController.class).error(e.getMessage(), e);
            return null;
        }
    }

    @PostMapping("/getEshopConfigPageInfo")
    public EshopConfigPageInfo getEshopConfigPageInfo(@RequestBody EShopPageInfo eshopInfo) {
        EshopConfigPageInfo configPageInfo = new EshopConfigPageInfo();
        try {
            configPageInfo.setProfileId(eshopInfo.getProfileId());
            configPageInfo.setOtypeId(eshopInfo.getOtypeId());
            configPageInfo.setShopType(eshopInfo.getEshopType());

            //是否支持自动上下架
            configPageInfo.setSupportAutoShelfOnOrOff(service.checkEshopSupportAutoShelfOnOrOff(eshopInfo.getProfileId(), eshopInfo.getOtypeId(), eshopInfo.getEshopType()));

            //是否支持自动创建往来单位
            configPageInfo.setSupportAutoCreateBtype(getSupportAutoCreateBtype(eshopInfo.getEshopType()));

            //回告往来单位
            configPageInfo.setSupportUploadBtype(EshopUtils.isFeatureSupported(EshopCustomerCallbackFeature.class, eshopInfo.getEshopType()));

            //是否支持开启回传发票
            configPageInfo.setAllowUploadEshopInvoice(EshopUtils.isFeatureSupported(EshopInvoiceFeature.class, eshopInfo.getEshopType()));

            //是否支持翱翔三方信息回传
            configPageInfo.setAllowAoXiang(service.checkEshopSupportAoXiang(eshopInfo.getEshopType()));
            //是否支持极速退款
            configPageInfo.setAllowAg(service.checkEshopSupportAG(eshopInfo.getEshopType()));

            //是否支持处理线上售后
            configPageInfo.setAllowProcessRefundOnline(service.processRefundOnline(eshopInfo));

            //当前网店类型的shopAccount的编辑状态是否需要检查授权状态
            configPageInfo.setNeedCheckShopAccount(service.needCheckShopAccountByShoptype(eshopInfo.getEshopType()));

            //修改网店账号是否需要重新授权
            configPageInfo.setModifyAccountNeedReAuth(eshopBusinessService.modifyAccountNeedReauth(eshopInfo.getEshopType()));

            configPageInfo.setSynFreightVisible(eshopBusinessService.synFreightVisible(eshopInfo.getEshopType()));

            //是否支持下载网店商品备注
            configPageInfo.setSupportDownloadSkuMemo(service.getShopTypeSupportDownloadSkuMemo(eshopInfo.getEshopType()));

            //是否为老客户
            configPageInfo.setOldVersion(service.isOldVersion());
            //是否支持显示网店分类
            configPageInfo.setAllowShowEshopClass(service.isAllowShowEshopClass());

        } catch (Exception ex) {
            logger.error("账套信息：{}，网店信息：{} 获取网店config信息失败=》getEshopConfigPageInfo，失败信息：{}", eshopInfo.getProfileId(), eshopInfo.getOtypeId(), ex.getMessage(), ex);
        }
        return configPageInfo;
    }

    private boolean getSupportAutoCreateBtype(ShopType shopType) {
        if (null == shopType) {
            return false;
        }
        HashMap<String, Boolean> autoCreateBtypeAndPlaintextPlatformDistributorName = saleBizConfig.getAutoCreateBtypeAndPlaintextPlatformDistributorName();
        if (null == autoCreateBtypeAndPlaintextPlatformDistributorName || autoCreateBtypeAndPlaintextPlatformDistributorName.isEmpty()) {
            return false;
        }
        Boolean ret = autoCreateBtypeAndPlaintextPlatformDistributorName.get(shopType.name().toLowerCase());
        return null != ret && ret;
    }

    @GetMapping(value = "/getStateSupport")
    public StateSubsidiesEntity getStateSupport() {
       return service.getStateSupport();
    }
    @PostMapping(value = "/saveStateSupport")
    public void saveStateSupport(@RequestBody StateSubsidiesEntity entity) {
        service.saveStateSupport(entity);
    }

    @PostMapping(value = "/saveEshopFromYdh")
    public BaseResponse saveEshopFromYdh(@RequestBody EShopPageInfo eshopInfo) {
        if(eshopInfo.getMode() == 0) {
            eshopInfo.setMode(1);
        }
        eshopInfo.setEshopAccount(eshopInfo.getOtypeFullname());
        return eshopBusinessService.saveEshopInfoFromYdh(eshopInfo);
    }
    @GetMapping("/getEshopAtype/{otypeId}")
    public Atype getEshopAtypeInfo(@PathVariable String otypeId) {
        return service.getEshopAtypeInfo(otypeId);
    }

    @PostMapping("/checkSubscribe")
    public boolean checkSubscribe(@RequestBody QueryEShopParameter parameter) {
        return service.checkSubscribe(parameter);
    }

    @PostMapping(value ="/getDefaultStock")
    public Stock getDefaultStock(){
        return eshopOrderBaseInfoService.getDefaultStock();
    }
}
