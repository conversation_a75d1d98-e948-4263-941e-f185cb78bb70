package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.DeliverCustomerQueryDTO;
import com.wsgjp.ct.sale.biz.jarvis.service.CustomerQueryService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description TODO
 * @Date 2020-04-29 16:29
 * @Created by lingxue
 */
@RestController
@Api(description = "自定义查询")
@RequestMapping("/${app.id}/jarvis/customerQuery")
public class JarvisCustomerQueryController {

    private CustomerQueryService customerQueryService;

    public JarvisCustomerQueryController(CustomerQueryService customerQueryService) {
        this.customerQueryService = customerQueryService;
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    public BigInteger add(@RequestBody CustomerRequest customerRequest) {
        return this.customerQueryService.add(CurrentUser.getProfileId(), customerRequest.getTitle(),customerRequest.getQueryCode(),
                CurrentUser.getEmployeeId(),customerRequest.getQueryParams());
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping("/update")
    public BigInteger update(@RequestBody CustomerRequest customerRequest) {
        return this.customerQueryService.update(CurrentUser.getProfileId(), customerRequest.getTitle(),customerRequest.getQueryCode(),
                customerRequest.getQueryId(),customerRequest.getQueryParams());
    }

    @ApiOperation(value = "修改名称", notes = "修改名称")
    @PostMapping("/modifyTitle")
    public void modifyTitle(BigInteger queryId,String title) {
        this.customerQueryService.modifyTitle(CurrentUser.getProfileId(), title,queryId);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/delete")
    public void delete(@RequestBody CustomerRequest customerRequest) {
        this.customerQueryService.delete(CurrentUser.getProfileId(), customerRequest.queryId, customerRequest.queryCode);
    }

    public static class CustomerRequest implements Serializable {
        private BigInteger queryId;
        private String title;
        private String queryCode;
        private Map<String, Object> queryParams;
        private String configJson;

        public String getConfigJson() {
            return configJson;
        }

        public void setConfigJson(String configJson) {
            this.configJson = configJson;
        }

        public BigInteger getQueryId() {
            return queryId;
        }

        public void setQueryId(BigInteger queryId) {
            this.queryId = queryId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getQueryCode() {
            return queryCode;
        }

        public void setQueryCode(String queryCode) {
            this.queryCode = queryCode;
        }

        public Map<String, Object> getQueryParams() {
            return queryParams;
        }

        public void setQueryParams(Map<String, Object> queryParams) {
            this.queryParams = queryParams;
        }
    }

    @ApiOperation(value = "查询", notes = "查询")
    @PostMapping("/list")
    public List<DeliverCustomerQueryDTO> list(@RequestBody CustomerRequest customerRequest) {
        return this.customerQueryService.list(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), customerRequest.getQueryCode());
    }

    @ApiOperation(value = "保存或更新配置", notes = "保存或更新配置")
    @PostMapping("/saveQueryConfig")
    public void saveQueryConfig(@RequestBody CustomerRequest customerRequest) {
        this.customerQueryService.saveQueryConfig(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(),customerRequest.getQueryCode(),
                customerRequest.getQueryId(), customerRequest.getConfigJson());
    }

    @ApiOperation(value = "根据id查询自定义查询信息", notes = "根据id查询自定义查询信息")
    @PostMapping("/queryCustomerQueryById")
    public DeliverCustomerQueryDTO queryCustomerQueryById(@RequestBody BigInteger queryId) {
        List<DeliverCustomerQueryDTO> queryResult = this.customerQueryService.queryCustomerQueryById(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), Arrays.asList(queryId));
        return queryResult.get(0);
    }
}


