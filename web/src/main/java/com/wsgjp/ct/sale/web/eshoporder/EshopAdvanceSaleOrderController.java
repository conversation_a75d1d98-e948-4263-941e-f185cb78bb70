package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderFreight;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceSubmitConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.OrderCreateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopCycleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchModifyOrderRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.PartialRefreshReuqest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.AdvanceOrderMarkParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryAdvanceDetailParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryAdvanceOrderParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.UpdateAdvanceOrderInfoParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryBaseTypeParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ManualOrderResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.SellerMemoResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.SubmitResponse;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopAdvanceOrderSysLog;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopAdvanceOrderSysQueryParams;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopAdvanceSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderManualService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.submit.impl.EshopAdvanceOrderSubmit;
import com.wsgjp.ct.sale.biz.eshoporder.util.SaleOrderExStatusAsyncHandleHelper;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import com.wsgjp.ct.sale.monitor.JarvisMonitorBuilder;
import com.wsgjp.ct.sale.monitor.entity.MonitorKeyConstant;
import com.wsgjp.ct.sale.web.eshoporder.entity.enums.PageMode;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.EshopSaleOrderInitRequest;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.EshopAdvanceOrderEditInitResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.monitor.support.MeterType;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-17
 */
@Api(tags = "预售订单相关")
@RequestMapping("${app.id}/eshoporder/advanceorder")
@RestController
public class EshopAdvanceSaleOrderController {
    private final EshopAdvanceSaleOrderService advanceSaleOrderService;
    private final EshopSaleOrderService orderService;
    private final EshopSaleOrderManualService manualService;
    private final EshopService eshopService;
    private static final Logger logger = LoggerFactory.getLogger(EshopAdvanceSaleOrderController.class);


    public EshopAdvanceSaleOrderController(EshopAdvanceSaleOrderService advanceSaleOrderService, EshopSaleOrderService orderService,
                                           EshopSaleOrderManualService manualService, EshopService eshopService) {
        this.advanceSaleOrderService = advanceSaleOrderService;
        this.orderService = orderService;
        this.manualService = manualService;
        this.eshopService = eshopService;
    }

    @PostMapping(value = "/partialRefresh")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    public List<EshopAdvanceOrderEntity> queryListOrders(@RequestBody PartialRefreshReuqest partialRefreshReuqest) {
        return advanceSaleOrderService.partialRefreshOrderList(partialRefreshReuqest.getVchcode());
    }

    @PostMapping(value = "/queryAdvanceOrders")
    // 打开预售订单监控埋点
//    @NgpResource(name = "pl.bs.eshoporder.page.open", tagStrings = "method,advanceorder.queryAdvanceOrders")
    public PageResponse<EshopAdvanceOrderEntity> queryAdvanceOrders(@RequestBody PageRequest<QueryAdvanceOrderParameter> pageParameter) {
        return advanceSaleOrderService.queryAdvanceOrders(pageParameter);
    }

    @PostMapping(value = "/queryAdvanceOrderDetails")
    public List<EshopAdvanceOrderDetail> queryOrdersDetails(@RequestBody QueryAdvanceDetailParameter parameter) {
        List<EshopAdvanceOrderDetail> detailList;
        detailList = advanceSaleOrderService.queryOrdersDetails(parameter);
        return detailList;
    }

    @PostMapping(value = "/queryAdvanceOrderFreights")
    public List<EshopAdvanceOrderFreight> queryOrderFreights(@RequestBody QueryAdvanceDetailParameter parameter) {
        return advanceSaleOrderService.queryOrderFreights(parameter);
    }

    @PostMapping(value = "/advanceSubmit")
    public SubmitResponse submit(@RequestBody QueryAdvanceOrderParameter parameter) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        EshopAdvanceOrderSubmit orderSubmit = new EshopAdvanceOrderSubmit(processLogger, parameter);
        SubmitResponse response = new SubmitResponse();
        parameter.setSubmit(true);
        ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.ORDER_MANUAL_SUBMIT_THREAD_NAME);
        threadPool.executeAsync(invoker -> {
            JarvisMonitorBuilder.NgpResource ngpResource = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_ADVANCE_SUBMIT,
                    null, MeterType.Summary, parameter.getEshopOrderIdList().size());
            ngpResource.start();
            GeneralResult<SubmitResponse> submit = orderSubmit.submit(null);
            processLogger.doFinish();
            ngpResource.end(StringUtils.isNotEmpty(submit.getMessage()));
        }, null);
        response.setTaskId(taskId);
        return response;
    }

    @RequestMapping(value = "/batchModifyBuyerMeessage", method = RequestMethod.POST)
    public SellerMemoResponse batchModifySellerMessage(@RequestBody BatchModifyOrderRequest parameter) {
        return advanceSaleOrderService.batchModifySellerMessage(parameter);
    }


    @RequestMapping(value = "/editInit", method = RequestMethod.POST)
    public EshopAdvanceOrderEditInitResponse getEditFormInitData(@RequestBody EshopSaleOrderInitRequest request) {
        String modeStr = request.getModeStr();
        PageMode mode = checkMode(modeStr);
        EshopAdvanceOrderEditInitResponse response = new EshopAdvanceOrderEditInitResponse(mode);
        response.setPriceControl(orderService.getPriceControl());
        if (mode == PageMode.MODIFY) {
            EshopAdvanceOrderEntity modifyOrder = advanceSaleOrderService.initModifyOrder(request.getId());
            response.setHasPostBill(false);
            response.setTitle("编辑预售订单");
            response.setAdvanceEntity(modifyOrder);
        } else if (mode == PageMode.COPY_CREATE) {
            response.setTitle("复制新增预售订单");
        } else {
            EshopAdvanceOrderEntity createOrder = advanceSaleOrderService.initCreateOrder();
            response.setTitle("新增订单");
            response.setAdvanceEntity(createOrder);
        }
        return response;
    }

    private PageMode checkMode(String modeStr) {
        for (PageMode mode : PageMode.values()) {
            if (mode.name().toLowerCase().equals(modeStr.toLowerCase())) {
                return mode;
            }
        }
        return PageMode.NEW;
    }

    @RequestMapping(value = "/saveAdvanceExpectDeliverTime", method = RequestMethod.POST)
    public BaseResponse saveAdvanceExpectDeliverTime(
            @RequestBody UpdateAdvanceOrderInfoParameter parameter) {
        return advanceSaleOrderService.saveAdvanceExpectDeliverTime(parameter);
    }


    @RequestMapping(value = "/modifyAdvanceTradeType", method = RequestMethod.POST)
    public BaseResponse modifyAdvanceTradeType(
            @RequestBody UpdateAdvanceOrderInfoParameter parameter) {
        return advanceSaleOrderService.modifyAdvanceTradeType(parameter);
    }


//    @RequestMapping(value = "/advanceGather", method = RequestMethod.POST)
//    public BaseResponse advanceGather(
//            @RequestBody UpdateAdvanceOrderInfoParameter parameter) {
//        BaseResponse baseResponse;
//        JarvisMonitorBuilder.NgpResource ngpResource = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_ADVANCE_GATHER,
//                "gatherType,"+parameter.getGatherType(), MeterType.Gauge, parameter.getAdvanceOrderEntities().size());
//        try {
//            ngpResource.start();
//            baseResponse = advanceSaleOrderService.advanceGather(parameter);
//            ngpResource.end(false);
//        } catch (Exception e) {
//            ngpResource.end(true);
//            throw e;
//        }
//        return baseResponse;
//    }

    @PostMapping(value = "/queryLogs")
    public PageResponse<EshopAdvanceOrderSysLog> queryLogs(@RequestBody PageRequest<EshopAdvanceOrderSysQueryParams> request) {
        PageResponse<EshopAdvanceOrderSysLog> ret = SysLogUtil.query(request);
        return SysLogUtil.query(request);
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ManualOrderResponse saveEshopSaleOrder(@RequestBody EshopAdvanceOrderEntity advanceOrder) {
        BigInteger otypeId = advanceOrder.getOtypeId();
        Otype otype = eshopService.getOtypeById(otypeId);
        if (otype == null || otype.isDeleted()) {
            throw new RuntimeException("网店已经被删除，保存订单失败！");
        }
        EshopSaleOrderEntity saleOrder = JsonUtils.toObject(JsonUtils.toJson(advanceOrder), EshopSaleOrderEntity.class);
        BigInteger vchcode = saleOrder.getId();
        saleOrder.setCreateType(OrderCreateType.INPUT);
        if (vchcode == null || vchcode.intValue() == 0) {
            // 新增
            return manualService.insertSaleOrder(saleOrder, otype);
        } else {
            // 编辑
            manualService.buildModifyOrder(saleOrder, otype);
            return advanceSaleOrderService.doModifyManualOrder(saleOrder, advanceOrder);
        }
    }

    @PostMapping("/updateOrderFlag")
    public void updateOrderFlag(@RequestBody QueryAdvanceOrderParameter parameter) {
        advanceSaleOrderService.updateAdvanceOrderFlag(parameter);

    }

    @PostMapping("/updateAdvanceOrder")
    public String updateAdvanceOrder(@RequestBody List<QueryAdvanceOrderParameter> parameter) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        try {
            ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.DOWNLOAD_ORDER_THREAD_NAME);
            threadPool.executeAsync(invoker -> {
                //子单主单都要查询
                parameter.forEach(p->p.setQueryCyclePurchaseType(2));
                parameter.forEach(p->p.setSubmit(false));
                List<EshopAdvanceOrderEntity> eshopAdvanceOrderEntities = advanceSaleOrderService.updateAdvanceOrder(parameter, processLogger, false);
                //订单做二次提交
                advanceSaleOrderService.doSaleOrderSubmit(eshopAdvanceOrderEntities);
                //徽标
                List<BigInteger> vchcodes = eshopAdvanceOrderEntities.stream().map(EshopAdvanceOrderEntity::getVchcode).collect(Collectors.toList());
                try {
                    logger.info("预售还原徽标发送消息，profileid:{}，vchcode: {}", CurrentUser.getProfileId(), vchcodes);
                    SaleOrderExStatusAsyncHandleHelper.notify(vchcodes);
                } catch (Exception e) {
                    logger.error("预售还原徽标发送消息，profileid:{}，vchcode: {}，原因:{}", CurrentUser.getProfileId(),vchcodes, e.getMessage());
                }
            }, null);
        } catch (Exception e) {
            processLogger.appendMsg("系统异常");
            processLogger.doFinish();
        }
        return taskId;
    }

    @RequestMapping("/checkAtypeIsDelete")
    public boolean checkAtypeIsDelete(@RequestBody QueryBaseTypeParameter parameter) {
        return eshopService.checkAtypeIsDelete(parameter);
    }

    @ApiOperation(value = "新增预售订单自定义标记")
    @PostMapping("insertAdvanceOrderMark")
    public void insertBillMarkRelationOld(@RequestBody AdvanceOrderMarkParam param) {
        List<AdvanceOrderMarkParam> markParams = advanceSaleOrderService.buildAdvanceOrdeerMarkReq(param);
        for (AdvanceOrderMarkParam markParam : markParams) {
            advanceSaleOrderService.insertAdvanceOrderMark(markParam);
        }
    }

    @ApiOperation(value = "删除预售订单自定义标记")
    @PostMapping("deleteAdvanceOrderMark")
    public String deleteAdvanceOrderMark(@RequestBody AdvanceOrderMarkParam param) {
        List<AdvanceOrderMarkParam> markParams = advanceSaleOrderService.buildAdvanceOrdeerMarkReq(param);
        for (AdvanceOrderMarkParam markParam : markParams) {
            advanceSaleOrderService.deleteAdvanceOrderMark(markParam);
        }
        return "删除成功";
    }

    @ApiOperation(value = "保存预售订单提交设置")
    @PostMapping("saveAdvanceSubmitConfig")
    public void saveAdvanceSubmitConfig(@RequestBody EshopAdvanceSubmitConfig config) {
        advanceSaleOrderService.saveAdvanceSubmitConfig(config);
    }

    @ApiOperation(value = "查询预售订单提交设置")
    @PostMapping("getAdvanceSubmitConfig")
    public EshopAdvanceSubmitConfig getAdvanceSubmitConfig() {
        return advanceSaleOrderService.getAdvanceSubmitConfig();
    }

    @ApiOperation(value = "获取配送周期相关信息")
    @PostMapping("/getCycleOrder")
    public List<EshopCycleOrderEntity> getCycleOrder(@RequestBody QueryAdvanceOrderParameter param) {
        if (null == param || CollectionUtils.isEmpty(param.getOtypeIds()) || StringUtils.isEmpty(param.getTradeOrderId())){
            return null;
        }
        return advanceSaleOrderService.getCycleOrder(param);
    }
}
