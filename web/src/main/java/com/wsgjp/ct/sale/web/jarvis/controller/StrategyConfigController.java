package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopAddressMapping;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopBusinessService;
import com.wsgjp.ct.sale.biz.jarvis.dto.imports.SplitStrategyDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.imports.request.SplitStrategyRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.imports.request.StockStrategyDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.imports.request.StockStrategyRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.StrategySaveCheckResponse;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.StrategyLogQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.*;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.audit.AuditStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.goodsAnalysis.GoodsAnalysisBaseConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.goodsAnalysis.GoodsAnalysisStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.goodsReplace.GoodsReplaceStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.goodsReplace.GoodsReplaceStrategySubConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.lack.LackStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.liveBroadCast.LiveBroadCastStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.lock.LockStrategyComboConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.lock.LockStrategyComplexConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.mark.MarkStrategyComboConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.merged.MergedCheckStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.merged.MergedStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.newStrategy.audit.AuditConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.newStrategy.author.AuthorConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.newStrategy.lock.LockConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.newStrategy.mark.MarkConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.newStrategy.merge.MergeConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.newStrategy.split.SplitConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.newStrategy.stock.WarehouseConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.refundAddress.RefundAddressStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.refundAddress.UpdateEshopAddressMappingByIdsRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.split.*;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.supplier.SupplierStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.warehouse.WarehouseStrategyComboConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.warehouse.WarehouseStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.service.ImportService;
import com.wsgjp.ct.sale.biz.jarvis.service.strategy.StrategyConfigService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyType;
import com.wsgjp.ct.sale.biz.jarvis.utils.excel.ImportCache;
import com.wsgjp.ct.sale.biz.jarvis.utils.excel.SplitStrategyImportListener;
import com.wsgjp.ct.sale.biz.jarvis.utils.excel.StockStrategyImportListener;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-01-27
 **/
@RestController
@Api(description = "发货单主表接口")
@RequestMapping("/${app.id}/jarvis/strategy")
public class StrategyConfigController {
    private StrategyConfigService strategyConfigService;
    private ImportService importService;
    private EshopBusinessService eshopBusinessService;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    public StrategyConfigController(StrategyConfigService strategyConfigService, ImportService importService, EshopBusinessService eshopBusinessService) {
        this.strategyConfigService = strategyConfigService;
        this.importService = importService;
        this.eshopBusinessService = eshopBusinessService;
    }

    /* --------------------- 仓储策略 ----------------------- */

    @ApiOperation(value = "创建仓库策略")
    @PostMapping("getNewWarehouseStrategyConfig")
    public StrategyConfigEntity createWarehouseStrategyConfig(@RequestBody WarehouseStrategyConfigEntity request) {
        return strategyConfigService.createWarehouseStrategyConfig(request);
    }

    @ApiOperation(value = "保存仓库策略")
    @PostMapping("saveWarehouseStrategyConfig")
    public boolean saveWarehouseStrategyConfig(@RequestBody WarehouseStrategyConfigEntity config) {
        return strategyConfigService.saveWarehouseStrategyConfig(Collections.singletonList(config));
    }

    @ApiOperation(value = "保存仓库组合相关策略")
    @PostMapping("saveWarehouseStrategyComboConfigs")
    public WarehouseStrategyComboConfig saveWarehouseStrategyComboConfigs(@RequestBody WarehouseStrategyComboConfig comboConfig) {
        return strategyConfigService.saveWarehouseStrategyComboConfigs(CurrentUser.getProfileId(), comboConfig);
    }

    @ApiOperation(value = "保存截停组合相关策略")
    @PostMapping("saveLockStrategyComboConfigs")
    public LockStrategyComboConfig saveLockStrategyComboConfigs(@RequestBody LockStrategyComboConfig comboConfig) {
        return strategyConfigService.saveLockStrategyComboConfigs(CurrentUser.getProfileId(), comboConfig);
    }

    @ApiOperation(value = "获取仓库组合相关策略")
    @PostMapping("getWarehouseStrategyComboConfigs")
    public WarehouseStrategyComboConfig getWarehouseStrategyComboConfigs(@RequestBody BigInteger id) {
        return strategyConfigService.getWarehouseStrategyComboConfigs(CurrentUser.getProfileId(), id);
    }

    @ApiOperation(value = "获取截停组合相关策略")
    @PostMapping("getLockStrategyComboConfigs")
    public LockStrategyComboConfig getLockStrategyComboConfigs(@RequestBody BigInteger id) {
        return strategyConfigService.getLockStrategyComboConfigs(CurrentUser.getProfileId(), id);
    }

    @ApiOperation(value = "获取仓库策略")
    @PostMapping("getWarehouseStrategyConfigs")
    public List<WarehouseStrategyConfigEntity> getWarehouseStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getWarehouseStrategyConfigs(CurrentUser.getProfileId(), StrategyType.STOCK, name);
    }

    @ApiOperation(value = "获取仓库策略")
    @PostMapping("getWarehouseStrategyConfigsBatchAd")
    public List<WarehouseStrategyConfigEntity> getWarehouseStrategyConfigsBatchAd(@RequestParam("name") String name) {
        return strategyConfigService.getWarehouseStrategyConfigs(CurrentUser.getProfileId(), StrategyType.STOCK, name);
    }

    /* --------------------- 标记策略 ----------------------- */

    @ApiOperation(value = "创建标记策略")
    @PostMapping("getNewMarkStrategyConfig")
    public StrategyConfigEntity createMarkStrategyConfig(@RequestBody StrategyConfigEntity request) {
        return strategyConfigService.createMarkStrategyConfig(request);
    }

    @ApiOperation(value = "获取标记策略")
    @PostMapping("getMarkStrategyConfigs")
    public List<StrategyConfigEntity> getMarkStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getStrategyConfigs(CurrentUser.getProfileId(), StrategyType.MARK, name);
    }

    @ApiOperation(value = "获取供应商策略")
    @PostMapping("getSupplierStrategyConfigs")
    public List<StrategyConfigEntity> getSupplierStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getStrategyConfigs(CurrentUser.getProfileId(), StrategyType.SUPPLIER, name);
    }

    //已确认，有问题
    @ApiOperation(value = "保存标记组合相关策略")
    @PostMapping("saveMarkStrategyComboConfigs")
    public MarkStrategyComboConfig saveMarkStrategyComboConfigs(@RequestBody MarkStrategyComboConfig comboConfig) {
        return strategyConfigService.saveMarkStrategyComboConfigs(CurrentUser.getProfileId(), comboConfig);
    }

    @ApiOperation(value = "获取标记组合相关策略")
    @PostMapping("getMarkStrategyComboConfigs")
    public MarkStrategyComboConfig getMarkStrategyComboConfigs(@RequestBody BigInteger id) {
        return strategyConfigService.getMarkStrategyComboConfigs(CurrentUser.getProfileId(), id);
    }

    /* --------------------- 拆分策略 ----------------------- */

    @ApiOperation(value = "创建拆分策略")
    @PostMapping("getNewSplitStrategyConfig")
    public StrategyConfigEntity createSplitStrategyConfig(@RequestBody SplitStrategyConfigEntity request) {
        return strategyConfigService.createSplitStrategyConfig(request);
    }

    @ApiOperation(value = "保存拆分策略")
    @PostMapping("saveSplitStrategyConfigs")
    public boolean saveSplitStrategyConfigs(@RequestBody SplitStrategyConfigEntity config) {
        return strategyConfigService.saveSplitStrategyConfigs(Collections.singletonList(config));
    }

    @ApiOperation(value = "获取拆分策略")
    @PostMapping("getSplitStrategyConfigs")
    public List<SplitStrategyConfigEntity> getSplitStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getSplitStrategyConfigs(CurrentUser.getProfileId(), name);
    }

    //已确认，会清空老数据
    @ApiOperation(value = "保存拆分商品相关策略")
    @PostMapping("saveSplitStrategyPTypeConfigs")
    public boolean saveSplitStrategyPTypeConfigs(@RequestBody SplitSpecialPTypeStrategyConfig pTypeConfigEntity) {
        return strategyConfigService.saveSplitStrategyPTypeConfigs(CurrentUser.getProfileId(), pTypeConfigEntity);
    }

    @ApiOperation(value = "获取拆分商品相关策略")
    @PostMapping("getSplitStrategyPTypeConfigs")
    public SplitSpecialPTypeStrategyConfig getSplitStrategyPTypeConfigs(@RequestBody BigInteger id) {
        return strategyConfigService.getSplitStrategyPTypeConfigs(CurrentUser.getProfileId(), id);
    }

    @ApiOperation(value = "保存数字拆分相关策略")
    @PostMapping("saveSplitDigitStrategyConfigs")
    public boolean saveSplitDigitStrategyConfigs(@RequestBody SplitDigitStrategyConfig pTypeConfigEntity) {
        return strategyConfigService.saveSplitDigitStrategyConfigs(CurrentUser.getProfileId(), pTypeConfigEntity);
    }

    @ApiOperation(value = "获取数字拆分相关策略")
    @PostMapping("getDigitSplitStrategyConfigs")
    public SplitDigitStrategyConfig getDigitSplitStrategyConfigs(@RequestBody BigInteger id) {
        return strategyConfigService.getDigitSplitStrategyConfigs(CurrentUser.getProfileId(), id);
    }

    /* --------------------- 按数量拆分策略 ----------------------- */
    @ApiOperation(value = "保存按数量拆分策略")
    @PostMapping("saveSplitByQtyStrategyConfigs")
    public SplitQtyConfig saveSplitByQtyStrategyConfigs(@RequestBody SplitQtyConfig config) {
        return strategyConfigService.createSplitByQtyStrategyConfig(config);
    }

    @ApiOperation(value = "获取按数量拆分策略")
    @PostMapping("getSplitByQtyStrategyConfigs")
    public SplitQtyConfig getSplitByQtyStrategyConfigs() {
        return strategyConfigService.getSplitByQtyStrategyConfigs(CurrentUser.getProfileId());
    }

    /* --------------------- 按商品拆分策略 ----------------------- */
    //已确认，未使用
    @ApiOperation(value = "保存按商品拆分策略")
    @PostMapping("saveSplitByPtypeStrategyConfigs")
    public SplitByPtypeConfig saveSplitByPtypeStrategyConfigs(@RequestBody SplitByPtypeConfig config) {
        return strategyConfigService.createSplitByPtypeStrategyConfig(config);
    }

    @ApiOperation(value = "获取按商品拆分策略")
    @PostMapping("getSplitByPtypeStrategyConfigs")
    public SplitByPtypeConfig getSplitByPtypeStrategyConfigs() {
        return strategyConfigService.getSplitByPtypeStrategyConfigs(CurrentUser.getProfileId());
    }

    /* --------------------- 按标记拆分策略 ----------------------- */
    @ApiOperation(value = "保存按标记拆分策略")
    @PostMapping("saveSplitByMarkStrategyConfigs")
    public SplitByMarkConfig saveSplitByMarkStrategyConfigs(@RequestBody SplitByMarkConfig config) {
        return strategyConfigService.createSplitByMarkStrategyConfig(config);
    }

    @ApiOperation(value = "获取按标记拆分策略")
    @PostMapping("getSplitByMarkStrategyConfigs")
    public SplitByMarkConfig getSplitByMarkeStrategyConfigs() {
        return strategyConfigService.getSplitByMarkStrategyConfigs(CurrentUser.getProfileId());
    }

    /* --------------------- 合并策略 ----------------------- */

    @ApiOperation(value = "创建合并策略")
    @PostMapping("addNewMergedStrategyConfig")
    public MergedStrategyConfigEntity createMergedStrategyConfig(@RequestBody MergedStrategyConfigEntity config) {
        return strategyConfigService.createMergedStrategyConfig(config);
    }

    @ApiOperation(value = "创建供应商策略")
    @PostMapping("addNewSupplierStrategyConfig")
    public SupplierStrategyConfigEntity createSupplierStrategyConfig(@RequestBody SupplierStrategyConfigEntity config) {
        return strategyConfigService.createSupplierStrategyConfig(config);
    }

    @ApiOperation(value = "保存审核策略")
    @PostMapping("saveMergedCheckStrategyConfigs")
    public boolean saveMergedCheckStrategyConfigs(@RequestBody MergedCheckStrategyConfigEntity config) {
        return strategyConfigService.saveMergedCheckStrategyConfigs(Collections.singletonList(config));
    }

    @ApiOperation(value = "获取合并策略")
    @PostMapping("getMergedStrategyConfigs")
    public List<StrategyConfigEntity> getMergedStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getStrategyConfigs(CurrentUser.getProfileId(), StrategyType.MERGED, name);
    }

    /* --------------------- 经手人策略 ----------------------- */

    @ApiOperation(value = "创建经手人策略")
    @PostMapping("getNewAuthorStrategyConfig")
    public StrategyConfigEntity createAuthorStrategyConfig(@RequestBody StrategyConfigEntity request) {
        return strategyConfigService.createAuthorStrategyConfig(request);
    }

    @ApiOperation(value = "获取经手人策略")
    @PostMapping("getAuthorStrategyConfigs")
    public List<StrategyConfigEntity> getAuthorStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getStrategyConfigs(CurrentUser.getProfileId(), StrategyType.AUTHOR, name);
    }

    /* --------------------- 缺货策略 ----------------------- */

    @ApiOperation(value = "创建缺货策略")
    @PostMapping("addNewLackStrategyConfig")
    public LackStrategyConfigEntity addNewLackStrategyConfig(@RequestBody LackStrategyConfigEntity config) {
        return strategyConfigService.addNewLackStrategyConfig(config);
    }

    @ApiOperation(value = "获取缺货策略")
    @PostMapping("getLackStrategyConfigs")
    public List<StrategyConfigEntity> getLackStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getStrategyConfigs(CurrentUser.getProfileId(), StrategyType.LACK, name);
    }

    /* --------------------- 截停策略 ----------------------- */

    @ApiOperation(value = "创建截停策略")
    @PostMapping("getNewLockStrategyConfig")
    public StrategyConfigEntity createLockStrategyConfig(@RequestBody StrategyConfigEntity request) {
        return strategyConfigService.createLockStrategyConfig(request);
    }

    @ApiOperation(value = "获取截停策略")
    @PostMapping("getLockStrategyConfigs")
    public List<StrategyConfigEntity> getLockStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getStrategyConfigs(CurrentUser.getProfileId(), StrategyType.LOCK, name);
    }

    @ApiOperation(value = "保存截停地区策略")
    @PostMapping("saveLockAreaConfig")
    public boolean saveLockAreaConfig(@RequestBody LockStrategyComplexConfigEntity areaConfigEntity) {
        return strategyConfigService.saveLockAreaConfig(CurrentUser.getProfileId(), areaConfigEntity);
    }
    @ApiOperation(value = "获取截停地区策略")
    @PostMapping("getLockAreaConfig")
    public LockStrategyComplexConfigEntity getLockAreaConfig(@RequestBody BigInteger id) {
        return strategyConfigService.getLockAreaConfig(CurrentUser.getProfileId(), id);
    }

    /* --------------------- 审核策略 ----------------------- */

    @ApiOperation(value = "创建审核策略")
    @PostMapping("getNewAuditStrategyConfig")
    public AuditStrategyConfigEntity createAuditStrategyConfig() {
        return strategyConfigService.createAuditStrategyConfig();
    }

    @ApiOperation(value = "保存审核策略")
    @PostMapping("saveAuditStrategyConfigs")
    public boolean saveAuditStrategyConfigs(@RequestBody AuditStrategyConfigEntity config) {
        return strategyConfigService.saveAuditStrategyConfigs(config);
    }

    @ApiOperation(value = "获取审核策略")
    @PostMapping("getAuditStrategyConfigs")
    public List<AuditStrategyConfigEntity> getAuditStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getAuditStrategyConfigs(CurrentUser.getProfileId(), name);
    }

    /* --------------------- 合并检查策略 ----------------------- */

    @ApiOperation(value = "获取合并检查策略")
    @PostMapping("getMergedCheckStrategyConfigs")
    public List<MergedCheckStrategyConfigEntity> getMergedCheckStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getMergedCheckStrategyConfigs(CurrentUser.getProfileId(), name);
    }

    /* --------------------- 商品替换策略 ----------------------- */
    @ApiOperation(value = "新增商品替换策略")
    @PostMapping("createGoodsReplaceStrategyConfigs")
    public StrategyConfigEntity createGoodsReplaceStrategyConfigs(@RequestBody StrategyConfigEntity config) {
        return strategyConfigService.createGoodsReplaceStrategyConfigs(CurrentUser.getProfileId(),config);
    }
    @ApiOperation(value = "保存商品替换策略")
    @PostMapping("saveGoodsReplaceStrategyConfigs")
    public GoodsReplaceStrategyConfigEntity saveGoodsReplaceStrategyConfigs(@RequestBody GoodsReplaceStrategyConfigEntity config) {
        return strategyConfigService.saveGoodsReplaceStrategyConfigs(CurrentUser.getProfileId(),config);
    }

    @ApiOperation(value = "获取商品替换策略列表")
    @PostMapping("getGoodsReplaceStrategyConfigs")
    public List<GoodsReplaceStrategyConfigEntity> getGoodsReplaceStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getGoodsReplaceStrategyConfig(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "获取商品替换子策略的具体内容")
    @PostMapping("getGoodsReplaceStrategySubConfigEntity")
    public GoodsReplaceStrategySubConfigEntity getGoodsReplaceStrategySubConfigEntity(@RequestParam BigInteger id) {
        return strategyConfigService.getGoodsReplaceStrategySubConfigEntity(CurrentUser.getProfileId(), id);
    }

    @ApiOperation(value = "保存商品替换子策略的具体内容")
    @PostMapping("saveGoodsReplaceStrategySubConfigEntity")
    public GoodsReplaceStrategySubConfigEntity saveGoodsReplaceStrategySubConfigEntity(@RequestBody GoodsReplaceStrategySubConfigEntity subConfigEntity) {
        return strategyConfigService.saveGoodsReplaceStrategySubConfigEntity(CurrentUser.getProfileId(), subConfigEntity);
    }

    @ApiOperation(value = "获取商品替换详细策略")
    @PostMapping("getGoodsReplaceStrategyPTypeConfig")
    public GoodsReplaceStrategyConfigEntity getGoodsReplaceStrategyPTypeConfig(@RequestParam BigInteger id) {
        return strategyConfigService.getGoodsReplaceStrategyPTypeConfig(CurrentUser.getProfileId(), id);
    }

    /* --------------------- 商品解析策略 ----------------------- */
    @ApiOperation(value = "新增商品解析策略")
    @PostMapping("createGoodsAnalysisStrategyConfigs")
    public StrategyConfigEntity createGoodsAnalysisStrategyConfigs(@RequestBody GoodsAnalysisStrategyConfigEntity config) {
        return strategyConfigService.createGoodsAnalysisStrategyConfigs(CurrentUser.getProfileId(),config);
    }

    //已确认，会清除
    @ApiOperation(value = "保存商品解析策略")
    @PostMapping("saveGoodsAnalysisStrategyConfigs")
    public StrategyConfigEntity saveGoodsAnalysisStrategyConfigs(@RequestBody StrategyConfigEntity config) {
        return strategyConfigService.saveGoodsAnalysisStrategyConfigs(CurrentUser.getProfileId(),config);
    }

    @ApiOperation(value = "保存商品解析策略具体规则")
    @PostMapping("saveGoodsAnalysisStrategyContent")
    public GoodsAnalysisBaseConfigEntity saveGoodsAnalysisStrategyContent(@RequestBody GoodsAnalysisBaseConfigEntity config) {
        return strategyConfigService.saveGoodsAnalysisStrategyContent(CurrentUser.getProfileId(),config);
    }
    @ApiOperation(value = "获取商品解析策略具体规则")
    @PostMapping("getGoodsAnalysisStrategyContent")
    public GoodsAnalysisBaseConfigEntity getGoodsAnalysisStrategyContent(@RequestBody GoodsAnalysisBaseConfigEntity config) {
        return strategyConfigService.getGoodsAnalysisStrategyContent(CurrentUser.getProfileId(), config.getId());
    }

    @ApiOperation(value = "获取商品解析策略列表")
    @PostMapping("getGoodsAnalysisStrategyConfigs")
    public List<StrategyConfigEntity> getGoodsAnalysisStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getGoodsAnalysisStrategyConfigs(CurrentUser.getProfileId(),name);
    }

    /* --------------------- 退货地址策略 ----------------------- */
    @ApiOperation(value = "获取退货地址策略")
    @PostMapping("getRefundAddressStrategyConfigs")
    public RefundAddressStrategyConfigEntity getRefundAddressStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getRefundAddressStrategyConfig(CurrentUser.getProfileId(), name);
    }

    @ApiOperation(value = "创建退货地址策略")
    @PostMapping("createRefundAddressStrategyConfig")
    public RefundAddressStrategyConfigEntity createRefundAddressStrategyConfig(@RequestBody RefundAddressStrategyConfigEntity configs) {
        return strategyConfigService.createRefundAddressStrategyConfig(configs);
    }

    @ApiOperation(value = "获取平台退货地址列表")
    @PostMapping("getEShopRefundAddressList")
    public List<EshopAddressMapping> getEShopRefundAddressList() {
        return strategyConfigService.queryAddressMapping(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "退货地址与平台退件地址创建对应关系")
    @PostMapping("updateEshopAddressMappingByIds")
    public void updateEshopAddressMappingByIds(@RequestBody UpdateEshopAddressMappingByIdsRequest request) {
        strategyConfigService.updateEshopAddressMappingByIds(CurrentUser.getProfileId(),request.getIds(),request.getRefundAddressId(),request.getOtypeIds());
    }

    /* --------------------- 直播策略 ----------------------- */
    @ApiOperation(value = "获取直播策略")
    @PostMapping("getLiveBroadcastStrategyConfigs")
    public List<StrategyConfigEntity> getLiveBroadcastStrategyConfigs(@RequestParam("name") String name) {
        return strategyConfigService.getStrategyConfigs(CurrentUser.getProfileId(), StrategyType.LIVE_BROADCAST, name);
    }

    @ApiOperation(value = "创建直播策略")
    @PostMapping("createLiveBroadCastStrategyConfig")
    public StrategyConfigEntity createLiveBroadCastStrategyConfig(@RequestBody LiveBroadCastStrategyConfigEntity request) {
        return strategyConfigService.createLiveBroadCastStrategyConfig(request);
    }

    @ApiOperation(value = "修改直播策略")
    @PostMapping("saveLiveBroadCastStrategyConfig")
    public boolean saveLiveBroadCastStrategyConfig(@RequestBody LiveBroadCastStrategyConfigEntity request) {
        return strategyConfigService.saveLiveBroadCastStrategyConfig(request);
    }

    /* --------------------- 公共方法 ----------------------- */

    @ApiOperation(value = "公共获取策略")
    @PostMapping("getStrategyConfig")
    public StrategyConfigBaseEntity getStrategyConfig(@RequestParam("id") BigInteger id) {
        return strategyConfigService.getStrategyConfig(CurrentUser.getProfileId(), id);
    }

    @ApiOperation(value = "公共保存策略")
    @PostMapping("saveStrategyConfigsToJson")
    public boolean saveStrategyConfigsToJson(@RequestBody StrategyConfigEntity config) {
        return strategyConfigService.saveStrategyConfigsToJson(Collections.singletonList(config));
    }

    @ApiOperation(value = "公共保存策略")
    @PostMapping("saveStrategyConfig")
    public boolean saveStrategyConfig(@RequestBody StrategyConfigEntity config) {
        return strategyConfigService.saveStrategyConfigs(Collections.singletonList(config));
    }

    @ApiOperation(value = "删除策略")
    @PostMapping("deleteStrategyConfig")
    public boolean deleteStrategyConfig(@RequestBody StrategyConfigEntity config) {
        return strategyConfigService.deleteRealStrategyConfig(CurrentUser.getProfileId(), config);
    }

    @ApiOperation(value = "修改策略")
    @PostMapping("updateStrategyConfigValue")
    public boolean updateStrategyConfigValue(@RequestBody StrategyConfigEntity config) {
        return strategyConfigService.updateStrategyConfigValue(CurrentUser.getProfileId(), config);
    }

    @ApiOperation(value = "获取newId")
    @PostMapping("getNewId")
    public BigInteger getNewId() {
        return UId.newId();
    }

    @ApiOperation(value = "保存和地区相关策略")
    @PostMapping("saveStrategyAreaConfigs")
    public boolean saveStrategyAreaConfigs(@RequestBody StrategyComplexConfigEntity areaConfigEntity) {
        return strategyConfigService.saveStrategyAreaConfigs(CurrentUser.getProfileId(), areaConfigEntity);
    }

    @ApiOperation(value = "获取地区相关策略")
    @PostMapping("getStrategyAreaConfigs")
    public List<StrategyComplexDetailEntity> getStrategyAreaConfigs(@RequestBody BigInteger id) {
        return strategyConfigService.getStrategyAreaConfigs(CurrentUser.getProfileId(), id);
    }

    @ApiOperation(value = "保存和地区相关策略")
    @PostMapping("saveStrategyAreaConfig")
    public boolean saveStrategyAreaConfig(@RequestBody StrategyComplexConfigEntity areaConfigEntity) {
        return strategyConfigService.saveStrategyAreaConfig(CurrentUser.getProfileId(), areaConfigEntity);
    }

//    @ApiOperation(value = "保存和地区相关策略")
//    @PostMapping("saveStrategyAreaForBatchEdit")
//    public boolean saveStrategyAreaForBatchEdit(@RequestBody StrategyBatchAddAreaEntity areaConfigEntity) {
//        return strategyConfigService.saveStrategyAreaForBatchEdit(CurrentUser.getProfileId(), areaConfigEntity);
//    }

    @ApiOperation(value = "获取地区相关策略")
    @PostMapping("getStrategyAreaConfig")
    public StrategyComplexConfigEntity getStrategyAreaConfig(@RequestBody BigInteger id) {
        return strategyConfigService.getStrategyAreaConfig(CurrentUser.getProfileId(), id);
    }

    @ApiOperation(value = "获取商品相关策略")
    @PostMapping("getStrategyPTypeConfigs")
    public List<StrategyComplexDetailEntity> getStrategyPTypeConfigs(@RequestBody BigInteger id) {
        return strategyConfigService.getStrategyPTypeConfigs(CurrentUser.getProfileId(), id);
    }

    //已确认，匹配商品指定标记会用，按商品截停会用，但会清除
    @ApiOperation(value = "保存商品相关策略")
    @PostMapping("saveStrategyPTypeConfigs")
    public boolean saveStrategyPTypeConfigs(@RequestBody StrategyComplexConfigEntity pTypeConfigEntity) {
        return strategyConfigService.saveStrategyPTypeConfigs(CurrentUser.getProfileId(), pTypeConfigEntity);
    }

    @ApiOperation(value = "获取商品相关策略")
    @PostMapping("getStrategyPTypeConfig")
    public StrategyComplexConfigEntity getStrategyPTypeConfig(@RequestBody BigInteger id) {
        return strategyConfigService.getStrategyPTypeConfig(CurrentUser.getProfileId(), id);
    }

    //已确认，会清除
    @ApiOperation(value = "保存商品相关策略")
    @PostMapping("saveStrategyPTypeConfig")
    public boolean saveStrategyPTypeConfig(@RequestBody StrategyComplexConfigEntity pTypeConfigEntity) {
        return strategyConfigService.saveStrategyPTypeConfig(CurrentUser.getProfileId(), pTypeConfigEntity);
    }

//    @ApiOperation(value = "保存商品相关策略")
//    @PostMapping("saveStrategyPTypeForBatchEdit")
//    public boolean saveStrategyPTypeForBatchEdit(@RequestBody StrategyBatchAddPtypeEntity strategyBatchAddPtypeEntity) {
//        return strategyConfigService.saveStrategyPTypeForBatchEdit(CurrentUser.getProfileId(), strategyBatchAddPtypeEntity);
//    }

    //已确认，按商品拆分模板在用，删除模板的时候会清除
    @ApiOperation(value = "保存商品相关策略")
    @PostMapping("saveSplitPTypes")
    public List<BigInteger> saveStrategyPTypeConfig(@RequestBody List<StrategyComplexDetailEntity> pTypeConfigEntity) {
        return strategyConfigService.saveSplitPtypes(CurrentUser.getProfileId(), pTypeConfigEntity);
    }

    @ApiOperation(value = "保存商品相关策略")
    @PostMapping("getSplitPTypes")
    public List<StrategyComplexDetailEntity> getSplitPTypes(@RequestBody List<BigInteger> ids) {
        return strategyConfigService.getSplitPtypes(CurrentUser.getProfileId(), ids);
    }

    @ApiOperation(value = "验证策略是否有重复的销售机构")
    @PostMapping("isSaveStrategyConfig")
    public StrategySaveCheckResponse isSaveStrategyConfig(@RequestBody StrategyConfigEntity entity) {
        return strategyConfigService.isSaveStrategyConfig(CurrentUser.getProfileId(), entity);
    }

    @ApiOperation(value = "执行策略时判断是否开启了某个策略")
    @GetMapping("hasActiveStrategyConfigs")
    public boolean hasActiveStrategyConfigs(String strategy) {
        return strategyConfigService.hasActiveStrategyConfigs(strategy);
    }

    @ApiOperation(value = "进入订单审核策略记录日志")
    @GetMapping("enterBillStrategy")
    public void enterBillStrategy() {
        strategyConfigService.enterBillStrategy();
    }

    @ApiOperation(value = "修改策略名称")
    @PostMapping("/modifyStrategy")
    public StrategySaveCheckResponse modifyStrategy(@RequestParam("strategyId") BigInteger strategyId, @RequestParam("title") String title, @RequestParam("status") Boolean status) {
        return strategyConfigService.modifyStrategy(CurrentUser.getProfileId(),title,status,strategyId);
    }

    @ApiOperation(value = "查询策略修改日志")
    @PostMapping("/listStrategyLogs")
    public PageResponse<DeliverStrategyInfoLog> listStrategyLogs(@RequestBody PageRequest<StrategyLogQueryParams> request) {
        return LogService.query(request);
    }

    @ApiOperation(value = "按分组导入商品", notes = "（）")
    @PostMapping("importPtypeByGroup")
    public void importPtypeByGroup(@RequestParam("file") MultipartFile file,@RequestHeader("msgId") String msgId,@RequestParam("config") String config) {
        SplitStrategyRequest request = new SplitStrategyRequest();
        request.setProfileId(CurrentUser.getProfileId());
        request.setMsgId(msgId);
        request.setCache(new ImportCache());
        request.setConfig(JsonUtils.toObject(config,SplitDigitStrategyConfig.class));
        //初始化redis
        RedisProcessMessage redisProcessMessage = new RedisProcessMessage(msgId);
        request.setMessage(redisProcessMessage);
        //导入前
        redisProcessMessage.getMsgLogger().appendMsg("开始导入");
        //导入
        SplitStrategyImportListener importListener = new SplitStrategyImportListener(request,importService);
        ThreadPoolFactory.build("import").executeAsync(req -> {
            try {
                EasyExcel.read(file.getInputStream(), SplitStrategyDTO.class, importListener).sheet().doRead();
            } catch (Exception e) {
                redisProcessMessage.getMsgLogger().appendMsg(e.getMessage());
                logger.error(String.format("导入报错：%s", e.getMessage()) ,e);
            } finally {
                redisProcessMessage.getMsgLogger().appendMsg("执行完毕");
                redisProcessMessage.setFinish();
            }
        },request);
    }

    @ApiOperation(value = "仓库策略-按商品匹配仓库-支持导入商品", notes = "（）")
    @PostMapping("importPtypeForStockStrategy")
    public void importPtypeForStockStrategy(@RequestParam("file") MultipartFile file,@RequestHeader("msgId") String msgId) {
        StockStrategyRequest request = new StockStrategyRequest();
        request.setProfileId(CurrentUser.getProfileId());
        request.setMsgId(msgId);
        request.setCache(new ImportCache());
        StrategyComplexConfigEntity strategyComplexConfigEntity = new StrategyComplexConfigEntity();
        strategyComplexConfigEntity.setContents(new ArrayList<>());
        request.setConfig(strategyComplexConfigEntity);
        //初始化redis
        RedisProcessMessage redisProcessMessage = new RedisProcessMessage(msgId);
        request.setMessage(redisProcessMessage);
        //导入前
        redisProcessMessage.getMsgLogger().appendMsg("开始导入");
        //导入
        StockStrategyImportListener importListener = new StockStrategyImportListener(request,importService);
        ThreadPoolFactory.build("import").executeAsync(req -> {
            try {
                EasyExcel.read(file.getInputStream(), StockStrategyDTO.class, importListener).sheet().doRead();
            } catch (Exception e) {
                if(!StringUtils.isEmpty(e.getMessage())){
                    boolean isStop = e.getMessage().contains("停止读取Excel文件");
                    String message = e.getMessage();
                    if(isStop){
                        message ="文件错误，停止读取Excel文件";
                    }
                    redisProcessMessage.getMsgLogger().appendMsg(message);
                }

                logger.error(String.format("导入报错：%s", e.getMessage()) ,e);
            } finally {
                redisProcessMessage.getMsgLogger().appendMsg("执行完毕");
                redisProcessMessage.setFinish();
            }
        },request);
    }

    /**************************************新策略功能*************************************************/
    @ApiOperation(value = "新策略-加载所有主策略", notes = "（）")
    @PostMapping("getMainStrategy")
    public StrategyConfigBaseEntity getMainStrategy() {
        return strategyConfigService.getMainStrategy(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "新策略-加载所有策略", notes = "（）")
    @PostMapping("modifyMainStatus")
    public void modifyMainStatus(@RequestParam("strategyType")StrategyType strategyType, @RequestParam("status")Boolean status) {
        strategyConfigService.modifyMainStatus(CurrentUser.getProfileId(),status,strategyType);
    }

    @ApiOperation(value = "获取指定策略--例如，拆分策略下的所有子策略的基本信息，不包含商品信息等，主要用户策略界面判断有几个子策略，用与界面控制")
    @PostMapping("getSubStrategyConfigs")
    public List<StrategyConfigEntity> getSubStrategyConfigs(@RequestParam("StrategyType")  StrategyType strategyType) {
        return strategyConfigService.getSubStrategyConfigs(CurrentUser.getProfileId(), strategyType, null);
    }

    @ApiOperation(value = "获取指拆分策略")
    @PostMapping("getNewSplitStrategyConfigs")
    public SplitConfig getNewSplitStrategyConfigs(@RequestParam("strategyId")  BigInteger strategyId) {
        return strategyConfigService.getNewSplitStrategyConfigs(CurrentUser.getProfileId(), strategyId);
    }

    @ApiOperation(value = "保存拆分策略")
    @PostMapping("saveNewSplitStrategyConfigs")
    public SplitConfig saveNewSplitStrategyConfigs(@RequestBody SplitConfig splitConfig) {
        return strategyConfigService.saveNewSplitStrategyConfigs(splitConfig);
    }

    @ApiOperation(value = "删除拆分策略")
    @PostMapping("deleteNewSplitStrategyConfigs")
    public boolean deleteNewSplitStrategyConfigs(@RequestParam("strategyId")  BigInteger strategyId) {
        return strategyConfigService.deleteNewSplitStrategyConfigs(CurrentUser.getProfileId(),strategyId);
    }

    @ApiOperation(value = "保存合并策略")
    @PostMapping("saveNewMergeStrategyConfigs")
    public MergeConfig saveNewMergeStrategyConfigs(@RequestBody MergeConfig mergeConfig) {
        return strategyConfigService.saveNewMergeStrategyConfigs(mergeConfig);
    }


    @ApiOperation(value = "检查仓库策略是否需要显示平台指定或推荐仓库配置")
    @PostMapping("checkEshopPlatformStoreMappingExist")
    public boolean checkEshopPlatformStoreMappingExist() {
        return eshopBusinessService.checkEshopPlatformStoreMappingExist(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "保存审核策略")
    @PostMapping("saveNewAuditStrategyConfigs")
    public AuditConfig saveNewAuditStrategyConfigs(@RequestBody AuditConfig config) {
        return strategyConfigService.saveNewAuditStrategyConfigs(config);
    }

    // ================================================新仓库策略========================================

    @ApiOperation(value = "保存仓库策略")
    @PostMapping("saveNewWarehouseStrategyConfigs")
    public WarehouseConfig saveNewWarehouseStrategyConfigs(@RequestBody WarehouseConfig config) {
        return strategyConfigService.saveNewWarehouseStrategyConfigs(config);
    }


    @ApiOperation(value = "获取仓库策略")
    @PostMapping("getNewWarehouseStrategyConfigs")
    public WarehouseConfig getNewWarehouseStrategyConfigs(@RequestParam("strategyId")  BigInteger strategyId) {
        return strategyConfigService.getNewWarehouseStrategyConfigs(CurrentUser.getProfileId(), strategyId);
    }

    // ================================================新截停策略=======================================


    @ApiOperation(value = "保存截停策略")
    @PostMapping("saveNewLockStrategyConfigs")
    public LockConfig saveNewLockStrategyConfigs(@RequestBody LockConfig config) {
        return strategyConfigService.saveNewLockStrategyConfigs(config);
    }

    @ApiOperation(value = "获取截停策略")
    @PostMapping("getNewLockStrategyConfigs")
    public LockConfig getNewLockStrategyConfigs(@RequestParam("strategyId")  BigInteger strategyId) {
        return strategyConfigService.getNewLockStrategyConfigs(CurrentUser.getProfileId(), strategyId);
    }

    // ================================================新标记策略=========================================
    @ApiOperation(value = "保存标记策略")
    @PostMapping("saveNewMarkStrategyConfigs")
    public MarkConfig saveNewMarkStrategyConfigs(@RequestBody MarkConfig config) {
        return strategyConfigService.saveNewMarkStrategyConfigs(config);
    }

    @ApiOperation(value = "获取标记策略")
    @PostMapping("getNewMarkStrategyConfigs")
    public MarkConfig getNewMarkStrategyConfigs(@RequestParam("strategyId")  BigInteger strategyId) {
        return strategyConfigService.getNewMarkStrategyConfigs(CurrentUser.getProfileId(), strategyId);
    }

    // ================================================新经手人策略========================================

    @ApiOperation(value = "保存经手人策略")
    @PostMapping("saveNewAuthorStrategyConfigs")
    public AuthorConfig saveNewAuthorStrategyConfigs(@RequestBody AuthorConfig config) {
        return strategyConfigService.saveNewAuthorStrategyConfigs(config);
    }

    @ApiOperation(value = "获取经手人策略")
    @PostMapping("getNewAuthorStrategyConfigs")
    public AuthorConfig getNewAuthorStrategyConfigs(@RequestParam("strategyId")  BigInteger strategyId) {
        return strategyConfigService.getNewAuthorStrategyConfigs(CurrentUser.getProfileId(), strategyId);
    }

    @ApiOperation(value = "获取审核策略")
    @PostMapping("getNewAuditStrategyConfigs")
    public List<AuditConfig> getNewAuditStrategyConfigs() {
        return strategyConfigService.getNewAuditStrategyConfigs(CurrentUser.getProfileId());
    }
}
