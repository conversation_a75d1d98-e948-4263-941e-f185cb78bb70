package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopRefundService;
import com.wsgjp.ct.sale.platform.dto.order.entity.ShopRefundAddress;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.PushMessageRequest;
import com.wsgjp.ct.sale.platform.entity.request.plugin.CommonRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.*;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.NormalResponse;
import com.wsgjp.ct.sale.platform.entity.response.refund.OrderRefundStatusResult;
import com.wsgjp.ct.sale.platform.entity.response.refund.RefundOrderDownloadResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "网店售后单相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/refund")
public class BifrostEshopRefundController {
    private final BifrostEshopRefundService refundService;

    public BifrostEshopRefundController(BifrostEshopRefundService refundService) {
        this.refundService = refundService;
    }

    @ApiOperation("全量下载售后单")
    @PostMapping("downloadByCreateTime")
    public RefundOrderDownloadResponse downloadByCreateTime(@RequestBody DownloadRefundByTimeRequest request) {
        RefundOrderDownloadResponse response = refundService.downloadRefundByCreateTime(request);
        return response;
    }

    @ApiOperation("增量下载售后单")
    @PostMapping("downloadByIncrementally")
    public RefundOrderDownloadResponse downloadIncrementally(@RequestBody DownloadRefundByTimeRequest request) {
        return refundService.downloadRefundIncrementally(request);
    }

    @ApiOperation("通过Id下载售后单")
    @PostMapping("downloadByIds")
    public RefundOrderDownloadResponse downloadById(@RequestBody DownloadRefundByParamRequest request) {
        return refundService.downloadRefundById(request);
    }

    @ApiOperation("通过订单信息下载售后单")
    @PostMapping("download/trade")
    public RefundOrderDownloadResponse downloadByTrade(@RequestBody DownloadRefundByTradesRequest request) {
        return refundService.downloadRefundOrderByTrades(request);
    }

    @ApiOperation("同意售后申请")
    @PostMapping("approve")
    public NormalResponse approve(@RequestBody ApproveRefundRequest request) {
        return refundService.approveRefundOrder(request);
    }

    @ApiOperation("拒绝售后申请")
    @PostMapping("reject")
    public NormalResponse reject(@RequestBody RejectRefundRequest request) {
        return refundService.rejectRefundOrder(request);
    }

    @ApiOperation("标记售后申请")
    @PostMapping("review")
    public NormalResponse review(@RequestBody ReviewRefundRequest request) {
        return refundService.reviewRefundOrder(request);
    }

    @ApiOperation("通知售后申请")
    @PostMapping("back/notice")
    public NormalResponse backNotice(@RequestBody RefundBackNoticeRequest request) {
        return refundService.backNotice(request);
    }

    @ApiOperation("登记收货信息")
    @PostMapping("stockReceived")
    public NormalResponse stockReceived(@RequestBody RefundBackNoticeRequest request) {
        return refundService.refundStockReceived(request);
    }

    @ApiOperation("检查AG极速退款开启状态")
    @PostMapping("checkAgEnabled")
    public boolean checkAgEnabled(@RequestBody CommonRequest request) {
        return refundService.checkAgEnabled(request);
    }

    @ApiOperation("AG(极速退款)确认")
    @PostMapping("confirmAg")
    public boolean confirmAg(@RequestBody AgConfirmParameter request) {
        return refundService.confirmAg(request);
    }

    @ApiOperation("取消订单回告")
    @PostMapping("orderCancelFeedback")
    public BaseResponse orderCancelFeedback(@RequestBody OrderCancelFeedbackRequest request) {
        return refundService.orderCancelFeedback(request);
    }

    @ApiOperation("售后单推送构建")
    @PostMapping("pushRefunds")
    public RefundOrderDownloadResponse pushRefunds(@RequestBody PushMessageRequest request) {
        return refundService.pushRefunds(request);
    }

    @ApiOperation("查询订单售后状态列表")
    @PostMapping("getRefundStatusList")
    public List<OrderRefundStatusResult> GetRefundStatusList(GetOrderRefundStatusRequest request) {
        return refundService.GetRefundStatusList(request);
    }

    @ApiOperation("查询店铺退货地址库")
    @PostMapping("getShopRefundAddressList")
    public List<ShopRefundAddress> getShopRefundAddressList(BaseRequest request) {
        return refundService.getShopRefundAddressList(request);
    }
}
