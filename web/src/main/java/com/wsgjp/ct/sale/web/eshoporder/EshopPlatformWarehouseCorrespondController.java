package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.EshopPlatformStoreMappingInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.PlatformStoreImportErrorData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopBizMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopPlatformStoreMappingService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopPlatformWarehouseCorrespondService;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.NormalRequest;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/5 10:08
 */
@Api(tags = "平台仓库对应")
@RequestMapping("${app.id}/eshoporder/eshopplatformwarehousecorrespond")
@RestController
public class EshopPlatformWarehouseCorrespondController {

    private final EshopPlatformWarehouseCorrespondService platformWarehouseCorrespondService;
    private final EshopPlatformStoreMappingService platformStoreMappingService;

    private final EshopBizMapper mapper;

    public EshopPlatformWarehouseCorrespondController(EshopPlatformWarehouseCorrespondService platformWarehouseCorrespondService, EshopPlatformStoreMappingService platformStoreMappingService, EshopBizMapper mapper) {
        this.platformWarehouseCorrespondService = platformWarehouseCorrespondService;
        this.platformStoreMappingService = platformStoreMappingService;
        this.mapper = mapper;
    }

    @PostMapping("/insertPlatformArtesianRotation")
    public BaseResponse insertPlatformArtesianRotation(@RequestBody EshopPlatformStoreMappingInDTO inDTO) {
        return platformWarehouseCorrespondService.insertPlatformArtesianRotation(inDTO);
    }

    @PostMapping("/refreshPlatformWarehouse")
    public BaseResponse refreshPlatformWarehouse(@RequestBody EshopPlatformStoreMappingInDTO inDTO) {
        if (inDTO != null) {
            inDTO.setType(1);
        }
        return platformStoreMappingService.refreshPlatformStore(inDTO);
    }

    @PostMapping("/platformStoreNameEdit")
    public BaseResponse platformStoreNameEdit(@RequestBody EshopPlatformStoreMappingInDTO inDTO) {
        return platformStoreMappingService.platformStoreNameEdit(inDTO);
    }

    @PostMapping("/saveFile")
    public List<PlatformStoreImportErrorData> uploadFile(MultipartFile importFile, BigInteger eshopId, String eshopName,
                                                         BigInteger ktypeId) {
        return platformStoreMappingService.uploadFileWarehouseCorrespond(importFile, eshopId, eshopName, ktypeId);
    }

    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody EshopPlatformStoreMappingInDTO inDTO) {
        return platformWarehouseCorrespondService.delete(inDTO);
    }

    @GetMapping(value = "/addSysLog")
    public void addSysLog() {
        PubSystemLogService.saveInfo("进入平台仓库对应");
    }


    @PostMapping("/checkIsShowFbpTip")
    public boolean checkIsShowFbpTip(@RequestBody NormalRequest request) {
        ShopType shopType = mapper.queryEshopTypeById(request.getProfileId(), request.getBigIntegerId());
        return shopType.equals(ShopType.JdongFBP) || shopType.equals(ShopType.JDong);
    }
}
