package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstant;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EnumState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.Pcategory;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.SafeQtyDeleteTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.SafeQtyTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuPageData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.MultiStockSyncSettingSaveEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryProductMarkRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryXcodeParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.CheckErrorInfo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.*;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopStockSyncLogger;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EnumStateService;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.stock.*;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.StockUtil;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.BatchProcessRequest;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.shopsale.service.StoreService;
import com.wsgjp.ct.sale.common.config.EshopOrderCommonConfig;
import com.wsgjp.ct.sale.common.constant.SysDataConst;
import com.wsgjp.ct.sale.common.entity.eshop.EshopSelectorData;
import com.wsgjp.ct.sale.common.entity.stock.NotifyMessageStockSyncParam;
import com.wsgjp.ct.sale.sdk.stock.entity.StockSaleQtyEntity;
import com.wsgjp.ct.sale.web.eshoporder.entity.CommonConst;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.stock.DeleteSafeQtyRequest;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.stock.StockRuleModifyRequest;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.BatchAddRuleResponse;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.StockSafeSaleQtyPageInitData;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.StockSyncPageInitData;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.dao.entity.SysDataEntity;
import com.wsgjp.ct.support.dao.mapper.SysDataMapper;
import com.wsgjp.ct.support.global.BaseConfig;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.ConfigFieldMetadata;
import com.wsgjp.ct.support.global.entity.IndustryConfig;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 24/4/2020 上午 9:11
 */
@Api(tags = "库存同步相关接口")
@RequestMapping("${app.id}/eshoporder/stock")
@RestController
public class EshopStockController {

	private final ManualStockSyncService manager;
	private final StockRuleService ruleService;
	private final SaleQtyMarkService markSvc;
	private final StockBuildService qtyService;
	private final EshopOrderCommonConfig eshopOrderCommonConfig;


	private final StockOversoldConfigService oversoldSvc;
	private final EshopOrderBaseInfoService baseInfoService;
	public EshopStockController(ManualStockSyncService manager, StockRuleService ruleService, SaleQtyMarkService markSvc, StockBuildService qtyService, EshopOrderCommonConfig eshopOrderCommonConfig, StockOversoldConfigService oversoldSvc, EshopOrderBaseInfoService baseInfoService) {
		this.manager = manager;
		this.ruleService = ruleService;
		this.markSvc = markSvc;
		this.qtyService = qtyService;
		this.eshopOrderCommonConfig = eshopOrderCommonConfig;
		this.oversoldSvc = oversoldSvc;
		this.baseInfoService = baseInfoService;
	}

	/**
	 * 库存同步查询
	 * @param query
	 * @return
	 */
	@PostMapping("/getStockPageData")
	@ApiOperation("库存同步网店商品列表")
	public PageResponse<StockSyncManagePageData> getStockSyncPageInfo(@RequestBody PageRequest<QueryStockPageDataParameter> query) {
		return manager.queryPageData(query);
	}

	@PostMapping("/getStockPageDataByEshop")
	@ApiOperation("库存同步网店商品列表")
	public List<StockSyncManagePageData> getStockPageDataByEshop(@RequestBody PageRequest<QueryStockPageDataParameter> query) {
		return manager.getStockPageDataByEshop(query);
	}

	@PostMapping("/getStockPageDataCount")
	@ApiOperation("库存同步网店商品列表总数")
	public int getStockSyncPageInfoCount(@RequestBody PageRequest<QueryStockPageDataParameter> query){
		return manager.queryPageDataCount(query.getQueryParams());
	}

	@PostMapping("/getPageDataDetail")
	@ApiOperation("获取库存同步商品明细")
	public List<StockSyncManagePageData> getPageDetail(@RequestBody QueryStockPageDataParameter parameter) {
		return manager.queryPageDetail(parameter);
	}


	@PostMapping("/refreshRowData")
	@ApiOperation("库存同步界面局部刷新")
	public StockSyncManagePageData refreshStockSyncRowData(@RequestBody StockSyncManagePageData rowData) {
		manager.refreshRowData(rowData);
		return rowData;
	}

	@PostMapping("/checkWarehouseType")
	@ApiOperation("库存同步界面局部刷新")
	public List<StockSyncManagePageData> checkWarehouseType(@RequestBody List<StockSyncManagePageData> rowData) {
		List<StockSyncManagePageData> stockSyncManagePageData = manager.checkWarehouseType(rowData);
		return stockSyncManagePageData;
	}

	@PostMapping("/getPageInitData")
	@ApiOperation("规则界面初始化数据")
	public StockSyncPageInitData initStockSyncPage() {
		StockSyncPageInitData initData = new StockSyncPageInitData();
		List<EnumState> ruleTypes = EnumStateService.getEnumState(CommonConst.RULE_TYPE_KEY);
		initData.setRuleTypeList(ruleTypes);
		initData.setStockSceneTypeList(EnumStateService.getEnumState(CommonConst.STOCK_SXENE_TYPE_KEY));
		initData.setSyncQtyTargetTypeList(EnumStateService.getEnumState(CommonConst.SYNC_QTY_TARGET_TYPE_KEY));
		initData.setStockMinLimitTypeList(EnumStateService.getEnumState(CommonConst.STOCK_MIN_LIMIT_TYPE_KEY));
		initData.setKtypeSource(ruleService.getBaseInfoKtypes());
		initData.setSyncTimeTypes(ruleService.GetStockSyncTimingTypes());
		initData.setWarehouseShopTypes(StockUtil.querySupportWarehouseSyncStockShopTypes());
		return initData;
	}

	@PostMapping("/getSafeQtyPageInit")
	@ApiOperation("超卖预警规则界面初始化数据")
	public StockSafeSaleQtyPageInitData getSafeQtyPageInit(){
		StockSafeSaleQtyPageInitData initData=new StockSafeSaleQtyPageInitData();
		List<Stock> ktypes = ruleService.getBaseInfoKtypes();
		IndustryConfig config = GlobalConfig.get(IndustryConfig.class);
		List<EshopSelectorData> eshopSelectorDataList = oversoldSvc.queryAllEshopSelectorData();
		eshopSelectorDataList = eshopSelectorDataList.stream().filter(e->!e.getEshopType().equals(ShopType.MiddleGroundSupplier)).collect(Collectors.toList());
		boolean enabled = baseInfoService.getSysData(StringConstant.SAFE_QTY_SYS_DATA_KEY);
		boolean productEnabled = baseInfoService.getSysData(StringConstant.PRODUCT_SAFE_QTY_SYS_DATA_KEY);
		boolean showDeleted = baseInfoService.getSysData(StringConstant.SAFE_QTY_SHOW_DELETED);
		initData.setEshopSafeQtyEnabled(enabled);
		initData.setProductSafeQtyEnabled(productEnabled);
		initData.setEshopSource(eshopSelectorDataList);
		initData.setKtypes(ktypes);
		initData.setEnabledProps(config.isEnabledProps());
		initData.setShowDeleted(showDeleted);
		return initData;
	}

	@PostMapping(value = "/getAllDefaultRule")
	@PageDataSource
	@ApiOperation("获取店铺默认同步规则列表")
	public PageResponse<StockSyncRule> getDefaultRuleList(@RequestBody PageRequest<QueryStockRuleParameter> pageRequest) {
		QueryStockRuleParameter parameter = pageRequest.getQueryParams();
		CommonUtil.initLimited(parameter);
		parameter.setShopTypes(StockUtil.querySupportSyncStockShopTypes());
		PageDevice.initPage(pageRequest);
		return PageDevice.readPage(ruleService.getDefaultRuleList(parameter));
	}
	@PostMapping(value = "/getAllProductSyncRule")
	@PageDataSource
	@ApiOperation("获取店铺商品同步规则列表")
	public PageResponse<ProductStockSyncRule> getAllProductSyncRule(@RequestBody PageRequest<QueryProductRuleParameter> pageRequest) {
		QueryProductRuleParameter parameter = pageRequest.getQueryParams();
		CommonUtil.initLimited(parameter);
		PageDevice.initPage(pageRequest);
		return PageDevice.readPage(ruleService.getProductRuleList(parameter));
	}

	@PostMapping(value = "/doAddProductRule")
	@ApiOperation("添加店铺商品同步规则")
	public String doAddProductRule(@RequestBody BatchProcessRequest<ProductStockSyncRule> pageRequest) {
		RedisProcessMessage processMessage = new RedisProcessMessage(pageRequest.getProcessId());
		RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
		messageLog.appendMsg("开始绑定数据");
		List<ProductStockSyncRule> productRuletList = pageRequest.getList();
		ruleService.addProductRuleList(productRuletList);
		messageLog.appendMsg("处理完成");
		processMessage.setFinish();
		return "";
	}
	@PostMapping(value = "/doDeleteProductRule")
	@ApiOperation("删除店铺商品同步规则")
	public String doDeleteProductRule(@RequestBody BatchProcessRequest<ProductStockSyncRule> pageRequest) {
		RedisProcessMessage processMessage = new RedisProcessMessage(pageRequest.getProcessId());
		RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
		messageLog.appendMsg("开始删除数据");
		List<ProductStockSyncRule> productRuletList = pageRequest.getList();
		ruleService.deleteProductRuleList(productRuletList);
		messageLog.appendMsg("处理完成");
		processMessage.setFinish();
		return "";
	}

	@PostMapping(value = "/getWarehouseRule")
	@PageDataSource
	@ApiOperation("获取分仓同步规则列表")
	public PageResponse<StockSyncRule> getWarehouseRuleList(@RequestBody PageRequest<QueryStockRuleParameter> pageRequest) {
		QueryStockRuleParameter parameter = pageRequest.getQueryParams();
		CommonUtil.initLimited(parameter);
		parameter.setShopTypes(StockUtil.querySupportWarehouseSyncStockShopTypes());
		PageDevice.initPage(pageRequest);
		return PageDevice.readPage(ruleService.queryWarehouseRuleList(parameter));
	}

	@PostMapping(value = "/getLadderRule")
	@PageDataSource
	@ApiOperation("获取阶梯库存同步规则列表")
	public PageResponse<StockSyncRule> getLadderRuleList(@RequestBody PageRequest<QueryStockRuleParameter> pageRequest){
		QueryStockRuleParameter parameter = pageRequest.getQueryParams();
		List<Integer> list = new ArrayList<>();
		list.add(ShopType.Doudian.getCode());
		parameter.setShopTypes(list);
		CommonUtil.initLimited(parameter);
		PageDevice.initPage(pageRequest);
		return PageDevice.readPage(ruleService.queryLadderDefaultRuleList(parameter));
	}

	@PostMapping("/getStockRules")
	@PageDataSource
	@ApiOperation("获取例外同步规则列表(分页)")
	public PageResponse<StockSyncRule> getStockRule(@RequestBody PageRequest<QueryStockRuleParameter> pageRequest) {
		QueryStockRuleParameter queryParams = pageRequest.getQueryParams();
		return PageDevice.readPage(ruleService.queryRulePageList(queryParams));
	}

	@PostMapping("/getAllStockRules")
	@ApiOperation("获取例外同步规则列表")
	public List<StockSyncRule> getAllStockRule(@RequestBody QueryStockRuleParameter queryParams) {
		return ruleService.queryRulePageList(queryParams);
	}

	@PostMapping("/getStockRuleById")
	@ApiOperation("获取例外同步规则列表")
	public StockSyncRule getStockRuleById(@RequestBody QueryStockRuleParameter queryParams) {
		List<StockSyncRule> ruleList = ruleService.queryRulePageList(queryParams);
		if(CollectionUtils.isEmpty(ruleList)){
			return null;
		}
		return ruleList.get(0);
	}

	@PostMapping("buildLockQty")
	@ApiOperation("构建例外规则的锁库数据")
	public List<StockRuleDetail> buildRuleLockUsedQty(@RequestBody StockSyncRule rule) {
		Pcategory pcategory = rule.getPcategory();
		if (pcategory == Pcategory.Combo) {
			ruleService.buildComboLockQty(rule);
		} else {
			ruleService.buildSkuLockQty(rule);
		}
		return rule.getStockRuleDetailList();
	}

	@PostMapping("/saveDefaultRule")
	@ApiOperation("保存默认同步规则")
	public void saveDefaultStockRule(@RequestBody StockSyncRule rule) {
		rule.setId(rule.getOtypeId());
		ruleService.saveDefaultRule(rule);
		if (!eshopOrderCommonConfig.getSpeedMode()) {
			ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
			threadPool.executeAsync(x -> markSvc.markStockRuleChange(rule.getOtypeId()), null);
		}
	}

	@PostMapping("/saveWarehouseRule")
	@ApiOperation("保存分仓默认同步规则")
	public void saveWarehouseRule(@RequestBody StockSyncRule rule) {
		ruleService.saveWarehouseRule(rule);
		if (!eshopOrderCommonConfig.getSpeedMode()) {
			ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
			threadPool.executeAsync(x -> markSvc.markStockRuleChange(rule.getOtypeId()), null);
		}
	}

	@PostMapping("/saveLadderDefaultRule")
	@ApiOperation("保存阶梯库存同步默认规则")
	public void saveLadderDefaultRule(@RequestBody StockSyncRule rule){
		ruleService.saveLadderDefaultRule(rule);
		//开启大促模式则不执行库存同步（不写入库存变更队列）
		if(!eshopOrderCommonConfig.getSpeedMode()) {
			ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
			threadPool.executeAsync(x -> markSvc.markStockRuleChange(rule.getOtypeId()), null);
		}
	}

	@PostMapping("/insertStockRule")
	@ApiOperation("新增例外同步规则")
	public void insertStockRule(@RequestBody StockSyncRule rule) {
		rule.setId(UId.newId());
		ruleService.checkFrozenStock(rule,null);
		ruleService.checkRuleExist(rule);
		ruleService.insertRule(rule);
		ruleService.recordRuleAddLog(rule);
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		threadPool.executeAsync(markSvc::markByRuleChange,rule);
	}

	@PostMapping("/updateStockRule")
	@ApiOperation("更新例外同步规则")
	public void updateStockRule(@RequestBody StockRuleModifyRequest request) {
		StockSyncRule rule = request.stockRule;
		StockSyncRule oldRule = request.getOldRule();
		if (StockRuleType.VirtualStock.equals(rule.getRuleType()))
		{
			List<StockRuleDetail> ruleDetails=rule.getStockRuleDetailList();
			for (StockRuleDetail detail:ruleDetails)
			{
				detail.setFrozenQty(rule.getCalculate().getRuleCalQty());
			}
		}
		ruleService.checkFrozenStock(rule, oldRule);
		ruleService.checkRuleExist(rule);
		ruleService.updateRule(rule);
		ruleService.recordRuleModifyLog(rule, oldRule);
		ruleService.notifySafeQtyByRuleChange(rule, oldRule);
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		threadPool.executeAsync(markSvc::markByRuleChange,rule);
	}


	@PostMapping("/batchInsertRule")
	@ApiOperation("批量写入例外同步规则")
	public BatchAddRuleResponse batchInsertRule(@RequestBody List<StockSyncRule> ruleList) {
		BatchAddRuleResponse response = new BatchAddRuleResponse();
		List<CheckErrorInfo> errorList = new ArrayList<>();
		List<StockSyncRule> successList = new ArrayList<>();
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		for (StockSyncRule rule : ruleList) {
			try {
				String ruleName = ruleService.buildRuleName(rule);
				rule.setId(UId.newId());
				rule.setRuleName(ruleName);
				ruleService.checkRuleExist(rule);
				ruleService.checkFrozenStock(rule,null);
				ruleService.insertRule(rule);
				ruleService.recordRuleAddLog(rule);
				threadPool.executeAsync(markSvc::markByRuleChange,rule);
				successList.add(rule);
			} catch (Exception ex) {
				CheckErrorInfo errorInfo = new CheckErrorInfo();
				errorInfo.setSource(String.format("商品【%s】", rule.getPtypeName()));
				errorInfo.setMessage(ex.getMessage());
				errorList.add(errorInfo);
			}
		}
		response.setErrorList(errorList);
		response.setSuccessList(successList);
		return response;
	}

	@PostMapping("/modifyProductSyncRule")
	@ApiOperation("修改商品使用的例外同步规则")
	public void modifyProductSyncRule(@RequestBody StockSyncManagePageData pageData) {
		ruleService.modifyProductSyncRule(pageData);
		ruleService.logProductSyncRuleChange(pageData);
		markSvc.markByRuleChange(pageData);
		ruleService.doNotifySafeQtyChangeByEshop(pageData.getProfileId(), pageData.getEshopId());
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		threadPool.executeAsync(markSvc::markByRuleChange,pageData);
	}

	@PostMapping("/batchModifyProductSyncRule")
	@ApiOperation("批量修改商品使用的例外同步规则")
	public void batchModifyProductSyncRule(@RequestBody List<StockSyncManagePageData> pageDataList) {
		if (CollectionUtils.isEmpty(pageDataList)) {
			return;
		}
		for (StockSyncManagePageData pageData : pageDataList) {
			ruleService.modifyProductSyncRule(pageData);
			ruleService.logProductSyncRuleChange(pageData);
			ruleService.doNotifySafeQtyChangeByEshop(pageData.getProfileId(), pageData.getEshopId());
			ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
			threadPool.executeAsync(markSvc::markByRuleChange,pageData);
		}
	}

	@PostMapping("/modifyProductSyncState")
	@ApiOperation("修改商品的同步状态")
	public String modifyProductSyncState(@RequestBody StockSyncManagePageData pageData) {
		if (pageData.getAllowAutoSync()|| pageData.isWarehouseSyncEnabled())
		{
			QueryProductRuleParameter parameter=new QueryProductRuleParameter();
			parameter.setProfileId(CurrentUser.getProfileId());
			parameter.setSkuList(Collections.singletonList(pageData.getSkuId()));
			List<ProductStockSyncRuleBase> rules=ruleService.getProductBaseRuleList(parameter);
			if (!rules.isEmpty())
			{
				return "商品已经被商品不自动同步规则使用，无法开启自动同步库存;如果想开启自动同步库存，请到商品同步库存规则列表删除对应商品！";
			}
		}
		ruleService.modifyProductSyncRule(pageData);
		ruleService.logProductSyncRuleStateChange(pageData);
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		threadPool.executeAsync(markSvc::markByRuleChange,pageData);
		return "";
	}

	@PostMapping("/batchModifySyncState")
	@ApiOperation("批量修改商品的同步状态")
	public String batchModifySyncState(@RequestBody StockSyncManageRequest request) {
		String  taskId=request.getProcessId();
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		threadPool.executeAsync(x->{
			List<StockSyncManagePageData> pageDataList=request.getNeedSaveList();
			ProcessLoggerImpl processLogger=new ProcessLoggerImpl(taskId);
			processLogger.appendMsg("开始处理");
			if (pageDataList==null||CollectionUtils.isEmpty(pageDataList)) {
				processLogger.appendMsg("没有需要处理的数据");
				processLogger.doFinish();
				return;
			}
			int logIndex=1;
			processLogger.appendMsg(String.format("总计有%s条数据需要处理",pageDataList.size()));
			processLogger.appendMsg(String.format("正在处理第%s条数据",logIndex));
			for (StockSyncManagePageData pageData : pageDataList) {
				processLogger.modifyMsg(String.format("正在处理第%s条数据",logIndex));
				ruleService.modifyProductSyncRule(pageData);
				ruleService.logProductSyncRuleStateChange(pageData);
				markSvc.markByRuleChange(pageData);
				logIndex++;
			}
			processLogger.modifyMsg("处理完毕");
			processLogger.doFinish();
		},null);
		return taskId;
	}

	@PostMapping("/clearProductSyncRule")
	@ApiOperation("清空商品的例外同步规则")
	public void clearProductSyncRule(@RequestBody StockSyncManagePageData pageData) {
		ruleService.clearProductSyncRule(pageData);
		ruleService.doNotifySafeQtyChangeByEshop(pageData.getProfileId(), pageData.getEshopId());
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		threadPool.executeAsync(markSvc::markByRuleChange,pageData);
	}

	@PostMapping("/batchClearProductSyncRule")
	@ApiOperation("批量清空商品的例外同步规则")
	public void batchClearProductSyncRule(@RequestBody List<StockSyncManagePageData> pageDataList) {
		if (CollectionUtils.isEmpty(pageDataList)) {
			return;
		}
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		for (StockSyncManagePageData pageData : pageDataList) {
			ruleService.clearProductSyncRule(pageData);
			ruleService.doNotifySafeQtyChangeByEshop(pageData.getProfileId(), pageData.getEshopId());
			threadPool.executeAsync(markSvc::markByRuleChange,pageData);
		}
	}

	@PostMapping("/deleteStockRule")
	@ApiOperation("删除例外同步规则")
	public void deleteStockRule(@RequestBody StockSyncRule stockSyncRule) {
		ruleService.notifySafeQtyByRuleChange(stockSyncRule, null);
		ruleService.deletedRule(stockSyncRule);
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		threadPool.executeAsync(markSvc::markByRuleChange,stockSyncRule);
	}

	@PostMapping("/batchDeleteStockRule")
	@ApiOperation("删除例外同步规则")
	public void batchDeleteStockRule(@RequestBody List<StockSyncRule> stockSyncRule) {
		ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.STOCK_SYNC_THREAD_NAME);
		for (StockSyncRule syncRule : stockSyncRule) {
			ruleService.notifySafeQtyByRuleChange(syncRule, null);
			ruleService.deletedRule(syncRule);
			threadPool.executeAsync(markSvc::markByRuleChange,syncRule);
		}
	}

	@PostMapping("/syncStock")
	@ApiOperation("手工同步库存")
	public String executeSyncStock(@RequestBody ManualStockSyncParameter parameter) {
		return manager.executeStockSync(parameter);
	}

	@PostMapping("/syncByMsgCenter")
	@ApiOperation("消息中心执行库存同步")
	public String syncByMsgCenter(@RequestBody NotifyMessageStockSyncParam param) {
		return manager.syncStockByNotifyMsg(param);
	}

	@PostMapping("/getXcodesByPtype")
	@ApiOperation("通过商品查询商家编码列表")
	public List<PtypeXcode> getXcodesByPtype(@RequestBody QueryXcodeParameter parameter) {
		return ruleService.getXcodes(parameter);
	}

	@PostMapping("/initStockLog")
	public StockLogInit initStockLog() {
		return new StockLogInit();
	}

	@PostMapping("/getStockLog")
	@ApiOperation("查询库存同步日志")
	public PageResponse<EshopStockSyncLogger> queryStockSyncLog(@RequestBody PageRequest<QueryStockSyncLogParameter> pageRequest) {
		QueryStockSyncLogParameter queryParams = pageRequest.getQueryParams();
		queryParams.setProfileId(CurrentUser.getProfileId());
		queryParams.setSyncState(2);
		return LogService.query(pageRequest);
	}

	@PostMapping("/getSaleQty")
	@ApiOperation("查询可销售库存")
	public List<StockSaleQtyEntity> querySaleQty(@RequestBody QuerySaleQtyParameter parameter) {
		List<BigInteger> skuIdList = parameter.getSkuIdList();
		List<BigInteger> ktypeIdList = parameter.getKtypeIdList();
		return qtyService.getSaleQtyList(skuIdList, ktypeIdList);
	}

	@PostMapping("/querySafeQtyConfigPageData")
	@ApiOperation("查询安全库存配置列表")
	public PageResponse<SafeSaleQtyConfigEntity> querySafeQtyConfigPageData(@RequestBody PageRequest<QuerySafeQtyPageDataParam> query){
		return oversoldSvc.queryPageData(query);
	}

	@PostMapping("/queryProductSafeQtyConfigPageData")
	@ApiOperation("查询安全库存配置列表")
	public PageResponse<SafeSaleQtyProductConfigEntity> queryProductSafeQtyConfigPageData(@RequestBody PageRequest<QuerySafeQtyPageDataParam> query){
		return oversoldSvc.queryProductPageData(query);
	}

	@PostMapping("/getEshopByConfigKtype")
	@ApiOperation("根据仓库查询相关配置的网店列表")
	public List<EshopSelectorData> getEshopByConfigKtype(@RequestBody QuerySyncConfigKtypeParam parameter){
		return oversoldSvc.getEshopByStockSyncConfig(parameter);
	}

	@PostMapping("/saveSafeSaleQtyConfig")
	@ApiOperation("保存超卖配置")
	public BigInteger saveSafeSaleQtyConfig(@RequestBody SaveSafeSaleQtyConfigParam param){
		return oversoldSvc.saveDefaultSafeQtyRule(param.getNewData(), param.getOldData());
	}
	@PostMapping("/checkSafeSaleQtyConfigExist")
	@ApiOperation("检查仓库超卖配置是否存在")
	public boolean CheckSafeSaleQtyConfigExist(@RequestBody SafeSaleQtyConfigEntity data){
		return oversoldSvc.CheckSafeSaleQtyConfigExist(data.getKtypeId());
	}
	@PostMapping("/CheckProductSafeSaleQtyConfigExist")
	@ApiOperation("检查商品超卖配置是否存在")
	public boolean CheckProductSafeSaleQtyConfigExist(@RequestBody SafeSaleQtyProductConfigEntity data){
		return oversoldSvc.CheckProductSafeSaleQtyConfigExist(data);
	}

	@PostMapping("/batchSaveSafeSaleQtyConfig")
	@ApiOperation("批量保存超卖配置")
	public List<SafeQtyConfigResponse> batchSaveSafeSaleQtyConfig(@RequestBody BatchSaveSafeSaleQtyConfigRequest param){
		return oversoldSvc.batchSaveDefaultSafeQtyRule(param);
	}

	@PostMapping("/saveProductSafeSaleQtyConfig")
	@ApiOperation("保存商品级别的超卖配置")
	public BigInteger saveProductSafeSaleQtyConfig(@RequestBody SaveProductSafeQtyConfigParam param){
		return oversoldSvc.saveProductSafeQtyRule(param.getNewData(), param.getOldData());
	}
	@PostMapping("/batchSaveProductSafeSaleQtyConfig")
	@ApiOperation("批量保存商品级别的超卖配置")
	public List<SafeQtyConfigResponse> batchSaveProductSafeSaleQtyConfig(@RequestBody BatchSaveProductSafeQtyConfigRequest param){
		return oversoldSvc.bacthSaveProductSafeQtyRule(param);
	}

	@PostMapping("/getDefaultInitData")
	@ApiOperation("获取默认规则解密初始化数据")
	public StockSyncRule getDefaultRuleModifyInitData(@RequestBody QueryStockRuleParameter parameter) {
		return ruleService.getNormalDefaultRule(parameter);
	}

	@PostMapping("/deleteSafeQty")
	@ApiOperation("删除安全库存配置")
	public void doDeleteSafeQtyCfg(@RequestBody DeleteSafeQtyRequest request){
		List<BigInteger> idList = request.getConfigIdList();
		BigInteger profileId = request.getProfileId();
		SafeQtyDeleteTypeEnum deleteType = request.getDeleteType();
		if(CollectionUtils.isEmpty(idList)){
			return;
		}
		if(request.getSafeQtyType()==null){
			throw new RuntimeException("安全库存配置的类型不能为空!");
		}
		if(request.getSafeQtyType().equals(SafeQtyTypeEnum.DEFAULT)){
			oversoldSvc.doStopSafeQtyCfg(profileId, idList, deleteType);
		}else {
			oversoldSvc.doStopProductSafeQtyCfg(profileId, idList, deleteType);
		}
	}

	static class StockLogInit {
		public String getBegin() {
			SimpleDateFormat sdf1 = new SimpleDateFormat(SysDataConst.DATE_TIME_PATTERN);
			Date date = new Date();
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.DATE, -6);
			calendar.set(Calendar.HOUR_OF_DAY, 0);
			calendar.set(Calendar.MINUTE, 0);
			calendar.set(Calendar.SECOND, 0);
			return sdf1.format(calendar.getTime());
		}

		public String getEnd() {
//			SimpleDateFormat sdf1 = new SimpleDateFormat(SysDataConst.DATE_TIME_PATTERN);
//			return sdf1.format(new Date());
			String start = DateUtils.formatDate(new Date(), SysDataConst.DATE_PATTERN);
			return String.format("%s 23:59:59", start);
		}

		public String getMinValue() {
			SimpleDateFormat sdf1 = new SimpleDateFormat(SysDataConst.DATE_TIME_PATTERN);
			Date date = new Date();
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.DATE, -60);
			return sdf1.format(calendar.getTime());
		}
	}


	@PostMapping("/getStockConfigList")
	@PageDataSource
	@ApiOperation("获取库存同步规则列表(分页)")
	public PageResponse<StockSyncConfigAllData> getStockConfigList(@RequestBody PageRequest<QueryStockConfigParameter> pageRequest) {
		QueryStockConfigParameter queryParams = pageRequest.getQueryParams();
		return PageDevice.readPage(ruleService.queryStockSyncConfigPageList(queryParams));
	}

	@PostMapping("/getStockConfigListCount")
	@ApiOperation("获取库存同步规则列表数量")
	public int getStockConfigListCount(@RequestBody PageRequest<QueryStockConfigParameter> query){
		return ruleService.queryStockSyncConfigPageListCount(query.getQueryParams());
	}


	@PostMapping("/AddStockConfig")
	@ApiOperation("添加库存同步规则")
	public BatchAddRuleResponse AddStockConfig(@RequestBody  StockSyncConfigAllData config) {
		BatchAddRuleResponse response = new BatchAddRuleResponse();
		List<CheckErrorInfo> errorInfos = ruleService.SaveStockConfigAndApplys(config);
		response.setErrorList(errorInfos);
		return response;
	}
	@PostMapping("/DeletedStockConfig")
	@ApiOperation("添加库存同步规则")
	public boolean DeletedStockConfig(@RequestBody  List<StockSyncConfigAllData> config) {
		ruleService.DeletedStockConfig(config);
		return true;
	}


	@PostMapping("/queryStockSyncConfigApplyEshopidMap")
	@ApiOperation("获取网店库存同步规则应用列表")
	public Map<BigInteger, List<StockSyncConfigApply>> queryStockSyncConfigApplyEshopidMap(@RequestBody QueryStockConfigParameter pageRequest) {
		return  ruleService.queryStockSyncConfigApplyEshopidMap(pageRequest);
	}

	@PostMapping("/modifyAutoSyncState")
	@ApiOperation("修改规则的开启状态")
	public String modifyAutoSyncState(@RequestBody StockSyncConfigAllData data) {
		ruleService.modifyAutoSyncState(data);
		return "";
	}

	@PostMapping("/getSkuTimingSyncDetail")
	@ApiOperation("获取库存同步商品明细")
	public MultiStockSyncSettingSaveEntity getSkuTimingSyncDetail(@RequestBody QueryProductMarkRequest parameter) {
		return ruleService.getMultiStockSyncSetting(parameter);
	}
	@PostMapping("/saveSkuMultiStockSyncSettingInfo")
	@ApiOperation("获取库存同步商品明细")
	public void saveSkuMultiStockSyncSettingInfo(@RequestBody MultiStockSyncSettingSaveEntity data) {
		if (ngp.utils.CollectionUtils.isEmpty(data.getUniqueIds()))
		{
			throw new RuntimeException("参数错误，没有传入网店商品信息");
		}
		if (ngp.utils.CollectionUtils.isEmpty(data.getSetting()))
		{
			throw new RuntimeException("参数错误，没有传入时效规则信息");
		}
		ruleService.saveSkuMultiStockSyncSettingInfo(data);
	}

	@ApiOperation(value = "获取可销售库存配置", notes = "获取可销售库存配置")
	@GetMapping("/getSaleQtySysData")
	public Map<String, Object> getAllSysData() {
		Map<String, Object> result = new HashMap<>();
		List<String> keys = Arrays.asList("sysGlobalSaleQtyNonPaidOrder","sysGlobalSaleQtyErrBusinessOrder",
				"recordsheetSaleOrderAvailableStock","recordsheetBuyOrderAvailableStock",
				"sysGlobalUnAccountInBillAvailableStock","sysGlobalUnAccountOutBillAvailableStock",
				"sysGlobalOnWayAvailableStock","sysGlobalTransferOrderAvailableStock");
		List<SysDataEntity> dateList = GetBeanUtil.getBean(SysDataMapper.class)
				.listSysDataEntities(CurrentUser.getProfileId(), keys);
		Map<String,List<SysDataEntity>> sysDataMap = CollectionUtils.isEmpty(dateList) ? new HashMap<>() : dateList.stream().collect(Collectors.groupingBy(SysDataEntity::getSubName));
		for (String key : keys) {
			if (!sysDataMap.containsKey(key))
			{
				result.put(key, 0);
				continue;
			}
			String value =  sysDataMap.get(key).get(0).getSubValue();
			if(StringUtils.isEmpty(value) || value.toLowerCase().equals("false"))
			{
				value = "0";
			}
			if(value.toLowerCase().equals("true"))
			{
				value = "1";
			}
			int num = Integer.parseInt(value);
			result.put(key, num);
		}
		return result;
	}

	@RequestMapping(value = "/syncStockMulti", method = RequestMethod.POST)
	@ApiOperation("新版本-手工同步库存")
	public String queryMultiStockSync(@RequestBody List<EshopProductSkuPageData> pageDataList){
		return manager.executeStockSyncNew(pageDataList);
	}
}
