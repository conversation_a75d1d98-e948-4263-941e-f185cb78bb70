package com.wsgjp.ct.sale.web.jarvis.request.wms;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 */
public enum WmsDeliverFilterTypeEnum implements CodeEnum {
    /**
     * 单据编号
     */
    BILL_NUMBER(0, "单据编号"),
    /**
     * 订单编号
     */
    TRADE_ID(1, "订单编号"),
    /**
     * 商品名称
     */
    PTYPE_NAME(2, "商品名称"),
    /**
     * 商品编号
     */
    PTYPE_CODE(3, "商品编号"),
    /**
     * 商品条码
     */
    PTYPE_BARCODE(4, "商品条码");
    private final int code;
    private final String name;

    WmsDeliverFilterTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
