package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import bf.datasource.page.Sort;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.BillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.service.JarvisTestService;
import com.wsgjp.ct.support.log.provider.MysqlLogProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/${app.id}/jarvis/test")
public class JarvisTestController {

    private final JarvisTestService service;
    private final TestConfig config;
    private final MysqlLogProvider mysqlLogProvider;

    public JarvisTestController(JarvisTestService service, TestConfig config, MysqlLogProvider mysqlLogProvider) {
        this.service = service;
        this.config = config;
        this.mysqlLogProvider = mysqlLogProvider;
    }

    @PostMapping("/post")
    public long post(PostData postData, HttpServletRequest servletRequest) {
        return System.currentTimeMillis();
    }

    @GetMapping("/getDbName")
    public String get(BigInteger profileId, BigInteger vchcode) {

        return mysqlLogProvider.getDbName(profileId + (vchcode == null ? "" : vchcode.toString()));
    }

    @GetMapping("/insert")
    public long insert() {
        service.insertDeliver();
        return System.currentTimeMillis();
    }

    @GetMapping("/selectin")
    public PageResponse<BillDeliverDTO> selectIn() {
        PageRequest request = new PageRequest();
        Sort sort = new Sort();
        sort.setAscending(false);
        sort.setDataField("pay_time");
        request.setSorts(Arrays.asList(sort));
        return service.selectIn(request);
    }

    @GetMapping("/add")
    public long add() {
        service.add();
        return System.currentTimeMillis();
    }

    @GetMapping("/get")
    public BillDeliverState get() {
        return service.get();
    }

    @GetMapping("/modify")
    public long modify() {
        service.modify();
        return System.currentTimeMillis();
    }

    @GetMapping("/list")
    public List<BillDeliverDTO> list() {
        return service.list();
    }

    @GetMapping("/config")
    public String config() {
        return config.toString();
    }

    @PostMapping("/addMark")
    public void addMark(@RequestBody AddMark addMark) {
        //service.addMark(addMark.getMarkCode(), addMark.getVchcodes(), CurrentUser.getProfileId(), addMark.getDetailId());
    }

    public static class AddMark {
        private int markCode;
        private List<BigInteger> vchcodes;
        private List<BigInteger> detailId;

        public List<BigInteger> getDetailId() {
            return detailId;
        }

        public void setDetailId(List<BigInteger> detailId) {
            this.detailId = detailId;
        }

        public int getMarkCode() {
            return markCode;
        }

        public void setMarkCode(int markCode) {
            this.markCode = markCode;
        }

        public List<BigInteger> getVchcodes() {
            return vchcodes;
        }

        public void setVchcodes(List<BigInteger> vchcodes) {
            this.vchcodes = vchcodes;
        }
    }


    /*@GetMapping("/testRedis")
    public void testRedis() throws InterruptedException {
        for (int i = 0; i < 10; i++) {
            new Thread(() -> {
                LockEntity lock = new LockEntity(new BigInteger("123456"), LockerOperationEnum.AUDIT, new BigInteger("123456"), "number1", System.currentTimeMillis());
                try (DistributedLocker redisLocker = LockerFactory.getRedisLocker(lock)) {
                    Thread.sleep(7000);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }).start();
        }
        Thread.sleep(6000);
        for (int i = 0; i < 5; i++) {
            new Thread(() -> {
                LockEntity lock = new LockEntity(new BigInteger("123456"), LockerOperationEnum.AUDIT, new BigInteger("123456"), "number2", System.currentTimeMillis());
                try (DistributedLocker redisLocker = LockerFactory.getRedisLocker(lock)) {
                    Thread.sleep(2000);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }).start();
        }

    }*/



    @Configuration
    @ConfigurationProperties(prefix = "hhj.test")
    public static class TestConfig {
        private String a;
        private String b;
        @Value("${hhj.test.c:sssss}")
        private String e;

        @Override
        public String toString() {
            return "TestConfig{" +
                    "a='" + a + '\'' +
                    ", b='" + b + '\'' +
                    ", e='" + e + '\'' +
                    '}';
        }

        public String getA() {
            return a;
        }

        public void setA(String a) {
            this.a = a;
        }

        public String getB() {
            return b;
        }

        public void setB(String b) {
            this.b = b;
        }

        public String getE() {
            return e;
        }

        public void setE(String e) {
            this.e = e;
        }
    }

    public static class PostData {
        private String a;
        private Integer b;

        public String getA() {
            return a;
        }

        public void setA(String a) {
            this.a = a;
        }

        public Integer getB() {
            return b;
        }

        public void setB(Integer b) {
            this.b = b;
        }
    }
}
