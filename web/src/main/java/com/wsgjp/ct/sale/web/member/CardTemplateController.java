package com.wsgjp.ct.sale.web.member;

import bf.datasource.page.PageRequest;
import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.framework.enums.AssertsChangeType;
import com.wsgjp.ct.framework.enums.AssertsSourceOperation;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.common.AssertBillType;
import com.wsgjp.ct.sale.biz.member.common.CardSourceType;
import com.wsgjp.ct.sale.biz.member.common.CustomResult;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.member.config.PosRedisLockTimeConfig;
import com.wsgjp.ct.sale.biz.member.model.dto.card.CardRecordDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.card.CardRecordQueryDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.card.GiveCardToVipRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.store.SsCardAssertBillDetailDto;
import com.wsgjp.ct.sale.biz.member.model.dto.store.SsCardAssertBillDto;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCardTemplate;
import com.wsgjp.ct.sale.biz.member.service.ISsCardAssertBillService;
import com.wsgjp.ct.sale.biz.member.service.ISsCardTemplateService;
import com.wsgjp.ct.sale.biz.member.utils.PosRedisLockerUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.annontaion.NgpResource;
import ngp.starter.web.base.ResultCode;
import ngp.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Api(tags = "卡券模板管理")
@RestController
@RequestMapping("${app.id}/member/cardTemplate")
public class CardTemplateController {
    @Autowired
    ISsCardTemplateService cardTemplateService;
    @Autowired
    private ISsCardAssertBillService ssCardAssertBillService;
    @Autowired
    private PosRedisLockTimeConfig posRedisLockTimeConfig;

    private static final String redisKey = "giveCardToVip:";

    @ApiOperation("新增卡券模板")
    @PostMapping("/insertOrUpdateCardTemplate")
    public void insertCardTemplate(@RequestBody SsCardTemplate ssCardTemplate) {
        cardTemplateService.insertOrUpdateCardTemplate(ssCardTemplate);
    }

    @ApiOperation("获取初始化卡券模板")
    @GetMapping(value = "/getCardTemplateInit/{id}")
    @WebLogs
    public SsCardTemplate getCardTemplateInit(@PathVariable BigInteger id) {
        return cardTemplateService.getCardTemplateInit(id);
    }

    @ApiOperation("删除优惠券模板")
    @PostMapping("/deleteCardTemplate")
    @PermissionCheck(key = PermissionShopSale.MEMBER_CARDTEMPLATE_DELETE)
    public CustomResult deleteCardTemplate(@RequestBody BigInteger id) {
        return cardTemplateService.deleteCardTemplate(id);
    }

    @ApiOperation("获取卡券列表")
    @PostMapping("/getCardTemplateList")
    @NgpResource(name = "shopsale.getCardTemplateList", tagStrings = "'tagA,'+0")
    public PageInfo<SsCardTemplate> getCardTemplateList(@RequestBody SsCardTemplate template) {
        return cardTemplateService.getCardTemplateList(template);
    }

    @ApiOperation("获取卡券列表（通过id）")
    @PostMapping("/getCardTemplateListByIds")
    @WebLogs
    public List<SsCardTemplate> getCardTemplateListByIds(@RequestBody List<BigInteger> ids) {
        return cardTemplateService.getCardTemplateListByIds(ids);
    }


    @ApiOperation("发放权益卡、优惠券列表")
    @PostMapping("/getCardTemplateListGrant")
    @WebLogs
    public PageInfo<SsCardTemplate> getCardTemplateListGrant(@RequestBody SsCardTemplate template) {
        return cardTemplateService.getCardTemplateListGrant(template);
    }

    @ApiOperation("获取卡券模板完整信息")
    @GetMapping(value = "/getCardTemplate/{id}")
    public SsCardTemplate getCardTemplate(@PathVariable BigInteger id) {
        return cardTemplateService.getCardTemplate(id);
    }

    /**
     * 在IE上GET请求会被缓存，导致信息不能刷新，开一个post接口
     */
    @ApiOperation("获取卡券模板完整信息")
    @PostMapping(value = "/getCardTemplate")
    public SsCardTemplate getCardTemplatePost(@RequestBody BigInteger id) {
        return cardTemplateService.getCardTemplate(id);
    }

    @ApiOperation("改变停用启用状态")
    @PostMapping("/stopCardTemplate")
    public CustomResult stopCardTemplate(@RequestBody SsCardTemplate template) {
        return cardTemplateService.stopCardTemplate(template);
    }

    @ApiOperation("获取卡券的已发放数量")
    @PostMapping("/getCreatedCount")
    public Integer getCreatedCount(@RequestBody BigInteger id) {
        return cardTemplateService.getCreatedCount(id);
    }

    /**
     * 如果要给非会员发券，vipIds里传0
     *
     * @param request
     * @return
     */
    @ApiOperation("发放卡券给会员")
    @PostMapping("/giveCardToVip")
    public CustomResult giveCardToVip(@RequestBody GiveCardToVipRequest request) {
        try {
            // 增加分布式锁，防止网络卡顿时重复添加数据
            boolean aTrue = PosRedisLockerUtils.setLock(redisKey + CurrentUser.getProfileId(), "true", posRedisLockTimeConfig.getConfig());
            if (aTrue) {
                // 单独调接口的都是手工发放
                request.setSourceType(CardSourceType.MANUAL_DISTRIBUTION);
                Map<BigInteger, List<BigInteger>> cardIds = cardTemplateService.giveCardToVip(request).getSuccessCard();
                if (cardIds != null && !cardIds.isEmpty()) {
                    List<SsCardAssertBillDto> list = new ArrayList<>();
                    for (BigInteger vipId : cardIds.keySet()) {
                        if (BigInteger.ZERO.equals(vipId)) {
                            // 非会员的不需要记录资产变动表
                          continue;
                        }
                        SsCardAssertBillDto ssCardAssertBillDto = new SsCardAssertBillDto(BigInteger.ZERO, "", "普通发放", AssertsSourceOperation.COMMON_GRANT);
                        List<SsCardAssertBillDetailDto> cardAssertBillDetailDtos = new ArrayList<>();
                        List<BigInteger> bigIntegers = cardIds.get(vipId);
                        if (bigIntegers != null && !bigIntegers.isEmpty()) {
                            for (BigInteger cardId : bigIntegers) {
                                if (cardId != null) {
                                    // 资产变动表-卡券
                                    cardAssertBillDetailDtos.add(SsCardAssertBillDetailDto.createData(
                                            BigDecimal.ONE,
                                            StringUtils.isBlank(request.getComment()) ? "" : request.getComment(),
                                            AssertBillType.card.getValue(),
                                            cardId,
                                            null,
                                            null,
                                            null,
                                            AssertsChangeType.MANUAL_DISTRIBUTION
                                    ));
                                }
                            }
                            if (!cardAssertBillDetailDtos.isEmpty()) {
                                ssCardAssertBillDto.setCardAssertBillDetailDtos(cardAssertBillDetailDtos);
                                ssCardAssertBillDto.setVipId(vipId);
                                ssCardAssertBillDto.setSourceType(request.getOperationSource());
                                list.add(ssCardAssertBillDto);
                            }
                        }
                    }
                    // 保存资产变动表
                    ssCardAssertBillService.saveAssertBillList(list);
                }
            } else {
                throw new RuntimeException("正在发放中，请稍等");
            }
        } catch (Exception e) {
            PosRedisLockerUtils.unLock(redisKey + CurrentUser.getProfileId());
            throw new RuntimeException(e.getMessage());
        }
        PosRedisLockerUtils.unLock(redisKey + CurrentUser.getProfileId());
        return new CustomResult(ResultCode.SUCCESS.getCode(), "发放卡券成功");
    }

    @ApiOperation("获取优惠券发放记录列表")
    @PostMapping("/getCardRecordList")
    @WebLogs
    public PageInfo<CardRecordDTO> getCardRecordList(@RequestBody PageRequest<CardRecordQueryDTO> request) {
        return cardTemplateService.getCardRecordList(request);
    }
}
