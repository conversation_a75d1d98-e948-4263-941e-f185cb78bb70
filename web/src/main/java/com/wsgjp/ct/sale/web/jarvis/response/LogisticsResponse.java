package com.wsgjp.ct.sale.web.jarvis.response;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

import java.io.Serializable;

public class LogisticsResponse<T> extends BaseResponse implements Serializable {
    @JacksonXmlProperty(localName = "result")
    private T result;

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }
}
