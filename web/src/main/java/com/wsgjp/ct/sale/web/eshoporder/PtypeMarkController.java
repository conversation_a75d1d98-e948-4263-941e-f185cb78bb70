package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryEshopProductMappingRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductClassService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductHandleService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductService;
import io.swagger.annotations.Api;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by kangyu on 2020-03-12.
 */
@Api(tags = "商品标记相关")
@RequestMapping("${app.id}/eshoporder/flagtype")
@RestController
public class PtypeMarkController {

    private final EshopProductService productService;
    private final EshopProductHandleService productLocalHandleService;
    private final EshopProductClassService classService;

    public PtypeMarkController(@Lazy EshopProductService productService,EshopProductHandleService productLocalHandleService, EshopProductClassService classService) {
        this.productService = productService;
        this.productLocalHandleService = productLocalHandleService;
        this.classService = classService;
    }

    /**
     * 商品对应gird，获取商品对应列表
     * @param params
     * @return
     */
    @RequestMapping(value = "/queryEshopProductMarks", method = RequestMethod.POST)
    public PageResponse<EshopProductRelationEntity> queryListOrders(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        List<EshopProductRelationEntity> listSku=productLocalHandleService.queryEshopProductSkuMapping(params);
        PageResponse<EshopProductRelationEntity> resp= PageDevice.readPage(listSku);
        int total=listSku.stream().filter(x->x.isRoot()==true).collect(Collectors.toList()).size();
        resp.setTotal(total);
        return resp;
    }

}
