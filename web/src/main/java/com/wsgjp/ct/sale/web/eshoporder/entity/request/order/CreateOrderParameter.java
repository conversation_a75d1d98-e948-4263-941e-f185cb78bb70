package com.wsgjp.ct.sale.web.eshoporder.entity.request.order;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/1 9:42
 */
public class CreateOrderParameter {

	private String tradeId;
	private BigInteger otypeId;
	private BigInteger etypeId;
	private BigInteger btypeId;
	private BigInteger ktypeId;
	private String remark;
	private String logisticsBillNo;
	private String logisticsCode;
	private String logisticsCompany;
	private String sellerMessage;
	private String buyerMessage;
	private String buyerAccount;
	private String receiver;
	private String mobile;
	private String province;
	private String city;
	private String district;
	private String town;
	private String address;
	private List<CreateOrderDetailEntity> detailList;

	public BigInteger getOtypeId() {
		return otypeId;
	}

	public void setOtypeId(BigInteger otypeId) {
		this.otypeId = otypeId;
	}

	public BigInteger getEtypeId() {
		return etypeId;
	}

	public void setEtypeId(BigInteger etypeId) {
		this.etypeId = etypeId;
	}

	public BigInteger getBtypeId() {
		return btypeId;
	}

	public void setBtypeId(BigInteger btypeId) {
		this.btypeId = btypeId;
	}

	public BigInteger getKtypeId() {
		return ktypeId;
	}

	public void setKtypeId(BigInteger ktypeId) {
		this.ktypeId = ktypeId;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getDistrict() {
		return district;
	}

	public void setDistrict(String district) {
		this.district = district;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public List<CreateOrderDetailEntity> getDetailList() {
		return detailList;
	}

	public void setDetailList(List<CreateOrderDetailEntity> detailList) {
		this.detailList = detailList;
	}

	public String getTradeId() {
		if (tradeId == null) {
			return "";
		}
		return tradeId;
	}

	public void setTradeId(String tradeId) {
		this.tradeId = tradeId;
	}

	public String getLogisticsBillNo() {
		return logisticsBillNo;
	}

	public void setLogisticsBillNo(String logisticsBillNo) {
		this.logisticsBillNo = logisticsBillNo;
	}

	public String getLogisticsCompany() {
		return logisticsCompany;
	}

	public void setLogisticsCompany(String logisticsCompany) {
		this.logisticsCompany = logisticsCompany;
	}

	public String getSellerMessage() {
		return sellerMessage;
	}

	public void setSellerMessage(String sellerMessage) {
		this.sellerMessage = sellerMessage;
	}

	public String getBuyerMessage() {
		return buyerMessage;
	}

	public void setBuyerMessage(String buyerMessage) {
		this.buyerMessage = buyerMessage;
	}

	public String getBuyerAccount() {
		return buyerAccount;
	}

	public void setBuyerAccount(String buyerAccount) {
		this.buyerAccount = buyerAccount;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getLogisticsCode() {
		return logisticsCode;
	}

	public void setLogisticsCode(String logisticsCode) {
		this.logisticsCode = logisticsCode;
	}
}
