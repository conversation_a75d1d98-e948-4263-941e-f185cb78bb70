package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.baseinfo.BaseInfoApi;
import com.wsgjp.ct.sale.biz.bifrost.entity.BaseInfoLog;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopSysConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.IntConstants;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.EshopPlatformStoreMappingInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.modifyOrderIndexInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.LogLevelEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.log.SystemLog;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BaseQuery;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopSortRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryEShopParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopBusinessService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopPlatformStoreMappingService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.member.utils.StringUtils;
import com.wsgjp.ct.sale.biz.shopsale.common.FeignResult;
import com.wsgjp.ct.sale.common.config.SystemGlobalConfig;
import com.wsgjp.ct.sale.common.entity.DoudianEntity;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformEshopSupport;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformSupport;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.BaseLogQueryParams;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.log.service.LogService;
import io.swagger.annotations.Api;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.IpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "网店基本信息列表")
@RequestMapping("${app.id}/eshoporder/eshoplist")
@RestController
public class EshopListContorller {
    private static final Logger logger = LoggerFactory.getLogger(EshopListContorller.class);

    private final EshopService service;
    private final EshopPlatformStoreMappingService storeMappingService;
    private final EshopOrderBaseInfoService eshopOrderBaseInfoService;
    private final EshopBusinessService eshopBusinessService;
    private final ServiceConfig serviceConfig;
    private final EshopTmcConfig eshopTmcConfig;
    private final BaseInfoApi baseInfoApi;


    public EshopListContorller(EshopService service, EshopPlatformStoreMappingService storeMappingService, EshopOrderBaseInfoService eshopOrderBaseInfoService, EshopBusinessService eshopBusinessService, ServiceConfig serviceConfig, EshopTmcConfig eshopTmcConfig, BaseInfoApi baseInfoApi) {
        this.service = service;
        this.storeMappingService = storeMappingService;
        this.eshopOrderBaseInfoService = eshopOrderBaseInfoService;
        this.eshopBusinessService = eshopBusinessService;
        this.serviceConfig = serviceConfig;
        this.eshopTmcConfig = eshopTmcConfig;
        this.baseInfoApi = baseInfoApi;
    }

    @PostMapping(value = "/getAllEshopList")
    @PageDataSource
    public PageResponse<EshopInfo> getAllEshopList(@RequestBody PageRequest<BaseQuery> pageRequest) {
        return PageDevice.readPage(service.getAllEshopList());
    }

    @PostMapping(value = "/getOrgAddonInfo")
    public OrganizationAddOnResponse getOrgAddonInfo(@RequestBody PageRequest<QueryEShopParameter> pageRequest) {
        OrganizationAddOnResponse response = new OrganizationAddOnResponse();
        List<Atype> atypes = eshopOrderBaseInfoService.getAtypeList(CurrentUser.getProfileId());
        List<Stock> stocks = eshopOrderBaseInfoService.getStockList();

        atypes = atypes == null ? new ArrayList<>() : atypes;
        stocks = stocks == null ? new ArrayList<>() : stocks;
        response.setAtypes(atypes);
        response.setStocks(stocks);
        return response;
    }

    @PostMapping(value = "/getOrgSupport")
    public OrganizationAddOnResponse getOrgSupport(@RequestBody PageRequest<QueryEShopParameter> pageRequest) {
        OrganizationAddOnResponse response = new OrganizationAddOnResponse();
        List<PlatformSupport> platformSupport = EshopUtils.getSupportPlatformTypes();
        List<PlatformEshopSupport> shopTypeSupport = EshopUtils.getSupportEshopTypes();
        if (CollectionUtils.isNotEmpty(pageRequest.getQueryParams().getNotAllowShopTypes())) {
            shopTypeSupport = shopTypeSupport.stream().filter(x -> pageRequest.getQueryParams().getNotAllowShopTypes().stream().noneMatch(item -> item != null && item == x.getType())).collect(Collectors.toList());
        }

        ArrayList<PlatformSupport> platformSupportNewList = platformSupport.stream()
                .collect(Collectors.collectingAndThen(Collectors
                                .toCollection(() -> new TreeSet<>(Comparator.comparing(PlatformSupport::getPlatformType))),
                        ArrayList::new));
        response.setPlatformSupport(platformSupportNewList);

        ArrayList<PlatformEshopSupport> shopTypeSupportNewList = shopTypeSupport.stream()
                .collect(Collectors.collectingAndThen(Collectors
                                .toCollection(() -> new TreeSet<>(Comparator.comparing(PlatformEshopSupport::getType))),
                        ArrayList::new));
        response.setShopTypeSupport(shopTypeSupportNewList);
        SystemGlobalConfig config = GlobalConfig.get(SystemGlobalConfig.class);
        boolean isRetail = config.isRetailEshopOrderConfig();
        response.setProductId(isRetail ? IntConstants.RETAIL_PRODUCT_ID : IntConstants.WHOLESALE_PRODUCT_ID);
        service.setDeliverDurationList(isRetail, response, pageRequest);

        String oldVersion = GlobalConfig.get("oldVersion");
        boolean isOldVersion = StringUtils.isBlank(oldVersion) || "1".equals(oldVersion.trim());
        response.setOldVersion(isOldVersion);
        String allowShowEshopClass = isOldVersion ? "true" : GlobalConfig.get("sys.func.eshop.group.enabled");
        String allowStateSubsidy = isOldVersion ? "true" : GlobalConfig.get("allowStateSubsidy");
        response.setAllowStateSubsidy((StringUtils.isNotEmpty(allowStateSubsidy) && "true".equals(allowStateSubsidy.trim())));
        response.setAllowShowEshopClass(StringUtils.isNotEmpty(allowShowEshopClass) && "true".equals(allowShowEshopClass.trim()));
        return response;
    }

    @PostMapping(value = "/getOrgList")
    public OrganizationPageResponse getOrgList(@RequestBody PageRequest<QueryEShopParameter> pageRequest) {
        OrganizationPageResponse response = service.getOrgList(pageRequest);
        response.setPageList(PageDevice.readPage(response.getOrgList()));
        return response;
    }

    @PostMapping(value = "/getOtypeList")
    public List<Otype> getOtypeList(@RequestBody EshopPlatformStoreMappingInDTO inDTO) {
        return service.getOtypeListByType(inDTO);
    }

    @PostMapping(value = "/getOtypeListBack")
    public List<Otype> getOtypeListBack(@RequestBody EshopPlatformStoreMappingInDTO inDTO) {
        return service.getOtypeListByTypeBack(inDTO);
    }

    @PostMapping(value = "/getnotSupportCreateShoptype")
    public List<Integer> getnotSupportShoptype() {
        return service.getnotSupportShoptype();
    }


    @GetMapping(value = "/getPlatformsAndEshopTypes")
    public OrganizationPageResponse getPlatformsAndEshopTypes() {
        OrganizationPageResponse response = new OrganizationPageResponse();
        List<PlatformSupport> platformSupport = EshopUtils.getSupportPlatformTypes();
        List<PlatformEshopSupport> shopTypeSupport = EshopUtils.getSupportEshopTypes();
        ArrayList<PlatformSupport> platformSupportNewList = platformSupport.stream()
                .collect(Collectors.collectingAndThen(Collectors
                                .toCollection(() -> new TreeSet<>(Comparator.comparing(PlatformSupport::getPlatformType))),
                        ArrayList::new));
        response.setPlatformSupport(platformSupportNewList);
        ArrayList<PlatformEshopSupport> shopTypeSupportNewList = shopTypeSupport.stream()
                .collect(Collectors.collectingAndThen(Collectors
                                .toCollection(() -> new TreeSet<>(Comparator.comparing(PlatformEshopSupport::getType))),
                        ArrayList::new));
        response.setShopTypeSupport(shopTypeSupportNewList);
        return response;
    }

    //手动下单选择销售平台
    @GetMapping("/getEnumState/{key}")
    public List<EnumState> getEnumState(@PathVariable String key) {
        List<EnumState> enumStates = new ArrayList<>();
        List<PlatformSupport> platformSupports = EshopUtils.getSupportPlatformTypes();
        for (PlatformSupport platformSupport : platformSupports) {
            if ("其他".equals(platformSupport.getName())) {
                continue;
            }
            EnumState enumState = new EnumState();
            enumState.setCode(platformSupport.getPlatformType());
            enumState.setName(platformSupport.getName());
            enumStates.add(enumState);
        }
        EnumState enumAll = new EnumState();
        enumAll.setCode(-1);
        enumAll.setName("所有");
        enumStates.add(enumAll);
        return enumStates;
    }

    //枚举值获取 不包含所有
    @GetMapping("/getMulEnumState/{key}")
    public List<EnumState> getMulEnumState(@PathVariable String key) {
        List<EnumState> enumStates = new ArrayList<>();
        List<PlatformEshopSupport> shopTypes = EshopUtils.getSupportEshopTypes();
        for (PlatformEshopSupport shopType : shopTypes) {
            if ("其他".equals(shopType.getName())) {
                continue;
            }
            EnumState enumState = new EnumState();
            enumState.setCode(shopType.getType());
            enumState.setName(shopType.getName());
            if (enumStates.stream().anyMatch(u -> u.getCode() == enumState.getCode())) {
                continue;
            }
            enumStates.add(enumState);
        }
        return enumStates;
    }

    @PostMapping(value = "/stopEshop")
    public BaseResponse stopEshop(@RequestBody EshopInfo eshopInfo) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            BigInteger profileId = CurrentUser.getProfileId();
            eshopInfo.setProfileId(profileId);
            service.stopEShop(eshopInfo);
            baseResponse.setSuccess(true);
        } catch (Exception ex) {
            baseResponse.setSuccess(false);
            baseResponse.setMessage(ex.getMessage());
        }
        return baseResponse;
    }

    @PostMapping(value = "/openEshopAg")
    public BaseResponse openEshopAg(@RequestBody EshopInfo eshopInfo) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            BigInteger profileId = CurrentUser.getProfileId();
            Boolean result = service.openEshopAg(profileId, eshopInfo.getOtypeId());
            baseResponse.setSuccess(result);
            baseResponse.setMessage(result ? "开启成功" : "未开启AG");
        } catch (Exception ex) {
            ex.printStackTrace();
            baseResponse.setSuccess(false);
            baseResponse.setMessage(ex.getMessage());
        }
        return baseResponse;
    }

    @PostMapping(value = "/deleteEshop")
    public BaseResponse deleteEshop(@RequestBody EshopInfo eshopInfo) {
        BaseResponse baseResponse = new BaseResponse();
        try {
            BigInteger profileId = CurrentUser.getProfileId();
            //检查是否是连锁店铺 如果是连锁店铺 删除总调需要同事删除所有子店
            service.checkAndDeleteEShop(profileId, eshopInfo.getOtypeId());
            eshopBusinessService.deleteEShop(profileId, eshopInfo.getOtypeId());
            baseResponse.setSuccess(true);
        } catch (Exception ex) {
            baseResponse.setSuccess(false);
            baseResponse.setMessage(ex.getMessage());
        }
        return baseResponse;
    }

    @PostMapping(value = "/deleteEshopsNew")
    public BaseResponse deleteEshopsNew(@RequestBody List<EshopInfo> eshopInfos) {
        BaseResponse baseResponse = new BaseResponse();
        List<EshopInfo> mainEshops = new ArrayList<>();
        List<EshopInfo> errorEshops = new ArrayList<>();
        BigInteger profileId = CurrentUser.getProfileId();
        for (EshopInfo eshopInfo : eshopInfos) {
            if (eshopInfo.isMainEshop() && StringUtils.isNotEmpty(eshopInfo.getGroupId())) {
                mainEshops.add(eshopInfo);
                continue;
            }
            try {
                //检查是否是连锁店铺 如果是连锁店铺 删除总调需要同事删除所有子店
                service.checkAndDeleteEShop(profileId, eshopInfo.getId());
                FeignResult eshopdelete = baseInfoApi.eshopdelete(Arrays.asList(eshopInfo.getId()));
                if (eshopdelete.getCode().equals("200")) {
                    eshopBusinessService.deleteEShop(profileId, eshopInfo.getId());
                } else {
                    errorEshops.add(eshopInfo);
                    baseResponse.setMessage(eshopdelete.getMessage());
                    continue;
                }
            } catch (Exception ex) {
                baseResponse.setSuccess(false);
                baseResponse.setMessage(ex.getMessage());
            }
        }
        if (CollectionUtils.isNotEmpty(errorEshops)) {
            for (EshopInfo mainEshop : mainEshops) {
                List<EshopInfo> errorInfos = errorEshops.stream().filter(u -> u.getGroupId().equals(mainEshop.getGroupId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(errorInfos)) {
                    FeignResult eshopdelete = baseInfoApi.eshopdelete(Arrays.asList(mainEshop.getId()));
                    if (eshopdelete.getCode().equals("200")) {
                        eshopBusinessService.deleteEShop(profileId, mainEshop.getId());
                    }
                }
            }
        } else {
            for (EshopInfo mainEshop : mainEshops) {
                FeignResult eshopdelete = baseInfoApi.eshopdelete(Arrays.asList(mainEshop.getId()));
                if (eshopdelete.getCode().equals("200")) {
                    eshopBusinessService.deleteEShop(profileId, mainEshop.getId());
                }
            }
        }
        return baseResponse;
    }

    @PostMapping(value = "/deleteEshops")
    public BaseResponse deleteEshops(@RequestBody List<BigInteger> otypeList) {
        BaseResponse baseResponse = new BaseResponse();
        EshopParameter params = new EshopParameter();
        try {
            if (otypeList == null || otypeList.size() == 0) {
                throw new RuntimeException("请选择要删除的网店！");
            }
            BigInteger profileId = CurrentUser.getProfileId();
            params.setProfileId(profileId);
            params.setOtypeIds(otypeList);
            for (BigInteger otypeI : otypeList) {
                //检查是否是连锁店铺 如果是连锁店铺 删除总调需要同事删除所有子店
                service.checkAndDeleteEShop(profileId, otypeI);
            }
            baseResponse = service.deleteEShops(params);
            service.cancelAuthDoNotify(params);
        } catch (Exception ex) {
            ex.printStackTrace();
            baseResponse.setSuccess(false);
            baseResponse.setMessage(ex.getMessage());
        }
        return baseResponse;
    }

    @GetMapping(value = "/cancelAuth/{otypeId}")
    public BaseResponse cancelAuth(@PathVariable BigInteger otypeId) {
        BigInteger profileId = CurrentUser.getProfileId();
        BaseResponse result = service.cancelAuth(profileId, otypeId);
        if (result.isSuccess()) {
            List<BigInteger> otypeList = new ArrayList<>();
            otypeList.add(otypeId);
            recordCancelAuthLogs(otypeList);
        }
        return result;
    }

    @PostMapping(value = "/cancelEShopAuth")
    public BaseResponse cancelEShopAuth(@RequestBody List<BigInteger> otypeList) {
        BaseResponse baseResponse = new BaseResponse();
        EshopParameter params = new EshopParameter();
        try {
            if (otypeList == null || otypeList.size() == 0) {
                throw new RuntimeException("请选择要取消授权的网店！");
            }
            BigInteger profileId = CurrentUser.getProfileId();
            params.setProfileId(profileId);
            params.setOtypeIds(otypeList);
            for (BigInteger otypeId : otypeList) {
                service.cancelAuthDoNotify(profileId, otypeId);
            }
            baseResponse = service.cancelEShopAuth(params);
            recordCancelAuthLogs(otypeList);
            checkHasEshopGroupNeedCancelAuth(params);
        } catch (Exception ex) {
            baseResponse.setSuccess(false);
            baseResponse.setMessage(ex.getMessage());
        }
        return baseResponse;
    }

    private void checkHasEshopGroupNeedCancelAuth(EshopParameter params) {
        try {
            List<BigInteger> otypeList = service.maeshopBranchInfoByGroupId(params);
            if (CollectionUtils.isEmpty(otypeList)) {
                return;
            }
            params.setProfileId(CurrentUser.getProfileId());
            params.setOtypeIds(otypeList);
            service.cancelEShopAuth(params);
            for (BigInteger otypeId : otypeList) {
                service.cancelAuthDoNotify(CurrentUser.getProfileId(), otypeId);
            }
            recordCancelAuthLogs(otypeList);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    //取消授权日志
    private void recordCancelAuthLogs(List<BigInteger> otypeList) {
        if (otypeList != null && otypeList.size() > 0) {
            otypeList.forEach(o -> {
                BaseInfoLog log = new BaseInfoLog();
                log.setBody("取消授权成功");
                log.setObjectId(o);
                log.setObjectType("otype");
                saveChangeLog(log);
            });
        }
    }

    @PostMapping(value = "/queryEShopPageInfo")
    public EshopPageCategoryClassResponse queryEShopPageInfo(@RequestBody QueryEShopParameter parameter) {
        EshopPageCategoryClassResponse response = service.queryEShopPageResponse(parameter);
        if (response != null) {
            List<EshopPlatformStoreMapping> storeMappings = storeMappingService.getPlatformStoreMappingByEshopId(parameter.getProfileId(), parameter.getOtypeId());
            storeMappings = storeMappings == null ? new ArrayList<>() : storeMappings;
            response.setStoreMappings(storeMappings);
        }
        return response;
    }


    @PostMapping(value = "/getAutoDownloadConfig")
    public OrganizationPageResponse getAutoDownloadConfig(@RequestBody PageRequest<QueryEShopParameter> pageParameter) {
        QueryEShopParameter parameter = pageParameter.getQueryParams();
        CommonUtil.initLimited(parameter);
        PageDevice.initPage(pageParameter);
        service.checkEshopTypeQueryParameter(parameter);
        OrganizationPageResponse response = service.getOrgListByDownload(pageParameter);
        List<Otype> orglist = service.getAutoDownConfig(response, parameter.getProfileId());
        response.setOrgList(orglist);
        response.setTotal(response.getTotal());
        return response;
    }

    @PostMapping(value = "/checkAuthTypeByOtypeId")
    public boolean checkAuthTypeByOtypeId(@RequestBody QueryEShopParameter parameter) {
        boolean isauth = service.checkAuthType(CurrentUser.getProfileId(), parameter.getShopType(), parameter.getEshopId());
        EshopConfig eshopConfigById = service.getEshopConfigById(CurrentUser.getProfileId(), parameter.getEshopId());
        return isauth && eshopConfigById.isRealStockQtyEnabled();
    }

    @PostMapping(value = "/getRealStockEnable")
    public boolean getRealStockEnable() {
        return service.getRealStockEnable(CurrentUser.getProfileId());
    }

    @PostMapping(value = "/addEshopAutoOrder")
    public BaseResponse addEshopAutoOrder(@RequestBody QueryEShopParameter parameter) {
        BigInteger profileId = parameter.getProfileId();
        BigInteger shopId = parameter.getEshopId();
        Date beginTime = parameter.getBeginTime();
        String fullname = parameter.getFullname();
        boolean result;
        try {
            result = service.addEshopAutoOrder(profileId, shopId, beginTime);
            if (result && parameter.getShopType() != null) {
                /*当开启自动下单成功时，开启tmc*/
                EShopPageInfo pageInfo = new EShopPageInfo();
                pageInfo.setOtypeId(parameter.getEshopId());
                pageInfo.setEshopType(parameter.getShopType());
                pageInfo.setTmcEnabled(true);
                BaseResponse tmcRep = service.registerTmc(pageInfo);
                CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR,String.format("profileId:%s,eshopId%s,tmcReq:%s",CurrentUser.getProfileId(),shopId,tmcRep.getMessage()));
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage());
            result = false;
        }
        BaseResponse response = new BaseResponse();
        if (result) {
            SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String logMsg = String.format("【%s】开启自动下单,首次下载时间【%s】", fullname, fmt.format(beginTime));
            SystemLog infoLog = buildEshopConfigLog(logMsg);
            LogService.add(infoLog);
            response.setMessage("SUCCESS");
            response.setSuccess(true);
        } else {
            response.setMessage("添加自动下单失败");
            response.setSuccess(false);
        }
        return response;
    }

    private SystemLog buildEshopConfigLog(String logMsg) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        SystemLog baseInfoLog = new SystemLog();
        baseInfoLog.setProfileId(profileId);
        baseInfoLog.setEtypeId(employeeId);
        baseInfoLog.setId(UId.newId());
        baseInfoLog.setBody(logMsg);
        Etype etype = eshopOrderBaseInfoService.getEtypeById(profileId, employeeId);
        baseInfoLog.setEfullname(etype.getFullname());
        baseInfoLog.setLogTime(DateUtils.getDate());
        baseInfoLog.setIp(IpUtils.getLocalHostIp());
        return baseInfoLog;
    }

    @PostMapping(value = "/updateEshopAutoOrder")
    public BaseResponse updateEshopAutoOrder(@RequestBody QueryEShopParameter parameter) {
        BigInteger profileId = parameter.getProfileId();
        BigInteger shopId = parameter.getEshopId();
        boolean result;
        try {
            result = service.updateEshopAutoOrder(profileId, shopId);
        } catch (Exception ex) {
            logger.error(ex.getMessage());
            result = false;
        }

        BaseResponse response = new BaseResponse();
        if (result) {
            SystemLog infoLog = buildEshopConfigLog("关闭自动下单");
            LogService.add(infoLog);
            response.setMessage("SUCCESS");
            response.setSuccess(true);
        } else {
            response.setMessage("关闭自动下单失败");
            response.setSuccess(false);
        }
        return response;

    }

    @PostMapping(value = "/saveChangeLog")
    public void saveChangeLog(@RequestBody BaseInfoLog baseInfoLog) {
        baseInfoLog.setProfileId(CurrentUser.getProfileId());
        baseInfoLog.setEtypeId(CurrentUser.getEmployeeId());
        baseInfoLog.setId(UId.newId());
        Etype etype = eshopOrderBaseInfoService.getEtypeById(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
        baseInfoLog.setEfullname(etype.getFullname());
        baseInfoLog.setLogTime(DateUtils.getDate());
        baseInfoLog.setIp(IpUtils.getLocalHostIp());
        LogService.add(baseInfoLog);
    }

    @PostMapping(value = "/queryOtypeLog")
    public PageResponse<BaseInfoLog> queryOtypeLog(@RequestBody PageRequest<BaseLogQueryParams> request) {
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        PageResponse<BaseInfoLog> query = LogService.query(request);
        if (query.getList() == null) {
            query.setList(new ArrayList<>());
        }
        return query;
    }


    @RequestMapping(value = "/updatePlEshopConfig")
    public boolean updatePlEshopConfig(@RequestBody Otype otype) {
        otype.setProfileId(CurrentUser.getProfileId());
        return service.updatePlEshopConfig(otype);
    }

    @PostMapping(value = "/modifyOrderIndex")
    public void modifyOrderIndex(@RequestBody modifyOrderIndexInDTO inDTO) {
        service.modifyOrderIndex(inDTO);
    }

    /**
     * 网店置顶
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/moveTop/{id}")
    public boolean moveTop(@PathVariable BigInteger id) {
        return service.moveTop(id);
    }

    /**
     *
     */
    @GetMapping(value = "/getEshopInfo/{id}")
    public DoudianEntity getEshopInfo(@PathVariable BigInteger id) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setEshopId(id);
        EshopInfo eshopInfoById = service.getEshopInfoById(parameter);
        DoudianEntity entity = new DoudianEntity();
        entity.setAppId(eshopTmcConfig.getDoudianAppKey());
        entity.setEshopType(eshopInfoById.getEshopType());
        entity.setShopId(eshopInfoById.getOnlineEshopId());
        entity.setShopName(eshopInfoById.getEshopAccount());
        entity.setAppName("管家婆网店ERP");
        return entity;
    }

    @GetMapping(value = "/moveBottom/{id}")
    public boolean moveBottom(@PathVariable BigInteger id) {
        return service.moveBottom(id);
    }

    /**
     * 网店上移
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/moveUp/{id}")
    public boolean moveUp(@PathVariable String id) {
        return service.moveUp(id);
    }

    /**
     * 网店下移
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/moveDown/{id}")
    public boolean moveDown(@PathVariable String id) {
        return service.moveDown(id);
    }

    @PostMapping(value = "/eshopMove")
    public boolean eshopMove(@RequestBody EshopSortRequest request) {
        return service.eshopMove(request);
    }

    @GetMapping(value = "/getAllowEshopProductUpAndDown")
    public List<String> getAllowEshopProductUpAndDown() {
        String allowEshopProductUpDown = serviceConfig.getAllowEshopProductUpDown();
        String[] split = allowEshopProductUpDown.split(",");
        return Arrays.asList(split);
    }

    @GetMapping(value = "/getSupportCreateInitStockShopType")
    public List<String> getSupportCreateInitStockShopType() {
        String allowEshopProductUpDown = serviceConfig.getSupportCreateInitStockShopType();
        String[] split = allowEshopProductUpDown.split(",");
        return Arrays.asList(split);
    }

    @GetMapping(value = "/getallowEshopProductPublish")
    public List<String> getallowEshopProductPublish() {
        String allowEshopProductUpDown = serviceConfig.getAllowEshopProductPublish();
        String[] split = allowEshopProductUpDown.split(",");
        return Arrays.asList(split);
    }

    @GetMapping(value = "/checkAutoShelfOnIsOpen")
    public boolean checkAutoShelfOnIsOpen(@RequestParam BigInteger eshopId) {
        Otype otype = service.getOtypeById(eshopId);
        return otype.isAutoShelfOn();
    }

    @GetMapping(value = "/hasSupportOpenPtypeUrl/{shoptype}")
    public boolean cancelAuth(@PathVariable ShopType shoptype) {
        return service.hasSupportOpenPtypeUrl(shoptype);
    }
}
