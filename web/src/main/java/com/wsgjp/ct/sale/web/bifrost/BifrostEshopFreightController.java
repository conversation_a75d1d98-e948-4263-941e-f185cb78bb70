package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopFreightService;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.platform.entity.response.freight.QueryFreightMappingParameter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: lj
 * @create: 2022-03-10
 **/
@Api(tags = "网店物流相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/freight")
public class BifrostEshopFreightController {

    private final BifrostEshopFreightService eshopFreightService;

    public BifrostEshopFreightController(BifrostEshopFreightService eshopFreightService) {
        this.eshopFreightService = eshopFreightService;
    }

    /**
     * 获取物流映射关系(获取管家婆和各平台的物流公司、物流编码映射关系)
     *
     * @param request
     * @return
     */
    @ApiOperation("获取平台和管家婆本地的物流映射")
    @PostMapping("getFreightMapping")
    public FreightMapping getFreightMapping(@RequestBody QueryFreightMappingParameter request) {
        return eshopFreightService.getFreightMapping(request);
    }
}
