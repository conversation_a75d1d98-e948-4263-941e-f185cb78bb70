package com.wsgjp.ct.sale.web.shopsale;

import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.PosSysData;
import com.wsgjp.ct.sale.biz.shopsale.common.ResultHandler;
import com.wsgjp.ct.sale.biz.shopsale.model.menu.MenuPermissionEntity;
import com.wsgjp.ct.sale.biz.shopsale.service.SystemConfigService;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(value = "${app.id}/shopsale/systemConfig", tags = {"系统配置"})
@RestController
@RequestMapping("${app.id}/shopsale/systemConfig")
public class SystemConfigController {

    private SystemConfigService systemConfigService;

    public SystemConfigController(SystemConfigService systemConfigService) {
        this.systemConfigService = systemConfigService;
    }

    @ApiOperation("获取权限")
    @GetMapping("/getPermission")
    @WebLogs
    public MenuPermissionEntity getPermission() {
        return ResultHandler.result(systemConfigService.getAll());
    }


    @ApiOperation("获取模块开关和版本信息")
    @GetMapping("/getModelSwitchAndVersion")
    @WebLogs
    public Map getModelSwitchAndVersion() {
        try {
            return systemConfigService.getModelSwitchAndVersion();
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation("获取系统配置")
    @GetMapping("/getConfig")
    @WebLogs
    public Map<String, Object> getConfig() {
        return ResultHandler.result(systemConfigService.getSystemConfig());
    }

    @ApiOperation("增加系统配置")
    @PostMapping("/insertConfig")
    @WebLogs
    public void insertConfig(@RequestBody List<PosSysData> dataList) {
        systemConfigService.insertConfig(dataList);
    }

    @ApiOperation("获取某个系统配置")
    @PostMapping("/getConfigByKey")
    @WebLogs
    public String getConfigByKey(@RequestBody PosSysData data) {
        return systemConfigService.getConfigByKey(data);
    }

    @ApiOperation("获取产品id")
    @GetMapping("/getProductId")
    public Integer getProductId() {
        return systemConfigService.getProductId();
    }

    @ApiOperation(value = "新增系统日志")
    @PostMapping(value = "/addSystemLog")
    public void addSystemLog(@RequestBody String message) {
        PubSystemLogService.saveInfo(message);
    }

    @ApiOperation(value = "获取自动登录url")
    @GetMapping(value = "/registerLoginUsing")
    @WebLogs
    public Map<String, Object> registerLoginUsing() {
        return ResultHandler.result(systemConfigService.registerLoginUsing());
    }
    @ApiOperation(value = "新增pos异常日志")
    @PostMapping(value = "/addPosErrorLog")
    public void addPosErrorLog(@RequestBody String message) {
        systemConfigService.addPosErrorLog(message);
    }

}
