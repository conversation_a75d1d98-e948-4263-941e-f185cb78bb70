package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryFreightInfoMainGridSourceRequest;
import com.wsgjp.ct.sale.biz.jarvis.common.HashMapCreater;
import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.dto.freight.BuiltinFreightCode;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.DeliverBillOperateResult;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverFreightInfoBillDeliver;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Freight;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.biz.jarvis.service.freight.FreightInfoService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-26 14:00
 */
@RestController
@Api(description = "物流信息")
@RequestMapping("/${app.id}/jarvis/freightInfo")
public class FreightInfoController {
    private FreightInfoService freightInfoService;

    private DeliverService deliverService;
    private BaseInfoService baseInfoService;

    public FreightInfoController(FreightInfoService freightInfoService,DeliverService deliverService,BaseInfoService baseInfoService) {
        this.freightInfoService = freightInfoService;
        this.deliverService=deliverService;
        this.baseInfoService=baseInfoService;
    }

    @ApiOperation(value = "物流查询条件", notes = "物流查询条件")
    @PostMapping("/getQueryFilterType")
    public List<DillDeliverState> getQueryFilterType() throws Exception {
        return freightInfoService.listQueryFilterType();
    }
    @ApiOperation(value = "物流内置编号", notes = "物流内置编号")
    @PostMapping("/listBuiltinFreightCodeList")
    public List<BuiltinFreightCode> listBuiltinFreightCodeList() throws Exception {
        List<BuiltinFreightCode> list  = freightInfoService.listBuiltinFreightCodeList();
        return list;

    }
    @ApiOperation(value = "获取改物流信息页面状态值", notes = "（）")
    @PostMapping("getPageStatus")
    public HashMap getPageStatus() throws Exception{
        HashMap result = HashMapCreater.create(45);
        result.put("freights",baseInfoService.getFregihtList(CurrentUser.getProfileId()));
        result.put("limitFreights",baseInfoService.getLimitFreightList(CurrentUser.getProfileId(),CurrentUser.getEmployeeId()));
        List<Freight> allFreights=  baseInfoService.getAllFregihtList(CurrentUser.getProfileId());
        if(CollectionUtils.isNotEmpty(allFreights)){
            allFreights.forEach(p -> {
                if (p.isStoped()) {
                    p.setFullName(String.format("(停用)%s", p.getFullName()));
                }
            });
        }
        result.put("allFreights",allFreights);
        List<Freight> limitAllFreights= baseInfoService.getLimitAllFreightList(CurrentUser.getProfileId(),CurrentUser.getEmployeeId());
        if(CollectionUtils.isNotEmpty(limitAllFreights)){
            limitAllFreights.forEach(p -> {
                if (p.isStoped()) {
                    p.setFullName(String.format("(停用)%s", p.getFullName()));
                }
            });
        }
        result.put("limitAllFreights",limitAllFreights);
        return result;
    }
    @PostMapping("queryFreightInfoMainGridSource")
    public List<DeliverFreightInfoBillDeliver> queryFreightInfoMainGridSource(@RequestBody QueryFreightInfoMainGridSourceRequest request){
        return deliverService.queryFreightInfoMainGridSource(request);
    }
    @PostMapping("checkFreightBillNoRepeat")
    public List<DeliverBillOperateResult> checkFreightBillNoRepeat(@RequestBody List<DeliverFreightInfoBillDeliver> freightInfos){
        BigInteger profileId=CurrentUser.getProfileId();
        BigInteger employeeId=CurrentUser.getEmployeeId();
        return freightInfoService.checkFreightBillNoRepeat(profileId,employeeId,freightInfos);
    }

}
