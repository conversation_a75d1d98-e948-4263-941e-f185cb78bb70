package com.wsgjp.ct.sale.web.externalfile;

import com.wsgjp.ct.sale.biz.analysiscloud.api.request.SaveInvoiceRequest;
import com.wsgjp.ct.sale.biz.api.response.GeneralResponse;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.AddOrEditVipDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.QrVipDto;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.SelectLevelResponseDTO;
import com.wsgjp.ct.sale.biz.member.model.entity.qrCode.QrVipTokenResponse;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.PosSysData;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.SsVip;
import com.wsgjp.ct.sale.biz.member.service.ISsVipLevelService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipQrCodeService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipService;
import com.wsgjp.ct.sale.biz.shopsale.service.BillService;
import com.wsgjp.ct.sale.biz.shopsale.service.SystemConfigService;
import com.wsgjp.ct.sale.biz.wx.dto.WxOldCardActiveDto;
import com.wsgjp.ct.sale.biz.wx.service.ISsWxVipCardTemplateService;
import com.wsgjp.ct.sale.biz.wx.service.NgpWxOpenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.HashMap;

/**
 * @program: sale
 * @author: tanglan
 * @create: 2024/5/27
 * @description: 对外提供的无需验证jwt的目录
 **/
@Api(value = "${app.id}/externalfile", tags = {"对外接口"})
@RestController
@RequestMapping("${app.id}/externalfile")
public class ExternalController {
    @Autowired
    BillService billService;

    @Autowired
    private ISsVipService ssVipService;

    @Autowired
    private ISsVipLevelService ssVipLevelService;

    @Autowired
    private ISsWxVipCardTemplateService ssWxVipCardTemplateService;

    @Autowired
    private NgpWxOpenService wxOpenService;

    @Autowired
    private ISsVipQrCodeService vipQrCodeService;

    @Autowired
    private SystemConfigService systemConfigService;

    /* 开票 -- 开始 */
    @ApiOperation(value = "获取发票源信息")
    @PostMapping("/getInvoiceInfo")
    public HashMap getInvoiceInfo(@RequestBody BigInteger vchcode) {
        return billService.getInvoiceInfo(vchcode);
    }

    @ApiOperation(value = "提交发票")
    @PostMapping("/submitInvoice")
    public GeneralResponse submitInvoice(@RequestBody SaveInvoiceRequest dto) {
        return billService.submitInvoice(dto);
    }
    /* 开票 -- 结束 */

    /* 二维码注册会员 -- 开始 */
    @ApiOperation(value = "获取注册会员链接")
    @PostMapping("/getRegisterVipUrl")
    public String getRegisterVipUrl(@RequestBody QrVipDto dto) {
        return vipQrCodeService.getRegisterVipUrl(dto);
    }

    @ApiOperation(value = "解析注册会员链接里的token")
    @PostMapping("/decodeRegisterVipToken")
    public QrVipTokenResponse decodeRegisterVipToken(@RequestBody String token) {
        return vipQrCodeService.decodeRegisterVipToken(token);
    }

    @ApiOperation(value = "获取默认等级（免费等级第一个）")
    @PostMapping(value = "/getDefaultVipLevel")
    SelectLevelResponseDTO getDefaultVipLevel() {
        return ssVipLevelService.getDefaultVipLevel();
    }

    @ApiOperation(value = "新增或编辑一个会员")
    @PostMapping("/addOrEditVip")
    public BigInteger addOrEditVip(@RequestBody AddOrEditVipDTO dto) throws Exception {
        return ssVipService.addOrEditVip(dto);
    }

    @ApiOperation(value = "校验手机号是否重复")
    @PostMapping("/checkPhone")
    public void checkPhone(@RequestBody SsVip vip) {
        ssVipService.checkPhone(vip.getPhone(), null, vip.getApplyStoreType(), vip.getOtypeIds());
    }

    @ApiOperation("获取某个系统配置")
    @PostMapping("/getConfigByKey")
    @WebLogs
    public String getConfigByKey(@RequestBody PosSysData data) {
        return systemConfigService.getConfigByKey(data);
    }
    /* 二维码注册会员 -- 结束 */


    /* 微信老会员激活 -- 开始 */
    @ApiOperation(value = "微信老会员绑定-校验手机号是否可用")
    @PostMapping("/changePhone")
    public BigInteger changePhone(@RequestBody String phone) {
        return ssWxVipCardTemplateService.changePhone(phone);
    }

    @ApiOperation(value = "老会员同步")
    @PostMapping(value = "/oldCardSync")
    public void oldCardSync(@RequestBody WxOldCardActiveDto dto) throws Exception {
        wxOpenService.getNgpWxMapService().oldCardSync(dto);
    }
    /* 微信老会员激活 -- 结束 */
}
