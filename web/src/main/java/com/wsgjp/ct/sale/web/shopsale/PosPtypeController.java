package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.PropValueName;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.PtypeLabel;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.*;
import com.wsgjp.ct.sale.biz.shopsale.service.IPosPtypeService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Api(value = "${app.id}/shopsale/posptype", tags = "pos离线数据")
@RestController
@RequestMapping("${app.id}/shopsale/posptype")
public class PosPtypeController {

    final IPosPtypeService ptypeService;

    public PosPtypeController(IPosPtypeService ptypeService) {
        this.ptypeService = ptypeService;
    }

    @ApiOperation("获取商品信息")
    @PostMapping("/getPtypeList")
    PageInfo<Ptype> getPtypeList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getPtypeList(request);
    }

    /**
     * @param request 包含 type 0=商品价格 1=门店价格本
     */
    @ApiOperation("获取商品价格信息")
    @PostMapping("/getPriceList")
    PageInfo<Price> getPriceList(@RequestBody PageRequest<Map<String, Object>> request) {
        return ptypeService.getPriceList(request);
    }

    @ApiOperation("获取商品权限信息")
    @PostMapping("/getPtypeLimitList")
    PageInfo<PtypeLimit> getPtypeLimitList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getPtypeLimitList(request);
    }

    @ApiOperation("获取商品库存信息")
    @PostMapping("/getStockList")
    PageInfo<Stock> getStockList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getStockList(request);
    }

    @ApiOperation("获取sku信息")
    @PostMapping("/getSkuList")
    PageInfo<Sku> getSkuList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getSkuList(request);
    }

    @ApiOperation("获取unit信息")
    @PostMapping("/getUnitList")
    PageInfo<Unit> getUnitList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getUnitList(request);
    }

    @ApiOperation("获取商家编码信息")
    @PostMapping("/getPtypeXcodeList")
    PageInfo<PtypeXcode> getPtypeXcodeList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getXcodeList(request);
    }

    @ApiOperation("获取套餐详情信息")
    @PostMapping("/getComboDetailList")
    PageInfo<ComboDetail> getComboDetailList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getComboDetailList(request);
    }

    @ApiOperation("获取商品条码信息")
    @PostMapping("/getFullBarcodeList")
    PageInfo<FullBarcode> getFullBarcodeList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getFullBarcodeList(request);
    }

    @ApiOperation("根据商品id获取商品全部信息,用于新增商品后同步")
    @PostMapping("/getPtypeAllInfoByIds")
    PtypeAllInfoDTO getPtypeAllInfoByIds(@RequestBody List<BigInteger> ptypeIds) {
        return ptypeService.getPtypeAllInfoByIds(ptypeIds);
    }

    @PostMapping("/getPtypeLabel")
    public List<PtypeLabel> getPtypeLabel() {
        return ptypeService.getPtypeLabels(CurrentUser.getProfileId());
    }

    @ApiOperation("获取商品标签数据信息")
    @PostMapping("/getDataLabelPtypeList")
    PageInfo<DataLabelPtype> getDataLabelPtypeList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getDataLabelPtypeList(request);
    }

    @ApiOperation("获取整个账套的属性值信息")
    @PostMapping("/getPropValueList")
    PageInfo<PropValueName> getPropValueList(@RequestBody PageRequest<Object> request) {
        return ptypeService.getPropValueList(request);
    }
}
