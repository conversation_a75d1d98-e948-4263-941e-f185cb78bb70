package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.eshoporder.entity.rebuild.RebuildMessage;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.RebuildEshoporderRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.RebuildEshoporderResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.rebuild.RebuildEshopOrderService;
import io.swagger.annotations.Api;
import ngp.loadbalancer.context.RouteContext;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.starter.web.base.GeneralException;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "系统重建相关")
@RequestMapping("${app.id}/eshoporder/rebuild")
@RestController
public class RebuildEshopOrderController {

    private final RebuildEshopOrderService rebuildService;
    private static final Logger logger = LoggerFactory.getLogger(RebuildEshopOrderController.class);


    public RebuildEshopOrderController(RebuildEshopOrderService rebuildService) {
        this.rebuildService = rebuildService;
    }

    @PostMapping("/rebuildeshoporder")
    @ResponseBody
    public RebuildEshoporderResponse rebuildEshopOrder(HttpServletRequest servletRequest) {
        RebuildEshoporderResponse response = new RebuildEshoporderResponse();
        List<RebuildMessage> messages = new ArrayList<>();
        response.setAppId("eshoporder");
        response.setAsynced(true);
        try {
            RouteContext context = RouteThreadLocal.getRoute();
            if (context == null) {
                throw new GeneralException("获取路由信息失败,请重新登录");
            }
            BigInteger profileId = context.getProfileId();
            String json = readBodyParam(servletRequest);
            RebuildEshoporderRequest request = JsonUtils.toObject(json, RebuildEshoporderRequest.class);
            logger.info(String.format("收到系统重建请求,账套id:%s,request:%s", profileId.toString(), json));
            rebuildService.rebuildEshopOrder(request, context);
            response.setSuccessed(true);
            RebuildMessage message = new RebuildMessage();
            message.setMessage("网店收到系统重建请求,处理完成.");
            message.setStatus("INFO");
            messages.add(message);
        } catch (Exception ex) {
            logger.error("eshoporder重建系统失败", ex);
            response.setSuccessed(false);
            RebuildMessage message = new RebuildMessage();
            message.setMessage(ex.getMessage());
            message.setStatus("ERROR");
            messages.add(message);
        }
        response.setMessages(messages);
        return response;
    }


    public String readBodyParam(HttpServletRequest servletRequest) throws Exception {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(servletRequest.getInputStream()))) {
            String str = "";
            StringBuilder wholeStr = new StringBuilder();
            while ((str = reader.readLine()) != null) {
                wholeStr.append(str.trim());
            }
            return wholeStr.toString();
        } catch (RuntimeException ex) {
            logger.error(ex.getMessage(), ex);
        }
        return null;
    }
}
