package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.common.CustomResult;
import com.wsgjp.ct.sale.biz.member.utils.ProcessUtil;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.promotion.PromotionPtypeRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.promotion.QueryPromotionRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.*;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.promotion.PromotionCredits;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.promotion.PromotionPageResponse;
import com.wsgjp.ct.sale.biz.shopsale.service.PromotionService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.annontaion.NgpResource;
import ngp.utils.DateUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Api(tags = "促销")
@RequestMapping("${app.id}/shopsale/promotion")
@RestController
public class PromotionController {

    @Autowired
    PromotionService promotionService;

    //region 促销
    @ApiOperation(value = "插入促销")
    @PostMapping(value = "/insertPromotion")
    @NgpResource(name = "shopsale.insertPromotion", tagStrings = "'tagA,'+0")
    boolean insertPromotion(@RequestBody PromotionInfo promotionInfo) {
        return promotionService.insertPromotion(promotionInfo);
    }


    @ApiOperation(value = "检查促销")
    @PostMapping(value = "/checkPromotionIn")
    boolean checkPromotionIn(@RequestBody PromotionInfo promotionInfo) {
        promotionInfo.setProfileId(CurrentUser.getProfileId());
        return promotionService.checkPromotionIn(promotionInfo);
    }

    @ApiOperation(value = "修改促销")
    @PostMapping(value = "/updatePromotion")
    boolean updatePromotion(@RequestBody PromotionInfo promotionInfo) {
        return promotionService.updatePromotion(promotionInfo);
    }

    @ApiOperation(value = "修改促销优先级")
    @PostMapping(value = "/exChangePromotionPriority")
    boolean exChangePromotionPriority(@RequestBody QueryPromotionRequest params) {
        return promotionService.exChangePromotionPriority(params);
    }

    @ApiOperation(value = "修改促销自动配置")
    @PostMapping(value = "/exChangePromotionAutomation")
    boolean exChangePromotionAutomation(@RequestBody SysData params) {
        return promotionService.exChangePromotionAutomation(params);
    }

    @ApiOperation(value = "修改促销自动配置")
    @PostMapping(value = "/getPromotionAutomation")
    @WebLogs
    String getPromotionAutomation() {
        return promotionService.getPromotionAutomation();
    }


    @ApiOperation(value = "删除促销")
    @PostMapping(value = "/deletePromotion")
    boolean deletePromotion(@RequestBody BigInteger id) {
        return promotionService.deletePromotion(id);
    }

    @ApiOperation(value = "促销启用状态改变")
    @PostMapping(value = "/stopPromotion")
    boolean stopPromotion(@RequestBody PromotionInfo promotionInfo) {
        return promotionService.stopPromotion(promotionInfo);
    }

    @ApiOperation(value = "获取促销基本信息")
    @PostMapping(value = "/getPromotionInfoInit")
    public PromotionPageResponse getPromotionInfoInit(@RequestBody QueryPromotionRequest params) {
        return promotionService.getPromotionInfoInit(params);
    }


    @ApiOperation(value = "获取促销基本信息")
    @PostMapping(value = "/getPromotionInfo")
    @NgpResource(name = "shopsale.getPromotionInfo", tagStrings = "'tagA,'+0")
    public PromotionInfo getPromotionInfo(@RequestBody QueryPromotionRequest params) {
        return promotionService.getPromotionInfo(params);
    }

    @ApiOperation(value = "获取完整促销信息")
    @PostMapping(value = "/getFullPromotionInfo")
    @WebLogs
    public PromotionInfo getFullPromotionInfo(@RequestBody BigInteger id) {
        return promotionService.getFullPromotionInfo(id);
    }

    @ApiOperation(value = "查询促销列表")
    @PostMapping(value = "/getPromotionList")
    @NgpResource(name = "shopsale.getPromotionList", tagStrings = "'tagA,'+0")
    PageResponse<PromotionInfo> getPromotionList(@RequestBody PageRequest<QueryPromotionRequest> request) {
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        List<PromotionInfo> list = promotionService.getPromotionList(request);
        return PageDevice.readPage(list);
    }


    //endregion

    //region 促销策略策略

    @ApiOperation(value = "插入促销策略")
    @PostMapping(value = "/insertStrategy")
    @NgpResource(name = "shopsale.insertStrategy", tagStrings = "'tagA,'+0")
    public boolean insertStrategy(@RequestBody PromotionStrategy strategy) {
        return promotionService.insertStrategy(strategy);
    }

    @ApiOperation(value = "批量插入促销粗略")
    @PostMapping(value = "/insertStrategies")
    public boolean insertStrategies(@RequestBody List<PromotionStrategy> list) {
        return promotionService.insertStrategies(list);
    }


    @ApiOperation(value = "修改促销策略")
    @PostMapping(value = "/updateStrategy")
    public boolean updateStrategy(@RequestBody PromotionStrategy strategy) {
        return promotionService.updateStrategy(strategy);
    }

    @ApiOperation(value = "获取促销策略列表")
    @PostMapping(value = "/getStrategy")
    List<PromotionStrategy> getStrategy(@RequestBody List<BigInteger> promotionIds) {
        return promotionService.getStrategy(promotionIds);
    }

    //endregion

    //region 促销机构
    @ApiOperation(value = "插入促销机构")
    @PostMapping(value = "/insertPromotionOtype")
    public boolean insertPromotionOtype(@RequestBody PromotionFilterType promotionOtype) {
        return promotionService.insertPromotionOtype(promotionOtype);
    }

    @ApiOperation(value = "插入促销机构列表")
    @PostMapping(value = "/insertPromotionOtypes")
    public boolean insertPromotionOtypes(@RequestBody List<PromotionFilterType> promotionOtype) {
        return promotionService.insertPromotionOtypes(promotionOtype);
    }

    @ApiOperation(value = "删除策略销售机构")
    @PostMapping(value = "/deletePromotionOtype")
    public boolean deletePromotionOtype(@RequestBody BigInteger promotionId) {
        return promotionService.deletePromotionOtype(promotionId);
    }

    @ApiOperation(value = "获取销策略售机构列表")
    @PostMapping(value = "/getPromotionOtype")
    public List<PromotionFilterType> getPromotionOtype(@RequestBody BigInteger promotionId) {
        return promotionService.getPromotionOtype(Arrays.asList(promotionId));
    }
    //endregion

    //region 促销商品

    @ApiOperation(value = "插入促销商品")
    @PostMapping(value = "/insertPromotionPtypes")
    public boolean insertPromotionPtypes(@RequestBody List<PromotionPtype> ptypeList) {
        return promotionService.insertPromotionPtypes(ptypeList);
    }

    @ApiOperation(value = "修改促销商品")
    @PostMapping(value = "/updatePromotionPtype")
    public boolean updatePromotionPtype(@RequestBody PromotionPtype ptype) {
        return promotionService.updatePromotionPtype(ptype);
    }

    @ApiOperation(value = "删除促销商品")
    @PostMapping(value = "/deletePromotionPtypes")
    public boolean deletePromotionPtypes(@RequestBody BigInteger promotionId) {
        return promotionService.deletePromotionPtypes(promotionId);
    }

    @ApiOperation(value = "删除单个促销商品")
    @PostMapping(value = "/deletePromotionPtypeById")
    public boolean deletePromotionPtypeById(@RequestBody BigInteger id) {
        return promotionService.deletePromotionPtypeById(id);
    }

    @ApiOperation(value = "获取促销商品列表")
    @PostMapping(value = "/getPromotionPtypes")
    public List<PromotionPtype> getPromotionPtypes(@RequestBody BigInteger promotionId) {
        return promotionService.getPromotionPtypes(Arrays.asList(promotionId));
    }

    @ApiOperation(value = "根据促销类型和ID获取促销商品列表")
    @PostMapping(value = "/getPromotionPtypesByTypeAndId")
    public List<PromotionPtype> getPromotionPtypesByTypeAndId(@RequestBody QueryPromotionRequest request) {
        // 促销类型：0：商品特价 1：订单满减 2:第二件折扣 3：满件赠 4：积分兑换 5：满额赠 6:N元组合购 7:价格等级折扣 8:包邮
        // 如果promotionType < 0，表示不限制类型
        // 可以通过promotionId或promotionIds指定促销ID
        return promotionService.getPromotionPtypesByTypeAndId(request);
    }

    @ApiOperation(value = "云订货查询促销信息")
    @PostMapping(value = "/getCloudOrderPromotionInfo")
    @WebLogs
    public List<PromotionInfo> getCloudOrderPromotionInfo(@RequestBody QueryPromotionRequest request) {
        // 支持两种查询方式：
        // 1. 入参：otypeId + ptypeIds列表 - 根据传入的商品信息获取对应的促销信息
        // 2. 入参：promotionIds列表 - 根据促销活动id查询所有的详情
        // 处理方案：
        // 1. 通过接口/sale/shopsale/promotion/getPromotionList查询到促销id列表（仅查询进行中的促销活动）
        // 2. 根据促销id列表获取完整的促销信息promotionService.getFullPromotionInfo(promotionIds)，并返回
        return promotionService.getCloudOrderPromotionInfo(request);
    }

    @ApiOperation(value = "检查商品中是否存在重复商品")
    @PostMapping(value = "/existPromotion")
    public List<PromotionStrategyPtype> existPromotion(@RequestBody PromotionPtypeRequest request) {
        return promotionService.existRepeatPromotion(request);
    }

    @ApiOperation(value = "清除旧规则中的重复商品信息")
    @PostMapping(value = "/clearOldPromotionPtype")
    public Boolean clearOldPromotionPtype(@RequestBody List<PromotionStrategyPtype> repeatPtypes) {
        return promotionService.clearRepeadPromotionPtypes(repeatPtypes);
    }

    @ApiOperation(value = "获取POS端的积分兑换商品")
    @PostMapping(value = "/getPromtionCredits")
    @WebLogs
    public PageResponse<PromotionCredits> getPromtionCredits(@RequestBody PageRequest<PromotionCredits> request) {
        return promotionService.getPromotionCreditsList(request);
    }

    @ApiOperation(value = "兑换pos端积分商品")
    @PostMapping(value = "/exchangeCreditsGoods")
    @WebLogs
    public CustomResult exchangeCreditsGoods(@RequestBody PromotionCreditsRequest request) throws ParseException {
        return promotionService.exchangeCreditsGoods(request);
    }

    @ApiOperation(value = "批量兑换pos端积分商品并核算")
    @PostMapping(value = "/batchExchangeCreditsGoodsWithPost")
    @WebLogs
    public CustomResult batchExchangeCreditsGoodsWithPost(@RequestBody List<PromotionCreditsRequest> requests) throws ParseException {
        return promotionService.batchExchangeCreditsGoodsWithPost(requests);
    }
    //endregion


    @ApiOperation(value = "删除活动中的商品")
    @PostMapping(value = "/deletedScorePromotion")
    public void deletedScorePromotion(@RequestBody PromotionRequest requests) throws ParseException {
        promotionService.deletedScorePromotion(requests);
    }

    @ApiOperation(value = "获取导入促销商品模板")
    @PostMapping(value = "/getImportPromotionPtypeTemplate")
    public void getImportPromotionPtypeTemplate(HttpServletResponse response, @RequestBody Integer promotionType) throws IOException {
        promotionService.getImportPromotionPtypeTemplate(response, promotionType);
    }

    @ApiOperation(value = "EXCEL导入特价商品")
    @RequestMapping(value = "/importPromotionPtypeByExcel", method = RequestMethod.POST)
    @NgpResource(name = "shopsale.importPromotionPtypeByExcel", tagStrings = "'tagA,'+0")
    public String importPromotionPtypeByExcel(MultipartFile file, double maxValue, Integer promotionType) throws IOException {
        return promotionService.importPromotionPtypeByExcel(file, maxValue, promotionType);
    }

    @ApiOperation(value = "导出错误结果")
    @PostMapping("doExportErrorResult")
    public void download(HttpServletResponse response, @RequestBody String processkey) throws IOException, ClassNotFoundException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("特价商品导入错误结果", "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + DateUtils.formatDate(new Date(), "yyyyMMdd") + ".xlsx");
        Pair<Class, List<Object>> res = ProcessUtil.getVipList(processkey);
        List<PromotionImportPtype> list = new ArrayList<>();
        for (Object o : res.getSecond()) {
            PromotionImportError importPtype = JSONObject.parseObject(JSONObject.toJSONString(o), PromotionImportError.class);
            list.add(importPtype);
        }
        EasyExcel.write(response.getOutputStream(), PromotionImportError.class).sheet().registerWriteHandler(new AbstractColumnWidthStyleStrategy() {
            @Override
            protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> list, Cell cell, Head head, Integer integer, Boolean aBoolean) {
                Sheet sheet = writeSheetHolder.getSheet();
                int columnIndex = cell.getColumnIndex();
                if (columnIndex == 5) {
                    sheet.setColumnWidth(columnIndex, 10000);
                } else {
                    sheet.setColumnWidth(columnIndex, 3000);

                }

            }
        }).doWrite(list);
    }

    /**
     * 根据促销ID查询包邮地址列表
     *
     * @param promotionId 促销ID
     * @return 包邮地址列表
     */
    @GetMapping(value = "/getPromotionAddresses")
    public List<PromotionAddress> getPromotionAddresses(@RequestParam("promotionId") BigInteger promotionId) {
        return promotionService.getPromotionAddressesByPromotionId(promotionId);
    }


    @ApiOperation(value = "根据促销ID获取详情")
    @PostMapping(value = "/getFullPromotionInfoWithIds")
    @WebLogs
    public List<PromotionInfo> getFullPromotionInfoWithIds(@RequestBody List<BigInteger> promotionIds) {
        return promotionService.getFullPromotionInfo(promotionIds);
    }
}
