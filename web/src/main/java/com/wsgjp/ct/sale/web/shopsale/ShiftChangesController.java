package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.shiftchanges.*;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.shiftchanges.ShiftChangesResponse;
import com.wsgjp.ct.sale.biz.shopsale.service.ShiftChangesService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.annontaion.NgpResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "交接班相关")
@RequestMapping("${app.id}/shopsale/shiftchanges")
@RestController

public class ShiftChangesController {
    @Autowired
    private ShiftChangesService shiftChangesService;

    @ApiOperation(value = "新增交接班")
    @PostMapping(value = "/insertShiftChangesRecord")
    @NgpResource(name = "shopsale.insertShiftChangesRecord", tagStrings = "'tagA,'+0")
    @WebLogs
    ShiftChangesRequset insertShiftChangesRecord(@RequestBody ShiftChangesRequset params) {
        return shiftChangesService.insertShiftChangesRecord(params);
    }

    @ApiOperation(value = "交接班统计")
    @PostMapping(value = "/getShiftChangeStatistics")
    @WebLogs
    ShiftChangesResponse getShiftChangeStatistics(@RequestBody BillFinanceInfoQuery requestParam) throws ParseException {
        return shiftChangesService.getShiftChangeStatistics(requestParam);
    }

    @ApiOperation(value = "交接班记录列表")
    @PostMapping(value = "/getShiftChangeList")
    @NgpResource(name = "shopsale.getShiftChangeList", tagStrings = "'tag,'+0")
    @PermissionCheck(key = PermissionShopSale.SHOPSALE_SHIFTCHANGESRECORD_VIEW)
    ShiftChangesPage getShiftChangeList(@RequestBody PageRequest<ShiftChangesRequset> requsetParam) throws ParseException {
        ShiftChangesRequset shiftChangesRequset = requsetParam.getQueryParams();
        shiftChangesRequset.setProfileId(CurrentUser.getProfileId());
        return shiftChangesService.getShiftChangesRecordList(requsetParam);
    }

    @ApiOperation(value = "查询交接班记录列表合计")
    @PostMapping("/getShiftChangesRecordSum/count")
    public Map getShiftChangesRecordSum(@RequestBody ShiftChangesRequset requsetParam) {
        return shiftChangesService.getShiftChangesRecordSum(requsetParam);
    }

    @ApiOperation(value = "查询交接班记单据记录")
    @PostMapping("/getPostTimeAndVchtypeList")
    @WebLogs
    public List<BillFinanceInfoDTO> getPostTimeAndVchtypeList(@RequestBody BillFinanceInfoQuery requsetParam) {
        return shiftChangesService.listPostTimeAndVchtype(requsetParam);
    }

    @ApiOperation(value = "查询交接班销售记录报表")
    @PostMapping("/getShiftChangesSaleRecord")
    @WebLogs
    public PageResponse<ShiftChangesRecordDto> getShiftChangesSaleRecord(@RequestBody PageRequest<ShiftChangesSaleRecordRequest> requestParam) {
        return shiftChangesService.getShiftChangesSaleRecord(requestParam);
    }

    @ApiOperation(value = "新增登录表记录")
    @PostMapping("/insertLoginRecord")
    @WebLogs
    public LoginRecordDto insertLoginRecord(@RequestBody LoginRecordDto requestParam) {
        return shiftChangesService.insertLoginRecord(requestParam);
    }

    @ApiOperation(value = "更新登录表记录")
    @PostMapping("/updateLoginRecord")
    @WebLogs
    public boolean updateAllLoginRecordShiftChanged(@RequestBody LoginRecordDto requestParam) {
        return shiftChangesService.updateAllLoginRecordShiftChanged(requestParam);
    }

}
