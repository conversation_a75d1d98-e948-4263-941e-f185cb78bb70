package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.IndustryConfig;
import com.wsgjp.ct.support.global.entity.SysGlobalConfig;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
public class ConfigInfo {

  public ConfigInfo() {
    IndustryConfig industryConfig = GlobalConfig.get(IndustryConfig.class);
    SysGlobalConfig globalConfig = GlobalConfig.get(SysGlobalConfig.class);
    enabledProps = industryConfig.isEnabledProps();
    enabledBatch = industryConfig.isEnabledBatch();
    enabledTax = globalConfig.isEnabledTax();
  }

  private Boolean enabledBatch;
  private Boolean enabledProps;
  private Boolean enabledTax;

  public Boolean getEnabledProps() {
    return enabledProps;
  }

  public Boolean getEnabledBatch() {
    return enabledBatch;
  }

  public Boolean getEnabledTax() {
    return enabledTax;
  }

  public void setEnabledTax(Boolean enabledTax) {
    this.enabledTax = enabledTax;
  }

  public void setEnabledBatch(Boolean enabledBatch) {
    this.enabledBatch = enabledBatch;
  }

  public void setEnabledProps(Boolean enabledProps) {
    this.enabledProps = enabledProps;
  }
}
