package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.entity.BillDetailLiveBroadcast;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverBillLiveBroadcastService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.StrategyUtils;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigInteger;
import java.util.Collections;
import java.util.List;

/**
 * @Description TODO
 * @Date 2023-06-19 16:00
 * @Created by lingxue
 */
@RestController
@Api("直播")
@RequestMapping("/${app.id}/jarvis/liveBroadcast")
public class DeliverBillLiveBroadcastController {
    private DeliverBillLiveBroadcastService liveBroadcastService;
    private static final Logger logger = LoggerFactory.getLogger(DeliverBillLiveBroadcastController.class);

    public DeliverBillLiveBroadcastController(DeliverBillLiveBroadcastService liveBroadcastService)
    {
        this.liveBroadcastService = liveBroadcastService;
    }

    @ApiOperation(value = "加载明细", notes = "")
    @PostMapping("loadDetails")
    public List<BillDetailLiveBroadcast> loadDetails(@RequestBody List<BigInteger> warehouseTaskIds) {
        return liveBroadcastService.loadDetails(CurrentUser.getProfileId(),warehouseTaskIds);
    }
    @ApiOperation(value = "加载直播场次", notes = "")
    @PostMapping("loadBroadcast")
    public List<BillDetailLiveBroadcast> loadBroadcast() {
        return liveBroadcastService.loadBroadcast(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "修改直播信息", notes = "")
    @PostMapping("modifyLiveBroadcast")
    @NeedProcessMsg(threadPoolName = NeedProcessMsg.ProcessName.BATCH_MODIFY_DISEDTAXEDPRICE)
    public void modifyDisedTaxedPrice(@RequestBody LiveBroadcastRequest request) {
        RedisProcessMessage processMessage = new RedisProcessMessage(request.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        try {
            messageLog.appendMsg("开始修改直播场次信息");
            liveBroadcastService.modifyLiveBroadcast(request.getWarehouseTaskIds(), request.getDetails(),request.isCover());
            messageLog.appendMsg("修改直播场次信息结束");
        } catch (Exception e) {
            messageLog.appendMsg(e.getMessage());
            StrategyProcessLog log = new StrategyProcessLog();
            log.setContent("执行错误："+e.getMessage());
            try {
                StrategyUtils.cacheProcessMessage(request.getProcessId(), Collections.singletonList(log));
            } catch (Exception redisCacheError) {
                StringWriter writer = new StringWriter();
                redisCacheError.printStackTrace(new PrintWriter(writer));
                logger.error("执行修改直播信息连接redis异常, {}", writer);
            }
        } finally {
            processMessage.setFinish();
        }
    }
}

class LiveBroadcastRequest
{
    private List<BigInteger> warehouseTaskIds;
    private List<BillDetailLiveBroadcast> details;
    private String processId;
    private boolean cover;

    public List<BigInteger> getWarehouseTaskIds() {
        return warehouseTaskIds;
    }

    public void setWarehouseTaskIds(List<BigInteger> warehouseTaskIds) {
        this.warehouseTaskIds = warehouseTaskIds;
    }

    public List<BillDetailLiveBroadcast> getDetails() {
        return details;
    }

    public void setDetails(List<BillDetailLiveBroadcast> details) {
        this.details = details;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public boolean isCover() {
        return cover;
    }

    public void setCover(boolean cover) {
        this.cover = cover;
    }
}

