package com.wsgjp.ct.sale.web.eshoporder.entity.request.stock;

import com.wsgjp.ct.sale.biz.eshoporder.log.EshopStockSyncActionLog;
import com.wsgjp.ct.support.log.annotation.Operator;
import com.wsgjp.ct.support.log.entity.QueryParams;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 18/6/2021 下午 5:14
 */
public class StockSyncActionLogQueryParams extends QueryParams {

	private String platformNumId;
	private String platformSkuId;
	private String platformXcode;
	private BigInteger eshopId;
	private String warehouseCode;
	private int actionType;

	@Operator(symbol = "like")
	private String efullname;

	@Operator(symbol = "like", field = "body")
	private String body;

	@Operator(symbol = ">=", field = "log_time")
	private Date beginTime;

	@Operator(symbol = "<=", field = "log_time")
	private Date endTime;

	public String getEfullname() {
		return efullname;
	}

	public void setEfullname(String efullname) {
		this.efullname = efullname;
	}

	public String getBody() {
		return body;
	}

	public void setBody(String body) {
		this.body = body;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	@Override
	public Class getLogClass() {
		return EshopStockSyncActionLog.class;
	}

	public String getPlatformNumId() {
		return platformNumId;
	}

	public void setPlatformNumId(String platformNumId) {
		this.platformNumId = platformNumId;
	}

	public String getPlatformSkuId() {
		return platformSkuId;
	}

	public void setPlatformSkuId(String platformSkuId) {
		this.platformSkuId = platformSkuId;
	}

	public String getPlatformXcode() {
		return platformXcode;
	}

	public void setPlatformXcode(String platformXcode) {
		this.platformXcode = platformXcode;
	}

	public BigInteger getEshopId() {
		return eshopId;
	}

	public void setEshopId(BigInteger eshopId) {
		this.eshopId = eshopId;
	}

	public String getWarehouseCode() {
		return warehouseCode;
	}

	public void setWarehouseCode(String warehouseCode) {
		this.warehouseCode = warehouseCode;
	}

	public int getActionType() {
		return actionType;
	}

	public void setActionType(int actionType) {
		this.actionType = actionType;
	}
}
