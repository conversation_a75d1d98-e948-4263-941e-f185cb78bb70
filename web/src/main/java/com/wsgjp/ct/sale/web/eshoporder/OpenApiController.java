package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopAuthService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.common.BaseInfoCacheUtil;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.GetPlatformOrdersInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.SendOrderDTO;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.DownloadType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchOrderParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.purchase.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopSaleOrderDownloadTask;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.openapi.QueryOnLineBabyRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopSaleOrderDownloadResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.OrderSendResult;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.openapi.QueryOnLineBabyResponse;
import com.wsgjp.ct.sale.biz.eshoporder.impl.purchase.PurchaseOrderManager;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderPlatformService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopPtypeChaneNotifyService;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.openApiEntity.PageCodeResopnse;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.ConfirmOrderRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderByIdRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.MergeTradeRequest;
import com.wsgjp.ct.sale.platform.entity.request.other.PageCodeRequest;
import com.wsgjp.ct.sale.platform.entity.response.decrypt.DecryptResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.ConfirmOrderResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.OrderDownloadResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.order.EshopOrderDownloadFromApiFeature;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.SaleApiBaseRequest;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.ShopTypeCount;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.douyin.DouDianNotifyCenterProps;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.douyin.Extra;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.douyin.Shop;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.resolveaddress.ReceiveResolveHelper;
import com.wsgjp.ct.support.resolveaddress.entity.AreaData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-12-30
 */
@Api(tags = "原始订单对外提供的接口相关")
@RequestMapping("${app.id}/eshoporder/api")
@RestController
public class OpenApiController {

    private final EshopSaleOrderPlatformService platformService;
    private final EshopService eshopService;
    private final PurchaseOrderManager purchaseService;
    private final EshopPtypeChaneNotifyService eshopPtypeChaneNotifyService;
    private final BifrostEshopOrderService eshopOrderService;
    private final BifrostEshopAuthService authService;
    private final BaseInfoCacheUtil cacheUtil;

    public OpenApiController(EshopSaleOrderPlatformService platformService, EshopService eshopService,
                             PurchaseOrderManager purchaseService, EshopPtypeChaneNotifyService eshopPtypeChaneNotifyService, BifrostEshopOrderService eshopOrderService, BifrostEshopAuthService authService, BaseInfoCacheUtil cacheUtil) {
        this.platformService = platformService;
        this.eshopService = eshopService;
        this.purchaseService = purchaseService;
        this.eshopPtypeChaneNotifyService = eshopPtypeChaneNotifyService;
        this.eshopOrderService = eshopOrderService;
        this.authService = authService;
        this.cacheUtil = cacheUtil;
    }

    @PostMapping("/getAllAreas")
    public List<AreaData> getAllArea() {
        return ReceiveResolveHelper.getAllArea();
    }

    @PostMapping("/getAreasWhereType")
    public List<AreaData> getAreasWhereType(int type) {
        Assert.isTrue(type > 0, "请输入正确的地址类型");
        return ReceiveResolveHelper.getAreaWhereType(type);
    }

    @PostMapping("/getChinaArea")
    public List<AreaData> getChinaArea() {
        return ReceiveResolveHelper.getChinaArea();
    }

    @PostMapping("/downloadOrders")
    public List<EshopOrderEntity> getApiOrders(@RequestBody List<BatchOrderParams> orderParams) {
        List<EshopOrderEntity> orderEntityList = new ArrayList<>();
        Map<BigInteger, List<BatchOrderParams>> collect =
                orderParams.stream()
                        .filter(params -> params.getEshopId().compareTo(BigInteger.ZERO) > 0)
                        .collect(Collectors.groupingBy(BatchOrderParams::getEshopId));
        for (Map.Entry<BigInteger, List<BatchOrderParams>> entry : collect.entrySet()) {
            EshopSaleOrderDownloadTask task = new EshopSaleOrderDownloadTask();
            task.setDownloadType(DownloadType.BY_MODIFY);
            task.setOtypeId(entry.getKey());
            EshopInfo eshopInfo = eshopService.getEshopInfoById(CurrentUser.getProfileId(), task.getOtypeId());
            task.setEshopInfo(eshopInfo);
            task.setFilterStr(StringUtils.join(
                    entry.getValue().stream()
                            .map(BatchOrderParams::getTradeOrderId)
                            .collect(Collectors.toList()),
                    ","));
            EshopSaleOrderDownloadResponse response = platformService.doUpdateOrder(task);
            if (response.getApiOrderList().size() > 0) {
                orderEntityList.addAll(response.getApiOrderList());
            }
        }
        return orderEntityList;
    }

    @PostMapping("/sendOrderOnline")
    @ApiOperation("同步物流单号接口")
    public List<OrderSendResult> sendOrder(@RequestBody List<SendOrderDTO> dtoList) {
        if (dtoList == null || dtoList.size() == 0) {
            throw new RuntimeException("没有需要同步发货状态的订单");
        }
        return platformService.sendOrderList(dtoList);
    }

    @PostMapping("/getMergeTradeIds")
    @ApiOperation("查询可合并的订单编号")
    public List<String> getMergeTradeIds(@RequestBody MergeTradeRequest request) {
        if (request == null || request.getOrderList() == null || request.getOrderList().size() == 0) {
            throw new RuntimeException("参数错误,未能获取到传入订单号");
        }
        return platformService.getMergeTradeIds(request);
    }

    @ApiOperation("根据订单号查询平台订单")
    @PostMapping("getPlatformOrders")
    public List<EshopOrderEntity> getPlatformOrders(@RequestBody GetPlatformOrdersInDTO inDTO) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(inDTO.getOrderIds())) {
            throw new RuntimeException("订单号为空");
        }
        if (inDTO.getEshopId() == null) {
            throw new RuntimeException("网店ID为空");
        }
        EshopInfo eshop = eshopService.getEshopInfoById(CurrentUser.getProfileId(), inDTO.getEshopId());
        if (eshop == null) {
            throw new RuntimeException("查询网店返回结果为空");
        }
        boolean featureSupported = EshopUtils.isFeatureSupported(EshopOrderDownloadFromApiFeature.class, eshop.getEshopType());
        if (!featureSupported) {
            throw new RuntimeException("不支持");
        }
        DownloadOrderByIdRequest request = new DownloadOrderByIdRequest();
        request.setTradeIdList(inDTO.getOrderIds());
        OrderDownloadResponse response = eshopOrderService.downloadOrderByTradeIds(request);
        if (response != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(response.getOrderList())) {
            return response.getOrderList();
        } else {
            throw new RuntimeException("下载订单返回结果为空");
        }
    }

    @ApiOperation("根据平台类型获取设备指纹")
    @PostMapping("getPageCodeByEshopType")
    public PageCodeResopnse getPageCodeByEshopType(@RequestBody PageCodeRequest pageCodeRequest) {
        return null;
    }

    /*https://k11pnjpvz1.feishu.cn/docs/doccnCy0bkVQwpRAWkGXkTRH3WK#KLMn6d*/
    @ApiOperation("销售订单变化通知接口")
    @PostMapping("saleOrdersChangeNotify")
    public SaleOrdersChangeNotifyResponse saleOrdersChangeNotify(@RequestBody SaleOrdersChangeNotifyRequest request) {
        return purchaseService.SaleOrdersChangeNotify(request);
    }

    /*https://k11pnjpvz1.feishu.cn/docs/doccnCy0bkVQwpRAWkGXkTRH3WK#pEc5Cw*/
    @ApiOperation("ERP不可下单的网点(往来单位)列表通知接口")
    @PostMapping("deleteBtypeInfoNotify")
    public PurchaseBaseResponse DeleteBtypeInfoNotify(@RequestBody DeleteBtypeInfoNotifyRequest request) {
        List<Otype> otypeList = eshopService.getAllOtypeList(CurrentUser.getProfileId());
        return purchaseService.DoDeleteBtypeInfoNotify(request, otypeList);
    }

    /*https://k11pnjpvz1.feishu.cn/docs/doccn73982NOGRK2qXUSgSlGnPh#kIClKA*/
    @ApiOperation("采购单签收接口")
    @PostMapping("purchaseDeliverChangeNotify")
    public PurchaseDeliverChangeResponse PurchaseDeliverChangeNotify(@RequestBody PurchaseDeliverChangeRequest request) {
        return purchaseService.purchaseDeliverChangeNotify(request);
    }

    @ApiOperation("本地商品查询对应网店商品")
    @PostMapping("queryOnLineBabyListByProduct")
    public QueryOnLineBabyResponse queryOnLineBabyListByProduct(@RequestBody @Valid QueryOnLineBabyRequest request) {
        return eshopPtypeChaneNotifyService.queryOnLineBabyListByProduct(request);
    }

    @ApiOperation("确认订单")
    @PostMapping("confirmOrder")
    public ConfirmOrderResponse confirmOrder(@RequestBody @Valid ConfirmOrderRequest request) {
        return platformService.confirmOrder(request);
    }

    @ApiOperation("判断是否存在抖音店铺")
    @PostMapping("queryHasDouDian")
    public boolean queryHasDouDianEshop(@RequestBody SaleApiBaseRequest request) {
        try {
            int count = eshopService.queryEshopCountByShopType(request.getProfileId(), ShopType.Doudian.getCode());
            return count > 0;
        } catch (Exception ex) {
            return false;
        }
    }

    @ApiOperation("查询客户拥有的店铺类型数量")
    @PostMapping("queryShopCount")
    public List<ShopTypeCount> queryShopCount(@RequestBody SaleApiBaseRequest request) {
        try {
            List<Otype> allOtype = cacheUtil.getAllOtype(request.getProfileId());
            if(CollectionUtils.isEmpty(allOtype)){
                return new ArrayList<>();
            }
            List<ShopTypeCount> result = new ArrayList<>();
            long ddCount = allOtype.stream().filter(x -> x.getEshopInfo().getEshopType().getPlatformType() == ShopType.Doudian.getPlatformType()).count();
            if(ddCount>0){
                ShopTypeCount dd=new ShopTypeCount();
                dd.setCount(ddCount);
                dd.setShopTypeCode(ShopType.Doudian.name());
                dd.setShopTypeName(ShopType.Doudian.getName());
                result.add(dd);
            }
            long pddCount = allOtype.stream().filter(x -> x.getEshopInfo().getEshopType().getPlatformType() == ShopType.PinDuoDuo.getPlatformType()).count();
            if(pddCount>0){
                ShopTypeCount pdd=new ShopTypeCount();
                pdd.setCount(pddCount);
                pdd.setShopTypeCode(ShopType.PinDuoDuo.name());
                pdd.setShopTypeName(ShopType.PinDuoDuo.getName());
                result.add(pdd);
            }
            return result;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    @ApiOperation("查询抖音消息中心需要使用的参数")
    @PostMapping("queryDouDianNotifyCenterJson")
    public DouDianNotifyCenterProps queryDouDianNotifyCenterJson(@RequestBody SaleApiBaseRequest request) {
        int ddCode = ShopType.Doudian.getCode();
        List<EshopInfo> shopList = eshopService.getEshopListByShopType(request.getProfileId(), ddCode);
        if (CollectionUtils.isEmpty(shopList)) {
            throw new RuntimeException("没有抖音店铺");
        }
        return buildProps(shopList);
    }

    private DouDianNotifyCenterProps buildProps(List<EshopInfo> shopList) {
        PlatformBaseConfig config = EshopFactoryManager.getConfig(ShopType.Doudian);
        if (config == null) {
            throw new RuntimeException("系统异常，没有获取到抖音的系统配置");
        }
        String appKey = config.getAppKey();
        DouDianNotifyCenterProps props = new DouDianNotifyCenterProps();
        Extra extra = new Extra();
        List<Shop> list = new ArrayList<>();
        doBuildShopList(shopList, list);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Shop first = list.get(0);
        extra.setShop(list);
        props.setExtra(extra);
        props.setAppId(appKey);
        props.setComponentId(317);
        props.setShopId(first.getShopId());
        props.setToken(first.getValue());
        return props;
    }

    private void doBuildShopList(List<EshopInfo> shopList, List<Shop> list) {
        for (EshopInfo eshopInfo : shopList) {
            BaseRequest req = new BaseRequest();
            req.setShopId(eshopInfo.getOtypeId());
            req.setShopType(ShopType.Doudian);
            DecryptResponse response = authService.getDecryptToken(req);
            if (!response.getSuccess()) {
                continue;
            }
            if (StringUtils.isEmpty(response.getToken())) {
                continue;
            }
            Shop shop = new Shop();
            shop.setShopId(eshopInfo.getOnlineEshopId());
            shop.setShopName(eshopInfo.getFullname());
            shop.setValue(response.getToken());
            list.add(shop);
        }
    }
}
