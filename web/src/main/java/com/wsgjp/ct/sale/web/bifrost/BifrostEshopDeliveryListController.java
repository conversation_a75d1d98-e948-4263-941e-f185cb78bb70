package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopDeliveryListService;
import com.wsgjp.ct.sale.platform.entity.request.deliverylist.DeliveryListPdfRequest;
import com.wsgjp.ct.sale.platform.entity.response.deliverylist.DeliveryListPdfResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "网店配送清单相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/deliveryList")
public class BifrostEshopDeliveryListController {
    private final BifrostEshopDeliveryListService eshopDeliveryListService;

    public BifrostEshopDeliveryListController(BifrostEshopDeliveryListService eshopDeliveryListService){
        this.eshopDeliveryListService = eshopDeliveryListService;
    }

    /**
     * 根据订单号批量获取配送清单
     */
    @ApiOperation("根据订单号批量获取配送清单")
    @PostMapping("batchDownloadDeliveryListPdf")
    public DeliveryListPdfResponse batchDownloadDeliveryListPdf(@RequestBody DeliveryListPdfRequest pdfRequest){
        return eshopDeliveryListService.batchDownloadDeliveryListPdf(pdfRequest);
    }
}

