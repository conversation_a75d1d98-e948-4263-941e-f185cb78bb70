package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import bf.datasource.page.Sort;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.statistics.*;
import com.wsgjp.ct.sale.biz.jarvis.service.statistics.DataStatistics;
import com.wsgjp.ct.sale.biz.jarvis.state.QueryTimeTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@Api(description = "订单监控看板")
@RequestMapping("/${app.id}/jarvis/statistics")
public class DataStatisticsController {

    private DataStatistics statistics;

    public DataStatisticsController(DataStatistics statistics) {
        this.statistics = statistics;
    }

    @ApiOperation(value = "未发订单数")
    @PostMapping("getNotSendBillCount")
    public List<DataStatisticsEntity> getNotSendBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getNotSendBillCount(request);
    }

    @ApiOperation(value = "已发订单数")
    @PostMapping("getSendBillCount")
    public List<DataStatisticsEntity> getSendBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getSendBillCount(request);
    }

    @ApiOperation(value = "即将超时订单数")
    @PostMapping("getAboutToOverTimeBillCount")
    public List<DataStatisticsEntity> getAboutToOverTimeBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getAboutToOverTimeBillCount(request);
    }

    @ApiOperation(value = "超时订单数")
    @PostMapping("getOverTimeBillCount")
    public List<DataStatisticsEntity> getOverTimeBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getOverTimeBillCount(request);
    }

    @ApiOperation(value = "未审核售后单数")
    @PostMapping("getNotAuditRefundBillCount")
    public List<DataStatisticsEntity> getNotAuditRefundBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getNotAuditRefundBillCount(request);
    }

    @ApiOperation(value = "已审核售后单数")
    @PostMapping("getAuditRefundBillCount")
    public List<DataStatisticsEntity> getAuditRefundBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getAuditRefundBillCount(request);
    }

    @ApiOperation(value = "即将超时售后单数")
    @PostMapping("getAboutToOverTimeRefundCount")
    public List<DataStatisticsEntity> getAboutToOverTimeRefundCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getAboutToOverTimeRefundCount(request);
    }

    @ApiOperation(value = "超时售后单数")
    @PostMapping("getOverTimeRefundCount")
    public List<DataStatisticsEntity> getOverTimeRefundCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getOverTimeRefundCount(request);
    }

    @ApiOperation(value = "同步单号即将超时订单数")
    @PostMapping("getAboutToOverTimeSyncCount")
    public List<DataStatisticsEntity> getAboutToOverTimeSyncCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getAboutToOverTimeSyncCount(request);
    }

    @ApiOperation(value = "同步单号超时订单数")
    @PostMapping("getOverTimeSyncCount")
    public List<DataStatisticsEntity> getOverTimeSyncCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getOverTimeSyncCount(request);
    }

    @ApiOperation(value = "同步失败订单数")
    @PostMapping("getSyncFailBillCount")
    public List<DataStatisticsEntity> getSyncFailBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getSyncFailBillCount(request);
    }

    @ApiOperation(value = "线上已发货，系统未出库订单数")
    @PostMapping("getOnlineSendNotLocalBillCount")
    public List<DataStatisticsEntity> getOnlineSendNotLocalBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getOnlineSendNotLocalBillCount(request);
    }

    @ApiOperation(value = "系统已出库，线上未发货订单数")
    @PostMapping("getLocalSendNotOnlineBillCount")
    public List<DataStatisticsEntity> getLocalSendNotOnlineBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getLocalSendNotOnlineBillCount(request);
    }

    @ApiOperation(value = "线上部分发货订单数")
    @PostMapping("getPartOnlineSendBillCount")
    public List<DataStatisticsEntity> getPartOnlineSendBillCount(@RequestBody DataStatisticsRequest request) throws Exception {
        return statistics.getPartOnlineSendBillCount(request);
    }

    @ApiOperation(value = "发货业务-商品")
    @PostMapping("getGoodsGridForSend")
    public PageResponse<GoodsForSendResponse> getGoodsGridForSend(@RequestBody PageRequest<DataStatisticsRequest> request) throws Exception {
        try {
            statistics.handleRequest(request.getQueryParams());
        } catch (Exception e) {
            statistics.handleLogger("发货业务-商品",e);
            return new PageResponse<>();
        }
        statistics.buildGoodsInfo(request);
        return statistics.getGoodsGridForSend(request);
    }

    @ApiOperation(value = "发货业务-订单")
    @PostMapping("getBillGridForSend")
    public PageResponse<BillsForSendResponse> getBillGridForSend(@RequestBody PageRequest<DataStatisticsRequest> request) throws Exception {
        try {
            statistics.handleRequest(request.getQueryParams());
        } catch (Exception e) {
            statistics.handleLogger("发货业务-订单",e);
            return new PageResponse<>();
        }
        List<Sort> sortList=new ArrayList<>();
        Sort defaultSort = new Sort();
        defaultSort.setAscending(true);
        defaultSort.setDataField("fullLinkStatus");
        sortList.add(defaultSort);
        //处理排序
        QueryTimeTypeEnum queryTimeType = request.getQueryParams().getQueryTimeType();
        Sort sort = new Sort();
        sort.setAscending(false);
        if (QueryTimeTypeEnum.TRADE_CREATE_TIME.equals(queryTimeType)) {
            sort.setDataField("tradeCreateTime");
        } else if (QueryTimeTypeEnum.PAY_TIME.equals(queryTimeType)) {
            sort.setDataField("payTime");
        }
        sortList.add(sort);
        request.setSorts(sortList);
        return statistics.getBillGridForSend(request);
    }

    @ApiOperation(value = "售后业务-商品")
    @PostMapping("getGoodsGridForRefund")
    public PageResponse<GoodsForRefundResponse> getGoodsGridForRefund(@RequestBody PageRequest<DataStatisticsRequest> request) throws Exception {
        return statistics.getGoodsGridForRefund(request);
    }

    @ApiOperation(value = "售后业务-售后单")
    @PostMapping("getBillGridForRefund")
    public PageResponse<EshopRefundEntity> getBillGridForRefund(@RequestBody PageRequest<DataStatisticsRequest> request) throws Exception {
        return statistics.getBillGridForRefund(request);
    }

}
