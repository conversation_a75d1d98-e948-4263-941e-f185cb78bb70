package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverSendService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2022-04-25 15:27
 */
@RestController
@Api(description = "交易单-同步单号接口")
@RequestMapping("/${app.id}/jarvis/onlineSend")
public class OnlineSendController {
    private BillDeliverSendService billDeliverSendService;

    public OnlineSendController(BillDeliverSendService billDeliverSendService) {
        this.billDeliverSendService = billDeliverSendService;
    }

    /*@ApiOperation(value = "交易单-同步物流单号")
    @PostMapping("syncWayBillByBill")
    @PermissionCheck(key = PermissionSysConst.DELIVER_COMMON_SYNC_WAY_BILL)
    public List<RebuildMessageVo> onlineSendByBill(@RequestBody ProcessRequest request) {
        try {
            List<RebuildMessageVo> result = billDeliverSendService.onlineSend(CurrentUser.getProfileId(), request.getVchcodes(), null);
            return result;
        } catch (Exception e) {
            List<RebuildMessageVo> errorList = new ArrayList<>();
            RebuildMessageVo errorResult=new RebuildMessageVo();
            errorResult.setStatus("ERROR");
            errorResult.setContent("执行错误：" + e.getMessage());
            errorList.add(errorResult);
            return errorList;
        }
    }*/
}
