package com.wsgjp.ct.sale.web.eshoporder.entity;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EnumState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundOperationType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public class EshopRefundInitResponse extends CommonInitResponse {
	private RefundOperationType refundOperationType;
	private String title;
	private EshopRefundEntity refund;
	private boolean strictSno;
	private List<EnumState> refundTypeSource;
	private List<EshopRefundConfigReason> reasonList;

	public EshopRefundInitResponse(RefundOperationType refundOperationType) {
		this.refundOperationType = refundOperationType;
	}

	public RefundOperationType getRefundOperationType() {
		return refundOperationType;
	}

	public void setRefundOperationType(RefundOperationType refundOperationType) {
		this.refundOperationType = refundOperationType;
	}

	public EshopRefundEntity getRefund() {
		return refund;
	}

	public void setRefund(EshopRefundEntity refund) {
		this.refund = refund;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public void setStrictSno(boolean strictSno) {
		this.strictSno = strictSno;
	}

	public boolean isStrictSno() {
		return strictSno;
	}

	public List<EshopRefundConfigReason> getReasonList() {
		return reasonList;
	}

	public void setReasonList(List<EshopRefundConfigReason> reasonList) {
		this.reasonList = reasonList;
	}

	public List<EnumState> getRefundTypeSource() {
		return refundTypeSource;
	}

	public void setRefundTypeSource(List<EnumState> refundTypeSource) {
		this.refundTypeSource = refundTypeSource;
	}
}
