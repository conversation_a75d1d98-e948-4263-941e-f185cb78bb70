package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.common.enums.core.utils.ClassHelper;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.api.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderGlobalConfig;
import com.wsgjp.ct.sale.biz.jarvis.boot.JarvisConfig;
import com.wsgjp.ct.sale.biz.jarvis.config.sys.BillDetailModifyConfig;
import com.wsgjp.ct.sale.biz.jarvis.config.sys.PostConfig;
import com.wsgjp.ct.sale.biz.jarvis.config.sys.SupplierBillConfig;
import com.wsgjp.ct.sale.biz.jarvis.dto.config.ConfigBase;
import com.wsgjp.ct.sale.biz.jarvis.dto.config.ConfigBaseDTO;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.jarvis.service.config.ConfigService;
import com.wsgjp.ct.support.annotation.ConfigPrefix;
import com.wsgjp.ct.support.annotation.Description;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.BaseConfig;
import com.wsgjp.ct.support.global.GlobalConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-19 18:34
 */
@RestController
@Api(description = "配置查询")
@RequestMapping("/${app.id}/jarvis/config")
public class ConfigController {

    private final Logger logger = LoggerFactory.getLogger(ConfigController.class);
    private final static String packageName = "com.wsgjp.ct.sale.biz.jarvis.config.";
    private ConfigService configService;
    private JarvisConfig jarvisConfig;

    public ConfigController(ConfigService configService, JarvisConfig jarvisConfig) {
        this.configService = configService;
        this.jarvisConfig = jarvisConfig;
    }

    @ApiOperation(value = "查询配置", notes = "查询配置")
    @PostMapping("/getConfigByClassName")
    public BaseConfig getConfigByClassName(@RequestParam String className, @RequestParam String prefix) {

        try {
            Class clz = Class.forName(packageName + className);
            BaseConfig config = (BaseConfig) GlobalConfig.get(clz, StringUtils.isEmpty(prefix) ? null : prefix);
            return config;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取配置出错！");
        }
    }

    @ApiOperation(value = "查询配置", notes = "查询配置")
    @PostMapping("/getConfigInfoByClassName")
    public ConfigBase getConfigInfoByClassName(@RequestParam String className, @RequestParam String prefix) {
        try {
            ConfigBase configBase = configService.getConfigByClassName(CurrentUser.getProfileId(), className, prefix);
            return configBase;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取配置出错！");
        }
    }

    @ApiOperation(value = "保存配置", notes = "保存配置")
    @PostMapping("/saveConfigByClassNmae")
    public void saveConfigByClassNmae(@RequestBody Object config) {
        try {
            String className = ((Map<String, Object>) config).get("className").toString();
            Object prefix = ((Map<String, Object>) config).get("prefix");
            Class clz = Class.forName(packageName + className);
            BaseConfig oldConfig = (BaseConfig) GlobalConfig.get(clz, (prefix == null ||StringUtils.isEmpty(prefix.toString()))? null : prefix.toString());
            String configJson = JsonUtils.toJson(config);
            ConfigBase configBase = JsonUtils.toObject(configJson, ConfigBase.class, clz);
            BaseConfig pickSecondConfig = configBase.getConfigInfo();
            List<ConfigBaseDTO> listInfo = configBase.getListEnabled();
            if (listInfo != null) {
                for (int i = 0; i < listInfo.size(); i++) {
                    ConfigBaseDTO configBaseDTO = listInfo.get(i);
                    Field field = pickSecondConfig.getClass().getDeclaredField(configBaseDTO.getField());
                    field.setAccessible(true);
                    field.setBoolean(pickSecondConfig, configBaseDTO.getEnabled());
                }
            }
            GlobalConfig.putAll(pickSecondConfig, StringUtils.isEmpty(configBase.getPrefix()) ? null : configBase.getPrefix());
            addLog(oldConfig, pickSecondConfig, clz,prefix == null ? "" : prefix.toString());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("保存配置出错！");
        }
    }

    @ApiOperation(value = "保存配置", notes = "保存配置")
    @PostMapping("/saveConfig")
    public void saveConfig(@RequestBody Map<String, Object> configs) {
        try {
            String className =  configs.get("className").toString();
            String prefix = configs.get("prefix").toString();
            if(prefix.equals("jarvis.scanCheckGoodsByBill") && !PermissionValiateService.validate(PermissionSysConst.DELIVER_SCAN_CHECK_GOODS_BY_BILL_CONFIG))
            {
                throw new RuntimeException("您没有该操作的权限！");
            }
            else if(prefix.equals("jarvis.scanCheckGoodsByAllBill") && !PermissionValiateService.validate(PermissionSysConst.DELIVER_SCAN_CHECK_GOODS_BY_ALL_BILL_CONFIG))
            {
                throw new RuntimeException("您没有该操作的权限！");
            }
            else if(prefix.equals("jarvis.scanCheckGoodsByGoods") && !PermissionValiateService.validate(PermissionSysConst.DELIVER_SCAN_CHECK_GOODS_BY_GOODS_CONFIG))
            {
                throw new RuntimeException("您没有该操作的权限！");
            }
            else if(prefix.equals("jarvis.scanCheckGoodsByBatch") && !PermissionValiateService.validate(PermissionSysConst.DELIVER_SCAN_CHECK_GOODS_BY_BATCH_CONFIG))
            {
                throw new RuntimeException("您没有该操作的权限！");
            }
            else if(prefix.equals("jarvis.scanCheckGoodsByMulti") && !PermissionValiateService.validate(PermissionSysConst.DELIVER_SCAN_CHECK_GOODS_BY_MULTI_CONFIG))
            {
                throw new RuntimeException("您没有该操作的权限！");
            }
            else if(prefix.equals("jarvis.sendDeliverBillByBill") && !PermissionValiateService.validate(PermissionSysConst.DELIVER_BILL_SEND_BY_BILL_CONFIG))
            {
                throw new RuntimeException("您没有该操作的权限！");
            }
            else if(prefix.equals("jarvis.sendDeliverBillByBatch") && !PermissionValiateService.validate(PermissionSysConst.DELIVER_BILL_SEND_BY_BATCH_CONFIG))
            {
                throw new RuntimeException("您没有该操作的权限！");
            }
            BaseConfig oldConfig = getConfigByClassName(className, prefix);
            Class clz = Class.forName(packageName + className);
            String configJson = JsonUtils.toJson(((Map<String, Object>) configs).get("configs"));
            BaseConfig configBase = (BaseConfig)JsonUtils.toObject(configJson, clz);
            GlobalConfig.putAll(configBase, StringUtils.isEmpty(prefix) ? null : prefix);
            addLog(oldConfig, configBase, clz, prefix);

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("保存配置出错！");
        }
    }

    @ApiOperation(value = "保存配置", notes = "保存配置")
    @PostMapping("/saveUserConfig")
    public void saveUserConfig(@RequestParam String key, @RequestParam String value) {
        try {
            GlobalConfig.putUserData(key, value,"");
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("保存配置出错！");
        }
    }

    private void addLog(BaseConfig oldConfig, BaseConfig baseConfig, Class clz, String prefix) {
//        ObjectMapper objectMapper = new ObjectMapper();
//        BaseConfig newConfig = objectMapper.convertValue(baseConfig, BaseConfig.class);
        Map<String, Object> newMap = baseConfig.toMap();
        Map<String, Object> oldMap = oldConfig.toMap();
        Description configDescription = baseConfig.getClass().getAnnotation(Description.class);
        String configName = "";
        if (configDescription != null) {
            String[] strs = configDescription.desc().split(";");
            if(strs.length>1){
                for (String str : strs) {
                    String[]  strValue  = str.split("=");
                    if(prefix.equals(strValue[0].toString())){
                        configName = strValue[1].toString();
                        break;
                    }
                }
            }else{
                configName = configDescription.desc();
            }


        }
        ConfigPrefix classPrefix = (ConfigPrefix) clz.getAnnotation(ConfigPrefix.class);
        String classPrefixStr = classPrefix.prefix();
        StringBuilder logContent = new StringBuilder();
        Field[] configFields = ClassHelper.getDeclaredFields(clz);
        for (int i = 0; i < configFields.length; i++) {
            Field field = configFields[i];
            field.setAccessible(true);
            String fieldName = field.getName();
            Description description = field.getAnnotation(Description.class);
            String key = getKey(classPrefixStr, fieldName);
            String oldValue = convertBoolean(oldMap.get(key));
            String newValue = convertBoolean(newMap.get(key));
            if (description != null && !oldValue.equals(newValue)) {
                Set<String> descSet=new HashSet<>(Arrays.asList("jarvisScanCheckGoodsByBillComboType","jarvisScanCheckGoodsByBillScanType","jarvisScanCheckGoodsByBillGiftType"));
                if(descSet.contains(key)){
                    oldValue=getCheckGoodesConfigDesc(key,oldValue);
                    newValue=getCheckGoodesConfigDesc(key,newValue);
                }
                logContent.append(String.format("%s:%s->%s ", description.desc(), oldValue, newValue));
            }
        }
        if (!StringUtils.isEmpty(logContent.toString())) {
            PubSystemLogService.saveInfo(String.format("业务配置，%s，【%s】", logContent.toString(), configName));
        }

    }

    /**
     * 功能描述: 获取验货配置中对应配置项的描述信息
     * 套餐验货设置,普通商品扫描配置
     * @param
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @since 2021-12-17 10:43
     */
    private String getCheckGoodesConfigDesc(String key,String value){
        String keyValue=String.format("%s_%s",key,value);
        Map<String,String> descMap=new HashMap();
        descMap.put("jarvisScanCheckGoodsByBillComboType_0","套餐独立验货");
        descMap.put("jarvisScanCheckGoodsByBillComboType_1","套餐与明细一起验货");
        //普通商品扫描配置
        descMap.put("jarvisScanCheckGoodsByBillScanType_0","每行商品逐一扫描，默认");
        descMap.put("jarvisScanCheckGoodsByBillScanType_1","每行商品扫描一次，数量自动填充");
        //赠品扫描配置
        descMap.put("jarvisScanCheckGoodsByBillGiftType_0","赠品不验货显示赠品，默认验货通过");
        descMap.put("jarvisScanCheckGoodsByBillGiftType_1","赠品验货，按正常商品验货");
        if(!descMap.containsKey(keyValue)){
            return value;
        }
        return descMap.get(keyValue);
    }

    private String convertBoolean(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof Boolean) {
            if ((Boolean) value) {
                return "是";
            } else {
                return "否";
            }
        }
        return value.toString();
    }

    private static String getKey(String prefix, String field) {
        return StringUtils.isNotEmpty(prefix) ? caseToCamelCase(prefix) + toFirstUpperCase(field) : field;
    }

    private static String caseToCamelCase(String key) {
        StringBuilder builder = new StringBuilder();
        char[] chars = key.toCharArray();
        builder.append(chars[0]);

        for (int i = 1; i < key.length(); ++i) {
            char character = chars[i - 1];
            if (character == '.') {
                char ch = (char) (chars[i] - 32);
                builder.append(ch);
            } else if (chars[i] != '.') {
                builder.append(chars[i]);
            }
        }

        return builder.toString();
    }

    private static String toFirstUpperCase(String str) {
        char[] ch = str.toCharArray();
        if (ch[0] >= 'a' && ch[0] <= 'z') {
            ch[0] = (char) (ch[0] - 32);
        }

        return new String(ch);
    }

    @ApiOperation(value = "获取所有配置", notes = "获取所有配置")
    @PostMapping("/getAllSysData")
    public Map<String, Object> getAllSysData() {
        return GlobalConfig.getLocalSysData();
    }

    @ApiOperation(value = "查询当前账套的徽标开启配置", notes = "部署级不计算徽标开关或者账套级不计算徽开关打开-> 返回false,表示此账套不需要前端页面实时查询徽标数量。反之需要")
    @PostMapping("/getCalculateExceptionStatusConfig")
    public boolean getCalculateExceptionStatusConfig() {
        // 开启了徽标消息关闭配置，立即不发送消息，遗留消息消费时也不执行业务，直接返回
        // 账套是否开启徽标功能
        String profileExceptionMessageAndConsumer = GlobalConfig.get(EshopOrderGlobalConfig.class).getEnableExceptionMessageAndConsumer();
        // 部署级不计算徽标开关或者账套级不计算徽开关打开-> 返回false,表示此账套不需要前端页面实时查询徽标数量。反之需要
        if (jarvisConfig.isNoExceptionMessageAndConsumer() || "0".equals(profileExceptionMessageAndConsumer)) {
            return false;
        } else {
            return true;
        }
    }

    @ApiOperation(value = "修改单据核算设置")
    @PostMapping("/updatePostConfig")
    public BaseResponse updatePostConfig(@RequestBody PostConfig config) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        try {
            GlobalConfig.putAll(config);
            //从【立即财务】修改为【交易成功财务】：不更新已提交订单的FinanceProcState
            //从【交易成功财务】修改为【立即财务】：将FinanceProcState为2的订单全部修改为0，对于全部出入库还未交易成功的订单，需要客户自己在订单查询界面点击重新核算，bill_date设置为点击重新核算的时间。
            if ("0".equals(config.getSendPostConfig())) {
                configService.updateFinanceProcState(2,0);
            }
            //售后业务，修改配置，待处理

            //记录系统日志
            PubSystemLogService.saveInfo(String.format("修改单据核算设置|发货业务：%s，售后业务：%s",config.getSendPostConfig(),config.getRefundPostConfig()));
        } catch (Exception e) {
            String msg = String.format("修改单据核算设置失败：%s", e.getMessage());
            response.setCode("-1");
            response.setMsg(msg);
            logger.error(msg,e);
            PubSystemLogService.saveInfo(msg);
        }
        return response;
    }

    @ApiOperation(value = "查询单据核算设置")
    @PostMapping("/getPostConfig")
    public PostConfig getPostConfig() {
        PostConfig response = new PostConfig();
        try {
            response.setSendPostConfig(GlobalConfig.get(PostConfig.class).getSendPostConfig());
            response.setRefundPostConfig(GlobalConfig.get(PostConfig.class).getRefundPostConfig());
        } catch (Exception e) {
            String msg = String.format("查询单据核算设置失败：%s", e.getMessage());
            logger.error(msg,e);
        }
        return response;
    }

    @ApiOperation(value = "查询供应商发货设置")
    @PostMapping("/getSupplierBillConfig")
    public SupplierBillConfig getSupplierBillConfig() {
        SupplierBillConfig response = new SupplierBillConfig();
        try {
            response.setAmountSubmit(GlobalConfig.get(SupplierBillConfig.class).isAmountSubmit());
            response.setAutoSubmit(GlobalConfig.get(SupplierBillConfig.class).isAutoSubmit());
            response.setSyncAfterSend(GlobalConfig.get(SupplierBillConfig.class).isSyncAfterSend());
        } catch (Exception e) {
            String msg = String.format("查询供应商发货设置失败：%s", e.getMessage());
            logger.error(msg,e);
        }
        return response;
    }

    @ApiOperation(value = "修改供应商发货设置")
    @PostMapping("/updateSupplierBillConfig")
    public BaseResponse updatePostConfig(@RequestBody SupplierBillConfig config) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        try {
            GlobalConfig.putAll(config);
            PubSystemLogService.saveInfo(String.format("修改代销订单发货配置|是否提交买家结算金额：%s, 审核通过代销订单，" +
                    "自动提交供应商发货: %s, 供应商完成发货后，订单自动系统发货：%s",config.isAmountSubmit() ? "是" : "否",
                    config.isAutoSubmit() ? "是" : "否", config.isSyncAfterSend() ? "是" : "否"));
        } catch (Exception e) {
            String msg = String.format("修改供应商发货设置失败：%s", e.getMessage());
            response.setCode("-1");
            response.setMsg(msg);
            logger.error(msg,e);
        }
        return response;
    }
    @ApiOperation(value = "保存单据设置配置")
    @PostMapping("/saveEshopBillConfig")
    public BaseResponse saveEshopBillConfig(@RequestBody Map<String, String> configs) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        try {
            GlobalConfig.putAll(configs);
            GlobalConfig.resetCache();
        } catch (Exception e) {
            String msg = String.format("修改单据设置配置失败：%s", e.getMessage());
            response.setCode("-1");
            response.setMsg(msg);
            logger.error(msg,e);
        }
        return response;
    }

    @ApiOperation(value = "保存页面上的显示配置 职员级别")
    @PostMapping("/saveUserPageShowConfig")
    public BaseResponse saveUserPageShowConfig(@RequestBody Map<String, String> configs) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        try {
            GlobalConfig.putAll(configs, true);
        } catch (Exception e) {
            String msg = String.format("修改配置失败：%s", e.getMessage());
            response.setCode("-1");
            response.setMsg(msg);
            logger.error(msg,e);
        }
        return response;
    }

    @ApiOperation(value = "获取单据设置配置 职员级别")
    @PostMapping("/getUserPageShowConfigByKeys")
    public Map<String, Object> getUserPageShowConfigByKeys(@RequestParam String key) {
        try {
            return configService.getUserConfig(key);
        } catch (Exception e) {
            String msg = String.format("获取配置失败：%s", e.getMessage());
            logger.error(msg,e);
            throw new RuntimeException(msg);
        }
    }

    @ApiOperation(value = "保存页面上的显示配置")
    @PostMapping("/savePageShowConfig")
    public BaseResponse savePageShowConfig(@RequestBody Map<String, String> configs) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        try {
            GlobalConfig.putAll(configs, false);
        } catch (Exception e) {
            String msg = String.format("修改配置失败：%s", e.getMessage());
            response.setCode("-1");
            response.setMsg(msg);
            logger.error(msg,e);
        }
        return response;
    }

    @ApiOperation(value = "获取单据设置配置")
    @PostMapping("/getPageShowConfigByKeys")
    public Map<String, Object> getPageShowConfigByKeys(@RequestParam String key) {
        try {
            return GlobalConfig.get(BillDetailModifyConfig.class).toMap();
        } catch (Exception e) {
            String msg = String.format("获取配置失败：%s", e.getMessage());
            logger.error(msg,e);
            throw new RuntimeException(msg);
        }
    }
    @ApiOperation(value = "获取单据设置配置")
    @PostMapping("/getEshopBillConfig")
    public Map<String, Object>  getEshopBillConfig() {
        Map<String, Object>  response = new HashMap<>();
        try {
            response.put("jarvisDeliverBillEnableNotChangeBillTotal",GlobalConfig.get("jarvisDeliverBillEnableNotChangeBillTotal"));
            response.put("jarvisDeliverBillDetailPictureShowPriority",GlobalConfig.get("jarvisDeliverBillDetailPictureShowPriority"));
            response.put("jarvisDeliverBillShowOnlineDetailPicturePriority",GlobalConfig.get("jarvisDeliverBillShowOnlineDetailPicturePriority"));
            response.put("jarvisDeliverBillDetailShowEnabled",GlobalConfig.get("jarvisDeliverBillDetailShowEnabled"));
            response.put("jarvisDeliverBillDetailShowType",GlobalConfig.get("jarvisDeliverBillDetailShowType"));
            response.put("jarvisDeliverBillComboShowType",GlobalConfig.get("jarvisDeliverBillComboShowType"));
            response.put("jarvisDeliverBillPicSizeType",GlobalConfig.get("jarvisDeliverBillPicSizeType"));
            response.put("jarvisDeliverBillIsAutoCreatePosition",GlobalConfig.get("jarvisDeliverBillIsAutoCreatePosition"));
            response.put("jarvisDeliverBillModifyQtyType",GlobalConfig.get("jarvisDeliverBillModifyQtyType"));
            response.put("jarvisDeliverBillEnableAllowEmptyBatch",GlobalConfig.get("jarvisDeliverBillEnableAllowEmptyBatch"));
            response.put("lowestSellingPriceEnable",GlobalConfig.get("lowestSellingPriceEnable"));
            response.put("sellingPriceType",GlobalConfig.get("sellingPriceType"));
            //response.put("auditSellerPriceCheck",GlobalConfig.get("auditSellerPriceCheck"));
            response.put("sellingPriceByCostSymbol",GlobalConfig.get("sellingPriceByCostSymbol"));
            response.put("sellingPriceByCostFloat",GlobalConfig.get("sellingPriceByCostFloat"));
            response.put("sellingPriceByListType",GlobalConfig.get("sellingPriceByListType"));
            response.put("jarvisDeliverBillISellingPriceType",GlobalConfig.get("jarvisDeliverBillISellingPriceType","1"));
            response.put("jarvisDeliverBillEnableEnoughBatchFirst",GlobalConfig.get("jarvisDeliverBillEnableEnoughBatchFirst"));
            response.put("jarvisDeliverBillAllowRefundOrderShipment",GlobalConfig.get("jarvisDeliverBillAllowRefundOrderShipment"));
            response.put("jarvisDeliverBillAllowRefundOrderShipmentWarn",GlobalConfig.get("jarvisDeliverBillAllowRefundOrderShipmentWarn"));
            response.put("jarvisDeliverBillPositivePreference",GlobalConfig.get("jarvisDeliverBillPositivePreference"));
            response.put("jarvisDeliverBillInvoiceSubmitEnable",GlobalConfig.get("jarvisDeliverBillInvoiceSubmitEnable"));
            response.put("jarvisDeliverBillInvoiceSubmitTradeStateEnable",GlobalConfig.get("jarvisDeliverBillInvoiceSubmitTradeStateEnable"));
            response.put("jarvisDeliverBillInvoiceSubmitTradeState",GlobalConfig.get("jarvisDeliverBillInvoiceSubmitTradeState"));
            response.put("jarvisDeliverBillInvoiceSubmitPostStateEnable",GlobalConfig.get("jarvisDeliverBillInvoiceSubmitPostStateEnable"));
            response.put("jarvisDeliverBillInvoiceSubmitPostState",GlobalConfig.get("jarvisDeliverBillInvoiceSubmitPostState"));
            response.put("jarvisDeliverBillInvoiceSubmitComboRowEnable",GlobalConfig.get("jarvisDeliverBillInvoiceSubmitComboRowEnable"));
            response.put("jarvisDeliverBillInvoiceSubmitComboRow",GlobalConfig.get("jarvisDeliverBillInvoiceSubmitComboRow"));
        } catch (Exception e) {
            String msg = String.format("获取单据设置配置失败：%s", e.getMessage());
            logger.error(msg,e);
        }
        return response;
    }
}