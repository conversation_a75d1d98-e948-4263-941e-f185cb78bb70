package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryLogoParameter;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.SaleOrderExceptionStatusService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: wcy
 * @Date: 2022/07/04/15:22
 * @Description:
 */
@RestController
@RequestMapping("/${app.id}/eshoporder/exceptionStatus")
public class SaleOrderExceptionStatusController {

    private SaleOrderExceptionStatusService saleOrderExceptionStatusService;

    public SaleOrderExceptionStatusController(SaleOrderExceptionStatusService saleOrderExceptionStatusService) {
        this.saleOrderExceptionStatusService = saleOrderExceptionStatusService;
    }

    @PostMapping("countExceptionStatus")
    public Map<String, Integer> countExceptionStatus(@RequestBody QueryLogoParameter param) {
//        Map<String, Integer> map = new HashMap<>();
//        map.put("网店商品未对应",10);
//        return map;
        return saleOrderExceptionStatusService.countExceptionStatus(param);
    }

    @PostMapping("countExceptionStatusForRefund")
    public Map<String, Integer> countExceptionStatusForRefund(@RequestBody QueryLogoParameter param) {
//        Map<String, Integer> map = new HashMap<>();
//        map.put("网店商品未对应",10);
//        return map;
        return saleOrderExceptionStatusService.countExceptionStatusForRefund(param);
    }


}
