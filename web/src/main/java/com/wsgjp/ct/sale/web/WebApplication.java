package com.wsgjp.ct.sale.web;

import ngp.starter.web.annotation.NgpWebEnabled;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2022/5/6 9:54
 */
@SpringBootApplication
@NgpWebEnabled
@EnableAsync
@MapperScan("com.wsgjp.ct.*.mapper")
@EnableScheduling
public class WebApplication {
    public static void main(String[] args) {
        // 入口3333
        // test11
        SpringApplication.run(WebApplication.class, args);
    }
}
