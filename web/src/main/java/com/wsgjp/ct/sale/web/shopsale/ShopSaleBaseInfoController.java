package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.baseinfo.core.dao.entity.Ktype;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.common.CustomResult;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.UnitSkuQueryDto;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.VipPwdRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.GetPtypeUnitAndBarcodeListRequestDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypeSalePriceDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypeUnitSkuPo;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.store.OtypeWithKtypeDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseOtype;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PtypePrice;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PtypeUnitPrice;
import com.wsgjp.ct.sale.biz.shopsale.service.ShopSaleBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

@Api(value = "${app.id}/shopsale/baseinfo", tags = {"基本信息"})
@RestController
@RequestMapping("${app.id}/shopsale/baseInfo")
public class ShopSaleBaseInfoController {

    @Autowired
    ShopSaleBaseInfoService shopSaleBaseInfoService;

    @ApiOperation(value = "获取职员列表")
    @PostMapping("/getetypelist")
    @WebLogs
    public PageResponse<Map> getEtypeList(@RequestBody PageRequest requestParams) {
        return shopSaleBaseInfoService.getEtypeList(requestParams);
    }

    @ApiOperation(value = "获取仓库列表")
    @PostMapping("/ktype/list")
    @WebLogs
    public PageResponse<Map> getKtypeList(@RequestBody PageRequest<Map> requestParams) {
        return shopSaleBaseInfoService.getKtypeList(requestParams);
    }

    @ApiOperation(value = "七牛文件上传")
    @PostMapping("/qiniu/upload")
    @WebLogs
    public Map qiniuUpload(MultipartFile file) {
        return shopSaleBaseInfoService.qiniuUpload(file);
    }


    @ApiOperation(value = "获取商品列表")
    @PostMapping("/getptypelist")
    public PageResponse<Map<String, Object>> getPtypeList(@RequestBody PageRequest requestParams) {
        return shopSaleBaseInfoService.getPtypeList(requestParams);
    }

    @ApiOperation(value = "获取销售机构")
    @PostMapping("/getOtypeList")
    @WebLogs
    public List<BaseOtype> getOtypeList(@RequestBody Map params) {
        return shopSaleBaseInfoService.getOtypeList(params);
    }

    @ApiOperation(value = "获取往来单位")
    @PostMapping("/getBtypeList")
    @WebLogs
    public List<BaseOtype> getBtypeList(@RequestBody Map params) {
        return shopSaleBaseInfoService.getBtypeList(params);
    }

    @ApiOperation(value = "商品单位查询")
    @PostMapping("/getPtypeRetailPriceUnit")
    Map<BigInteger, List<PtypeUnitSkuPo>> getPtypeRetailPriceUnit(@RequestBody UnitSkuQueryDto requestParams) {
        return shopSaleBaseInfoService.getPtypeRetailPriceUnit(requestParams);
    }

    @ApiOperation(value = "商品单位查询")
    @PostMapping("/getPtypeRetailPriceUnitWithPtype")
    Map<BigInteger, List<PtypeUnitSkuPo>> getPtypeRetailPriceUnitWithPtype(@RequestBody UnitSkuQueryDto requestParams) {
        return shopSaleBaseInfoService.getPtypeRetailPriceUnitWithPtype(requestParams);
    }

    @ApiOperation(value = "根据商品id和skuId获取商品unit和对应unit条码列表")
    @PostMapping("/getPtypeUnitList")
    @WebLogs
    List<PtypeUnitPrice> getPtypeUnit(@RequestBody GetPtypeUnitAndBarcodeListRequestDTO requestDTO) {
        return shopSaleBaseInfoService.getPtypeUnit(requestDTO);
    }

    @ApiOperation(value = "通过商品id(列表)查询商品信息，包含sku、unit、price、库存等所有信息")
    @PostMapping("/getGoodsInfoByIds")
    Hashtable<BigInteger, List<PtypeUnitSkuPo>> getGoodsAllInfoByPtypeIds(@RequestBody UnitSkuQueryDto requestParams) {
        return shopSaleBaseInfoService.getGoodsAllInfoByPtypeIds(requestParams);
    }

    @ApiOperation(value = "获取账号下的仓库")
    @PostMapping(value = "/getKtypeList")
    @WebLogs
    public List<Ktype> getKtypeList() {
        return shopSaleBaseInfoService.getKtypeListByProfileId();
    }


    @ApiOperation(value = "查询门店和关联的仓库")
    @PostMapping(value = "/selectOtypeWithKtypeInfo")
    @WebLogs
    public List<OtypeWithKtypeDTO> selectOtypeWithKtypeInfo() {
        return shopSaleBaseInfoService.selectOtypeWithKtypeInfo();
    }

    @ApiOperation(value = "获取会员储值密码")
    @PostMapping(value = "/getVipPassword")
    public String getVipPassword(String vipCardId) {
        return shopSaleBaseInfoService.getVipPassword(vipCardId);
    }

    @ApiOperation(value = "更新会员储值密码")
    @PostMapping(value = "/updateVipPassword")
    @WebLogs
    CustomResult updateVipPassword(@RequestBody VipPwdRequest request) {
        return shopSaleBaseInfoService.updateVipPassword(request);
    }

    @ApiOperation(value = "验证会员储值密码")
    @PostMapping(value = "/validVipPassword")
    @WebLogs
    boolean validVipPassword(@RequestBody VipPwdRequest request) {
        return shopSaleBaseInfoService.validVipPassword(request);
    }

    @ApiOperation(value = "获取店铺商品价格本价格")
    @PostMapping("/getPubBillPrice")
    @WebLogs
    public List<PtypePrice> getPubBillPrice(@RequestBody PtypeSalePriceDTO dto) {
        return shopSaleBaseInfoService.getPubBillPrice(dto);
    }


}
