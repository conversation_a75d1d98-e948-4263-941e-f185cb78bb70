package com.wsgjp.ct.sale.web.member;


import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.member.model.dto.rights.SsRightsRequest;
import com.wsgjp.ct.sale.biz.member.model.entity.rights.MemberEquityValue;
import com.wsgjp.ct.sale.biz.member.service.ISsRightsConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;

@Api(tags = "权益相关")
@RequestMapping("${app.id}/member/rights")
@RestController
public class SsRightsConfigController {

    @Autowired
    ISsRightsConfigService service;

    @ApiOperation(value = "权益配置列表")
    @PostMapping(value = "/getAllRightsConfigList")
    PageInfo<MemberEquityValue> getAllRightsConfigList(@RequestBody SsRightsRequest requsetParam) throws ParseException {
        return service.getAllRightsConfigList(requsetParam);
    }

    @ApiOperation(value = "新增一条权益配置")
    @PostMapping(value = "/insertRights")
    @PermissionCheck(key = PermissionShopSale.MEMBER_RIGHTS_ADD)
    boolean insertRights(@RequestBody MemberEquityValue requsetParam) throws ParseException {
        return service.insertRights(requsetParam);
    }

    @ApiOperation(value = "更新权益配置")
    @PostMapping(value = "/updateRights")
    @PermissionCheck(key = PermissionShopSale.MEMBER_RIGHTS_EDIT)
    Boolean updateRights(@RequestBody MemberEquityValue requsetParam) throws ParseException {
        return service.updateRights(requsetParam);
    }

    @ApiOperation(value = "停用一条或者多条权益配置")
    @PostMapping(value = "/stopRights")
    Boolean stopRights(@RequestBody SsRightsRequest requsetParam) throws ParseException {
        return service.stopRights(requsetParam);
    }

    @ApiOperation(value = "删除一条或者多条权益配置")
    @PostMapping(value = "/deleteRights")
    @PermissionCheck(key = PermissionShopSale.MEMBER_RIGHTS_DELETE)
    Boolean deleteRights(@RequestBody SsRightsRequest requsetParam) throws ParseException {
        requsetParam.setDeleted(true);
        return service.deleteRights(requsetParam);
    }
}
