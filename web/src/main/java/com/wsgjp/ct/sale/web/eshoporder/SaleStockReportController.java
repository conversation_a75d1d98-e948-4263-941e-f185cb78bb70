package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.SaleStockReportDto;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.SaleStockReportQuery;
import com.wsgjp.ct.sale.biz.eshoporder.service.stock.SaleStockReportService;
import com.wsgjp.ct.sale.biz.shopsale.common.PageSummary;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(tags = {"可销售库存报表查询接口"})
@RestController
@RequestMapping("${app.id}/eshoporder/saleStockReport")
public class SaleStockReportController {

    @Autowired
    private SaleStockReportService saleStockReportService;

    /**
     *  分页查询可销售库存报表
     * @param request
     * @return
     */
    @ApiOperation(value = "查询可销售库存报表")
    @PostMapping("/list")
    public PageResponse<SaleStockReportDto> list(@RequestBody PageRequest<SaleStockReportQuery> request) {
        try {
            PageResponse<SaleStockReportDto> result = saleStockReportService.list(request);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     *  可销售库存报表合计
     * @param request
     * @return
     */
    @ApiOperation(value = "查询可销售库存报表合计")
    @PostMapping("/list/count")
    public PageSummary listSummary(@RequestBody PageRequest<SaleStockReportQuery> request) {
        try {
            return saleStockReportService.listSummary(request);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     *  获取相关配置对应是否展示对应的库存列
     */
    @ApiOperation(value = "获取相关配置对应是否展示对应的库存列")
    @PostMapping("/configureColumns")
    public Map<String,Boolean> getConfigureColumns() {
        try {
            return saleStockReportService.getConfigureColumns();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}
