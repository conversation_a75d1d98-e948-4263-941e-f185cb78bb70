package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BaseQuery;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 22/5/2020 上午 10:23
 */
public class CloseSaleOrderParameter extends BaseQuery {

	private List<EshopSaleOrderEntity> orderList;
	private String closeReason;
	private BigInteger otypeId;

	public String getCloseReason() {
		if(closeReason==null) {
			return "";
		}
		return closeReason;
	}

	public void setCloseReason(String closeReason) {
		this.closeReason = closeReason;
	}

	public List<EshopSaleOrderEntity> getOrderList() {
		return orderList;
	}

	public void setOrderList(List<EshopSaleOrderEntity> orderList) {
		this.orderList = orderList;
	}

	public BigInteger getOtypeId() {
		return otypeId;
	}

	public void setOtypeId(BigInteger otypeId) {
		this.otypeId = otypeId;
	}
}
