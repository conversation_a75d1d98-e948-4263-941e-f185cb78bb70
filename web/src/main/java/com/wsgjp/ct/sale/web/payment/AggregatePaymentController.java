package com.wsgjp.ct.sale.web.payment;


import bf.datasource.DataSourceManager;
import com.wsgjp.ct.sale.sdk.payment.biz.AggregatePaymentService;
import com.wsgjp.ct.sale.sdk.payment.entity.request.AggregatePaymentRequest;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PaymentInfo;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PaymentResponse;
import io.swagger.annotations.Api;
import ngp.loadbalancer.context.RouteContext;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.starter.web.annotation.NotWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;

@Api(value = "${app.id}/aggregatePayment", tags = {"聚合支付"})
@RestController
@RequestMapping("${app.id}/aggregatePayment")

public class AggregatePaymentController {
    private static final Logger log = LoggerFactory.getLogger(AggregatePaymentController.class);

    private final AggregatePaymentService paymentService;

    public AggregatePaymentController(AggregatePaymentService paymentService) {
        this.paymentService = paymentService;
    }

    @GetMapping("/payResultCallback/{routeCode}")
    @NotWrapper
    public String payResultCallbackTTG(HttpServletRequest request, HttpServletResponse response, @PathVariable(
            "routeCode") String routeCode) {
        // 本地调试
//        RouteContext context = new RouteContext();
//        context.setProfileId(new BigInteger("1109914400061448193"));
//        context.setServerId("20231114");
//        context.setDeploy("master");
//        context.setEmployeeId(new BigInteger("1109914403647578112"));
//        RouteThreadLocal.setRoute(context);
//        DataSourceManager.setName(RouteThreadLocal.getRoute().getServerId());
        return paymentService.callback(request, response, routeCode);
    }

    @PostMapping("/payResultCallback/{routeCode}")
    @NotWrapper
    public String payResultCallbackHuiFu(HttpServletRequest request, HttpServletResponse response, @PathVariable(
            "routeCode") String routeCode) {
        // 本地调试
//        RouteContext context = new RouteContext();
//        context.setProfileId(new BigInteger("1109914400061448193"));
//        context.setServerId("20231114");
//        context.setDeploy("master");
//        context.setEmployeeId(new BigInteger("1109914403647578112"));
//        RouteThreadLocal.setRoute(context);
//        DataSourceManager.setName(RouteThreadLocal.getRoute().getServerId());
        return paymentService.callback(request, response, routeCode);
    }

    @PostMapping("/tqfPay")
    public String tqfPay(@RequestBody AggregatePaymentRequest request) {
        // 本地调试
        RouteContext context = new RouteContext();
        context.setProfileId(new BigInteger("1109914400061448193"));
        context.setServerId("20231114");
        context.setDeploy("feature-5.9");
        context.setEmployeeId(new BigInteger("1109914403647578112"));
        RouteThreadLocal.setRoute(context);
        DataSourceManager.setName(RouteThreadLocal.getRoute().getServerId());
//        String map = "{\n" +
//                "\t\"channelUser\": \"000000\",\n" +
//                "\t\"errCode\": \"3045\",\n" +
//                "\t\"errMsg\": \"缺少必要字段 terminal_info(UP01A5100)\",\n" +
//                "\t\"expiredTime\": \"2025-05-21 10:31:04\",\n" +
//                "\t\"mchOrderNo\": \"8888855555\",\n" +
//                "\t\"orderState\": 3,\n" +
//                "\t\"payInterfaceMchNo\": \"6602900581129QX\",\n" +
//                "\t\"payInterfaceOrderId\": \"250521118352760725\",\n" +
//                "\t\"payOrderId\": \"P1925001365039669249\",\n" +
//                "\t\"state\": 3,\n" +
//                "\t\"wayCode\": \"WX_BAR\"\n" +
//                "}";
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.enable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES); // 遇到未知字段会抛异常
//        try {
//            TqfPaymentResponse1 object = JsonUtils.toObject(map, TqfPaymentResponse1.class);
//            TqfPaymentResponse1 base = mapper.readValue(map, TqfPaymentResponse1.class);
//            System.out.println(base);
//        } catch (JsonProcessingException e) {
//            e.printStackTrace(); // 添加此行打印具体错误
//            throw new RuntimeException(e);
//        }
//        TqfBaseResponse base = JsonUtils.toObject(map, TqfBaseResponse.class);
        PaymentResponse<PaymentInfo> paymentInfoPaymentResponse = paymentService.payOrder(request);
//        paymentService.serviceCallback(new BigInteger("1747656974747170341"),  new BigInteger("1747656974747170341"),  "PXX-20250519-00107");
        return "";
    }


}
