package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductShelvesUpDownLog;
import com.wsgjp.ct.support.log.annotation.Operator;
import com.wsgjp.ct.support.log.entity.QueryParams;

import java.math.BigInteger;
import java.util.Date;

public class ShelveUpDownLogQueryParams extends QueryParams {

    @Operator(symbol = "like")
    private String etypeName;

    @Operator(symbol = "like")
    private String description;

    private Integer opreateType;
    private int createType;
    private BigInteger eshopId;
    private String platformNumId;
    private String platformSkuId;

    @Operator(symbol = ">=", field = "opreate_time")
    private Date beginTime;

    @Operator(symbol = "<=", field = "opreate_time")
    private Date endTime;

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getEtypeName() {
        return etypeName;
    }

    public void setEtypeName(String etypeName) {
        this.etypeName = etypeName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getOpreateType() {
        return opreateType;
    }

    public void setOpreateType(Integer opreateType) {
        this.opreateType = opreateType;
    }

    public int getCreateType() {
        return createType;
    }

    public void setCreateType(int createType) {
        this.createType = createType;
    }

    @Override
    public Class getLogClass() {
        return EshopProductShelvesUpDownLog.class;
    }


}
