package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.common.threads.ThreadFunc;
import com.wsgjp.ct.sale.biz.common.threads.ThreadManager;
import com.wsgjp.ct.sale.biz.common.threads.ThreadUtils;
import com.wsgjp.ct.sale.biz.common.threads.groups.BillGroup;
import com.wsgjp.ct.sale.biz.jarvis.common.redis.LockerOperationEnum;
import com.wsgjp.ct.sale.biz.jarvis.common.redis.impl.CommonRedisBizLocker;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.bus.center.BusDataCenter;
import com.wsgjp.ct.sale.bus.center.BusStarter;
import com.wsgjp.ct.sale.bus.entity.Task;
import com.wsgjp.ct.sale.bus.entity.TaskData;
import com.wsgjp.ct.sale.bus.entity.TaskResult;
import com.wsgjp.ct.sale.bus.entity.TaskType;
import com.wsgjp.ct.sale.bus.mapper.BusMapper;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@Api(description = "总线测试接口")
@RequestMapping("/${app.id}/jarvis/bus")
@ConditionalOnProperty(value = "sale.jarvis.deliver.tool.enabled-debug",havingValue = "true")
public class BusController {
    BusDataCenter busDataCenter;
    BusStarter busStarter;
    private BusMapper busMapper;

    public BusController(BusDataCenter busDataCenter, BusStarter busStarter, BusMapper busMapper) {
        this.busDataCenter = busDataCenter;
        this.busStarter = busStarter;
        this.busMapper = busMapper;
    }
    @ApiOperation(value = "推送一个消息", notes = "（）")
    @PostMapping("send")
    public void send(String busContent,TaskType taskType) {
        Task task = new Task();
        task.setBusContent(busContent);
        task.setId(UId.newId());
        task.setTaskType(taskType);
        task.setProfileId(CurrentUser.getProfileId());
        task.setExpectedTime(new Date());
        task.setIdentityLabel(String.valueOf(UId.newId()));
        busStarter.sendBusOrExecute(Arrays.asList(task),CurrentUser.getProfileId());
    }
    @ApiOperation(value = "发起一个锁定", notes = "（）")
    @PostMapping("lock")
    public void lock(String key) {
        CommonRedisBizLocker locker = new CommonRedisBizLocker(CurrentUser.getProfileId(),key, LockerOperationEnum.TOOL_BUG,120);
        locker.unLock();
    }

    @ApiOperation(value = "检查线程池分组", notes = "（）")
    @PostMapping("testThreadManager")
    public void testThreadManager(Integer size) {
        List<Integer> data = new ArrayList<>();
        List<BillDeliverDTO> Bills = new ArrayList<>();
        for (Integer i = 0; i < size; i++) {
            data.add(i);
            BillDeliverDTO billDeliverDTO = new BillDeliverDTO();
            billDeliverDTO.setWarehouseTaskId(new BigInteger(String.valueOf(i)));
            billDeliverDTO.setVchcode(new BigInteger(String.valueOf(i % 2)));
            billDeliverDTO.setDeliverDetail(new ArrayList<>());
            BillDeliverDetailDTO billDetailDeliver = new BillDeliverDetailDTO();
            billDetailDeliver.setVchcode(new BigInteger(String.valueOf(i % 4)));
            billDeliverDTO.setWarehouseTaskId(new BigInteger(String.valueOf(i)));
            billDeliverDTO.getDeliverDetail().add(billDetailDeliver);
            Bills.add(new BillDeliverDTO());
        }
        ThreadManager<Integer, Integer, Integer> threadManager = new ThreadManager<>(ThreadUtils.cancelPost);
        threadManager.execute(data, new ThreadFunc<Integer, Integer, Integer>() {
            @Override
            public List<Integer> invoke(List<Integer> data, Integer yourConfig) {
                return data;
            }
        });
        ThreadManager<BillDeliverDTO, Integer, BillDeliverDTO> threadManager2 = new ThreadManager<>(ThreadUtils.cancelPost,new BillGroup());
        threadManager2.execute(Bills, new ThreadFunc<BillDeliverDTO, Integer, BillDeliverDTO>() {
            @Override
            public List<BillDeliverDTO> invoke(List<BillDeliverDTO> data, Integer yourConfig) {
                return new ArrayList<>();
            }
        });
    }

    @ApiOperation(value = "执行测试任务", notes = "（）")
    @PostMapping("run")
    public void run(BigInteger id) {
        TaskData data = busMapper.select(CurrentUser.getProfileId(), id);
        List<TaskResult> execute = busStarter.execute(data,"test-controller");
    }

    @ApiOperation(value = "执行测试任务", notes = "（）")
    @PostMapping("invokeTraining")
    public void invokeTraining() {
        // 指定任务类型采集执行，对过期任务进行执行
        busStarter.invokeTraining(busStarter.defaultTrainingConfigs());
        //关闭超期未执行的任务
        busStarter.closeAllErrorTasks();
    }
//    @ApiOperation(value = "执行测试任务", notes = "（）")
//    @PostMapping("invokeBusiness")
//    public void invokeBusiness(){
//        Task task = new Task(Md5Utils.md5(CurrentUser.getProfileId().toString()), TaskType.CompensateAfterAudit, new Date(), "");
//        busStarter.execute(new TaskData(task));
//    }


}
