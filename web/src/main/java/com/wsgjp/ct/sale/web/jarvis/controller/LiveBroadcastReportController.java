package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.page.PageSummary;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.LiveBroadcastReportResult;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.LiveBroadcastReportQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.service.LiveBroadcastReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description TODO
 * @Date 2023-08-30 14:04
 * @Created by lingxue
 */
@RestController
@Api(description = "直播订单统计")
@RequestMapping("/${app.id}/jarvis/liveBroadcastReport")
public class LiveBroadcastReportController {
    @Autowired
    private LiveBroadcastReportService liveBroadcastReportService;

    @ApiOperation(value = "查询直播订单统计")
    @PostMapping("/query")
    public PageResponse<LiveBroadcastReportResult> query (@RequestBody PageRequest<LiveBroadcastReportQueryParams> params) {
        // 使用装饰类 替代pageHelper 保障原有的代码逻辑不会受到影响
        return liveBroadcastReportService.query(params);
    }

    @ApiOperation(value = "查询直播订单合计")
    @PostMapping("/count")
    public PageSummary count (@RequestBody PageRequest<LiveBroadcastReportQueryParams> params) {
        // 使用装饰类 替代pageHelper 保障原有的代码逻辑不会受到影响
        return liveBroadcastReportService.getSummary(params);
    }

    @ApiOperation(value = "查询直播订单明细")
    @PostMapping("/queryDetails")
    public PageResponse<BillDeliverDetailDTO> queryDetails(@RequestBody PageRequest<LiveBroadcastReportQueryParams> params) {
        // 使用装饰类 替代pageHelper 保障原有的代码逻辑不会受到影响
        return liveBroadcastReportService.queryDetails(params);
    }
}
