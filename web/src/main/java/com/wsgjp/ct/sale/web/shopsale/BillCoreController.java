package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.bill.service.ZyBillCoreService;
import com.wsgjp.ct.sale.biz.bill.utils.UserInfoUtils;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.record.dto.request.DeleteBillRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.BillCoreListRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.OperationBillResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.TdBillCoreDAO;
import com.wsgjp.ct.sale.biz.shopsale.service.OrderBillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.annontaion.NgpResource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(value = "${app.id}/shopsale/billcore", tags = "单据查询")
@RestController
@RequestMapping("${app.id}/shopsale/billcore")
public class BillCoreController {
    OrderBillService billService;

    ZyBillCoreService zyBillCoreService;

    public BillCoreController(OrderBillService billService, ZyBillCoreService zyBillCoreService) {
        this.billService = billService;
        this.zyBillCoreService = zyBillCoreService;
    }

    @ApiOperation(value = "销售出库单查询")
    @PostMapping("/list")
    @WebLogs
    public PageResponse<Map> billCoreList(@RequestBody PageRequest<BillCoreListRequest> requestParams) {
        return billService.billCoreList(requestParams);
    }

    @ApiOperation(value = "删除单据")
    @PostMapping("/check/deleteBill")
    @WebLogs
    public OperationBillResponse deleteBill(@RequestBody DeleteBillRequest requestParams) {
        return billService.deleteBill(requestParams);
    }

    @ApiOperation(value = "销售出库单和优惠辅助表")
    @PostMapping("/listAndPreferential")
    @WebLogs
    public PageResponse<Map> listAndPreferential(@RequestBody PageRequest<BillCoreListRequest> requestParams) {
        return billService.listAndPreferential(requestParams);
    }

    @ApiOperation(value = "零售单据查询")
    @PostMapping("/getSaleOrderList")
    @NgpResource(name = "shopsale.getSaleOrderList", tagStrings = "'tagA,'+0")
    public PageResponse<TdBillCoreDAO> getSaleOrderList(@RequestBody PageRequest<BillCoreListRequest> requestParams) {
        BillCoreListRequest queryParams = requestParams.getQueryParams();
        queryParams.commaSplitStringToList();
        UserInfoUtils.baseUserInfo(queryParams);
        return billService.getSaleOrderList(requestParams);
    }

    @ApiOperation(value = "未支付订单查询")
    @PostMapping("/getNoPayOrderList")
    public PageResponse getNoPayOrderList(@RequestBody PageRequest<BillCoreListRequest> requestParams) {
        BillCoreListRequest queryParams = requestParams.getQueryParams();
        queryParams.commaSplitStringToList();
        UserInfoUtils.zyUserInfo(queryParams);
        return zyBillCoreService.getNoPayOrderList(requestParams);
    }


}
