package com.wsgjp.ct.sale.web.eshoporder;

import com.alibaba.fastjson.JSONObject;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.CustomerQuery;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.CustomerQueryDTO;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundQuickFilterTypeVO;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.CustomerQueryService;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverPageQueryConfig;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "原始订单相关")
@RequestMapping("${app.id}/eshoporder/customerQuery")
@RestController
public class EshopOrderCustomerQueryController {

    private final CustomerQueryService customerQueryService;
    private static final Logger logger = LoggerFactory.getLogger(EshopOrderCustomerQueryController.class);

    public EshopOrderCustomerQueryController(CustomerQueryService customerQueryService) {
        this.customerQueryService = customerQueryService;
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    public BigInteger add(@RequestBody CustomerRequest customerRequest) {
        return this.customerQueryService.add(customerRequest.getTitle(), customerRequest.getQueryCode(), CurrentUser.getEmployeeId(), customerRequest.getQueryParams());
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/addForRefund")
    public BigInteger addForRefund(@RequestBody CustomerRequest customerRequest) {
        return this.customerQueryService.addForRefund(customerRequest.getTitle(), customerRequest.getQueryCode(), CurrentUser.getEmployeeId(), customerRequest.getQueryParams(), customerRequest.getId());
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping("/update")
    public String update(@RequestBody CustomerRequest customerRequest) {
        String ret = "";
        try {
//            logger.info(JSONObject.toJSONString(customerRequest));
            this.customerQueryService.update(CurrentUser.getProfileId(), customerRequest.getTitle(), customerRequest.getQueryCode(),
                    customerRequest.getQueryId(), customerRequest.getQueryParams());
            return "";
        } catch (RuntimeException ex) {
            String errorStr = String.format("修改失败，%s", ex.getMessage());
            logger.error(errorStr);
            ret = errorStr;
        }
        return ret;
    }

    @ApiOperation(value = "修改", notes = "修改")
    @PostMapping("/updateForRefund")
    public String updateForRefund(@RequestBody List<CustomerQuery> list) {
        String ret = "";
        try {
            this.customerQueryService.updateForRefund(list);
            return "";
        } catch (RuntimeException ex) {
            String errorStr = String.format("修改失败，%s", ex.getMessage());
            logger.error(errorStr);
            ret = errorStr;
        }
        return ret;
    }

    @ApiOperation(value = "修改名称", notes = "修改名称")
    @PostMapping("/modifyTitle")
    public void modifyTitle(BigInteger queryId, String title) {
        this.customerQueryService.modifyTitle(CurrentUser.getProfileId(), title, queryId);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @GetMapping("/delete/{id}")
    public void delete(@PathVariable("id") BigInteger id) {
        this.customerQueryService.delete(CurrentUser.getProfileId(), id);
    }

    @PostMapping("/getList")
    public List<CustomerQueryDTO> getCustomQuery(@RequestBody String queryCode) {
        return customerQueryService.list(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), queryCode);
    }

    @PostMapping("/getListForRefund")
    public List<CustomerQuery> getListForRefund(@RequestBody String queryCode) {
        return customerQueryService.getListForRefund(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), queryCode);
    }

    @PostMapping(value = "/getRefundQuickFilterType")
    public List<RefundQuickFilterTypeVO> getRefundQuickFilterType(@RequestBody String queryCode) {
        return customerQueryService.getRefundQuickFilterType(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), queryCode);
    }

    @ApiOperation(value = "保存或更新配置", notes = "保存或更新配置")
    @PostMapping("/saveQueryConfig")
    public void saveQueryConfig(@RequestBody CustomerRequest customerRequest) {
        this.customerQueryService.saveQueryConfig(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), customerRequest.getQueryCode(),
                customerRequest.getQueryId(), customerRequest.getConfigJson());
    }

    @ApiOperation(value = "查询页面配置", notes = "查询页面配置")
    @GetMapping("/queryPageConfigByPageName/{pageName}")
    public DeliverPageQueryConfig queryPageConfigByPageName(@PathVariable String pageName) {
        if (StringUtils.isEmpty(pageName)) {
            return null;
        }
        return this.customerQueryService.queryPageConfigByPageName(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), pageName);
    }

    public static class CustomerRequest implements Serializable {
        private BigInteger id;
        private BigInteger queryId;
        private String title;
        private String queryCode;
        private Map<String, Object> queryParams;
        private String configJson;

        public BigInteger getId() {
            return id;
        }

        public void setId(BigInteger id) {
            this.id = id;
        }

        public String getConfigJson() {
            return configJson;
        }

        public void setConfigJson(String configJson) {
            this.configJson = configJson;
        }

        public BigInteger getQueryId() {
            return queryId;
        }

        public void setQueryId(BigInteger queryId) {
            this.queryId = queryId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getQueryCode() {
            return queryCode;
        }

        public void setQueryCode(String queryCode) {
            this.queryCode = queryCode;
        }

        public Map<String, Object> getQueryParams() {
            return queryParams;
        }

        public void setQueryParams(Map<String, Object> queryParams) {
            this.queryParams = queryParams;
        }
    }
}

