package com.wsgjp.ct.sale.web.member;

import com.wsgjp.ct.sale.biz.member.model.entity.qrCode.SsVipQrCode;
import com.wsgjp.ct.sale.biz.member.service.ISsVipQrCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "${app.id}/member/qrCode", tags = {"会员注册二维码接口"})
@RestController
@RequestMapping("${app.id}/member/qrCode")
public class SsVipQrCodeController {

    @Autowired
    private ISsVipQrCodeService ssVipQrCodeService;


    @ApiOperation(value = "获取注册会员二维码")
    @PostMapping("/getRegisterVipQrCode")
    public SsVipQrCode getRegisterVipQrCode() {
        return ssVipQrCodeService.getRegisterVipQrCode();
    }
}
