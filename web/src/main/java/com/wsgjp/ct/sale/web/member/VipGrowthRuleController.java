package com.wsgjp.ct.sale.web.member;


import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.member.common.CustomResult;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.VipGrowthRule;
import com.wsgjp.ct.sale.biz.member.service.IVipGrowthRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "成长值规则")
@RestController
@RequestMapping("${app.id}/member/vipGrowthRule")
public class VipGrowthRuleController {

    @Autowired
    private IVipGrowthRuleService vipGrowthRuleService;

    @ApiOperation("获取成长值规则")
    @GetMapping("/getVipGrowthRule")
    public VipGrowthRule getVipGrowthRule() {
        return vipGrowthRuleService.getVipGrowthRule();
    }

    @ApiOperation("更新成长值规则")
    @PostMapping("/saveOrUpdata")
    @PermissionCheck(key = PermissionShopSale.MEMBER_LEVEL_RULE)
    public CustomResult saveOrUpdata(@RequestBody VipGrowthRule vipGrowthRule) {
        return vipGrowthRuleService.saveOrUpdate(vipGrowthRule);
    }

}
