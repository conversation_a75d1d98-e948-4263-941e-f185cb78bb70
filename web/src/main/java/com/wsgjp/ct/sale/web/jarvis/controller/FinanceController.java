package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.annotation.PageFilterMap;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.QueryMap;
import com.wsgjp.ct.sale.biz.jarvis.finance.dto.percentage.PercentageCardinalCalcRequest;
import com.wsgjp.ct.sale.biz.jarvis.finance.dto.percentage.PercentageDetailInfo;
import com.wsgjp.ct.sale.biz.jarvis.finance.dto.percentage.PercentageDetailQueryParam;
import com.wsgjp.ct.sale.biz.jarvis.service.finance.FinanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(description = "销售提成相关接口")
@RequestMapping("/${app.id}/jarvis/finance")
public class FinanceController {
    @Autowired
    private FinanceService financeService;

    @ApiOperation(value = "计算提成明细", notes = "（）")
    @PostMapping("calculate")
    public void calculate(@RequestBody PercentageCardinalCalcRequest req) {
        financeService.calculate(req);
    }

    @ApiOperation(value = "获取提成明细", notes = "（）")
    @PostMapping("list")
    @PageFilterMap(filterMap = QueryMap.PercentageQueryMap.class)
    public PageResponse<PercentageDetailInfo> list(@RequestBody PageRequest<PercentageDetailQueryParam> req) {
        return financeService.listPercentage(req);
    }

}
