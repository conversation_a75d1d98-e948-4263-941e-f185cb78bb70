package com.wsgjp.ct.sale.web.eshoporder.entity;

import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderSysDataConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EnumState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.SaleOrderPriceControl;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EnumStateService;
import com.wsgjp.ct.sale.web.eshoporder.entity.enums.PageMode;
import com.wsgjp.ct.sale.web.eshoporder.entity.enums.SaleOrderEditFormCloseType;
import com.wsgjp.ct.support.global.GlobalConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
public class EshopAdvanceSaleOrderInitResponse extends CommonInitResponse {

  public EshopAdvanceSaleOrderInitResponse(PageMode mode) {
    EshopOrderSysDataConfig sysConfig = GlobalConfig.get(EshopOrderSysDataConfig.class);
    int code = sysConfig.getEditFormCloseType();
    closeType = SaleOrderEditFormCloseType.values()[code];
    this.mode = mode;
  }


  private EshopAdvanceOrderEntity advanceOrderEntity;
  private String title;
  private PageMode mode;
  private SaleOrderEditFormCloseType closeType;
  private SaleOrderPriceControl priceControl;

  public SaleOrderPriceControl getPriceControl() {
    return priceControl;
  }

  public void setPriceControl(SaleOrderPriceControl priceControl) {
    this.priceControl = priceControl;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public PageMode getMode() {
    return mode;
  }

  private List<EnumState> deliverTypeList;

  public List<EnumState> getDeliverTypeList() {
    return EnumStateService.getEnumState("deliverTypeSource");
  }



  public void setAdvanceOrderEntity(EshopAdvanceOrderEntity advanceOrderEntity) {
    this.advanceOrderEntity = advanceOrderEntity;
  }

  public EshopAdvanceOrderEntity getAdvanceOrderEntity() {
    return advanceOrderEntity;
  }

  public SaleOrderEditFormCloseType getCloseType() {
    return closeType;
  }

  public void setCloseType(SaleOrderEditFormCloseType closeType) {
    this.closeType = closeType;
  }
}
