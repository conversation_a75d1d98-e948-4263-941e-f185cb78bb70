package com.wsgjp.ct.sale.web.eshoporder.entity.enums;

import bf.datasource.typehandler.CodeEnum;

/**
 * <AUTHOR>
 * @date 2020/2/12 0012 16:57
 */
public enum PageMode implements CodeEnum {
    /**
     * 新增界面
     */
    NEW(0,"新增"),
    MODIFY(1,"编辑"),
    COPY_CREATE(2,"复制新增"),
    COPY_CREATE_FROM_WAREHOUSE(3,"复制新增(by task)"),
    ;

    private int index;
    private String name;

    PageMode(int index, String name) {
        this.index=index;
        this.name=name;
    }

    @Override
    public String toString(){
        return name;
    }

    @Override
    public int getCode() {
        return index;
    }


}
