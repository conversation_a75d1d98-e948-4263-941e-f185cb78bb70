package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.bill.mapper.DtypeMapper;
import com.wsgjp.ct.sale.biz.bill.mapper.EtypeBillMapper;
import com.wsgjp.ct.sale.biz.bill.model.dao.BaseDtypeEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodNew;
import com.wsgjp.ct.sale.biz.eshoporder.service.sales.saletask.SaleTaskNewService;
import com.wsgjp.ct.sale.biz.jarvis.config.DefinedAsyncTaskProcess;
import com.wsgjp.ct.sale.biz.jarvis.config.TaskNumberConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.definedTask.DefinedAsyncQueryTypeEnum;
import com.wsgjp.ct.sale.biz.jarvis.entity.definedTask.ResponseAsyncTaskExecuteState;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.QueryTask;
import com.wsgjp.ct.sale.biz.jarvis.service.pageHelper.HelperDefinedSyncTaskComponentServiceImpl;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import io.swagger.annotations.Api;
import ngp.idgenerator.UId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "销售目标接口")
@RequestMapping("${app.id}/eshoporder/tasknew")
@RestController
public class TaskNewController {

    private EtypeBillMapper baseEtypeMapper;
    private DtypeMapper dtypeMapper;

    private final Logger logger = LoggerFactory.getLogger(TaskNewController.class);

    private SaleTaskNewService saleTaskNewService;

    @Autowired
    private HelperDefinedSyncTaskComponentServiceImpl HelperDefinedSyncTaskComponentService;

    public TaskNewController(EtypeBillMapper baseEtypeMapper, DtypeMapper dtypeMapper, SaleTaskNewService saleTaskNewService) {
        this.baseEtypeMapper = baseEtypeMapper;
        this.dtypeMapper = dtypeMapper;
        this.saleTaskNewService = saleTaskNewService;
    }

    @PostMapping(value = "/listSaleTaskNew")
    public PageResponse<SaleTaskNewDTO> listSaleTaskNew(@RequestBody PageRequest<QuerySaleTaskNewParam> param) {
        return saleTaskNewService.querySaleTask(param);
    }


    @GetMapping(value = "/uid/{count}")
    public List<BigInteger> listPlSalePeriodList(@PathVariable int count) {
        List<BigInteger> ids = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            ids.add(UId.newId());
        }
        Collections.sort(ids);
        return ids;
    }

    @GetMapping(value = "/listPlSalePeriodList/{groupName}")
    public List<PlSalePeriodNew> listPlSalePeriodList(@PathVariable String groupName) {
        return saleTaskNewService.listPlSalePeriod(groupName, CurrentUser.getProfileId());
    }

    @PostMapping(value = "/open")
    public void open(@RequestBody List<BigInteger> taskIds) {
        // saleTaskNewService.modifyTaskStatus(taskIds, SaleTaskNewStatusEnum.DOING_STATUS);
        saleTaskNewService.openTask(taskIds, 0, null, null);
    }

    @PostMapping(value = "/close")
    public void close(@RequestBody List<BigInteger> taskIds) {
        saleTaskNewService.modifyTaskStatus(taskIds, SaleTaskStatusNewEnum.STOP_STATUS);
    }

    @PostMapping(value = "/delete")
    public void delete(@RequestBody List<BigInteger> taskIds) {
        saleTaskNewService.deleteTask(taskIds);
    }

    @PostMapping(value = "/save")
    public void save(@RequestBody SaleTaskNewDTO saleTask) {
        saleTaskNewService.saveTask(saleTask);
    }


//    @PostMapping(value = "/getExecStatusTopInfo")
//    public SaleTaskExecStatusTopInfo getExecStatusTopInfo(@RequestBody BigInteger taskId) {
//        SaleTaskExecStatusTopInfo info = saleTaskNewService.getExecStatusTopInfo(taskId);
//        return info;
//    }

    @PostMapping("/getExecStatusTopInfo2")
    @DefinedAsyncTaskProcess(threadPoolName = DefinedAsyncTaskProcess.ThreadPoolNames.EShopSaleStatistic, queryType = DefinedAsyncQueryTypeEnum.EShopSaleStatistic, loadClass = SaleTaskNewService.class)
    public ResponseAsyncTaskExecuteState getEShopSaleStatistics(@RequestBody QueryTask queryTask) {
        return saleTaskNewService.getExecStatusTopInfo(queryTask, HelperDefinedSyncTaskComponentService);
    }


//    @PostMapping(value = "/getOpenType")
//    public String getOpenType(@RequestBody BigInteger taskId) {
//        String info = saleTaskNewService.getOpenType(taskId);
//        return info;
//    }

    @PostMapping(value = "/listSaleTaskNewReport")
    public PageResponse<QuerySaleTaskReportResult> listSaleTaskNewReport(@RequestBody PageRequest<QuerySaleTaskReportParam> param) {
        buildQuery(param);
        return saleTaskNewService.listSaleTaskNewReport(param);
    }

    private void buildQuery(@RequestBody PageRequest<QuerySaleTaskReportParam> request) {
        QuerySaleTaskReportParam param = request.getQueryParams();
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        if (PermissionValiateService.isAdmin()) {
            return;
        }

        List<BaseDtypeEntity> dtypeList = dtypeMapper.getDtypeByEtypeId(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
        // 不是部门负责人
        if (dtypeList == null || dtypeList.isEmpty()) {
            param.setPowerDtypeIds(Collections.singletonList(new BigInteger("-1")));
            param.setPowerEtypeIds(Collections.singletonList(CurrentUser.getEmployeeId()));
        } else {
            List<BaseDtypeEntity> allDtypes = new ArrayList<>();
            for (BaseDtypeEntity baseDtypeEntity : dtypeList) {
                List<BaseDtypeEntity> childDtypes = dtypeMapper.getDtypeByTypeId(CurrentUser.getProfileId(), baseDtypeEntity.getTypeid());
                if (childDtypes != null && !childDtypes.isEmpty()) {
                    allDtypes.addAll(childDtypes);
                }
            }
            List<BigInteger> dtypeIds = allDtypes.stream().map(BaseDtypeEntity::getId).distinct().collect(Collectors.toList());
            param.setPowerDtypeIds(dtypeIds);

            if (!dtypeIds.isEmpty()) {
                List<Etype> etypes = dtypeMapper.getEtypeByDtypeId(CurrentUser.getProfileId(), dtypeIds);
                if (etypes == null || etypes.isEmpty()) {
                    param.setPowerEtypeIds(Collections.singletonList(CurrentUser.getEmployeeId()));
                } else {
                    param.setPowerEtypeIds(etypes.stream().map(Etype::getId).distinct().collect(Collectors.toList()));
                }
                // 还需要往职员中添加部门，后面去过滤目标表中的task_group_value时候，需要同时使用部门和经手人过滤
                param.getPowerEtypeIds().addAll(dtypeIds);
            }
        }
    }

    @PostMapping("/getTaskNumberConfig")
    public TaskNumberConfig getTaskNumberConfig() {
        TaskNumberConfig response = new TaskNumberConfig();
        try {
//            response.setLastDate(GlobalConfig.get(TaskNumberConfig.class).getLastDate());
            response.setLastNumber(GlobalConfig.get(TaskNumberConfig.class).getLastNumber());
//            SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
//            String lastDate = DATE_FORMAT.format(response.getLastDate());
//            Date nowDate = new Date();
//            String currentDate = DATE_FORMAT.format(nowDate);
//            if (lastDate.equals(currentDate)) {
            int lastNumber = response.getLastNumber() + 1;
            String number = String.format("%06d", lastNumber);
            response.setCurrentNumber(number);
            response.setLastNumber(lastNumber);
//            } else {
//                int lastNumber = 1;
//                String number = "XSMB-" + currentDate + "-" + String.format("%03d", lastNumber);
//                response.setCurrentNumber(number);
//                response.setLastNumber(lastNumber);
//                response.setLastDate(nowDate);
//            }
        } catch (Exception e) {
            String msg = String.format("查询销售目标管理设置失败：%s", e.getMessage());
            logger.error(msg, e);
        }
        return response;
    }
}
