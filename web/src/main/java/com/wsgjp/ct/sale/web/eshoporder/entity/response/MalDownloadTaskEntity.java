package com.wsgjp.ct.sale.web.eshoporder.entity.response;

import java.math.BigInteger;

public class MalDownloadTaskEntity {
    private String taskId;
    private BigInteger otypeId;
    private String otypeName;
    private Boolean completed;
    private Boolean success;
    private String message;
    private String allMessage;
    private Integer progress;
    public MalDownloadTaskEntity()
    {
        this.taskId = "";
        this.otypeId = BigInteger.ZERO;
        this.otypeName="";
        this.completed =false;
        this.success = true;
        this.message ="";
        this.allMessage= "";
        this.progress =0;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getOtypeName() {
        return otypeName;
    }

    public void setOtypeName(String otypeName) {
        this.otypeName = otypeName;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getAllMessage() {
        return allMessage;
    }

    public void setAllMessage(String allMessage) {
        this.allMessage = allMessage;
    }

    public Boolean getCompleted() {
        return completed;
    }

    public void setCompleted(Boolean completed) {
        this.completed = completed;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }
}
