package com.wsgjp.ct.sale.web.member;

import com.wsgjp.ct.sale.biz.member.model.vo.vip.VipDownLoadDTO;
import com.wsgjp.ct.sale.biz.member.service.ISsVipDownloadService;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "会员下载")
@RestController
@RequestMapping("${app.id}/member/vipDownload")
public class VipDownLoadController {

    @Autowired
    private ISsVipDownloadService vipDownloadService;

    @ApiOperation(value = "获取网店列表")
    @PostMapping("/getShops")
    public List<EshopInfo> getShops(@RequestBody VipDownLoadDTO dto) {
        return vipDownloadService.getShops(dto);
    }

    @ApiOperation(value = "下载会员")
    @PostMapping("/downloadVip")
    public String downloadVip(@RequestBody VipDownLoadDTO dto) {
        return vipDownloadService.downLoadVip(dto);
    }
}
