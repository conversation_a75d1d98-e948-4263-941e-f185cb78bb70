package com.wsgjp.ct.sale.web.jarvis.response;

import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.dto.gift.DeliverGiftRuleGroupDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;
import com.wsgjp.ct.sale.biz.jarvis.state.GiftRuleStatusEnum;

import java.util.List;

/**
 * <AUTHOR>
 */
public class GiftRuleListResponse {
    private List<DillDeliverState> giveType;
    private List<DillDeliverState> stockType;
    private List<DillDeliverState> statusType;
    private List<DillDeliverState> amountType;
    private List<DillDeliverState> giveWayDetailType;
    private List<DillDeliverState> giveWayType;
    private String start;
    private String end;
    private GiftRuleStatusEnum status;
    private List<DillDeliverState> giveContentType;
    private List<DillDeliverState> sectionType;
    private List<DillDeliverState> ruleTypes;
    private List<DillDeliverState> repeatTypes;
    private List<DeliverGiftRuleGroupDTO> groups;
    private List<Organization> otypeList;

    public List<DillDeliverState> getRepeatTypes() {
        return repeatTypes;
    }

    public void setRepeatTypes(List<DillDeliverState> repeatTypes) {
        this.repeatTypes = repeatTypes;
    }

    public List<DillDeliverState> getGiveType() {
        return giveType;
    }

    public void setGiveType(List<DillDeliverState> giveType) {
        this.giveType = giveType;
    }

    public List<DillDeliverState> getStockType() {
        return stockType;
    }

    public void setStockType(List<DillDeliverState> stockType) {
        this.stockType = stockType;
    }



    public List<DillDeliverState> getAmountType() {
        return amountType;
    }

    public void setAmountType(List<DillDeliverState> amountType) {
        this.amountType = amountType;
    }

    public List<DillDeliverState> getGiveWayDetailType() {
        return giveWayDetailType;
    }

    public void setGiveWayDetailType(List<DillDeliverState> giveWayDetailType) {
        this.giveWayDetailType = giveWayDetailType;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public List<DillDeliverState> getGiveWayType() {
        return giveWayType;
    }

    public void setGiveWayType(List<DillDeliverState> giveWayType) {
        this.giveWayType = giveWayType;
    }

    public List<DillDeliverState> getStatusType() {
        return statusType;
    }

    public void setStatusType(List<DillDeliverState> statusType) {
        this.statusType = statusType;
    }

    public GiftRuleStatusEnum getStatus() {
        return status;
    }

    public void setStatus(GiftRuleStatusEnum status) {
        this.status = status;
    }

    public void setGiveContentType(List<DillDeliverState> giveContentType) {
        this.giveContentType = giveContentType;
    }

    public List<DillDeliverState> getGiveContentType() {
        return giveContentType;
    }

    public void setSectionType(List<DillDeliverState> sectionType) {
        this.sectionType = sectionType;
    }

    public List<DillDeliverState> getSectionType() {
        return sectionType;
    }

    public void setRuleTypes(List<DillDeliverState> ruleTypes) {
        this.ruleTypes = ruleTypes;
    }

    public List<DillDeliverState> getRuleTypes() {
        return ruleTypes;
    }

    public List<DeliverGiftRuleGroupDTO> getGroups() {
        return groups;
    }

    public void setGroups(List<DeliverGiftRuleGroupDTO> groups) {
        this.groups = groups;
    }

    public List<Organization> getOtypeList() {
        return otypeList;
    }

    public void setOtypeList(List<Organization> otypeList) {
        this.otypeList = otypeList;
    }
}
