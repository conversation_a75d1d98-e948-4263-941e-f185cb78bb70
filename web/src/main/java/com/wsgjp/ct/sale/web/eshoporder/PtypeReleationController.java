package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.base.Stopwatch;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.api.enums.WebToToolApiEnum;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.QuerySupplierProductRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.ComboDetailPo;
import com.wsgjp.ct.sale.biz.eshoporder.api.totool.WebToToolApiFactory;
import com.wsgjp.ct.sale.biz.eshoporder.api.totool.packing.Pair;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderToolConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EnumState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Prop;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.PropValueName;
import com.wsgjp.ct.sale.biz.eshoporder.entity.carpa.TreeNodeData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.Pcategory;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProductOperateLogType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.TotalInfoList;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.RefreshPtypeCategoryParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.RefreshPtypeParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.UpdatePtypeParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.AddEshopProductResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ComboSaveResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.EShopPageInfo;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductSyncConditionMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.CustomerQueryService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.*;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.jarvis.config.AsyncProcess;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.BatchProcessRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Stock;
import com.wsgjp.ct.sale.common.config.EshopOrderCommonConfig;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.enums.StockState;
import com.wsgjp.ct.sale.platform.feature.product.EshopProductSellerClassFeature;
import com.wsgjp.ct.sale.platform.utils.Common;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.EshopMatchRolesSaveRequest;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.ShelveUpDownLogQueryParams;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import jodd.io.FileUtil;
import ngp.idgenerator.UId;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.CollectionUtils;
import ngp.utils.Md5Utils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Created by kangyu on 2020-02-18.
 */
@Api(tags = "商品对应相关")
@RequestMapping("${app.id}/eshoporder/relation")
@RestController
public class PtypeReleationController {
    private final Logger sysLogger = LoggerFactory.getLogger(PtypeReleationController.class);
    private final EshopProductService productService;
    private final EshopProductHandleService productLocalHandleService;
    private final EshopProductClassService classService;
    private final EshopMatchRoleService matchRoleService;
    private final EshopProductMarkService markService;
    private final EshopProductMapper productMapper;
    private final EshopProductMappingLogService mappingLogService;
    private final CustomerQueryService queryService;
    private final EshopPtypeChaneNotifyService ptypeChaneNotifyService;
    private final EshopProductSyncConditionMapper eshopProductSyncConditionMapper;
    private final EshopOrderCommonConfig eshopOrderCommonConfig;
    private final EshopService eshopService;
    private final EshopOrderToolConfig eshopOrderToolConfig;
    private final ServiceConfig serviceConfig;

    private final EshopProductNotifyService notifyService;

    public PtypeReleationController(ServiceConfig serviceConfig, @Lazy EshopProductService productService, EshopProductHandleService productLocalHandleService, EshopProductClassService classService, EshopMatchRoleService matchRoleService, EshopProductMarkService markService, EshopProductMapper productMapper, EshopProductMappingLogService mappingLogService, CustomerQueryService queryService, EshopPtypeChaneNotifyService ptypeChaneNotifyService, EshopProductSyncConditionMapper eshopProductSyncConditionMapper, EshopOrderCommonConfig eshopOrderCommonConfig, EshopService eshopService, EshopOrderToolConfig eshopOrderToolConfig, EshopProductNotifyService notifyService) {
        this.productService = productService;
        this.serviceConfig = serviceConfig;
        this.productLocalHandleService = productLocalHandleService;
        this.classService = classService;
        this.matchRoleService = matchRoleService;
        this.markService = markService;
        this.productMapper = productMapper;
        this.mappingLogService = mappingLogService;
        this.queryService = queryService;
        this.ptypeChaneNotifyService = ptypeChaneNotifyService;
        this.eshopProductSyncConditionMapper = eshopProductSyncConditionMapper;
        this.eshopOrderCommonConfig = eshopOrderCommonConfig;
        this.eshopService = eshopService;
        this.eshopOrderToolConfig = eshopOrderToolConfig;
        this.notifyService = notifyService;
    }

    @RequestMapping(value = "/changePtypeMark", method = RequestMethod.POST)
    public void changePtypeMark(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        productLocalHandleService.modifyProductMark(params.getQueryParams());
    }

    /**
     * 商品对应gird，获取商品对应列表
     *
     * @param params
     * @return
     */
    @PageDataSource
    @RequestMapping(value = "/queryEshopRelations", method = RequestMethod.POST)
    public PageResponse<EshopProductRelationEntity> queryListOrders(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        return productLocalHandleService.queryEshopProductMapping(params, "query");
    }

    @GetMapping(value = "/getComboDetail")
    public List<ComboDetailPo> getComboDetail(BigInteger ptypeId) {
        return productLocalHandleService.getComboDetail(ptypeId);
    }

    /**
     * 商品对应gird，获取商品对应列表
     *
     * @param params
     * @return
     */
    @PageDataSource
    @RequestMapping(value = "/queryEshopRelationsMain", method = RequestMethod.POST)
    public PageResponse<EshopProductRelationEntity> queryEshopRelationsMain(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        return productLocalHandleService.queryEshopProductMappingMainInfo(params, "query");
    }

    @RequestMapping(value = "/getMd5Str", method = RequestMethod.POST)
    public String getMd5Str(@RequestBody String str) {
        return Md5Utils.md5(str);
    }

    /**
     * 商品对应gird，获取商品对应列表
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/queryPartialRelations", method = RequestMethod.POST)
    public List<EshopProductRelationEntity> queryPartialList(@RequestBody QueryEshopProductMappingRequest params) {
        List<EshopProductRelationEntity> parList = productLocalHandleService.queryEshopProductMappingRoot(params);
        if (parList == null || parList.size() == 0) {
            return new ArrayList();
        } else {
            List<EshopProductRelationEntity> listSku = productLocalHandleService.queryEshopProductMappingDetailByRoots(parList, params, "query", false);
            List<EshopProductRelationEntity> filterdParlist = listSku.stream().filter(x -> x.isRoot()).collect(Collectors.toList());
            List<BigInteger> parIdList = filterdParlist.stream().filter(x -> x.isRoot()).map(x -> x.getId()).collect(Collectors.toList());
            listSku = listSku.stream().filter(x -> (x.isRoot() && parIdList.contains(x.getId()) || (parIdList.contains(x.getPid())))).collect(Collectors.toList());
            return listSku;
        }
    }


    /**
     * 查询单个商品
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/queryEshopRelationsSingle", method = RequestMethod.POST)
    public List<EshopProductRelationEntity> queryEshopRelationsSingle(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        PageResponse<EshopProductRelationEntity> query = productLocalHandleService.queryEshopProductMappingSingle(params, "query");
        return query == null ? new ArrayList<EshopProductRelationEntity>() : query.getList();
    }

    /**
     * 手工对应批量按商家编码对应
     *
     * @param params 网店商品信息
     * @return taskId
     */
    @RequestMapping(value = "/batchRelationByXcode", method = RequestMethod.POST)
    public String batchRelationByXcode(@RequestBody BatchProcessRequest<EshopProductRelationEntity> params) {
        RedisProcessMessage processMessage = new RedisProcessMessage(params.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        productLocalHandleService.eshopProductRelationByXcode(params.getList(), messageLog, params, processMessage);
        return params.getProcessId();
    }

    /**
     * 手工对应批量按条码编码对应sale/eshoporder/relation/batchRelationByXcode
     *
     * @param params 网店商品信息
     * @return taskId
     */
//    @NgpResource(name = "pl.bs.eshoporder.page.open", tagStrings = "method,batchRelationByBarcode")
    @RequestMapping(value = "/batchRelationByBarcode", method = RequestMethod.POST)
    public String batchRelationByBarcode(@RequestBody List<EshopProductRelationEntity> params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        productLocalHandleService.batchRelationByBarcode(params, processLogger);
        return taskId;
    }

    /**
     * 网店商品打标，获取商品标记列表
     *
     * @param params
     * @return
     */
    @PageDataSource
    @RequestMapping(value = "/queryProductMarkList", method = RequestMethod.POST)
    public PageResponse<EshopProductMarkPageEntity> queryProductMarkList(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        PageResponse<EshopProductMarkPageEntity> resp = markService.queryProductMarkListPage(params);
        List<BigInteger> parIdList = resp.getList().stream().filter(x -> x.isRoot()).map(x -> x.getId()).collect(Collectors.toList());
        List<EshopProductMarkPageEntity> listSku = resp.getList().stream().filter(x -> (x.isRoot() && parIdList.contains(x.getId()) || (parIdList.contains(x.getPid())))).collect(Collectors.toList());
        resp.setList(listSku);
        return resp;
    }

    /**
     * 网店商品打标，获取商品标记列表查询单个sku
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/queryEshopMarkSingle", method = RequestMethod.POST)
    public List<EshopProductMarkPageEntity> queryEshopMarkSingle(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        List<EshopProductMarkPageEntity> resp = markService.queryProductMarkList(params);
        List<BigInteger> parIdList = resp.stream().filter(x -> x.isRoot()).map(x -> x.getId()).collect(Collectors.toList());
        List<EshopProductMarkPageEntity> listSku = resp.stream().filter(x -> (x.isRoot() && parIdList.contains(x.getId()) || (parIdList.contains(x.getPid())))).collect(Collectors.toList());
        return listSku;
    }

    @RequestMapping(value = "/recordPubSystemLog", method = RequestMethod.POST)
    public void recordPubSystemLog(String body) {
        productService.recordPubSystemLog(body);
    }

    /**
     * 网店商品下载查询
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/queryPtypeDownLoadList")
    public PageResponse<EshopProductRelationEntity> queryPtypeDownLoadList(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        return PageDevice.readPage(productLocalHandleService.queryPtypeDownLoadList(params));
    }

    /**
     * 网店商品上下架查询
     */
    @PageDataSource
    @PostMapping(value = "/queryLoaingdAndUnloading")
    public PageResponse<EshopProductMapping> queryLoaingdAndUnloading(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        return PageDevice.readPage(productLocalHandleService.queryLoaingdAndUnloading(params));
    }

    /**
     * 设置网店商品上下架规则
     */
    @PostMapping(value = "/updateShelfOnOffAndSyncCount")
    public String updateShelfOnOffAndSyncCount(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        return productLocalHandleService.updateShelfOnRules(params);
    }

    /**
     * 网店商品上下架partialRefresh
     */
    @PostMapping(value = "/partialqueryLoaingdAndUnloading")
    public List<EshopProductMapping> partialqueryLoaingdAndUnloading(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        return productLocalHandleService.queryLoaingdAndUnloading(params);
    }

    /**
     * 执行网店商品上下架
     *
     * @return
     */
    @PageDataSource
    @PostMapping(value = "/excuteLoadingAndUploading")
    public String excuteLoadingAndUploading(@RequestBody PageRequest<EshopProductShelf> params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        EshopProductShelf queryParams = params.getQueryParams();
        productLocalHandleService.excuteLoadingAndUploadingThread(processLogger, queryParams);
        return taskId;
    }

    @PostMapping(value = "/queryShelveUpDownLog")
    public PageResponse<EshopProductShelvesUpDownLog> queryOtypeLog(@RequestBody PageRequest<ShelveUpDownLogQueryParams> request) {
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        PageResponse<EshopProductShelvesUpDownLog> query = LogService.query(request);
        if (query.getList() == null) {
            query.setList(new ArrayList<>());
        }
        return query;
    }

    /**
     * eshopInfo查询
     *
     * @param eshopId
     * @return
     */
    @PostMapping(value = "/queryEshopInfo")
    public EshopInfo queryEshopInfo(BigInteger eshopId) {
        BigInteger profileId = CurrentUser.getProfileId();
        return productLocalHandleService.queryEshopInfo(profileId, eshopId);
    }

    @PostMapping(value = "/getProductRelation")
    public List<EshopInfo> getProductRelation(BigInteger eshopId) {
        BigInteger profileId = CurrentUser.getProfileId();
        return productLocalHandleService.queryBranchEshopByEshopId(profileId, eshopId);
    }

    @RequestMapping(value = "/getShopTypeIncreament", method = RequestMethod.POST)
    public Boolean getShopTypeIncreament(String shoptype) {
        return matchRoleService.getShopType(shoptype, "increament");
    }

    @RequestMapping(value = "/getShopTypeByArticleNumber", method = RequestMethod.POST)
    public Boolean getShopTypeByArticleNumber(String shoptype) {
        return matchRoleService.getShopType(shoptype, "articleNumber");
    }

    @RequestMapping(value = "/getKtypeList", method = RequestMethod.GET)
    public List<Stock> getKtypeList() {
        return matchRoleService.getKtypeList(CurrentUser.getProfileId());
    }

    @RequestMapping(value = "/getSysData", method = RequestMethod.GET)
    public Boolean getSysData(String subName) {
        return matchRoleService.getSysData(subName);
    }

    @RequestMapping(value = "/getsyncProductTime", method = RequestMethod.GET)
    public EshopProductSyncCondition getsyncProductTime(BigInteger eshopid) {
        return eshopProductSyncConditionMapper.queryAutoDownloadProductTask(new EshopProductSyncCondition(eshopid, true));
    }

    /**
     * 网店商品下载 禁用下载
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/changeStatus", method = RequestMethod.POST)
    public void changeStatus(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        productLocalHandleService.downloadEnable(params.getQueryParams());
    }

    /**
     * 网店商品下载保存本地对应商品名和属性
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/uploadLocalInfo", method = RequestMethod.POST)
    public boolean uploadLocalInfo(@RequestBody EshopPlatXcodeModifyRequest params) {

        return productService.uploadLocalInfo(params);
    }


    /**
     * 获取商品对应日志
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/queryProductlogList", method = RequestMethod.POST)
    public PageResponse<EshopProductMappingLog> queryProductlogList(@RequestBody PageRequest<EshopProductLogQueryParams> params) {
        return mappingLogService.queryProductLogList(params);
    }

    @RequestMapping(value = "/saveMark", method = RequestMethod.POST)
    public String saveMark(@RequestBody List<EshopOrderMarkRequest> req) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl messageLog = new ProcessLoggerImpl(taskId);
        ThreadPool test = ThreadPoolFactory.build("load-upload");
        test.executeAsync(invoker -> {
            try {
                markService.saveProductMarkNew(req, messageLog);
            } catch (Exception ex) {
                messageLog.appendMsg(ex.getMessage());
            } finally {
                messageLog.doFinish();
            }
        }, "网店商品打标");
        return taskId;
    }

    /**
     * 刷新商品
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/refreshProduct", method = RequestMethod.POST)
    public String refreshEshopProduct(@RequestBody RefreshPtypeParams params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        params.setTaskId(taskId);
        queryService.buildInsertPtyperefreshTaskParamas(taskId, params.getEshopId(), 0, params.getIsIncrement(), params.isClearRedundantProduct(), params.getStockState().getCode(), params.getClassName());
        Pair<RefreshPtypeParams, ProcessLoggerImpl> pair = new Pair<>(params, processLogger);
        //判断大促模式
        if (eshopOrderCommonConfig.isSpeedMode() || eshopOrderToolConfig.isRefreshPtypeByTool()) {
            GeneralResult<String> result = WebToToolApiFactory.forwardReq(pair, WebToToolApiEnum.REFRESH_ESHOP_PRODUCT, productService::refreshEshopProduct);
            return null == result || StringUtils.isEmpty(result.getData()) ? taskId : result.getData();
        }
        productService.refreshEshopProduct(pair);
        return taskId;
    }

    /**
     * 刷新店铺网店商品类目
     *
     * @return
     */
    @RequestMapping(value = "/refreshProductCategory", method = RequestMethod.POST)
    public String refreshEshopProductCategory(@RequestBody RefreshPtypeCategoryParams params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        params.setTaskId(taskId);
        productService.refreshEshopProductCategory(params, processLogger);
        return taskId;
    }


    /**
     * 刷新商城类目：用于商城扣点
     *
     * @return
     */
    @RequestMapping(value = "/refreshShopCategory", method = RequestMethod.POST)
    public String refreshEshopCategory(@RequestBody RefreshPtypeCategoryParams params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        params.setTaskId(taskId);
        productService.refreshEshopCategory(params, processLogger);
        return taskId;
    }

    /**
     * 刷新单个网店商品
     */
    @RequestMapping(value = "/refreshProductSingle", method = RequestMethod.POST)
    public String refreshProductSingle(@RequestBody RefreshPtypeParams params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        Pair<RefreshPtypeParams, ProcessLoggerImpl> pair = new Pair<>(params, processLogger);
        //判断大促模式
        if (eshopOrderCommonConfig.isSpeedMode() || eshopOrderToolConfig.isRefreshPtypeByTool()) {
            GeneralResult<String> result = WebToToolApiFactory.forwardReq(pair, WebToToolApiEnum.REFRESH_PRODUCT_SIGNLE, productService::refreshEshopProductSingle);
            return null == result || StringUtils.isEmpty(result.getData()) ? "" : result.getData();
        }
        return productService.refreshEshopProductSingle(pair);
    }

    /**
     * 商品下载与对应
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/downloadPtypeAsLocal", method = RequestMethod.POST)
    public String downloadPtypeAsLocal(@RequestBody UpdatePtypeParams params) {
        String taskId = UId.newId().toString();
        params.setTaskid(new BigInteger(taskId));
        queryService.buildInsertPtypeDownloadTaskParamas(taskId, params.getEshopId(), 1, 0);
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        productService.downloadproduct(params, processLogger);
        return taskId;
    }

    @RequestMapping(value = "/downloadPtype", method = RequestMethod.POST)
    public String downloadPtype(@RequestBody UpdatePtypeParams params) {
        String taskId = "createPtype" + UId.newId();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        productService.createLocalPtype(params, processLogger);
        return taskId;
    }

    @RequestMapping(value = "/checkrelationByXcodeAndNoXcode", method = RequestMethod.POST)
    public boolean checkrelationByXcodeAndNoXcode(@RequestBody BigInteger eshopid) {
        boolean status = productService.checkrelationByXcodeAndNoXcode(eshopid);
        return status;
    }

    /**
     * 属性对应关系查询
     *
     * @return
     */
    @RequestMapping(value = "/queryEshopAttrRelation", method = RequestMethod.POST)
    public List<EshopProductSkuAttrRelation> queryEshopAttrRelation(@RequestBody EshopProductSkuAttrRelationRequest request) {
        return productService.queryEshopSkuAttrRelation(request);
    }


    /**
     * 删除属性对应关系
     *
     * @return
     */
    @RequestMapping(value = "/deleteEshopAttrRelation", method = RequestMethod.POST)
    public void deleteEshopAttrRelation(@RequestBody List<BigInteger> id) {
        productService.deleteEshopAttrRelation(id);
    }

    /**
     * 属性对应关系保存
     *
     * @return
     */
    @RequestMapping(value = "/saveEshopAttrRelation", method = RequestMethod.POST)
    public boolean saveEshopAttrRelation(@RequestBody List<EshopProductSkuAttrRelation> attrRelationList) {
        productService.saveEshopAttrRelation(attrRelationList);
        return true;
    }

    /**
     * 插入属性对应
     *
     * @return
     */
    @RequestMapping(value = "/addEshopAttrRelation", method = RequestMethod.POST)
    public boolean addEshopAttrRelation(@RequestBody List<EshopProductSkuAttrRelation> attrRelationList) {
        productService.addEshopAttrRelation(attrRelationList);
        return true;
    }

    /**
     * 属性对应关系保存
     *
     * @return
     */
    @RequestMapping(value = "/saveANewProductRelation", method = RequestMethod.POST)
    @AsyncProcess(threadPoolName = AsyncProcess.ProcessName.modifyMappingtypeProcessName)
    public String saveANewProductRelation(@RequestBody UpdatePtypeParams params) {
        RedisProcessMessage processMessage = new RedisProcessMessage(params.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        try {
            messageLog.appendMsg("开始构建数据");
            String message = productService.saveANewProductRelation(params, messageLog);
            messageLog.appendMsg(message);
            return message;
        } catch (Exception e) {
            return e.getMessage();
        } finally {
            messageLog.appendMsg("生成属性商品结束");
            processMessage.setFinish();
        }

    }

    /**
     * 属性查询
     *
     * @return
     */
    @RequestMapping(value = "/queryEshopAttr", method = RequestMethod.POST)
    public List<String> queryEshopAttr() {
        List<String> propList = productService.queryEshopAttr();
        return propList;
    }

    /**
     * 属性查询
     *
     * @return
     */
    @RequestMapping(value = "/queryEshopPropList", method = RequestMethod.POST)
    public List<Prop> queryEshopPropList() {
        List<Prop> propList = productService.queryEshopPropList();
        return propList;
    }

    /**
     * 属性查询
     *
     * @return
     */
    @RequestMapping(value = "/getPropValueList", method = RequestMethod.POST)
    public List<PropValueName> getPropValueList() {
        List<PropValueName> propValueList = productService.getPropValueList();
        return propValueList;
    }

    /**
     * 生成本地普通商品
     *
     * @return
     */
    @RequestMapping(value = "/doAddPtypeRelationBatch", method = RequestMethod.POST)
    @AsyncProcess(threadPoolName = AsyncProcess.ProcessName.modifyMappingtypeProcessName)
    public AddEshopProductResponse doAddPtypeRelationBatch(@RequestBody BatchProcessRequest<SingalHandleProductRequest> handleProductRequest) {
        StringBuilder msg = new StringBuilder();
        int addPtypeCheckvalue = handleProductRequest.getAddPtypeCheckvalue();
        boolean removeHasRelationItem = handleProductRequest.getRemoveHasRelationItem();
        List<SingalHandleProductRequest> list = handleProductRequest.getList();
        if (addPtypeCheckvalue == 2) {
            list = list.stream().filter(x -> (x.getMappingType() == MappingType.XCODEMAPPING && StringUtils.isNotEmpty(x.getPlatXcode())) || x.getMappingType() == MappingType.NOMARL).collect(Collectors.toList());
        }
        if (removeHasRelationItem) {
            list = list.stream().filter(x -> x.isBind() == false).collect(Collectors.toList());
        }
        RedisProcessMessage processMessage = new RedisProcessMessage(handleProductRequest.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        messageLog.appendMsg("正在生成本地商品，请稍后");
        AddEshopProductResponse response = new AddEshopProductResponse();
        int i = messageLog.appendMsg(String.format("正在生成本地商品,共%s条，正在处理第%s条", list.size(), 0));
        int times = 0;
        Map<String, List<SingalHandleProductRequest>> map = new HashMap<>();
        for (SingalHandleProductRequest req : list) {
            if (req.getAddPtypeMode() != 1 && StringUtils.isEmpty(req.getPlatformPropertiesName())) {
                messageLog.appendMsg(String.format("网店商品【%s】属性值为空已过滤", req.getPlatNameFilterStr()));
                continue;
            }
            if (map.get(req.getPlatformNumId()) == null) {
                map.put(req.getPlatformNumId(), Arrays.asList(req));
            } else {
                List<SingalHandleProductRequest> reqs = map.get(req.getPlatformNumId());
                ArrayList<SingalHandleProductRequest> singalHandleProductRequests = new ArrayList<>();
                singalHandleProductRequests.addAll(reqs);
                singalHandleProductRequests.add(req);
                map.put(req.getPlatformNumId(), singalHandleProductRequests);
            }
        }

        for (Map.Entry<String, List<SingalHandleProductRequest>> m : map.entrySet()) {
            List<SingalHandleProductRequest> value = m.getValue();
            times += value.size();
            messageLog.modifyMsg(String.format("正在生成本地商品,共%s条，正在处理第%s条", list.size(), times), i);
            List<String> platformPropertiesName = value.stream().map(SingalHandleProductRequest::getPlatformPropertiesName).collect(Collectors.toList());
            AddEshopProductResponse result = productLocalHandleService.addEshopProductBatch(value.get(0), addPtypeCheckvalue, platformPropertiesName, ProductOperateLogType.ADDLOCALNORMALPTYPE);
            if (result.getErrorMsg() == null) {
                continue;
            }
            messageLog.appendMsg(result.getErrorMsg());
            if (msg.length() > 0) {
                msg.append(",").append(result.getErrorMsg());
            } else {
                msg = new StringBuilder(result.getErrorMsg());
            }
        }
        response.setErrorMsg(msg.toString());
        messageLog.appendMsg("生成商品结束");
        processMessage.setFinish();
        return response;
    }

    /**
     * 批量增加本地属性商品
     *
     * @param handleProductRequest
     * @return
     */
    @RequestMapping(value = "/doAddPtypeRelationBatchList", method = RequestMethod.POST)
    public String doAddPtypeRelationBatchList(@RequestBody List<SingalHandleProductRequest> handleProductRequest) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        productLocalHandleService.doAddPtypeRelationBatchList(handleProductRequest, processLogger);
        return taskId;
    }

    @RequestMapping(value = "/downloadMainImage", method = RequestMethod.POST)
    public String downloadMainImage(@RequestBody List<EshopProductRelationEntity> handleProductRequest) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        productLocalHandleService.downloadMainImage(handleProductRequest, processLogger);
        return taskId;
    }

    /**
     * 增，根据线上商品信息新增本地商品（参数用不完）
     *
     * @return
     */
    @RequestMapping(value = "/doAddPtypeRelation", method = RequestMethod.POST)
    public AddEshopProductResponse doAddPtypeRelation(@RequestBody SingalHandleProductRequest handleProductRequest) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        AddEshopProductResponse result = productLocalHandleService.addEshopProduct(handleProductRequest, processLogger);
        return result;
    }

    @RequestMapping(value = "/checkPlatformProperties", method = RequestMethod.POST)
    public AddEshopProductResponse checkPlatformProperties(@RequestBody SingalHandleProductRequest handleProductRequest) {
        AddEshopProductResponse result = productLocalHandleService.checkPlatformProperties(handleProductRequest);
        return result;
    }

    @RequestMapping(value = "/doAddPtypeRelationSync", method = RequestMethod.POST)
    public String doAddPtypeRelationSync(@RequestBody SingalHandleProductRequest handleProductRequest) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        productLocalHandleService.doAddPtypeRelationSync(handleProductRequest, processLogger);
        return taskId;
    }

    /**
     * 临时对应新增商品
     *
     * @return
     */
    @RequestMapping(value = "/addPtype", method = RequestMethod.POST)
    public AddEshopProductResponse addPtype(@RequestBody EshopProductMapping productMapping) {
        AddEshopProductResponse result = productLocalHandleService.addPtype(productMapping);
        return result;
    }

    /**
     * 绑，商品级别
     *
     * @param handleProductRequest
     * @return
     */
    @RequestMapping(value = "/changePtypeRelation", method = RequestMethod.POST)
    @AsyncProcess(threadPoolName = AsyncProcess.ProcessName.modifyMappingtypeProcessName)
    public String changePtypeRelation(@RequestBody SingalHandleProductRequest handleProductRequest) throws Exception {
        RedisProcessMessage processMessage = new RedisProcessMessage(handleProductRequest.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        messageLog.appendMsg("开始绑定对应关系");
        messageLog.appendMsg("正在开始构建数据");
        messageLog.appendMsg("自动过滤网店商品行");
        if (handleProductRequest.getPcategory() != Pcategory.Combo) {
            productLocalHandleService.createSkuAndXcode(handleProductRequest);
        }
        if (handleProductRequest.getPcategory() == Pcategory.Combo) {
            productLocalHandleService.buildComboInfo(handleProductRequest);
        }
        String result = productLocalHandleService.changePtypeRelation(handleProductRequest, false, null);
        if (result.length() > 0) {
            messageLog.appendMsg(result);
        }
        messageLog.appendMsg("对应关系绑定结束");
        processMessage.setFinish();
        return result;
    }

    @RequestMapping(value = "/buildCombo", method = RequestMethod.POST)
    public ComboSaveResponse buildCombo(@RequestBody List<SingalHandleProductRequest> handleProductRequest) {
        return productLocalHandleService.buildCombo(handleProductRequest, "");
    }

    @RequestMapping(value = "/AutoCreateCombo", method = RequestMethod.POST)
    public ComboSaveResponse AutoCreateCombo(@RequestBody List<SingalHandleProductRequest> handleProductRequest) {
        return productLocalHandleService.AutoCreateCombo(handleProductRequest);
    }

    @RequestMapping(value = "/syncProductRelation", method = RequestMethod.POST)
    public List<EshopInfo> syncProductRelation(@RequestBody List<EshopInfo> eshopInfos) {
        productLocalHandleService.syncProductRelation(eshopInfos);
        return eshopInfos;
    }

    @RequestMapping(value = "/syncProductMark", method = RequestMethod.POST)
    public List<EshopInfo> syncProductMark(@RequestBody List<EshopInfo> eshopInfos) {
        productLocalHandleService.syncProductMark(eshopInfos);
        return eshopInfos;
    }

    /**
     * 绑，sku级别
     *
     * @param handleProductRequest
     * @return
     */
    @RequestMapping(value = "/changePtypeSkuRelation", method = RequestMethod.POST)
    public String changePtypeSkuRelation(SingalHandleProductRequest handleProductRequest) throws Exception {
        return productLocalHandleService.changePtypeRelation(handleProductRequest, false, null);
    }

    /**
     * 批量绑定
     *
     * @param productResuest
     * @return
     */
    @RequestMapping(value = "/changePtypeSkuRelationBatch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @AsyncProcess(threadPoolName = AsyncProcess.ProcessName.modifyMappingtypeProcessName)
//    @NgpResource(name = "pl.bs.eshoporder.page.open", tagStrings = "method,relation.changePtypeSkuRelationBatch")
    public String changePtypeSkuRelationBatch(@RequestBody BatchProcessRequest<SingalHandleProductRequest> productResuest) {
        RedisProcessMessage processMessage = new RedisProcessMessage(productResuest.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        List<SingalHandleProductRequest> productResuestList = productResuest.getList();
        try {
            AtomicLong atomicLong = new AtomicLong();
            messageLog.appendMsg("开始绑定对应关系");
            messageLog.appendMsg("正在开始构建数据");
            messageLog.appendMsg("自动过滤网店商品行");
            int i = messageLog.appendMsg(String.format("正在绑定对应关系，绑定第%s条", 0));
            ThreadPool test = ThreadPoolFactory.build("batch-unbind");
            List<List<SingalHandleProductRequest>> splitList = Common.splitList(productResuestList, 200);
            test.submitTaskList(reqList -> {
                productLocalHandleService.BatchchangePtypeRelation(reqList, messageLog, i, atomicLong);
                return "";
            }, splitList);
            test.executeAsync(x -> {
                productLocalHandleService.notifyAndInsertLog(productResuestList, ProductOperateLogType.BindMappingBatch);
            }, null);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            messageLog.appendMsg("批量绑定对应关系失败：" + ex.getMessage());
            sysLogger.error("method:changePtypeSkuRelationBatch,profileId: {}, 批量绑定对应关系失败：{}", CurrentUser.getProfileId(), ex.getMessage(), ex);
            return ex.getMessage();
        } finally {
            messageLog.appendMsg("对应关系绑定结束");
            processMessage.setFinish();
        }
        return productResuest.getProcessId();
    }

    /**
     * 批量清除对应关系
     *
     * @param handleProductRequests
     * @return
     */
    @RequestMapping(value = "/clearPtypeRelationBatch", method = RequestMethod.POST)
    public String clearPtypeRelationBatch(@RequestBody List<SingalHandleProductRequest> handleProductRequests) {
        Stopwatch sw1 = Stopwatch.createStarted();
        if (handleProductRequests != null && handleProductRequests.size() > 0) {
            ThreadPool build = ThreadPoolFactory.build("batch-unbind");
            build.submitTaskList(req -> {
                if (!req.isHasProperties()) {
                    String msg = productLocalHandleService.clearProductMapping(req, true);
                    if (msg.length() > 0) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return msg;
                    }
                }
                return null;
            }, handleProductRequests);
            productLocalHandleService.asyncInsertLog(handleProductRequests, ProductOperateLogType.ClearMappingBatch);
        }
        sw1.stop();
        return "";
    }

    /**
     * 清除商品对应关系
     *
     * @param handleProductRequest
     * @return
     */
    @RequestMapping(value = "/clearPtypeRelation", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public String clearPtypeRelation(@RequestBody SingalHandleProductRequest handleProductRequest) {

        String s = productLocalHandleService.clearProductMapping(handleProductRequest, false);
        productLocalHandleService.asyncInsertLog(Arrays.asList(handleProductRequest), ProductOperateLogType.ClearMapping);
        return s;

    }

    /**
     * 修改sku对应方式
     */
    @RequestMapping(value = "/updataProductExpandMappingType", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public void updataProductExpandMappingType(@RequestBody UpdateProductExpandRequest handleProductRequest) {
        productLocalHandleService.updataProductExpandMappingType(handleProductRequest);
    }

    /**
     * 修改sku对应方式
     */
    @RequestMapping(value = "/deleteProduct", method = RequestMethod.POST)
    public void deleteProducts(@RequestBody List<SingalHandleProductRequest> data) {
        productLocalHandleService.deleteEshopProducts(data);
    }

    /**
     * 获取生成线上商品编码规则
     *
     * @return
     */
    @RequestMapping(value = "/getOnlinePtypeRules", method = RequestMethod.POST)
    public List<EshopProductXcodeRuleEntity> getOnlinePtypeRules() {
        return productLocalHandleService.getOnlinePtypeRules();
    }


    /**
     * 根据类型获取对应treeNode
     *
     * @param type
     * @return
     */
    @RequestMapping(value = "/getRelationTreeNodes", method = RequestMethod.POST)
    public List<TreeNodeData> getRelationTreeNodes(@RequestBody SingalHandleProductRequest type) {
        //获取网店的卖家分类
        if (type.getId().intValue() == 4) {
            EshopInfo eshopInfo = eshopService.getEshopInfoById(type.getProfileId(), type.getEshopId());
            SaleBizConfig saleBizConfig = GetBeanUtil.getBean(SaleBizConfig.class);
            List<Integer> notShowSellerShopTypes = saleBizConfig.getDownLoadPlatformCategoriesShopTypeList();
            if (CollectionUtils.isNotEmpty(notShowSellerShopTypes) && null != eshopInfo && notShowSellerShopTypes.contains(eshopInfo.getEshopType().getCode())) {
                return new ArrayList<>();
            }
            return classService.getEshopProductClassTreeNode(type.getProfileId(), type.getEshopId());
        } else {
            return productLocalHandleService.getTreeNodeByType(type.getId().intValue());
        }
    }

    /**
     * 获取店铺配置商家编码对应配置列表
     *
     * @return
     */
    @RequestMapping(value = "/getEshopMatchRoles", method = RequestMethod.POST)
    public List<EshopMatchRole> getEshopMatchRoles() {
        return matchRoleService.getEshopMatchRole();
    }

    /**
     * 修改网店商品自动刷新配置
     *
     * @return
     */
    @RequestMapping(value = "/updateAutoRefreshProduct", method = RequestMethod.POST)
    public void updateAutoRefreshProduct(@RequestBody EShopPageInfo info) {
        matchRoleService.updateAutoRefreshProduct(info);
    }

    /**
     * 修改 快速对应保存对应关系配置
     *
     * @return
     */
    @RequestMapping(value = "/autoSaveMappingRelation", method = RequestMethod.POST)
    public void updateautoSaveMappingRelation(@RequestBody EShopPageInfo info) {
        eshopService.autoSaveMappingRelation(info);
    }

    @RequestMapping(value = "/EshopConfigById", method = RequestMethod.POST)
    public EshopConfig EshopConfigById(@RequestBody EShopPageInfo info) {
        return eshopService.getEshopConfigById(CurrentUser.getProfileId(), info.getOtypeId());
    }

    /**
     * 保存店铺商家编码配置列表e
     *
     * @param matchRoleReq
     * @return
     */
    @RequestMapping(value = "/saveEshopMatchRole", method = RequestMethod.POST)
    @AsyncProcess(threadPoolName = AsyncProcess.ProcessName.modifyMappingtypeProcessName)
    public void saveEshopMatchRole(@RequestBody EshopMatchRolesSaveRequest matchRoleReq) {
        RedisProcessMessage processMessage = new RedisProcessMessage(matchRoleReq.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        try {
            messageLog.appendMsg("准备修改店铺对应模式");
            //清除规则对应关系
            BigInteger profileId = CurrentUser.getProfileId();
            List<EshopConfig> configList = new ArrayList<>();
            List<EshopMatchRole> matchRoles = matchRoleReq.getMatchRoles();
            if (matchRoles != null && matchRoles.size() > 0) {
                for (EshopMatchRole role : matchRoles) {
                    //判断是按什么方式对应 修改成按商家编码对应则需要删除手工对应时候的库存同步规则的对应状态
                    List<EshopProductSkuMapping> list = productMapper.queryByProfileIdAndeshopId(profileId, role.getId(), null);
                    EshopConfig config = new EshopConfig();
                    config.setEshopId(role.getId());
                    config.setEshopName(role.getFullname());
                    config.setMappingType(role.isMatchPtypeByXcode() ? MappingType.XCODEMAPPING : MappingType.NOMARL);
                    configList.add(config);
                }
            }
            if (configList != null && configList.size() > 0) {
                for (EshopConfig config : configList) {
                    matchRoleService.saveEshopMatchRole(config, matchRoleReq.isSyncStock(), messageLog);
                    //todo 根据issyncstock配置同步对应网店库存

                }
            }
            for (EshopMatchRole role : matchRoles) {
                String relation = role.isMatchPtypeByXcode() ? "按商编码对应" : "手工对应";
                String body = "【" + role.getOrgName() + "】修改了网店商品对应规则为【" + relation + "】";
                productService.recordPubSystemLog(body);
            }
            messageLog.appendMsg("店铺对应模式修改结束");
        } catch (Exception e) {
            messageLog.appendMsg(e.getMessage());
        } finally {
            processMessage.setFinish();
        }
    }

    /**
     * 保存批量店铺商家编码并上传
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/uploadPtypeXcode", method = RequestMethod.POST)
    public String uploadPtypeXcode(@RequestBody EshopUploadPtypeXcodeRequest params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        productService.createAndUploadXcode(params, processLogger);
        return taskId;
    }

    /**
     * 保存单个店铺商家编码并上传
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/uploadPtypeXcodeSingle", method = RequestMethod.POST)
    public String uploadPtypeXcodeSingle(@RequestBody EshopPlatXcodeModifyRequest params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        return productService.doUploadXcodeSingle(params, processLogger);
    }

    /**
     * 单个修改本地sku编码
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/uploadXcodeSingle", method = RequestMethod.POST)
    public String uploadXcodeSingle(@RequestBody EshopPlatXcodeModifyRequest params) {
        return productService.uploadXcodeSingle(params);
    }

    /**
     * 修改单个本地商品信息
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/modifyPtype", method = RequestMethod.POST)
    public String modifyPtype(@RequestBody EshopPlatXcodeModifyRequest params) {
        return productService.modifyPtype(params);
    }

    /**
     * 新增品牌并修改本地商品信息品牌id
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/modifyBrand", method = RequestMethod.POST)
    public String modifyBrand(@RequestBody EshopPlatXcodeModifyRequest params) {
        return productService.modifyBrand(params);
    }


    @RequestMapping(value = "/modifyUnitName", method = RequestMethod.POST)
    public String modifyUnitName(@RequestBody EshopPlatXcodeModifyRequest params) {
        return productService.modifyUnitName(params);
    }

    /**
     * 保存单个库存同步规则
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/doUpddateSyncRule", method = RequestMethod.POST)
    public void doUpddateSyncRule(@RequestBody EshopSyncRuleRequest params) {
        productService.doUpddateSyncRule(params);
    }

    /**
     * 商品或套餐变化时，通知修改商品对应状态以及记录日志
     *
     * @param changeList
     * @return
     */
    @RequestMapping(value = "/notifyRelationWhenChangePtype", method = RequestMethod.POST)
    public void notifyRelationWhenChangePtype(@RequestBody List<EshopProductChangeEntity> changeList) {
        ptypeChaneNotifyService.saveLogWhenPtypeChange(changeList);
    }

    @GetMapping("/getStockState/{key}/{eshopId}")
    public List<EnumState> getStockState(@PathVariable String key, @PathVariable BigInteger eshopId) {
        List<EnumState> states = new ArrayList<>();
        List<StockState> stockStats = productLocalHandleService.getEshopStockStates((eshopId));
        for (StockState stockState : stockStats) {
            EnumState state = new EnumState();
            state.setCode(stockState.getCode());
            state.setName(stockState.getName());
            states.add(state);
        }
        return states;

    }

    @PostMapping("/downloadRelationModal")
    @ResponseBody
    public String downloadRelationModal(HttpServletResponse response, @RequestBody List<EshopProductRelationEntity> entities) {
        try {
            String excelName = "EXCEL快速对应";
            // 生成数据
            List<EshopRelationProductExcelEntity> productList = new ArrayList();
            List<EshopRelationComboExcelEntity> comboList = new ArrayList();
            String platName = "";
            for (EshopProductRelationEntity en : entities) {
                if (!StringUtils.isEmpty(en.getPlatName())) {
                    platName = en.getPlatName();
                }
                if (!en.isHasProperties()) {
                    en.setPlatName(platName);
                    EshopRelationProductExcelEntity productExcelEntity = new EshopRelationProductExcelEntity();
                    EshopRelationComboExcelEntity comboExcelEntity = new EshopRelationComboExcelEntity();
                    BeanUtils.copyProperties(en, productExcelEntity);
                    BeanUtils.copyProperties(en, comboExcelEntity);
                    productList.add(productExcelEntity);
                    comboList.add(comboExcelEntity);
                }
            }
            InputStream templateFileName = FileUtil.class.getClassLoader().getResourceAsStream("static/eshoporder/template/EXCEL.xls");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode(excelName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");

            // 多sheel导出
            ExcelWriter excelWriter = EasyExcel.write(new BufferedOutputStream(response.getOutputStream())).withTemplate(templateFileName).build();
            WriteSheet test1 = EasyExcel.writerSheet(1, "EXCEL快速对应（仅对应）").head(EshopRelationProductExcelEntity.class).build();
            WriteSheet test2 = EasyExcel.writerSheet(2, "EXCEL快速对应 (新增套餐)").head(EshopRelationComboExcelEntity.class).build();
            excelWriter.write(productList, test1).write(comboList, test2);
            excelWriter.finish();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @PostMapping("/downloadRelationModalWithQuery")
    @ResponseBody
    public String downloadRelationModalWithQuery(HttpServletResponse response, @RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        List<EshopProductSkuMapping> entities = productLocalHandleService.queryEshopProductMappingSimple(params);
        try {
            String fileName = "Excel快速对应";
            // 生成数据
            List<EshopRelationProductExcelEntity> productList = new ArrayList();
            List<EshopRelationComboExcelEntity> comboList = new ArrayList();
            for (EshopProductSkuMapping en : entities) {
                EshopRelationProductExcelEntity productExcelEntity = new EshopRelationProductExcelEntity();
                productExcelEntity.setPlatformSkuId(en.getPlatformSkuId());
                productExcelEntity.setPlatName(en.getPlatfullname());
                productExcelEntity.setPlatXcode(en.getPlatXcode());
                productExcelEntity.setPlatformPropertiesName(en.getPlatformPropertiesName());

                EshopRelationComboExcelEntity comboExcelEntity = new EshopRelationComboExcelEntity();
                comboExcelEntity.setPlatformSkuId(en.getPlatformSkuId());
                comboExcelEntity.setPlatName(en.getPlatfullname());
                comboExcelEntity.setPlatXcode(en.getPlatXcode());
                comboExcelEntity.setPlatformPropertiesName(en.getPlatformPropertiesName());
                productList.add(productExcelEntity);
                comboList.add(comboExcelEntity);
            }
            InputStream templateFileName = FileUtil.class.getClassLoader().getResourceAsStream("static/eshoporder/template/EXCEL.xls");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
            // 多sheel导出
            ExcelWriter excelWriter = EasyExcel.write(new BufferedOutputStream(response.getOutputStream())).withTemplate(templateFileName).build();
            WriteSheet test1 = EasyExcel.writerSheet(1, "EXCEL快速对应（仅对应）").head(EshopRelationProductExcelEntity.class).build();
            WriteSheet test2 = EasyExcel.writerSheet(2, "EXCEL快速对应 (新增套餐)").head(EshopRelationComboExcelEntity.class).build();
            excelWriter.write(productList, test1).write(comboList, test2);
            excelWriter.finish();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @PostMapping("/downloadErrorDate")
    @ResponseBody
    public void downloadErrorDate(HttpServletResponse response, @RequestBody List<TotalInfoList> entities) {
        try {

            String excelName = "账单导入错误数据";
            // 生成数据
            List<TotalInfoList> orderList = new ArrayList();
            String platName = "";
            for (TotalInfoList en : entities) {
                if (!en.isStatus()) {
                    orderList.add(en);
                }
            }
            InputStream templateFileName = FileUtil.class.getClassLoader().getResourceAsStream("static/eshoporder/template/ErrorData.xls");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode(excelName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");

            // 多sheel导出
            ExcelWriter excelWriter = EasyExcel.write(new BufferedOutputStream(response.getOutputStream())).build();
            WriteSheet test1 = EasyExcel.writerSheet(0, "账单").head(TotalInfoList.class).build();
            excelWriter.write(orderList, test1);
            excelWriter.finish();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/importExcel")
    public String importExcel(MultipartFile file, BigInteger eshopId, int qtyLength, int priceLength) throws IOException {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        ThreadPool test = ThreadPoolFactory.build("refresh-ptype");
        test.executeAsync(invoker -> {
            try {
                InputStream inputStream = file.getInputStream();
                ByteArrayOutputStream byteArrayOutputStream = cloneInputStream(inputStream);
                BufferedInputStream buffInputStream = new BufferedInputStream(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));
                BufferedInputStream buffInputStream2 = new BufferedInputStream(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));
                productLocalHandleService.processImportAndRelation(buffInputStream, eshopId, processLogger, qtyLength, priceLength, buffInputStream2);
            } catch (Exception ex) {
                processLogger.appendMsg(ex.getMessage());
            } finally {
                processLogger.appendMsg("网店商品对应结束");
                processLogger.doFinish();
            }
        }, "网店商品对应");

        return taskId;
    }

    private static ByteArrayOutputStream cloneInputStream(InputStream input) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = input.read(buffer)) > -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
            return baos;
        } catch (IOException e) {
            LoggerFactory.getLogger(PtypeReleationController.class).error(e.getMessage(), e);
            return null;
        }
    }

    @PostMapping("/doNotifyOrderRelation")
    public void notifyOrderRelation(@RequestBody List<EshopProductSkuMapping> changedSkuMappingList) {
        List<EshopProductSkuMapping> mappingList = changedSkuMappingList.stream().filter(x -> x.getPtypeId().compareTo(BigInteger.ZERO) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }
        ThreadPool pool = ThreadPoolFactory.build("batch-unbind");
        pool.executeAsync(x -> {
            notifyService.updateOrderDetailMapping(changedSkuMappingList, null);
        }, null);
    }

    @RequestMapping(value = "/querySupplierProduct", method = RequestMethod.POST)
    public PageResponse<EshopProductSkuMapping> querySupplierProduct(@RequestBody PageRequest<QuerySupplierProductRequest> params) {
        return productLocalHandleService.querySupplierProduct(params);
    }

    @RequestMapping(value = "/listProductArticleNumberByExcel", method = RequestMethod.POST)
    public StringBuilder listProductArticleNumberByExcel(MultipartFile loadfile, String showName) throws IOException {
        if (loadfile == null) {
            throw new RuntimeException("导入文件不正确， 请重新选择！");
        }
        showName = StringUtils.isEmpty(showName) ? "" : showName;
        String name = loadfile.getOriginalFilename();
        if (!StringUtils.endsWithIgnoreCase(name, ".xls") && !StringUtils.endsWith(name, ".xlsx")) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        InputStream inputStream = loadfile.getInputStream();
        ByteArrayOutputStream byteArrayOutputStream = cloneInputStream(inputStream);
        BufferedInputStream buffInputStream11 = new BufferedInputStream(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));
        BufferedInputStream buffInputStream22 = new BufferedInputStream(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()));
        List<EshopProductArticleNumberImportEntity> list = productLocalHandleService.readProductIdImportSheetData(buffInputStream11, buffInputStream22, showName);
        if (list == null) {
            throw new RuntimeException("文件异常，导入失败，请检查导入模板及其格式是否正确！");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("文件里没有需要导入的数据，请检查导入模板！");
        }
        return getExcelData(list.stream().map(EshopProductArticleNumberImportEntity::getCol_0).collect(Collectors.toList()), showName);
    }


    private StringBuilder getExcelData(List<String> list, String showName) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            String col_0 = list.get(i);
            if (StringUtils.isBlank(col_0)) {
                throw new RuntimeException(String.format("导入模板内容不正确:%s不能为空数据，请检查导入模板！", showName));
            }
            boolean checked = productLocalHandleService.checkId(col_0);
            if (!checked) {
                throw new RuntimeException(String.format("导入模板内容不正确:%s只能包含数字、字母、下划线和短横线，请检查导入模板！错误行数据信息：%s", showName, col_0));
            }
            if (i == list.size() - 1) {
                // 最后一个不需要拼接逗号
                stringBuilder.append(col_0);
            } else {
                stringBuilder.append(col_0).append(",");
            }
        }
        return stringBuilder;

    }

    /**
     * 网店商品选择框，获取网店商品列表
     *
     * @param request
     * @return
     */
    @PageDataSource
    @RequestMapping(value = "/queryEshopProductSelectInfo", method = RequestMethod.POST)
    public PageResponse<EshopProductSelectorInfo> queryEshopProductSelectInfo(@RequestBody PageRequest<QueryEshopProductSelectRequest> request) {
        QueryEshopProductSelectRequest params = request.getQueryParams();
        if (params == null) {
            return new PageResponse<>();
        }
        if (params.getOtypeId() == null || params.getOtypeId() == BigInteger.ZERO) {
            throw new RuntimeException("网店参数错误，请检查");
        }
        params.setProfileId(CurrentUser.getProfileId());
        List<EshopProductSelectorInfo> parList = productLocalHandleService.queryEshopProductSelectMain(params);
        return PageDevice.readPage(parList);
    }


    @RequestMapping(value = "/getShopTypeSupportRefreshProductPageInfo", method = RequestMethod.POST)
    public ShopTypeSupportRefreshProductPageInfo getShopTypeSupportRefreshProductPageInfo(String shopType) {
        ShopTypeSupportRefreshProductPageInfo pageInfo = new ShopTypeSupportRefreshProductPageInfo();
        pageInfo.setShopTypeIncreament(matchRoleService.getShopType(shopType, "increament"));
        pageInfo.setShopTypeByArticleNumber(matchRoleService.getShopType(shopType, "articleNumber"));
        ShopType shopType1 = StringUtils.isNotEmpty(shopType) ? ShopType.valueOf(Integer.parseInt(shopType)) : ShopType.ErrorEshop;
        pageInfo.setProductRefreshSupportTypes(eshopService.getProductDownLoadByParams(shopType1));
        pageInfo.setSupportCreateInitStockShopType(eshopService.getSupportCreateInitStockShopType());
        pageInfo.setKtypeList(matchRoleService.getKtypeList(CurrentUser.getProfileId()));
        return pageInfo;
    }

    @RequestMapping(value = "/getShopTypeAllowProductClass", method = RequestMethod.POST)
    public Boolean getShopTypeAllowProductClass(String shopType) {
        if (StringUtils.isEmpty(shopType)) {
            return false;
        }
        int shopTypeInt = Integer.parseInt(shopType);
        ShopType eshopType = ShopType.valueOf(shopTypeInt);
        return EshopUtils.isFeatureSupported(EshopProductSellerClassFeature.class, eshopType);
    }

    /**
     * 网店商品选择框：网店商品对应列表查询
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/queryPtypeSelectorPtypeRelationList", method = RequestMethod.POST)
    public PageResponse<EshopProductRelationEntity> queryPtypeSelectorPtypeRelationList(@RequestBody PageRequest<QueryEshopProductMappingRequest> params) {
        return productLocalHandleService.queryEshopProductSkuRelationsList(params);
    }


    /**
     * 网店商品选择框：已选择的网店商品对应数据查询
     *
     * @param params 请求参数
     * @return 响应
     */
    @RequestMapping(value = "/queryPtypeSelectorSelectedPtypeRelationList", method = RequestMethod.POST)
    public List<EshopProductRelationEntity> queryPtypeSelectorSelectedPtypeRelationList(@RequestBody QueryEshopProductMappingRequest params) {
        return productLocalHandleService.queryPtypeSelectorSelectedPtypeRelationList(params);
    }
}
