package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.service.audit.DeliverBillAutoAuditService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(description = "自动审核测试接口")
@RequestMapping("/${app.id}/jarvis/autoAudit")
@ConditionalOnProperty(value = "sale.jarvis.deliver.tool.enabled-debug",havingValue = "true")
public class AutoAuditTestController {
    private DeliverBillAutoAuditService autoAuditService;
    public AutoAuditTestController(DeliverBillAutoAuditService autoAuditService) {
        this.autoAuditService = autoAuditService;
    }
    @ApiOperation(value = "执行测试任务", notes = "（）")
    @PostMapping("invoke")
    public void invoke(){
        autoAuditService.audit(CurrentUser.getProfileId(), "NORMAL");
        autoAuditService.modifyAuditWeight(CurrentUser.getProfileId());
    }


}
