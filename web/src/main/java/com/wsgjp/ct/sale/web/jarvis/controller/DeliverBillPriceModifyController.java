package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.common.ResultDTO;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.SimpleStrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.NeedProcessMsgBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessFaceBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessMessageMemory;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessResponse;
import com.wsgjp.ct.sale.common.enums.ModifyScene;
import com.wsgjp.ct.sale.biz.jarvis.newpackage.face.ExStatusFace;
import com.wsgjp.ct.sale.biz.jarvis.newpackage.face.WarehouseBillIds;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverBillPriceModifyService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.StrategyUtils;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillExStatusAsyncHandleHelper;
import com.wsgjp.ct.sale.common.enums.ModifyScene;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

/**
 * @Description TODO
 * @Date 2023-04-17 10:25
 * @Created by lingxue
 */
@RestController
@Api("批量修改价格")
@RequestMapping("/${app.id}/jarvis/modifyPrice")
public class DeliverBillPriceModifyController {
    private DeliverBillPriceModifyService deliverBillPriceModifyService;
    BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper;
    private static final Logger logger = LoggerFactory.getLogger(DeliverBillPriceModifyController.class);


    public DeliverBillPriceModifyController(DeliverBillPriceModifyService deliverBillPriceModifyService,BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper)
    {
        this.deliverBillPriceModifyService = deliverBillPriceModifyService;
        this.billExStatusAsyncHandleHelper = billExStatusAsyncHandleHelper;
    }
    @ApiOperation(value = "加载明细", notes = "")
    @PostMapping("loadDetails")
    public  List<BillDeliverDetailDTO> loadDetails(@RequestBody List<BigInteger> warehouseTaskIds) {
        return deliverBillPriceModifyService.loadDisedTaxedDetails(CurrentUser.getProfileId(),warehouseTaskIds);
    }

    @ApiOperation(value = "加载明细", notes = "")
    @PostMapping("loadPurchaseDetails")
    public  List<BillDeliverDetailDTO> loadPurchaseDetails(@RequestBody List<BigInteger> warehouseTaskIds) {
        return deliverBillPriceModifyService.loadPurchaseDetails(CurrentUser.getProfileId(),warehouseTaskIds);
    }

    @ApiOperation(value = "获取策略分销价", notes = "")
    @PostMapping("getSaleDistributionPrice")
    public Map<String, BigDecimal> getSaleDistributionPrice(@RequestBody List<BillDeliverDetailDTO> details) {
        return deliverBillPriceModifyService.getSaleDistributionPrice(details);
    }

    @ApiOperation(value = "获取价格等级", notes = "")
    @PostMapping("getPriceLevel")
    public Map<String, BigDecimal> getPriceLevel(@RequestBody PriceLevelRequest request) {
        return deliverBillPriceModifyService.getPriceLevel(request.getDetails(), request.getPriceLevel());
    }

    @ApiOperation(value = "修改价格", notes = "")
    @PostMapping("modifyDisedTaxedPrice")
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.BATCH_MODIFY_DISEDTAXEDPRICE)
    public ProcessResponse<StrategyProcessLog> modifyDisedTaxedPrice(@RequestBody PriceLevelRequest request) {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request,request.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        List<ExStatusFace> exStatusFaces = new ArrayList<>();
        try {
            List<ResultDTO<Boolean>> result =  deliverBillPriceModifyService.modifyDisedTaxedPrice(request.getWarehouseTaskIds(), request.getDetails(),processMessage);
            List<StrategyProcessLog> errors = new ArrayList<>();
            for (ResultDTO<Boolean> error:result) {
                StrategyProcessLog log = new StrategyProcessLog();
                log.setVchcode(error.getWarehouseTaskId());
                log.setBillNumber(error.getBillNumber());
                log.setTaskNumber(error.getTaskNumber());
                log.setContent(error.getMessage());
                log.setTradeOrderId(error.getTradeId());
                log.setMessage(error.getMessage());
                log.setWarehouseTaskId(error.getWarehouseTaskId());
                errors.add(log);
                exStatusFaces.add(new WarehouseBillIds(null,log.getWarehouseTaskId()));
            }
//            StrategyUtils.cacheProcessMessage(request.getProcessId(), errors);
            messageLog.cacheProcessErrorMessage(errors);
        } catch (Exception e) {
            messageLog.appendMsg(e.getMessage());
            StrategyProcessLog log = new StrategyProcessLog();
            log.setContent("执行错误："+e.getMessage());
            try {
//                StrategyUtils.cacheProcessMessage(request.getProcessId(), Collections.singletonList(log));
                messageLog.cacheProcessErrorMessage(Collections.singletonList(log));
            } catch (Exception redisCacheError) {
                StringWriter writer = new StringWriter();
                redisCacheError.printStackTrace(new PrintWriter(writer));
                logger.error("执行折后金额连接redis异常, {}", writer);
            }
        } finally {
            processMessage.setFinish();
            billExStatusAsyncHandleHelper.logoComprehensiveSceneByFace(exStatusFaces, Arrays.asList(ModifyScene.Detail_Modify),false);
        }
        return ProcessResponse.result(StrategyProcessLog.class,processMessage);
    }

    @ApiOperation(value = "修改采购价格", notes = "")
    @PostMapping("modifyPurchasePrice")
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.BATCH_MODIFY_PURCHASE_PRICE)
    public ProcessResponse<StrategyProcessLog> modifyPurchasePrice(@RequestBody PriceLevelRequest request) {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request,request.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        try {
            List<ResultDTO<Boolean>> result =  deliverBillPriceModifyService.modifyPurchasePrice(request.getWarehouseTaskIds(), request.getDetails(),processMessage);
            List<StrategyProcessLog> errors = new ArrayList<>();
            for (ResultDTO<Boolean> error:result) {
                StrategyProcessLog log = new StrategyProcessLog();
                log.setVchcode(error.getWarehouseTaskId());
                log.setBillNumber(error.getBillNumber());
                log.setTaskNumber(error.getTaskNumber());
                log.setContent(error.getMessage());
                log.setTradeOrderId(error.getTradeId());
                log.setMessage(error.getMessage());
                errors.add(log);
            }
//            StrategyUtils.cacheProcessMessage(request.getProcessId(), errors);
            messageLog.cacheProcessErrorMessage(errors);
//            messageLog.setError(errors.size() != 0);
        } catch (Exception e) {
            messageLog.appendMsg(e.getMessage());
            StrategyProcessLog log = new StrategyProcessLog();
            log.setContent("执行修改采购单价错误："+e.getMessage());
            try {
//                StrategyUtils.cacheProcessMessage(request.getProcessId(), Collections.singletonList(log));
                messageLog.cacheProcessErrorMessage(Collections.singletonList(log));
            } catch (Exception redisCacheError) {
                StringWriter writer = new StringWriter();
                redisCacheError.printStackTrace(new PrintWriter(writer));
                logger.error("执行修改采购单价连接redis异常, {}", writer);
            }
        } finally {
            processMessage.setFinish();
        }
       return ProcessResponse.result(StrategyProcessLog.class,processMessage);
    }
}

class PriceLevelRequest implements ProcessFaceBatchAd
{
    private List<BigInteger> warehouseTaskIds;
    private List<BillDeliverDetailDTO> details;
    private int priceLevel;
    private String processId;
    private Boolean notProcess;

    @Override
    public Boolean getNotProcess() {
        return notProcess;
    }

    public void setNotProcess(Boolean notProcess) {
        this.notProcess = notProcess;
    }

    public List<BigInteger> getWarehouseTaskIds() {
        return warehouseTaskIds;
    }

    public void setWarehouseTaskIds(List<BigInteger> warehouseTaskIds) {
        this.warehouseTaskIds = warehouseTaskIds;
    }

    public List<BillDeliverDetailDTO> getDetails() {
        return details;
    }

    public void setDetails(List<BillDeliverDetailDTO> details) {
        this.details = details;
    }

    public int getPriceLevel() {
        return priceLevel;
    }

    public void setPriceLevel(int priceLevel) {
        this.priceLevel = priceLevel;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }
}
