package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.wsgjp.ct.redis.process.message.bll.RedisMessageUtil;
import com.wsgjp.ct.sale.biz.bill.service.AddBillLogs;
import com.wsgjp.ct.sale.biz.eshoporder.api.EshopOrderAutoToolApi;
import com.wsgjp.ct.sale.biz.eshoporder.api.PtypeApi;
import com.wsgjp.ct.sale.biz.eshoporder.api.enums.WebToToolApiEnum;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.BaseBtype;
import com.wsgjp.ct.sale.biz.eshoporder.api.totool.WebToToolApiFactory;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.financecheck.FlowBindBillOprateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.orderImport.EshopSaleOrderGatherImportEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.platformdecrypt.PlatformCheckDownloadCache;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopSaleItemExcelEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.EshopSaleOrderGatherInfo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.EshopSaleOrderGatherRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.DiffFeeBalanceResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.DiffFeeBalanceResponseNew;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.StoreFinanceCheckPageData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.standardApi.StandardApiResponse;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderBaseInfoMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.orderimport.EShopSaleOrderImportConfigManager;
import com.wsgjp.ct.sale.biz.eshoporder.service.platformcheck.PlatformCheckService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BarcodeScalePtype;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.eshoporder.OrderOpreateType;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.DownloadPageInitResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.redis.factory.CacheType;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import jodd.io.FileUtil;
import ngp.idgenerator.UId;
import ngp.redis.RedisPoolFactory;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "平台对账相关接口")
@RequestMapping("${app.id}/eshoporder/platformcheck")
@RestController
public class PlatformCheckController {

    private final AddBillLogs addBillLogs;
    private final PlatformCheckService platformCheckService;
    private final EshopOrderBaseInfoMapper baseInfoMapper;
    private final SaleBizConfig config;
    private final EshopOrderAutoToolApi toolApi;
    private final PtypeApi baseApi;

    private final EshopService eshopService;
    private final RedisPoolFactory redisPoolFactory;
    private static final Logger logger = LoggerFactory.getLogger(PlatformCheckController.class);

    public PlatformCheckController(AddBillLogs addBillLogs, PlatformCheckService platformCheckService, EshopOrderBaseInfoMapper baseInfoMapper, SaleBizConfig config,
                                   EshopOrderAutoToolApi toolApi, PtypeApi baseApi, EshopService eshopService,
                                   RedisPoolFactory redisPoolFactory) {
        this.addBillLogs = addBillLogs;
        this.platformCheckService = platformCheckService;
        this.baseInfoMapper = baseInfoMapper;
        this.config = config;
        this.toolApi = toolApi;
        this.baseApi = baseApi;
        this.eshopService = eshopService;
        this.redisPoolFactory = redisPoolFactory;
    }

    @RequestMapping(value = "/init", method = RequestMethod.POST)
    public DownloadPageInitResponse getInitData() {
        DownloadPageInitResponse response = new DownloadPageInitResponse();
        QueryEShopParameter queryEShopParameter = new QueryEShopParameter();
        queryEShopParameter.setShopTypeInt(-1);
        queryEShopParameter.setAddStopSuffix(true);
        List<EshopInfo> eshopInfoList = eshopService.getEshopByShopType(queryEShopParameter);
        response.setEshopDatasource(eshopInfoList);
        buildInitDataByCache(response);
        if (StringUtils.isNotEmpty(response.getTaskId())) {
            return response;
        }
        response.setNewDownloadEnabled(true);
        Date nowDate = new Date();
        Date begin = DateUtils.addDays(nowDate, -7);
        response.setBegin(begin);
        response.setEnd(nowDate);
        return response;
    }

    private void buildInitDataByCache(DownloadPageInitResponse response) {
        String downloadKey = String.format("%s-%s-downloadPayRecord", CurrentUser.getProfileId(),
                CurrentUser.getEmployeeId());
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String cache = template.opsForValue().get(downloadKey);
        if (StringUtils.isNotEmpty(cache)) {
            PlatformCheckDownloadCache cacheData = JsonUtils.toObject(cache, PlatformCheckDownloadCache.class);
            response.setBegin(cacheData.getBegin());
            response.setEnd(cacheData.getEnd());
            response.setEshopId(cacheData.getEshopId());
            response.setEshopName(cacheData.getEshopName());
            response.setTaskId(cacheData.getTaskId());
            response.setNewDownloadEnabled(false);
        }
    }


    @PostMapping(value = "/bindFlowBillRelation")
    public StandardApiResponse bindFlowBillRelation(@RequestBody String vchcode, BigInteger billAccountId) {
        try {
            if (StringUtils.isEmpty(vchcode)) {
                return StandardApiResponse.fail(String.format("vchcode为空，"));
            }
            platformCheckService.relationByFlowAndVchcodeAndaccount(vchcode);
            return StandardApiResponse.ok("资金流水对账对应关系清理完成");
        } catch (Exception e) {
            logger.info("资金流水对应关系失败，错误信息：" + e);
            return StandardApiResponse.fail(String.format("资金流水对账对应关系失败: %s", e.getMessage()));
        }
    }


    @RequestMapping(value = "/addNotCheckAccountStatus", method = RequestMethod.POST)
    public String addNotCheckAccountStatus(@RequestBody List<EshopFinanceCheckResultEntity> resultInsertList) {
        String errorMassage = "";
        try {
            List<EshopFinanceCheckResultEntity> collect = resultInsertList.stream().filter(x -> x.getVchcode() == null || x.getVchcode().compareTo(BigInteger.ZERO) == 0).collect(Collectors.toList());
            if (collect.size() > 0) {
                return "vchcode值不能为空，检查你的数据";
            }
            List<BigInteger> flowIds =platformCheckService.getFlowIds(resultInsertList);
            platformCheckService.getAllVcocodeAndModify(resultInsertList);
            //判断是否有对应关系 如果有需要清除
            for (EshopFinanceCheckResultEntity res : resultInsertList) {
                if (res.getCheckStatus() == 3) {
                    if (res.isNeedCleanRelation()) {
                        platformCheckService.cleanFlowBillRelation(res.getVchcode());
                        platformCheckService.addLogForNotChaeckAccount(res.getVchcode(), FlowBindBillOprateType.NOT_CHECK_ACCOUNT.getCode(),FlowBindBillOprateType.NOT_CHECK_ACCOUNT.getName());

                    }
                } else if (res.getCheckStatus() == 0) {
                    platformCheckService.cleanNotCheckAccountStatus(res.getVchcode());
                    platformCheckService.addLogForNotChaeckAccount(res.getVchcode(), FlowBindBillOprateType.CANCEL_NOT_CHECK_ACCOUNT.getCode(),FlowBindBillOprateType.CANCEL_NOT_CHECK_ACCOUNT.getName());
                }
            }
            if (resultInsertList.get(0).getCheckStatus() == 3) {
                platformCheckService.addFinanceCheckResult(resultInsertList);
            }
            if(flowIds.size()>0){
                platformCheckService.updateflowMatchStatus(flowIds);
            }
        } catch (Exception e) {
            errorMassage = e.getMessage();
            logger.error("新增无需对账报错，错误信息" + e);
        }
        return errorMassage;

    }

    @RequestMapping(value = "/download", method = RequestMethod.POST)
    public String downloadPayRecord(@RequestBody DownloadPayRecordRequest task) {
        EshopInfo eshopInfo = new EshopInfo();
        eshopInfo.setOtypeId(task.getOtypeId());
        eshopInfo.setFullname(task.getFullname());
        task.setEshopInfo(eshopInfo);
        boolean toolEnabled = config.isDownloadPayRecordByToolEnabled();
        task.setProfileId(CurrentUser.getProfileId());
        task.setLoginUserId(CurrentUser.getEmployeeId());
        String downloadKey = String.format("%s-%s-downloadPayRecord", task.getProfileId(), task.getLoginUserId());
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String cache = template.opsForValue().get(downloadKey);
        if (StringUtils.isNotEmpty(cache)) {
            PlatformCheckDownloadCache cacheData = JsonUtils.toObject(cache, PlatformCheckDownloadCache.class);
            return cacheData.getTaskId();
        }
        if (toolEnabled) {
            GeneralResult<String> result = WebToToolApiFactory.forwardReq(task, WebToToolApiEnum.DOWNLOAD_PAY_RECORD, platformCheckService::downloadPayRecord);
            if (result.getCode() == 200) {
                return result.getData();
            }
            logger.error("使用工具下载账单失败，自动调整到站点下载，错误信息:{}({})", result.getMessage(), result.getTraceId());
            return result.getData();
        } else {
            try {
                return platformCheckService.downloadPayRecord(task);
            } catch (RuntimeException ex) {
                throw new RuntimeException(String.format("执行下载账单报错:%s", ex.getMessage()), ex);
            }
        }
    }

    @RequestMapping(value = "/queryAccounts", method = RequestMethod.POST)
    public PageResponse<PlatformCheckAccountsPageData> QueryAccounts(@RequestBody PageRequest<PlatformCheckAccountsRequest> parameter) {
        CommonUtil.initLimited(parameter.getQueryParams());
        PageDevice.initPage(parameter);
        return PageDevice.readPage(platformCheckService.queryAccounts(parameter.getQueryParams()));
    }

    @RequestMapping(value = "/queryPaymentFlowAtypesMap", method = RequestMethod.POST)
    @PageDataSource
    public PageResponse<PaymentFlowAtypeMapEntity> QueryAccountMapInfos(@RequestBody PageRequest<PaymentFlowAtypesMapRequest> parameter) {
        CommonUtil.initLimited(parameter.getQueryParams());
        PageDevice.initPage(parameter);
        return PageDevice.readPage(platformCheckService.QueryAccountMapInfos(parameter.getQueryParams()));
    }

    @RequestMapping(value = "/updatePaymentFlowAtypesMap", method = RequestMethod.POST)
    public String UpdateAccountMapInfos(@RequestBody List<PaymentFlowAtypeMapEntity> atypeMap) {
        return platformCheckService.UpdateAccountMapInfos(atypeMap);
    }

    //生成财务单据
    @RequestMapping(value = "/DoBalanceAccount", method = RequestMethod.POST)
    public String downloadPayRecord(@RequestBody DoBalanceAccountRequest task) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        task.setProcessLogger(processLogger);
        task.setProfileId(CurrentUser.getProfileId());
        task.setLoginUserId(CurrentUser.getEmployeeId());
        platformCheckService.asyncDoBalanceAccount(task);
        return taskId;
    }

    @RequestMapping(value = "/getTaskid", method = RequestMethod.POST)
    public String getTaskid() {
        String taskId = CurrentUser.getProfileId().toString() + CurrentUser.getEmployeeId().toString();
        return taskId;
    }


    @RequestMapping(value = "/DoDeletePaymentNumber", method = RequestMethod.POST)
    public String DoDeletePaymentNumber(@RequestBody PaymentFlowBatchOperateInfo parameter) {
        platformCheckService.DoDeletePaymentNumber(parameter);
        return "";
    }

    @RequestMapping(value = "/queryFinanceCheck", method = RequestMethod.POST)
    @PageDataSource
    public PageResponse<PlatformFinanceCheckPageData> QueryFinanceCheck(@RequestBody PageRequest<PlatformFinanceCheckRequest> parameter) {
        return platformCheckService.queryFinanceCheck(parameter);
    }
    @RequestMapping(value = "/queryFinanceCheckMain", method = RequestMethod.POST)
    @PageDataSource
    public PageResponse<PlatformFinanceCheckPageData> queryFinanceCheckMain(@RequestBody PageRequest<PlatformFinanceCheckRequest> parameter) {
        return platformCheckService.queryFinanceCheckMain(parameter);
    }

    @RequestMapping(value = "/calculateInAndOut", method = RequestMethod.POST)
    public List<CheckAccountStatistics> calculateInAndOut(@RequestBody PlatformFinanceCheckRequest parameter) {
        List<CheckAccountStatistics> res = platformCheckService.calculateInAndOut(parameter);
        return res;
    }

    @RequestMapping(value = "/getFinanceCheckDetailInfo", method = RequestMethod.POST)
    public FinanceCheckDetailInfo getFinanceCheckDetailInfo(@RequestBody PlatformFinanceCheckRequest parameter) {
        return platformCheckService.getFinanceCheckDetailInfo(CurrentUser.getProfileId(), parameter);
    }

    @RequestMapping(value = "/queryStoreFinanceCheck", method = RequestMethod.POST)
    @PageDataSource
    public PageResponse<StoreFinanceCheckPageData> queryStoreFinanceCheck(@RequestBody PageRequest<StoreFinanceCheckRequest> parameter) {
        return platformCheckService.queryStoreFinanceCheck(parameter);
    }

    @RequestMapping(value = "/getStoreFinanceCheckDetailInfo", method = RequestMethod.POST)
    public List<EshopPaymentFlowEntity> getStoreFinanceCheckDetailInfo(@RequestBody StoreFinanceCheckRequest request) {
        if (!request.isNeedAllPaymentNumber()) {
            if (StringUtils.isEmpty(request.getBillNumber())) {
                return new ArrayList<>();
            }
        }
        return platformCheckService.getStoreFinanceCheckDetailInfo(request.getProfileId(), request.getOtypeId(), request.getBillNumber(), request.getPaymentNumber());
    }

    @RequestMapping(value = "/deleteBindFlow", method = RequestMethod.POST)
    public void deleteBindFlow(@RequestBody EshopPaymentFlowEntity request) {
        platformCheckService.deleteBindFlow(request.getProfileId(), request.getId());
    }

    @RequestMapping(value = "/deleteFlowBindBillRelation", method = RequestMethod.POST)
    public void deleteFlowBindBillRelation(@RequestBody CapitalPaymetFlowResponse request) {
        platformCheckService.deleteFlowBindBillRelation(request);
    }

    /**
     * 查询单据
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/getStoreFinanceCheckFlowInfo", method = RequestMethod.POST)
    public List<EshopPaymentFlowEntity> getStoreFinanceCheckFlowInfo(@RequestBody StoreFinanceCheckRequest request) {
        return platformCheckService.getStoreFinanceCheckFlowInfo(request.getProfileId(), request.getOtypeId(), request.getBillNumber(), request.getPaymentNumber());
    }

    /**
     * 查询未对账的流水
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryBill", method = RequestMethod.POST)
    public PageResponse<CapitalPaymetFlowResponse> queryBill(@RequestBody PageRequest<CapitalPaymetFlowRequest> request) {
        CapitalPaymetFlowRequest queryParams = request.getQueryParams();
        PageDevice.initPage(request);
        return PageDevice.readPage(platformCheckService.queryBill(queryParams));
    }
    @PostMapping(value = "/queryBillCount")
    public int queryBillCount(@RequestBody PageRequest<CapitalPaymetFlowRequest> request) {
        CapitalPaymetFlowRequest queryParams = request.getQueryParams();
        PageDevice.initPage(request);
        return platformCheckService.queryBill(queryParams).size();
    }

    @PostMapping(value = "/doCreateBtype")
    public BigInteger doCreateBtype(@RequestBody String btypeName) {
        BigInteger btypeId = BigInteger.ZERO;
        BaseBtype baseBtype = new BaseBtype();
        baseBtype.setBcategory(1);
        baseBtype.setFullname(btypeName);
        baseBtype.setUsercode(UId.newId().toString());
        baseBtype.setAccType(1);
        Btype btypeByName = baseInfoMapper.getBtypeByName(CurrentUser.getProfileId(), btypeName);
        if (null == btypeByName) {
            GeneralResult<Object> generalResult = baseApi.saveBtype(baseBtype);
            if (generalResult.getCode() == 200) {
                btypeId = JsonUtils.toObject(JsonUtils.toJson(generalResult.getData()), BigInteger.class);
            }
        } else {
            btypeId = btypeByName.getId();
        }
        return btypeId;
    }

    /**
     * 流水绑定单据
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/flowBindBill", method = RequestMethod.POST)
    public void flowBindBill(@RequestBody List<CapitalPaymetFlowResponse> request) {
        platformCheckService.flowBindBill(request);
    }

    @RequestMapping(value = "/updateFlowBillNumber", method = RequestMethod.POST)
    public void updateFlowBillNumber(@RequestBody List<EshopPaymentFlowEntity> request) {
        platformCheckService.updateFlowBillNumber(request);
    }


    @RequestMapping(value = "/updateMarkInfoByOrderNo", method = RequestMethod.POST)
    public boolean updateMarkInfoByOrderNo(@RequestBody PlatformFinanceCheckRequest parameter) {
        return platformCheckService.updateMarkInfoByOrderNo(parameter.getId(), parameter.getMarkInfo());

    }

    @RequestMapping(value = "/queryDiffFeeBalance", method = RequestMethod.POST)
    @PageDataSource
    public PageResponse<DiffFeeBalanceResponse> QueryDiffFeeBalance(@RequestBody PageRequest<DiffFeeBalanceRequest> parameter) {
        return PageDevice.readPage(platformCheckService.QueryDiffFeeBalance(parameter.getQueryParams()));
    }


    /**
     * 多对一的对账
     *
     * @param parameter
     * @return
     */
    @RequestMapping(value = "/doDiffFeeBalanceNew", method = RequestMethod.POST)
    public String doDiffFeeBalanceNew(@RequestBody DiffFeeBalanceResponseNew parameter) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        platformCheckService.DoDiffFeeBalanceNew(processLogger, parameter);
        return taskId;
    }


    @RequestMapping(value = "/DoFinanceAccountImport", method = RequestMethod.POST)
    public String importByExcel(MultipartFile loadfile, EshopSaleOrderImportRequest importTask) {
        String name = loadfile.getOriginalFilename();
        importTask.setAuto(false);
        if (!StringUtils.endsWithIgnoreCase(name, ".xls") && !StringUtils.endsWith(name, ".xlsx")) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        String taskId = UId.newId().toString();
        importTask.setTaskId(taskId);
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        importTask.setProcessLogger(processLogger);
        if (importTask.getTempplateId() == null
                || StringUtils.isEmpty(importTask.getTempplateId().toString())
                || importTask.getTempplateId().compareTo(BigInteger.ZERO) == 0) {
            importTask.setOrderTemplates(EShopSaleOrderImportConfigManager.GetFinanceAccountTemplates());
        }
        ThreadPool threadPool = ThreadPoolFactory.build("platofrmcheck-balance");
        threadPool.executeAsync(task -> {
            try {
                platformCheckService.asyncImportFinanceAccount(loadfile.getInputStream(), importTask);
            } catch (Exception e) {

            } finally {
                processLogger.doFinish();
            }
        }, importTask);

        return importTask.getTaskId();
    }


    @RequestMapping(value = "/DoFinanceStoreBillImport", method = RequestMethod.POST)
    public String DoFinanceStoreBillImport(MultipartFile loadfile, EshopSaleOrderImportRequest importTask) {
        String name = loadfile.getOriginalFilename();
        importTask.setAuto(false);
        if (!StringUtils.endsWithIgnoreCase(name, ".xls") && !StringUtils.endsWith(name, ".xlsx")) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        String taskId = UId.newId().toString();
        importTask.setTaskId(taskId);
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        importTask.setProcessLogger(processLogger);
        if (importTask.getTempplateId() == null
                || StringUtils.isEmpty(importTask.getTempplateId().toString())
                || importTask.getTempplateId().compareTo(BigInteger.ZERO) == 0) {
            importTask.setOrderTemplates(EShopSaleOrderImportConfigManager.GetFinanceStoreTemplates());
        }
        processLogger.appendMsg("开始导入!");
        ThreadPool threadPool = ThreadPoolFactory.build("platofrmcheck-balance");
        threadPool.executeAsync(task -> {
            try {
                platformCheckService.asyncImportFinanceStoreBill(loadfile.getInputStream(), importTask);
            } catch (Exception e) {

            } finally {
                processLogger.appendMsg("导入完成!");
                processLogger.doFinish();
            }
        }, importTask);

        return importTask.getTaskId();
    }

    @RequestMapping(value = "/DoFinanceAccountImportNew", method = RequestMethod.POST)
    public String DoFinanceAccountImportNew(MultipartFile loadfile1, EshopSaleOrderImportRequest importTask) throws IOException {
        String name = loadfile1.getOriginalFilename();
        importTask.setAuto(true);
        if (!StringUtils.endsWithIgnoreCase(name, ".xls") && !StringUtils.endsWith(name, ".xlsx")) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        String taskId = UId.newId().toString();
        importTask.setTaskId(taskId);
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        importTask.setProcessLogger(processLogger);
        if (importTask.getTempplateId() == null
                || StringUtils.isEmpty(importTask.getTempplateId().toString())
                || importTask.getTempplateId().compareTo(BigInteger.ZERO) == 0) {
            importTask.setOrderTemplates(EShopSaleOrderImportConfigManager.GetFinanceAccountTemplates());
        }
        ThreadPool threadPool = ThreadPoolFactory.build("platofrmcheck-balance");
        threadPool.executeAsync(task -> {
            try {
                platformCheckService.asyncImportFinanceAccount(loadfile1.getInputStream(), importTask);
            } catch (Exception e) {

            } finally {
                processLogger.doFinish();
            }
        }, importTask);
        return importTask.getTaskId();
    }

    @RequestMapping(value = "/CapitalPaymentFlowImport", method = RequestMethod.POST)
    public String CapitalPaymentFlowImport(MultipartFile loadfile, CapitalPaymentFlowImportRequest importTask) throws IOException {
        String name = loadfile.getOriginalFilename();
        String taskId = UId.newId().toString();
        importTask.setTaskId(taskId);
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        processLogger.appendMsg("开始导入资金流水");
        if (!StringUtils.endsWithIgnoreCase(name, ".xls") && !StringUtils.endsWith(name, ".xlsx")) {
            processLogger.appendMsg("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
            processLogger.appendMsg("导入结束");
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }

        importTask.setProcessLogger(processLogger);
        ThreadPool threadPool = ThreadPoolFactory.build("platofrmcheck-balance");
        threadPool.executeAsync(invokes -> {
            try {
                platformCheckService.asyncImportCapitalPaymentFlow(loadfile.getInputStream(), importTask);
                processLogger.appendMsg("导入资金流水结束");
                processLogger.doFinish();
            }catch (Exception e){
                logger.error("导入出错"+e.getMessage());
                logger.error("导入出错"+e);
                processLogger.doFinish();
            }
        }, "批量导入资金流水");

        return importTask.getTaskId();
    }

    @RequestMapping(value = "/InitSystemFlowAtypeMapInfo", method = RequestMethod.POST)
    public void InitSystemFlowAtypeMapInfo() {
        platformCheckService.InitSystemFlowAtypeMapInfo(CurrentUser.getProfileId());
    }

/*    @RequestMapping(value = "/buildCheckAccount", method = RequestMethod.POST)
    public List<CheckAccount> buildCheckAccount(@RequestBody PlatformFinanceCheckRequest parameter) {
        List<PlatformFinanceCheckPageData> dataList = JSONArray.parseArray(parameter.getData(),
				PlatformFinanceCheckPageData.class);
        return platformCheckService.buildCheckAccount(dataList, parameter.getCheckAccountsStatus());
    }*/

    @PostMapping(value = "/downloadSaleItem")
    @ResponseBody
    public void downloadRelationModal(HttpServletResponse response,
                                      @RequestBody List<EshopSaleItemExcelEntity> entity) {
        try {
            String excelName = "交易项目失败数据";
            List<EshopSaleItemExcelEntity> saleItemList = new ArrayList<>();
            String platName = "";
            for (EshopSaleItemExcelEntity en : entity) {
                if (!en.isStatus()) {
                    saleItemList.add(en);
                }
            }
            InputStream templateFileName = FileUtil.class.getClassLoader().getResourceAsStream("static/eshoporder" +
                    "/template/ErrorData.xls");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode(excelName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
            // 多sheel导出
            ExcelWriter excelWriter = EasyExcel.write(new BufferedOutputStream(response.getOutputStream())).build();
            WriteSheet test1 = EasyExcel.writerSheet(0, excelName).head(EshopSaleItemExcelEntity.class).build();
            excelWriter.write(saleItemList, test1);
            excelWriter.finish();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/importSaleItemExcel")
    public String importExcel(MultipartFile file, int platform) throws IOException {
        String taskId = UId.newId().toString();
        BufferedInputStream buffInputStream = new BufferedInputStream(file.getInputStream());
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        platformCheckService.processImportAndRelation(buffInputStream, platform, taskId, processLogger);
        return taskId;
    }

    @RequestMapping(value = "/queryFinanceCheckGroup", method = RequestMethod.POST)
    @PageDataSource
    public PageResponse<FinanceCheckGroupRes> queryFinanceCheckGroup(@RequestBody PageRequest<FinanceCheckGroupReq> parameter) {
        return PageDevice.readPage(platformCheckService.queryFinanceCheckGroup(parameter.getQueryParams()));
    }

    @RequestMapping(value = "/deleteFinanceCheckGroup", method = RequestMethod.POST)
    public void deleteFinanceCheckGroup(@RequestBody PageRequest<FinanceCheckGroupReq> parameter) {
        platformCheckService.deleteFinanceCheckGroup(parameter.getQueryParams());
    }

    @RequestMapping(value = "/queryFinanceCheckGroupPage", method = RequestMethod.POST)
    @PageDataSource
    public PageResponse<FinanceCheckGroupRes> queryFinanceCheckGroupPage(@RequestBody PageRequest<FinanceCheckGroupReq> parameter) {
        return PageDevice.readPage(platformCheckService.queryFinanceCheckGroupPage(parameter.getQueryParams()));
    }

    @RequestMapping(value = "/createFinanceCheckGroup", method = RequestMethod.POST)
    public boolean createFinanceCheckGroup(@RequestBody PageRequest<FinanceCheckGroupReq> parameter) {
        return platformCheckService.createFinanceCheckGroup(parameter.getQueryParams());
    }

    @RequestMapping(value = "/createColumNameModel", method = RequestMethod.POST)
    public boolean createColumNameModel(@RequestBody PageRequest<ColumNameModelEntity> parameter) {
        return platformCheckService.createColumNameModel(parameter.getQueryParams());
    }

    @RequestMapping(value = "/queryColumNameModel", method = RequestMethod.POST)
    public PageResponse<ColumNameModelEntity> queryColumNameModel(@RequestBody PageRequest<ColumNameModelEntity> parameter) {
        return PageDevice.readPage(platformCheckService.queryColumNameModel(parameter.getQueryParams()));
    }

    @RequestMapping(value = "/queryColumNameModelSingle", method = RequestMethod.POST)
    public ColumNameModelEntity queryColumNameModelSingle(@RequestBody PageRequest<ColumNameModelEntity> parameter) {
        return platformCheckService.queryColumNameModelSingle(parameter.getQueryParams());
    }

    @RequestMapping(value = "/querytemplateName", method = RequestMethod.POST)
    public List<ColumNameModelEntity> querytemplateName() {
        return platformCheckService.querytemplateName();
    }

    @RequestMapping(value = "/DeleteColumNameModel", method = RequestMethod.POST)
    public boolean DeleteColumNameModel(@RequestBody PageRequest<ColumNameModelEntity> parameter) {
        return platformCheckService.DeleteColumNameModel(parameter.getQueryParams());
    }

    @RequestMapping(value = "/queryPaymentFlow", method = RequestMethod.POST)
    @PageDataSource
    public PageResponse<CapitalPaymetFlowResponse> queryPaymentFlow(@RequestBody PageRequest<CapitalPaymetFlowRequest> parameter) {
        return platformCheckService.queryFlowAndBill(parameter);
    }

    /**
     * 作废标记
     *
     * @param parameter
     * @return
     */
    @RequestMapping(value = "/canceledProcessState", method = RequestMethod.POST)
    public String canceledProcessState(@RequestBody List<CapitalPaymetFlowResponse> parameter) {
        return platformCheckService.canceledProcessState(parameter);
    }

    /**
     * 删除流水
     *
     * @param parameter
     * @return
     */
    @RequestMapping(value = "/doDeleteFlow", method = RequestMethod.POST)
    public void doDeleteFlow(@RequestBody List<CapitalPaymetFlowResponse> parameter) {
        platformCheckService.doDeleteFlow(parameter);
    }

    /**
     * 线下收款
     */
    @RequestMapping(value = "/asyncSaveSaleOrderGatherInfo", method = RequestMethod.POST)
    public String saveSaleOrderGatherInfo(@RequestBody PaymentFlowBatchOperateInfo orderGatherInfo) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        try {
            ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.DOWNLOAD_ORDER_THREAD_NAME);
            threadPool.executeAsync(invoker -> {
                long sTime = System.currentTimeMillis();
                platformCheckService.saveSaleOrderGatherInfo(orderGatherInfo, processLogger);
                System.out.println("线下收款总耗时：" + (System.currentTimeMillis() - sTime) + "ms,一共处理了"
                        + orderGatherInfo.getPaymentFlowList().size() + "条订单");
                processLogger.doFinish();
            }, null);
        } catch (RuntimeException e) {
            processLogger.appendMsg(String.format("线下收款处理异常,信息:%s", e.getMessage()));
            processLogger.doFinish();
        }
        return taskId;
    }

    /**
     * 线下付款
     */
    @RequestMapping(value = "/asyncSaveRefundPaymentInfo", method = RequestMethod.POST)
    public String asyncSaveRefundPaymentInfo(@RequestBody EshopRefundDoPaymentRequest refundPaymentRequest) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        try {
            ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.DOWNLOAD_ORDER_THREAD_NAME);
            threadPool.executeAsync(invoker -> {
                long sTime = System.currentTimeMillis();
                platformCheckService.saveRefundPaymentInfo(refundPaymentRequest, processLogger);
                System.out.println("线下付款总耗时：" + (System.currentTimeMillis() - sTime) + "ms,一共处理了"
                        + refundPaymentRequest.getPaymentFlowList().size() + "条订单");
                processLogger.doFinish();
            }, null);
        } catch (RuntimeException e) {
            processLogger.appendMsg(String.format("线下付款处理异常,信息:%s", e.getMessage()));
            processLogger.doFinish();
        }
        return taskId;
    }

    /**
     * 审核账单
     */
    @RequestMapping(value = "/doAuditPaymentNumber", method = RequestMethod.POST)
    public String DoAuditPaymentNumber(@RequestBody CheckAccountAuditParams parameter) {
        platformCheckService.DoAuditPaymentNumber(parameter);
        return "";
    }

    @RequestMapping(value = "/checkIsAllConfirm", method = RequestMethod.POST)
    public boolean checkIsAllConfirm(@RequestBody List<PlatformFinanceCheckPageData> items) {
        return platformCheckService.checkIsAllConfirm(items);
    }
    /**
     * 平台对账
     */
    @RequestMapping(value = "/asyncDoPlatformCheck", method = RequestMethod.POST)
    public String saveSaleOrderGatherInfo(@RequestBody DoPlatformCheckRequest request) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        try {
            ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.DOWNLOAD_ORDER_THREAD_NAME);
            threadPool.executeAsync(invoker -> {
                platformCheckService.doPlatformCheck(request, processLogger);
            }, null);
        } catch (RuntimeException e) {
            processLogger.appendMsg(String.format("手工对账处理异常,信息:%s", e.getMessage()));
            processLogger.doFinish();
        }
        return taskId;
    }

    /**
     * 手工对账 推荐对账 系统对账
     */
    @RequestMapping(value = "/savePlatformCheckStatus", method = RequestMethod.POST)
    public String savePlatformCheckStatus(@RequestBody List<StoreFinanceCheckPageData> request) {
        return platformCheckService.savePlatformCheckStatus(request);
    }

    @GetMapping("/CheckAccountsGet/{eshopOrderId}")
    public List<PlatformCheckAccountsPageData> getCloseReason(@PathVariable BigInteger eshopOrderId) {
        return platformCheckService.queryAccountsByEshopOrderId(eshopOrderId);
    }


    @GetMapping("/nameDuplicated/{templateName}")
    public boolean nameDuplicated(@PathVariable String templateName) {
        return platformCheckService.nameDuplicated(templateName);
    }

    @RequestMapping(value = "/columNameConfig", method = RequestMethod.POST)
    public BaseResponse columNameConfig(@RequestBody SysData sysData) {
        platformCheckService.columNameConfig(sysData);
        return new BaseResponse(true, "新增配置成功");
    }

    @GetMapping("/listColumNameConfig")
    public List<SysData> listColumNameConfig() {
        return platformCheckService.listColumNameConfig(null);
    }

    @GetMapping("/listColumNameConfigById/{id}")
    public List<SysData> listColumNameConfigById(@PathVariable BigInteger id) {
        return platformCheckService.listColumNameConfig(id);
    }

    @RequestMapping(value = "/doSaleOrderGather", method = RequestMethod.POST)
    public String doDiffFeeBalanceNew(@RequestBody EshopSaleOrderGatherRequest parameter) {
        List<EshopSaleOrderGatherInfo> gatherList = parameter.getOrderList();
        return platformCheckService.DoSaleOrderGather(gatherList, OrderOpreateType.ORDER_GATHER);
    }

    @RequestMapping(value = "/DoGatherOrderImport", method = RequestMethod.POST)
    public String DoGatherOrderImport(MultipartFile loadfile, EshopSaleOrderImportRequest importTask) {
        String name = loadfile.getOriginalFilename();
        importTask.setAuto(false);
        if (!StringUtils.endsWithIgnoreCase(name, ".xls") && !StringUtils.endsWith(name, ".xlsx")) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        try {
            if (null == loadfile.getInputStream()) {
                throw new NullPointerException("the inputStream is null!");
            }
        }catch (Exception e)
        {
            throw new NullPointerException("the inputStream is null!");
        }
        String taskId = UId.newId().toString();
        importTask.setTaskId(taskId);
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        importTask.setProcessLogger(processLogger);
        if (importTask.getTempplateId() == null
                || StringUtils.isEmpty(importTask.getTempplateId().toString())
                || importTask.getTempplateId().compareTo(BigInteger.ZERO) == 0) {
            importTask.setOrderTemplates(EShopSaleOrderImportConfigManager.GetSaleOrderImportTemplates());
        }
        ThreadPool threadPool = ThreadPoolFactory.build("platofrmcheck-balance");
        threadPool.executeAsync(task -> {
            try {
                platformCheckService.asyncImportSaleOrderGatherInfo(loadfile.getInputStream(), importTask);
            } catch (Exception e) {
                logger.error(String.format("DoGatherOrderImport error: %s  ,profileId:%s",e.getMessage(),CurrentUser.getProfileId()));
            } finally {
                processLogger.doFinish();
            }
        }, importTask);

        return importTask.getTaskId();

    }

    @RequestMapping(value = "/DoDeleteSaleOrderGatherNumber", method = RequestMethod.POST)
    public String DoDeleteSaleOrderGatherNumber(@RequestBody EshopSaleOrderGatherRequest parameter) {
        return platformCheckService.DoDeleteSaleOrderGatherNumber(parameter);
    }

    @PostMapping("/saleOrderImportDate")
    @ResponseBody
    public void downloadErrorDate(HttpServletResponse response, @RequestBody List<GatherOrderImportInfoList> entities) {
        try {
            String excelName = "导入收款错误数据";
            List<EshopSaleOrderGatherImportEntity> orderList = new ArrayList();
            for (GatherOrderImportInfoList en : entities) {
                if (!en.isStatus()) {
                    EshopSaleOrderGatherImportEntity item = new EshopSaleOrderGatherImportEntity();
                    item.setTradeId(en.getTradeId());
                    item.setPayNo(en.getPayNo());
                    item.setGatherTotal(en.getGatherTotal());
                    item.setRefundNumber(en.getRefundNumber());
                    item.setMessage(en.getMessage());
                    orderList.add(item);
                }
            }
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(excelName, "UTF-8");
            //response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + DateUtils.formatDate(new Date(), "yyyyMMdd") + ".xlsx");
            ExcelWriter excelWriter = EasyExcel.write(new BufferedOutputStream(response.getOutputStream())).build();
            WriteSheet test1 = EasyExcel.writerSheet(0, "导入收款结果").head(EshopSaleOrderGatherImportEntity.class).build();
            excelWriter.write(orderList, test1);
            excelWriter.finish();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
