package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.platform.dto.vip.VipCustomerEntity;
import com.wsgjp.ct.sale.platform.sdk.entity.callback.SliceDownloadCallbackResult;
import com.wsgjp.ct.sale.platform.sdk.entity.request.VipCustomerDownloadRequest;
import com.wsgjp.ct.sale.platform.sdk.service.EshopVipDownloadService;
import com.wsgjp.ct.sale.web.bifrost.request.VipCustomerRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "会员/会员用户相关接口")
@RestController
@RequestMapping("${app.id}/bifrost/vip")
public class BifrostEshopVipController {
    private final EshopVipDownloadService vipService;
    private static final Logger LOGGER = LoggerFactory.getLogger(BifrostEshopVipController.class);
    public BifrostEshopVipController(EshopVipDownloadService vipService) {
        this.vipService = vipService;
    }

    @ApiOperation("获取会员用户列表")
    @PostMapping("getVipCustomers")
    public void getVipCustomers(@RequestBody VipCustomerRequest request) {
        VipCustomerDownloadRequest serviceRequest = new VipCustomerDownloadRequest();
        serviceRequest.setShopId(request.getShopId());
        serviceRequest.setShopType(request.getShopType());
        serviceRequest.setStartTime(request.getBeginTime());
        serviceRequest.setEndTime(request.getEndTime());
        serviceRequest.setCallback(this::printResult);
        vipService.downloadVipCustomersByCreateTime(serviceRequest);
    }
    
    private void printResult(SliceDownloadCallbackResult<VipCustomerEntity> results){
        LOGGER.info("获取会员用户列表result"+JsonUtils.toJson(results));
    }
}
