package com.wsgjp.ct.sale.web.jarvis.request;

import com.wsgjp.ct.sale.biz.jarvis.log.common.OperationEnum;

import java.math.BigInteger;

/**
 * <p>Title: BillLogRequest </p>
 * <p>Description: 订单日志 </p>
 *
 * <AUTHOR>
 * @date 2020-08-17
 */
public class BillLogRequest {
    private BigInteger vchcode;
    private BigInteger warehouseTaskId;
    private OperationEnum operation;
    private String content;

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public OperationEnum getOperation() {
        return operation;
    }

    public void setOperation(OperationEnum operation) {
        this.operation = operation;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public BigInteger getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(BigInteger warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }
}
