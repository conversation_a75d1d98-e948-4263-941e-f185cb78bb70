package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.BatchAddPtypeResult;
import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.BatchPrintConfigPtypeDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.BatchPrintPtypeQueryParamDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.DeliverPrintBatchConfigDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.StrategySaveCheckResponse;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.PrintBatchConfigPriorityParam;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.PrintBatchConfigQueryParam;
import com.wsgjp.ct.sale.biz.jarvis.service.printbatch.DeliverPrintBatchConfigService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wangkai
 * @Date: 2020-03-12 10:33
 */
@Api(description = "打单波次策略")
@RestController
@RequestMapping("/${app.id}/jarvis/PrintBatchConfig")
public class DeliverPrintBatchConfigController {
    private DeliverPrintBatchConfigService configService;

    public DeliverPrintBatchConfigController(DeliverPrintBatchConfigService configService) {
        this.configService = configService;
    }

    @ApiOperation(value = "添加波次策略", notes = "添加波次策略")
    @PostMapping("/add")
    public DeliverPrintBatchConfigDTO add(@RequestBody DeliverPrintBatchConfigDTO configDTO) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        this.configService.insert(profileId, employeeId, configDTO);
        DeliverPrintBatchConfigDTO config = this.configService.getConfig(profileId, employeeId, configDTO.getId());
        return config;
    }


    @ApiOperation(value = "更新波次策略优先级", notes = "更新波次策略优先级")
    @PostMapping("/changePriority")
    void changePriority(@RequestBody PrintBatchConfigPriorityParam param) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        this.configService.changePriority(profileId, employeeId, param);
    }

    @ApiOperation(value = "更新波次策略", notes = "更新波次策略")
    @PostMapping("/update")
    void update(@RequestBody List<DeliverPrintBatchConfigDTO> configDTOs) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        this.configService.update(profileId, employeeId, configDTOs);
    }

    @ApiOperation(value = "修改波次策略", notes = "修改波次策略")
    @PostMapping("/modify")
    DeliverPrintBatchConfigDTO modify(@RequestBody DeliverPrintBatchConfigDTO configDTO) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        this.configService.modify(profileId, employeeId, configDTO);
        DeliverPrintBatchConfigDTO config = this.configService.getConfig(profileId, employeeId, configDTO.getId());
        return config;
    }

    @ApiOperation(value = "删除波次策略", notes = "删除波次策略")
    @PostMapping("/delete")
    @Transactional(rollbackFor = RuntimeException.class)
    void delete(@RequestBody List<BigInteger> ids) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        this.configService.delete(profileId, employeeId, ids);


        List<DeliverPrintBatchConfigDTO> list = configService.list(new PrintBatchConfigQueryParam(), profileId, employeeId);

        List<DeliverPrintBatchConfigDTO> configDTOs = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            DeliverPrintBatchConfigDTO configDTO = list.get(i);
            DeliverPrintBatchConfigDTO deliverPrintBatchConfigDTO = new DeliverPrintBatchConfigDTO();
            deliverPrintBatchConfigDTO.setId(configDTO.getId());
            deliverPrintBatchConfigDTO.setPriority(i + 1);
            deliverPrintBatchConfigDTO.setConfigName(configDTO.getConfigName());
            configDTOs.add(deliverPrintBatchConfigDTO);
        }

        update(configDTOs);
    }

    @ApiOperation(value = "获取一条波次策略", notes = "获取一条波次策略")
    @PostMapping("/getConfig/{id}")
    DeliverPrintBatchConfigDTO getConfig(@PathVariable("id") BigInteger id) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        DeliverPrintBatchConfigDTO config = this.configService.getConfig(profileId, employeeId, id);
//        String json = JsonUtils.toJson(config);
        return config;
    }

    @ApiOperation(value = "查询波次策略列表", notes = "查询波次策略列表")
    @PostMapping("/list")
    public List<DeliverPrintBatchConfigDTO> list(@RequestBody PrintBatchConfigQueryParam param) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        List<DeliverPrintBatchConfigDTO> list = configService.list(param, profileId, employeeId);
        return list;
    }

    @PostMapping("/hasRepeatableOtypeId")
    @ApiOperation(value = "新增时检查是否有重复的销售机构")
    public StrategySaveCheckResponse hasRepeatableOtypeId(@RequestBody DeliverPrintBatchConfigDTO configDTO) {
        return configService.hasRepeatableOtypeId(configDTO);
    }

    @ApiOperation(value = "批量插入包含或不包含商品", notes = "批量插入包含或不包含商品")
    @PostMapping("/batchAddPtype")
    public List<BatchAddPtypeResult> batchAddPtype(@RequestBody List<BatchPrintConfigPtypeDTO> ptypeDTOS) {
        ptypeDTOS.forEach(p -> {
            p.setId(UId.newId());
            p.setProfileId(CurrentUser.getProfileId());
        });
        return configService.batchAddPtype(ptypeDTOS);
    }


    @ApiOperation(value = "获取策略Id", notes = "新生成策略Id")
    @PostMapping("/getConfigId")
    BigInteger getConfigId() {
        return UId.newId();
    }

    @ApiOperation(value = "获取策略Id", notes = "新生成策略Id")
    @PostMapping("/getPtypes")
    public List<BatchPrintConfigPtypeDTO> getPtypes(@RequestBody BatchPrintPtypeQueryParamDTO paramDTO) {
        paramDTO.setProfileId(CurrentUser.getProfileId());
        return configService.getPtypes(paramDTO);
    }

    @ApiOperation(value = "通过id删除包含与不包含商品信息", notes = "通过id删除包含与不包含商品信息")
    @PostMapping("/deletePtypeById")
    public void deletePtypeById(List<BigInteger> ids) {
        configService.deletePtypeById(CurrentUser.getProfileId(), ids);
    }

    @ApiOperation(value = "通过策略id删除包含与不包含商品信息", notes = "通过策略id删除包含与不包含商品信息")
    @GetMapping("/deletePtypeByConfig")
    public void deletePtypeById(BigInteger configId) {
        configService.deletePtypeByConfig(CurrentUser.getProfileId(), configId);
    }


}
