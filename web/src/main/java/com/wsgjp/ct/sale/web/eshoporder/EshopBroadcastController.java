package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.BroadcastSessionDto;
import com.wsgjp.ct.sale.biz.eshoporder.entity.broadcast.LiveBroadcastStockUpRecordEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.broadcast.PtypeTypeRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.broadcast.PtypeTypeRespone;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.LiveBroadcastStockUpRecordParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.BroadcastSessionRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryBasePtypeParameter;
import com.wsgjp.ct.sale.biz.eshoporder.log.LiveBroadcastStockUpRecordLog;
import com.wsgjp.ct.sale.biz.eshoporder.log.LiveBroadcastStockUpRecordQueryParams;
import com.wsgjp.ct.sale.biz.eshoporder.service.broadcast.EshopBroadcastService;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "直播相关接口")
@RequestMapping("${app.id}/eshoporder/broadcast")
@RestController
public class EshopBroadcastController {

    private final EshopBroadcastService service;

    public EshopBroadcastController(EshopBroadcastService service) {

        this.service = service;
    }

    @PostMapping(value = "/QueryBroadcastSession")
    @PageDataSource
    public PageResponse<BroadcastSessionDto> QueryBroadcastSession(@RequestBody PageRequest<BroadcastSessionRequest> param) {
        PageResponse<BroadcastSessionDto> pgResp= PageDevice.readPage(service.QueryBroadcastSession(param.getQueryParams()));
        return pgResp;
    }
    @PostMapping(value = "/SaveBroadcastSession")
    public String SaveBroadcastSession(@RequestBody BroadcastSessionDto param) {
        return service.SaveBroadcastSession(param);
    }
    @PostMapping(value = "/ModifyBroadcastSession")
    public String ModifyBroadcastSession(@RequestBody BroadcastSessionDto param) {
        return service.ModifyBroadcastSession(param);
    }
    @PostMapping(value = "/DeleteBroadcastSession")
    public String DeleteBroadcastSession(@RequestBody List<BroadcastSessionDto> params) {

        String errorMsg=service.DeleteSessionCheckAndDelete(params);
        if (StringUtils.isNotEmpty(errorMsg))
        {
            return errorMsg;
        }

        return "";
    }

    /**
     * 查询选品记录主表
     * @param param
     * @return {@link PageResponse}<{@link LiveBroadcastStockUpRecordEntity}>
     */
    @PostMapping(value = "/queryLiveBroadcastStockUpRecord")
    public PageResponse<LiveBroadcastStockUpRecordEntity> queryLiveBroadcastStockUpRecord(@RequestBody PageRequest<LiveBroadcastStockUpRecordParam> param) {
        return service.query(param);
    }

    /**
     * 查询选品记录明细
     * @param id
     * @return {@link LiveBroadcastStockUpRecordEntity}
     */
    @GetMapping(value = "/queryLiveBroadcastStockUpRecordDetail/{id}")
    public LiveBroadcastStockUpRecordEntity queryLiveBroadcastStockUpRecordDetail(@PathVariable("id") BigInteger id) {
        return service.queryLiveBroadcastStockUpRecordDetail(id);
    }

    /**
     * 逻辑删除选品记录
     * @param id
     */
    @GetMapping(value = "/deleteLiveBroadcastStockUpRecord/{id}")
    public void deleteLiveBroadcastStockUpRecord(@PathVariable("id") BigInteger id) {
         service.delete(id);
    }

    /**
     * 明细页面初始化数据
     * @param id
     * @return {@link LiveBroadcastStockUpRecordEntity}
     */
    @GetMapping(value = "/liveBroadcastStockUpRecordEditInit/{id}")
    public LiveBroadcastStockUpRecordEntity liveBroadcastStockUpRecordEditInit(@PathVariable("id") BigInteger id) {
        return service.editInit(id);
    }

    /**
     * 打印完成，记录日志
     * @param id
     */
    @GetMapping(value = "/saveLiveBroadcastStockUpRecordPrintLog/{id}")
    public void saveLiveBroadcastStockUpRecordPrintLog(@PathVariable("id") BigInteger id) {
         service.buildAndSaveLog(id,"打印了标签");
    }

    /**
     * 查询商品的分类信息
     * @return {@link PageResponse}<{@link LiveBroadcastStockUpRecordEntity}>
     */
    @PostMapping(value = "/queryPtypeType")
    public List<PtypeTypeRespone> queryPtypeType(@RequestBody PtypeTypeRequest ptypeTypeRequest) {
        return service.queryPtypeType(ptypeTypeRequest);
    }


    /**
     * 保存选品记录
     * @param entity
     */
    @PostMapping(value = "/saveLiveBroadStockUpRecord")
    public String saveLiveBroadStockUpRecord(@RequestBody LiveBroadcastStockUpRecordEntity entity) {
         return service.savePtypeXcodeAndLiveBroadStockUpRecord(entity);
    }

    /**
     * 日志查询
     * @param request
     * @return {@link PageResponse}<{@link LiveBroadcastStockUpRecordLog}>
     */
    @PostMapping(value = "/queryLogs")
    public PageResponse<LiveBroadcastStockUpRecordLog> queryLogs(@RequestBody PageRequest<LiveBroadcastStockUpRecordQueryParams> request) {
        return SysLogUtil.query(request);
    }


    @PostMapping(value = "/getCombInfoByBarCode")
    public List<Ptype> getCombInfoByBarCode(@RequestBody QueryBasePtypeParameter param) {
        return service.getCombInfoByBarCode(param);
    }

}
