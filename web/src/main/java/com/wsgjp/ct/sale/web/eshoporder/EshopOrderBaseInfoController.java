package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryBarcodeIdParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryBaseTypeParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryDistributionBalanceTaxedPriceParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.SimpleSaleOrderDetail;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import io.swagger.annotations.Api;
import ngp.loadbalancer.context.RouteThreadLocal;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;

@Api(tags = "基础信息")
@RequestMapping("${app.id}/eshoporder/baseinfo")
@RestController
public class EshopOrderBaseInfoController {
	private final EshopOrderBaseInfoService eshopOrderBaseInfoService;

	public EshopOrderBaseInfoController(EshopOrderBaseInfoService eshopOrderBaseInfoService) {
		this.eshopOrderBaseInfoService = eshopOrderBaseInfoService;
	}


	@GetMapping("/getAtypeList")
	public List<Atype> getAtypeList() {
		BigInteger profileId = RouteThreadLocal.getRoute().getProfileId();
		return eshopOrderBaseInfoService.getAtypeList(profileId);
	}

	@GetMapping("/getBtypeList/{freighted}")
	public List<Btype> getBtypeList(@PathVariable Integer freighted) {
		BigInteger profileId = RouteThreadLocal.getRoute().getProfileId();
		return eshopOrderBaseInfoService.getBtypeList(profileId, freighted);
	}

	@GetMapping("/getETypeList")
	public List<Etype> getETypeList() {
		BigInteger profileId = RouteThreadLocal.getRoute().getProfileId();
		return eshopOrderBaseInfoService.getEtypeList(profileId);
	}

	@GetMapping("/getKTypeList")
	public List<Stock> getKTypeList() {
		return eshopOrderBaseInfoService.getStockLimited();
	}

	@PostMapping
	public BaseBarcode getBarcodeId(@RequestBody QueryBarcodeIdParameter parameter){
		return eshopOrderBaseInfoService.queryBaseBarcode(parameter);
	}

	@ResponseBody
	@GetMapping("/getBtypeInfos/{freighted}")
	public List<Btype> getBtypeList(@PathVariable int freighted) {
		BigInteger profileId = RouteThreadLocal.getRoute().getProfileId();
		return eshopOrderBaseInfoService.getBtypeList(profileId, freighted);
	}

	@PostMapping("/getEtype")
	public Etype getEtype(@RequestBody QueryBaseTypeParameter parameter){
		return eshopOrderBaseInfoService.getEtype(parameter);
	}

	@GetMapping(value = "/getFreightTemplates")
	public List<LogisticsTemplate>  getFreightTemplates()
	{
		return eshopOrderBaseInfoService.getFreightTemplates(null);
	}


	@GetMapping("/getBtypeByBtypename/{btypeName}")
	public Btype getBtypeList(@PathVariable String btypeName) {
		BigInteger profileId = RouteThreadLocal.getRoute().getProfileId();
		return eshopOrderBaseInfoService.getBtypeByName(profileId, btypeName);
	}


	@PostMapping("/getDistributionBalanceTaxedPrice")
	public List<SimpleSaleOrderDetail> getDistributionBalanceTaxedPrice(@RequestBody QueryDistributionBalanceTaxedPriceParameter parameter) {
		return eshopOrderBaseInfoService.getDistributionBalanceTaxedPrice(parameter);
	}
}
