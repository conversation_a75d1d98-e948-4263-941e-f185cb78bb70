package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.entity.Buyer;
import com.wsgjp.ct.sale.biz.jarvis.log.JarvisLogServiceHelper;
import com.wsgjp.ct.sale.biz.jarvis.log.common.OperationEnum;
import com.wsgjp.ct.sis.client.SisClient;
import com.wsgjp.ct.sis.client.entity.EncryptFullAdapter;
import com.wsgjp.ct.sis.client.utils.SisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description TODO
 * @Date 2020-04-14 14:49
 * @Created by lingxue
 */
@RestController
@Api(description = "买家信息")
@RequestMapping("/${app.id}/jarvis/buyer")
public class JarvisBuyerController {
    @ApiOperation(value = "解密收货人信息", notes = "解密收货人信息")
    @PostMapping("decrypt")
    public Buyer decrypt(@RequestBody DecryptBuyerRequest request) throws Exception {
        if(StringUtils.isEmpty(request.getBuyer().getDi()) )
        {
            return request.getBuyer();
        }
        List<EncryptFullAdapter> requestBuyer = new ArrayList<>();
        requestBuyer.add(request.getBuyer());
        SisClient.batchDecrypt(requestBuyer);
        if (request.getWarehouseTaskId() != null) {
            JarvisLogServiceHelper.addWarehouseTaskLog(OperationEnum.VIEW_BUYER,null, JarvisLogServiceHelper.getWarehouseLogInfo(Arrays.asList(request.getWarehouseTaskId())), "查看买家信息");
        }
//        uploadToPlatformDecryptLog(request);
        return request.getBuyer();
    }

    /*private void uploadToPlatformDecryptLog(DecryptBuyerRequest request) {
        UploadOrderInfoRequest orderInfoRequest = new UploadOrderInfoRequest();
        orderInfoRequest.setShopType(getPlatformName(request.getBuyer().getOtypeId()));
        orderInfoRequest.setOperation("查询订单");
        orderInfoRequest.setOrderIds(JarvisLogService.getBillNumber(request.getVchcode()).getTradeId());
        SysPlatformLog.add(orderInfoRequest);
    }*/

    private String getPlatformName(BigInteger orgId) {
        return SisUtils.getSisShopType(SisClient.getShopType(orgId)).getName();
    }
}

class DecryptBuyerRequest {
    private BigInteger vchcode;
    private BigInteger warehouseTaskId;
    private Buyer buyer;

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public Buyer getBuyer() {
        return buyer;
    }

    public void setBuyer(Buyer buyer) {
        this.buyer = buyer;
    }

    public BigInteger getWarehouseTaskId() {
        return warehouseTaskId;
    }

    public void setWarehouseTaskId(BigInteger warehouseTaskId) {
        this.warehouseTaskId = warehouseTaskId;
    }
}
