package com.wsgjp.ct.sale.web.eshoporder;

import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.QiniuUploadResult;
import com.wsgjp.ct.sale.biz.eshoporder.entity.productpublish.UploadFIleToPlatformRequest;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.config.PlatformEshopConfig;
import com.wsgjp.ct.sale.platform.entity.request.product.PlatImageUploadRequest;
import com.wsgjp.ct.sale.platform.entity.response.product.PlatImageUploadResponse;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.sdk.service.EshopPublishProductService;
import com.wsgjp.ct.support.config.QiniuConfig;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.utils.QiniuUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/20 0020 20:18
 */
@Api(tags = "公用信息相关")
@RequestMapping("${app.id}/eshoporder/filesManager")
@RestController
public class FilesManagerController {

    private QiniuConfig qiniuConfig;
    private EshopPublishProductService productService;
    private final PlatformEshopConfig platformEshopConfig;
    private final BifrostEshopService eshopService;


    public FilesManagerController(QiniuConfig qiniuConfig, EshopPublishProductService productService, PlatformEshopConfig platformEshopConfig, BifrostEshopService eshopService) {
        this.qiniuConfig = qiniuConfig;
        this.productService = productService;
        this.platformEshopConfig = platformEshopConfig;
        this.eshopService = eshopService;
    }


    @RequestMapping(value = "/qiniu/uploadconfig", method = RequestMethod.POST)
    public Map<String, Object> getQiniuToken(@RequestParam(required = false) String[] filenames, @RequestParam(required = false) Boolean privated, @RequestParam(required = false) Long expire) {
        return getQiniuConfig(filenames, privated, expire, 0);
    }

    private Map<String, Object> getQiniuConfig(String[] filenames, Boolean privated, Long expire, int fileChekType) {
        List<String> keys = new ArrayList<>();
        if (filenames != null && filenames.length > 0) {
            for (int i = 0; i < filenames.length; i++) {
                String file = filenames[i];
                String ext = file.substring(file.lastIndexOf('.') + 1);
                ext = ext.toLowerCase();
                fileTypeCheckError(fileChekType, ext);
                String key = MessageFormat.format("{0}/{1}.{2}", CurrentUser.getProfileId().toString(), UId.newId().toString(), ext);
                keys.add(key);
            }
        } else {
            throw new RuntimeException("filenames不能为空");
        }
        String bucket = qiniuConfig.getBucket();
        if (privated != null && privated) {
            //私有空间
            bucket = qiniuConfig.getPrivateBucket();
        }
        Auth auth = Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey());
        StringMap putPolicy = new StringMap();
        putPolicy.put("returnBody", "{\"key\":\"$(key)\",\"hash\":\"$(etag)\",\"bucket\":\"$(bucket)\",\"fsize\":$(fsize)}");
        //1M=1048576Byte  这里10M最大约束 todo 是否考虑配置
        putPolicy.put("fsizeLimit", qiniuConfig.getFsizeLimit());
        putPolicy.put("mimeLimit", "image/*");
        long expireSeconds = 3600;
        if (expire != null) {
            expireSeconds = expire.longValue();
        }
        String upToken = auth.uploadToken(bucket, null, expireSeconds, putPolicy);
        Map<String, Object> rs = new HashedMap<>();
        rs.put("uptoken", upToken);
        rs.put("domain", "http://" + QiniuUtils.getFirstDomain(bucket));
        rs.put("keys", keys);
        rs.put("thumbnailCode", qiniuConfig.getThumbnailCode());
        rs.put("region", qiniuConfig.getRegion());
        return rs;
    }

    /**
     * 0=图片检查，1=所有文件类型
     *
     * @param type
     */
    private void fileTypeCheckError(int type, String ext) {
        //jpg，jpeg，gif，png，bmp
        if (type == 0) {
            if (!Arrays.asList("jpg", "jpeg", "jfif", "gif", "png", "bmp").contains(ext)) {
                throw new RuntimeException("仅支持文件格式：jpg，jpeg，jfif，gif，png，bmp");
            }
        }
    }


    @ApiOperation("七牛文件上传")
    @RequestMapping(value = "/qiniu/upload", method = RequestMethod.POST)
    public QiniuUploadResult qiniuUpload(MultipartFile file) {
        String key = "";
        byte[] data;
        try {
            data = file.getBytes();
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("文件解析失败：" + e.getMessage());
        }
        String filename = file.getOriginalFilename();
        String prefix = filename.substring(filename.lastIndexOf(".") + 1);
        key = MessageFormat.format("{0}/{1}.{2}", CurrentUser.getProfileId(), UId.newId().toString(), prefix);
        QiniuUtils.upload(key, data);
        QiniuUploadResult resultVo = new QiniuUploadResult();
        resultVo.setDomain(QiniuUtils.getDomainUrl(qiniuConfig.getDomain()));
        resultVo.setThumbnail(QiniuUtils.getThumbnail(key));
        resultVo.setKey(key);
        resultVo.setUrl(QiniuUtils.getFullUrl(key));
        resultVo.setOriginalFilename(filename);
        return resultVo;
    }

    /**
     * 富文本上传图片/视频到七牛
     */
    @ApiOperation(" 富文本上传图片/视频到七牛")
    @RequestMapping(value = "/file/htmlEditorQiNiuUpload", method = RequestMethod.POST)
    public List<String> htmlEditorQiNiuUpload(@RequestParam("Filedata") List<MultipartFile> filedataList, @RequestParam("type") String type) {
        List<String> imageUrls = new ArrayList<>();
        String typeName = StringUtils.equals(type, "image") ? "图片" : "视频";
        for (MultipartFile file : filedataList) {
            fileValidate(type, file);
            String key = getFileName(file.getOriginalFilename(), typeName);
            QiniuUtils.upload(key, getFileBytes(file));
            imageUrls.add(QiniuUtils.getFullUrl(key));
        }
        return imageUrls;
    }

    private byte[] getFileBytes(MultipartFile file) {
        try {
            return file.getBytes();
        } catch (Exception ex) {
            throw new RuntimeException("上传失败，请重试");
        }
    }

    private String getFileName(String originalFilename, String typeName) {
        if (StringUtils.isBlank(originalFilename)) {
            throw new RuntimeException(String.format("存在%s名为空!", typeName));
        }
        return MessageFormat.format("{0}/{1}.{2}", String.valueOf(CurrentUser.getProfileId()), String.valueOf(UId.newId()), this.getFileFormat(originalFilename));
    }

    private String getFileFormat(String originalFilename) {
        if (StringUtils.isBlank(originalFilename)) {
            throw new RuntimeException("未获取到文件名称，请删除重新上传!");
        }
        return originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
    }

    /**
     * 富文本上传图片/视频到各电商平台
     */
    @ApiOperation(" 富文本上传图片/视频到各电商平台")
    @RequestMapping(value = "/file/htmlEditorPlatformUpload", method = RequestMethod.POST)
    public List<String> htmlEditorPlatformUpload(@RequestParam("Filedata") List<MultipartFile> fileDataList, @RequestParam("type") String type, @RequestParam("eshopId") String eshopId) {
        if (StringUtils.isBlank(eshopId)) {
            throw new RuntimeException("必须先选择绑定的网店!");
        }
        EshopInfo eshopInfo = eshopService.getEshopInfoById(CurrentUser.getProfileId(), new BigInteger(eshopId));
        if (eshopInfo == null) {
            throw new RuntimeException("未查询到店铺信息");
        }
        PlatformBaseConfig config = EshopFactoryManager.getConfig(eshopInfo.getEshopType());
        List<String> imageUrls = new ArrayList<>();
        String typeName = StringUtils.equals(type, "image") ? "图片" : "视频";
        for (MultipartFile fileData : fileDataList) {
            fileValidate(type, fileData, config);
            PlatImageUploadRequest platReq = new PlatImageUploadRequest();
            platReq.setImageCateId("");
            platReq.setImgName(this.getFileName(fileData.getOriginalFilename(), typeName));
            platReq.setShopId(BigInteger.valueOf(Long.parseLong(eshopId)));
            platReq.setImageBytes(getFileBytes(fileData));
            platReq.setNeedFullImageUrl(true);
            PlatImageUploadResponse response = productService.imageUpload(platReq);
            if (response.getSuccess()) {
                imageUrls.add(response.getImgUrl());
            } else {
                throw new RuntimeException(response.getMessage());
            }
        }
        return imageUrls;
    }

    private void fileTypeValidate(String type) {
        if (!StringUtils.equals("image", type) && !StringUtils.equals("media", type)) {
            throw new RuntimeException("只能上传图片或视频文件!");
        }
    }

    private void fileValidate(String type, MultipartFile fileData) {
        fileTypeValidate(type);
        if (StringUtils.equals("image", type)) {
            doFileValidate(fileData, platformEshopConfig.getImageSizeLimit(), platformEshopConfig.getImageFormatLimits(), "图片");
        } else {
            doFileValidate(fileData, platformEshopConfig.getVideoSizeLimit(), platformEshopConfig.getVideoFormatLimits(), "视频");
        }
    }

    private void fileValidate(String type, MultipartFile fileData, PlatformBaseConfig config) {
        fileTypeValidate(type);
        if (StringUtils.equals("image", type)) {
            doFileValidate(fileData, config.getImageSizeLimit(), config.getImageFormatLimits(), "图片");
        } else {
            doFileValidate(fileData, config.getVideoSizeLimit(), config.getVideoFormatLimits(), "视频");
        }
    }

    private void doFileValidate(MultipartFile fileData, double sizeLimit, List<String> formatLimits, String typeName) {
        if (fileData == null) {
            return;
        }
        String originalFilename = fileData.getOriginalFilename();
        if (fileData.getSize() > sizeLimit * 1024 * 1024) {
            throw new RuntimeException(String.format("%s【%s】上传失败！%s大小不能超过%sMB！", typeName, originalFilename, typeName, sizeLimit));
        }
        String fileFormat = this.getFileFormat(fileData.getOriginalFilename());
        if (CollectionUtils.isNotEmpty(formatLimits) && !formatLimits.contains(fileFormat)) {
            throw new RuntimeException(String.format("%s【%s】上传失败！不支持上传%s格式的%s！", typeName, originalFilename, fileFormat, typeName));
        }
    }

    @ApiOperation("七牛文件上传")
    @RequestMapping(value = "/file/uploadToPlatform", method = RequestMethod.POST)
    public PlatImageUploadResponse uploadToPlatform(@RequestBody UploadFIleToPlatformRequest request) {
        PlatImageUploadRequest platReq = new PlatImageUploadRequest();
        platReq.setImageCateId("");
        platReq.setImageUrl(request.getPicUrl());
        platReq.setImgName(request.getFileName());
        platReq.setShopId(request.getEshopId());
        platReq.setNeedFullImageUrl(request.getNeedFullImageUrl());
        platReq.setUploadType(request.getUploadType());
        return productService.imageUpload(platReq);
    }
}
