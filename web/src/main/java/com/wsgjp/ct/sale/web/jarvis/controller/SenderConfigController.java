package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.SenderConfigBatchRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.SenderConfigDAO;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.SenderConfigQueryParam;
import com.wsgjp.ct.sale.biz.jarvis.service.SenderConfigService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/${app.id}/jarvis/SenderConfigController")
public class SenderConfigController {

    private SenderConfigService senderConfigService;

    public SenderConfigController(SenderConfigService senderConfigService) {
        this.senderConfigService = senderConfigService;
    }

    @RequestMapping("set")
    public void set(@RequestBody List<SenderConfigDAO> request) {
        senderConfigService.set(request);
    }

    @RequestMapping("save")
    public void save(@RequestBody List<SenderConfigDAO> request) {
        senderConfigService.save(request);
    }

    @RequestMapping("batchSave")
    public void batchSave(@RequestBody SenderConfigBatchRequest request) {
        senderConfigService.batchSave(request);
    }

    @RequestMapping("list")
    public List<SenderConfigDAO> list(@RequestBody SenderConfigQueryParam request) {
        return senderConfigService.list(request);
    }

}
