package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.entity.OrderMarkInfo;
import com.wsgjp.ct.common.enums.core.enums.MarkShowType;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.redis.process.message.bll.RedisMessageUtil;
import com.wsgjp.ct.sale.biz.bifrost.entity.BaseInfoLog;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopPluginService;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderSysDataConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstant;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.UnitPrice;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.SpeedModeOperatingFrequency;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderMentionDropItem;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeDownloadTask;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryXcodeParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ProcessMessageInfo;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopStockSyncActionLog;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.CustomerQueryService;
import com.wsgjp.ct.sale.biz.eshoporder.service.receiver.ReceiveManager;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.common.config.EshopOrderCommonConfig;
import com.wsgjp.ct.sale.platform.dto.order.entity.OrderInvoice;
import com.wsgjp.ct.sale.platform.dto.plugin.DropDownPlugin;
import com.wsgjp.ct.sale.platform.entity.request.order.GetInvoiceRequest;
import com.wsgjp.ct.sale.platform.entity.request.plugin.CommonRequest;
import com.wsgjp.ct.sale.platform.sdk.service.EshopOrderService;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.BaseLogQueryParams;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.NormalRequest;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.stock.StockSyncActionLogQueryParams;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.config.QiniuConfig;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.IndustryConfig;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.redis.factory.CacheType;
import com.wsgjp.ct.support.resolveaddress.entity.ReceiveInfo;
import io.swagger.annotations.Api;
import ngp.redis.RedisPoolFactory;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/2/20 0020 20:18
 */
@Api(tags = "公用信息相关")
@RequestMapping("${app.id}/eshoporder/common")
@RestController
public class CommonController {

    private final EshopOrderBaseInfoService service;
    private final CustomerQueryService queryService;
    private final BifrostEshopPluginService eshopPluginService;
    private final EshopOrderCommonConfig eshopOrderCommonConfig;
    private final ServiceConfig config;

    private final RedisPoolFactory redisPoolFactory;
    private final EshopOrderService orderService;
    private QiniuConfig qiniuConfig;

    public CommonController(EshopOrderBaseInfoService service, CustomerQueryService queryService, BifrostEshopPluginService eshopPluginService, EshopOrderCommonConfig eshopOrderCommonConfig, ServiceConfig config, RedisPoolFactory redisPoolFactory, EshopOrderService orderService, QiniuConfig qiniuConfig) {
        this.service = service;
        this.queryService = queryService;
        this.eshopPluginService = eshopPluginService;
        this.eshopOrderCommonConfig = eshopOrderCommonConfig;
        this.config = config;
        this.redisPoolFactory = redisPoolFactory;
        this.orderService = orderService;
        this.qiniuConfig = qiniuConfig;
    }

    @RequestMapping(value = "/stopProcessMsg", method = RequestMethod.POST)
    public void stopProcessMsg(@RequestBody NormalRequest request) {
        String processId = request.getRequestStr();
        ProcessLoggerImpl pLogger = new ProcessLoggerImpl(processId);
        pLogger.appendMsg("正在执行终止，请稍后");
        pLogger.appendMsg("终止完成");
        pLogger.doFinish();
    }

    @RequestMapping(value = "/getProcessMsg", method = RequestMethod.POST)
    public ProcessMessageInfo getProcessMsg(@RequestBody NormalRequest request) {
        String processId = request.getRequestStr();
        ProcessMessageInfo messageInfo = new ProcessMessageInfo();
        try {
            String processMsg = RedisMessageUtil.getProcessMsg(processId);
            boolean finish = RedisMessageUtil.getFinish(processId);
            String percentPre = EshopOrderConst.DOWNLOAD_ORDER_PROCESS_PERCENT_PRE;
            String resPre = EshopOrderConst.DOWNLOAD_ORDER_PROCESS_RESULT;
            String submitType = EshopOrderConst.DOWNLOAD_ORDER_PROCESS_SUBMIT_TYPE;

            String percentStr = RedisMessageUtil.get(String.format("%s%s", percentPre, processId));
            String resultStr = RedisMessageUtil.get(String.format("%s%s", resPre, processId));
            String submitStr = RedisMessageUtil.get(String.format("%s%s", submitType, processId));
            if (StringUtils.isNotEmpty(percentStr)) {
                messageInfo.setPercent(
                        CommonUtil.getStringToInteger(percentStr) > 100 ? 0 : CommonUtil.getStringToInteger(percentStr));
            }
            if (StringUtils.isNotEmpty(submitStr)) {
                messageInfo.setSubmitType(CommonUtil.getStringToInteger(submitStr));
            }
            String successCount = RedisMessageUtil.get(String.format("%s%s", StringConstant.PROCESS_SUCCESS_COUNT_KEY, processId));
            String failedCount = RedisMessageUtil.get(String.format("%s%s", StringConstant.PROCESS_FAIL_COUNT_KEY, processId));
            messageInfo.setSuccessCount(CommonUtil.getStringToInteger(successCount));
            messageInfo.setFailedCount(CommonUtil.getStringToInteger(failedCount));
            messageInfo.setSuccess(StringUtils.isNotEmpty(resultStr));
            messageInfo.setCompleted(finish);
            messageInfo.setMessage(processMsg);
        } catch (Exception ex) {
            messageInfo.setMessage(ex.getMessage());
        }
        return messageInfo;
    }


    @RequestMapping(value = "/getErrorMsg", method = RequestMethod.POST)
    public ProcessMessageInfo getErrorMsg(@RequestBody NormalRequest request) {
        String processId = request.getRequestStr();
        ProcessMessageInfo messageInfo = new ProcessMessageInfo();
        try {
            String processMsg = RedisMessageUtil.getProcessMsg(processId);
            messageInfo.setMessage(processMsg);

        } catch (Exception ex) {
            messageInfo.setMessage(ex.getMessage());
        }
        return messageInfo;
    }

    @RequestMapping(value = "/getProcess", method = RequestMethod.POST)
    public ProcessMessageInfo getProcess(@RequestBody NormalRequest request) {
        String processId = request.getRequestStr();
        ProcessMessageInfo messageInfo = new ProcessMessageInfo();
        try {
            String processMsg = RedisMessageUtil.getProcessMsg(processId);
            boolean finish = RedisMessageUtil.getFinish(processId);
            messageInfo.setSuccess(StringUtils.isNotEmpty(processMsg));
            messageInfo.setCompleted(finish);
            messageInfo.setMessage(processMsg);
        } catch (Exception ex) {
            messageInfo.setMessage(ex.getMessage());
        }
        return messageInfo;
    }

    @RequestMapping(value = "/resolveAddress", method = RequestMethod.POST)
    public ReceiveInfo resolveAddress(@RequestBody NormalRequest request) {
        return ReceiveManager.resolve(request.getRequestStr());
    }

    @RequestMapping(value = "/getOrderState", method = RequestMethod.POST)
    public List<DropDownPlugin> getOrderStateByEshopType(@RequestBody NormalRequest request) {
        List<DropDownPlugin> result = new ArrayList<>();
        ShopType type = ShopType.valueOf(request.getIntId());
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(type);
        List<DropDownPlugin> statusList = eshopPluginService.orderDownloadSupport(commonRequest);
        DropDownPlugin kvp = new DropDownPlugin(999, "全部", true);
        result.add(kvp);
        if (statusList == null || statusList.size() == 0) {
            return result;
        }
        return statusList;
    }

    @RequestMapping(value = "/saveEditFormCloseType/{value}", method = RequestMethod.GET)
    public void saveEditFormCloseType(@PathVariable int value) {
        EshopOrderSysDataConfig sysDataConfig = GlobalConfig.get(EshopOrderSysDataConfig.class);
        sysDataConfig.setEditFormCloseType(value);
        GlobalConfig.putAll(sysDataConfig);
    }

    @GetMapping(value = "/getEshopOrderSysData")
    public EshopOrderSysDataConfig getEshopOrderConfig() {
        return GlobalConfig.get(EshopOrderSysDataConfig.class);
    }

    @RequestMapping(value = "/getEnabledProps", method = RequestMethod.GET)
    public Boolean getEnabledProps() {
        IndustryConfig config = GlobalConfig.get(IndustryConfig.class);
        return config.isEnabledProps();
    }

    @RequestMapping(value = "/getBaseUnit", method = RequestMethod.POST)
    public List<UnitPrice> getBaseUnitIdList(@RequestBody NormalRequest request) {
        return service.getBaseUnitByIds(request.getProfileId(), request.getIdList());
    }

    @RequestMapping(value = "/getBaseXcode", method = RequestMethod.POST)
    public PtypeXcode getBasePtypeXcode(@RequestBody QueryXcodeParameter parameter) {
        return service.getPtypeXcode(parameter);
    }

    @GetMapping(value = "/addSysLog/{info}")
    public void addSysLog(@PathVariable String info) {
        PubSystemLogService.saveInfo(info);
    }

    @GetMapping(value = "/getMentionItems")
    public List<OrderMentionDropItem> getMentionDropList() {
        return queryService.queryDropItems();
    }

    @PostMapping(value = "/queryOtypeLog")
    public PageResponse<BaseInfoLog> queryOtypeLog(@RequestBody PageRequest<BaseLogQueryParams> request) {
        if (request.getQueryParams().getBeginTime() == null) {
            request.getQueryParams().setBeginTime(CommonUtil.getAnyDayBeginTime(30));
        }
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        return LogService.query(request);
    }

    @PostMapping(value = "/queryStockActionLog")
    public PageResponse<EshopStockSyncActionLog> queryStockActionLog(@RequestBody PageRequest<StockSyncActionLogQueryParams> request) {
        if (request.getQueryParams().getBeginTime() == null) {
            request.getQueryParams().setBeginTime(CommonUtil.getAnyDayBeginTime(30));
        }
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        return LogService.query(request);
    }

    @PostMapping("/modifyPtypeDownloadTask")
    public void processPtypeDownloadTask(@RequestBody PtypeDownloadTask task) {
        task.setProfileId(CurrentUser.getProfileId());
        queryService.modifyPtypeDownloadTask(task);
    }

    @PostMapping("/stopProductCategoryProcessTask")
    public void stopProductCategoryProcessTask(@RequestBody PtypeDownloadTask task) {
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(task.getTaskid().toString());
        processLogger.doFinish();
    }

    @PostMapping("/insertPtypeDownloadTask")
    public void insertPtypeDownloadTask(@RequestBody PtypeDownloadTask task) {
        queryService.insertPtypeDownloadTask(task);
    }

    @PostMapping("/queryPtypeDownloadTask")
    public PtypeDownloadTask queryPtypeDownloadTask(@RequestBody PtypeDownloadTask task) {
        return queryService.queryPtypeDownloadTask(task);
    }


    @GetMapping(value = "/getEshopOrderMarkItems/{type}")
    public List<OrderMarkInfo> queryEshopOrderMarkItems(@PathVariable MarkShowType type) {
        return queryService.queryEshopOrderMarkItems(type);
    }

    @GetMapping("/getUnitPrice/{unitId}")
    public UnitPrice getUnitPrice(@PathVariable BigInteger unitId) {
        BigInteger profileId = CurrentUser.getProfileId();
        return service.getUnitPrice(profileId, unitId);
    }

    //是否大促模式
    @GetMapping(value = "/isSpeedMode")
    public Boolean isSpeedMode() {
        return eshopOrderCommonConfig.isSpeedMode();
    }

    //大促模式下操作频率
    @GetMapping(value = "/operatingFrequency")
    public Integer operatingFrequency() {
        return eshopOrderCommonConfig.getExpireTime();
    }

    //大促模式下批量操作数量
    @GetMapping(value = "/operatingNum")
    public Integer operatingNum() {
        return eshopOrderCommonConfig.getOperateNum();
    }

    //获取帮助地址
    @GetMapping(value = "/getHelpUrl")
    public Map<String, String> getHelpUrl(String key) {
        Map<String, String> map = new HashMap<>();
        String helpUrlConfig = config.getHelpUrlConfig();
        if (helpUrlConfig.length() > 0) {
            ArrayList<HashMap> list = JsonUtils.toList(helpUrlConfig, HashMap.class);
            for (HashMap hashMap : list) {
                String nextKey = (String) hashMap.keySet().iterator().next();
                Object value = hashMap.get(nextKey);
                map.put(nextKey, value.toString());
                continue;
            }
        }
        return map;
    }

    @GetMapping(value = "/isOpenDDGXNewFrontDecrypt")
    public boolean isOpenDDGXNewFrontDecrypt() {
        return eshopOrderCommonConfig.isOpenDDGXNewFrontDecrypt();
    }

    //大促期间操作频率是否被允许
    @GetMapping(value = "/isAllowOperationSpeedMode/{operateCode}/{otypeId}")
    public Boolean isAllowOperationSpeedMode(@PathVariable Integer operateCode, @PathVariable BigInteger otypeId) {
        if (eshopOrderCommonConfig.isSpeedMode()) {
            for (SpeedModeOperatingFrequency speedModeOperatingFrequency : SpeedModeOperatingFrequency.values()) {
                if (speedModeOperatingFrequency.getCode().equals(operateCode)) {
                    StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
                    String keyStr = (-1 == otypeId.intValue() ? CurrentUser.getProfileId() + speedModeOperatingFrequency.getKeyStr() : CurrentUser.getProfileId() + speedModeOperatingFrequency.getKeyStr() + otypeId);
                    if (Boolean.TRUE.equals(template.hasKey(keyStr))) {
                        return false;
                    } else if (Boolean.FALSE.equals(template.hasKey(keyStr))) {
                        template.opsForValue().set(keyStr, speedModeOperatingFrequency.getKeyStr(), eshopOrderCommonConfig.getExpireTime(), TimeUnit.MINUTES);
                        return true;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 插入系统级配置数据
     *
     * @param sysData
     * @return
     */
    @RequestMapping(value = "setSysData", method = RequestMethod.POST)
    public boolean setSysData(@RequestBody SysData sysData) {
        return service.addSysData(sysData);
    }

    /**
     * 批量插入系统级配置数据
     *
     * @param sysDatas
     * @return
     */
    @RequestMapping(value = "setSysDatas", method = RequestMethod.POST)
    public boolean setSysDatas(@RequestBody List<SysData> sysDatas) {
        boolean resultSta = true;
        for (SysData sysData : sysDatas) {
            resultSta = service.addSysData(sysData);
        }
        return resultSta;
    }

    @GetMapping("/getSysData/{sysDataKey}")
    public boolean getSysData(@PathVariable String sysDataKey) {
        if (StringUtils.isEmpty(sysDataKey)) {
            return false;
        }
        return service.getSysData(sysDataKey);
    }

    @RequestMapping(value = "setUserData", method = RequestMethod.POST)
    public boolean setUserData(@RequestBody List<SysData> userDataList) {
        boolean resultSta = true;
        for (SysData sysData : userDataList) {
            GlobalConfig.putUserData(sysData.getSubName(), sysData.getSubValue(), sysData.getDescription());
        }
        return resultSta;
    }

    @RequestMapping(value = "getOrderInvoice", method = RequestMethod.POST)
    public OrderInvoice getOrderInvoice(@RequestBody GetInvoiceRequest request) {
        OrderInvoice invoice = orderService.getInvoice(request);
        if (null == invoice) {
            throw new RuntimeException("平台发票信息为空");
        }
        return invoice;
    }

    @GetMapping("/killTask/{taskId}")
    public void forceKillTask(@PathVariable String taskId){
        if(StringUtils.isEmpty(taskId)){
            throw new RuntimeException("需要停止执行的taskId为空");
        }
        try {
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            String key = StringConstant.FORCE_KILL_TASK_KEY + taskId;
            template.opsForValue().set(key, taskId, 2, TimeUnit.HOURS);
        }catch (Exception ex){
            throw new RuntimeException("停止执行任务失败：" + ex.getMessage(), ex);
        }
    }
}
