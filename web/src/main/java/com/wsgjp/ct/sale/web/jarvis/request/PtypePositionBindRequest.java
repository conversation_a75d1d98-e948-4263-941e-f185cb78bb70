package com.wsgjp.ct.sale.web.jarvis.request;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class PtypePositionBindRequest {

    private BigInteger ktypeId;
    private BigInteger ptypeId;
    private BigInteger skuId;
    private String position;
    private String ktypeName;
    private boolean force;

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public BigInteger getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getKtypeName() {
        return ktypeName;
    }

    public void setKtypeName(String ktypeName) {
        this.ktypeName = ktypeName;
    }


    public boolean isForce() {
        return force;
    }

    public void setForce(boolean force) {
        this.force = force;
    }

}
