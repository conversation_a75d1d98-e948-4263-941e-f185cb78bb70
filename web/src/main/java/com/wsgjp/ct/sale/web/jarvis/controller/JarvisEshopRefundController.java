package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.entity.refund.DefaultKtypeAndAdmin;
import com.wsgjp.ct.sale.biz.jarvis.service.refund.JarvisEshopRefundService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "售后管理")
@RequestMapping("${app.id}/jarvis/refund")
@RestController
public class JarvisEshopRefundController {

    private JarvisEshopRefundService refundService;

    public JarvisEshopRefundController(JarvisEshopRefundService refundService) {
        this.refundService = refundService;
    }

    /**
     * 获取默认
     *
     * @return
     */
    @PostMapping("/getDefaultKtypeAndAdmin")
    public DefaultKtypeAndAdmin getDefaultKtypeAndAdmin() {
        return refundService.getDefaultKtypeAndAdmin();
    }
}
