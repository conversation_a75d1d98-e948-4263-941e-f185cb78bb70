package com.wsgjp.ct.sale.web.shopsale;

import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.sale.biz.member.model.entity.statistics.StoreStatisticsDto;
import com.wsgjp.ct.sale.biz.member.service.ISsStoreStatisticsService;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.report.BusinessPercentageDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.report.BusinessPercentageRequest;
import com.wsgjp.ct.sale.biz.shopsale.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.annontaion.NgpResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "shopsale报表相关")
@RequestMapping("${app.id}/shopsale/report")
@RestController

public class ReportController {

    @Autowired
    private ReportService reportService;

    @Autowired
    private ISsStoreStatisticsService ssStoreStatisticsService;

    @ApiOperation(value = "营业占比")
    @PostMapping(value = "/getBusinessPercentageList")
    @NgpResource(name = "shopsale.getBusinessPercentageList", tagStrings = "'tagA,'+0")
    List<BusinessPercentageDTO> getBusinessPercentageList(@RequestBody BusinessPercentageRequest params) {
        return reportService.getBusinessPercentageList(params);
    }

    @ApiOperation(value = "门店收入统计")
    @PostMapping(value = "/getStoreIncomeStatisticsList")
    PageInfo<StoreStatisticsDto> getStoreIncomeStatisticsList(@RequestBody StoreStatisticsDto params) {
        return ssStoreStatisticsService.getStoreIncomeStatisticsList(params);
    }

    @ApiOperation(value = "门店收入统计POS")
    @PostMapping(value = "/getStoreIncomeStatisticsListForPOS")
    StoreStatisticsDto getStoreIncomeStatisticsListForPOS(@RequestBody StoreStatisticsDto params) {
        return ssStoreStatisticsService.getStoreIncomeStatisticsListForPOS(params);
    }
}
