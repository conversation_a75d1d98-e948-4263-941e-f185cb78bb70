package com.wsgjp.ct.sale.web.member;


import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.member.model.dto.card.VipLevelRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.ChangeVipLevelDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.SelectLevelRequestDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.SelectLevelResponseDTO;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCardTemplate;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.SsVipLevel;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.SsVipLevelAssessPeriod;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.VipGrowthCompute;
import com.wsgjp.ct.sale.biz.member.service.ISsVipLevelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.text.ParseException;
import java.util.List;

@Api(tags = "等级相关")
@RequestMapping("${app.id}/member/vipLevel")
@RestController
public class SsVipLevelController {

    @Autowired
    ISsVipLevelService service;

    @ApiOperation(value = "VIP等级列表（分页）")
    @PostMapping(value = "/getAllSsVipLevelList")
    PageInfo<SelectLevelResponseDTO> getAllSsVipLevelList(@RequestBody SelectLevelRequestDTO request) throws ParseException {
        return service.getAllSsVipLevelList(request);
    }

    @ApiOperation(value = "VIP等级列表(不分页)")
    @PostMapping(value = "/getVipLevelList")
    @PermissionCheck(key = PermissionShopSale.MEMBER_LEVEL_VIEW)
    @WebLogs
    List<SelectLevelResponseDTO> getVipLevelList(@RequestBody SelectLevelRequestDTO request) {
        return service.getVipLevelList(request);
    }

    @ApiOperation(value = "新增一个Vip等级")
    @PostMapping(value = "/insertSsVipLevel")
    @PermissionCheck(key = PermissionShopSale.MEMBER_LEVEL_ADD)
    BigInteger insertSsVipLevel(@RequestBody VipLevelRequest requestParam) throws ParseException {
        return service.insertSsVipLevel(requestParam);
    }

    @ApiOperation(value = "更新一个Vip等级")
    @PostMapping(value = "/updateSsVipLevel")
    @PermissionCheck(key = PermissionShopSale.MEMBER_LEVEL_EDIT)
    Boolean updateSsVipLevel(@RequestBody VipLevelRequest requestParam) throws ParseException {
        return service.updateSsVipLevel(requestParam);
    }

    @ApiOperation(value = "获取最高等级")
    @PostMapping(value = "/getMaxVipLevel")
    @PermissionCheck(key = PermissionShopSale.MEMBER_LEVEL_VIEW)
    SsVipLevel getMaxVipLevel(@RequestBody Integer vipType) {
        return service.getMaxVipLevel(vipType);
    }

    @ApiOperation(value = "停止一个Vip等级")
    @PostMapping(value = "/stopSsVipLevel")
    Boolean stopSsVipLevel(@RequestBody SsVipLevel requestParam) throws ParseException {
        return service.stopSsVipLevel(requestParam);
    }

    @ApiOperation(value = "获取等级权益卡信息")
    @PostMapping(value = "/getCardByLevelId")
    SsCardTemplate getCardByLevelId(BigInteger id) {
        return service.getCardByLevelId(id);
    }

    @ApiOperation(value = "获取等级评估周期信息")
    @GetMapping("getLevelAssessPeriod")
    SsVipLevelAssessPeriod getLevelAssessPeriod() {
        return service.getLevelAssessPeriod();
    }

    @ApiOperation(value = "新增或修改等级评估周期信息")
    @PostMapping("saveOrUpdateLevelAssessPeriod")
    @PermissionCheck(key = PermissionShopSale.MEMBER_LEVEL_ASSESS)
    void saveOrUpdateLevelAssessPeriod(@RequestBody SsVipLevelAssessPeriod ssVipLevelAssessPeriod) {
        service.saveOrUpdateLevelAssessPeriod(ssVipLevelAssessPeriod);
    }

    @ApiOperation(value = "计算获得多少成长值")
    @PostMapping("growthCompute")
    Integer growthCompute(@RequestBody VipGrowthCompute compute) {
        return service.growthCompute(compute);
    }


    @ApiOperation(value = "修改会员等级")
    @PostMapping("changeVipLevelToSpecifiedLevel")
    @PermissionCheck(key = PermissionShopSale.MEMBER_VIP_MODIFYLEVEL)
    void changeVipLevelToSpecifiedLevel(@RequestBody ChangeVipLevelDTO request) {
        service.changeVipLevelToSpecifiedLevel(request);
    }

}
