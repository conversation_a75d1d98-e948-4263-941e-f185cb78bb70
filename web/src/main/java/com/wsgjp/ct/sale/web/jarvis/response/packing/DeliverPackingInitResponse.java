package com.wsgjp.ct.sale.web.jarvis.response.packing;

import com.wsgjp.ct.sale.biz.jarvis.config.DeliverPackingConfig;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Employee;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Freight;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DeliverPackingInitResponse {
    private Integer suspendBillCount;
    private Integer suspendPackingCount;
    private List<Employee> employees;
    private List<Freight> freightList;
    private DeliverPackingConfig config;
    private BigInteger employeeId;
    private BillDeliverDTO bill;
    private int qtyLength;
    private boolean serialEnabled;
    private boolean propEnabled;
    private boolean batchEnabled;
    private List<Organization> eshops;
    private List<DillDeliverState> packingTypes;
    private List<DillDeliverState> showProcessState;

    public Integer getSuspendBillCount() {
        return suspendBillCount;
    }

    public void setSuspendBillCount(Integer suspendBillCount) {
        this.suspendBillCount = suspendBillCount;
    }

    public Integer getSuspendPackingCount() {
        return suspendPackingCount;
    }

    public void setSuspendPackingCount(Integer suspendPackingCount) {
        this.suspendPackingCount = suspendPackingCount;
    }

    public List<Employee> getEmployees() {
        return employees;
    }

    public void setEmployees(List<Employee> employees) {
        this.employees = employees;
    }

    public DeliverPackingConfig getConfig() {
        return config;
    }

    public void setConfig(DeliverPackingConfig config) {
        this.config = config;
    }

    public BigInteger getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(BigInteger employeeId) {
        this.employeeId = employeeId;
    }

    public BillDeliverDTO getBill() {
        return bill;
    }

    public void setBill(BillDeliverDTO bill) {
        this.bill = bill;
    }

    public int getQtyLength() {
        return qtyLength;
    }

    public void setQtyLength(int qtyLength) {
        this.qtyLength = qtyLength;
    }

    public List<Freight> getFreightList() {
        return freightList;
    }

    public void setFreightList(List<Freight> freightList) {
        this.freightList = freightList;
    }

    public boolean isSerialEnabled() {
        return serialEnabled;
    }

    public void setSerialEnabled(boolean serialEnabled) {
        this.serialEnabled = serialEnabled;
    }

    public boolean isPropEnabled() {
        return propEnabled;
    }

    public void setPropEnabled(boolean propEnabled) {
        this.propEnabled = propEnabled;
    }

    public boolean isBatchEnabled() {
        return batchEnabled;
    }

    public void setBatchEnabled(boolean batchEnabled) {
        this.batchEnabled = batchEnabled;
    }

    public List<DillDeliverState> getPackingTypes() {
        return packingTypes;
    }

    public void setPackingTypes(List<DillDeliverState> packingTypes) {
        this.packingTypes = packingTypes;
    }

    public List<Organization> getEshops() {
        return eshops;
    }

    public void setEshops(List<Organization> eshops) {
        this.eshops = eshops;
    }

    public List<DillDeliverState> getShowProcessState() {
        return showProcessState;
    }

    public void setShowProcessState(List<DillDeliverState> showProcessState) {
        this.showProcessState = showProcessState;
    }
}
