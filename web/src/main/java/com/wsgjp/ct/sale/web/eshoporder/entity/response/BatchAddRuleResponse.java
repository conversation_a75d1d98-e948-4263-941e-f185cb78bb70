package com.wsgjp.ct.sale.web.eshoporder.entity.response;

import com.wsgjp.ct.sale.biz.eshoporder.entity.response.CheckErrorInfo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/1 19:48
 */
public class BatchAddRuleResponse {
	private List<CheckErrorInfo> errorList ;
	private List<StockSyncRule> successList;

	public List<CheckErrorInfo> getErrorList() {
		return errorList;
	}

	public void setErrorList(List<CheckErrorInfo> errorList) {
		this.errorList = errorList;
	}

	public List<StockSyncRule> getSuccessList() {
		return successList;
	}

	public void setSuccessList(List<StockSyncRule> successList) {
		this.successList = successList;
	}
}
