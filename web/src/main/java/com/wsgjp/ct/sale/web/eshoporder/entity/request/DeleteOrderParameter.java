package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import java.math.BigInteger;

/**
 * Created by dengcl on 2019-12-30.
 */
public class DeleteOrderParameter {
    private String tradeId;
    private BigInteger eshopId;
    private BigInteger profileId;

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    @Override
    public String toString() {
        return String.format("DeleteOrderParameter{tradeId='%s', eshopId=%s, profileId=%s}", tradeId, eshopId, profileId);
    }
}
