package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverSplitRequestDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.openwms.SplitBillDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.openwms.SplitBillDetailDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.openwms.WmsBillDeliverSplitBillRequestDTO;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverSendService;
import com.wsgjp.ct.sale.biz.jarvis.service.WmsSendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * created  by qhy on 2021-12-10
 */
@RestController
@RequestMapping("/${app.id}/jarvis/wmsSendController")
public class WmsSendController {
    private WmsSendService wmsSendService;
    private BillDeliverSendService billDeliverSendService;

    private Logger logger = LoggerFactory.getLogger(getClass());

    public WmsSendController(WmsSendService wmsSendService, BillDeliverSendService billDeliverSendService) {
        this.wmsSendService = wmsSendService;
        this.billDeliverSendService = billDeliverSendService;
    }

    /***
     * 我这边接口判断，如果是分批发货，会执行  拆单-》要发货的单据保存物流信息-》发货的单据执行发货
     * 如果是整单，会执行 修改明细信息-》执行发货   是否发货根据传入的发货vchcode执行。只执行发货，不执行核算过账
     * @param request
     * @return
     */
    /*@Deprecated
    @ApiOperation(value = "wms拆分发货", notes = "wms拆分发货 新")
    @PostMapping("/wmsSplitSend")
    public WmsApiResult wmsSplitSend(@RequestBody WmsSendParam request) {
        logger.error("== wms拆分发货开始 ==\r\n" + JsonUtils.toJson(request));
        //测试账号  exwms  wms  1
      //  request = JsonUtils.toObject("{\"sendVchcode\":703577198556512359,\"oldVchcode\":703577198556512359,\"splitInfo\":{\"vchcode\":703577198556512359,\"freightName\":\"申通e物流\",\"freightNo\":\"1639101978902\",\"detailList\":[{\"vchcode\":703577198556512359,\"detailId\":703577198556577895,\"splitQty\":2.00000000,\"serialNos\":[{\"id\":null,\"vchcode\":null,\"detailId\":null,\"inoutType\":\"OUT_STOCK\",\"ptypeId\":null,\"skuId\":null,\"ktypeId\":null,\"snno\":\"tyu\",\"sn1\":null,\"sn2\":null,\"snRemark\":\"出库单返回序列号\",\"batchno\":\"546\",\"produceDate\":\"2021-06-19 00:00:00\",\"expireDate\":\"2022-12-16 00:00:00\",\"batchId\":null}],\"batchno\":\"546\",\"produceDate\":\"2021-06-19 00:00:00\",\"expireDate\":\"2022-12-16 00:00:00\",\"ptypeId\":599638818194220995,\"newDetailId\":null},{\"vchcode\":703577198556512359,\"detailId\":703577198556577895,\"splitQty\":2.00000000,\"serialNos\":[{\"id\":null,\"vchcode\":null,\"detailId\":null,\"inoutType\":\"OUT_STOCK\",\"ptypeId\":null,\"skuId\":null,\"ktypeId\":null,\"snno\":\"uio\",\"sn1\":null,\"sn2\":null,\"snRemark\":\"出库单返回序列号\",\"batchno\":\"45678\",\"produceDate\":\"2021-06-19 00:00:00\",\"expireDate\":\"2022-12-16 00:00:00\",\"batchId\":null}],\"batchno\":\"45678\",\"produceDate\":\"2021-06-19 00:00:00\",\"expireDate\":\"2022-12-16 00:00:00\",\"ptypeId\":599638818194220995,\"newDetailId\":null}],\"oldVchcode\":null,\"vchtype\":\"SelfSaleOrder\",\"freightBtypeId\":622465929590091778},\"splitNotSendInfo\":null}\n", WmsSendParam.class);
        WmsApiResult result = new WmsApiResult();

        try {
            result = wmsSendService.wmsSend(CurrentUser.getProfileId(), request);
            if (request.getSendVchcode() != null) {
                // 获取发货单创建方式
                boolean isOnlineSend = billDeliverSendService.getBillDeliverOnlineSend(request);
                //调用发货
                List<RebuildMessageVo> postResults = billDeliverSendService.send(CurrentUser.getProfileId(), Arrays.asList(request.getSendVchcode()), CurrentUser.getEmployeeId(),isOnlineSend);
                if (postResults == null || postResults.size() == 0) {
                    result.setSuccess(true);
                    result.setMessage("确认出库成功");
                    logger.error("== wms拆分发货结束 ==\r\n" + JsonUtils.toJson(result));
                    return result;
                }
                RebuildMessageVo postResult = postResults.get(0);
                if ("ERROR".equals(postResult.getStatus()) || "WARN".equals(postResult.getStatus())) {
                    result.setSuccess(false);
                    result.setMessage("信息写入成功，发货出错！备注：" + postResult.getContent());
                }
                else{
                    result.setSuccess(true);
                    result.setMessage("确认出库成功");
                }

            }else{
                result.setSuccess(true);
                result.setMessage("确认修改成功");
            }
            logger.error("== wms拆分发货结束 ==\r\n" + JsonUtils.toJson(result));
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            result.setSuccess(false);
            result.setMessage("确认失败：" + ex.getMessage());
            logger.error("== wms拆分发货结束 ==\r\n" + JsonUtils.toJson(result));
            return result;
        }
    }*/
    private SplitBillDTO buildSplitBillDTO(BigInteger oldVchcode, WmsBillDeliverSplitBillRequestDTO splitInfo, Boolean isAdd) {
        SplitBillDTO splitBillDTO = new SplitBillDTO();
        List<SplitBillDetailDTO> detailList = new ArrayList<>();
        for (BillDeliverSplitRequestDTO billDeliverSplitRequestDTO : splitInfo.getDetailList()) {
            SplitBillDetailDTO splitBillDetailDTO = new SplitBillDetailDTO();
            splitBillDetailDTO.setDetailId(billDeliverSplitRequestDTO.getNewDetailId());
            splitBillDetailDTO.setOldDetailId(billDeliverSplitRequestDTO.getDetailId());
            splitBillDetailDTO.setPtypeId(billDeliverSplitRequestDTO.getPtypeId());
            splitBillDetailDTO.setUnitQty(billDeliverSplitRequestDTO.getSplitQty());
            detailList.add(splitBillDetailDTO);
        }

        splitBillDTO.setAdd(isAdd);
        splitBillDTO.setOldVchcode(oldVchcode);
        splitBillDTO.setVchcode(splitInfo.getVchcode());
        splitBillDTO.setDetailList(detailList);
        splitBillDTO.setVchtype(splitInfo.getVchtype());
        return splitBillDTO;
    }

}
