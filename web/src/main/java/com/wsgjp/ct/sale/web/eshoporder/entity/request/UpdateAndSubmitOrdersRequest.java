package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.sale.biz.eshoporder.submit.support.OrderInfoForUdate;
import com.wsgjp.ct.sale.biz.jarvis.dto.SubmitDeliverRelationDTO;

import java.util.List;

public class UpdateAndSubmitOrdersRequest {
    private boolean needUpdate;
    private List<OrderInfoForUdate> orderInfos;
    private List<SubmitDeliverRelationDTO> relations;

    public boolean isNeedUpdate() {
        return needUpdate;
    }

    public void setNeedUpdate(boolean needUpdate) {
        this.needUpdate = needUpdate;
    }

    public List<OrderInfoForUdate> getOrderInfos() {
        return orderInfos;
    }

    public void setOrderInfos(List<OrderInfoForUdate> orderInfos) {
        this.orderInfos = orderInfos;
    }

    public List<SubmitDeliverRelationDTO> getRelations() {
        return relations;
    }

    public void setRelations(List<SubmitDeliverRelationDTO> relations) {
        this.relations = relations;
    }
}
