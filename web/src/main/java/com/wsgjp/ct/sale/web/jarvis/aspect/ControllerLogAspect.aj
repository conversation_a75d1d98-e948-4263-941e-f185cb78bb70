package com.wsgjp.ct.jarvis.web.aspect;

import ngp.utils.JsonUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: wangkai
 * @Date: 2020-02-24 14:48
 */
@Aspect
@Component
public aspect ControllerLogAspect {
    private Logger logger = LoggerFactory.getLogger(ControllerLogAspect.class);

    @Pointcut("execution(public * com.wsgjp.ct.jarvis.web.controller.*(..))")
    public void controllerLog() {
    }

    @Before("controllerLog()")
    public void doBefore(JoinPoint joinPoint) throws Exception {
        writeLog(joinPoint);
        writeDetailLog(joinPoint);
    }


    private void writeLog(JoinPoint joinPoint) throws Exception {
        Signature signature = joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();
        logger.info(String.format("Controller服务层［%s］请求入参:%s", signature.getName(), JsonUtils.toJson(args)));
    }

    public void writeDetailLog(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("\r\n");
        stringBuffer.append(String.format("实例名:%s", joinPoint.getTarget().toString()));
        stringBuffer.append("\r\n");
        stringBuffer.append(String.format("方法:%s", signature.getName()));
        Object[] args = joinPoint.getArgs();
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            stringBuffer.append("\r\n");
            stringBuffer.append(String.format("参数[%s]:%s", i, JsonUtils.toJson(arg)));
        }
        logger.info(stringBuffer.toString());
    }
}
