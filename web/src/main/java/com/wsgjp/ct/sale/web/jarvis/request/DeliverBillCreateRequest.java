package com.wsgjp.ct.sale.web.jarvis.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = "发货单创建请求实体", description = "封装发货单提交相关参数")
public class DeliverBillCreateRequest {
    @ApiModelProperty(value = "账套Id")
    private BigInteger profileid;
    @ApiModelProperty(value = "订单编号")
    private String tradeid;
    @ApiModelProperty(value = "订单创建时间")
    private Date tradeCreateTime;
    @ApiModelProperty(value = "物流公司Id")
    private BigInteger freightBtypeid;
    @ApiModelProperty(value = "商城扣费")
    private BigDecimal mallFee;
    @ApiModelProperty(value = "服务费")
    private BigDecimal serviceFee;
    @ApiModelProperty(value = "卖家运费")
    private BigDecimal buyerFreightFee;
    @ApiModelProperty(value = "业务类型")
    private Byte businessType;
    @ApiModelProperty(value = "发货类型")
    private Byte deliverType;
    @ApiModelProperty(value = "发货单扩展属性")
    private DeliverBillCreateExtendRequest billExtend;
    @ApiModelProperty(value = "卖家留言")
    private String buyerMessage;
    @ApiModelProperty(value = "卖家备注")
    private String sellerMemo;
    @ApiModelProperty(value = "卖家旗帜")
    private Byte sellerFlag;
    @ApiModelProperty(value = "下载时间")
    private Date downloadTime;
    @ApiModelProperty(value = "付款时间")
    private Date payTime;
    @ApiModelProperty(value = "买家账号Id")
    private BigInteger buyerid;

    public BigInteger getFreightBtypeid() {
        return freightBtypeid;
    }

    public void setFreightBtypeid(BigInteger freightBtypeid) {
        this.freightBtypeid = freightBtypeid;
    }

    public BigDecimal getMallFee() {
        return mallFee;
    }

    public void setMallFee(BigDecimal mallFee) {
        this.mallFee = mallFee;
    }

    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    public BigDecimal getBuyerFreightFee() {
        return buyerFreightFee;
    }

    public void setBuyerFreightFee(BigDecimal buyerFreightFee) {
        this.buyerFreightFee = buyerFreightFee;
    }

    public byte getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public byte getDeliverType() {
        return deliverType;
    }

    public void setDeliverType(Byte deliverType) {
        this.deliverType = deliverType;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public byte getSellerFlag() {
        return sellerFlag;
    }

    public void setSellerFlag(Byte sellerFlag) {
        this.sellerFlag = sellerFlag;
    }

    public Date getDownloadTime() {
        return downloadTime;
    }

    public void setDownloadTime(Date downloadTime) {
        this.downloadTime = downloadTime;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public BigInteger getBuyerid() {
        return buyerid;
    }

    public void setBuyerid(BigInteger buyerid) {
        this.buyerid = buyerid;
    }



    public Date getTradeCreateTime() {
        return tradeCreateTime;
    }

    public void setTradeCreateTime(Date tradeCreateTime) {
        this.tradeCreateTime = tradeCreateTime;
    }

    public String getTradeid() {
        return tradeid;
    }

    public void setTradeid(String tradeid) {
        this.tradeid = tradeid;
    }

    public DeliverBillCreateExtendRequest getBillExtend() {
        return billExtend;
    }

    public void setBillExtend(DeliverBillCreateExtendRequest billExtend) {
        this.billExtend = billExtend;
    }

    public BigInteger getProfileid() {
        return profileid;
    }

    public void setProfileid(BigInteger profileid) {
        this.profileid = profileid;
    }
}
