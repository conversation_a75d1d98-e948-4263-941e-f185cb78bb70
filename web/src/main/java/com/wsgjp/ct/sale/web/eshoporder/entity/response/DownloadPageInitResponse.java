package com.wsgjp.ct.sale.web.eshoporder.entity.response;

import com.wsgjp.ct.sale.common.entity.EshopInfo;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/31 11:41
 */
public class DownloadPageInitResponse {
	private String taskId;
	private Date begin;
	private Date end;
	private BigInteger eshopId;
	private String eshopName;
	private Boolean newDownloadEnabled;
	private List<EshopInfo> eshopDatasource;

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public Date getBegin() {
		return begin;
	}

	public void setBegin(Date begin) {
		this.begin = begin;
	}

	public Date getEnd() {
		return end;
	}

	public void setEnd(Date end) {
		this.end = end;
	}

	public BigInteger getEshopId() {
		return eshopId;
	}

	public void setEshopId(BigInteger eshopId) {
		this.eshopId = eshopId;
	}

	public String getEshopName() {
		return eshopName;
	}

	public void setEshopName(String eshopName) {
		this.eshopName = eshopName;
	}

	public List<EshopInfo> getEshopDatasource() {
		return eshopDatasource;
	}

	public void setEshopDatasource(List<EshopInfo> eshopDatasource) {
		this.eshopDatasource = eshopDatasource;
	}

	public Boolean getNewDownloadEnabled() {
		return newDownloadEnabled;
	}

	public void setNewDownloadEnabled(Boolean newDownloadEnabled) {
		this.newDownloadEnabled = newDownloadEnabled;
	}
}
