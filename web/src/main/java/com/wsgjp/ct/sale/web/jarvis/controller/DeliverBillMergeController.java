package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.ProcessRequest;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverMergeService;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.biz.jarvis.service.strategy.DeliverBillStrategyService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.StrategyUtils;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.SimpleStrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyExecuteEntity;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.NeedProcessMsgBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessMessageMemory;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessResponseAndResult;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.Language;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * @Date 2020-04-22 10:32
 * @Created by lingxue
 */
@RestController
@Api("订单合并")
@RequestMapping("/${app.id}/jarvis/merge")
public class DeliverBillMergeController {
    private BillDeliverMergeService mergeService;
    private DeliverBillStrategyService strategyService;
    private DeliverService deliverService;
    private BaseInfoService baseInfoService;
    private Logger carbalogger = LoggerFactory.getLogger(getClass());

    public DeliverBillMergeController(BillDeliverMergeService mergeService, DeliverBillStrategyService strategyService, DeliverService deliverService,
                                      BaseInfoService baseInfoService) {
        this.mergeService = mergeService;
        this.strategyService = strategyService;
        this.deliverService = deliverService;
        this.baseInfoService = baseInfoService;
    }

    @ApiOperation(value = "异步订单合并", notes = "")
    @PostMapping("asyncStrategyMergeBills")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_MERGE_BY_AUTO)
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.strategyMergeProcessName, redirectUrl = "sale/jarvis/merge/asyncStrategyMergeBills", serviceName = "strategyMergeBill")
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> asyncMergeBills(@RequestBody ProcessRequest request) {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request,request.getProcessId());
        RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
        List<BigInteger> newTaskIds = request.getWarehouseTaskIds();
        try {
            List<StrategyProcessLog> result = deliverService.mergeBillsByStrategy(CurrentUser.getProfileId(), request,logger);
            newTaskIds = result.stream().map(r->r.getOriginalVchcode()).distinct().collect(Collectors.toList());
            processMessage.doCacheProcessServiceResultMessage(newTaskIds);
            logger.cacheProcessErrorMessage(result);
        } catch (Exception e) {
            carbalogger.error(String.format("[web]异步订单合并asyncStrategyMergeBills-账套：%s-参数：%s-报错：%s",
                    CurrentUser.getProfileId(),
                    request.getWarehouseTaskIds(),
                    e.getMessage()),e);
            SimpleStrategyProcessLog log = new SimpleStrategyProcessLog();
            log.setContent(e.getMessage() == null ? e.getClass().getSimpleName() : e.getMessage());
            logger.cacheProcessErrorMessage(Arrays.asList(log));
        } finally {
            processMessage.setFinish();
        }
        return ProcessResponseAndResult.result(StrategyProcessLog.class,BigInteger.class,processMessage);
    }

    @ApiOperation(value = "同步订单合并（仅供仓储组使用）", notes = "")
    @PostMapping("mergeBills")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_MERGE_BY_AUTO)
    public void mergeBills(@RequestBody ProcessRequest request) {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request,request.getProcessId());
        RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
        try {
            List<StrategyProcessLog> result = deliverService.mergeBillsByStrategy(CurrentUser.getProfileId(), request,logger);
            logger.cacheProcessErrorMessage(result);
        } catch (Exception e) {
            carbalogger.error(String.format("[web]同步订单合并mergeBills-账套：%s-参数：%s-报错：%s",
                    CurrentUser.getProfileId(),
                    request.getWarehouseTaskIds(),
                    e.getMessage()),e);
            SimpleStrategyProcessLog log = new SimpleStrategyProcessLog();
            log.setContent(e.getMessage() == null ? e.getClass().getSimpleName() : e.getMessage());
            logger.cacheProcessErrorMessage(Arrays.asList(log));
        } finally {
            processMessage.setFinish();
        }
    }


    @ApiOperation(value = "取消订单合并", notes = "")
    @PostMapping("cancelMergeBills")
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.cancelMergeBillsProcessName)
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> cancelMergeBills(@RequestBody ProcessRequest request) throws Exception {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request, request.getProcessId());
        List<BigInteger> newTaskIds = new ArrayList<>();
        try {
            RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
            logger.appendMsg(Language.get("beginCancelMergeBill", String.format("开始取消合并，共【%s】张", request.getWarehouseTaskIds().size())));
            List<StrategyExecuteEntity> wrappers = new ArrayList<>();
            try {
                wrappers = mergeService.cancel(CurrentUser.getProfileId(), request.getWarehouseTaskIds());
                for (StrategyExecuteEntity data : wrappers) {
                    newTaskIds.addAll(data.getRelationVchcodes());
                }
            } catch (Exception e) {
                carbalogger.error(String.format("[web]取消订单合并cancelMergeBills-账套：%s-参数：%s-报错：%s",
                        CurrentUser.getProfileId(),
                        request.getWarehouseTaskIds(),
                        e.getMessage()),e);
                logger.appendMsg(Language.get("cancelMergeBillError", e.getMessage()));
            }
            logger.appendMsg(Language.get("endCancelMergeBill", "取消合并结束"));
            processMessage.doCacheProcessServiceResultMessage(newTaskIds);
            logger.cacheProcessErrorMessage(StrategyUtils.getStrategyExecuteLog(wrappers));
        } finally {
            processMessage.setFinish();
        }
        return ProcessResponseAndResult.result(StrategyProcessLog.class,BigInteger.class,processMessage);
    }

    @ApiOperation(value = "取消订单合并", notes = "")
    @PostMapping("syncCancelMergeBills")
    public ProcessResponseAndResult<StrategyProcessLog,List<StrategyExecuteEntity>> syncCancelMergeBills(@RequestBody ProcessRequest request) throws Exception {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request,request.getProcessId());
        List<StrategyExecuteEntity> wrappers = new ArrayList<>();
        try {
            RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
            logger.appendMsg(Language.get("beginCancelMergeBill", String.format("开始取消合并，共【%s】张", request.getWarehouseTaskIds().size())));

            try {
                wrappers = mergeService.cancel(CurrentUser.getProfileId(), request.getWarehouseTaskIds());
            } catch (Exception e) {
                carbalogger.error(String.format("[web]取消订单合并syncCancelMergeBills-账套：%s-参数：%s-报错：%s",
                        CurrentUser.getProfileId(),
                        request.getWarehouseTaskIds(),
                        e.getMessage()),e);
                logger.appendMsg(Language.get("cancelMergeBillError", e.getMessage()));
            }
            logger.appendMsg(Language.get("endCancelMergeBill", "取消合并结束"));
            processMessage.doCacheProcessServiceResultMessage(wrappers);
            logger.cacheProcessErrorMessage(StrategyUtils.getStrategyExecuteLog(wrappers));
        } finally {
            processMessage.setFinish();
        }
        return ProcessResponseAndResult.result(StrategyProcessLog.class,StrategyExecuteEntity.class,processMessage);
    }
}
