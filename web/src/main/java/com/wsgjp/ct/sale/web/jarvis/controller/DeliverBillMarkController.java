package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.entity.DeliverNewMark;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.MarkApplyType;
import com.wsgjp.ct.common.enums.core.service.mark.MarkHandle;
import com.wsgjp.ct.common.enums.core.state.MarkComputedStateEnum;
import com.wsgjp.ct.common.enums.core.state.MarkQueryType;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.common.util.FileUrlDownloader;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverMarkDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverRelationDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.ModifyDeliverBillMarkRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.BaseInfoCheckRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverCommonLog;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverMark;
import com.wsgjp.ct.sale.biz.jarvis.entity.mark.DeliverBillMarkButton;
import com.wsgjp.ct.sale.biz.jarvis.entity.mark.DeliverBillMarkSource;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.MarkLogQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.log.JarvisLogService;
import com.wsgjp.ct.sale.biz.jarvis.newpackage.face.ExStatusFace;
import com.wsgjp.ct.sale.biz.jarvis.newpackage.face.WarehouseBillIds;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoInUseCheckService;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverMarkService;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverRelationService;
import com.wsgjp.ct.sale.biz.jarvis.service.template.TemplateManagementService;
import com.wsgjp.ct.sale.biz.jarvis.state.DeliverCommonLogEntryEnum;
import com.wsgjp.ct.sale.biz.jarvis.state.DeliverCommonLogTitleEnum;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillExStatusAsyncHandleHelper;
import com.wsgjp.ct.sale.biz.jarvis.utils.RedisProcessMsgUtils;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.NeedProcessMsgBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessMessageMemory;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessResponse;
import com.wsgjp.ct.sale.common.enums.ModifyScene;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.utils.WebUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-18
 **/
@RestController
@Api("订单标记")
@RequestMapping("/${app.id}/jarvis/deliverBillMark")
public class DeliverBillMarkController {
    private final BillDeliverMarkService markService;
    private final BillDeliverRelationService relationService;
    private final BaseInfoInUseCheckService baseInfoInUseCheckService;
    private final BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper;
    private TemplateManagementService templateService;

    private static final Logger logger = LoggerFactory.getLogger(DeliverBillMarkController.class);
    public DeliverBillMarkController(BillDeliverMarkService markService,
                                     BillDeliverRelationService relationService,
                                     BaseInfoInUseCheckService baseInfoInUseCheckService,
                                     TemplateManagementService templateService,
                                     BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper) {
        this.markService = markService;
        this.relationService = relationService;
        this.baseInfoInUseCheckService = baseInfoInUseCheckService;
        this.templateService = templateService;
        this.billExStatusAsyncHandleHelper = billExStatusAsyncHandleHelper;
    }

    @ApiOperation(value = "标记管理弹框-获取自定义标记")
    @PostMapping("getMarks")
    public PageResponse<DeliverBillMarkSource> getDeliverBillMarks() {
        List<DeliverBillMarkSource> sources = markService.getMarkGridSourceList(CurrentUser.getProfileId());
        return PageDevice.readPage(sources);
    }
    @ApiOperation(value = "标记管理弹框-新增标记")
    @PostMapping("insertMark")
    public BaseResponse insertDeliverBillMark(@RequestBody List<BillDeliverMarkDTO> marks) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        response.setMsg("保存成功");
        try {
            List<BillDeliverMarkDTO> newMarks = new ArrayList<>();
            StringBuilder addErrRepeatMsg = new StringBuilder();
            StringBuilder updateErrRepeatMsg = new StringBuilder();
            StringBuilder addUserErrRepeatMsg = new StringBuilder();
            StringBuilder updateUserErrRepeatMsg = new StringBuilder();
            Map<String, List<BaseOrderMarkEnum>> systemMarkMap = Arrays.stream(BaseOrderMarkEnum.values()).collect(Collectors.groupingBy(BaseOrderMarkEnum::getName));
            List<DeliverBillMarkSource> userMarkList = markService.getMarkGridSourceList(CurrentUser.getProfileId());
            Map<String, List<DeliverBillMarkSource>> userMarkListMap = userMarkList.stream().collect(Collectors.groupingBy(BillDeliverMarkDTO::getMarkName));
            for (BillDeliverMarkDTO mark : marks) {
                boolean flag = true;
                String markName = mark.getMarkName();
                List<BaseOrderMarkEnum> systemMarks = systemMarkMap.get(markName);
                if (CollectionUtils.isNotEmpty(systemMarks)) {
                    flag = false;
                    if (mark.getId().equals(BigInteger.valueOf(0))) {
                        addErrRepeatMsg.append(String.format("【%s】", markName));
                    } else {
                        updateErrRepeatMsg.append(String.format("【%s】", markName));
                    }
                }
                List<DeliverBillMarkSource> userMarks = userMarkListMap.get(markName);
                if (CollectionUtils.isNotEmpty(userMarks)) {
                    if (mark.getId().equals(BigInteger.valueOf(0))) {
                        addUserErrRepeatMsg.append(String.format("【%s】", markName));
                        flag = false;
                    } else {
                        if (userMarks.get(0).getId().compareTo(mark.getId())!=0) {
                            updateUserErrRepeatMsg.append(String.format("【%s】", markName));
                            flag = false;
                        }
                    }
                }
                if (flag) {
                    newMarks.add(mark);
                }
            }

            if (CollectionUtils.isNotEmpty(newMarks)) {
                List<BillDeliverMarkDTO> addMarks = new ArrayList<>();
                List<BillDeliverMarkDTO> updateMarks = new ArrayList<>();
                for (BillDeliverMarkDTO mark : newMarks) {
                    mark.setProfileId(CurrentUser.getProfileId());
                    if (mark.getId().equals(BigInteger.valueOf(0))) {
                        mark.setId(UId.newId());
                        addMarks.add(mark);
                    } else {
                        updateMarks.add(mark);
                    }
                }
                markService.insertMarkBatch(addMarks);
                markService.updateMarkBatch(updateMarks);
                try{
                    List<DeliverCommonLog> logs=new ArrayList<>();
                    for (BillDeliverMarkDTO addMark:addMarks
                         ) {
                        DeliverCommonLog log=createDeliverCommonLogByTitle(DeliverCommonLogTitleEnum.ADD_MARK_LOG,null,addMark);
                        if(log!=null){
                            logs.add(log);
                        }
                    }
                    for (BillDeliverMarkDTO updateMark:updateMarks
                    ) {
                        List<BillDeliverMarkDTO> oldMarks=userMarkList.stream().filter(key->key.getId().equals(updateMark.getId())).collect(Collectors.toList());
                        if(oldMarks!=null&&oldMarks.size()>0){
                            DeliverCommonLog log=createDeliverCommonLogByTitle(DeliverCommonLogTitleEnum.MODIFY_MARK_LOG,oldMarks.get(0),updateMark);
                            if(log!=null){
                                logs.add(log);
                            }
                        }
                    }
                    if(logs!=null&&logs.size()>0){
                        LogService.addRange(logs);
                    }
                }catch (Exception ex){

                }
            }

            StringBuilder repeatMsgAll = new StringBuilder();
            if (StringUtils.isNotEmpty(addErrRepeatMsg.toString())) {
                addErrRepeatMsg.append("与系统标记内容相同");
            }
            if (StringUtils.isNotEmpty(addUserErrRepeatMsg.toString())) {
                addUserErrRepeatMsg.append("与自定义标记内容相同");
                addErrRepeatMsg.append(addUserErrRepeatMsg);
            }
            if (StringUtils.isNotEmpty(addErrRepeatMsg)) {
                repeatMsgAll.append(addErrRepeatMsg).append("，禁止新增");
            }
            if (StringUtils.isNotEmpty(updateErrRepeatMsg.toString())) {
                updateErrRepeatMsg.append("与系统标记内容相同");
            }
            if (StringUtils.isNotEmpty(updateUserErrRepeatMsg.toString())) {
                updateUserErrRepeatMsg.append("与自定义标记内容相同");
                updateErrRepeatMsg.append(updateUserErrRepeatMsg);
            }
            if (StringUtils.isNotEmpty(updateErrRepeatMsg)) {
                repeatMsgAll.append("；").append(updateErrRepeatMsg).append("，禁止修改");
            }
            if (StringUtils.isNotEmpty(repeatMsgAll)) {
                if (newMarks.size() > 0) {
                    response.setCode("1");
                    response.setMsg(String.format("部分保存失败，原因：%s", repeatMsgAll));
                } else {
                    response.setCode("-1");
                    response.setMsg(String.format("保存失败，请重试，原因：%s", repeatMsgAll));
                }
            }
        } catch (Exception ex) {
            response.setCode("-1");
            response.setMsg(String.format("保存失败，请重试，原因：【%s】", ex.getMessage()));
        }
        return response;
    }
    @ApiOperation(value = "标记管理弹框-查询标记日志")
    @PostMapping("listMarkLogs")
    public PageResponse<DeliverCommonLog> listMarkLogs(@RequestBody PageRequest<MarkLogQueryParams> request) {
        if (request.getQueryParams().getBeginTime() != null && request.getQueryParams().getEndTime() != null) {
            Date endTime = request.getQueryParams().getEndTime();
            request.getQueryParams().setEndTime(new Date(endTime.getTime() + 999));
        }
        request.getQueryParams().setEntry(String.valueOf(DeliverCommonLogEntryEnum.MARK_LOG.getCode()));
        //先根据profileid查询
        PageResponse<DeliverCommonLog> res = LogService.query(request);
        List<DeliverCommonLog> resLogs = new ArrayList<>();
        if (ngp.utils.CollectionUtils.isNotEmpty(res.getList())) {
            for (int i = 0; i < res.getList().size(); i++) {
                DeliverCommonLog log = JsonUtils.toObject(JsonUtils.toJson(res.getList().get(i)), DeliverCommonLog.class);
                resLogs.add(log);
            }
        }
        resLogs.sort(Comparator.comparing(c -> c.getCreateTime(), Comparator.reverseOrder()));
        res.setList(resLogs);
        return res;
    }

    @ApiOperation(value = "标记管理弹框-删除标记")
    @PostMapping("deleteMark")
    public BaseResponse deleteDeliverBillMark(BigInteger id) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        BigInteger profileId = CurrentUser.getProfileId();
        List<BigInteger> ids=new ArrayList<>();
        ids.add(id);
        List<BillDeliverMarkDTO> oldMarks=markService.getMarkList(ids,profileId);
        try {

            String msg = handleCheckInUse(id, profileId);
            if (StringUtils.isNotEmpty(msg)) {
                response.setCode("-1");
                response.setMsg(String.format("删除标记失败，原因：【%s】", msg));
                return response;
            }
            markService.deleteMark(id, profileId);
        } catch (Exception ex) {
            response.setCode("-1");
            response.setMsg(String.format("删除标记失败，请重试，原因：【%s】", ex.getMessage()));
            logger.error(response.getMsg(),ex);
            return response;
        }
        try{
            BillDeliverMarkDTO oldMark=oldMarks.get(0);
            DeliverCommonLog log=createDeliverCommonLogByTitle(DeliverCommonLogTitleEnum.DELETE_MARK_LOG,oldMark,null);
            if(log!=null){
                LogService.add(log);
            }
        }catch (Exception ex){}
        return response;
    }
    private DeliverCommonLog createDeliverCommonLogByTitle(DeliverCommonLogTitleEnum type,BillDeliverMarkDTO oldMark,BillDeliverMarkDTO newMark){
        if(oldMark==null&&newMark==null){
            return null;
        }
        if(type==DeliverCommonLogTitleEnum.DELETE_MARK_LOG&&oldMark==null){
            return null;
        }
        if(type==DeliverCommonLogTitleEnum.MODIFY_MARK_LOG&&(oldMark==null||newMark==null)){
            return null;
        }
        if(type==DeliverCommonLogTitleEnum.ADD_MARK_LOG&&newMark==null){
            return null;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        DeliverCommonLog log=new DeliverCommonLog();
        log.setId(UId.newId());
        log.setTitle(type.getName());
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());
        log.setProfileId(profileId);
        log.setEntry(String.valueOf(DeliverCommonLogEntryEnum.MARK_LOG.getCode()));
        log.setEtypeId(CurrentUser.getEmployeeId());
        log.setEtypeName(JarvisLogService.getEtypeName());
        switch (type){
            case ADD_MARK_LOG:
                log.setContent(MessageFormat.format("标记名称【{0}】；标记颜色【{1}】",newMark.getMarkName(),newMark.getMarkColor()));
                break;
            case DELETE_MARK_LOG:
                log.setContent(MessageFormat.format("标记名称【{0}】；标记颜色【{1}】",oldMark.getMarkName(),oldMark.getMarkColor()));
                break;
            case MODIFY_MARK_LOG:
                if(oldMark.getMarkName().equalsIgnoreCase(newMark.getMarkName())&&oldMark.getMarkColor().equalsIgnoreCase(newMark.getMarkColor())){
                    return null;
                }else if(!oldMark.getMarkName().equalsIgnoreCase(newMark.getMarkName())&&oldMark.getMarkColor().equalsIgnoreCase(newMark.getMarkColor())){
                    log.setContent(MessageFormat.format("修改标记名字。标记名称【{0}】修改为【{1}】",oldMark.getMarkName(),newMark.getMarkName()));
                }else if(oldMark.getMarkName().equalsIgnoreCase(newMark.getMarkName())&&!oldMark.getMarkColor().equalsIgnoreCase(newMark.getMarkColor())){
                    log.setContent(MessageFormat.format("修改标记【{2}】颜色。标记颜色【{0}】修改为【{1}】",oldMark.getMarkColor(),newMark.getMarkColor(),oldMark.getMarkName()));
                }else if(!oldMark.getMarkName().equalsIgnoreCase(newMark.getMarkName())&&!oldMark.getMarkColor().equalsIgnoreCase(newMark.getMarkColor())){
                    log.setContent(MessageFormat.format("修改标记名字和颜色。标记名称【{0}】修改为【{1}】，标记颜色【{2}】修改为【{3}】",oldMark.getMarkName(),newMark.getMarkName(),oldMark.getMarkColor(),newMark.getMarkColor()));
                }
                break;
        }
        return log;
    }

    private String handleCheckInUse(BigInteger id, BigInteger profileId){
        BaseInfoCheckRequest checkRequest = new BaseInfoCheckRequest();
        checkRequest.setId(id);
        checkRequest.setProfileId(profileId);
        String strategyMsg = baseInfoInUseCheckService.checkInUseToMark(checkRequest);
        if (StringUtils.isNotEmpty(strategyMsg)) {
            return strategyMsg;
        }
        String markMsg = markService.checkInUseToMark(id, profileId);
        if (StringUtils.isNotEmpty(markMsg)) {
            return markMsg;
        }
        String templateMsg = templateService.checkInUseToMarkForTemplate(id, profileId);
        if (StringUtils.isNotEmpty(templateMsg)) {
            return templateMsg;
        }
        return null;
    }

    @ApiOperation(value = "自定义标记弹框-获取自定义标记+开放的系统标记")
    @PostMapping("getMarkButtons")
    public List<DeliverBillMarkButton> getDeliverBillMarkButtons() {
        return markService.getMarkButtonSourceList(CurrentUser.getProfileId());
    }

    @ApiOperation(value = "根据场景加载标记")
    @PostMapping("getMarkButtonsByMarkApplyType")
    public List<DeliverBillMarkButton> getDeliverBillMarkButtonsByMarkApplyType(@RequestBody MarkApplyType markApplyType) {
        return markService.getMarkButtonSourceListByMarkApplyType(CurrentUser.getProfileId(),markApplyType);
    }

    @ApiOperation(value = "根据场景获取系统标记或自定义标记", notes = "（）")
    @PostMapping("getMarksByMarkApplyType")
    public List<DeliverMark> getDeliverBillMarksByMarkApplyType(@RequestBody MarkQueryRequest request) {
        return markService.getDeliverBillMarksByMarkApplyType(CurrentUser.getProfileId(), request.getMarkApplyType(), request.getContain());
    }

    @ApiOperation(value = "处理自定义标记和系统标记")
    @PostMapping("handleUserMark")
    public BaseResponse handleUserMark(@RequestBody List<BillDeliverRelationDTO> request) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");

        try {
            List<BillDeliverRelationDTO> relationDeleteList = relationService.getRelationDeleteList(request);
            request.addAll(relationDeleteList);
            handleUserMarkRequest(request);
        } catch (Exception ex) {
            handleException(response, ex);
        } finally {
            handlePostProcessing(request);
        }

        return response;
    }

    private void handleUserMarkRequest(List<BillDeliverRelationDTO> request) {
        relationService.handleUserMark(request);
    }

    private void handleException(BaseResponse response, Exception ex) {
        response.setCode("-1");
        response.setMsg(String.format("处理订单标记失败，请重试，原因：【%s】", ex.getMessage()));
        logger.error("处理订单标记失败", ex);
    }

    private void handlePostProcessing(List<BillDeliverRelationDTO> request) {
        List<ExStatusFace> faces = request.stream()
                .map(billDeliverRelationDTO -> new WarehouseBillIds(null, billDeliverRelationDTO.getPrimaryId()))
                .collect(Collectors.toList());

        billExStatusAsyncHandleHelper.logoComprehensiveSceneByFace(faces, Collections.singletonList(ModifyScene.BillOtherModify), false);
    }

    @ApiOperation(value = "处理自定义标记和系统标记(异步信息)")
    @PostMapping("batchUpdateBillByMarkForProcessMsg")
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.defaultProcessName)
    public ProcessResponse<StrategyProcessLog> batchUpdateBillByMarkForProcessMsg(@RequestBody ModifyDeliverBillMarkRequest request) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        RedisProcessMessage processMessage = new ProcessMessageMemory(request, request.getProcessId());
        RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
        logger.appendMsg("开始执行...");
        try {
            List<BillDeliverRelationDTO> relationDeleteList = relationService.getRelationDeleteList(request.getMarkDatas());
            request.getMarkDatas().addAll(relationDeleteList);
            relationService.handleUserMarkForProcessMsg(request,logger);
            logger.appendMsg("执行完毕");
            logger.appendMsg(String.format("%s标记成功", request.getHandleTypeMsg()));
        } catch (Exception ex) {
            response.setCode("-1");
            String errorMessage=String.format("处理订单标记失败，请重试，原因：【%s】", ex.getMessage());
            response.setMsg(errorMessage);
            logger.appendMsg(String.format("%s标记失败：%s", request.getHandleTypeMsg(),errorMessage));
            RedisProcessMsgUtils.resetLoggerError(logger,true);
        } finally {
            logger.appendMsg("开始执行修改标记后的业务处理");
            if(CollectionUtils.isNotEmpty(request.getMarkDatas())){
                List<ExStatusFace> faces = new ArrayList<>();
                for (BillDeliverRelationDTO billDeliverRelationDTO : request.getMarkDatas()) {
                    faces.add(new WarehouseBillIds(null, billDeliverRelationDTO.getPrimaryId()));
                }
                billExStatusAsyncHandleHelper.logoComprehensiveSceneByFace(faces, Arrays.asList(ModifyScene.BillOtherModify), false);
            }
            logger.appendMsg("执行修改标记后的业务处理完毕");
            processMessage.setFinish();
        }
        return ProcessResponse.result(StrategyProcessLog.class,processMessage);
    }
    @ApiOperation(value = "下载标记中的URL")
    @PostMapping("downloadFileByMark")
    public void downloadFileByMark(@RequestBody MarkQueryRequest request) {
        try {
            List<String> urls;
            if (request.getDetailId() != null) {
                urls = markService.queryDetailMarkDataFileUrls(Collections.singletonList(request.getDetailId()), request.isPost());
            } else {
                urls = markService.queryMarkDataFileUrls(Collections.singletonList(request.getWarehouseTaskId()), request.isPost());
            }
            WebUtils.download("物流签收拍照" + DateUtils.formatDate(DateUtils.getDate(), "yyyyMMdd") + ".zip",
                    FileUrlDownloader.downloadAndZip(urls));
        } catch (Exception e) {
            logger.error("下载文件异常 profile {}", CurrentUser.getProfileId(), e);
            throw new RuntimeException(e);
        }
    }

    static class MarkQueryRequest {
        private MarkApplyType markApplyType;
        private MarkQueryType contain = MarkQueryType.System;
        private BigInteger warehouseTaskId;
        private boolean post;
        private String tradeOrderId;
        private BigInteger detailId;

        public BigInteger getWarehouseTaskId() {
            return warehouseTaskId;
        }

        public void setWarehouseTaskId(BigInteger warehouseTaskId) {
            this.warehouseTaskId = warehouseTaskId;
        }

        public boolean isPost() {
            return post;
        }

        public void setPost(boolean post) {
            this.post = post;
        }

        public MarkApplyType getMarkApplyType() {
            return markApplyType;
        }

        public void setMarkApplyType(MarkApplyType markApplyType) {
            this.markApplyType = markApplyType;
        }

        public MarkQueryType getContain() {
            return contain;
        }

        public void setContain(MarkQueryType contain) {
            this.contain = contain;
        }

        public String getTradeOrderId() {
            return tradeOrderId;
        }

        public void setTradeOrderId(String tradeOrderId) {
            this.tradeOrderId = tradeOrderId;
        }

        public BigInteger getDetailId() {
            return detailId;
        }

        public void setDetailId(BigInteger detailId) {
            this.detailId = detailId;
        }
    }

}


