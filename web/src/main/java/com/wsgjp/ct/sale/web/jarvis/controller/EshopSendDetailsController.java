package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import bf.datasource.page.Sort;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.page.PageSummary;
import com.wsgjp.ct.sale.biz.jarvis.dto.EshopSendDetailsDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Stock;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.EshopSendDetailsParams;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.EshopSendDetailsService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;
import java.util.stream.Collectors;

/**
 * created  by qhy on 2021-12-02
 */
@RestController
@Api(description = "网店发货明细表")
@RequestMapping("/${app.id}/jarvis/eshopSendDetails")
public class EshopSendDetailsController {
    private EshopSendDetailsService eshopSendDetailsService;
    private BaseInfoService baseInfoService;
    public EshopSendDetailsController(EshopSendDetailsService eshopSendDetailsService, BaseInfoService baseInfoService) {
        this.eshopSendDetailsService = eshopSendDetailsService;
        this.baseInfoService = baseInfoService;
    }

    @ApiOperation(value = "查询网店发货明细", notes = "（）")
    @PostMapping("queryEshopSendDetails")
    public PageResponse<EshopSendDetailsDTO> query(@RequestBody PageRequest<EshopSendDetailsParams> request) throws Exception {
        handleSort(request);
        buildQuery(request);
        //前端没有传排序，则暂不支持排序，导出也一样
        return eshopSendDetailsService.listBillDeliversNew(request);
    }

    private void handleSort(PageRequest<EshopSendDetailsParams> request) {
        if (null==request.getSorts()) {
            return;
        }
        for (Sort sort : request.getSorts()) {
            if ("currencyTaxTotal".equals(sort.getDataField())) {
                sort.setAscending(!sort.getAscending());
            }
        }
    }

    @ApiOperation(value = "查询网店发货明细", notes = "（）")
    @PostMapping("queryEshopSendDetails/count")
    public PageSummary queryCount(@RequestBody PageRequest<EshopSendDetailsParams> request) throws Exception {
        buildQuery(request);
        return eshopSendDetailsService.listBillDeliversCountNew(request);
    }

    private void buildQuery(@RequestBody PageRequest<EshopSendDetailsParams> request) {
        EshopSendDetailsParams param = request.getQueryParams();
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        if (param.getKtypeId() == null && !PermissionValiateService.isAdmin() && PermissionValiateService.isKtypeLimited()) {
            List<Stock> stocks = baseInfoService.getKTypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            List<BigInteger> stockIds = stocks.stream().map(Stock::getId).distinct().collect(Collectors.toList());
            param.setKtypeId(stockIds);
        }
        if (param.getOrgId() == null && !PermissionValiateService.isAdmin() && PermissionValiateService.isOtypeLimited()) {
            List<Organization> eshops = baseInfoService.getEshopOrganizationsLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            List<BigInteger> eshopIds = eshops.stream().map(Organization::getId).distinct().collect(Collectors.toList());
            param.setOrgId(eshopIds);
        }
        eshopSendDetailsService.fillBuyerIdList(request);
    }


    @ApiOperation(value = "网店发货统计", notes = "（）")
    @PostMapping("queryEshopSendBills")
    public PageResponse<EshopSendDetailsDTO> queryEshopSendBills(@RequestBody PageRequest<EshopSendDetailsParams> request) throws Exception {
        buildQuery(request);
        //前端没有传排序，则暂不支持排序，导出也一样
        boolean usedNewScheme = request.getQueryParams().isUsedNewScheme();
        return eshopSendDetailsService.listBillDeliverListNew(request);
    }


    @ApiOperation(value = "网店发货统计合计", notes = "（）")
    @PostMapping("queryEshopSendBills/count")
    public PageSummary queryEshopSendBillsCount(@RequestBody PageRequest<EshopSendDetailsParams> request) throws Exception {
        buildQuery(request);
        //前端没有传排序，则暂不支持排序，导出也一样
        return eshopSendDetailsService.listBillDeliverListCountNew(request);
    }

}
