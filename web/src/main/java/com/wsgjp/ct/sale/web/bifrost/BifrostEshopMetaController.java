package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopMetaService;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformEshopSupport;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformSupport;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.mate.FeatureRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "网店元信息相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/meta")
public class BifrostEshopMetaController {
    private final BifrostEshopMetaService eshopMetaService;


    public BifrostEshopMetaController(BifrostEshopMetaService eshopMetaService) {
        this.eshopMetaService = eshopMetaService;
    }

    @ApiOperation("当前是否支持该店铺类型")
    @PostMapping("isSupported")
    public boolean isSupported(@RequestBody BaseRequest request) {
        return eshopMetaService.isSupported(request);
    }

    @ApiOperation("列出当前已支持的所有店铺类型")
    @PostMapping("listSupportedShopTypes")
    public List<ShopType> listSupportedShopTypes(@RequestBody BaseRequest request) {
        return eshopMetaService.listSupportedShopTypes(request);
    }

    @ApiOperation("列出当前店铺类型所支持所有特性")
    @PostMapping("listSupportedFeatures")
    public List<String> listSupportedFeatures(@RequestBody BaseRequest request) {
        return eshopMetaService.listSupportedFeatures(request);
    }

    @ApiOperation("列出支持指定特性的所有店铺类型")
    @PostMapping("listFeatureSupportedShopTypes")
    public List<ShopType> listFeatureSupportedShopTypes(@RequestBody FeatureRequest request) {
        return eshopMetaService.listFeatureSupportedShopTypes(request);
    }

    @ApiOperation("查询当前已支持的并且开启使用的所有店铺类型")
    @PostMapping("listSupportedAndEnabledShopTypes")
    public List<ShopType> listSupportedAndEnabledShopTypes(@RequestBody BaseRequest request) {
        return eshopMetaService.listSupportedAndEnabledShopTypes(request);
    }

    @ApiOperation("查询当前已支持的并且开启使用的所有店铺类型")
    @PostMapping("getSupportPlatformTypes")
    public List<PlatformSupport> getSupportPlatformTypes(@RequestBody BaseRequest request) {
        return eshopMetaService.getSupportPlatformTypes(request);
    }

    @ApiOperation("获取当前支持并且开启的所有店铺类型信息")
    @PostMapping("getSupportEshopTypes")
    public List<PlatformEshopSupport> getSupportEshopTypes(@RequestBody BaseRequest request) {
        return eshopMetaService.getSupportEshopTypes(request);
    }
}
