package com.wsgjp.ct.sale.web.jarvis.controller;


import com.wsgjp.ct.sale.biz.jarvis.dto.area.Area;
import com.wsgjp.ct.sale.biz.jarvis.service.area.AreaService;
import com.wsgjp.ct.sale.biz.jarvis.state.AreaTypeEnum;
import com.wsgjp.ct.support.business.digital.Total;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-31 14:00
 */

@RestController
@Api(description = "地区基本信息")
@RequestMapping("/${app.id}/jarvis/area")
public class AreaController {

    private AreaService areaService;

    public AreaController(AreaService areaService) {
        this.areaService = areaService;
    }

    @ApiOperation(value = "获取省", notes = "（）")
    @PostMapping("listProvinceArea")
    public List<Area> getPropList() throws Exception {
        List<Area> result = areaService.listProvinceArea();
        return result;
    }

    @ApiOperation(value = "获取子项", notes = "（）")
    @PostMapping("listChildData")
    public List<Area> getPropList(@RequestParam String parentid) throws Exception {
        List<Area> result = areaService.listChildData(parentid);
        return result;
    }

    @PostMapping("/test")
    public TestData test(@RequestBody TestData ttt) {
        TestData testData1 = new TestData();
        testData1.setDate(ttt.getDate());
        testData1.setA(System.currentTimeMillis());
        testData1.setB(System.currentTimeMillis());
        testData1.setAreaTypeEnum(AreaTypeEnum.Freight_FEE_AREA);
        testData1.setTotal(ttt.getTotal());
        return testData1;
    }

    public static class TestData {
        private Date date;
        private Long a;
        private long b;

        @Total
        private BigDecimal total;
        private AreaTypeEnum areaTypeEnum;

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

        public Long getA() {
            return a;
        }

        public void setA(Long a) {
            this.a = a;
        }

        public long getB() {
            return b;
        }

        public void setB(long b) {
            this.b = b;
        }

        public AreaTypeEnum getAreaTypeEnum() {
            return areaTypeEnum;
        }

        public void setAreaTypeEnum(AreaTypeEnum areaTypeEnum) {
            this.areaTypeEnum = areaTypeEnum;
        }

        public BigDecimal getTotal() {
            return total;
        }

        public void setTotal(BigDecimal total) {
            this.total = total;
        }
    }

}
