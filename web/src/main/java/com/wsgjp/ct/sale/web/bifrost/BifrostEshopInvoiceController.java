package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopInvoiceService;
import com.wsgjp.ct.sale.platform.entity.request.invoice.InvoiceUploadRequest;
import com.wsgjp.ct.sale.platform.entity.response.invoice.InvoiceUploadResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 2022-03-10
 *
 * <AUTHOR>
 **/
@Api(tags = "网店发票相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/invoice")
public class BifrostEshopInvoiceController {

    private final BifrostEshopInvoiceService eshopInvoiceService;

    public BifrostEshopInvoiceController(BifrostEshopInvoiceService eshopFreightService) {
        this.eshopInvoiceService = eshopFreightService;
    }

    @ApiOperation("上传发票到平台")
    @PostMapping("uploadEshopInvoice")
    public InvoiceUploadResponse uploadEshopInvoice(@RequestBody InvoiceUploadRequest request) {
        return eshopInvoiceService.uploadEshopInvoice(request);
    }
}
