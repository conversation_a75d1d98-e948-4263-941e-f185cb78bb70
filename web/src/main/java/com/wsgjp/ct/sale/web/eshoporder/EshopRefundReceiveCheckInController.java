package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.RenewReceiveCheckInRequest;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.RefundConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.RefundReceiveCheckInConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.PartialRefreshReuqest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsBatchEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopReceiveCheckInResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ReceiveCheckResponse;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopRefundCheckInSysLog;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopRefundCheckInSysQueryParams;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopOrderEshopRefundService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopRefundReceiveCheckInService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.biz.jarvis.config.AsyncProcess;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.BatchProcessRequest;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.refund.checkin.response.QueryRefundReceiveCheckInInfoResponse;
import com.wsgjp.ct.sale.biz.refund.refund.request.PlatformHandleRefundEntity;
import com.wsgjp.ct.sale.common.enums.publish.CheckinState;
import com.wsgjp.ct.sale.web.eshoporder.entity.EshopRefundReceiveCheckInInitResponse;
import com.wsgjp.ct.sale.web.eshoporder.entity.enums.PageMode;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.EshopSaleOrderInitRequest;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ngp.idgenerator.UId;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-02
 */
@Api(tags = "售后收货登记相关")
@RequestMapping("${app.id}/eshoporder/refundreceivecheckin")
@RestController
public class EshopRefundReceiveCheckInController {

    private final EshopRefundReceiveCheckInService receiveCheckInService;
    private final EshopOrderEshopRefundService refundService;
    private final BaseInfoService baseInfoService;
    private final EshopOrderBaseInfoService eshopOrderBaseInfoService;

    public EshopRefundReceiveCheckInController(EshopRefundReceiveCheckInService receiveCheckInService, EshopOrderEshopRefundService refundService, BaseInfoService baseInfoService, EshopOrderBaseInfoService eshopOrderBaseInfoService) {
        this.receiveCheckInService = receiveCheckInService;
        this.refundService = refundService;
        this.baseInfoService = baseInfoService;
        this.eshopOrderBaseInfoService = eshopOrderBaseInfoService;
    }

    @PostMapping(value = "/partialRefresh")
    public List<EshopRefundReceiveCheckInEntity> partialRefresh(@RequestBody PartialRefreshReuqest partialRefreshReuqest) {
        return receiveCheckInService.partialRefreshRefundReceiveCheckInListNEW(partialRefreshReuqest.getId());
    }

    @ApiOperation(httpMethod = "POST", value = "获取收货登记列表")
    @PostMapping(value = "/queryRefundReceiveCheckIns")
    public PageResponse<EshopRefundReceiveCheckInEntity> queryRefundReceiveCheckInList(@ApiParam(value = "请求参数实体") @RequestBody PageRequest<ReceiveCheckInParameter> pageParameter) {
        return PageDevice.readPage(receiveCheckInService.queryRefundReceiveCheckInListNEWNEW(pageParameter.getQueryParams(), pageParameter));
    }

    @ApiOperation(httpMethod = "POST", value = "获取收货登记")
    @PostMapping(value = "/queryRefundReceiveCheckIn")
    public EshopRefundReceiveCheckInEntity queryRefundReceiveCheckInList(@ApiParam(value = "请求参数实体") @RequestBody ReceiveCheckInParameter parameter) {
        return receiveCheckInService.queryRefundReceiveCheckIn(parameter);
    }

    @ApiOperation(httpMethod = "POST", value = "获取收货登记详情列表")
    @PostMapping(value = "/queryRefundReceiveCheckInDetailInfo")
    public QueryRefundReceiveCheckInInfoResponse queryRefundReceiveCheckInDetailInfo(@ApiParam(value = "请求参数实体") @RequestBody QueryOrderDetailParameter queryOrderDetailParameter) {
        return receiveCheckInService.queryCheckInInfo(queryOrderDetailParameter);
    }

    @RequestMapping(value = "/editReceiveCheckInInit", method = RequestMethod.POST)
    public EshopRefundReceiveCheckInInitResponse getEditAdvanceFormInitData(@RequestBody EshopSaleOrderInitRequest request) {
        PageMode mode = checkMode(request.getModeStr());
        EshopRefundReceiveCheckInInitResponse response = new EshopRefundReceiveCheckInInitResponse(mode);
        EshopRefundConfig refundConfig = refundService.getRefundConfig();
        RefundConfig refundGlobalConfig = GlobalConfig.get(RefundConfig.class);
        response.setStrictSno(refundGlobalConfig.isStrictSno());
        response.setEnableGoodsCheckin(refundConfig != null && refundConfig.getEnableGoodsCheckin() != null && refundConfig.getEnableGoodsCheckin());
        response.setAutoStockInAfterAudited(refundConfig != null && refundConfig.getAutoStockInAfterAudited() != null && refundConfig.getAutoStockInAfterAudited());
        EshopRefundReceiveCheckInEntity checkInEntity;
        if (mode == PageMode.MODIFY) {
            checkInEntity = receiveCheckInService.initModifyOrder(request.getId());
            response.setTitle(RefundReceiveCheckInConstantEnum.EDIT_RECEIVE_RECORD.getName());
            response.setCheckInEntity(checkInEntity);
        } else {
            checkInEntity = receiveCheckInService.initCreateReceiveCheckInNEW();
            checkInEntity.setCheckinState(CheckinState.OPEN);
            response.setTitle(RefundReceiveCheckInConstantEnum.ADD_RECEIVE_RECORD.getName());
            response.setCheckInEntity(checkInEntity);
        }
        response.setEtypeList(eshopOrderBaseInfoService.getEtypeList(CurrentUser.getProfileId()));
        response.setKtypeList(baseInfoService.getAllKTypesToNotStopNoWms(CurrentUser.getProfileId()));
        return response;
    }

    @RequestMapping(value = "/checkFreight", method = RequestMethod.POST)
    public ReceiveCheckResponse checkFreight(@RequestBody EshopRefundReceiveCheckInEntity checkInEntity) {
        return receiveCheckInService.checkReceive(checkInEntity);
    }

    /**
     * 新增收货登记
     *
     * @param checkInEntity
     * @return
     */
    @RequestMapping(value = "/saveRefundReceiveCheckIn", method = RequestMethod.POST)
    public BaseResponse saveEshopRefundReceiveCheckIn(@RequestBody EshopRefundReceiveCheckInEntity checkInEntity) {
        boolean useDistributor = getConfig("useDistributor");
        return receiveCheckInService.doSaveReceiveCheckIn(checkInEntity, useDistributor);
    }

    private boolean getConfig(String key) {
        EshopOrderBaseInfoService bean = GetBeanUtil.getBean(EshopOrderBaseInfoService.class);
        SysData useDistributor = bean.getSysDataPO(key);
        if (useDistributor == null) {
            useDistributor = new SysData();
            useDistributor.setSubValue("0");
        }
        return "1".equals(useDistributor.getSubValue());
    }


    @RequestMapping(value = "/deleteReceiveCheckIn", method = RequestMethod.POST)
    public String deleteReceiveCheckIn(@RequestBody ReceiveCheckInOperateBaseRequest parameter) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        parameter.setProcessLogger(processLogger);
        parameter.setLoginUserId(CurrentUser.getEmployeeId());
        receiveCheckInService.deleteEshopRefundReceiveCheckIn(parameter);
        return taskId;
    }

    @RequestMapping(value = "/getBatchInfo", method = RequestMethod.POST)
    public GoodsBatchEntity getBatchInfo(@RequestBody GetBatchInfoParameter parameter) {
        return receiveCheckInService.getBatchInfo(parameter);
    }

    /**
     * 收货登记关联售后单
     *
     * @param relateParameter
     * @return
     */
    @RequestMapping(value = "/relateRefund", method = RequestMethod.POST)
    public BaseResponse relateRefund(@RequestBody CheckInRelateRefundParameter relateParameter) {
        return receiveCheckInService.relateRefund(relateParameter);
    }

    @RequestMapping(value = "/queryRefundList", method = RequestMethod.POST)
    public PageResponse<EshopRefundEntity> queryRefundList(@RequestBody PageRequest<QueryRefundParameter> parameter) {
        return PageDevice.readPage(refundService.queryEshopRefundList(parameter.getQueryParams()));
    }

    @RequestMapping(value = "/queryRefundPopData", method = RequestMethod.POST)
    public List<EshopRefundEntity> queryRefundPopData(
            @RequestBody QueryRefundParameter parameter) {
        return refundService.queryRefundDataForCheckInNew(parameter);
    }

    @RequestMapping(value = "/queryReceiveConfigs", method = RequestMethod.POST)
    public EshopRefundReceiveCheckInConfig queryReceiveConfigs() {
        return receiveCheckInService.getReceiveConfig();
    }

    @RequestMapping(value = "/saveReceiveConfigs", method = RequestMethod.POST)
    public BaseResponse saveReceiveConfigs(@RequestBody EshopRefundReceiveCheckInConfig config) {
        return receiveCheckInService.saveReceiveConfigs(config);
    }

    @RequestMapping(value = "/otherStockIn", method = RequestMethod.POST)
    public String otherStockIn(@RequestBody ReceiveCheckInGenerateBillRequest request) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        request.setProcessLogger(processLogger);
        request.setLoginUserId(CurrentUser.getEmployeeId());
        receiveCheckInService.markNoMessage(request);
        receiveCheckInService.otherStockIn(request);
        return taskId;
    }

    @RequestMapping(value = "/stockInForOldCheckIn", method = RequestMethod.POST)
    public String stockInForOldCheckIn(@RequestBody ReceiveCheckInOperateBaseRequest request) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        request.setProcessLogger(processLogger);
        request.setLoginUserId(CurrentUser.getEmployeeId());
        receiveCheckInService.stockInForOldCheckIn(request);
        return taskId;
    }

    /**
     * 此接口报废，报损逻辑在直接入库操作之后直接报损
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/lossProduct", method = RequestMethod.POST)
    public String lossProduct(@RequestBody ReceiveCheckInGenerateBillRequest request) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        request.setProcessLogger(processLogger);
        request.setLoginUserId(CurrentUser.getEmployeeId());
        receiveCheckInService.lossProduct(request);
        return taskId;
    }

    @RequestMapping(value = "/renewReceiveCheckInRelateBill", method = RequestMethod.POST)
    public BaseResponse renewReceiveCheckInRelateBill(@RequestBody RenewReceiveCheckInRequest request) {
        return receiveCheckInService.renewReceiveCheckInRelateBill(request);
    }

    @RequestMapping(value = "/checkCanEdit", method = RequestMethod.POST)
    public BaseResponse checkCanEdit(@RequestBody ReceiveCheckInParameter request) {
        return receiveCheckInService.checkCanEdit(request);
    }

    @PostMapping(value = "/queryLogs")
    public PageResponse<EshopRefundCheckInSysLog> queryLogs(@RequestBody PageRequest<EshopRefundCheckInSysQueryParams> request) {
        return SysLogUtil.query(request);
    }

    private PageMode checkMode(String modeStr) {
        for (PageMode mode : PageMode.values()) {
            if (mode.name().equalsIgnoreCase(modeStr)) {
                return mode;
            }
        }
        return PageMode.NEW;
    }

    /**
     * 检测物流信息是否为拒收或者无信息件 返回标记和备注信息
     *
     * @param freightNo 退货物流单号
     * @return 返回实体
     */
    @GetMapping("/checkFreightInfo/{freightNo}")
    public EshopReceiveCheckInResponse checkFreightInfo(@PathVariable("freightNo") String freightNo) {
        EshopRefundReceiveCheckInEntity checkInEntity = receiveCheckInService.checkFreightInfo(freightNo);
        EshopReceiveCheckInResponse response = new EshopReceiveCheckInResponse();
        response.setSuccess(true);
        response.setCheckInEntities(Collections.singletonList(checkInEntity));
        return response;
    }

    @GetMapping("/getDetailForRefuse/{freightNo}")
    public List<EshopRefundApplyDetail> getDetailForRefuse(@PathVariable("freightNo") String freightNo) {
        return receiveCheckInService.checkFreighgetDetailForRefusetInfo(freightNo);
    }


    @RequestMapping(value = "/checkDifferent", method = RequestMethod.POST)
    public boolean checkDifferent(@RequestBody EshopRefundReceiveCheckInEntity checkInEntity) {
        return receiveCheckInService.checkDifferent(checkInEntity);
    }

    @RequestMapping(value = "/checkDifferentForQty", method = RequestMethod.POST)
    public boolean checkDifferentForQty(@RequestBody EshopRefundReceiveCheckInEntity checkInEntity) {
        return receiveCheckInService.checkDifferentForQty(checkInEntity);
    }

    @RequestMapping(value = "/changePrintState", method = RequestMethod.POST)
    public boolean changePrintState(@RequestBody List<EshopRefundReceiveCheckInEntity> list) {
        return receiveCheckInService.changePrintState(list);
    }

    /*
    改动东西 重新提交
     */
    @RequestMapping(value = "/batchCheckin", method = RequestMethod.POST)
    @AsyncProcess(threadPoolName = AsyncProcess.ProcessName.modifyMappingtypeProcessName)
    public BaseResponse batchCheckin(@RequestBody BatchProcessRequest<EshopRefundReceiveCheckInEntity> request) {
        BaseResponse response = new BaseResponse();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(request.getProcessId());
        processLogger.appendMsg("开始批量验货");
        List<EshopRefundReceiveCheckInEntity> list = request.getList();
        receiveCheckInService.batchCheckin(list, processLogger);
        processLogger.appendMsg("批量验货成功");
        return response;
    }

}
