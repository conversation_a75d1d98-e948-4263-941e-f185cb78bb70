package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.redis.process.message.bll.RedisMessageUtil;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.redis.process.message.config.RedisMessageSys;
import com.wsgjp.ct.sale.biz.jarvis.common.ProcessParamsVo;
import com.wsgjp.ct.sale.biz.jarvis.common.ProcessUtil;
import com.wsgjp.ct.sale.biz.jarvis.common.process.Message;
import com.wsgjp.ct.sale.biz.jarvis.common.process.ProcessMessage;
import com.wsgjp.ct.sale.biz.jarvis.config.NavProcessStatus;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.reject.BatchPreProcessRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.DeliverBillOperateResult;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.split.SplitDigitStrategyConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.wms.BatchReturnDeliverBillRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.wms.ReturnDeliverBillRequest;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverProcessService;
import com.wsgjp.ct.sale.biz.jarvis.service.nav.NavStockService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.StrategyUtils;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.SimpleStrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.RedisProcessMsgUtils;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@Api(description = "订单流程")
@RequestMapping("/${app.id}/jarvis/deliverBillProcessState")
public class DeliverBillProcessController {

    private final Logger logger = LoggerFactory.getLogger(DeliverBillProcessController.class);
    private BillDeliverProcessService billDeliverProcessService;
    private NavStockService processService;

    public DeliverBillProcessController(BillDeliverProcessService billDeliverProcessService, NavStockService processService) {
        this.billDeliverProcessService = billDeliverProcessService;
        this.processService = processService;
    }



    private void CheckLimit(String limitName) {
        if (PermissionValiateService.isAdmin()) {
            return;
        }
        if (StringUtils.isEmpty(limitName)) {
            return;
        }
        if (!PermissionValiateService.validate(PermissionSysConst.REJECT_AUDIT)) {
            throw new RuntimeException("您没有该操作的权限！");
        }
    }

    @ApiOperation(value = "获取流程是否启用", notes = "")
    @PostMapping("getProcess")
    public NavProcessStatus getProcess() {
        return this.processService.getNavProcessStatus();
    }

    @ApiOperation(value = "获取进度", notes = "")
    @PostMapping("getProgressMessage")
    public String getProgressMessage(String msgId) {
        ValueOperations redis = RedisMessageSys.getRedis();
        return (String) redis.get(msgId);
    }

    @ApiOperation(value = "获取进度日志", notes = "获取进度日志")
    @PostMapping("getOperateProgressMessage")
    public ProcessMessage getOperateProgressMessage(String msgId) {
        boolean finish = RedisMessageUtil.getFinish(msgId);
        String processMsg = RedisMessageUtil.getProcessMsg(msgId);

        ProcessMessage processMessage = new ProcessMessage();
        processMessage.setFinish(finish);
        processMessage.setMessages(new ArrayList<>());
        if (StringUtils.isEmpty(processMsg)) {
            return processMessage;
        }
        String[] split = processMsg.split("\\r\\n");
        for (String str : split) {
            String[] split1 = str.split(",");
            try {
                Message message = JsonUtils.toObject(split1[1] + "," + split1[2], Message.class);
                if (message == null) {
                    processMessage.getMessages().add(new Message().setMsg(str));
                } else {
                    processMessage.getMessages().add(new Message().setMsg(split1[0] + "," + message.getMsg()).setColor(message.getColor()));
                }
            } catch (Exception e) {
                processMessage.getMessages().add(new Message().setMsg(str));
            }
        }

        return processMessage;
    }

    @ApiOperation(value = "获取业务执行结果", notes = "获取业务执行结果")
    @PostMapping("getProgressServiceResult")
    public ServiceResult getProgressServiceResult(String msgId) {
        ServiceResult serviceResult = new ServiceResult();
        String res = RedisProcessMsgUtils.getProcessMsg(msgId);
        if (StringUtils.isEmpty(res)) {
            return serviceResult;
        }
        try {
            List<BigInteger> taskIds = JsonUtils.toList(res, BigInteger.class);
            serviceResult.setVchcodes(taskIds);
            serviceResult.setWarehouseTaskIds(taskIds);
        } catch (Exception e) {
            String errMsg = String.format("进度框获取执行结果-解析结果失败，原因：%s",e.getMessage());
            logger.error(errMsg, e);
        }
        return serviceResult;
    }


    @ApiOperation(value = "获取进度是否完成", notes = "获取进度是否完成")
    @PostMapping("getProgressFinish")
    public Boolean getProgressFinish(String msgId) {
        return RedisMessageUtil.getFinish(msgId);
    }

    @ApiOperation(value = "获取进度消息", notes = "获取进度")
    @PostMapping("getHavResultProgressMessage")
    public List<SimpleStrategyProcessLog> getHavResultProgressMessage(String msgId, Integer index, Integer length) {
        return billDeliverProcessService.getHavResultProgressMessage(msgId, index, length);
    }

    @ApiOperation(value = "获取进度消息", notes = "获取进度")
    @PostMapping("getImportResultProgressMessage")
    public SplitDigitStrategyConfig getImportResultProgressMessage(String msgId) {
        return billDeliverProcessService.getImportResultProgressMessage(msgId);
    }

    @ApiOperation(value = "获取执行成功的消息", notes = "获取进度")
    @PostMapping("getSuccessProgressMessage")
    public List<SimpleStrategyProcessLog> getSuccessProgressMessage(String msgId, Integer index, Integer length) {
        return billDeliverProcessService.getSuccessProgressMessage(msgId, index, length);
    }

    @ApiOperation(value = "获取执行失败的消息", notes = "获取进度")
    @PostMapping("getErrorProgressMessage")
    public List<SimpleStrategyProcessLog> getErrorProgressMessage(String msgId, Integer index, Integer length) {
        return billDeliverProcessService.getErrorProgressMessage(msgId, index, length);
    }

    @ApiOperation(value = "获取进度消息的数量", notes = "获取进度")
    @PostMapping("getHavResultProgressMessageCount")
    public boolean getHavResultProgressMessageCount(String msgId) {
        return billDeliverProcessService.getHavResultProgressMessageCount(msgId);
    }

    @ApiOperation(value = "获取当前进度情况")
    @RequestMapping(value = "/process/get", method = RequestMethod.POST)
    public ProcessUtil.CurrentProcess getProcessMessage(@RequestBody ProcessParamsVo vo) {
        ProcessUtil.CurrentProcess res = ProcessUtil.getProcess(vo.getProcesskey(), vo.getStart());
        return res;
    }
    /** 驳回方法 **/
    @ApiOperation(value = "批量返回流程-有进度框", notes = "")
    @PostMapping("batchPreProcess")
    @NeedProcessMsg(threadPoolName = NeedProcessMsg.ProcessName.defaultProcessName)
    public void batchPreProcess(@RequestBody BatchPreProcessRequest request) {
        RedisProcessMessage processMessage = new RedisProcessMessage(request.getProcessId());
        RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
        List<DeliverBillOperateResult> deliverBillOperateResults = new ArrayList<>();
        try {
            if (CollectionUtils.isEmpty(request.getWarehouseTaskIds())) {
                return;
            }
            logger.appendMsg("驳回开始执行");
            deliverBillOperateResults = billDeliverProcessService.returnProcess(request, logger);
            if (CollectionUtils.isNotEmpty(deliverBillOperateResults)) {
                List<SimpleStrategyProcessLog> errorResults = new ArrayList<>();
                List<SimpleStrategyProcessLog> successResults = new ArrayList<>();
                for (DeliverBillOperateResult result : deliverBillOperateResults) {
                    SimpleStrategyProcessLog processLog = getSimpleStrategyProcessLog(result);
                    if (result.isSuccess()) {
                        successResults.add(processLog);
                    } else {
                        errorResults.add(processLog);
                    }
                }
                if (CollectionUtils.isNotEmpty(errorResults)) {
                    StrategyUtils.cacheProcessMessage2(request.getProcessId(), errorResults);
                }
                if (CollectionUtils.isNotEmpty(successResults)) {
                    StrategyUtils.cacheSuccessProcessMessage(request.getProcessId(), successResults);
                }
            }
        } catch (Exception ex) {
            this.logger.error("驳回失败", ex);
            StrategyProcessLog result = new StrategyProcessLog();
            result.setMessage("执行错误：" + ex.getMessage());
            StrategyUtils.cacheProcessMessage(request.getProcessId(), Collections.singletonList(result));
            logger.appendMsg("驳回执行错误：" + ex.getMessage());
        } finally {
            RedisProcessMsgUtils.appendEndMessage(logger, "驳回执行结束",
                    request.getWarehouseTaskIds().size(),
                    request.getWarehouseTaskIds().size()-(int)deliverBillOperateResults.stream().filter(r->!r.isSuccess()).map(DeliverBillOperateResult::getWarehouseTaskId).distinct().count());
            processMessage.setFinish();
        }
    }

    @NotNull
    private static SimpleStrategyProcessLog getSimpleStrategyProcessLog(DeliverBillOperateResult result) {
        SimpleStrategyProcessLog processLog = new SimpleStrategyProcessLog();
        processLog.setBillNumber(result.getBillNumber());
        processLog.setTradeOrderId(result.getTradeOrderId());
        processLog.setMessage(result.getErrorMessage());
        processLog.setContent(result.getErrorMessage());
        processLog.setVchcode(result.getVchcode());
        processLog.setWarehouseTaskId(result.getWarehouseTaskId());
        processLog.setTaskNumber(result.getTaskNumber());
        return processLog;
    }

    @ApiOperation(value = "交易单-批量返回待审核", notes = "wms模块调用")
    @PostMapping("batchReturnDeliverBill")
    public void batchReturnDeliverBill(@RequestBody BatchReturnDeliverBillRequest request) {
        ThreadPoolFactory.build("jarvis-deliverbill-process").executeAsync(t -> {
            RedisProcessMessage processMessage = new RedisProcessMessage(request.getProcessId());
            RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
            try {
                if (!StringUtils.isEmpty(request.getProcessId())) {
                    int index = 1;
                    logger.appendMsg("开始执行");
                    int msgId = logger.appendMsg("开始驳回...");
                    List<ReturnDeliverBillRequest> preProcessRequests = new ArrayList<>();
                    List<BigInteger> vchcodes = request.getRequests().stream().map(ReturnDeliverBillRequest::getVchcode).collect(Collectors.toList());
                    for (ReturnDeliverBillRequest requestItem : request.getRequests()) {
                        if (vchcodes.contains(requestItem.getVchcode())) {
                            preProcessRequests.add(requestItem);
                        }
                    }

                    List<DeliverBillOperateResult> operateResults = new ArrayList<>();
                    for (ReturnDeliverBillRequest requestItem : preProcessRequests) {
                        operateResults = billDeliverProcessService.returnDeliverBill(requestItem);
                        logger.modifyMsg(String.format("共 %s 条单据，已驳回 %s 条单据", preProcessRequests.size(), index), msgId);
                        index++;
                    }

                    List<SimpleStrategyProcessLog> logs = operateResults.stream().map(obj -> {
                        SimpleStrategyProcessLog log = new SimpleStrategyProcessLog();
                        log.setTradeOrderId(obj.getTradeOrderId());
                        log.setBillNumber(obj.getBillNumber());
                        log.setContent(obj.getErrorMessage());
                        return log;
                    }).collect(Collectors.toList());
                    StrategyUtils.cacheProcessSimpleMessage(request.getProcessId(), logs);

                    operateResults = operateResults.stream().filter(o -> !o.isSuccess()).collect(Collectors.toList());
                    for (DeliverBillOperateResult result : operateResults) {
                        logger.appendMsg(String.format("订单编号：%s，驳回失败。失败原因：%s", result.getTradeOrderId(), result.getErrorMessage()));
                    }
                } else {
                    request.getRequests().forEach(requestItem -> billDeliverProcessService.returnDeliverBill(requestItem));
                }
            } catch (Exception ex) {
                logger.appendMsg("执行错误：" + ex.getMessage());
                SimpleStrategyProcessLog log = new SimpleStrategyProcessLog();
                log.setContent(ex.getMessage());
                StrategyUtils.cacheProcessSimpleMessage(request.getProcessId(), Collections.singletonList(log));
            } finally {
                logger.appendMsg("执行结束");
                processMessage.setFinish();
            }
        },null);
    }
    @ApiOperation(value = "返回流程", notes = "")
    @PostMapping("preProcess")
    public BaseResponse toPreProcess(@RequestBody BatchPreProcessRequest request) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        try {
            CheckLimit(request.getLimitName());
            billDeliverProcessService.returnProcess(request, null);
        } catch (Exception ex) {
            response.setCode("-1");
            response.setMsg(String.format("驳回失败，请重试，原因：【%s】", ex.getMessage()));
        }
        return response;
    }
    @ApiOperation(value = "批量返回流程-无进度框", notes = "sale模块调用")
    @PostMapping("batchPre")
    public List<BatchPreResult> batchPre(@RequestBody BatchPreProcessRequest request) {
        List<BatchPreResult> resultList = new ArrayList<>();
        List<DeliverBillOperateResult> errOperateResults = billDeliverProcessService.returnProcess(request, null);
        if(CollectionUtils.isNotEmpty(errOperateResults)){
            Optional<DeliverBillOperateResult> first = errOperateResults.stream().filter(r -> !r.isSuccess()).findFirst();
            if (first.isPresent()) {
                DeliverBillOperateResult result = first.get();
                BatchPreResult res = new BatchPreResult();
                res.setVchcode(result.getVchcode());
                res.setTaskNumber(result.getTaskNumber());
                res.setBillNumber(result.getBillNumber());
                res.setTradeId(result.getTradeOrderId());
                res.setSuccess(false);
                res.setMessage(result.getErrorMessage());
                res.setCanContinue(false);
                resultList.add(res);
            }
        }
        return resultList;
    }

    static class BatchPreResult {
        private BigInteger vchcode;
        private String billNumber;
        private String taskNumber;
        private String tradeId;
        private boolean success;
        private String message;
        private boolean canContinue;

        public BigInteger getVchcode() {
            return vchcode;
        }

        public void setVchcode(BigInteger vchcode) {
            this.vchcode = vchcode;
        }

        public String getBillNumber() {
            return billNumber;
        }

        public void setBillNumber(String billNumber) {
            this.billNumber = billNumber;
        }

        public String getTradeId() {
            return tradeId;
        }

        public void setTradeId(String tradeId) {
            this.tradeId = tradeId;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public boolean isCanContinue() {
            return canContinue;
        }

        public void setCanContinue(boolean canContinue) {
            this.canContinue = canContinue;
        }

        public String getTaskNumber() {
            return taskNumber;
        }

        public void setTaskNumber(String taskNumber) {
            this.taskNumber = taskNumber;
        }
    }

    static class ServiceResult{
        private List<BigInteger> vchcodes;
        private List<BigInteger> warehouseTaskIds;

        public List<BigInteger> getVchcodes() {
            return vchcodes;
        }

        public void setVchcodes(List<BigInteger> vchcodes) {
            this.vchcodes = vchcodes;
        }

        public List<BigInteger> getWarehouseTaskIds() {
            return warehouseTaskIds;
        }

        public void setWarehouseTaskIds(List<BigInteger> warehouseTaskIds) {
            this.warehouseTaskIds = warehouseTaskIds;
        }
    }
}

