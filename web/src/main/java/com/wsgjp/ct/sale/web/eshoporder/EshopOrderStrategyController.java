package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.eshoporder.entity.strategy.EshopOrderStrategyDto;
import com.wsgjp.ct.sale.biz.eshoporder.service.strategy.EshopOrderStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "商品解析策略相关")
@RequestMapping("${app.id}/eshoporder/strategy")
@RestController
public class EshopOrderStrategyController {

    private EshopOrderStrategyService service;

    public EshopOrderStrategyController(EshopOrderStrategyService service) {
        this.service = service;
    }

    @PostMapping("/saveEshopOrderStrategy")
    @ApiOperation(value = "保存策略")
    public void saveEshopOrderStrategy(@RequestBody List<EshopOrderStrategyDto> pageDtos)  {
        service.saveEshopOrderStrategy(pageDtos);
    }

    @PostMapping("/getEshopOrderStrategy")
    @ApiOperation(value = "查询策略")
    public EshopOrderStrategyDto getEshopOrderStrategy() {
        return service.getEshopOrderStrategy();
    }
}
