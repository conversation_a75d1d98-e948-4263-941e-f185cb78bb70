package com.wsgjp.ct.sale.web.jarvis.controller;

/**
 * @Description TODO
 * @Date 2021-11-09 9:23
 * @Created by lingxue
 */

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.eshoporder.EShopOrderApi;
import com.wsgjp.ct.sale.biz.api.response.GetPageCodeResponse;
import com.wsgjp.ct.sale.platform.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description TODO
 * @Date 2021-11-08 16:05
 * @Created by lingxue
 */
@RestController
@Api(description = "拼多多风控检查")
@RequestMapping("/${app.id}/jarvis/pinduoduoController")
public class PinduoduoController {
    private EShopOrderApi eShopOrderApi;

    public PinduoduoController(EShopOrderApi eShopOrderApi)
    {
        this.eShopOrderApi = eShopOrderApi;
    }

    @PostMapping("getPageCode")
    @ApiOperation(value = "获取页面code", notes = "获取页面code")
    public GetPageCodeResponse getPageCode(HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        if(StringUtils.isEmpty(referer)){
            throw new RuntimeException("获取PageCode所需的http referer，请检查");
        }
        return eShopOrderApi.getPinduoduoPageCode(ShopType.PinDuoDuo,referer).getData();
    }
}

