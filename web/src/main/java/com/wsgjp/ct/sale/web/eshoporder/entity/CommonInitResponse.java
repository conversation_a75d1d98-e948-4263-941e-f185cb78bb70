package com.wsgjp.ct.sale.web.eshoporder.entity;


import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.IndustryConfig;
import com.wsgjp.ct.support.global.entity.SysGlobalConfig;

/**
 * <AUTHOR>
 * @date 2020/6/3
 */
public class CommonInitResponse {


  public CommonInitResponse() {
    IndustryConfig industryConfig = GlobalConfig.get(IndustryConfig.class);
    SysGlobalConfig globalConfig = GlobalConfig.get(SysGlobalConfig.class);
    enabledProps = industryConfig.isEnabledProps();
    enabledBatch = industryConfig.isEnabledBatch();
    enabledSerialNumber=industryConfig.isEnabledSerialNumber();
    enabledTax = globalConfig.isEnabledTax();
  }

  private Boolean enabledBatch;
  private Boolean enabledProps;
  private Boolean enabledTax;
  private Boolean enabledSerialNumber;

  public Boolean getEnabledBatch() {
    return enabledBatch;
  }

  public void setEnabledBatch(Boolean enabledBatch) {
    this.enabledBatch = enabledBatch;
  }

  public Boolean getEnabledProps() {
    return enabledProps;
  }

  public void setEnabledProps(Boolean enabledProps) {
    this.enabledProps = enabledProps;
  }

  public Boolean getEnabledTax() {
    return enabledTax;
  }

  public void setEnabledTax(Boolean enabledTax) {
    this.enabledTax = enabledTax;
  }

  public Boolean getEnabledSerialNumber() {
    return enabledSerialNumber;
  }

  public void setEnabledSerialNumber(Boolean enabledSerialNumber) {
    this.enabledSerialNumber = enabledSerialNumber;
  }
}
