package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.jarvis.dto.distribution.NotifySupplierSendFreightInfo;
import com.wsgjp.ct.sale.biz.jarvis.dto.distribution.SupplierBillDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.distribution.SupplierBillQueryParam;
import com.wsgjp.ct.sale.biz.jarvis.service.distribution.DistributionService;
import com.wsgjp.ct.sale.platform.dto.warehouse.EshopSupplierInfo;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.sale.web.jarvis.response.CommonResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-07-22
 */
@RestController
@Api(description = "发货单代销接口")
@RequestMapping("/${app.id}/jarvis/deliverBillDistribution")
public class DeliverBillDistributionController {

    private static final Logger logger = LoggerFactory.getLogger(DeliverBillDistributionController.class);

    private DistributionService distributionService;

    public DeliverBillDistributionController(DistributionService distributionService) {
        this.distributionService = distributionService;
    }

    @ApiOperation("获取供货商列表")
    @PostMapping("getSupplier")
    public List<EshopSupplierInfo> getSupplierInfo(@RequestBody GetSupplierInfoRequest request) {
        return distributionService.getSupplierInfo(request.getVchcodes());
    }

    /*@ApiOperation("修改供货商信息")
    @PostMapping("updateSupplier")
    public boolean updateSupplierInfo(@RequestBody UpdateSupplierInfoRequest request) {
        return distributionService.updateSupplierInfo(request.getVchcodes(),request.getScpProxyId(),request.getScpProxyName());
    }

    @ApiOperation("淘宝分单")
    @PostMapping("distribute")
    @PermissionCheck(key = DeliverPermissionConst.JARVIS_DELIVER_TAOBAO_DISTRIBUTION)
    public DistributeOrderResponse distribute(@RequestBody DistributeRequest request) {
        return distributionService.distribute(request.getVchcodes());
    }

    @ApiOperation("取消淘宝分单")
    @PostMapping("distributeCancel")
    @PermissionCheck(key = DeliverPermissionConst.JARVIS_DELIVER_TAOBAO_DISTRIBUTION_CANCEL)
    public CancelDistributeResponse distributeCancel(@RequestBody DistributeRequest request) {
        return distributionService.distributeCancel(request.getVchcodes());
    }*/

    @ApiOperation("提交供应商发货")
    @PostMapping("submitSupplier")
    public BaseResponse submitSupplier(@RequestBody DistributeRequest request) {
        try {
            distributionService.submitSupplier(request.getWarehouseTaskIds());
        } catch (Exception e) {
            return CommonResponse.fail(e.getMessage());
        }
        return CommonResponse.success();
    }

    @ApiOperation("取消供应商发货")
    @PostMapping("cancelSubmitSupplier")
    public BaseResponse cancelSubmitSupplier(@RequestBody DistributeRequest request) {
        try {
            return CommonResponse.success(distributionService.cancelSubmitSupplier(request.getWarehouseTaskIds()));
        } catch (Exception e) {
            return CommonResponse.fail(e.getMessage());
        }
    }

    @ApiOperation("获取供应商发货订单")
    @PostMapping("querySupplierBills")
    public PageResponse<SupplierBillDTO> querySupplierBills(@RequestBody SupplierBillQueryParam request) {
        logger.debug("获取供应商发货订单 参数：{}", JsonUtils.toJson(request));
        return distributionService.querySupplierBills(request);
    }

    @ApiOperation("同步供应商发货信息")
    @PostMapping("syncSupplierFreightInfo")
    public BaseResponse syncSupplierFreightInfo(@RequestBody List<NotifySupplierSendFreightInfo> freightInfos) {
        try {
            logger.debug("同步供应商发货信息 参数：{}", JsonUtils.toJson(freightInfos));
            distributionService.syncSupplierFreightInfo(freightInfos);
            return CommonResponse.success();
        } catch (Exception e) {
            logger.error("同步供应商发货失败", e);
            return CommonResponse.fail(e.getMessage());
        }
    }
}

class GetSupplierInfoRequest {
    private List<BigInteger> vchcodes;

    public List<BigInteger> getVchcodes() {
        return vchcodes;
    }

    public void setVchcodes(List<BigInteger> vchcodes) {
        this.vchcodes = vchcodes;
    }
}

class UpdateSupplierInfoRequest {
    private List<BigInteger> vchcodes;
    private String scpProxyId;
    private String scpProxyName;

    public List<BigInteger> getVchcodes() {
        return vchcodes;
    }

    public void setVchcodes(List<BigInteger> vchcodes) {
        this.vchcodes = vchcodes;
    }

    public String getScpProxyId() {
        return scpProxyId;
    }

    public void setScpProxyId(String scpProxyId) {
        this.scpProxyId = scpProxyId;
    }

    public String getScpProxyName() {
        return scpProxyName;
    }

    public void setScpProxyName(String scpProxyName) {
        this.scpProxyName = scpProxyName;
    }
}

class DistributeRequest {
    private List<BigInteger> vchcodes;
    private List<BigInteger> warehouseTaskIds;

    public List<BigInteger> getVchcodes() {
        return vchcodes;
    }

    public void setVchcodes(List<BigInteger> vchcodes) {
        this.vchcodes = vchcodes;
    }

    public List<BigInteger> getWarehouseTaskIds() {
        return warehouseTaskIds;
    }

    public void setWarehouseTaskIds(List<BigInteger> warehouseTaskIds) {
        this.warehouseTaskIds = warehouseTaskIds;
    }
}