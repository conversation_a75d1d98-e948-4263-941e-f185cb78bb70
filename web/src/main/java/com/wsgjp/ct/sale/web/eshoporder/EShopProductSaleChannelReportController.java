package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSaleChannelReport;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopProductSaleChannelReportRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductaChannelReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: cql
 * @date: 2023/8/8
 */

@RestController
@Api(description = "商品销售渠道统计")
@RequestMapping("${app.id}/eshoporder/report")
public class EShopProductSaleChannelReportController {
    @Autowired
    private EshopProductaChannelReportService channelReportService;
    @ApiOperation(value = "查询网店商品销售渠道统计")
    @PostMapping("/getEShopProductSaleReport")
    public PageResponse<EshopProductSaleChannelReport> getEShopProductSaleReport (@RequestBody PageRequest<EshopProductSaleChannelReportRequest> esQueryParams) {
        List<EshopProductSaleChannelReport> eshopProductSaleReport = channelReportService.getEshopProductSaleReport(esQueryParams.getQueryParams());
        return PageDevice.readPage( eshopProductSaleReport);
    }
}
