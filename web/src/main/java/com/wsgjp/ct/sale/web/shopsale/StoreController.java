package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.api.profile.ProfileApi;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.shopsale.constanst.OrganizationType;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.base.BaseInfoLog;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.log.BaseLogQueryParams;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.store.BatchStoreRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.store.QueryStoreParams;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.store.*;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.base.BaseResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.baseinfo.Etype;
import com.wsgjp.ct.sale.biz.shopsale.service.StoreService;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import com.wsgjp.ct.sale.common.enums.OtypeBusinessType;
import com.wsgjp.ct.sale.common.enums.OtypeStoreType;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.annontaion.NgpResource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "门店相关")
@RequestMapping("${app.id}/shopsale/store")
@RestController
public class StoreController {

    private final ProfileApi profileApi;
    private StoreService service;

    public StoreController(StoreService service,
                           @Qualifier("com.wsgjp.ct.sale.biz.api.profile.ProfileApi") ProfileApi profileApi) {
        this.service = service;
        this.profileApi = profileApi;
    }
    //region 门店

    @ApiOperation(value = "获取门店列表")
    @PostMapping(value = "/getStoreList")
    @NgpResource(name = "shopsale.getStoreList", tagStrings = "'tagA,'+0")
    public PageResponse<StoreInfo> getStoreList(@RequestBody PageRequest<QueryStoreParams> parameter) {
        parameter.getQueryParams().setOcategory(OrganizationType.ZY_STORE);
        parameter.getQueryParams().setBusinessType(OtypeBusinessType.ZY.getCode());
        parameter.getQueryParams().setStoreType(OtypeStoreType.LS_STORE.getCode());
        parameter.getQueryParams().setProfileId(CurrentUser.getProfileId());
        List<StoreInfo> storeList = service.getStoreList(parameter);
        return PageDevice.readPage(storeList);
    }

    @ApiOperation(value = "获取购买以及已开启云零售数")
    @PostMapping(value = "/getBuyAndAllowLoginCount")
    @WebLogs
    public Map<String, Integer> getBuyAndAllowLoginCount() {
        return service.getBuyAndAllowLoginCount();
    }

    @ApiOperation(value = "获取门店列表(仅获取允许云零售登录的门店)")
    @GetMapping(value = "/getStoreNameList/{etypeId}")
    @WebLogs
    public List<StoreInfo> getStoreNameList(@PathVariable(name = "etypeId") BigInteger etypeId) {
        return service.getStoreNameList(etypeId);
    }

    @ApiOperation(value = "获取门店基本信息")
    @PostMapping(value = "/getStoreInfo")
    @NgpResource(name = "shopsale.getStoreInfo", tagStrings = "'tagA,'+0")
    public StoreInfo getStoreInfo(@RequestBody QueryStoreParams params) {
        return service.getStoreInfo(params);
    }

    @ApiOperation(value = "获取门店完整信息")
    @PostMapping(value = "/getFullStoreInfo")
    public StoreInfo getFullStoreInfo(@RequestBody BigInteger otypeId) {
        return service.getFullStoreInfo(otypeId, null, null);
    }

    @ApiOperation(value = "获取使用中门店完整信息(仅开通了云零售登录)")
    @PostMapping(value = "/getUsingFullStoreInfo")
    @WebLogs
    public StoreInfo getUsingFullStoreInfo(@RequestBody BigInteger otypeId) {
        return service.getFullStoreInfo(otypeId, 0, 1);
    }

    @ApiOperation(value = "保存或修改门店")
    @PostMapping(value = "/saveOrUpdateStore")
    public BaseResponse saveOrUpdateStore(@RequestBody StoreInfo eshopInfo) {
        BaseResponse response = new BaseResponse();
        try {
            response = service.saveOrUpdateStore(eshopInfo);
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    @ApiOperation(value = "停用门店")
    @PostMapping(value = "/stopStores")
    public void stopStores(@RequestBody BatchStoreRequest request) {
        try {
            if (request.getIds() == null || request.getIds().size() == 0) {
                throw new RuntimeException("请至少选择一个门店！");
            }
            service.stopStores(request);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex.getMessage());
        }
    }

    @ApiOperation(value = "更改允许云零售登录状态")
    @PostMapping(value = "/allowAutoPosLogin")
    @WebLogs
    public void allowAutoPosLogin(@RequestBody StoreInfo storeInfo) {
        try {
            if (null == storeInfo || storeInfo.getId() == null) {
                throw new RuntimeException("请至少选择一个门店！");
            }
            storeInfo.setProfileId(CurrentUser.getProfileId());
            service.modifyAllowPosLoginState(storeInfo);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex.getMessage());
        }
    }


    @ApiOperation(value = "批量删除门店")
    @PostMapping(value = "/deleteStores")
    @PermissionCheck(key = PermissionSysConst.SHOP_STORE_DELETE)
    public void deleteStores(@RequestBody List<BigInteger> otypeList) {
        try {
            if (otypeList == null || otypeList.size() == 0) {
                throw new RuntimeException("请选择要删除的门店信息！");
            }
            service.deleteStores(otypeList);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex.getMessage());
        }
    }

    @ApiOperation(value = "检查门店是否被使用")
    @PostMapping(value = "/checkStoreUsed")
    String checkStoreUsed(@RequestBody List<BigInteger> otypeList) {
        return service.checkStoreUsed(otypeList);
    }

    //endregion

    //region 收银机
    @ApiOperation(value = "获取收银机列表")
    @PostMapping(value = "/getCashierList")
    public List<StoreCashier> getCashierList(@RequestBody BigInteger otypeId) {
        return service.getCashierList(otypeId, null);
    }


    @ApiOperation(value = "检查收银机")
    @PostMapping(value = "/getCashierCountByName")
    boolean getCashierCountByName(@RequestBody StoreCashier storeCashier) {
        return service.getCashierCountByName(storeCashier);
    }

    @ApiOperation(value = "检查收银机编号")
    @PostMapping(value = "/getCashierCountByUserCode")
    boolean getCashierCountByUserCode(@RequestBody StoreCashier storeCashier) {
        return service.getCashierCountByUserCode(storeCashier);
    }


    //endregion

    //region 门店职员


    @ApiOperation(value = "获取职员列表")
    @PostMapping(value = "/getStoreEtypeList")
    public List<StoreEtype> getStoreEtypeList(@RequestBody BigInteger otypeId) {
        return service.getStoreEtypeList(otypeId);
    }

    @ApiOperation(value = "获取职员列表过滤重复职员")
    @PostMapping(value = "/getStoreEtypeListOther")
    public List<StoreEtype> getStoreEtypeListOther(@RequestBody BigInteger otypeId) {
        return service.getStoreEtypeListOther(otypeId);
    }

    //endregion
    //region 门店设置

    @ApiOperation(value = "获取门店设置")
    @PostMapping(value = "/getStoreSetting")
    public StoreInfo getStoreSetting(@RequestBody BigInteger otypeId) {
        return service.getStoreSetting(otypeId);
    }

    @ApiOperation(value = "保存门店设置")
    @PostMapping(value = "/setStoreSetting")
    public boolean setStoreSetting(@RequestBody StoreInfo request) {
        return service.setStoreSetting(request);
    }

    //endregion

    //region 支付方式

    @ApiOperation(value = "获取基本的支付方式")
    @PostMapping(value = "/getStorePaywayList")
    public List<StorePayway> getStorePaywayList(@RequestBody Map params) {
        return service.getStorePaymentList(params);
    }

    @ApiOperation(value = "获取职员列表")
    @PostMapping(value = "/getEtypeList")
    public List<Etype> getEtypeList(@RequestBody Map params) {
        return service.getEtypeList(params);
    }

    @ApiOperation(value = "获取门店支付方式列表 和 新单据信息")
    @PostMapping(value = "/getPaymentListByOtypeIdMap")
    @WebLogs
    @Deprecated
    // 未发现使用
    public Map getPaymentListByOtypeIdMap(@RequestBody Map request) {
        return service.getStorePaymentByOtypeIdMap(request);
    }

    @ApiOperation(value = "获取门店支付方式列表 和 新单据信息")
    @PostMapping(value = "/getPaymentListByOtypeId")
    @Deprecated
    // 未发现使用
    public List<StorePayway> getPaymentListByOtypeId(@RequestBody BigInteger otypeId) {
        return service.getStorePaymentByOtypeId(otypeId);
    }

    //endregion

    //region 日志

    @ApiOperation(value = "新增日志")
    @PostMapping(value = "/addStoreLog")
    public void addEshopLog() {
        PubSystemLogService.saveInfo("进入门店");
    }

    @ApiOperation(value = "查看日志列表")
    @PostMapping(value = "/queryOtypeLog")
    public PageResponse<BaseInfoLog> queryOtypeLog(@RequestBody PageRequest<BaseLogQueryParams> request) {
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        return LogService.query(request);
    }

    @ApiOperation(value = "保存修改日志")
    @PostMapping(value = "/saveChangeLog")
    public void saveChangeLog(@RequestBody BaseInfoLog baseInfoLog) {
        service.saveLog(baseInfoLog);
    }


    @ApiOperation(value = "保存打印配置")
    @PostMapping(value = "/savePrintConfig")
    public void savePrintConfig(@RequestBody List<PrintConfigInfo> printConfigList) {
        service.savePrintConfig(printConfigList);
    }


    @ApiOperation(value = "获取打印配置")
    @PostMapping(value = "/getPrintConfig")
    public List<PrintConfigInfo> getPrintConfigInfo(@RequestBody BigInteger cashierId) {
        return service.getPrintConfigList(cashierId);
    }

    //endregion
}
