package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BaseQuery;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopMatchRole;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-03-13 15:37
 */
public class EshopMatchRolesSaveRequest extends BaseQuery {
    private List<EshopMatchRole>  matchRoles;
    private boolean isSyncStock;
    private String processId;


    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public boolean isSyncStock() {
        return isSyncStock;
    }

    public void setSyncStock(boolean syncStock) {
        isSyncStock = syncStock;
    }

    public List<EshopMatchRole> getMatchRoles() {
        return matchRoles;
    }

    public void setMatchRoles(List<EshopMatchRole> matchRoles) {
        this.matchRoles = matchRoles;
    }
}
