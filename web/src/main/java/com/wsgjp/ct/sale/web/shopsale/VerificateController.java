package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.VerificateModifyDetailsRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.VerificateRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.VerificateResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.VerificateStateCountResponse;
import com.wsgjp.ct.sale.biz.shopsale.service.WmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(value = "${app.id}/shopsale/verificate", tags = {"全渠道订单核销"})
@RestController
@RequestMapping("${app.id}/shopsale/verificate")
public class VerificateController {
    @Autowired
    private WmsService wmsService;

    @ApiOperation(value = "核销")
    @PostMapping("/doVerificate")
    @WebLogs
    public VerificateResponse doVerificate(@RequestBody VerificateRequest request) {
        return wmsService.doVerificate(request);
    }

    @ApiOperation(value = "系统发货")
    @PostMapping("/doYunLiLocalSend")
    @WebLogs
    public VerificateResponse doYunLiLocalSend(@RequestBody VerificateRequest request) {
        return wmsService.doYunLiLocalSend(request);
    }

    @ApiOperation(value = "单据状态的数量")
    @PostMapping("/queryVerificateStateCount")
    @WebLogs
    public VerificateStateCountResponse queryVerificateStateCount(@RequestBody VerificateRequest request) {
        return wmsService.queryVerificateStateCount(request);
    }

    @ApiOperation(value = "单据详情")
    @PostMapping("/queryVerificateBillInfo")
    @WebLogs
    //BillDeliverDTO wms和sale使用的实体有区别 无法映射
    public List<Map> queryVerificateBillInfo(@RequestBody PageRequest<VerificateRequest> request) {
        return wmsService.queryVerificateBillInfo(request);
    }

    @ApiOperation(value = "表头信息")
    @PostMapping("/queryVerificateHeaderInfo")
    //BillDeliverDTO wms和sale使用的实体有区别 无法映射
    public PageResponse<Map> queryVerificateHeaderInfo(@RequestBody PageRequest<VerificateRequest> request) {
        return wmsService.queryVerificateHeaderInfo(request);
    }

    @ApiOperation(value = "根据核销码 获取对应商品详情")
    @PostMapping("/queryBillInfoByNumber")
    @WebLogs
    //BillDeliverDTO wms和sale使用的实体有区别 无法映射
    public List<Map> queryBillInfoByNumber(@RequestBody VerificateRequest request) {
        return wmsService.queryBillInfoByNumber(request);
    }


    @ApiOperation(value = "获取全渠道订单 筛选条件")
    @PostMapping("/getStatus")
    HashMap getStatus(@RequestBody Map param) {
        return wmsService.getStatus(param);
    }


    @ApiOperation(value = "修改明细批次/序列号")
    @PostMapping("/updateTaskDetails")
    VerificateResponse updateTaskDetails(@RequestBody VerificateModifyDetailsRequest request) {
        return wmsService.updateTaskDetails(request);
    }

    @ApiOperation(value = "全渠道选单核销")
    @PostMapping("/verifyWithOrder")
    VerificateResponse verifyWithOrder(@RequestBody VerificateRequest request) {
        return wmsService.verifyWithOrder(request);
    }

}
