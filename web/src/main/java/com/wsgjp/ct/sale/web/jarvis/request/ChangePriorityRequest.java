package com.wsgjp.ct.sale.web.jarvis.request;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class ChangePriorityRequest {
    private BigInteger changeRuleId;
    private BigInteger changePriority;
    private BigInteger changedRuleId;
    private BigInteger changedPriority;

    public BigInteger getChangeRuleId() {
        return changeRuleId;
    }

    public void setChangeRuleId(BigInteger changeRuleId) {
        this.changeRuleId = changeRuleId;
    }
    public BigInteger getChangedRuleId() {
        return changedRuleId;
    }

    public void setChangedRuleId(BigInteger changedRuleId) {
        this.changedRuleId = changedRuleId;
    }


    public BigInteger getChangePriority() {
        return changePriority;
    }

    public void setChangePriority(BigInteger changePriority) {
        this.changePriority = changePriority;
    }

    public BigInteger getChangedPriority() {
        return changedPriority;
    }

    public void setChangedPriority(BigInteger changedPriority) {
        this.changedPriority = changedPriority;
    }
}
