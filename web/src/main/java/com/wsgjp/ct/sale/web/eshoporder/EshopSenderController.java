package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopSenderInfo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchOrderParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QuerySenderRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ModifySellerMemoResult;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.SaveSenderResult;
import com.wsgjp.ct.sale.biz.eshoporder.service.EshopOrderApiService;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.SenderService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-03-02 15:31
 */
@Api(tags = "寄件人相关")
@RequestMapping("${app.id}/eshoporder/sender")
@RestController
public class EshopSenderController {

    private final SenderService service;
    private final EshopOrderApiService apiService;

    public EshopSenderController(SenderService service, EshopOrderApiService apiService) {
        this.service = service;
        this.apiService = apiService;
    }

    @PostMapping(value = "/getList")
    public List<EshopSenderInfo> getSenderList(@RequestBody QuerySenderRequest request){
        if(request.getSenderName()==null || "".equals(request.getSenderName())){
            return new ArrayList<>();
        }
        return service.getSender(request);
    }

    @PostMapping("/save")
    public SaveSenderResult saveSender(@RequestBody EshopSenderInfo senderInfo){
        return apiService.saveSender(senderInfo);
    }

    @PostMapping("/modifyById")
    public SaveSenderResult modifySender(@RequestBody EshopSenderInfo senderInfo){
        SaveSenderResult result=new SaveSenderResult();
        try {
            service.modifySender(senderInfo);
            result.setSender(senderInfo);
        }catch (Exception ex){
            result.setSuccess(false);
            result.setErrorMessage(String.format("保存寄件人失败%s",ex.getMessage()));
        }
        return result;
    }

    @PostMapping("/query")
    public List<EshopSenderInfo> querySender(@RequestBody QuerySenderRequest request){
        return service.getSender(request);
    }

    @PostMapping("/modifySellerMemo")
    public List<ModifySellerMemoResult> modifySellerMemo(@RequestBody List<BatchOrderParams> batchOrderParams){
        if(batchOrderParams==null){
            throw new RuntimeException("请求参数为空，修改失败");
        }
        return service.modifySellerMemo(batchOrderParams);
    }
}
