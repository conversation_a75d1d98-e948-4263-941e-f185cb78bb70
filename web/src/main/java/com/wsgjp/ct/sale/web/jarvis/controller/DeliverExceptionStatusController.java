package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.DeliverPrintBatchQueryParamDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.DeliverExceptionStatusCountParam;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverExceptionStatusService;
import com.wsgjp.ct.sale.biz.jarvis.service.printbatch.PrintBatchServerManager;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-12-17
 **/
@RestController
@RequestMapping("/${app.id}/jarvis/exceptionStatus")
public class DeliverExceptionStatusController {

    private final DeliverExceptionStatusService deliverExceptionStatusService;
    private PrintBatchServerManager printBatchServerManager;

    public DeliverExceptionStatusController(DeliverExceptionStatusService deliverExceptionStatusService, PrintBatchServerManager printBatchServerManager) {
        this.deliverExceptionStatusService = deliverExceptionStatusService;
        this.printBatchServerManager = printBatchServerManager;
    }

    @PostMapping("countExceptionStatus")
    public Map<String, Integer> countExceptionStatus(@RequestBody DeliverExceptionStatusCountParam param) {
        return deliverExceptionStatusService.countExceptionStatus(param);
    }

    @PostMapping("countPrintBatchException")
    public Map<String,String> countPrintBatchException(@RequestBody DeliverPrintBatchQueryParamDTO param){
        return  printBatchServerManager.countPrintBatchException(param);
    }
}
