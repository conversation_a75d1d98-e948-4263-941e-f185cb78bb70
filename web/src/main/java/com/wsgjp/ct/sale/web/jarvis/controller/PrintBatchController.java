package com.wsgjp.ct.sale.web.jarvis.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Api("PDA波次相关业务")
@RequestMapping("/${app.id}/jarvis/printBatchController")
public class PrintBatchController {
//    private PrintBatchServerManager printBatchServerManager;
//
//    public PrintBatchController(PrintBatchServerManager printBatchServerManager) {
//        this.printBatchServerManager = printBatchServerManager;
//    }
//
//    @ApiOperation(value = "获取子波次总数量", notes = "获取子波次总数量")
//    @PostMapping("/getChildBatchCount")
//    public BaseResponse getChildBatchCount(@RequestBody GetChildBatchCountRequest request) {
//        return this.printBatchServerManager.getDeliverPrintBatchCoordinationCountByDefault(request.getKtypeId());
//    }
//
//    @ApiOperation(value = "获取子波次列表", notes = "获取子波次列表")
//    @PostMapping("/getChildBatchList")
//    public BaseResponse getChildBatchList(@RequestBody GetChildBatchListParam request) {
//        return this.printBatchServerManager.getChildBatchList(request);
//    }
//
//    @ApiOperation(value = "获取子波次明细", notes = "获取子波次明细")
//    @PostMapping("/getChildBatchDetailList")
//    public List<BatchDetail> getChildBatchDetailList(@RequestBody BigInteger CoordinationId) {
//        return this.printBatchServerManager.getChildBatchDetailList(CurrentUser.getProfileId(),CoordinationId);
//    }
//
//    @ApiOperation(value = "领用、取消领用子波次、挂起波次恢复成配货中", notes = "领用、取消领用子波次、挂起波次恢复成配货中")
//    @PostMapping("/updatePrintBatchCoordinationAssignState")
//    public BaseResponse updatePrintBatchCoordinationAssignState(@RequestBody ChangeCoordinationAssignStateRequest request) {
//        return this.printBatchServerManager.updatePrintBatchCoordinationAssignState(request);
//    }
//
//    @ApiOperation(value = "子波次拣货完成", notes = "子波次拣货完成")
//    @PostMapping("/childFinish")
//    public BaseResponse childFinish(@RequestBody ChildFinishRequest request) {
//        return this.printBatchServerManager.childFinish(request);
//    }
//
//    @ApiOperation(value = "剔除波次", notes = "剔除波次")
//    @PostMapping("/excludePrintBatch")
//    public BaseResponse excludePrintBatch(@RequestBody ExcludePrintBatchRequest request) {
//        String processId = UId.newId().toString();
//        printBatchServerManager.excludePrintBatch(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), request.getVchcodes(), request.isClearnFreightInfo(), new RedisProcessMessage(processId));
//        boolean finish = RedisMessageUtil.getFinish(processId);
//        String processMsg = RedisMessageUtil.getProcessMsg(processId);
//        if (processMsg.contains("只支持待配货波次！")) {
//            return BaseResponse.error(-1, "只支持待配货波次！");
//        }
//        if (processMsg.contains("并发导致数据异常，请查询后重新操作！")) {
//            return BaseResponse.error(-2, "并发导致数据异常，请查询后重新操作！");
//        }
//        return BaseResponse.success();
//    }
//
//    @ApiOperation(value = "挂起波次", notes = "挂起波次")
//    @PostMapping("/holdBatch")
//    BaseResponse holdBatch(@RequestBody HoldBatchRequest request) throws Exception {
//        return this.printBatchServerManager.holdBatch(CurrentUser.getProfileId(), request);
//    }
}
