package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.page.PageSummary;
import com.wsgjp.ct.sale.biz.jarvis.config.PageDefinedAsyncTaskProcess;
import com.wsgjp.ct.sale.biz.jarvis.entity.EShopSaleStatistics;
import com.wsgjp.ct.sale.biz.jarvis.entity.definedTask.DefinedAsyncQueryTypeEnum;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.EShopSaleStatisticsQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.service.EShopSaleStatisticsService;
import com.wsgjp.ct.sale.biz.jarvis.service.impl.EShopSaleStatisticsServiceImpl;
import com.wsgjp.ct.sale.biz.jarvis.service.pageHelper.PageHelperDefinedSyncTaskComponentServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: wangChongYang
 * @date: 2021/11/8
 * @apiNote
 */

@RestController
@Api(description = "网店运营统计")
@RequestMapping("/${app.id}/jarvis/eShopSaleStatistics")
public class EShopSaleStatisticsController {

    @Autowired
    private EShopSaleStatisticsService esService;
    @Autowired
    private PageHelperDefinedSyncTaskComponentServiceImpl pageHelperDefinedSyncTaskComponentService;

    @ApiOperation(value = "查询网店运营统计")
    @PostMapping("/getEShopSaleStatistics")
    @PageDefinedAsyncTaskProcess(threadPoolName = PageDefinedAsyncTaskProcess.ThreadPoolNames.EShopSaleStatistic,queryType = DefinedAsyncQueryTypeEnum.EShopSaleStatistic, loadClass = EShopSaleStatisticsServiceImpl.class)
    public PageResponse<EShopSaleStatistics> getEShopSaleStatistics (@RequestBody PageRequest<EShopSaleStatisticsQueryParams> esQueryParams) {
        if(esQueryParams.getQueryParams().getQueryType() == 1) {
            return esService.getEShopSaleOrderStatistics(esQueryParams);
        } else if(esQueryParams.getQueryParams().getQueryType() == 4) {
            return esService.getEShopPtypeStatistics(esQueryParams);
        }
        // 使用装饰类 替代pageHelper 保障原有的代码逻辑不会受到影响
        return esService.getEShopSaleStatistics(esQueryParams, pageHelperDefinedSyncTaskComponentService);
//        return esService.getEShopSaleStatistics(esQueryParams);
    }
    @ApiOperation(value = "查询网店运营统计合计（按单据统计）")
    @PostMapping("/getEShopSaleOrderStatistics/count")
    public PageSummary getEShopSaleOrderStatisticsCount(@RequestBody PageRequest<EShopSaleStatisticsQueryParams> esQueryParams) {
        return esService.getEShopSaleOrderStatisticsCount(esQueryParams);
    }
    @ApiOperation(value = "查询网店运营统计合计（按单据统计）")
    @PostMapping("/getEShopSaleDetailStatistics/count")
    public PageSummary getEShopSaleDetailStatistics(@RequestBody PageRequest<EShopSaleStatisticsQueryParams> esQueryParams) {
        return esService.getEShopSaleDetailStatistics(esQueryParams);
    }

}
