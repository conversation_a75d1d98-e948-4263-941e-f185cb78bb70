package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import bf.datasource.page.Sort;
import bf.datasource.wings.provide.WingsCutSelect;
import com.alibaba.excel.EasyExcel;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopAuthService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopPluginService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopProductService;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.api.EshopOrderAutoToolApi;
import com.wsgjp.ct.sale.biz.common.threads.ThreadManager;
import com.wsgjp.ct.sale.biz.eshoporder.api.enums.WebToToolApiEnum;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.MalDownloadTaskEntity;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.SubmitResList;
import com.wsgjp.ct.sale.biz.eshoporder.api.totool.WebToToolApiFactory;
import com.wsgjp.ct.sale.biz.eshoporder.api.totool.packing.Pair;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderToolConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.ExportConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.SyncOrderConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.AutoSubmitConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderGlobalConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderSysDataConfig;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.OrderDownLoadTask;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.OrderDownRecordDto;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.QueryOrderRecordParam;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.CheckOrderDelete;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceSubmitConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.DownloadType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.MainPageSaleOrderDataQueryType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.OrderCreateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.log.EshopSaleOrderSyncTaskLog;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.orderImport.EshopOrderIdImportVo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.orderImport.OrderImportTemplate;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchModifyOrderRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchOrderParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.DeletedOrderParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.PartialRefreshReuqest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.platformdecrypt.PlatformDecryptRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.*;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopSaleOrdeSyncTaskQueryParams;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopSaleOrderSysLog;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopSaleOrderSysQueryParams;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.orderimport.EShopSaleOrderImportConfigManager;
import com.wsgjp.ct.sale.biz.eshoporder.service.orderimport.EshopSaleOrderImportService;
import com.wsgjp.ct.sale.biz.eshoporder.service.platformdecrypt.PlatformDecryptServive;
import com.wsgjp.ct.sale.biz.eshoporder.service.receiver.EshopBuyerService;
import com.wsgjp.ct.sale.biz.eshoporder.submit.impl.EshopSaleOrderManualSubmit;
import com.wsgjp.ct.sale.biz.eshoporder.submit.impl.RestoreOrderInfo;
import com.wsgjp.ct.sale.biz.eshoporder.submit.support.OrderInfoForUdate;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.SaleOrderUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.biz.jarvis.dto.BuildRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.SubmitDeliverRelationDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.SaleOrderSelectDeliverBillRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.SaleOrderSelectDeliverBillResponse;
import com.wsgjp.ct.sale.biz.jarvis.open.JarvisOpenApi;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.common.config.EshopOrderCommonConfig;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.log.LogOrder;
import com.wsgjp.ct.sale.common.enums.OperationEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.OrderOpreateType;
import com.wsgjp.ct.sale.common.log.PlatformBizKeyPointFeedback;
import com.wsgjp.ct.sale.common.utils.ProcessUtil;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.anno.NgpMonitorResource;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.monitor.entity.VeidooConst;
import com.wsgjp.ct.sale.platform.dto.order.entity.EshopSaleOrderFreight;
import com.wsgjp.ct.sale.platform.dto.token.EshopAuthInfo;
import com.wsgjp.ct.sale.platform.entity.request.auth.GetTokenRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.CloseOrderRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.ModifyMemoRequest;
import com.wsgjp.ct.sale.platform.entity.request.plugin.GetPlatOrderUrlRequest;
import com.wsgjp.ct.sale.platform.entity.request.plugin.GetPlatProductUrlRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.CloseOrderResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.ModifyOrderMemoResponse;
import com.wsgjp.ct.sale.platform.enums.SellerFlag;
import com.wsgjp.ct.sale.platform.enums.TradeTypeEnum;
import com.wsgjp.ct.sale.platform.factory.moatlog.config.MoatConfig;
import com.wsgjp.ct.sale.platform.feature.order.EshopOpenOrderUrlFeature;
import com.wsgjp.ct.sale.platform.feature.order.EshopOrderMemoFeature;
import com.wsgjp.ct.sale.platform.feature.product.EshopOpenProductUrlFeature;
import com.wsgjp.ct.sale.web.eshoporder.entity.enums.PageMode;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.CloseSaleOrderParameter;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.EshopSaleOrderInitRequest;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.UpdateAndSubmitOrdersRequest;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.EshopSaleOrderEditInitResponse;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.InitRelationResponse;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.OrderSelectorPageInit;
import com.wsgjp.ct.sis.client.SisClient;
import com.wsgjp.ct.sis.client.entity.EncryptFullAdapter;
import com.wsgjp.ct.sis.client.entity.platform.TbParameterConstant;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.IndustryConfig;
import com.wsgjp.ct.support.global.entity.SysGlobalConfig;
import com.wsgjp.ct.support.redis.factory.CacheType;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.opentracing.Span;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.redis.RedisPoolFactory;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.*;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-12-30
 */
@Api(tags = "原始订单相关")
@RequestMapping("${app.id}/eshoporder/order")
@RestController
public class EshopSaleOrderController {

    private final EshopSaleOrderPlatformService platformService;
    private final EshopSaleOrderManualService manualService;
    private final EshopSaleOrderService orderService;
    private final EshopService eshopService;
    private final EshopSaleOrderImportService orderImportService;
    private final PlatformDecryptServive decryptService;
    private final EshopSaleOrderNotifyService notifyService;
    private final EshopBuyerService eshopBuyerService;
    private final RedisPoolFactory redisPoolFactory;
    private static final Logger logger = LoggerFactory.getLogger(EshopSaleOrderController.class);
    private final BifrostEshopProductService eshopProductService;
    private final BifrostEshopOrderService eshopOrderService;
    private final BifrostEshopPluginService eshopPluginService;
    private final PlatformBizKeyPointFeedback platformBizKeyPointFeedback;
    private final EshopOrderCommonConfig eshopOrderCommonConfig;
    private final EshopOrderAutoToolApi ehopOrderAutoToolApi;
    private MoatConfig moatConfig;
    private final BifrostEshopAuthService bifrostEshopAuthService;
    private final EshopOrderToolConfig eshopOrderToolConfig;
    private final SyncOrderConfig syncOrderConfig;
    private final BaseInfoService baseInfoService;
    private final EshopOrderDownloadRecordService recordService;
    private static final String NAV_REDIS_TITLE = "NAV";
    private static final String NAV_REDIS_SEC0NDS_KEY = "nav_redis_seconds";
    private final RedisPoolFactory factory;
    private final JarvisOpenApi jarvisOpenApi;

    public EshopSaleOrderController(
            EshopSaleOrderPlatformService platformService,
            EshopSaleOrderManualService manualService,
            EshopSaleOrderService orderService,
            EshopService eshopService,
            EshopSaleOrderImportService orderImportService,
            PlatformDecryptServive decryptService, EshopSaleOrderNotifyService notifyService,
            EshopBuyerService eshopBuyerService, BifrostEshopProductService eshopProductService,
            BifrostEshopOrderService eshopOrderService, BifrostEshopPluginService eshopPluginService,
            PlatformBizKeyPointFeedback platformBizKeyPointFeedback, EshopOrderCommonConfig eshopOrderCommonConfig,
            EshopOrderAutoToolApi ehopOrderAutoToolApi, RedisPoolFactory redisPoolFactory,
            MoatConfig moatConfig, BifrostEshopAuthService bifrostEshopAuthService,
            EshopOrderToolConfig eshopOrderToolConfig, SyncOrderConfig syncOrderConfig,
            BaseInfoService baseInfoService, EshopOrderDownloadRecordService recordService,
            RedisPoolFactory factory, JarvisOpenApi jarvisOpenApi) {
        this.platformService = platformService;
        this.manualService = manualService;
        this.orderService = orderService;
        this.eshopService = eshopService;
        this.orderImportService = orderImportService;
        this.decryptService = decryptService;
        this.notifyService = notifyService;
        this.eshopBuyerService = eshopBuyerService;
        this.redisPoolFactory = redisPoolFactory;
        this.eshopProductService = eshopProductService;
        this.eshopOrderService = eshopOrderService;
        this.eshopPluginService = eshopPluginService;
        this.platformBizKeyPointFeedback = platformBizKeyPointFeedback;
        this.eshopOrderCommonConfig = eshopOrderCommonConfig;
        this.ehopOrderAutoToolApi = ehopOrderAutoToolApi;
        this.bifrostEshopAuthService = bifrostEshopAuthService;
        this.eshopOrderToolConfig = eshopOrderToolConfig;
        this.syncOrderConfig = syncOrderConfig;
        this.moatConfig = moatConfig;
        this.baseInfoService = baseInfoService;
        this.recordService = recordService;
        this.factory = factory;
        this.jarvisOpenApi = jarvisOpenApi;
    }


    @PostMapping(value = "/queryListOrders")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    // 打开原始订单监控埋点
    @NgpMonitorResource(name = "pl.bs.eshoporder.page.open", tagStrings = "tag,queryListOrders")
    public PageResponse<EshopSaleOrderEntity> queryListOrders(@RequestBody PageRequest<QueryOrderParameter> pageParameter) {
        PageResponse<EshopSaleOrderEntity> eshopSaleOrderEntityPageResponse = orderService.queryListOrdersPage(pageParameter, false, false);
        // 日志上报--查询订单
        List<LogOrder> list = eshopSaleOrderEntityPageResponse.getList().stream().map(item -> new LogOrder(item.getTradeOrderId(), item.getOtypeId())).collect(Collectors.toList());
//        platformBizKeyPointFeedback.feedback(list, OperationEnum.Select_Order);
        return eshopSaleOrderEntityPageResponse;
    }

    @PostMapping(value = "/queryListOrdersCount")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    @NgpMonitorResource(name = "pl.bs.eshoporder.page.open", tagStrings = "tag,queryListOrdersCount")
    @WingsCutSelect
    public int queryListOrdersCount(@RequestBody PageRequest<QueryOrderParameter> pageParameter) {
        return orderService.querySaleOrderListCount(pageParameter.getQueryParams());
    }

    @PostMapping(value = "/queryListOrderIds")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    public List<BigInteger> queryListOrderIds(@RequestBody PageRequest<QueryOrderParameter> pageParameter) {
        return orderService.querySaleOrderListIds(pageParameter.getQueryParams());
    }


    @PostMapping(value = "/queryListOrdersSimpleInfo")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    // 打开原始订单监控埋点
    @NgpMonitorResource(name = "pl.bs.eshoporder.page.open", tagStrings = "tag,queryListOrders")
    public PageResponse<EshopSaleOrderEntity> queryListOrdersSimpleInfo(@RequestBody PageRequest<QueryOrderParameter> pageParameter) {
        PageResponse<EshopSaleOrderEntity> eshopSaleOrderEntityPageResponse = orderService.queryListOrdersPage(pageParameter, true, true);
        // 日志上报--查询订单
        List<LogOrder> list = eshopSaleOrderEntityPageResponse.getList().stream().map(item -> new LogOrder(item.getTradeOrderId(), item.getOtypeId())).collect(Collectors.toList());
        platformBizKeyPointFeedback.feedback(list, OperationEnum.Select_Order);
        return eshopSaleOrderEntityPageResponse;
    }

    @PostMapping(value = "/queryListOrdersForSelectCount")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    @NgpMonitorResource(name = "pl.bs.eshoporder.page.open", tagStrings = "tag,queryListOrdersCount")
    public int queryListOrdersForSelectCount(@RequestBody PageRequest<QueryOrderParameter> pageParameter) {
        return orderService.querySaleOrderListForSelectCount(pageParameter.getQueryParams());
    }

    @PostMapping(value = "/queryListOrdersForSelect")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    // 打开原始订单监控埋点
    @NgpMonitorResource(name = "pl.bs.eshoporder.page.open", tagStrings = "tag,queryListOrders")
    public PageResponse<EshopSaleOrderEntity> queryListOrdersForSelect(@RequestBody PageRequest<QueryOrderParameter> pageParameter) {
        PageResponse<EshopSaleOrderEntity> eshopSaleOrderEntityPageResponse = orderService.queryListOrdersForSelect(pageParameter, true, false);
        return eshopSaleOrderEntityPageResponse;
    }

    @PostMapping(value = "/partialRefresh")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    public PageResponse<EshopSaleOrderEntity> queryListOrders(@RequestBody PartialRefreshReuqest partialRefreshReuqest) {
        return PageDevice.readPage(orderService.partialRefreshOrderList(partialRefreshReuqest.getId()));
    }

    @PostMapping(value = "/supportViewOpenOrder")
    public boolean supportViewOpenOrder(@RequestBody DeletedOrderParameter parameter) {
        ShopType shopType = ShopType.valueOf(parameter.getShopType());
        if (shopType.equals(ShopType.ErrorEshop)) {
            return false;
        }
        return EshopUtils.isFeatureSupported(EshopOpenOrderUrlFeature.class, ShopType.valueOf(parameter.getShopType()));
    }

    @PostMapping(value = "/openUrl")
    public String openUrl(@RequestBody QueryOrderDetailParameter parameter) {
        EshopInfo eshop = eshopService.getEshopInfoById(parameter.getProfileId(), parameter.getEshopId());
        if (eshop == null) {
            throw new RuntimeException("未查询到网店");
        }
        boolean featureSupported = EshopUtils.isFeatureSupported(EshopOpenProductUrlFeature.class, eshop.getEshopType());
        if (!featureSupported) {
            return "";
        }
        GetPlatProductUrlRequest request = new GetPlatProductUrlRequest();
        request.setPlatformSkuId(parameter.getPlatformSkuId());
        request.setPlatformNumId(parameter.getPlatformSkuInfo());
        request.setDefaultSkuId(parameter.getDefaultSkuId());
        request.setShopId(eshop.getOtypeId());
        return eshopProductService.getPlatformProductUrl(request);
    }

    @PostMapping(value = "/openOnlineOrderUrl")
    public String openOnlineOrderUrl(@RequestBody DeletedOrderParameter parameter) {
        EshopInfo eshop = eshopService.getEshopInfoById(parameter.getProfileId(), parameter.getOtypeId());
        if (eshop == null) {
            throw new RuntimeException("未查询到网店");
        }
        boolean featureSupported = EshopUtils.isFeatureSupported(EshopOpenProductUrlFeature.class, eshop.getEshopType());
        if (!featureSupported) {
            return "";
        }
        GetPlatOrderUrlRequest getPlatOrderUrlRequest = new GetPlatOrderUrlRequest();
        getPlatOrderUrlRequest.setOrderId(parameter.getTradeOrderId());
        getPlatOrderUrlRequest.setShopId(eshop.getOtypeId());
        return eshopOrderService.getPlatformOrderUrl(getPlatOrderUrlRequest);
    }

    /**
     * 手动提交订单
     *
     * @param parameter
     * @return
     */
    @PostMapping(value = "/submit")
    public SubmitResponse submit(@RequestBody QueryOrderParameter parameter) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        EshopSaleOrderManualSubmit orderSubmit = new EshopSaleOrderManualSubmit(processLogger, parameter);
        SubmitResponse response = new SubmitResponse();
        ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.ORDER_MANUAL_SUBMIT_THREAD_NAME);
        Span traceSpan = ThreadManager.getTraceSpan();
        threadPool.executeAsync(invoker -> {
            ThreadManager.initTraceSpan(traceSpan);
            GeneralResult<SubmitResponse> result = orderSubmit.submit(null);
            MonitorService monitorService = GetBeanUtil.getBean(MonitorService.class);
            /**
             * 埋点监控
             */
            if (null != result && result.getData() != null && result.getData().getMap() != null) {
                HashMap<String, Object> map = result.getData().getMap();
                //  待提交订单数
                if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_MANUAL_TP_WAIT_COUNT.getTopic()) != null) {
                    monitorService.recordSum(MonitorTypeEnum.PL_BS_ORDER_COMMIT_MANUAL_TP_WAIT_COUNT.getTopic(), VeidooConst.ORDER_TYPE, "ALL", (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_MANUAL_TP_WAIT_COUNT.getTopic()));
                }
                //  提交预售单平均耗时
                if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_TP_TIME.getTopic()) != null) {
                    monitorService.recordTP(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_TP_TIME.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.ADVANCE_FORWARD_SALE.getName(), (Long) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_TP_TIME.getTopic()));
                }
                //  提交预售单QPS
                if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_QPS.getTopic()) != null) {
                    monitorService.recordOPSSSuccess(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_QPS.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.ADVANCE_FORWARD_SALE.getName(), (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_QPS.getTopic()));
                }
                //  提交预售单异常QPS
                if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_ERROR_QPS.getTopic()) != null) {
                    monitorService.recordOPSSFail(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_QPS.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.ADVANCE_FORWARD_SALE.getName(), (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_ERROR_QPS.getTopic()));
                }
                //  提交交易单平均耗时
                if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_TP_TIME.getTopic()) != null) {
                    monitorService.recordTP(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_TP_TIME.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.NORMAL.getName(), (Long) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_TP_TIME.getTopic()));
                }
                //  提交交易单QPS
                if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_QPS.getTopic()) != null) {
                    monitorService.recordOPSSSuccess(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_QPS.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.NORMAL.getName(), (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_QPS.getTopic()));
                }
                // 提交交易单异常QPS
                if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_ERROR_QPS.getTopic()) != null) {
                    monitorService.recordOPSSFail(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_QPS.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.NORMAL.getName(), (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_ERROR_QPS.getTopic()));
                }
            }
            processLogger.doFinish();
        }, null);
        response.setTaskId(taskId);
        return response;
    }

    @PostMapping(value = "/queryOrderDetails")
    public List<EshopSaleOrderDetail> queryOrdersDetails(@RequestBody QueryOrderDetailParameter parameter) {
        List<EshopSaleOrderDetail> detailList;
        detailList = orderService.queryOrdersDetails(parameter);
        return detailList;
    }

    @PostMapping(value = "/queryOrderFreights")
    public List<EshopSaleOrderFreight> queryOrderFreights(@RequestBody QueryOrderDetailParameter parameter) {
        return orderService.queryOrderFreights(parameter);
    }

    @PostMapping(value = "/getDeliverbills")
    public List<DeliverBillEntity> getDeliverbills(@RequestBody QueryOrderParameter parameter) {
        return orderService.getDeliverbills(parameter);
    }

    @PostMapping(value = "/showPanel")
    public EshopOrderGlobalConfig showPanel(@RequestBody QueryOrderParameter parameter) {
        return GlobalConfig.get(EshopOrderGlobalConfig.class);
    }

    @PostMapping(value = "/putSubmitConfig")
    public void insertSubmitConfig(@RequestBody AutoSubmitConfig config) {
        GlobalConfig.putAll(config);
        PubSystemLogService.saveInfo(" 修改平台订单提交配置");
    }

    @GetMapping(value = "/getSubmitConfig")
    public AutoSubmitConfig getSubmitConfig() {
        return GlobalConfig.get(AutoSubmitConfig.class);
    }

    @PostMapping("/getReciverInfo")
    public EshopBuyer getReciverInfo(@RequestBody QueryOrderParameter parameter) throws Exception {
        List<EshopSaleOrderEntity> orderEntityList = orderService.quertOrderList(parameter);
        if (null == orderEntityList || orderEntityList.size() == 0) {
            throw new RuntimeException("未查询到订单数据");
        }
        List<EncryptFullAdapter> encryptFullAdapterList = orderService.getEncryptInfos(orderEntityList);
        SisClient.batchDecrypt(encryptFullAdapterList);
        return orderService.buildReceiverInfo(encryptFullAdapterList.get(0), orderEntityList.get(0).getEshopBuyer());
    }

    @PostMapping("/decryptBuyer")
    public EshopBuyer decryptBuyer(@RequestBody QueryBuyerRequest parameter) {
        List<EshopBuyer> buyerList = eshopBuyerService.queryBuyerList(parameter);
        if (null == buyerList || buyerList.size() == 0) {
            throw new RuntimeException("未查询到订单数据");
        }
        return buyerList.get(0);
    }

    @PostMapping("/initTradeOrderId")
    public EshopSaleOrderEntity initTradeOrderId(@RequestBody EshopSaleOrderEntity order) {
        if (null == order || null == order.getOtypeId() || BigInteger.ZERO.compareTo(order.getOtypeId()) == 0) {
            throw new RuntimeException("初始化订单编号失败，参数异常");
        }
        order.setProfileId(CurrentUser.getProfileId());
        manualService.initOrderTradeId(order);
        if (StringUtils.isEmpty(order.getTradeOrderId())) {
            throw new RuntimeException("初始化订单编号失败，订单编号为空");
        }
        return order;
    }

    @RequestMapping(value = "/editInit", method = RequestMethod.POST)
    public EshopSaleOrderEditInitResponse getEditFormInitData(@RequestBody EshopSaleOrderInitRequest request) {
        String modeStr = request.getModeStr();
        BigInteger btypeId = request.getBtypeId();
        PageMode mode = checkMode(modeStr);
        EshopSaleOrderEditInitResponse response = new EshopSaleOrderEditInitResponse(mode);
        response.setPriceControl(orderService.getPriceControl());
        if (mode == PageMode.MODIFY) {
            EshopSaleOrderEntity modifyOrder = manualService.initModifyOrder(request.getId());
            response.setHasPostBill(false);
            response.setTitle("编辑订单");
            response.setSaleOrderEntity(modifyOrder);
        } else if (mode == PageMode.COPY_CREATE) {
            response.setTitle("复制新增订单");
        } else if (mode == PageMode.COPY_CREATE_FROM_WAREHOUSE) {
            EshopSaleOrderEntity copyOrder = jarvisOpenApi.getEshopSaleOrderByTaskId(new BuildRequest(request.getId()));
            response.setTitle("复制新增订单");
            response.setSaleOrderEntity(copyOrder);
        } else {
            EshopSaleOrderEntity createOrder = manualService.initCreateOrder(btypeId);
            response.setTitle("新增订单");
            response.setSaleOrderEntity(createOrder);
        }
        return response;
    }

    private PageMode checkMode(String modeStr) {
        for (PageMode mode : PageMode.values()) {
            if (mode.name().toLowerCase().equals(modeStr.toLowerCase())) {
                return mode;
            }
        }
        return PageMode.NEW;
    }

    @PostMapping(value = "/createPlatFromDetailKey")
    public List<String> createPlatFromDetailKey(@RequestBody QueryOrderDetailParameter parameter) {
        parameter.setMappingState(MappingState.NotMapping);
        List<EshopSaleOrderDetail> detailList = orderService.queryOrderDetails(parameter);
        List<String> keys = new ArrayList<>();
        for (EshopSaleOrderDetail detail : detailList) {
            String key = Md5Utils.md5(String.format("%s%s%s%s", parameter.getProfileId(), detail.getOtypeId(), detail.getPlatformPtypeId(), detail.getPlatformPropertiesName()));
            keys.add(key);
        }
        return keys;
    }


    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ManualOrderResponse saveEshopSaleOrder(@RequestBody EshopSaleOrderEntity saleOrder) {
        logger.error(String.format("profileId:%s,tradeOrderId:%s,saveEshopSaleOrder:%s", CurrentUser.getProfileId(), saleOrder.getTradeOrderId(), JsonUtils.toJson(saleOrder)));
        BigInteger otypeId = saleOrder.getOtypeId();
        if (otypeId == null || BigInteger.ZERO.compareTo(otypeId) == 0) {
            throw new RuntimeException("请选择正确网店！");
        }
        if (saleOrder.getKtypeId() == null || BigInteger.ZERO.compareTo(saleOrder.getKtypeId()) == 0) {
            throw new RuntimeException("请选择正确仓库！");
        }
        Otype otype = eshopService.getOtypeById(otypeId);
        if (otype == null || otype.isDeleted()) {
            throw new RuntimeException("网店已经被删除，保存订单失败！");
        }
        BigInteger vchcode = saleOrder.getId();
        saleOrder.setCreateType(OrderCreateType.INPUT);
        if (vchcode == null || vchcode.compareTo(BigInteger.ZERO) == 0) {
            return manualService.insertSaleOrder(saleOrder, otype);
        } else {
            return manualService.modifyOrder(saleOrder, otype);
        }
    }

    @PostMapping(value = "/queryLogs")
    public PageResponse<EshopSaleOrderSysLog> queryLogs(@RequestBody PageRequest<EshopSaleOrderSysQueryParams> request) {
        PageResponse<HashMap> response = SysLogUtil.query(request);
        //这里如果打开这个配置，会对日志的分页有些影响
        //一般用于排查棘手问题的，不建议开
        EshopOrderSysDataConfig sysDataConfig = GlobalConfig.get(EshopOrderSysDataConfig.class);
        if (null != response && sysDataConfig.getShowOrderLog() != 1 && CollectionUtils.isNotEmpty(response.getList())) {
            response.getList().removeIf(l -> l.get("comment").toString().contains("KEYLOG"));
        }
        return JsonUtils.toObject(JsonUtils.toJson(response), PageResponse.class);
    }

    @PostMapping(value = "/deleteOrders")
    public boolean deleteOrders(@RequestBody DeletedOrderParameter parameter) {
        boolean result = orderService.deleteEshopSaleOrder(parameter, notifyService);
        return true;
    }

    @PostMapping(value = "/updateOrderSingle")
    public void updateOrderSingle(@RequestBody List<BatchOrderParams> orderParams) {
        Map<BigInteger, List<BatchOrderParams>> collect = orderParams.stream().filter(params -> params.getEshopId().compareTo(BigInteger.ZERO) > 0).collect(Collectors.groupingBy(BatchOrderParams::getEshopId));
        for (Map.Entry<BigInteger, List<BatchOrderParams>> entry : collect.entrySet()) {
            try {
                EshopSaleOrderDownloadTask task = new EshopSaleOrderDownloadTask();
                String taskId = UId.newId().toString();
                task.setTaskId(taskId);
                ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
                task.setProcessLogger(processLogger);
                EshopInfo eshopInfo = eshopService.getEshopInfoById(CurrentUser.getProfileId(), entry.getKey());
                task.setEshopInfo(eshopInfo);
                getTask(entry, task);
                task.setOrderInfos(SaleOrderUtil.getEshopOrderEntityByOrderParams(entry.getValue()));
                EshopSaleOrderDownloadResponse response = platformService.doUpdateOrder(task);
                if (!response.isSuccess()) {
                    throw new RuntimeException(response.getMessage());
                }
            } catch (RuntimeException e) {
                throw new RuntimeException(String.format("更新订单异常,信息:%s", e.getMessage()));
            }
        }
    }

    /**
     * 更新订单
     *
     * @param orderParams
     * @return
     */
    @PostMapping(value = "/updateOrders")
    public String updateOrders(@RequestBody List<BatchOrderParams> orderParams) {
        Map<BigInteger, List<BatchOrderParams>> collect = orderParams.stream()
                .filter(params -> params.getEshopId().compareTo(BigInteger.ZERO) > 0)
                .collect(Collectors.groupingBy(BatchOrderParams::getEshopId));
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        platformService.updateOrder(collect, processLogger, taskId);
        return taskId;
    }


    @PostMapping(value = "/restoreOrders")
    public RestoreResponse restoreOrders(@RequestBody UpdateAndSubmitOrdersRequest orderParams) {
        RestoreResponse response = new RestoreResponse();
        doDeliverUpdateOrder(orderParams.getOrderInfos(), orderParams.isNeedUpdate());
        RestoreOrderInfo restore = new RestoreOrderInfo();
        List<SubmitResList> resLists = new ArrayList<>();
        List<SubmitDeliverRelationDTO> dtoList = restore.doBuildSubmitInfo(orderParams.getOrderInfos(), orderParams.getRelations(), resLists, true);
        response.setResLists(resLists);
        response.setDtoList(dtoList);
        return response;
    }

    public void doDeliverUpdateOrder(List<OrderInfoForUdate> orderInfo, boolean needUpdate) {
        if (!needUpdate) {
            return;
        }
        Map<BigInteger, List<OrderInfoForUdate>> collect = orderInfo.stream().filter(params -> params.getOtypeId().compareTo(BigInteger.ZERO) > 0).collect(Collectors.groupingBy(d -> d.getOtypeId()));
        for (Map.Entry<BigInteger, List<OrderInfoForUdate>> entry : collect.entrySet()) {
            try {
                EshopSaleOrderDownloadTask task = new EshopSaleOrderDownloadTask();
                EshopInfo eshopInfo = eshopService.getEshopInfoById(entry.getValue().get(0).getProfileId(), entry.getKey());
                task.setEshopInfo(eshopInfo);
                task.setOtypeId(entry.getKey());
                task.setDownloadType(DownloadType.BY_MODIFY);
                List<String> tradeIds = entry.getValue().stream().map(OrderInfoForUdate::getTradeId).collect(Collectors.toList());
                task.setFilterStr(StringUtils.join(tradeIds, ","));
                task.setNeedSubmit(false);
                EshopSaleOrderDownloadResponse updates = platformService.doUpdateOrder(task);
                if (!updates.isSuccess()) {
                    logger.error(updates.getMessage());
                }
            } catch (RuntimeException e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    @PostMapping(value = "/batchModifyBuyerMeessage")
    public SellerMemoResponse batchModifyBuyerMeessage(@RequestBody BatchModifyOrderRequest request) {
        SellerMemoResponse result = new SellerMemoResponse();
        result.setSuccess(true);
        result.setMessages(new ArrayList<>());
        try {
            if (StringUtils.isNotEmpty(request.getMessage())) {
                request.setMessage(HttpUtils.htmlDecode(request.getMessage()));
            }
            List<BatchOrderParams> paramsList = request.getOrderParamsList();
            Map<BigInteger, List<BatchOrderParams>> collect =
                    paramsList.stream()
                            .filter(params -> params.getEshopId().compareTo(BigInteger.ZERO) > 0)
                            .collect(Collectors.groupingBy(BatchOrderParams::getEshopId));
            for (Map.Entry<BigInteger, List<BatchOrderParams>> entry : collect.entrySet()) {
                List<BatchOrderParams> needs = entry.getValue();
                EshopInfo eshop = eshopService.getEshopInfoById(request.getProfileId(), entry.getKey());
//                EshopFactory entity = null;
                for (BatchOrderParams need : needs) {
                    OrderSellerFlag flag = OrderSellerFlag.values()[need.getFlagId()];
                    String message = request.getMessage();
                    String replace = "覆盖";
                    if (request.isClearFlag()) {
                        flag = OrderSellerFlag.WHITE;
                    }
                    if (!request.isClearFlag() && request.getFlagId() > 0) {
                        flag = OrderSellerFlag.values()[request.getFlagId()];
                    }
                    if (request.isFormat()) {
                        message = String.format("%s%s%s", need.getSellerMemo(),
                                StringUtils.isEmpty(need.getSellerMemo()) ? "" : ",", request.getMessage());
                        replace = "追加";
                    }
                    if (message.length() > 500) {
                        throw new RuntimeException("卖家备注超过系统允许的最大长度");
                    }
                    QueryOrderParameter parameter = new QueryOrderParameter();
                    parameter.setEshopOrderIdList(Arrays.asList(need.getEshopOrderId()));
                    parameter.setProfileId(CurrentUser.getProfileId());
                    parameter.setEshopOrderId(need.getEshopOrderId());
                    List<EshopSaleOrderEntity> orderList = orderService.getSimpleOrderInfo(parameter);
                    if (orderList == null || orderList.size() == 0) {
                        continue;
                    }
                    EshopSaleOrderEntity orderEntity = orderList.get(0);
                    String changeLog = String.format("原卖家备注:%s,%s,后卖家备注:%s;原旗帜:%s,改为:%s",
                            orderEntity.getSellerMemo(), replace,
                            message,
                            orderEntity.getSellerFlag().getEmptyName(), flag.getEmptyName());
                    if (need.isOnline()) {
                        String msg = "";
                        try {
                            boolean isimplement = EshopUtils.isFeatureSupported(EshopOrderMemoFeature.class, eshop.getEshopType());
                            if (!isimplement) {
                                throw new RuntimeException(String.format("%s不支持同步备注至后台", eshop.getEshopType().getName()));
                            }
//                            if (entity == null) {
//                                entity = EshopFactoryCreator.getInstance().setEshopSystemParams(eshop.toSystemParams()).create();
//                            }
//                            NormalResponse response = new NormalResponse();
                            ModifyMemoRequest memoRequest = new ModifyMemoRequest();
                            memoRequest.setFlag(SellerFlag.values()[flag.getCode()]);
                            memoRequest.setMemo(message);
                            memoRequest.setTradeId(need.getTradeOrderId());
                            memoRequest.setClearFlag(request.isClearFlag());
                            memoRequest.setFormat(request.isFormat());
                            memoRequest.setShopId(entry.getKey());
                            memoRequest.setShopType(eshop.getEshopType());
//                            EshopFactory factory = EshopFactoryCreator.getInstance().setEshopSystemParams(eshop.toSystemParams()).create();
//                            EshopOrderMemoFeature feature = factory.getFeature(EshopOrderMemoFeature.class);
//                            response = feature.modifyOrderMemo(memoRequest);
                            ModifyOrderMemoResponse response = eshopOrderService.modifyOrderMemo(memoRequest);

                            //todo  成功就修改数据库的 记录日志 通知发货单
                            if (!response.getSuccess()) {
                                msg = String.format("同步备注至后台失败：%s", response.getMessage());
                            }
                        } catch (Exception ignored) {
                            msg = String.format("同步备注至后台失败：%s", ignored.getMessage());
                        }
                        if (!StringUtils.isEmpty(msg)) {
                            result.setSuccess(false);
                            TradeMsg taskMsg = new TradeMsg(need.getTradeOrderId(), msg);
                            result.getMessages().add(taskMsg);
                        }
                    }
                    orderService.updateEshopOrderSellerMemo(request.getProfileId(), flag.getCode(), need.getEshopOrderId(), message);
                    orderEntity.setSellerMemo(message);
                    orderEntity.setSellerFlag(flag);
                    notifyService.notifyLocal(orderEntity);
                    SysLogUtil.add(SysLogUtil.buildLog(orderEntity, OrderOpreateType.MODIFY_BUYER_MESSAGE, changeLog));
                }
            }
        } catch (RuntimeException ex) {
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

    @PostMapping(value = "/batchModifyMessage")
    public Map<String, String> batchModifyMessage(@RequestBody BatchModifyOrderRequest request) {
        Map<String, String> result = new HashMap<>();
        try {
            if (StringUtils.isNotEmpty(request.getMessage())) {
                request.setMessage(HttpUtils.htmlDecode(request.getMessage()));
            }
            List<BatchOrderParams> paramsList = request.getOrderParamsList();
            for (BatchOrderParams need : paramsList) {
                if (request.isFormat()) {
                    need.setRemark(String.format("%s%s%s", need.getRemark(), StringUtils.isEmpty(need.getRemark()) ? "" : ",", request.getMessage()));
                }
                QueryOrderParameter parameter = new QueryOrderParameter();
                parameter.setEshopOrderId(need.getEshopOrderId());
                String subMessage = request.isFormat() ? need.getRemark() : request.getMessage();
                if (subMessage.length() > 200) {
                    throw new RuntimeException("系统备注超过允许的最大长度");
                }
                String sellerMessage = (subMessage.length() > 200 ? subMessage.substring(0, 200) : subMessage).trim();
                EshopSaleOrderEntity orderEntity = orderService.getSimpleOrder(parameter);
                String changeLog = String.format("原系统备注:%s,%s,改为:%s",
                        orderEntity.getRemark(), request.isFormat() ? "追加" : "覆盖", sellerMessage);
                orderService.updateEShopOrderRemark(request.getProfileId(), need.getEshopOrderId(), sellerMessage);
                SysLogUtil.add(SysLogUtil.buildLog(orderEntity, OrderOpreateType.MODIFY_REAMRK, changeLog));
            }
        } catch (Exception ex) {
            result.put("result", "false");
            result.put("msg", String.format("修改失败:%s", ex.getMessage()));
            return result;
        }
        result.put("result", "true");
        result.put("msg", "修改成功");
        return result;
    }


    /**
     * 修改卖家备注
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/asyncModifyBuyerMeessage")
    public String asyncModifyBuyerMeessage(@RequestBody BatchModifyOrderRequest request) {
        // 日志上报---修改系统备注
        List<LogOrder> collect = request.getOrderParamsList().stream().map(item -> {
            return new LogOrder(item.getTradeOrderId(), item.getEshopId());
        }).collect(Collectors.toList());
        platformBizKeyPointFeedback.feedback(collect, OperationEnum.Modify_Order);

        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        try {
            ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.DOWNLOAD_ORDER_THREAD_NAME);
            threadPool.executeAsync(invoker -> {
                long sTime = System.currentTimeMillis();
                orderService.batchModifyBuyerMeessage(request, processLogger);
                System.out.println("修改备注总耗时：" + (System.currentTimeMillis() - sTime) + "ms,一共修改了" + request.getOrderParamsList().size() + "条");
                processLogger.doFinish();
            }, null);
        } catch (RuntimeException e) {
            processLogger.appendMsg(String.format("更新订单异常,信息:%s", e.getMessage()));
            processLogger.doFinish();
        }
        return taskId;
    }

    @PostMapping("/updateOrderFlag")
    public void updateOrderFlag(@RequestBody QueryOrderParameter parameter) {
        EshopSaleOrderEntity orderEntity = orderService.getSimpleOrder(parameter);
        orderService.updateOrderFlag(parameter.getProfileId(), parameter.getEshopOrderId(), parameter.getIntSellerFlag());
        String changeLog = String.format("修改旗帜,原旗帜:%s,改为:%s", orderEntity.getSellerFlag().getName(), CommonUtil.getSellerFlageName(parameter.getIntSellerFlag()));

        SysLogUtil.add(SysLogUtil.buildLog(orderEntity, OrderOpreateType.MODIFY_BUYER_MESSAGE, changeLog));
    }

    @PostMapping("/closeOrder")
    public CloseOrderResponse closeOrder(@RequestBody CloseSaleOrderParameter parameter) {
        // 日志上报---关闭订单
        List<LogOrder> logOrderList = parameter.getOrderList().stream().map(item -> {
            return new LogOrder(item.getTradeOrderId(), item.getOtypeId());
        }).collect(Collectors.toList());
        platformBizKeyPointFeedback.feedback(logOrderList, OperationEnum.Modify_Order);

        CloseOrderResponse response = new CloseOrderResponse();
        List<EshopSaleOrderEntity> orderList = parameter.getOrderList();
        if (orderList == null || orderList.size() == 0) {
            return response;
        }
        List<String> collect = orderList.stream().map(EshopSaleOrderEntity::getTradeOrderId).collect(Collectors.toList());
        CloseOrderRequest request = new CloseOrderRequest();
        request.setCloseReason(parameter.getCloseReason());
//		request.setProfileId(parameter.getProfileId());
        request.setShopId(parameter.getOtypeId());
//		request.setOtypeId(parameter.getOtypeId());
        request.setTradeIdList(collect);
        response = platformService.closeOrder(request);
        if (response.getSuccess()) {
            orderService.closeOrder(request);
            notifyService.doNotifyOrderClose(orderList);
            orderList.forEach(item -> SysLogUtil.add(SysLogUtil.buildLog(item, OrderOpreateType.CLOSE_ORDER, "")));
        }
        return response;
    }

    @GetMapping("/closeReason/get/{otypeId}")
    public String getCloseReason(@PathVariable BigInteger otypeId) {
        return platformService.getCloseOrderReason(otypeId);
    }

    @RequestMapping(value = "/downloadById", method = RequestMethod.POST)
    public String downloadOrderById(@RequestBody EshopSaleOrderDownloadTask task) {
        //判断大促模式限制频率
        checkSpeedModeAbs(task);
        if (eshopOrderToolConfig.isDownloadOrderByTool()) {
            GeneralResult<String> result = WebToToolApiFactory.forwardReq(task, WebToToolApiEnum.DOWNLOAD_ORDER_BY_ID, orderService::downloadOrderById);
            return null == result ? null : result.getData();
        }
        return orderService.downloadOrderById(task);
    }

    @RequestMapping(value = "/mulDownload", method = RequestMethod.POST)
    public List<MalDownloadTaskEntity> mulDownloadOrder(@RequestBody EshopSaleOrderDownloadTask task) {
        //判断大促模式限制频率
        checkSpeedModeAbs(task);
        //判断【下载提速】限制频率
        checkDownloadSpeed(task);
        //开启走工具配置
        if (eshopOrderToolConfig.isDownloadOrderByTool()) {
            GeneralResult<String> result = WebToToolApiFactory.forwardReq(task, WebToToolApiEnum.MUL_DOWNLOAD_ORDER, orderService::mulDownloadOrder);
            return null == result ? null : JsonUtils.toList(JsonUtils.toJson(result.getData()), MalDownloadTaskEntity.class);
        }
        return orderService.mulDownloadOrder(task);
    }

    private void checkDownloadSpeed(EshopSaleOrderDownloadTask task) {
        if (null == task.getDownloadType() || !task.getDownloadType().equals(DownloadType.BY_AUTO_DOWNLOAD_NOW)) {
            return;
        }
        String key = String.format("%s_%s_%s_%s", CurrentUser.getProfileId(), task.getOtypeIds(), CurrentUser.getEmployeeId(), "download_order_queue_jump_timelimit");
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        Boolean ifAbsent = template.opsForValue().setIfAbsent(key, key, syncOrderConfig.getDownloadOrderQueueJumpTimelimit(), TimeUnit.MINUTES);
        if (null == ifAbsent || !ifAbsent) {
            throw new RuntimeException(String.format("【立即下载】频率为: %s 分钟", syncOrderConfig.getDownloadOrderQueueJumpTimelimit()));
        }
    }

    /**
     * 下载订单---导入订单编号功能
     * 解析Excel获取订单编号列表
     *
     * @param loadfile 上传excel文件
     * @return
     */
    @RequestMapping(value = "/listOrderIdByExcel", method = RequestMethod.POST)
    public StringBuilder listOrderIdByExcel(MultipartFile loadfile) throws IOException {
        String name = loadfile.getOriginalFilename();
        // 先把MultipartFile转化为File
        File file = inputStreamToFile(loadfile);
        if (!StringUtils.endsWithIgnoreCase(name, ".xls") && !StringUtils.endsWith(name, ".xlsx")) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        // 用来拼接订单编号
        StringBuilder stringBuilder = new StringBuilder();
        List<EshopOrderIdImportVo> list = EasyExcel.read(file).head(EshopOrderIdImportVo.class).sheet().doReadSync();
        if (list.size() == 0) {
            throw new RuntimeException("文件里没有订单编号数据！");
        }
        for (int i = 0; i < list.size(); i++) {
            String col_0 = list.get(i).getCol_0();
            if (null == col_0) {
                throw new RuntimeException("文件异常，导入失败，请检查模板格式");
            }
            if ("".equals(col_0)) {
                throw new RuntimeException("订单号不能为空字符串！错误行数：" + (i + 2));
            }
            if (!checkId(col_0)) {
                throw new RuntimeException("订单编号中只能包含数字、字母、下划线和短横线！错误行数：" + (i + 2));
            }
            if (i == list.size() - 1) {
                // 最后一个不需要拼接逗号
                stringBuilder.append(col_0);
            } else {
                stringBuilder.append(col_0).append(",");
            }
        }
        return stringBuilder;
    }

    /**
     * 判定ID是否符号条件
     *
     * @param id
     * @return
     */
    private boolean checkId(String id) {

        final String format = "[^\\uF900-\\uFA2D\\w-_]";
        Pattern pattern = Pattern.compile(format);
        Matcher matcher = pattern.matcher(id);
        return !matcher.find();
    }

    private void checkSpeedModeAbs(EshopSaleOrderDownloadTask task) {
        //判断大促模式
        if (eshopOrderCommonConfig.isSpeedMode()) {
            String key = CurrentUser.getProfileId() + SpeedModeOperatingFrequency.MUL_DOWNLOAD_ORDER.getKeyStr() + task.getOtypeId();
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            Boolean ifAbsent = template.opsForValue().setIfAbsent(key, key, eshopOrderCommonConfig.getExpireTime(), TimeUnit.MINUTES);
            if (null == ifAbsent || !ifAbsent) {
                throw new RuntimeException(String.format("大促期间手工下载订单频率为: %s 分钟", eshopOrderCommonConfig.getExpireTime()));
            }
        }
    }


    /**
     * MultipartFile转化为File
     *
     * @param loadfile
     * @return
     */
    private static File inputStreamToFile(MultipartFile loadfile) throws IOException {
        File file = null;
        OutputStream os = null;
        try {
            InputStream ins = loadfile.getInputStream();
            file = new File(Md5Utils.md5(loadfile.getOriginalFilename()));
            os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                os.close();
            }
        }
        return file;
    }


    private EshopSaleOrderDownloadTask buildDownloadTask(EshopSaleOrderDownloadTask task, String taskId) {
        EshopSaleOrderDownloadTask itemTask = new EshopSaleOrderDownloadTask();
        itemTask.setTaskId(taskId);
        itemTask.setBeginTime(task.getBeginTime());
        itemTask.setEndTime(task.getEndTime());
        itemTask.setPageNo(task.getPageNo());
        itemTask.setLastDownloadTime(task.getLastDownloadTime());
        itemTask.setOtypeId(task.getOtypeId());
        itemTask.setStatus(task.getStatus());
        itemTask.setEshopInfo(task.getEshopInfo());
        itemTask.setProcessLogger(task.getProcessLogger());
        itemTask.setFilterStr(task.getFilterStr());
        itemTask.setFilterType(task.getFilterType());
        itemTask.setDownloadType(task.getDownloadType());
        itemTask.setTradeFrom(task.getTradeFrom());
        itemTask.setOtypeIds(task.getOtypeIds());
        return itemTask;

    }

    private MalDownloadTaskEntity buildErrorTaskInfo(MalDownloadTaskEntity itemEshop, String errorMssage) {
        itemEshop.setCompleted(true);
        itemEshop.setSuccess(false);
        itemEshop.setProgress(100);
        itemEshop.setMessage(errorMssage);
        itemEshop.setAllMessage(errorMssage);
        return itemEshop;
    }

    @RequestMapping(value = "/initManualRelation", method = RequestMethod.POST)
    public InitRelationResponse initManualRelation(@RequestBody QueryOrderDetailParameter parameter) {
        InitRelationResponse response = new InitRelationResponse();
        IndustryConfig config = GlobalConfig.get(IndustryConfig.class);
        response.setEnabledProps(config.isEnabledProps());
        String hashKeys = orderService.doInitUnRelationOrderDetails(parameter);
        response.setHashKeys(hashKeys);
        response.setInsertUnRelationByNormalOrder(1 == GlobalConfig.get(EshopOrderSysDataConfig.class).getInsertUnRelationByNormalOrder());
        return response;
    }

    @RequestMapping(value = "/unRelationDetailsGet", method = RequestMethod.POST)
    public PageResponse<OrderRelationOperation> getUnRelationDetails(@RequestBody PageRequest<QueryOrderDetailParameter> parameter) {
        List<Sort> sorts = parameter.getSorts();
        if (CollectionUtils.isNotEmpty(sorts)) {
            for (Sort sort : sorts) {
                if (sort.getDataField().equals("eshopName")) {
                    sort.setDataField("fullname");
                }
                if (sort.getDataField().equals("platformPtypeName")) {
                    sort.setDataField("platform_fullname");
                }
                if (sort.getDataField().equals("platformPropertiesName")) {
                    sort.setDataField("platform_properties_name");
                }
                if (sort.getDataField().equals("platformPtypeXcode")) {
                    sort.setDataField("platform_xcode");
                }
            }
        }
        PageDevice.initPage(parameter);
        List<OrderRelationOperation> operations = orderService.queryUnRelationOrderDetails(parameter.getQueryParams());
        return PageDevice.readPage(operations);
    }

    @RequestMapping(value = "/saveRelation", method = RequestMethod.POST)
    public OrderRelationResponse saveOrderRelation(@RequestBody List<OrderRelationOperation> details) {
        return orderService.notifyOrderToRelation(details, new OrderRelationTask());
    }

    /**
     * 临时对应代码
     *
     * @param details
     * @return
     */
    @RequestMapping(value = "/saveRelationAsync", method = RequestMethod.POST)
    public String saveOrderRelationAsync(@RequestBody List<OrderRelationOperation> details) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        processLogger.appendMsg(String.format("开始进行对应，一共%d个线上网店商品需要对应", details.size()));
        details.forEach(x -> {
            x.setOprate(ProductOperateLogType.TempMapping);
        });
        Pair<List<OrderRelationOperation>, ProcessLoggerImpl> pair = new Pair<>(details, processLogger);
        //判断大促模式
        if (eshopOrderCommonConfig.isSpeedMode() || eshopOrderToolConfig.isRefreshPtypeByTool()) {
            GeneralResult<String> result = WebToToolApiFactory.forwardReq(pair, WebToToolApiEnum.SAVE_ORDER_RELATION_ASYNC, this::doNotifyOrderToRelation);
            return null == result || StringUtils.isEmpty(result.getData()) ? taskId : result.getData();
        }
        doNotifyOrderToRelation(pair);
        return taskId;
    }

    private String doNotifyOrderToRelation(Pair<List<OrderRelationOperation>, ProcessLoggerImpl> pair) {
        List<OrderRelationOperation> details = pair.getFirst();
        ProcessLoggerImpl processLogger = pair.getSecond();
        String threadName = EshopOrderConst.DOWNLOAD_ORDER_THREAD_NAME;
        ThreadPool threadPool = ThreadPoolFactory.build(threadName);
        threadPool.executeAsync(invoker -> {
            OrderRelationTask task = new OrderRelationTask(processLogger, processLogger.getLoggerKey());
            OrderRelationResponse response = orderService.notifyOrderToRelation(invoker, task);
            processLogger.appendMsg("临时对应结束");
            processLogger.doFinish();
        }, details);
        return null == processLogger ? "" : processLogger.getLoggerKey();
    }

    @RequestMapping(value = "/GetNewOrderImportTemplate", method = RequestMethod.GET)
    public OrderImportTemplate GetNewOrderImportTemplate() {
        OrderImportTemplate templateUI;
        templateUI = EShopSaleOrderImportConfigManager.GetNewOrderImportTemplate(CurrentUser.getProfileId());
        return templateUI;
    }

    /**
     * 导入订单
     *
     * @param file
     * @param importTask
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/DoOrderImport", method = RequestMethod.POST)
    public String importByExcel(MultipartFile file, EshopSaleOrderImportRequest importTask) throws IOException {
        String name = file.getOriginalFilename();
        if (!StringUtils.endsWithIgnoreCase(name, ".xls") && !StringUtils.endsWith(name, ".xlsx")) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        String taskId = UId.newId().toString();
        importTask.setTaskId(taskId);
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        importTask.setProcessLogger(processLogger);
        if (importTask.getTempplateId() == null
                || StringUtils.isEmpty(importTask.getTempplateId().toString())
                || importTask.getTempplateId().compareTo(BigInteger.ZERO) > 0) {
            importTask.setOrderTemplates(EShopSaleOrderImportConfigManager.GetOrderTemplates(importTask));
            importTask.setOrderDetailTemplates(EShopSaleOrderImportConfigManager.GetOrderDetailTemplates(true, importTask));
        }
        orderImportService.asyncImportOrder(file.getInputStream(), importTask);
        return importTask.getTaskId();
    }

    @RequestMapping(value = "/exportExcel", method = RequestMethod.GET)
    public void exportExcel(HttpServletResponse response, @RequestParam(required = true) String processkey, @RequestParam(required = false) String filename) throws IOException, ClassNotFoundException {
        org.springframework.data.util.Pair<Class, List<Object>> res = ProcessUtil.getListResult(processkey);
        if (res == null) {
            return;
        }
        if (com.qiniu.util.StringUtils.isNullOrEmpty(filename)) {
            filename = "错误反馈" + DateUtils.formatDate(new Date(), "yyyyMMdd");
        }
        EshopSaleOrderImportService.exportData(response, filename, res.getFirst(), res.getSecond());
    }

    @RequestMapping(value = "/checkExcelImportFail", method = RequestMethod.GET)
    public boolean checkExcelImportFail(@RequestParam("processkey") String processkey) {
        try {
            org.springframework.data.util.Pair<Class, List<Object>> res = ProcessUtil.getListResult(processkey);
            return res != null && !CollectionUtils.isEmpty(res.getSecond());
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    @PostMapping(value = "/queryListOrdersByVchcodes")
    public List<EshopSaleOrderEntity> queryListOrdersByVchcodes(@RequestBody GetSaleOrderInfoRequest request) {
        if (request.getProfileId() == null || request.getProfileId().longValue() == 0) {
            throw new RuntimeException("账套ID不能为空");
        }
        if (request.getCold() == null) {
            request.setCold(false);
        }

        return orderService.queryListOrdersByVchcodes(request);
    }

    @PostMapping(value = "/queryListOrdersByVchcodesForJXC")
    public List<EshopSaleOrderEntity> queryListOrdersByVchcodesForJXC(@RequestBody GetSaleOrderInfoRequest request) {
        if (request.getProfileId() == null || request.getProfileId().longValue() == 0) {
            throw new RuntimeException("账套ID不能为空");
        }
        if (request.getCold() == null) {
            request.setCold(false);
        }
        List<BigInteger> vchcodeList = request.getEshopOrderIdList();
        if (vchcodeList.size() > 0) {
            BigInteger vchcode = vchcodeList.get(0);
        } else {
            return new ArrayList<EshopSaleOrderEntity>();
        }
        Integer processState = orderService.getOrderProcessState(request);
        List<Integer> postStates = orderService.getPostState(request);
        List<Integer> collect = postStates.stream().filter(item -> {
            return item < 800;
        }).collect(Collectors.toList());
        // 订单未提交 直接取原单数据
        if (processState == null || processState == 0 || (processState == 1 && (collect.size() > 0 || postStates.size() == 0))) {
            return orderService.queryListOrdersByVchcodes(request);
        }
        List<EshopSaleOrderEntity> eshopSaleOrderEntities = orderService.queryListOrdersByVchcodesForJXC(request);
        if (eshopSaleOrderEntities.size() == 1 && eshopSaleOrderEntities.get(0) == null) {
            return new ArrayList<EshopSaleOrderEntity>();
        }
        return eshopSaleOrderEntities;
    }

    @NotNull
    private static String getRequestRedisKey(MainPageSaleOrderQueryParameter request) {
        String requestMd5 = Md5Utils.md5(JsonUtils.toJson(request));
        String key = MessageFormat.format("{0}:{1}:{2}:{3}", NAV_REDIS_TITLE, CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), requestMd5);
        return key;
    }

    private EshopSaleOrderResult deliverCountResultRedisSet(MainPageSaleOrderQueryParameter request, ValueOperations<String, String> redis, String key) {
        EshopSaleOrderResult result = new EshopSaleOrderResult();
        switch (request.getMainPageSaleOrderDataQueryType()) {
            case ORDER_DATA:
                orderService.findEshopOrderResultForMainPageOrderData(request, result);
                break;
            case AGENCY_MATTERS:
                orderService.findEshopOrderResultForMainPageAgencyMatters(request, result);
                break;
            case ABNORMAL_ATTENTION:
                orderService.findEshopSaleOrderResultForMainPageAbnormalAttention(request, result);
                break;
            case ALL:
                orderService.findEshopOrderResultForMainPageOrderData(request, result);
                orderService.findEshopOrderResultForMainPageAgencyMatters(request, result);
                orderService.findEshopSaleOrderResultForMainPageAbnormalAttention(request, result);
                break;
            default:
                return result;
        }
        orderService.setResultUrl(result, request);
        setNavRedis(redis, key, result);
        return result;
    }


    private EshopSaleOrderResult doQuerySaleOrderResult(MainPageSaleOrderQueryParameter request) {
        String key = getRequestRedisKey(request);
        StringRedisTemplate biz = factory.getTemplate(CacheType.BIZ.getKey());
        ValueOperations<String, String> redis = biz.opsForValue();
        if (null != request.getHandleRefresh() && request.getHandleRefresh()) {
            return deliverCountResultRedisSet(request, redis, key);
        }
        String redisValue = redis.get(key);
        if (StringUtils.isEmpty(redisValue)) {
            return deliverCountResultRedisSet(request, redis, key);
        }
        try {
            return JsonUtils.toObject(redisValue, EshopSaleOrderResult.class);
        } catch (Exception e) {
            logger.error("profileId:{} employId:{} NAV JsonUtils.toObject error:{}", CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), e.getMessage(), e);
        }
        return deliverCountResultRedisSet(request, redis, key);
    }

    private void setNavRedis(ValueOperations<String, String> redis, String key, EshopSaleOrderResult result) {
        if (result == null) {
            return;
        }
        String navRedisSecondsStr = orderService.getSysDataInfo(NAV_REDIS_SEC0NDS_KEY);
        long navRedisSeconds = 300;
        if (StringUtils.isNotEmpty(navRedisSecondsStr)) {
            try {
                navRedisSeconds = Long.parseLong(navRedisSecondsStr);
            } catch (Exception exx) {
                logger.error("profileId:{} employId:{} convert nav redis seconds error:{}", CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), exx.getMessage(), exx);
            }
        }
        try {
            redis.set(key, JsonUtils.toJson(result), navRedisSeconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("profileId:{} employId:{} NAV redis set error:{}", CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), e.getMessage(), e);
        }
    }

    //查询首页数据
    @PostMapping(value = "/queryOnSaleOrders")
    public EshopSaleOrderResult queryOnSaleOrders(@RequestBody MainPageSaleOrderQueryParameter request) {
        EshopSaleOrderResult result = new EshopSaleOrderResult();
        if (null == request || null == request.getMainPageSaleOrderDataQueryType()) {
            return result;
        }
        return doQuerySaleOrderResult(request);
    }


    /**
     * 从手机端查询网店订单首页数据
     *
     * @param handleRefresh Boolean 是否刷新
     * @return EshopSaleOrderResult
     */
    @GetMapping(value = "/queryOnSaleOrdersFromMobile/{handleRefresh}")
    public EshopSaleOrderResult queryOnSaleOrdersFromMobile(@PathVariable Boolean handleRefresh) {
        MainPageSaleOrderQueryParameter request = new MainPageSaleOrderQueryParameter();
        request.setHandleRefresh(handleRefresh);
        request.setMainPageSaleOrderDataQueryType(MainPageSaleOrderDataQueryType.ALL);
        return doQuerySaleOrderResult(request);
    }

    @PostMapping(value = "/getPlatformDecryptParams")
    public PlatformDecryptRequest getPlatformDecryptParams(@RequestBody GetPlatformDecryptParamsRequest request) {
        if (request.getProfileId() == null || request.getProfileId().longValue() == 0) {
            request.setProfileId(CurrentUser.getProfileId());
        }
        return decryptService.GetPlatformDecryptParam(request.getTradeIds(), request.getOtypeId(), request.getProfileId(), request.getVchcode());
    }

    private void getTask(Map.Entry<BigInteger, List<BatchOrderParams>> entry, EshopSaleOrderDownloadTask task) {
        task.setOtypeId(entry.getKey());
        List<BatchOrderParams> value = entry.getValue();
        task.setDownloadType(value.get(0).getDownloadType());
        List<String> tradeOrderIds = value.stream().map(BatchOrderParams::getTradeOrderId).collect(Collectors.toList());
        task.setFilterStr(StringUtils.join(tradeOrderIds, ","));
    }

    @PostMapping(value = "/initConfig")
    public SysGlobalConfig initConfig(@RequestBody QueryOrderParameter parameter) {
        return GlobalConfig.get(SysGlobalConfig.class);
    }

    @PostMapping(value = "/industryConfig")
    public IndustryConfig industryConfig(@RequestBody QueryOrderParameter parameter) {
        return GlobalConfig.get(IndustryConfig.class);
    }

    @PostMapping(value = "/getSelectorPageInit")
    public OrderSelectorPageInit getSelectorPageInit() {
        return null;
    }

    @GetMapping(value = "/getOrderImportByTool")
    public boolean GetOrderImportType() {
        ExportConfig config = GetBeanUtil.getBean(ExportConfig.class);
        return config.isImportNeedByTool();
    }

    @PostMapping("/getYchParams")
    public Map<String, String> getYchParams(@RequestBody GetSecTokenParameter parameter) {
        BigInteger profileId = CurrentUser.getProfileId();
        EshopInfo eshopInfo = eshopService.getEshopInfoById(profileId, parameter.getEshopId());
        if (eshopInfo == null) {
            return null;
        }
        if (StringUtils.isEmpty(eshopInfo.getEshopAccount()) || StringUtils.isEmpty(eshopInfo.getToken())) {
            throw new RuntimeException("店铺未授权,请授权以后再进行操作");
        }

        MoatConfig currentMoatConfig = reloadMoatConfig();
        String ip = CommonUtil.getIpAddress();
        Map<String, String> secTokenParams = new HashMap<>();
        secTokenParams.put("appKey", currentMoatConfig.getMoatAppKey());
        secTokenParams.put("userId", CurrentUser.getEmployeeId().toString());
        secTokenParams.put("taobaoUserId", eshopInfo.getEshopAccount());
        secTokenParams.put("topAppKey", currentMoatConfig.getTaobaoAppKey());
        secTokenParams.put("appName", "管家婆ERP");
        secTokenParams.put("userIp", ip);
        secTokenParams.put("ati", parameter.getAti());
        secTokenParams.put("sessionId", eshopInfo.getToken());
        secTokenParams.put("time", DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss"));
        if (StringUtils.isNotEmpty(parameter.getSecToken())) {
            secTokenParams.put("secToken", parameter.getSecToken());
        }
        if (StringUtils.isNotEmpty(parameter.getTopToken())) {
            secTokenParams.put("topToken", parameter.getTopToken());
        }
        if (StringUtils.isNotEmpty(parameter.getMethod())) {
            secTokenParams.put("method", parameter.getMethod());
        }

        String sign = EshopUtils.calculateTaobaoSign(secTokenParams, currentMoatConfig.getMoatAppSercret());
        secTokenParams.put("sign", sign);
        return secTokenParams;
    }

    private MoatConfig reloadMoatConfig() {
        String appName = GlobalConfig.get(TbParameterConstant.TAOBAO_APPLICATION_NAME);
        try {
            if (ngp.utils.StringUtils.isNotEmpty(appName)
                    && this.moatConfig.getExtraMoatConfig() != null
                    && !this.moatConfig.getExtraMoatConfig().isEmpty()
                    && this.moatConfig.getExtraMoatConfig().containsKey(appName)) {
                return moatConfig.getExtraMoatConfig().get(appName);
            }
        } catch (Exception ex) {
            logger.error("读取MoatConfig配置失败,{}", ex.getMessage(), ex);
        }
        return moatConfig;
    }

    @PostMapping("/getOnceToken")
    public EshopAuthInfo getOnceToken(@RequestBody GetSecTokenParameter parameter) {
        if (StringUtils.isEmpty(parameter.getSecToken())) {
            throw new RuntimeException("请先获取secToken以后再尝试获取一次性token");
        }
        GetTokenRequest request = new GetTokenRequest();
        request.setShopId(parameter.getEshopId());
        request.setSecToken(parameter.getSecToken());
        return bifrostEshopAuthService.getToken(request);
    }

    @GetMapping("/getDownLoadOrderCache/{key}")
    public String getDownLoadOrderCache(@PathVariable String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        String keyStr = key + CurrentUser.getProfileId() + CurrentUser.getEmployeeId();
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        if (Boolean.FALSE.equals(template.hasKey(keyStr))) {
            return null;
        }
        return template.opsForValue().get(keyStr);
    }

    @GetMapping("/getDownLoadOrderParamCache/{key}")
    public EshopSaleOrderDownloadTask getDownLoadOrderParamCache(@PathVariable String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        String keyStr = key + CurrentUser.getProfileId() + CurrentUser.getEmployeeId();
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        if (Boolean.FALSE.equals(template.hasKey(keyStr))) {
            return null;
        }
        String json = template.opsForValue().get(keyStr);
        return JsonUtils.toObject(json, EshopSaleOrderDownloadTask.class);
    }

    @GetMapping("/getInitTradeId/{otypeId}")
    public String getInitTradeId(@PathVariable BigInteger otypeId) {
        if (null == otypeId) {
            return "";
        }
        Otype otype = eshopService.getOtypeById(otypeId);
        if (otype == null || otype.isDeleted()) {
            throw new RuntimeException("网店已经被删除");
        }
        return manualService.getInitTradeId(otypeId);
    }

    //获取下载记录
    @PostMapping("/getAutoDownloadRecord")
    public PageResponse<EshopSaleOrderSyncTaskLog> getAutoDownloadRecord(@RequestBody PageRequest<EshopSaleOrdeSyncTaskQueryParams> pageParameter) {
        if (null == pageParameter) {
            return null;
        }
        pageParameter.getQueryParams().setProfileId(CurrentUser.getProfileId());
        return SysLogUtil.query(pageParameter);
    }

    @PostMapping("/queryOrderDownloadRecord")
    public List<OrderDownRecordDto> queryOrderDownloadRecord(@RequestBody QueryOrderRecordParam param) {
        return recordService.queryOrderDownloadRecord(param);
    }

    @PostMapping("/sendDownLoadTask")
    public void sendDownLoadTask(@RequestBody OrderDownLoadTask task) {
        recordService.sendDownLoadTask(task);
    }

    @PostMapping("/deleteOrder")
    public void deleteOrder(@RequestBody QueryOrderParameter param) {
        if (null == param || CollectionUtils.isEmpty(param.getEshopOrderIds())) {
            return;
        }
        orderService.deleteOrder(param.getEshopOrderIds(),OrderOpreateType.DELETE_ORDER);
    }

    @PostMapping("/deleteRecovery")
    public void deleteRecovery(@RequestBody QueryOrderParameter param) {
        if (null == param || CollectionUtils.isEmpty(param.getEshopOrderIds())) {
            return;
        }
        orderService.deleteRecovery(param.getEshopOrderIds(),OrderOpreateType.DELETE_ORDER);
    }

    @PostMapping("/checkDeleteOrder")
    public CheckOrderDelete checkDeleteOrder(@RequestBody List<EshopSaleOrderEntity> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return new CheckOrderDelete();
        }
        return orderService.checkDeleteOrder(orders);
    }


    @GetMapping("/getPageInitInfo")
    public HashMap getPageInitInfo() {
        HashMap result = new HashMap<>();
        result.put("ptypeLabels", baseInfoService.getPtypeLabels(CurrentUser.getProfileId()));
        result.put("hasJDAuthShop", eshopService.checkHasJDAuthShop());
        return result;
    }

    @GetMapping("/hasJDAuthShop")
    public boolean hasJDAuthShop() {
        return eshopService.checkHasJDAuthShop();
    }

    @ApiOperation(value = "获取配送周期相关信息")
    @PostMapping("/getCycleOrder")
    public List<EshopCycleOrderEntity> getCycleOrder(@RequestBody QueryOrderParameter param) {
        if (null == param || CollectionUtils.isEmpty(param.getOtypeIds()) || StringUtils.isEmpty(param.getTradeOrderId())) {
            return null;
        }
        return orderService.getCycleOrder(param);
    }

    @GetMapping("/checkCycleOrderSubmit")
    public boolean checkCycleOrderSubmit() {
        return orderService.checkCycleOrderSubmit();
    }

    @PostMapping("/getOrderInvoiceInfo")
    public EshopSaleOrderInvoiceInfo getOrderInvoiceInfo(@RequestBody EshopSaleOrderInvoiceInfo invoice) {
        if (null == invoice) {
            return invoice;
        }
        return orderService.getOrderDecryptInvoice(invoice);
    }

    @GetMapping("/checkUnRelationProduct")
    public boolean checkUnRelationProduct() {
        return orderService.checkUnRelationProduct();
    }

    @ApiOperation(value = "原单获取关联单据")
    @PostMapping("getSaleOrderBillByOrderId")
    public List<SaleOrderSelectDeliverBillResponse> getSaleOrderBillByOrderId(@RequestBody SaleOrderSelectDeliverBillRequest request) {
        return orderService.getSaleOrderBillByOrderId(request);
    }

    @ApiOperation(value = "查询原单自动提交设置")
    @PostMapping("getEshopOrderAutoSubmitConfig")
    public EshopOrderAutoSubmitConfig getEshopOrderAutoSubmitConfig() {
        return orderService.getEshopOrderAutoSubmitConfig();
    }

    @ApiOperation(value = "保存原单自动提交设置")
    @PostMapping("saveEshopOrderAutoSubmitConfig")
    public void saveEshopOrderAutoSubmitConfig(@RequestBody EshopOrderAutoSubmitConfig config) {
        orderService.saveEshopOrderAutoSubmitConfig(config);
    }
}
