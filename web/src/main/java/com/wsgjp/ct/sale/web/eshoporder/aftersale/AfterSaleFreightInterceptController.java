package com.wsgjp.ct.sale.web.eshoporder.aftersale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.freightintercept.AfterSaleFreightInterceptEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.aftersale.QueryFreightInterceptParameter;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.*;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "售后管理")
@RequestMapping("${app.id}/eshoporder/aftersale")
@RestController
public class AfterSaleFreightInterceptController {
    private final AfterSaleFreightInterceptService interceptService;


    private static final Logger logger = LoggerFactory.getLogger(AfterSaleFreightInterceptController.class);

    public AfterSaleFreightInterceptController(AfterSaleFreightInterceptService interceptService) {

        this.interceptService = interceptService;
    }
    /**
     * 查询售后单列表
     */
    @PostMapping(value = "/queryAftersaleFreightInterceptCount")
    @PermissionCheck(key = PermissionSysConst.ESHOP_REFUND_VIEW)
    public PageResponse<AfterSaleFreightInterceptEntity> queryAftersaleFreightInterceptCount(@RequestBody PageRequest<QueryFreightInterceptParameter> parameter) {
        return interceptService.queryAfterSaleFreightIntercept(parameter);
    }

    /**
     * 查询售后单列表
     */
    @PostMapping(value = "/queryAftersaleFreightInterceptList")
    @PermissionCheck(key = PermissionSysConst.ESHOP_REFUND_VIEW)
    public PageResponse<AfterSaleFreightInterceptEntity> queryAftersaleFreightInterceptList(@RequestBody PageRequest<QueryFreightInterceptParameter> parameter) {
        return interceptService.queryAfterSaleFreightIntercept(parameter);
    }


}
