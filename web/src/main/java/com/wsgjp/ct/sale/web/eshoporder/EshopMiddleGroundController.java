package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.middleground.EshopPtypeDetailEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.middleground.EshopPublishPtypeEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryPtypeDetailParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.exception.EhsopProductDetailException;
import com.wsgjp.ct.sale.biz.eshoporder.service.middleground.EshopMiddleGroundService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePo;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePublishDto;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.UnitSkuQueryPageDto;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.MarkData;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import com.wsgjp.ct.sale.common.entity.middle.dto.DoBatchSynDTO;
import com.wsgjp.ct.sale.common.entity.middle.dto.ListBatchQueryDto;
import com.wsgjp.ct.sale.common.entity.middle.pojo.KtypeInfo;
import com.wsgjp.ct.sale.common.entity.middle.vo.BatchInfoVo;
import com.wsgjp.ct.sale.platform.dto.product.SkuUnitInfo;
import com.wsgjp.ct.support.config.QiniuConfig;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.utils.QiniuUtils;
import io.swagger.annotations.Api;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-07-26
 * @Description
 */
@Api(tags = "供应链中台相关")
@RequestMapping("${app.id}/eshoporder/middleground")
@RestController
public class EshopMiddleGroundController {

    private final Logger logger = LoggerFactory.getLogger(EshopMiddleGroundController.class);
    private final EshopMiddleGroundService middleGroundService;

    public EshopMiddleGroundController(EshopMiddleGroundService middleGroundService) {
        this.middleGroundService = middleGroundService;
    }

    /**
     * 获取商品详情列表
     */
    @PostMapping("/getPtypeDetails")
    @PermissionCheck(key = PermissionSysConst.ESHOP_PRODUCT_DETAIL_VIEW)
    public PageResponse<EshopPtypeDetailEntity> getProductDetails(@RequestBody PageRequest<QueryPtypeDetailParameter> pageParameter) {
        PageResponse<EshopPtypeDetailEntity> resp = new PageResponse<>();
        try {
            resp = middleGroundService.getProductDetails(pageParameter);
            List<EshopPtypeDetailEntity> list = resp.getList();
            for (EshopPtypeDetailEntity eshopPtypeDetailEntity : list) {
                eshopPtypeDetailEntity.setPlatformPicUrl(QiniuUtils.getFullUrl(eshopPtypeDetailEntity.getPlatformPicUrl()));
            }
            if (CollectionUtils.isEmpty(resp.getList()) && 0 == resp.getTotal()) {
                logger.warn("获取商品详情列表为空，请检查！");
                return resp;
            }
            return resp;
        } catch (EhsopProductDetailException e) {
            logger.warn(String.format("失败码：%s，失败信息：%s，失败原因：%s", e.getErrCode(), e.getErrMsg(), e.getMessage()));
            return resp;
        }
    }

    /**
     * 保存商品详情
     */
    @PostMapping("/saveProductDetail")
    public BaseResponse saveWholeDetail(@RequestBody EshopPtypeDetailEntity detail) {
        BaseResponse resp = new BaseResponse();
        try {
            middleGroundService.saveProductDetail(detail);
            resp.setSuccess(true);
        } catch (EhsopProductDetailException e) {
            resp.setSuccess(false);
            resp.setMessage(e.getErrMsg());
            logger.warn(String.format("失败码：%s，失败信息：%s，失败原因：%s", e.getErrCode(), e.getErrMsg(), e.getMessage()));
        }
        return resp;
    }

    /**
     * 删除商品详情
     */
    @PostMapping("/removeProductDetail")
    public BaseResponse removeProductDetail(@RequestBody EshopPtypeDetailEntity detail) {
        BaseResponse resp = new BaseResponse();
        try {
            middleGroundService.delProductDetail(detail);
            resp.setSuccess(true);
        } catch (EhsopProductDetailException e) {
            resp.setSuccess(false);
            resp.setMessage(e.getErrMsg());
            logger.warn(String.format("失败码：%s，失败信息：%s，失败原因：%s", e.getErrCode(), e.getErrMsg(), e.getMessage()));
        }
        return resp;
    }

    @PostMapping(value = "/uploadFile")
    public ArrayList<String> qiniuUpload(@RequestParam("Filedata") MultipartFile[] attachments) throws Exception {
        ArrayList<String> urls = new ArrayList<>();
        for (MultipartFile attachment : attachments) {
            String attachmentName = attachment.getOriginalFilename();
            String substring = "";
            if (attachmentName != null) {
                substring = attachmentName.substring(attachmentName.lastIndexOf(".") + 1);
            }
            if (!"jpg".equalsIgnoreCase(substring) && !"png".equalsIgnoreCase(substring) && !"jpeg".equalsIgnoreCase(substring) && !"bmp".equalsIgnoreCase(substring)) {
                throw new RuntimeException("仅支持上传格式为bmp，jpg，png，jpeg的图片");
            }
            String key = "";
            byte[] data;
            try {
                data = attachment.getBytes();
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("文件解析失败：" + e.getMessage());
            }
            String filename = attachment.getOriginalFilename();
            String prefix = filename.substring(filename.lastIndexOf(".") + 1);
            key = MessageFormat.format("{0}/{1}.{2}", CurrentUser.getProfileId().toString(), UId.newId().toString(), prefix);
            QiniuConfig qiniuConfig = GetBeanUtil.getBean(QiniuConfig.class);
            Auth auth = Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey());
            StringMap putPolicy = new StringMap();
            putPolicy.put("returnBody", "{\"key\":\"$(key)\",\"hash\":\"$(etag)\",\"bucket\":\"$(bucket)\",\"fsize\":$(fsize)}");
            //10M限制
            putPolicy.put("fsizeLimit", qiniuConfig.getFsizeLimit());
            long expireSeconds = 3600;
            String bucket = qiniuConfig.getBucket();
            String upToken = auth.uploadToken(bucket, null, expireSeconds, putPolicy);
            //上传对象
            Configuration cfg = new Configuration(qiniuConfig.getRegion().getZone());
            cfg.useHttpsDomains = false;
            UploadManager uploadManager = new UploadManager(cfg);
            uploadManager.put(data, key, upToken);
            String fullUrl = QiniuUtils.getFullUrl(key);
            urls.add(fullUrl);
        }

        return urls;
    }

    @PostMapping("/getPtypeForPublish")
    public PageResponse<EshopPublishPtypeEntity> getPtypeForPublish(int pageIndex, int pageSize,
                                                                    @RequestBody List<EshopPublishPtypeEntity> data) {
        PageResponse<EshopPublishPtypeEntity> resp = new PageResponse<>();
        resp.setPageIndex(pageIndex);
        resp.setPageSize(pageSize);
        List<EshopPublishPtypeEntity> ptypePublishList;
        try {
            ptypePublishList = middleGroundService.getPtypeForPublish(data);
            if (CollectionUtils.isEmpty(ptypePublishList)) {
                logger.warn("获取商品详情列表为空，请检查！");
                return resp;
            }
            resp.setList(ptypePublishList);
            resp.setTotal(ptypePublishList.size());
            return resp;
        } catch (EhsopProductDetailException e) {
            logger.warn(String.format("报错码：%s，报错信息：%s，报错原因：%s", e.getErrCode(), e.getErrMsg(), e.getMessage()));
            return resp;
        }

    }

    /**
     * 商品发布
     */
    @PostMapping("/publishProductToPlatform")
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse publishProductToPlatform(@RequestBody PtypePublishDto dto) {
        StringBuilder stringBuilder = middleGroundService.doProductPublish(dto);
        return new BaseResponse(true, stringBuilder.toString());
    }

    /**
     * 商品删除
     */
    @PostMapping("/deleteProduct")
    public BaseResponse deleteProduct(@RequestBody PtypePublishDto dto) {
        return middleGroundService.deleteProduct(dto);
    }


    /**
     * 获取商品列表
     */
    @PostMapping("ptypeList")
    @PageDataSource
    public PageResponse<PtypePo> ptypeList(@RequestBody PageRequest<UnitSkuQueryPageDto> pageRequest) {
        UnitSkuQueryPageDto queryParams = pageRequest.getQueryParams();
        queryParams.setProfileId(CurrentUser.getProfileId());
        PageDevice.initPage(pageRequest);
        return PageDevice.readPage(middleGroundService.ptypeList(queryParams));
    }

    /**
     * 获取发布明细列表
     */
    @PostMapping("listPublishDetail")
    @PageDataSource
    public PageResponse<PlEshopProductPublishDetail> listPublishDetail(@RequestBody PageRequest<PlEshopProductPublishDetail> pageRequest) {
        PlEshopProductPublishDetail queryParams = pageRequest.getQueryParams();
        queryParams.setProfileId(CurrentUser.getProfileId());
        PageDevice.initPage(pageRequest);
        return PageDevice.readPage(middleGroundService.listPublishDetail(queryParams));
    }


    @PostMapping("getSkuAndUnit")
    public List<SkuUnitInfo> getSkuAndUnit(@RequestBody UnitSkuQueryPageDto dto) {
        return middleGroundService.getSkuAndUnit(dto);
    }

    /**
     * 查询批次信息列表
     */
    @PostMapping("listBatch")
    @PageDataSource
    public PageResponse<BatchInfoVo> listBatch(@RequestBody PageRequest<ListBatchQueryDto> pageRequest) {
        ListBatchQueryDto dto = pageRequest.getQueryParams();
        return PageDevice.readPage(middleGroundService.listBatch(dto));
    }

    /**
     * 获取仓库列表
     */
    @PostMapping("listKtype")
    public List<KtypeInfo> listKtype() {
        return middleGroundService.listKtype();
    }

    /**
     * 同步批次号
     */
    @PostMapping("doBatchSyn")
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse doBatchSyn(@RequestBody DoBatchSynDTO dto) {
        return middleGroundService.doBatchSyn(dto);
    }

    @PostMapping("listBatchLog")
    @PageDataSource
    public PageResponse<MarkData> listBatchLog(@RequestBody PageRequest<PlEshopProductPublishDetail> pageRequest) {
        PlEshopProductPublishDetail params = pageRequest.getQueryParams();
        return PageDevice.readPage(middleGroundService.listBatchLog(params));
    }

    @PostMapping("ruleConfig")
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse ruleConfig(@RequestBody DoBatchSynDTO dto) {
        return middleGroundService.ruleConfig(dto);
    }

    @PostMapping("getRuleConfig")
    @Transactional(rollbackFor = Exception.class)
    public SysData getRuleConfig(@RequestBody DoBatchSynDTO dto) {
        return middleGroundService.getSysDataPo(dto.getEshopId().toString());
    }

}
