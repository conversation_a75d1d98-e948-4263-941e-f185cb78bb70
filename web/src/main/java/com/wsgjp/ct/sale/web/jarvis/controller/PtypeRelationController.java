package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.EshopProductRelationEntity;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.OrderRelationResponse;
import com.wsgjp.ct.sale.biz.jarvis.service.ptyperelation.PtypeRelationService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * created  by qhy on 2022-03-03
 */
@RestController
@Api("商品对应")
@RequestMapping("/${app.id}/jarvis/ptypeRelationController")
public class PtypeRelationController {
    private PtypeRelationService ptypeRelationService;

    public PtypeRelationController(PtypeRelationService ptypeRelationService) {
        this.ptypeRelationService = ptypeRelationService;
    }

    @ApiOperation(value = "发货单商品对应", notes = "发货单商品对应")
    @PostMapping("/deliverBillRetation")
    public OrderRelationResponse deliverBillRetation(@RequestBody List<EshopProductRelationEntity> request) {
        return ptypeRelationService.billRetation(CurrentUser.getProfileId(),request);
    }
}
