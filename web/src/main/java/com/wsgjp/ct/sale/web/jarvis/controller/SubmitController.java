package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.submit.SubmitFromJxcDTO;
import com.wsgjp.ct.sale.biz.jarvis.service.SubmitBillService;
import com.wsgjp.ct.sale.biz.jarvis.utils.DataUtils;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.sale.web.jarvis.response.CommonResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/${app.id}/jarvis/submit")
@Api(description = "提交接口")
public class SubmitController {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final SubmitBillService submitBillService;

    public SubmitController(SubmitBillService submitBillService) {
        this.submitBillService = submitBillService;
    }

    @ApiOperation(value = "进销存订单提交", notes = "")
    @PostMapping("/doSubmit")
    public BaseResponse doSubmit(@RequestBody SubmitFromJxcDTO submitDto) {
        try {
           return CommonResponse.success(submitBillService.submitFromJxc(submitDto));
        } catch (Exception e) {
            logger.error("提交失败 {} {}", CurrentUser.getProfileId(), JsonUtils.toJson(submitDto), e);
            return CommonResponse.fail(DataUtils.getValue(e.getMessage(), "提交失败，未知原因"));
        }
    }
}
