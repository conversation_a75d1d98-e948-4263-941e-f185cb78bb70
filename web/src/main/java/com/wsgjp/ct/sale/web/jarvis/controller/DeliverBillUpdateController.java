package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.api.wms.WmsApi;
import com.wsgjp.ct.sale.biz.jarvis.boot.JarvisConfig;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.*;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.DeliverBillBatchUpdateByMarkRquest;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.DeliverBillUpdatePlanSendTimeRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.mark.DeliverBillDetailMarkUpdate;
import com.wsgjp.ct.sale.biz.jarvis.newpackage.face.WarehouseBillIds;
import com.wsgjp.ct.sale.biz.jarvis.open.JarvisOpenApi;
import com.wsgjp.ct.sale.biz.jarvis.open.dto.DeliverBillUpdateResponse;
import com.wsgjp.ct.sale.biz.jarvis.open.dto.RefundUpdateResponse;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.biz.jarvis.service.deliverbillupdate.DeliverBillUpdateService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillExStatusAsyncHandleHelper;
import com.wsgjp.ct.sale.biz.jarvis.utils.RedisProcessMsgUtils;
import com.wsgjp.ct.sale.biz.jarvis.utils.Slf4jPrintStackUtil;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.NeedProcessMsgBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessMessageMemory;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessResponse;
import com.wsgjp.ct.sale.common.enums.UpdateTypeEnum;
import com.wsgjp.ct.sale.common.notify.request.DeliverBillPlatformMarkUpdateDao;
import com.wsgjp.ct.sale.common.notify.request.DeliverBillUpdateRequest;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.sale.web.jarvis.response.CommonResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-06-04
 **/

@RestController
@RequestMapping("/${app.id}/jarvis/deliverBillUpdate")
public class DeliverBillUpdateController {

    private DeliverBillUpdateService updateService;
    private DeliverService deliverService;
    private Logger logger = LoggerFactory.getLogger(getClass());
    private WmsApi openWmsApi;
    private BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper;
    private JarvisConfig jarvisConfig;

    private JarvisOpenApi jarvisOpenApi;

    public DeliverBillUpdateController(DeliverBillUpdateService updateService, DeliverService deliverService,WmsApi openWmsApi,
                                       BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper,
                                       JarvisOpenApi jarvisOpenApi,
                                       JarvisConfig jarvisConfig) {
        this.updateService = updateService;
        this.deliverService = deliverService;
        this.openWmsApi=openWmsApi;
        this.billExStatusAsyncHandleHelper = billExStatusAsyncHandleHelper;
        this.jarvisConfig = jarvisConfig;
        this.jarvisOpenApi = jarvisOpenApi;
    }

    @ApiOperation(value = "原始订单更新发货单主表信息")
    @PostMapping("updateBill")
    public DeliverBillUpdateResponse DeliverBillUpdate(@RequestBody List<DeliverBillUpdateRequest> request) {
        return jarvisOpenApi.DeliverBillUpdate(request);
    }

    @ApiOperation(value = "售后单更新发货单主表信息")
    @PostMapping("refundUpdate")
    public RefundUpdateResponse RefundUpdate(@RequestBody List<RefundOrderDAO> request) {
        return jarvisOpenApi.refundUpdate(request);
    }


    @ApiOperation(value = "原始订单更新发货单平台标记")
    @PostMapping("updateBillPlatformMark")
    public DeliverBillUpdateResponse updateBillPlatformMark(@RequestBody List<DeliverBillPlatformMarkUpdateDao> request) {
        return jarvisOpenApi.updateBillPlatformMark(request);
    }

    @ApiOperation(value = "获取言址票物流的更新信息")
    @PostMapping("queryDeliverBillMarkUpdateData")
    public DeliverBillUpdateResponse<List<DeliverBillMarkChangeInfo>> queryDeliverBillMarkUpdateData(@RequestBody DeliverBillUpdateByMarkRequest request) {
        DeliverBillUpdateResponse<List<DeliverBillMarkChangeInfo>> response = new DeliverBillUpdateResponse<>();
        try {
            response.setCode("0");
            response.setData(updateService.queryDeliverBillMarkUpdateData(CurrentUser.getProfileId(), request.getWarehouseTaskId(), request.isPost()));
        } catch (Exception e) {
            response.setCode("-1");
            response.setMsg(String.format("获取言址票物流的更新信息，原因：【%s】", e.getMessage()));
            logger.error("获取言址票物流的更新信息", e);
        }
        return response;
    }

    @ApiOperation(value = "根据言址票物流mark data 里的Id更新订单")
    @PostMapping("updateBillByMarkDatas")
    public DeliverBillUpdateResponse updateBillByMarkDatas(@RequestBody DeliverBillUpdateByMarkRequest request) {
        DeliverBillUpdateResponse response = new DeliverBillUpdateResponse();
        try {
            response.setCode("0");
            updateService.updateBillByMarkDatas(CurrentUser.getProfileId(), new WarehouseBillIds(request.getVchcode(),request.getWarehouseTaskId()),
                    request.getOrderId(), request.getInvoiceOpera(), request.getMemoOpera(), request.getBuyerInfoOpera(),request.getFreightInfoOpera());
        } catch (Exception e) {
            response.setCode("-1");
            response.setMsg(String.format("更新言址票物流失败，原因：【%s】", e.getMessage()));
            logger.error("更新言址票物流失败：", e);
        }
        return response;
    }

    @ApiOperation(value = "判断订单是否合并过")
    @PostMapping("checkBillHasMerged")
    public DeliverBillUpdateResponse<Boolean> checkBillHasMerged(@RequestBody DeliverBillUpdateByMarkRequest request) {
//        ErrorFace.看我看我别忘了改("凌雪 点击标记检查订单可合并");
        DeliverBillUpdateResponse<Boolean> response = new DeliverBillUpdateResponse<>();
        try {
            response.setCode("0");
            response.setData(updateService.checkBillHasMerged(CurrentUser.getProfileId(), request.getWarehouseTaskId(), request.isPost()));
        } catch (Exception e) {
            response.setCode("-1");
            response.setMsg(String.format("更新言址票物流失败，原因：【%s】", e.getMessage()));
            logger.error("更新言址票物流失败：", e);
        }
        return response;
    }

    @ApiOperation(value = "标记更新发货单主表信息")
    @PostMapping("updateBillByMark")
    public DeliverBillUpdateResponse DeliverBillUpdateByMark(@RequestBody DeliverBillUpdateByMarkRequest request) {
        DeliverBillUpdateResponse response = new DeliverBillUpdateResponse();
        response.setCode("0");
        try {
            updateService.updateBillByMark(CurrentUser.getProfileId(), request.getOrderId(), new WarehouseBillIds(request.getVchcode(),request.getWarehouseTaskId()), request.getDeliverMarkEnumCode());
        } catch (Exception ex) {
            response.setCode("-1");
            response.setMsg(String.format("更新失败，原因：【%s】", ex.getMessage()));
            logger.error("标记更新发货单主表信息失败 {}", Slf4jPrintStackUtil.getErrorStack(ex));
        }
        return response;
    }
    @ApiOperation(value = "标记更新发货单主表信息")
    @PostMapping("batchUpdateBillByMark")
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.defaultProcessName)
    public ProcessResponse<StrategyProcessLog> DeliverBillBatchUpdateByMark(@RequestBody DeliverBillBatchUpdateByMarkRquest request) {
        BigInteger employeeId = CurrentUser.getEmployeeId();
        BigInteger profileId = CurrentUser.getProfileId();
        RedisProcessMessage processMessage = new ProcessMessageMemory(request,request.getProcessId());
        RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
        try {
            logger.appendMsg("开始执行");
            updateService.batchUpdateBillByMark(profileId,employeeId, request,logger);
            logger.appendMsg("执行完毕");
        } catch (Exception e) {
            String msg = String.format("执行错误：%s",e.getMessage());
            logger.appendMsg(msg);
            RedisProcessMsgUtils.resetLoggerError(logger,true);
        } finally {
            processMessage.setFinish();
        }
        return ProcessResponse.result(StrategyProcessLog.class,processMessage);
    }
    @ApiOperation(value = "标记更新发货单主表,明细信息")
    @PostMapping("updateDetailMark")
    public DeliverBillUpdateResponse updateDetailMark(@RequestBody DeliverBillDetailMarkUpdate request) {
        DeliverBillUpdateResponse response = new DeliverBillUpdateResponse();
        response.setCode("0");
        try {
            updateService.updateDetailMark(CurrentUser.getProfileId(), request);
        } catch (Exception ex) {
            response.setCode("-1");
            response.setMsg(String.format("操作失败，原因：【%s】", ex.getMessage()));
        }
        return response;
    }

    @ApiOperation(value = "标记更新发货单主表信息 无用")
    @PostMapping("updateBillByMarks")
    public void DeliverBillUpdateByMarks(@RequestBody List<DeliverBillUpdateByMarkRequest> requests) {
        try {
            for (DeliverBillUpdateByMarkRequest request : requests) {
                updateService.updateBillByMark(CurrentUser.getProfileId(), request.getOrderId(),new WarehouseBillIds(request.getVchcode(),request.getWarehouseTaskId()), request.getDeliverMarkEnumCode());
            }
        } catch (Exception ex) {
            logger.error("批量更新描述错误 {}", Slf4jPrintStackUtil.getErrorStack(ex));
        }
    }

    @ApiOperation(value = "售后获取关联发货单")
    @PostMapping("getRefundDeliverBill")
    public BaseResponse getRefundDeliverBill(@RequestBody RefundDeliverBillRequest request) {
//        if(request.getVchcodes() != null && request.getVchcodes().size() > jarvisConfig.getRefundSelectSize()) {
//            return CommonResponse.fail("单次勾选单据数据行数不得操作100条，请重新选择单据");
//        }
        try {
            return CommonResponse.success(deliverService.getRefundDeliverBill(CurrentUser.getProfileId(), request));
        } catch (Exception e) {
            logger.error("售后获取关联发货单错误 {}", Slf4jPrintStackUtil.getErrorStack(e));
            return CommonResponse.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "原单获取关联发货单")
    @PostMapping("getDeliverBill")
    public List<SaleOrderSelectDeliverBillResponse> getDeliverBillByOrderId(@RequestBody SaleOrderSelectDeliverBillRequest request) {
        return deliverService.getDeliverBillDetailsByOrderId(CurrentUser.getProfileId(), request.getOtypeId(), request.getOrderId(), request.getTradeOrderId());
    }

    @ApiOperation(value = "获取发货单明细")
    @PostMapping("getDeliverDetails")
    public Map<BigInteger, List<BillDeliverDetailDTO>> getDeliverDetails(@RequestBody List<BigInteger> orderIds) {
        try {
            List<BillDeliverDetailDTO> details = deliverService.getDeliverBillDetailsByOrderId(CurrentUser.getProfileId(), orderIds);
            Map<BigInteger, List<BillDeliverDetailDTO>> result = details.stream().collect(Collectors.groupingBy(BillDeliverDetailDTO::getOrderId));
            return result;
        } catch (Exception e) {
            logger.error("获取orderId关联发货单明细失败 {}", Slf4jPrintStackUtil.getErrorStack(e));
            return new HashMap<>();
        }
    }

    @ApiOperation(value = "修改预计发货时间")
    @PostMapping("updateBillPlanSendTime")
    public BaseResponse updateBillPlanSendTime(@RequestBody List<DeliverBillUpdatePlanSendTimeRequest> requests) {
        try {
            updateService.updateBillPlanSendTime(requests);
        } catch (Exception e) {
            logger.error("延迟发货失败：", e);
            return CommonResponse.fail("延迟发货失败");
        }
        return CommonResponse.success();
    }
    @ApiOperation(value = "取消预计发货时间")
    @PostMapping("cancelBillPlanSendTime")
    public BaseResponse cancelBillPlanSendTime(@RequestBody List<DeliverBillUpdatePlanSendTimeRequest> requests) {
        try {
            updateService.cancelBillPlanSendTime(requests);
        } catch (Exception e) {
            logger.error("取消延迟发货失败：", e);
            return CommonResponse.fail("取消延迟发货失败");
        }
        return CommonResponse.success();
    }

    @ApiOperation(value = "修改主单号")
    @PostMapping("modifyMainBill")
    public void modifyMainBill(@RequestBody BillDeliverDTO bill) {
        updateService.modifyMainBill(bill);
    }


    public static class DeliverBillUpdateByMarkRequest implements Serializable {
        private BigInteger orderId;
        private UpdateTypeEnum type;
        private BaseOrderMarkEnum deliverMarkEnumCode;
        private BigInteger vchcode;
        private BigInteger warehouseTaskId;
        private boolean post;
        private DeliverMarkModifyType memoOpera;
        private DeliverMarkModifyType invoiceOpera;
        private DeliverMarkModifyType buyerInfoOpera;
        private DeliverMarkModifyType freightInfoOpera;
        public DeliverMarkModifyType getMemoOpera() {
            return memoOpera;
        }

        public void setMemoOpera(DeliverMarkModifyType memoOpera) {
            this.memoOpera = memoOpera;
        }

        public DeliverMarkModifyType getInvoiceOpera() {
            return invoiceOpera;
        }

        public void setInvoiceOpera(DeliverMarkModifyType invoiceOpera) {
            this.invoiceOpera = invoiceOpera;
        }

        public DeliverMarkModifyType getBuyerInfoOpera() {
            return buyerInfoOpera;
        }

        public void setBuyerInfoOpera(DeliverMarkModifyType buyerInfoOpera) {
            this.buyerInfoOpera = buyerInfoOpera;
        }

        public boolean isPost() {
            return post;
        }

        public void setPost(boolean post) {
            this.post = post;
        }

        public BigInteger getOrderId() {
            return orderId;
        }

        public void setOrderId(BigInteger orderId) {
            this.orderId = orderId;
        }

        public UpdateTypeEnum getType() {
            return type;
        }

        public void setType(UpdateTypeEnum type) {
            this.type = type;
        }

        public BigInteger getVchcode() {
            return vchcode;
        }

        public void setVchcode(BigInteger vchcode) {
            this.vchcode = vchcode;
        }

        public BaseOrderMarkEnum getDeliverMarkEnumCode() {
            return deliverMarkEnumCode;
        }

        public void setDeliverMarkEnumCode(BaseOrderMarkEnum deliverMarkEnumCode) {
            this.deliverMarkEnumCode = deliverMarkEnumCode;
        }

        public BigInteger getWarehouseTaskId() {
            return warehouseTaskId;
        }

        public void setWarehouseTaskId(BigInteger warehouseTaskId) {
            this.warehouseTaskId = warehouseTaskId;
        }

        public DeliverMarkModifyType getFreightInfoOpera() {
            return freightInfoOpera;
        }

        public void setFreightInfoOpera(DeliverMarkModifyType freightInfoOpera) {
            this.freightInfoOpera = freightInfoOpera;
        }
    }


}
