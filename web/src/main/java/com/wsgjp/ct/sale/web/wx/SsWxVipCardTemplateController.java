package com.wsgjp.ct.sale.web.wx;

import bf.datasource.page.PageRequest;
import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.sale.biz.wx.dto.SelectWxVipCardListDto;
import com.wsgjp.ct.sale.biz.wx.entity.OpsWxopenAuth;
import com.wsgjp.ct.sale.biz.wx.entity.SsWxVipCardTemplate;
import com.wsgjp.ct.sale.biz.wx.service.ISsWxVipCardTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;

@AllArgsConstructor
@RestController
@Slf4j
@RequestMapping(value = "/${app.id}/wx/member")
@Api(tags = "微信会员接口")
public class SsWxVipCardTemplateController {

    private final ISsWxVipCardTemplateService ssWxVipCardTemplateService;

    @ApiOperation(value = "微信会员列表")
    @PostMapping("/list")
    public PageInfo<SsWxVipCardTemplate> list(@RequestBody PageRequest<SelectWxVipCardListDto> dto) {
        return ssWxVipCardTemplateService.list(dto);
    }

    @ApiOperation(value = "微信会员列表")
    @PostMapping("/getTemplate")
    public SsWxVipCardTemplate getTemplate(@RequestBody BigInteger wxTemplateId) {
        return ssWxVipCardTemplateService.getTemplate(wxTemplateId);
    }

    @ApiOperation(value = "自动同步设置")
    @PostMapping("/autoSyncSetting")
    public void autoSyncSetting(@RequestBody OpsWxopenAuth autoSync) {
        ssWxVipCardTemplateService.autoSyncSetting(autoSync);
    }

    @ApiOperation(value = "获取自动同步状态")
    @GetMapping("/getAutoSync")
    public OpsWxopenAuth getAutoSync() {
        return ssWxVipCardTemplateService.getAutoSync();
    }

    @ApiOperation(value = "下载二维码")
    @PostMapping("/downloadQrCode")
    @ResponseBody
    public void downloadQrCode(@RequestBody String qrCodeUrl, HttpServletResponse response) {
        ssWxVipCardTemplateService.downloadQrCode(qrCodeUrl, response);
    }

}
