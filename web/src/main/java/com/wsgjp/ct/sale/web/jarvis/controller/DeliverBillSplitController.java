package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.common.util.VersionUtil;
import com.wsgjp.ct.sale.biz.jarvis.common.deliverBill.SplitBillUtil;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.ProcessRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.StrategyComplexDetailEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.split.SplitByTemplate;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.split.SplitGoodsType;
import com.wsgjp.ct.sale.biz.jarvis.entity.template.TemplateManagementEntity;
import com.wsgjp.ct.sale.biz.jarvis.mapper.StrategyConfigMapper;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverSplitService;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.biz.jarvis.service.strategy.StrategyConfigService;
import com.wsgjp.ct.sale.biz.jarvis.service.template.TemplateManagementService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.StrategyUtils;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.*;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.split.PTypeComboDetailSplitMode;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.split.SpecialPTypeConfig;
import com.wsgjp.ct.sale.biz.jarvis.strategy.executor.AssignedStrategyConfigsExecutor;
import com.wsgjp.ct.sale.biz.jarvis.strategy.impl.split.SplitMultiGoodsSubStrategyConfig;
import com.wsgjp.ct.sale.biz.jarvis.strategy.impl.split.SplitSpecialPTypeSubStrategyConfig;
import com.wsgjp.ct.sale.biz.jarvis.strategy.impl.split.SplitStrategyConfig;
import com.wsgjp.ct.sale.biz.jarvis.utils.Slf4jPrintStackUtil;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.NeedProcessMsgBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessFaceBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessMessageMemory;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessResponseAndResult;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * @Date 2020-04-20 17:42
 * @Created by lingxue
 */
@RestController
@Api("订单拆分")
@RequestMapping("/${app.id}/jarvis/split")
public class DeliverBillSplitController {
    private BillDeliverSplitService splitService;
    private DeliverService deliverService;
    private TemplateManagementService templateManagementService;
    private AssignedStrategyConfigsExecutor assignedStrategyConfigsExecutor;
    private StrategyConfigMapper strategyConfigMapper;
    private StrategyConfigService strategyConfigService;
    private SplitBillUtil splitBillUtil;
    private Logger carbalogger = LoggerFactory.getLogger(getClass());

    public DeliverBillSplitController(BillDeliverSplitService splitService, DeliverService deliverService,
                                      TemplateManagementService templateManagementService,
                                      AssignedStrategyConfigsExecutor assignedStrategyConfigsExecutor,
                                      StrategyConfigMapper strategyConfigMapper,
                                      StrategyConfigService strategyConfigService,
                                      SplitBillUtil splitBillUtil) {
        this.splitService = splitService;
        this.deliverService = deliverService;
        this.templateManagementService = templateManagementService;
        this.assignedStrategyConfigsExecutor = assignedStrategyConfigsExecutor;
        this.strategyConfigMapper = strategyConfigMapper;
        this.strategyConfigService = strategyConfigService;
        this.splitBillUtil = splitBillUtil;
    }

    @ApiOperation(value = "异步订单拆分", notes = "")
    @PostMapping("asyncStrategySplitBills")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_SPLIT_BY_AUTO)
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.strategySplitProcessName, redirectUrl = "sale/jarvis/split/asyncStrategySplitBills", serviceName = "strategySplitBill")
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> asyncSplitBills(@RequestBody ProcessRequest request) {
        return splitBillsBase(request,VersionUtil.isNewVersion()?StrategyType.BATCH_AD_SPLIT:StrategyType.SPLIT);
    }

    @ApiOperation(value = "同步订单拆分（仅供仓储组使用）", notes = "")
    @PostMapping("splitBills")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_SPLIT_BY_AUTO)
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> splitBills(@RequestBody ProcessRequest request) {
        return splitBillsBase(request,VersionUtil.isNewVersion()?StrategyType.BATCH_AD_SPLIT:StrategyType.SPLIT);
    }
    @ApiOperation(value = "按数量拆分", notes = "")
    @PostMapping("asyncSplitBillsByQty")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_SPLIT_BY_AUTO)
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.splitByQtyProcessName, redirectUrl = "sale/jarvis/split/asyncSplitBillsByQty", serviceName = "splitByQty")
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> splitBillsByQty(@RequestBody ProcessRequest request) {
        return splitBillsBase(request,StrategyType.SPLIT_ONLY_QTY);
    }

    @ApiOperation(value = "按商品拆分", notes = "")
    @PostMapping("asyncSplitBillsByGoods")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_SPLIT_BY_AUTO)
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.splitByGoodsProcessName, redirectUrl = "sale/jarvis/split/asyncSplitBillsByGoods", serviceName = "splitByGoods")
    public List<BigInteger> splitBillsByPtype(@RequestBody SplitByTemplate request) {
       return deliverService.splitBillsByPtype(request);
        }


    /**
     * 多品固定组合拆分
     * @param templateId
     * @return
     */
    private SplitStrategyConfig getConfig(BigInteger templateId,int templateType)
    {
        TemplateManagementEntity template = this.templateManagementService.queryTemplateDetail(templateId);

        BigInteger profileId = CurrentUser.getProfileId();
        SplitStrategyConfig config = new SplitStrategyConfig();
        config.setProfileId(profileId);
        config.setEnabled(true);
        config.setName("按商品拆分");
        config.setExecuteWay(StrategyExecuteWayEnum.EXECUTE_STRATEGY);
        config.setOtypeType(1);
        config.setStrategyType(StrategyType.SPLIT_ONLY_GOODS);
        List<PTypeComboDetailSplitMode> comboDetailSplitModes = strategyConfigService.getCombos(profileId,null);
        //多品固定组合
        if(templateType == 2) {
            template.setTemplateContent(template.getTemplateContent().replace("ptypeDetail","details"));
            SplitMultiGoodsSubStrategyConfig subConfig = JsonUtils.toObject(template.getTemplateContent(), SplitMultiGoodsSubStrategyConfig.class);
            List<BigInteger> ids = new ArrayList<>();
            for (SpecialPTypeConfig pTypeConfig:subConfig.getSpecialPTypeConfigs()) {
                ids.add(pTypeConfig.getId());
            }
            List<StrategyComplexDetailEntity> details = this.strategyConfigService.getSplitPtypes(profileId,ids);
            for (SpecialPTypeConfig pTypeConfig:subConfig.getSpecialPTypeConfigs()) {
                for (StrategyComplexDetailEntity detail:details) {
                    if(pTypeConfig.getId().equals(detail.getId()))
                    {
                        pTypeConfig.setDetails(detail.getPtypeDetail());
                        continue;
                    }
                }
            }
            subConfig.setName("按商品拆分");
            subConfig.setExecuteWay(StrategyExecuteWayEnum.EXECUTE_STRATEGY);
            subConfig.setEnabled(true);
            subConfig.setProfileId(profileId);
            subConfig.setComboDetailSplitModes(comboDetailSplitModes);
            config.setSubConfigs(Collections.singletonList(subConfig));
        }
        else
        {
            SplitSpecialPTypeSubStrategyConfig subConfig = JsonUtils.toObject(template.getTemplateContent(), SplitSpecialPTypeSubStrategyConfig.class);
            subConfig.setName("按商品拆分");
            subConfig.setExecuteWay(StrategyExecuteWayEnum.EXECUTE_STRATEGY);
            subConfig.setEnabled(true);
            subConfig.setProfileId(profileId);
            subConfig.setComboDetailSplitModes(comboDetailSplitModes);
            subConfig.getContents().forEach(content->{
                if(content.getMaxValue().equals(BigDecimal.ZERO))
                {
                    content.setMaxValue(BigDecimal.valueOf(999999999));
                }
                List<StrategyComplexDetailEntity> details = this.strategyConfigService.getSplitPtypes(profileId,Collections.singletonList(content.getId()));
                content.setPtypeDetail(details.get(0).getPtypeDetail());
                content.setGoodsType(templateType == 0? SplitGoodsType.ALL:SplitGoodsType.SINGLE);
            });
            config.setSubConfigs(Collections.singletonList(subConfig));
        }
        return config;
    }


    @ApiOperation(value = "按标记拆分", notes = "")
    @PostMapping("asyncSplitBillsByMark")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_SPLIT_BY_AUTO)
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.splitByGoodsProcessName, redirectUrl = "sale/jarvis/split/asyncSplitBillsByMark", serviceName = "splitByMark")
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> splitBillsByMark(@RequestBody ProcessRequest request) {
        return splitBillsBase(request,StrategyType.SPLIT_ONLY_MARK);
    }

    private ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> splitBillsBase(ProcessRequest request,StrategyType strategyType) {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request,request.getProcessId());
        RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
        List<BigInteger> warehouseTaskIds = request.getWarehouseTaskIds();
        List<StrategyProcessLog> result = new ArrayList<>();
        try {
            result = deliverService.splitBillsByStrategy(CurrentUser.getProfileId(), request, strategyType,logger);
            warehouseTaskIds = result.stream().map(r->r.getOriginalVchcode()).distinct().collect(Collectors.toList());
            processMessage.doCacheProcessServiceResultMessage(warehouseTaskIds);
            logger.cacheProcessErrorMessage(result);
        } catch (Exception ex)
        {
            SimpleStrategyProcessLog log = new SimpleStrategyProcessLog();
            log.setContent(ex.getMessage() == null ? ex.getClass().getSimpleName() : ex.getMessage());
            logger.cacheProcessErrorMessage(Arrays.asList(log));
            carbalogger.error(String.format("订单拆分失败，warehouseTaskId：{}，错误：{}", request.getWarehouseTaskIds(), Slf4jPrintStackUtil.getErrorStack(ex)),ex);
        }
        finally {
            processMessage.setFinish();
        }
        return ProcessResponseAndResult.result(StrategyProcessLog.class,BigInteger.class,processMessage);
    }

    @ApiOperation(value = "异步取消订单拆分", notes = "")
    @PostMapping("cancelSplitBills")
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.cancelSplitBillsProcessName)
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> cancelSplitBills(@RequestBody ProcessRequest request) {
        return _cancelSplitBills(request);
    }

    @ApiOperation(value = "同步取消订单拆分（仅供仓储组使用）", notes = "")
    @PostMapping("sycnCancelSplitBills")
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> syncCancelSplitBills(@RequestBody ProcessRequest request) {
        return _cancelSplitBills(request);
    }

    @NotNull
    private ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> _cancelSplitBills(ProcessRequest request) {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request, request.getProcessId());
        RedisProcessMessage.MsgLogger logger = processMessage.getMsgLogger();
        List<BigInteger> newTaskIds = new ArrayList<>();
        try {
            List<StrategyExecuteEntity> datas = splitService.cancel(CurrentUser.getProfileId(), request.getWarehouseTaskIds());
            for (StrategyExecuteEntity data : datas) {
                newTaskIds.addAll(data.getRelationVchcodes());
            }
            processMessage.doCacheProcessServiceResultMessage(newTaskIds);
            logger.cacheProcessErrorMessage(StrategyUtils.getStrategyExecuteLog(datas));
        } catch (Exception ex) {
            carbalogger.error(String.format("订单取消拆分失败，vchcode：%s，错误：%s", request.getWarehouseTaskIds(), Slf4jPrintStackUtil.getErrorStack(ex)), ex);
        } finally {
            processMessage.setFinish();
        }
        return ProcessResponseAndResult.result(StrategyProcessLog.class,BigInteger.class,processMessage);
    }

    @ApiOperation(value = "获取关联订单", notes = "")
    @PostMapping("getRelation")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_SPLIT_BY_AUTO)
    public List<BigInteger> getRelation(@RequestBody ProcessRequest request) {
        return splitService.getRelation(CurrentUser.getProfileId(),request.getWarehouseTaskIds());
    }

    @ApiOperation(value = "获取vchcode", notes = "")
    @PostMapping("buildVchcode")
    public List<BigInteger> buildVchcodes(@RequestBody int count) {
        List<BigInteger> vchcodes = new ArrayList<>();
        for(int i=0;i<count;i++)
        {
            vchcodes.add(UId.newId());
        }
        return vchcodes;
    }

    @ApiOperation(value = "splitDeliverBills", notes = "（）")
    @PostMapping("splitDeliverBills")
    public void SplitBillTest(@RequestBody List<BigInteger> taskIds)
    {
        try {
            splitBillUtil.splitBillsByTaskId(taskIds);
        }
        catch (Exception e)
        {
            carbalogger.error("拆分交易单失败， {}", e.getMessage());
            throw e;
        }
    }

    @ApiOperation(value = "splitSendTask", notes = "（）")
    @PostMapping("splitSendTask")
    public void splitSendTask(@RequestBody SplitBillRequest request)
    {
        carbalogger.error("任务单拆分数据报文：{}",JsonUtils.toJson(request));
        RedisProcessMessage processMessage = new ProcessMessageMemory(request,request.getMessageId());
        try {
            splitBillUtil.splitBillAndTask(request.getTaskIds(),true,processMessage.getMsgLogger());
        }
        catch (Exception ex)
        {
            carbalogger.error(String.format("订单取消拆分失败，错误：%s", request.getTaskIds()), ex);
        }
        finally {
            if(request.getMessageId() == null) {
                processMessage.setFinish();
            }
        }
    }
}

class SplitBillRequest implements ProcessFaceBatchAd
{
    @ApiModelProperty(value = "进度信息id")
    private String messageId;
    @ApiModelProperty(value = "任务单id", required = true)
    private List<BigInteger> taskIds;

    private boolean splitByVchcode;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public List<BigInteger> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<BigInteger> taskIds) {
        this.taskIds = taskIds;
    }

    public boolean isSplitByVchcode() {
        return splitByVchcode;
    }

    public void setSplitByVchcode(boolean splitByVchcode) {
        this.splitByVchcode = splitByVchcode;
    }

    @Override
    public Boolean getNotProcess() {
        return null;
    }

    @Override
    public String getProcessId() {
        return "";
    }
}


