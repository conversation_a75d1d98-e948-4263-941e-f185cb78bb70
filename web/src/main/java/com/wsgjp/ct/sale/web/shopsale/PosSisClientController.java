package com.wsgjp.ct.sale.web.shopsale;

import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.PosBuyer;
import com.wsgjp.ct.sale.biz.shopsale.service.PosSisClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;

@Api(tags = "pos组加密解密接口")
@RestController
@RequestMapping("${app.id}/shopsale/sisClient")
public class PosSisClientController {

    @Autowired
    private PosSisClientService sisClientService;

    @ApiOperation(value = "批量解密买家信息")
    @PostMapping("/batchDecryptBuyers")
    public List<PosBuyer> batchDecryptBuyers(@RequestBody List<BigInteger> buyerIds) {
        return sisClientService.batchDecryptBuyers(buyerIds);
    }
}
