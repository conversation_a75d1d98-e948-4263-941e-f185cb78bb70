package com.wsgjp.ct.sale.web.shopsale;

import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.AddOrUpdateOtypePriceRequestDTO;
import com.wsgjp.ct.sale.biz.shopsale.service.IBasePtypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;

@Api(value = "${app.id}/shopsale/ptype", tags = {"商品"})
@RestController
@RequestMapping("${app.id}/shopsale/ptype")
public class PtypeController {

    final IBasePtypeService ptypeService;

    public PtypeController(IBasePtypeService ptypeService) {
        this.ptypeService = ptypeService;
    }

    @ApiOperation(value = "更新商品店铺零售价")
    @PostMapping("/addOrUpdateOtypePrice")
    public BigInteger addOrUpdateOtypePrice(@RequestBody AddOrUpdateOtypePriceRequestDTO request) throws Exception {
        return ptypeService.addOrUpdateOtypePrice(request);
    }
}
