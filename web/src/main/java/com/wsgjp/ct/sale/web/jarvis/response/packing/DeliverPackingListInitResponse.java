package com.wsgjp.ct.sale.web.jarvis.response.packing;

import com.wsgjp.ct.sale.biz.jarvis.dto.DeliverCustomerQueryDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.dto.Dtype;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.*;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.DeliverPackingQueryParams;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DeliverPackingListInitResponse {
    private DeliverPackingQueryParams queryParams;
    private List<Employee> employees;
    private List<Stock> ktypeList;
    private List<Freight> freightList;
    private List<Organization> organizationList;
    private List<Btype> btypeList;
    private List<Dtype> dtypeList;
    private List<DillDeliverState> packingTypes;
    private List<DillDeliverState> packingStates;

    private boolean serialEnabled;
    private boolean propEnabled;
    private boolean batchEnabled;

    private List<DeliverCustomerQueryDTO> customerQuery;

    public List<DeliverCustomerQueryDTO> getCustomerQuery() {
        return customerQuery;
    }

    public void setCustomerQuery(List<DeliverCustomerQueryDTO> customerQuery) {
        this.customerQuery = customerQuery;
    }

    public List<Organization> getOrganizationList() {
        return organizationList;
    }

    public void setOrganizationList(List<Organization> organizationList) {
        this.organizationList = organizationList;
    }

    public List<Btype> getBtypeList() {
        return btypeList;
    }

    public void setBtypeList(List<Btype> btypeList) {
        this.btypeList = btypeList;
    }

    public DeliverPackingQueryParams getQueryParams() {
        return queryParams;
    }

    public void setQueryParams(DeliverPackingQueryParams queryParams) {
        this.queryParams = queryParams;
    }

    public List<Employee> getEmployees() {
        return employees;
    }

    public void setEmployees(List<Employee> employees) {
        this.employees = employees;
    }

    public List<Stock> getKtypeList() {
        return ktypeList;
    }

    public void setKtypeList(List<Stock> ktypeList) {
        this.ktypeList = ktypeList;
    }

    public List<Freight> getFreightList() {
        return freightList;
    }

    public void setFreightList(List<Freight> freightList) {
        this.freightList = freightList;
    }

    public List<Dtype> getDtypeList() {
        return dtypeList;
    }

    public void setDtypeList(List<Dtype> dtypeList) {
        this.dtypeList = dtypeList;
    }

    public boolean isSerialEnabled() {
        return serialEnabled;
    }

    public void setSerialEnabled(boolean serialEnabled) {
        this.serialEnabled = serialEnabled;
    }

    public boolean isPropEnabled() {
        return propEnabled;
    }

    public void setPropEnabled(boolean propEnabled) {
        this.propEnabled = propEnabled;
    }

    public boolean isBatchEnabled() {
        return batchEnabled;
    }

    public void setBatchEnabled(boolean batchEnabled) {
        this.batchEnabled = batchEnabled;
    }

    public List<DillDeliverState> getPackingTypes() {
        return packingTypes;
    }

    public void setPackingTypes(List<DillDeliverState> packingTypes) {
        this.packingTypes = packingTypes;
    }

    public List<DillDeliverState> getPackingStates() {
        return packingStates;
    }

    public void setPackingStates(List<DillDeliverState> packingStates) {
        this.packingStates = packingStates;
    }
}
