package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.eshoporder.entity.request.ExpenseBillInfoRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BusinessBillDetailInfoResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021-03-24
 */
@Api(tags = "分摊订单对外提供的接口相关")
@RequestMapping("${app.id}/eshoporder/shareOrderApi")
@RestController
public class ApiShareOrderController {
    private final EshopService eshopService;

    public ApiShareOrderController(EshopService eshopService) {
        this.eshopService = eshopService;
    }

    /**
     * 查询销售出库单
     * @param request
     * @return
     */
    @PostMapping("/getShareOrderApi")
    public BusinessBillDetailInfoResponse getShareOrder(@RequestBody ExpenseBillInfoRequest request) {
        return eshopService.finBuinessDetailInfoByVchcode(request);
    }
}
