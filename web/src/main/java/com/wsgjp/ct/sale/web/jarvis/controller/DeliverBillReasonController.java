package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.service.DeliverBillReasonService;
import com.wsgjp.ct.sale.sdk.reason.entity.BillDeliverReasonDTO;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-12-05
 */
@RestController
@Api(description = "截停或驳回-原因管理")
@RequestMapping("/${app.id}/jarvis/deliverBillReason")
public class DeliverBillReasonController {
    private final static Logger logging = LoggerFactory.getLogger(DeliverBillReasonController.class);
    private final DeliverBillReasonService deliverBillReasonService;

    public DeliverBillReasonController(DeliverBillReasonService deliverBillReasonService) {
        this.deliverBillReasonService = deliverBillReasonService;
    }

    @ApiOperation(value = "保存原因")
    @PostMapping("saveReason")
    public BaseResponse save(@RequestBody List<BillDeliverReasonDTO> requestList){
        BaseResponse response = new BaseResponse();
        try {
            deliverBillReasonService.saveReason(requestList);
            response.setCode("0");
        } catch (Exception e) {
            response.setCode("1");
            response.setMsg(e.getMessage());
            logging.error(e.getMessage(),e);
        }
        return response;
    }

    @ApiOperation(value = "删除原因")
    @PostMapping("deleteReason")
    public BaseResponse deleteReason(@RequestBody BillDeliverReasonDTO request) {
        BaseResponse response = new BaseResponse();
        try {
            deliverBillReasonService.deleteReason(request);
            response.setCode("0");
        } catch (Exception e) {
            response.setCode("1");
            response.setMsg(e.getMessage());
            logging.error(e.getMessage(),e);
        }
        return response;
    }

    @ApiOperation(value = "查询是否被使用")
    @PostMapping("queryReasonByUsed")
    public BaseResponse queryReasonByUsed(@RequestBody BillDeliverReasonDTO request) {
        BaseResponse response = new BaseResponse();
        try {
            deliverBillReasonService.queryReasonByUsed(request);
            response.setCode("0");
        } catch (Exception e) {
            response.setCode("1");
            response.setMsg(e.getMessage());
        }
        return response;
    }

    @ApiOperation(value = "交易单操作原因表")
    @PostMapping("queryReason")
    public List<BillDeliverReasonDTO> queryReason(@RequestBody BillDeliverReasonDTO request) {
        return deliverBillReasonService.queryReason(request.getReasonType(),null);
    }

}
