package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopAuthService;
import com.wsgjp.ct.sale.platform.dto.token.EshopAuthInfo;
import com.wsgjp.ct.sale.platform.entity.request.auth.*;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.tmc.TmcRegisterResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "网店授权相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/auth")
public class BifrostEshopAuthController {
    private final BifrostEshopAuthService authService;

    public BifrostEshopAuthController(BifrostEshopAuthService authService) {
        this.authService = authService;
    }

    @ApiOperation("获取授权地址")
    @PostMapping("getAuthUrl")
    public String getAuthUrl(@RequestBody EshopState eshopState) {
        return authService.getAuthUrl(eshopState);
    }

    @ApiOperation("获取token")
    @PostMapping("getToken")
    public EshopAuthInfo getToken(@RequestBody GetTokenRequest request) {
        return authService.getToken(request);
    }

    @ApiOperation("刷新token")
    @PostMapping("refreshToken")
    public EshopAuthInfo refreshToken(@RequestBody RefreshTokenRequest request) {
        return authService.refreshToken(request);
    }

    @ApiOperation("检查是否有订阅")
    @PostMapping("checkSubscribe")
    public boolean checkSubscribe(@RequestBody CheckSubscribesRequest request) {
        return authService.checkSubscribes(request);
    }

    @ApiOperation("订阅tmc")
    @PostMapping("registerTmc")
    public TmcRegisterResponse registerTmc(@RequestBody TmcRegisterRequest request) {
        return authService.registerTmc(request);
    }

    @ApiOperation("订阅rds")
    @PostMapping("registerRds")
    public BaseResponse registerRds(@RequestBody RdsRegisterRequest request) {
        return authService.registerRds(request);
    }
}
