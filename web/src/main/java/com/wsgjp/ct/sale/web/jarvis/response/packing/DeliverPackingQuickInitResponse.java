package com.wsgjp.ct.sale.web.jarvis.response.packing;

import com.wsgjp.ct.sale.biz.jarvis.config.DeliverPackingConfig;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Employee;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DeliverPackingQuickInitResponse {
    private List<Employee> employees;
    private DeliverPackingConfig config;
    private BigInteger employeeId;
    public List<Employee> getEmployees() {
        return employees;
    }

    public void setEmployees(List<Employee> employees) {
        this.employees = employees;
    }

    public DeliverPackingConfig getConfig() {
        return config;
    }

    public void setConfig(DeliverPackingConfig config) {
        this.config = config;
    }

    public BigInteger getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(BigInteger employeeId) {
        this.employeeId = employeeId;
    }
}
