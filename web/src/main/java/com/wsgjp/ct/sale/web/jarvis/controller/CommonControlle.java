package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.ptype.PtypeXcode;
import com.wsgjp.ct.sale.biz.jarvis.dto.ptype.QueryXcodeParameter;
import com.wsgjp.ct.sale.biz.jarvis.dto.ptype.UnitDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.secret.SecretInfo;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.common.Md5Service;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.sale.web.jarvis.response.CommonResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-04-20 16:49
 */
@RestController
@Api(description = "公用")
@RequestMapping("/${app.id}/jarvis/common")
public class CommonControlle {
    private BaseInfoService service;
    private Md5Service deliverDetailsCommonService;

    public CommonControlle(BaseInfoService service, Md5Service deliverDetailsCommonService) {
        this.service = service;
        this.deliverDetailsCommonService = deliverDetailsCommonService;
    }

    @RequestMapping(value = "/getBaseXcode", method = RequestMethod.POST)
    public PtypeXcode getBasePtypeXcode(@RequestBody QueryXcodeParameter parameter) throws Exception {
        return service.getPtypeXcode(CurrentUser.getProfileId(),parameter);
    }
    @ApiOperation(value = "获取商品的单位信息")
    @PostMapping("/getPtypeUnits")
    public List<UnitDTO> getPtypeUnits(@RequestBody BigInteger ptypeId) {
        return service.getPtypeUnits(CurrentUser.getProfileId(), ptypeId);
    }
    @ApiOperation(value = "获取明细MD5")
    @PostMapping("/getDetailsMd5")
    public String getDetailsMd5(@RequestBody List<BillDeliverDetailDTO> billDeliverDetailDTOs) {
       return  deliverDetailsCommonService.getDetailsMd5(billDeliverDetailDTOs);
    }

    @ApiOperation(value = "获取明细MD5")
    @PostMapping("/getDetailsMd5Str")
    public String getDetailsMd5(@RequestBody BigInteger warehouseTaskId) {
        return  deliverDetailsCommonService.getDetailsMd5Str(CurrentUser.getProfileId(),warehouseTaskId);
    }

    @ApiOperation(value = "获取主表MD5")
    @PostMapping("/getTaskMd5Str")
    public String getTaskMd5(@RequestBody BigInteger taskId) {
        return  deliverDetailsCommonService.getBillMd5Str(CurrentUser.getProfileId(), taskId);
    }

    @ApiOperation(value = "解密 decrypt")
    @PostMapping("/decrypt")
    public BaseResponse<SecretInfo> decrypt(@RequestBody SecretInfo secretInfo) {
        try {
            return CommonResponse.success(service.decrypt(CurrentUser.getProfileId(), secretInfo));
        } catch (Exception e) {
            return CommonResponse.fail(String.format("解密失败，原因：【%s】", StringUtils.isEmpty(e.getMessage())? "未知原因" : e.getMessage()));
        }
    }

    @ApiOperation(value = "解密 decrypt sender")
    @PostMapping("/decryptSender")
    public BaseResponse<SecretInfo> decryptSender(@RequestBody SecretInfo secretInfo) {
        try {
            return CommonResponse.success(service.decryptSender(CurrentUser.getProfileId(), secretInfo));
        } catch (Exception e) {
            return CommonResponse.fail(String.format("解密失败，原因：【%s】", StringUtils.isEmpty(e.getMessage())? "未知原因" : e.getMessage()));
        }
    }
}
