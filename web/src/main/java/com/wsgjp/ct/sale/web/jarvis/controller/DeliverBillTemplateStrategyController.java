package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.printbatch.BillDeliverTemplateStrategyDTO;
import com.wsgjp.ct.sale.biz.jarvis.service.printbatch.DeliverBillTemplateStrategyService;
import com.wsgjp.ct.sale.biz.jarvis.service.printbatch.PrintBatchExecuterService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyType;
import com.wsgjp.ct.support.context.CurrentUser;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;

/**
 * @Author: wangkai
 * @Date: 2020-04-07 19:14
 */

@RestController
@RequestMapping("/${app.id}/jarvis/TemplateStrategy")
public class DeliverBillTemplateStrategyController {

    private DeliverBillTemplateStrategyService deliverBillTemplateStrategyService;
    private PrintBatchExecuterService printBatchExecuterService;

    public DeliverBillTemplateStrategyController(DeliverBillTemplateStrategyService deliverBillTemplateStrategyService,
                                                 PrintBatchExecuterService printBatchExecuterService) {
        this.deliverBillTemplateStrategyService = deliverBillTemplateStrategyService;
        this.printBatchExecuterService = printBatchExecuterService;
    }

    @PostMapping("list")
    public List<BillDeliverTemplateStrategyDTO> getTemplateStrategy(@RequestParam("strategyName") String strategyName) {
        List<BillDeliverTemplateStrategyDTO> strategys = this.deliverBillTemplateStrategyService.getList(CurrentUser.getProfileId(), StrategyType.DELIVER, strategyName);
        return strategys;
    }

    @PostMapping("getConfig/{id}")
    public BillDeliverTemplateStrategyDTO getOne(@PathVariable("id") BigInteger id) {
        BillDeliverTemplateStrategyDTO strategy = this.deliverBillTemplateStrategyService.getById(CurrentUser.getProfileId(), id);
        return strategy;
    }

//    @ApiOperation(value = "匹配发货单模板")
//    @PostMapping("matchDeliverTemplate")
//    public void MatchDeliverTemplate(@RequestBody ProcessRequest request) {
//        RedisProcessMessage processMessage = new RedisProcessMessage(request.getProcessId());
//        try {
//            List<StrategyProcessLog> logs = this.printBatchExecuterService.MatchDeliverTemplate(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), request.getVchcodes(), processMessage);
//            StrategyUtils.cacheProcessMessage(request.getProcessId(), logs);
//        } finally {
//            processMessage.setFinish();
//        }
//    }
}
