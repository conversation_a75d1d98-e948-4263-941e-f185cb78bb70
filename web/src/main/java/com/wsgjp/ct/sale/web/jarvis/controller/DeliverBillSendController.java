package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.bill.core.handle.entity.enums.SelfDeliveryModeEnum;
import com.wsgjp.ct.bill.core.handle.entity.enums.Vchtypes;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.jarvis.common.HashMapCreater;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.AfterSendDeliverBillRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.SendRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.RebuildMessageVo;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Stock;
import com.wsgjp.ct.sale.biz.jarvis.mapper.BillDeliverDetailMapper;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverSendService;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.biz.jarvis.service.strategy.DeliverBillStrategyService;
import com.wsgjp.ct.sale.biz.jarvis.service.template.TemplateService;
import com.wsgjp.ct.sale.biz.jarvis.state.FreightSyncStateEnum;
import com.wsgjp.ct.sale.biz.jarvis.state.OrderTypeEnum;
import com.wsgjp.ct.sale.biz.jarvis.state.RefundStateEnum;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.NeedProcessMsgBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessMessageMemory;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessResponseAndResult;
import com.wsgjp.ct.sale.common.enums.TradeStateEnum;
import com.wsgjp.ct.sale.platform.entity.response.sendgoods.SyncFreightBillNoResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@RestController
@Api(description = "扫描发货")
@RequestMapping("/${app.id}/jarvis/sendBill")
public class DeliverBillSendController {
    private DeliverService deliverService;
    private BillDeliverSendService billDeliverSendService;
    private BaseInfoService baseInfoService;
    private TemplateService templateService;
    private BillDeliverDetailMapper billDeliverDetailMapper;
    private DeliverBillStrategyService strategyService;

    private final static String className = "com.wsgjp.ct.sale.biz.jarvis.config.SendDeliverBillConfig";

    public DeliverBillSendController(DeliverService deliverService,
                                     BillDeliverSendService billDeliverSendService,
                                     BaseInfoService baseInfoService,
                                     TemplateService templateService,
                                     BillDeliverDetailMapper billDeliverDetailMapper,
                                     DeliverBillStrategyService strategyService) {
        this.deliverService = deliverService;
        this.billDeliverSendService = billDeliverSendService;
        this.baseInfoService = baseInfoService;
        this.templateService = templateService;
        this.billDeliverDetailMapper = billDeliverDetailMapper;
        this.strategyService = strategyService;
    }

    @ApiOperation(value = "获取基本信息")
    @PostMapping("init")
    public HashMap init() throws Exception {
        HashMap result = HashMapCreater.create(13);
        result.put("employees", this.baseInfoService.getETypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId()));
        result.put("ktypes", this.baseInfoService.getKTypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId()));
        result.put("freightTemplates", this.templateService.getTemplatesByNotStop(CurrentUser.getProfileId()));
        result.put("freights", this.baseInfoService.getFregihtList(CurrentUser.getProfileId()));
        result.put("eshops", baseInfoService.getEshopOrganizationsLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId()));
        result.put("syncState", baseInfoService.getDeliverBillStatus(FreightSyncStateEnum.class));
        result.put("orderType", baseInfoService.getDeliverBillStatus(OrderTypeEnum.class));
        result.put("billType", baseInfoService.getCommonType(Vchtypes.class));
        result.put("tradeState", baseInfoService.getDeliverBillStatus(TradeStateEnum.class));
        result.put("refundState", baseInfoService.getDeliverBillStatus(RefundStateEnum.class));
        result.put("selfDeliveryMode", baseInfoService.getDeliverBillStatus(SelfDeliveryModeEnum.class));
        result.put("currentEmployee", CurrentUser.getEmployeeId());
        result.put("processStatus", Collections.emptyList());
        return result;
    }

//    @ApiOperation(value = "根据Vchcodes获取待发货订单")
//    @PostMapping("getBillDeliverByVchcodes")
//    public List<BillDeliverDTO> getBillDeliverByVchcodes(@RequestBody SendRequest request) {
//        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getVchcodes())) {
//            return null;
//        }
//        return this.deliverService. getBillDeliverByVchcodes(CurrentUser.getProfileId(), request.getVchcodes(),false);
//    }

    @ApiOperation(value = "获取订单信息")
    @PostMapping("getDeliverByVchcode")
    public BillDeliverDTO getDeliverByVchcode(BigInteger vchcode) throws Exception {
        BillDeliverDTO bill = this.deliverService.getBillDeliverByVchcode(CurrentUser.getProfileId(), vchcode);//TODO-优化 这里是扫描发货的功能，已经搬移到wms项目了，可以移除

        if (!PermissionValiateService.isAdmin() && PermissionValiateService.isKtypeLimited()) {
            List<Stock> stocks = baseInfoService.getKTypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            if (!stocks.stream().anyMatch(k -> k.getId().equals(bill.getKtypeId()))) {
                throw new RuntimeException("您没有该批单据的仓库权限！");
            }
        }
        if (!PermissionValiateService.isAdmin() && PermissionValiateService.isOtypeLimited()) {
            List<Organization> eshops = baseInfoService.getEshopOrganizationsLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            if (!eshops.stream().anyMatch(e -> e.getId().equals(bill.getOtypeId()))) {
                throw new RuntimeException("您没有该单据的销售机构权限！");
            }
        }
        return bill;
    }

    private void CheckLimit(String limitName) {
        if (!PermissionValiateService.isAdmin() && !PermissionValiateService.validate(PermissionSysConst.DELIVER_COMMON_SEND)) {
            throw new RuntimeException("您没有该操作的权限！");
        }
    }


    @ApiOperation(value = "本地发货")
    @PostMapping("send")
    @PermissionCheck(key = PermissionSysConst.DELIVER_COMMON_SEND)
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.defaultProcessName)
    public ProcessResponseAndResult<StrategyProcessLog,List<RebuildMessageVo>> sendByBatch(@RequestBody SendRequest request) throws Exception {
        RedisProcessMessage processMessage= new ProcessMessageMemory(request,request.getProcessId());
        billDeliverSendService.sendByBatchBatchAdd(CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), request,processMessage);
        return ProcessResponseAndResult.result(StrategyProcessLog.class,RebuildMessageVo.class,processMessage);
    }

//    @ApiOperation(value = "简化流程系统发货后，标记订单的拣货状态为已拣货")
//    @PostMapping("updateSimpleProcessSendAssignState")
//    public void updateSimpleProcessSendAssignState(@RequestBody SendRequest request) {
//        deliverService.updateSimpleProcessSendAssignState(CurrentUser.getProfileId(), request.getVchcodes());
//    }


    @ApiOperation(value = "网店订单发货后通知业务处理")
    @PostMapping("afterSend")
    public void afterSend(@RequestBody AfterSendDeliverBillRequest request){
        billDeliverSendService.afterSend(request);
    }

    @ApiOperation(value = "网店订单同步线上后发货通知接口")
    @PostMapping("afterSendOnline")
    public void afterSendOnline(@RequestBody List<SyncFreightBillNoResponse> request){
        billDeliverSendService.syncAfter(request);
    }
}
