package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.service.PostpositionService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-20 9:07
 */
@RestController
@Api(description = "")
@RequestMapping("/${app.id}/jarvis/postposition")
public class PostpositionController {
    private PostpositionService postpositionService;

    public PostpositionController(PostpositionService postpositionService) {
        this.postpositionService = postpositionService;
    }

    @ApiOperation(value = "后置发货", notes = "后置发货")
    @PostMapping("/sendBill")
    public void sendBill(@RequestBody List<BigInteger> vchcodes) {

        try {
             postpositionService.sendBill(CurrentUser.getProfileId(),vchcodes);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("自动发货出错");
        }

    }
    @ApiOperation(value = "打印发货单", notes = "打印发货单")
    @PostMapping("/getPrintBillSendInfo")
    public void getPrintBillSendInfo(@RequestBody List<BigInteger> vchcodes) {

        try {
            postpositionService.getPrintBillSendInfo(CurrentUser.getProfileId(),vchcodes);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("打印发货单出错");
        }

    }
    @ApiOperation(value = "打印物流单", notes = "打印物流单")
    @PostMapping("/getPrintFreightInfo")
    public void getPrintFreightInfo(@RequestBody List<BigInteger> vchcodes) {

        try {
            postpositionService.getPrintFreightInfo(CurrentUser.getProfileId(),vchcodes);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("打印发货单出错");
        }

    }
    @ApiOperation(value = "打印商品条码", notes = "打印商品条码")
    @PostMapping("/getPrintPtypeBarcodeInfo")
    public void getPrintPtypeBarcodeInfo(@RequestBody List<BigInteger> vchcodes) {

        try {
            postpositionService.getPrintPtypeBarcodeInfo(CurrentUser.getProfileId(),vchcodes);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("打印发货单出错");
        }

    }
    @ApiOperation(value = "打印商品吊牌", notes = "打印商品吊牌")
    @PostMapping("/getPrintPtypeTagInfo")
    public void getPrintPtypeTagInfo(@RequestBody List<BigInteger> vchcodes) {

        try {
            postpositionService.getPrintPtypeTagInfo(CurrentUser.getProfileId(),vchcodes);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("打印发货单出错");
        }

    }
}
