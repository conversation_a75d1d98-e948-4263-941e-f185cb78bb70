package com.wsgjp.ct.sale.web.shopsale;

import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BarcodeScaleConfig;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BarcodeScalePtype;
import com.wsgjp.ct.sale.biz.shopsale.service.BarcodeScaleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.util.List;

@Api(tags = {"门店条码秤"})
@RestController
@RequestMapping("${app.id}/shopsale/barcodeScale")
public class BarcodeScaleController {

    @Autowired
    private BarcodeScaleService service;

    @ApiOperation("获取条码秤配置")
    @PostMapping("/getBarcodeScaleConfig")
    @WebLogs
    public BarcodeScaleConfig getBarcodeScaleConfig(@RequestBody BigInteger otypeId) {
        return service.getBarcodeScaleConfig(otypeId);
    }

    @ApiOperation("保存条码秤配置")
    @PostMapping("/saveBarcodeScaleConfig")
    @WebLogs
    public void saveBarcodeScaleConfig(@RequestBody BarcodeScaleConfig request) throws Exception {
        service.saveBarcodeScaleConfig(request);
    }

    @ApiOperation("获取条码秤商品")
    @PostMapping("/getBarcodeScalePtype")
    @WebLogs
    public List<BarcodeScalePtype> getBarcodeScalePtype(@RequestBody BigInteger otypeId) {
        return service.getBarcodeScalePtype(otypeId);
    }

    @ApiOperation("保存条码秤商品")
    @PostMapping("/saveBarcodeScalePtype")
    @WebLogs
    public void saveBarcodeScalePtype(@RequestBody List<BarcodeScalePtype> request) throws Exception {
        service.saveBarcodeScalePtype(request);
    }

    @ApiOperation("导出条码秤商品")
    @PostMapping("/exportBarcodeScalePtype")
    public void exportBarcodeScalePtype(HttpServletResponse response, @RequestBody List<BarcodeScalePtype> request) throws Exception {
        service.exportBarcodeScalePtype(response, request);
    }

    @ApiOperation("下载条码秤商品导入模板")
    @PostMapping("/downLoadBarcodeScalePtypeTemplate")
    public void downLoadBarcodeScalePtypeTemplate(HttpServletResponse response) throws Exception {
        service.downLoadBarcodeScalePtypeTemplate(response);
    }

    @ApiOperation("下载条码秤商品导入模板")
    @PostMapping("/importBarcodeScalePtypeByExcel")
    public String importBarcodeScalePtypeByExcel(MultipartFile file, Boolean pluCheck) {
        return service.importBarcodeScalePtypeByExcel(file, pluCheck);
    }

    @ApiOperation(value = "导出错误结果")
    @PostMapping("doExportErrorResult")
    public void doExportErrorResult(HttpServletResponse response, @RequestBody String processkey) throws IOException, ClassNotFoundException {
        service.doExportErrorResult(response, processkey);
    }


}
