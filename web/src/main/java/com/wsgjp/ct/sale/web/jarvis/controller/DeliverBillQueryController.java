package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.annotation.PageFilterMap;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.common.util.VersionUtil;
import com.wsgjp.ct.sale.biz.jarvis.dto.query.JumpQueryResponse;
import com.wsgjp.ct.sale.biz.jarvis.dto.query.QueryBillResponse;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.*;
import com.wsgjp.ct.sale.biz.jarvis.service.WarehouseTaskQueryService;
import com.wsgjp.ct.sale.monitor.JarvisMonitorBuilder;
import com.wsgjp.ct.sale.monitor.entity.MonitorKeyConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.support.MeterType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(description = "发货单主表相关查询接口")
@RequestMapping("/${app.id}/jarvis/bill/query")
public class DeliverBillQueryController {

    @Autowired
    private WarehouseTaskQueryService queryService;

    @ApiOperation(value = "查询发货单", notes = "（）")
    @PageFilterMap(filterMap = QueryMap.DeliverQueryMap.class)
    @PostMapping("listBill")
    public PageResponse<QueryBillResponse> listBill(@RequestBody PageRequest<BillDeliverQueryParamsBatchAd> request) {
        JarvisMonitorBuilder.NgpResource ngpResource
                = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_QUERY_DELIVER, String.format("type,%s", request.getQueryParams().isQueryColdBill()), MeterType.Gauge, 1);
        ngpResource.start();
        try {
            //执行查询
            PageResponse<QueryBillResponse> bills = VersionUtil.isNewVersion() ? queryService.listBillBatchAd(request) : queryService.listBill(request);
            ngpResource.end(false);
            return bills;
        } catch (Exception exception) {
            ngpResource.end(true);
            throw exception;
        }
    }

    @ApiOperation(value = "查询订单-交易单", notes = "（）")
    @PostMapping("findBill")
    public PageResponse<QueryBillResponse> findBill(@RequestBody PageRequest<BillDeliverQueryParams> request) {

        //执行查询
        PageResponse<QueryBillResponse> bills = queryService.findBill(request);

        //返回结果
        return bills;
    }

    @ApiOperation(value = "同步单号-交易单", notes = "（）")
    @PostMapping("findSyncBill")
    @PageFilterMap(filterMap = QueryMap.FindSyncBillMap.class)
    public PageResponse<QueryBillResponse> findSyncBill(@RequestBody PageRequest<BillDeliverQueryParams> request) {
        //执行查询
        PageResponse<QueryBillResponse> bills = queryService.findSyncBill(request);
        //返回结果
        return bills;
    }



    @PostMapping("getPageParamByJumpParam")
    public JumpQueryResponse getPageParamByJumpParam(@RequestBody JumpQueryParams request) {
        return queryService.getPageParamByJumpParam(request);
    }

    @ApiOperation(value = "查询单张发货单详情，包括明细", notes = "（）")
    @PostMapping("getTask")
    public QueryBillResponse getTask(@RequestBody TaskQueryParams request) {
        return queryService.getTask(request.getTaskId(), request.isPost());
    }
}
