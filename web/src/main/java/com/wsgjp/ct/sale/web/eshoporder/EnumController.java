package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.config.EnumConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EnumState;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EnumStateService;
import com.wsgjp.ct.sale.platform.feature.EshopFeature;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.CheckPlatformSupportParameter;
import com.wsgjp.ct.support.global.GlobalConfig;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.StringTokenizer;

/**
 * <AUTHOR>
 * @date 2020-02-13
 */
@Api(tags = "枚举相关接口")
@RequestMapping("${app.id}/eshoporder/enum")
@RestController
public class EnumController {

    @GetMapping("/getEnumState/{key}")
    public List<EnumState> getEnumState(@PathVariable String key) {
        return EnumStateService.getEnumState(key);
    }

    @GetMapping("/getConfig")
    public EnumConfig getConfig() {
        return GlobalConfig.get(EnumConfig.class);
    }

    @GetMapping("/getEnumStateMap/{key}")
    public HashMap getEnumStateMap(@PathVariable String key) {
        StringTokenizer stringTokenizer = new StringTokenizer(key, ",");
        HashMap<String, List<EnumState>> map = new HashMap<>(stringTokenizer.countTokens());
        while (stringTokenizer.hasMoreTokens()) {
            String token = stringTokenizer.nextToken();
            map.put(token, EnumStateService.getEnumState(token));
        }
        return map;
    }

    @PostMapping("/checkSupport")
    public boolean checkSupport(@RequestBody CheckPlatformSupportParameter parameter) {
//        EshopFactory factory = EshopFactoryCreator.getInstance()
//                .createByType(parameter.getShopType());
        Class<? extends EshopFeature> clazz = EnumStateService.getClazzByName(parameter.getKey());
        if (clazz == null) {
            return false;
        }
        return EshopUtils.isFeatureSupported(clazz, parameter.getShopType());
    }

    private List<String> getPifaEshop(String ocategorys) {
        List<String> arrays = new ArrayList<>();
        String[] strs = ocategorys.split(",");
        for (String str : strs) {
            arrays.add(str);
        }
        return arrays;
    }

}
