package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopAreaSaleReport;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopAreaSaleReportRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EShopAreaSaleReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: cql
 * @date: 2023/12/8
 */

@RestController
@Api(description = "区域销售统计")
@RequestMapping("${app.id}/eshoporder/areaSaleRport")
public class EShopAreaSaleReportController {
    @Autowired
    private EShopAreaSaleReportService areaSaleReportService;
    @ApiOperation(value = "查询网店区域销售统计")
    @PostMapping("/getEShopAreaSaleReport")
    @PageDataSource
    public PageResponse<EshopAreaSaleReport> getEShopAreaSaleReport (@RequestBody PageRequest<EshopAreaSaleReportRequest> esQueryParams) {
        List<EshopAreaSaleReport> eshopProductSaleReport = areaSaleReportService.getEshopAreaSaleReport(esQueryParams);
        return PageDevice.readPage( eshopProductSaleReport);
    }
}
