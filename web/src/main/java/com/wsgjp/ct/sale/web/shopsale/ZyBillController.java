package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.bill.model.zy.*;
import com.wsgjp.ct.sale.biz.bill.service.AddBillLogs;
import com.wsgjp.ct.sale.biz.bill.service.ZyBillCoreService;
import com.wsgjp.ct.sale.biz.bill.service.ZyBillService;
import com.wsgjp.ct.sale.biz.bill.utils.UserInfoUtils;
import com.wsgjp.ct.sale.biz.member.utils.PosRedisLockerUtils;
import com.wsgjp.ct.sale.biz.shopsale.common.FeignResult;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.BillCoreListRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsBillDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.OperationBillResponse;
import com.wsgjp.ct.sale.biz.shopsale.service.OrderBillService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(value = "${app.id}/shopsale/zyBill", tags = {"单据"})
@RestController
@RequestMapping("${app.id}/shopsale/zyBill")
public class ZyBillController {

    @Autowired
    private ZyBillService zyBillService;

    @Autowired
    private ZyBillCoreService zyBillCoreService;

    @Autowired
    private AddBillLogs addBillLogs;
    @Autowired
    OrderBillService billService;

    @ApiOperation(value = "自用系统单据查询")
    @PostMapping("/getSaleOrderList")
    public PageResponse getSaleOrderList(@RequestBody PageRequest<BillCoreListRequest> requestParams) {
        BillCoreListRequest queryParams = requestParams.getQueryParams();
        queryParams.commaSplitStringToList();
        UserInfoUtils.zyUserInfo(queryParams);
        return zyBillCoreService.getSaleOrderList(requestParams);
    }

    @ApiOperation(value = "批量修改支付方式")
    @PostMapping("/batchChangePayment")
    public String batchChangePayment(@RequestBody BatchChangePayment param) {
        return zyBillService.batchChangePayment(param);
    }

    @ApiOperation(value = "修改支付信息")
    @PostMapping("/changePaymentInformation")
    public void changePaymentInformation(@RequestBody PaymentInformation param) throws Exception {
        zyBillService.changePaymentInformation(param);
    }

    @ApiOperation(value = "确认支付")
    @PostMapping("/confirmPayment")
    public void confirmPayment(@RequestBody ConfirmPaymentEntity param) throws Exception {
        zyBillService.confirmPayment(param);
    }

    @ApiOperation(value = "修改分佣比例")
    @PostMapping("/changeTotal")
    public void changeTotal(@RequestBody ChangeTotalEntity param) throws Exception {
        zyBillService.changeTotal(param);
    }

    @ApiOperation(value = "修改分佣比例 - 前端用")
    @PostMapping("/changeTotalJs")
    public void changeTotalJs(@RequestBody LinkedHashMap<String, Object> request) throws Exception {
        Map goodsBill = (Map) request.get("goodsBill");
        GoodsBillDTO bill = JsonUtils.toObject(JsonUtils.toJson(goodsBill), GoodsBillDTO.class);
        zyBillService.changeTotalJs(bill);
    }

    @ApiOperation(value = "批量修改分佣比例 - 前端用")
    @PostMapping("/batchChangeTotalJs")
    public void batchChangeTotalJs(@RequestBody BatchChangeTotalJsDTO request) throws Exception {
        zyBillService.batchChangeTotalJs(request);
    }

    @ApiOperation(value = "反核算")
    @PostMapping("/cancelBill")
    public void cancelBill(@RequestBody BigInteger vchcode) {
        zyBillService.cancelBill(vchcode);
    }

    @ApiOperation(value = "验证可用额度是否足够")
    @PostMapping("/calculateAvailableCredit")
    public BigInteger calculateAvailableCredit(@RequestBody CalculateAvailableEntity entity) {
        return zyBillService.calculateAvailableCredit(entity);
    }

    @ApiOperation(value = "资金流水对账清理")
    @PostMapping("/clearFlowBillRelation")
    public void clearFlowBillRelation(@RequestBody BigInteger vchcode) {
        zyBillService.clearFlowBillRelation(vchcode, true);
    }

    @ApiOperation(value = "添加日志")
    @PostMapping("/addBillLogs")
    public void addBillLogs(@RequestBody GoodsBillDTO bill) {
        addBillLogs.addBillLogs(bill);
    }


    @PostMapping("/test1")
    public void addBillLogs1(@RequestBody BigInteger vchcode) throws Exception {
        boolean ok = PosRedisLockerUtils.setLock("test1" + CurrentUser.getProfileId(), "ok", 10);
        System.out.println(ok);
//        zyBillService.test1(vchcode);
    }

    /**
     * ！！！！慎用，自用系统用于清除同步错误的数据
     *
     * @param billStr
     * @return
     */
    @ApiOperation(value = "自用删除历史错误单据")
    @PostMapping("/zyDeleteErrorBill")
    public List<OperationBillResponse> zyDeleteErrorBill(@RequestBody String billStr) {
        return billService.zyDeleteHistoryBill(billStr);
    }

    @ApiOperation(value = "获取能够反核算的职员id")
    @PostMapping("/zyCanCancel")
    public List<String> zyCanCancel() {
        return zyBillService.zyCanCancel();
    }

    @ApiOperation(value = "代理商余额支付查看需支付金额")
    @PostMapping("/getProxyBalance")
    public FeignResult<ProxyBalance> getProxyBalance(@RequestBody BigInteger vchcode) {
        return zyBillService.getProxyBalance(vchcode);
    }

    @ApiOperation(value = "修改附加说明")
    @PostMapping("/changeMemo")
    public void changeMemo(@RequestBody ChangeMemoEntity memo) {
        zyBillService.changeMemo(memo);
    }

    @ApiOperation(value = "修改营业员")
    @PostMapping("/changeEtype")
    public void changeEtype(@RequestBody ChangeEtypeEntity etype) {
        zyBillService.changeEtype(etype);
    }

    @ApiOperation(value = "修改销售类型")
    @PostMapping("/changeCustomSaleStage")
    public void changeCustomSaleStage(@RequestBody ChangeCustomSaleStageEntity entity) {
        zyBillService.changeCustomSaleStage(entity);
    }

    @ApiOperation(value = "验证是否在确认收款中")
    @PostMapping("/checkLock")
    public void checkLock(@RequestBody BigInteger vchcode) throws Exception {
        zyBillService.checkLock(vchcode);
    }

    @ApiOperation(value = "验证是否能够删除")
    @PostMapping("/validateDelete")
    public void validateDelete(@RequestBody List<GoodsBillDTO> bills) throws Exception {
        zyBillService.validateDelete(bills);
    }

    @ApiOperation(value = "获取发票红冲审核状态")
    @PostMapping("/getRedInvoiceStatus")
    public List<RedInvoiceStatus> getRedInvoiceStatus(@RequestBody List<BigInteger> sourceInvoiceIds) {
        return zyBillService.getRedInvoiceStatus(sourceInvoiceIds);
    }
}
