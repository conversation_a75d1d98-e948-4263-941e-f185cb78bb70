package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopAuthService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.PermissionCroseServerConst;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Employee;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.WriteBackRefundStateParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.WriteBackRefundStateResponse;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopRefundBillReleationMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.EshopOrderTestService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.callback.EshopRefundBillCore;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.sendgoods.SendGoodsFreightInfo;
import com.wsgjp.ct.sale.platform.entity.request.auth.EshopState;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.SendGoodsRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.loadbalancer.context.RouteContext;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;

/**
 * <AUTHOR>
 */
@RestController
@Api(tags = {"测试API"})
@RequestMapping("${app.id}/eshoporder/test")
public class EshopOrderTestController {

    @Autowired
    private EshopOrderTestService eshopOrderTestService;
    @Autowired
    EshopRefundBillCore eshopRefundBillCore;
    @Autowired
    EshopRefundBillReleationMapper billReleationMapper;
    @Autowired
    BifrostEshopAuthService bifrostEshopAuthService;

    @GetMapping("/sendGood")
    public void sendGood() {
        SendGoodsRequest request = new SendGoodsRequest();
        SendGoodsFreightInfo freightInfo = new SendGoodsFreightInfo();
        freightInfo.setFreightBillNo("102546588745");
        freightInfo.setFreightCode("7");

        EshopSystemParams systemParams = new EshopSystemParams();
        systemParams.setProfileId(new BigInteger("468467065234153473"));
        systemParams.seteShopId(new BigInteger("4003840806254948813"));
        systemParams.setToken("be03e57a71f95c0ffdaa03f018602e3");
        systemParams.setRefreshToken("97cd3a8cc8d1ff21e0dbe695a9301ef");
        //systemParams.setShopType(ShopType.YouZan);
        Calendar now = Calendar.getInstance();
        now.setTime(new Date());
        now.set(Calendar.DATE, now.get(Calendar.DATE) + 7);
        Calendar now1 = Calendar.getInstance();
        now1.setTime(new Date());
        now1.set(Calendar.DATE, now.get(Calendar.DATE));
        systemParams.setExpiresIn(now.getTime());
        systemParams.setReExpiresIn(now1.getTime());
//        EshopFactory factory = EshopFactoryCreator.getInstance()
//                .setEshopSystemParams(systemParams)
//                .create();
//        EshopOrder eshopOrder = factory.createEshopOrder();
    }

    @GetMapping("/get")
    public int get() {
        return eshopOrderTestService.select();
    }

    @GetMapping("/getCheckEmployee")
    @ApiOperation("注解权限验证")
    @PermissionCheck(key = PermissionSysConst.ESHOP_SALE_VIEW)
    public Employee getCheckEmployee() {
        return eshopOrderTestService.getEmployee();
    }

    @GetMapping("/getEmployee")
    @ApiOperation("获取职员无权限验证")
    public Employee getEmployee() {
        return eshopOrderTestService.getEmployee();
    }

    @GetMapping("/getCrossServerEmployee")
    @ApiOperation("跨服注解权限验证")
    @PermissionCheck(crossServerKey = PermissionCroseServerConst.PERMISSION_CROSE_PRODUCT)
    public Employee getCrossServerEmployee() {
        return eshopOrderTestService.getEmployee();
    }

    @GetMapping("/checkPermission")
    @ApiOperation("权限验证")
    public String checkPermission() {
        String message = "允许访问";
        PermissionValiateService.checkCrossServer(PermissionSysConst.ESHOP_SALE_VIEW);
        return message;
    }

    @GetMapping("/getRoute")
    @ApiOperation("获取路由信息")
    public String getRoute() {
        RouteContext context = RouteThreadLocal.getRoute();
        return String.format("profileId:%s,employeeId:%s", context.getProfileId(), context.getEmployeeId());
    }


    @GetMapping("/auth")
    @ApiOperation("测试授权")
    public void test(HttpServletRequest request, HttpServletResponse resp) throws Exception {
        String shopType = request.getParameter("shoptype");
        ShopType shopTypeEnum = ShopType.TaoBao;
        if (StringUtils.isNumeric(shopType)) {
            shopTypeEnum = ShopType.valueOf(Integer.valueOf(shopType));
        }
        EshopSystemParams params = new EshopSystemParams();
        RouteContext context = RouteThreadLocal.getRoute();
        params.setShopType(shopTypeEnum);
        params.setProfileId(new BigInteger("111"));
        BigInteger eshopId = new BigInteger("1111");
        params.seteShopId(eshopId);
//        EshopFactory factory = EshopFactoryCreator.getInstance()
//                .setEshopSystemParams(params)
//                .create();
//        EshopAuth eshopAuth = factory.createEshopAuth();
        EshopState state = new EshopState();
        state.setProductId(RouteThreadLocal.getRoute().getProductId());
        String authUrl = bifrostEshopAuthService.getAuthUrl(state);
        resp.sendRedirect(authUrl);
    }

    @GetMapping("/testWriteBackRefundState")
    @ApiOperation("123")
    public void test2() {
        WriteBackRefundStateParameter parameter = new WriteBackRefundStateParameter();
        parameter.setBillType(2);
        parameter.setProfileId(new BigInteger("889563187404378113"));
        parameter.setSourceVchcodes(Collections.singletonList(new BigInteger("1149565211003745060")));
        WriteBackRefundStateResponse response = eshopRefundBillCore.writeBackRefundState(parameter);
    }

}
