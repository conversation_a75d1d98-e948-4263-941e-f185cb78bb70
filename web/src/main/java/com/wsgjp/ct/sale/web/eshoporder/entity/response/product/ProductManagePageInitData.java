package com.wsgjp.ct.sale.web.eshoporder.entity.response.product;

import com.wsgjp.ct.sale.biz.eshoporder.entity.response.OperateType;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sis.server.dao.enums.ShopType;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR> 2025/6/9 14:57
 */
public class ProductManagePageInitData {
    private List<BigInteger> selectedEshopIds;
    private List<EshopInfo> eshopInfoList;
    private List<OperateType> productOperateLogTypes;
    private List<Integer> supportSyncToSonShopTypes;

    public List<BigInteger> getSelectedEshopIds() {
        return selectedEshopIds;
    }

    public void setSelectedEshopIds(List<BigInteger> selectedEshopIds) {
        this.selectedEshopIds = selectedEshopIds;
    }

    public List<EshopInfo> getEshopInfoList() {
        return eshopInfoList;
    }

    public void setEshopInfoList(List<EshopInfo> eshopInfoList) {
        this.eshopInfoList = eshopInfoList;
    }
    public List<Integer> getSupportSyncToSonShopTypes() {
        return supportSyncToSonShopTypes;
    }

    public void setSupportSyncToSonShopTypes(List<Integer> supportSyncToSonShopTypes) {
        this.supportSyncToSonShopTypes = supportSyncToSonShopTypes;
    }

    public void setProductOperateLogTypes(List<OperateType> productOperateLogTypes) {
        this.productOperateLogTypes = productOperateLogTypes;
    }

    public List<OperateType> getProductOperateLogTypes() {
        return productOperateLogTypes;
    }
}
