package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.BatchSaveBtypeMappingRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryBtypeMappingRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.SaveBtypeMappingRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.standardApi.platformBtype.PlatformBtypeMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.basic.SaveBtypeMappingResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.PlatformBtypeService;
import io.swagger.annotations.Api;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/14 0014 13:27
 */
@Api(tags = "平台业务组基础信息接口")
@RequestMapping("${app.id}/eshoporder/basic")
@RestController
public class BasicInfoController {

    private final PlatformBtypeService service;
    private final EshopOrderBaseInfoService baseInfoService;

    public BasicInfoController(PlatformBtypeService service, EshopOrderBaseInfoService baseInfoService) {
        this.service = service;
        this.baseInfoService = baseInfoService;
    }

    @PostMapping("queryPlatformBtypeMapping")
    public PageResponse<PlatformBtypeMapping> queryPlatformBtypeMapping(@RequestBody PageRequest<QueryBtypeMappingRequest> request){
        QueryBtypeMappingRequest queryParams = request.getQueryParams();
        if(queryParams.isQueryByOrder()){
            return service.queryPlatformBtypeByOrder(request);
        }
        return service.queryPlatformBtypeList(request);
    }

    @PostMapping("batchBindBtypeMapping")
    public SaveBtypeMappingResponse doBatchBindBtypeMapping(@RequestBody SaveBtypeMappingRequest request){
        SaveBtypeMappingResponse response=new SaveBtypeMappingResponse();
        List<PlatformBtypeMapping> mappingList = request.getMappingList();
        if (CollectionUtils.isEmpty(mappingList)){
            return response;
        }
        for (PlatformBtypeMapping mapping : mappingList) {
            String error = service.doSaveBtypeMapping(mapping, request.getQueryParams());
            if(StringUtils.isNotEmpty(error)){
                response.setSuccess(false);
            }
            try {
                service.doModifyCurrentOrder(mapping,request.getQueryParams());
                response.setSuccess(true);
            }catch (Exception ex){
                response.setSuccess(false);
            }
        }
        return response;
    }

    @PostMapping("bindBtypeMapping")
    public SaveBtypeMappingResponse doBindBtypeMapping(@RequestBody SaveBtypeMappingRequest request){
        SaveBtypeMappingResponse response=new SaveBtypeMappingResponse();
        PlatformBtypeMapping mapping = request.getMapping();
        StringBuilder msg = new StringBuilder();
        String error = service.doSaveBtypeMapping(mapping, request.getQueryParams());
        if(StringUtils.isNotEmpty(error)){
            response.setSuccess(false);
            msg.append("回告平台供应商信息错误，");
        }
        try {
            service.doModifyCurrentOrder(mapping,request.getQueryParams());
            response.setSuccess(true);
            if (StringUtils.isEmpty(msg)){
                msg.append("修改本地订单往来单位成功！");
            }else {
                msg.append("但修改本地订单往来单位成功！");
            }
        }catch (Exception ex){
            response.setSuccess(false);
            msg.append("修改本地订单往来单位失败");
        }
        response.setMsg(msg.toString());
        return response;
    }

    @PostMapping("addBtypeMapping")
    public SaveBtypeMappingResponse doAddBtypeMapping(@RequestBody SaveBtypeMappingRequest request){
        PlatformBtypeMapping mapping = request.getMapping();
        try {
            service.doCreateBtype(mapping);
        }catch (Exception ex){
            SaveBtypeMappingResponse response=new SaveBtypeMappingResponse();
            response.setSuccess(false);
            response.setMsg(ex.getMessage());
            return response;
        }
        SaveBtypeMappingResponse response = doBindBtypeMapping(request);
        response.setMapping(mapping);
        return response;
    }
    @PostMapping("batchAddBtypeMapping")
    public SaveBtypeMappingResponse batchAddBtypeMapping(@RequestBody BatchSaveBtypeMappingRequest request){
        List<PlatformBtypeMapping> mappings = request.getMappings();
        StringBuilder errorInfo=new StringBuilder();
        SaveBtypeMappingResponse response=new SaveBtypeMappingResponse();
        for (PlatformBtypeMapping mapping:mappings) {
            try {
                //循环保存往来单位
               String message= service.doCreateBtype(mapping);
               if (StringUtils.isNotEmpty(message)) {
                   errorInfo.append(message).append(";");
               } else {
                   //保存对应关系并通知订单
                   SaveBtypeMappingRequest req=new SaveBtypeMappingRequest();
                   req.setMapping(mapping);
                   QueryBtypeMappingRequest mappingRequest=new QueryBtypeMappingRequest();
                   mappingRequest.setEshopId(mapping.getEshopId());
                   mappingRequest.setQueryByOrder(false);
                   req.setQueryParams(mappingRequest);
                   SaveBtypeMappingResponse mappingResponse = doBindBtypeMapping(req);
                   if (!mappingResponse.getSuccess()) {
                       errorInfo.append(mappingResponse.getMsg());
                   }
               }
            }catch (Exception ex){
                errorInfo.append(ex.getMessage());
            }
        }
        response.setSuccess(StringUtils.isEmpty(errorInfo.toString()));
        response.setMsg(errorInfo.toString());
        return response;
    }

    @PostMapping("batchClearBtypeMapping")
    public void doClearBtypeMapping(@RequestBody List<PlatformBtypeMapping> mappings){
        service.doBatchClearBtypeMapping(mappings);
    }

    @PostMapping("ktypeList")
    public List<Stock> queryKtypeList(){
        return baseInfoService.getStockLimited();
    }
}
