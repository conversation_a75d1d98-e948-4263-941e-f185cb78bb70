package com.wsgjp.ct.sale.web.jarvis.response.sensitive;

import com.wsgjp.ct.sis.client.common.SensitiveFieldEnum;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class DecryptFieldResponse {
    private BigInteger shopId;
    private BigInteger orderId;
    private String di;
    private SensitiveFieldEnum field;
    private String value;
    private String requestUrl;
    private String requestBody;

    public BigInteger getShopId() {
        return shopId;
    }

    public void setShopId(BigInteger shopId) {
        this.shopId = shopId;
    }

    public BigInteger getOrderId() {
        return orderId;
    }

    public void setOrderId(BigInteger orderId) {
        this.orderId = orderId;
    }

    public String getDi() {
        return di;
    }

    public void setDi(String di) {
        this.di = di;
    }

    public SensitiveFieldEnum getField() {
        return field;
    }

    public void setField(SensitiveFieldEnum field) {
        this.field = field;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }
}
