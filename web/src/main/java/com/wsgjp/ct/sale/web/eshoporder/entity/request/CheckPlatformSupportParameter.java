package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BaseQuery;

/**
 * <AUTHOR>
 * @date 22/5/2020 上午 11:22
 */
public class CheckPlatformSupportParameter extends BaseQuery {

	private ShopType shopType;
	private String key;

	public ShopType getShopType() {
		return shopType;
	}

	public void setShopType(ShopType shopType) {
		this.shopType = shopType;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}
}
