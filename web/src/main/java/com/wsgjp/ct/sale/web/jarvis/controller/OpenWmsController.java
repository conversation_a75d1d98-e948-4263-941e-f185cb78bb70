package com.wsgjp.ct.sale.web.jarvis.controller;


import com.wsgjp.ct.sale.biz.api.response.openwms.WmsApiResult;
import com.wsgjp.ct.sale.biz.jarvis.entity.wms.ReturnDeliverBillRequest;
import com.wsgjp.ct.sale.biz.jarvis.service.OpenWmsService;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/${app.id}/jarvis/openwms")
public class OpenWmsController {
    private final OpenWmsService openWmsService;

    public OpenWmsController(@Lazy OpenWmsService openWmsService) {
        this.openWmsService = openWmsService;
    }

    /*@PostMapping("confirm")
    public WmsApiResult confirm(@RequestBody BillConfirmDto confirmDto) {
        return openWmsService.confirm(confirmDto);
    }

    @PostMapping("freightInfoSave")
    public WmsApiResult freightInfoSave(@RequestBody FreightInfoSaveDto freightInfoSave) {
        return openWmsService.freightInfoSave(freightInfoSave);
    }

    @PostMapping("/reject")
    public List<WmsApiResult> reject(@RequestBody List<ReturnDeliverBillRequest> requests) {
        return openWmsService.reject(requests);
    }

    @PostMapping("modifyDetails")
    public void modifyDetails(@RequestBody DetailModifyDto detailModify) throws Exception {
        openWmsService.modifyDetails(detailModify);
    }*/

    @PostMapping("/reject")
    public List<WmsApiResult> reject(@RequestBody List<ReturnDeliverBillRequest> requests) {
        return openWmsService.reject(requests);
    }
}

