package com.wsgjp.ct.sale.web.member;

import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.model.dto.card.GetCardRequest;
import com.wsgjp.ct.sale.biz.member.model.vo.card.VipGetCard;
import com.wsgjp.ct.sale.biz.member.service.ISsCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

@Api(tags = "卡券管理")
@RestController
@RequestMapping("${app.id}/member/card")
public class CardController {

    @Autowired
    private ISsCardService cardService;


    /**
     * 获取卡券信息（使用范围筛选）
     */
    @ApiOperation(value = "通过会员id获取该会员已获取的卡券")
    @PostMapping(value = "/getCardByVipId")
    @WebLogs
    List<VipGetCard> getCardByVipId(@RequestBody GetCardRequest request) {
        return cardService.getCardByVipId(request);
    }

    @ApiOperation(value = "通过会员id获取该会员的权益卡列表 用于解绑页面")
    @PostMapping(value = "/getRightCardWithVipId")
    PageInfo<VipGetCard> getRightCardWithVipId(@RequestBody GetCardRequest request) {
        return cardService.getRightCardWithVipId(request);
    }

    /**
     * 正常会员使用优惠券是通过资产变动接口实现，且同时会变动多种信息
     * 非会员无法调用资产变动接口，所以开通专门的非会员使用优惠券接口
     * 会员名下的优惠券不可调用，这个接口不记录会员资产变动
     *
     * @param request  使用的优惠券  key是id，value是数量 正数是使用 负数是恢复
     */
    @ApiOperation(value = "使用优惠券-非会员")
    @PostMapping(value = "/useCard")
    public void useCard(@RequestBody Map<BigInteger, Integer> request) {
        cardService.useCard(request, true, null);
    }

}
