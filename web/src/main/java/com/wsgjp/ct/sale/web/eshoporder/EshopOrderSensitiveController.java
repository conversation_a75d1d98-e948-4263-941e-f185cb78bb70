package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.profile.ProfileApi;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopAuthService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.bifrost.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryOrderParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.EShopPageInfo;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.OperationEnum;
import com.wsgjp.ct.sale.common.log.PlatformBizKeyPointFeedback;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.buyer.DecryptOrderRequest;
import com.wsgjp.ct.sale.platform.entity.response.decrypt.DecryptResponse;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.factory.middlegroundfxs.sdk.request.DecryptMiddleGroundOrderRequest;
import com.wsgjp.ct.sale.platform.factory.middlegroundfxs.sdk.response.DecryptMiddleGroundOrderResponse;
import com.wsgjp.ct.sis.client.SisClient;
import com.wsgjp.ct.sis.client.common.EncrypAndDecrypSenceEnum;
import com.wsgjp.ct.sis.client.common.SensitiveFieldEnum;
import com.wsgjp.ct.sis.client.entity.*;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequestMapping("/${app.id}/eshoporder/sensitive")
@RestController
@Api(value = "敏感信息解密接口")
public class EshopOrderSensitiveController {
    private final PlatformBizKeyPointFeedback platformBizKeyPointFeedback;
    private final ProfileApi profileApi;
    private final BifrostEshopAuthService bifrostEshopAuthService;
    private final EshopService eshopService;
    private final EshopSaleOrderService eshopSaleOrderService;
    private final ServiceConfig serviceConfig;
    private final BifrostEshopOrderService eshopOrderService;

    public EshopOrderSensitiveController(PlatformBizKeyPointFeedback platformBizKeyPointFeedback, ProfileApi profileApi, BifrostEshopAuthService bifrostEshopAuthService, EshopService eshopService, EshopSaleOrderService eshopSaleOrderService, ServiceConfig serviceConfig, BifrostEshopOrderService eshopOrderService) {
        this.platformBizKeyPointFeedback = platformBizKeyPointFeedback;
        this.profileApi = profileApi;
        this.bifrostEshopAuthService = bifrostEshopAuthService;
        this.eshopService = eshopService;
        this.eshopSaleOrderService = eshopSaleOrderService;
        this.serviceConfig = serviceConfig;
        this.eshopOrderService = eshopOrderService;
    }


    @PostMapping("batchDecrypt")
    @ApiOperation(value = "批量解密接口")
    public Map<BigInteger, DecryptShopResponse> batchDecrypt(@ApiParam(value = "订单列表") @RequestBody List<SensitiveOrder> request) throws Exception {
        EncrypAndDecrypSenceEnum scene = null;
        for (SensitiveOrder sensitiveOrder : request) {
            scene = sensitiveOrder.getScene();
        }
        Map<BigInteger, DecryptShopResponse> decrypt = SisClient.decrypt(request, scene);
        for (BigInteger bigInteger : decrypt.keySet()) {
            for (DecryptOrderResponse order : decrypt.get(bigInteger).getOrders()) {
                String[] split = order.getDi().split(":");
                platformBizKeyPointFeedback.feedback(bigInteger, split[split.length - 1], OperationEnum.DECRYPT);
            }
        }
        return decrypt;
    }

    @PostMapping("decryptField")
    public List<FieldDecryptResponse> decryptField(@RequestBody FieldDecryptRequest request, HttpServletRequest httpServletRequest) throws Exception {
        request.setErpHost(CurrentUser.getClientDomain());
        return SisClient.decryptFields(request);
    }

    @PostMapping("listSensitiveFields")
    public List<SensitiveFieldEnum> listSensitiveFields() {
        return Arrays.asList(SensitiveFieldEnum.values());
    }
    @PostMapping("decryptByFrontPreCheck")
    public String decryptByFrontPreCheck(@RequestBody DecryptRequest request){
        request.setProfileId(CurrentUser.getProfileId());
        String errorMsg="";
        //兼容线下订单检查，没有网店的时候可以解密
        EShopPageInfo eshop=eshopService.queryEshopPageInfoById(request.getShopId());
        if(eshop==null)
        {
            return "";
        }
        ShopType shopType=eshop.getEshopType();
        if(eshopService.ShopTypesSupport(shopType,serviceConfig.getCantDecryptAfterSendShopTypes())) {
            EshopSaleOrderEntity order = getSimpleEshopSaleOrderByFrontPreCheck(request,1);
            if (order!=null&&order.getLocalTradeState().getCode()>=3&&order.getLocalTradeState().getCode()!=7) {
                errorMsg=String.format("%s平台订单不支持%s订单的解密",shopType.getName(),order.getLocalTradeState().getName());
            } else if (order!=null&&order.getRefundState()!= RefundStatus.NONE) {
                errorMsg=String.format("%s平台订单不支持已产生%s售后单的解密",shopType.getName(),order.getRefundState().getName());
            }
        }
        if (StringUtils.isNotEmpty(errorMsg)){
            return errorMsg;
        }
        if(eshopService.ShopTypesSupport(shopType,serviceConfig.getCantDecryptByCloseOrderShopTypes())) {
            EshopSaleOrderEntity order = getSimpleEshopSaleOrderByFrontPreCheck(request,2);
            if (order!=null&&order.getLocalTradeState().getCode()==5) {
                errorMsg=String.format("%s平台订单不支持%s订单的解密",shopType.getName(),order.getLocalTradeState().getName());
            }
        }
       return errorMsg;
    }



    private EshopSaleOrderEntity getSimpleEshopSaleOrderByFrontPreCheck(DecryptRequest request, int diIndex) {
        QueryOrderParameter param=new QueryOrderParameter();
        param.setOtypeIds(Collections.singletonList(request.getShopId()));
        String di= request.getOrderList().get(0);
        String[] split = di.split(":");
        //修改买家信息之后，变为本地加密，di只有两截了
        String tradeId = split.length < diIndex+1 ? di.split(":")[split.length-1] : di.split(":")[diIndex];
        param.setTradeOrderId(tradeId);
        return eshopSaleOrderService.getSimpleOrder(param);
    }

    @PostMapping("decryptByFront")
    public DecryptOrderResponse decryptByFront(@RequestBody DecryptRequest request){
        request.setProfileId(CurrentUser.getProfileId());
        DecryptShopResponse response = SisClient.decryptByFront(request);
        List<DecryptOrderResponse> orders = response.getOrders();
        if(CollectionUtils.isEmpty(orders)){
            return null;
        }
        return orders.get(0);
    }
    @PostMapping("getDecryptToken")
    public String getDecryptToken(@RequestBody DecryptRequest request){
        BaseRequest req=new BaseRequest();
        req.setShopId(request.getShopId());
        EshopInfo eshopInfo = eshopService.getEshopInfoById(CurrentUser.getProfileId(), req.getShopId());
        if (eshopInfo==null)
        {
            throw new RuntimeException("找不到网店信息，网店id="+request.getShopId());
        }
        try
        {
            EshopSystemParams systemParams = CommonUtil.toSystemParams(eshopInfo);
            req.setShopType(eshopInfo.getEshopType());
            req.setSystemParams(systemParams);
            DecryptResponse resp=bifrostEshopAuthService.getDecryptToken(req);
            return resp.getToken();
        }
        catch (Exception ex)
        {
            throw new RuntimeException("获取解密授权信息出错:"+ex.getMessage());
        }
    }

    @PostMapping("decryptMiddleGround")
    public DecryptMiddleGroundOrderResponse decryptMiddleGround(@RequestBody DecryptMiddleGroundOrderRequest request){
        request.setProfileId(CurrentUser.getProfileId());
        if (null==request || null == request.getShopId() || BigInteger.ZERO.compareTo(request.getShopId())==0 || StringUtils.isEmpty(request.getDi())){
            throw new RuntimeException("解密参数错误");
        }
        String platformName = request.getDi().split(":")[0];
        if (StringUtils.isEmpty(platformName) || !"MIDDLEGROUNDGYS".equals(platformName)){
            throw new RuntimeException("平台类型错误");
        }
        String di = request.getDi();
        String realDi = request.getDi().substring(request.getDi().indexOf(":") + 1);
        if (StringUtils.isEmpty(realDi)){
            throw new RuntimeException("上游解密参数错误");
        }
        //调用接口返回数据
        request.setDi(realDi);
        DecryptOrderRequest decryptOrderRequest = new DecryptOrderRequest();
        decryptOrderRequest.setShopId(request.getShopId());
        decryptOrderRequest.setParams(request);
        com.wsgjp.ct.sale.platform.entity.response.order.DecryptOrderResponse response = eshopOrderService.orderDecrypt(decryptOrderRequest);
        if (null == response || null == response.getData() || !response.getSuccess()){
            throw new RuntimeException("调用上游解密接口错误 " + ( null!=response && StringUtils.isNotEmpty(response.getMessage()) ? response.getMessage() : ""));
        }
        if (response.getData().isError()){
            throw new RuntimeException(response.getData().getMessage());
        }
        //下游di回填
        DecryptMiddleGroundOrderResponse data = response.getData();
        List<DecryptOrderResponse> orders = data.getOrders();
        if (CollectionUtils.isEmpty(orders)){
            return data;
        }
        orders.forEach(o->o.setDi(di));
        return data;
    }
}
