package com.wsgjp.ct.sale.web.jarvis.controller;

/**
 * <AUTHOR>
 * @date 2020-03-04 15:01
 */

import com.wsgjp.ct.sale.biz.jarvis.dto.request.RebuildParamVo;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.RebuildResultVo;
import com.wsgjp.ct.sale.biz.jarvis.service.RebuildSystemService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(description = "重建接口")
@RequestMapping("/${app.id}/jarvis")
public class RebuildController {


    private RebuildSystemService rebuildSystemService;

    public RebuildController(RebuildSystemService rebuildSystemService) {
        this.rebuildSystemService = rebuildSystemService;
    }

    /**
     * 系统重建-网店接口
     */
    @RequestMapping(value = "/rebuildSystem", method = RequestMethod.POST)
    public RebuildResultVo rebuildSystem(@RequestBody RebuildParamVo rebuildRequestDTO) {
        RebuildResultVo res =  rebuildSystemService.rebuildSystem(rebuildRequestDTO, CurrentUser.getProfileId());
        return res;

    }
}
