package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.jarvis.common.DeliverPermissionConst;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.HandAuditRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.ProcessRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.stategy.StrategyExecuteDto;
import com.wsgjp.ct.sale.biz.jarvis.dto.stategy.StrategyExecuteRequest;
import com.wsgjp.ct.sale.biz.jarvis.entity.strategy.audit.AuditStrategyConfigEntity;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.jarvis.service.audit.DeliverBillAuditService;
import com.wsgjp.ct.sale.biz.jarvis.service.audit.impl.DeliverBillAuditServiceImpl;
import com.wsgjp.ct.sale.biz.jarvis.service.strategy.DeliverBillStrategyService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.StrategyUtils;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.SimpleStrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.RedisProcessMsgUtils;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.NeedProcessMsgBatchAd;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessMessageMemory;
import com.wsgjp.ct.sale.biz.jarvis.utils.processMsg.ProcessResponseAndResult;
import com.wsgjp.ct.sale.monitor.JarvisMonitorBuilder;
import com.wsgjp.ct.sale.monitor.entity.MonitorKeyConstant;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.support.MeterType;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-22
 **/
@RestController
@Api("订单审核")
@RequestMapping("/${app.id}/jarvis/audit")
public class DeliverBillAuditController {

    private DeliverBillAuditService deliverBillAuditService;
    private DeliverBillStrategyService strategyService;
    private final static Logger logger = LoggerFactory.getLogger(DeliverBillAuditController.class);

    public DeliverBillAuditController(DeliverBillAuditService deliverBillAuditService, DeliverBillStrategyService strategyService) {
        this.deliverBillAuditService = deliverBillAuditService;
        this.strategyService = strategyService;
    }

    @ApiOperation(value = "仓储拆分后审核", notes = "（）")
    @PostMapping("auditAfterSplitMerge")
    public List<SimpleStrategyProcessLog> auditAfterSplitMerge(@RequestBody HandAuditRequest request) {
        return deliverBillAuditService.auditAfterSplitMerge(request.getWarehouseTaskIds(), CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), new DeliverBillAuditServiceImpl.ForceAudit(request.isForce()));
    }

    @ApiOperation(value = "手动审核", notes = "（）")
    @PostMapping("handAudit")
    @PermissionCheck(key = DeliverPermissionConst.JARVIS_DELIVER_HAND_AUDIT)
    @NeedProcessMsgBatchAd(redirectUrl= "sale/jarvis/audit/handAudit",threadPoolName = NeedProcessMsg.ProcessName.handleAuditProcessName, serviceName = "HandAudit")
    public void handAudit(@RequestBody HandAuditRequest request) {
        deliverBillAuditService.handAuditForProcess(request.getWarehouseTaskIds(), CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), new DeliverBillAuditServiceImpl.ForceAudit(request.isForce()), request.getProcessId());
    }

    @ApiOperation(value = "策略审核", notes = "（）")
    @PostMapping("autoAudit")
    @PermissionCheck(key = DeliverPermissionConst.JARVIS_DELIVER_AUTO_AUDIT)
    @NeedProcessMsg(redirectUrl= "sale/jarvis/audit/autoAudit",threadPoolName = NeedProcessMsg.ProcessName.autoAuditProcessName, serviceName = "AutoAudit")
    public void autoAudit(@RequestBody ProcessRequest request) {
        deliverBillAuditService.strategyAuditForProcess(request.getWarehouseTaskIds(), CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), false, request.getProcessId());
    }

    @ApiOperation(value = "执行所有策略", notes = "（）")
    @PostMapping("doAllStrategy")
    @PermissionCheck(key = DeliverPermissionConst.JARVIS_DELIVER_AUTO_AUDIT)
    @NeedProcessMsg(redirectUrl= "sale/jarvis/audit/doAllStrategy",threadPoolName = NeedProcessMsg.ProcessName.autoAuditProcessName, serviceName = "DoAllStrategy")
    public List<BigInteger> doAllStrategy(@RequestBody ProcessRequest request) {
        return deliverBillAuditService.executeStrategyForProcess(request.getWarehouseTaskIds(), CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), false, request.getProcessId());
    }

    @GetMapping("hasActiveStrategyConfigs")
    public String hasActiveStrategyConfigs() {
        return deliverBillAuditService.hasActiveStrategyConfigs();
    }

    @ApiOperation(value = "执行策略", notes = "（）")
    @PostMapping("doStrategy")
    @PermissionCheck(key = PermissionSysConst.DELIVER_MATCH_STRATEGY)
    public List<BigInteger> doStrategy(@RequestBody StrategyExecuteRequest request) {
        RedisProcessMessage processMessage = new RedisProcessMessage(request.getProcessId());
        RedisProcessMessage.MsgLogger msgLogger = processMessage.getMsgLogger();
        if(request == null || request.getType() == null || request.getWarehouseTaskIds() == null)
        {
            msgLogger.appendMsg("执行策略请求参数错误");
            logger.error("执行策略请求参数错误："+CurrentUser.getProfileId()+" "+(request==null?"":JsonUtils.toJson(request)));
            throw new RuntimeException("执行策略请求参数错误");
        }
        JarvisMonitorBuilder.NgpResource ngpResource = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_DEALING_STRATEGY, "type," + request.getType().getName(),
                MeterType.Summary, request.getWarehouseTaskIds().size());
        List<BigInteger> warehouseTaskIds = request.getWarehouseTaskIds();
        try {
            ngpResource.start();
            List<StrategyProcessLog> results = strategyService.execute(request.getWarehouseTaskIds(), CurrentUser.getProfileId(), request.getType(), msgLogger, null);
            warehouseTaskIds = results.stream().map(r->r.getWarehouseTaskId()).distinct().collect(Collectors.toList());
            StrategyUtils.cacheProcessMessage(request.getProcessId(), results);
            ngpResource.end(false);
        } catch (Exception e) {
            ngpResource.end(true);
            throw e;
        } finally {
            processMessage.setFinish();

        }
        return warehouseTaskIds;
    }


    /**
     * 执行策略核接口，提供定制化策略方法，传啥策略执行啥策略(包括新版审核策略-江超)，按手工审核配置
     * @param request
     * @return
     */
    @PostMapping("doCustomerStrategy")
    @NeedProcessMsg(threadPoolName = NeedProcessMsg.ProcessName.strategyProcessName)
    public void doCustomerStrategy(@RequestBody StrategyExecuteDto request) {
        logger.debug(String.format("仓储调用定制策略执行接口， 传参Request: %s，profileId：%s", JsonUtils.toJson(request), CurrentUser.getProfileId()));
        RedisProcessMessage processMessage = new RedisProcessMessage(request.getProcessId());
        RedisProcessMessage.MsgLogger msgLogger = processMessage.getMsgLogger();
        try {

            List<StrategyProcessLog> results = strategyService.execute(CurrentUser.getProfileId(), request, msgLogger);
            StrategyUtils.cacheProcessMessage(request.getProcessId(), results);
        } catch (Exception e) {
            logger.error(String.format("仓储调用定制策略执行接口失败， profileId：%s", CurrentUser.getProfileId()), e);
        } finally {
            processMessage.setFinish();
        }
        logger.debug(String.format("仓储调用定制策略执行接口成功， profileId：%s", CurrentUser.getProfileId()));
    }


    @ApiOperation(value = "执行策略", notes = "（）")
    @PostMapping("doAsyncStrategy")
    @PermissionCheck(key = PermissionSysConst.DELIVER_MATCH_STRATEGY)
    @NeedProcessMsgBatchAd(threadPoolName = NeedProcessMsg.ProcessName.strategyProcessName, redirectUrl = "sale/jarvis/audit/doAsyncStrategy", serviceName = "DoStrategy")
    public ProcessResponseAndResult<StrategyProcessLog,List<BigInteger>> doAsyncStrategy(@RequestBody StrategyExecuteRequest request) {
        RedisProcessMessage processMessage = new ProcessMessageMemory(request, request.getProcessId());
        RedisProcessMessage.MsgLogger msgLogger = processMessage.getMsgLogger();
        if (request == null || request.getType() == null || request.getWarehouseTaskIds() == null) {
            msgLogger.appendMsg("执行策略请求参数错误");
            logger.error(String.format("执行策略请求参数错误：%s %s", CurrentUser.getProfileId(), request == null ? "" : JsonUtils.toJson(request)));
            RedisProcessMsgUtils.resetLoggerError(msgLogger, true);
            throw new RuntimeException("执行策略请求参数错误");
        }
        JarvisMonitorBuilder.NgpResource ngpResource = JarvisMonitorBuilder.create(MonitorKeyConstant.BIZ_DEALING_STRATEGY, "type," + request.getType().getName(),
                MeterType.Summary, request.getWarehouseTaskIds().size());
        List<BigInteger> newTaskIds = request.getWarehouseTaskIds();
        try {
            ngpResource.start();
            List<StrategyProcessLog> results = strategyService.execute(request.getWarehouseTaskIds(), CurrentUser.getProfileId(), request.getType(), msgLogger, request.getExtendConfig());
            newTaskIds = results.stream().map(r -> r.getOriginalVchcode()).distinct().collect(Collectors.toList());
            processMessage.doCacheProcessServiceResultMessage(newTaskIds);
            // 这里实际是将成功和失败的都写入，所以使用Error这个方法
            processMessage.doCacheProcessErrorMessage(results);
            ngpResource.end(false);
        } catch (Exception e) {
            msgLogger.appendMsg("策略执行失败：" + e.getMessage());
            ngpResource.end(true);
            RedisProcessMsgUtils.resetLoggerError(msgLogger, true);
        } finally {
            processMessage.setFinish();
        }
        return ProcessResponseAndResult.result(StrategyProcessLog.class, BigInteger.class, processMessage);
    }

    @ApiOperation(value = "是否开启策略审核", notes = "（）")
    @PostMapping("isOpenStrategyAudit")
    public boolean isOpenStrategyAudit() {
        AuditStrategyConfigEntity auditStrategyConfigEntity = strategyService.getAuditStrategyConfig(CurrentUser.getProfileId());
        return auditStrategyConfigEntity != null && auditStrategyConfigEntity.getStrategyStatus() && auditStrategyConfigEntity.getConfig() != null && auditStrategyConfigEntity.getConfig().isAutoAuditEnabled();
    }

}

