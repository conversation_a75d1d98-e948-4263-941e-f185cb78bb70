package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.page.PageSummary;
import com.wsgjp.ct.sale.biz.jarvis.dto.DistributionGrossProfitStatisticsDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Btype;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Employee;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Stock;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.DistributionGrossProfitStatisticsParams;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.DistributionGrossProfitStatisticsService;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Api(description = "分销毛利统计")
@RequestMapping("/${app.id}/jarvis/distributionGrossProfitStatistics")
public class DistributionGrossProfitStatisticsController {

    private BaseInfoService baseInfoService;
    private DistributionGrossProfitStatisticsService distributionGrossProfitStatisticsService;

    public DistributionGrossProfitStatisticsController(BaseInfoService baseInfoService, DistributionGrossProfitStatisticsService distributionGrossProfitStatisticsService) {
        this.baseInfoService = baseInfoService;
        this.distributionGrossProfitStatisticsService = distributionGrossProfitStatisticsService;
    }

    @ApiOperation(value = "分销毛利统计", notes = "（）")
    @PostMapping("list")
    public PageResponse<DistributionGrossProfitStatisticsDTO> queryEshopSendBills(@RequestBody PageRequest<DistributionGrossProfitStatisticsParams> request) throws Exception {
        buildQuery(request);
        //前端没有传排序，则暂不支持排序，导出也一样
        return distributionGrossProfitStatisticsService.listBillDeliverListNew(request);
    }


    @ApiOperation(value = "分销毛利统计合计", notes = "（）")
    @PostMapping("list/count")
    public PageSummary queryEshopSendBillsCount(@RequestBody PageRequest<DistributionGrossProfitStatisticsParams> request) throws Exception {
        buildQuery(request);
        //前端没有传排序，则暂不支持排序，导出也一样
        return distributionGrossProfitStatisticsService.listBillDeliverListCountNew(request);
    }

    private void buildQuery(@RequestBody PageRequest<DistributionGrossProfitStatisticsParams> request) {
        DistributionGrossProfitStatisticsParams param = request.getQueryParams();
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        if (!PermissionValiateService.isAdmin() && PermissionValiateService.isKtypeLimited()) {
            List<Stock> stocks = baseInfoService.getKTypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            List<BigInteger> stockIds = stocks.stream().map(Stock::getId).distinct().collect(Collectors.toList());
            stockIds.add(BigInteger.ZERO);
            param.setKtypeIds(stockIds);
        }
        if (param.getEshops() == null && !PermissionValiateService.isAdmin() && PermissionValiateService.isOtypeLimited()) {
            List<Organization> eshops = baseInfoService.getEshopOrganizationsLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            List<BigInteger> eshopIds = eshops.stream().map(Organization::getId).distinct().collect(Collectors.toList());
            eshopIds.add(BigInteger.ZERO);
            param.setEshops(eshopIds);
        }
        if (!PermissionValiateService.isAdmin() && PermissionValiateService.isEtypeLimited()) {
            List<Employee> employees = baseInfoService.getETypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            List<BigInteger> etypeIds = employees.stream().map(Employee::getId).distinct().collect(Collectors.toList());
            etypeIds.add(BigInteger.ZERO);
            param.setEtypeIds(etypeIds);
        }
        if ((param.getBtypes() == null || param.getBtypes().isEmpty()) && !PermissionValiateService.isAdmin() && PermissionValiateService.isBtypeLimited()) {
            List<Btype> btypes = baseInfoService.getBtypeLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            List<BigInteger> btypeIds = btypes.stream().map(Btype::getId).distinct().collect(Collectors.toList());
            btypeIds.add(BigInteger.ZERO);
            param.setBtypes(btypeIds);
        }
    }
}
