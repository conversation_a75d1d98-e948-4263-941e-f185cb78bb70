package com.wsgjp.ct.sale.web.jarvis.response;

import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.dto.gift.DeliverGiftRuleDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.gift.DeliverGiftRuleGroupDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverMark;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Employee;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GiftRuleGetResponse {
    private List<Organization> otypeList;
    private List<Employee> employeeList;
    private List<DeliverMark> markList;
    private DeliverGiftRuleDTO giftRule;
    private List<DillDeliverState> timeType;
    private List<DillDeliverState> ptypeType;
    private List<DillDeliverState> giveType;
    private List<DillDeliverState> stockType;
    private List<DillDeliverState> giveContentType;
    private List<DillDeliverState> amountType;
    private List<DillDeliverState> giveWayType;
    private List<DillDeliverState> giveWayDetailType;
    private List<DillDeliverState> sectionType;
    private List<DillDeliverState> tradeFromType;
    private List<DillDeliverState> ruleTypes;
    private List<DillDeliverState> repeatTypes;
    private List<DillDeliverState> compareTypes;
    private Date startTime;
    private Date endTime;
    private Date payStartTime;
    private Date payEndTime;
    private List<DeliverGiftRuleGroupDTO> groups;

    public List<Organization> getOtypeList() {
        return otypeList;
    }

    public void setOtypeList(List<Organization> otypeList) {
        this.otypeList = otypeList;
    }

    public DeliverGiftRuleDTO getGiftRule() {
        return giftRule;
    }

    public void setGiftRule(DeliverGiftRuleDTO giftRule) {
        this.giftRule = giftRule;
    }

    public List<DillDeliverState> getAmountType() {
        return amountType;
    }

    public void setAmountType(List<DillDeliverState> amountType) {
        this.amountType = amountType;
    }

    public List<DillDeliverState> getGiveContentType() {
        return giveContentType;
    }

    public void setGiveContentType(List<DillDeliverState> giveContentType) {
        this.giveContentType = giveContentType;
    }

    public List<DillDeliverState> getStockType() {
        return stockType;
    }

    public void setStockType(List<DillDeliverState> stockType) {
        this.stockType = stockType;
    }

    public List<DillDeliverState> getGiveType() {
        return giveType;
    }

    public void setGiveType(List<DillDeliverState> giveType) {
        this.giveType = giveType;
    }

    public List<DillDeliverState> getPtypeType() {
        return ptypeType;
    }

    public void setPtypeType(List<DillDeliverState> ptypeType) {
        this.ptypeType = ptypeType;
    }

    public List<DillDeliverState> getTimeType() {
        return timeType;
    }

    public void setTimeType(List<DillDeliverState> timeType) {
        this.timeType = timeType;
    }

    public List<DillDeliverState> getGiveWayType() {
        return giveWayType;
    }

    public void setGiveWayType(List<DillDeliverState> giveWayType) {
        this.giveWayType = giveWayType;
    }

    public List<DillDeliverState> getGiveWayDetailType() {
        return giveWayDetailType;
    }

    public void setGiveWayDetailType(List<DillDeliverState> giveWayDetailType) {
        this.giveWayDetailType = giveWayDetailType;
    }

    public List<DillDeliverState> getSectionType() {
        return sectionType;
    }

    public void setSectionType(List<DillDeliverState> sectionType) {
        this.sectionType = sectionType;
    }

    public List<DillDeliverState> getTradeFromType() {
        return tradeFromType;
    }

    public void setTradeFromType(List<DillDeliverState> tradeFromType) {
        this.tradeFromType = tradeFromType;
    }

    public List<DillDeliverState> getRuleTypes() {
        return ruleTypes;
    }

    public void setRuleTypes(List<DillDeliverState> ruleTypes) {
        this.ruleTypes = ruleTypes;
    }


    public List<DillDeliverState> getRepeatTypes() {
        return repeatTypes;
    }

    public void setRepeatTypes(List<DillDeliverState> repeatTypes) {
        this.repeatTypes = repeatTypes;
    }

    public List<Employee> getEmployeeList() {
        return employeeList;
    }

    public void setEmployeeList(List<Employee> employeeList) {
        this.employeeList = employeeList;
    }

    public List<DeliverMark> getMarkList() {
        return markList;
    }

    public void setMarkList(List<DeliverMark> markList) {
        this.markList = markList;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getPayStartTime() {
        return payStartTime;
    }

    public void setPayStartTime(Date payStartTime) {
        this.payStartTime = payStartTime;
    }

    public Date getPayEndTime() {
        return payEndTime;
    }

    public void setPayEndTime(Date payEndTime) {
        this.payEndTime = payEndTime;
    }

    public List<DillDeliverState> getCompareTypes() {
        return compareTypes;
    }

    public void setCompareTypes(List<DillDeliverState> compareTypes) {
        this.compareTypes = compareTypes;
    }

    public List<DeliverGiftRuleGroupDTO> getGroups() {
        return groups;
    }

    public void setGroups(List<DeliverGiftRuleGroupDTO> groups) {
        this.groups = groups;
    }
}
