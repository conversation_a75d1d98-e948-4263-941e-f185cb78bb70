package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.jarvis.dto.SaleBusinessStatisticsDto;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.SaleBusinessStatisticsParams;
import com.wsgjp.ct.sale.biz.jarvis.service.SaleBusinessStatisticsService;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "销售业务统计")
@RequestMapping("/${app.id}/jarvis/businessReport")
public class SaleBusinessStatisticsController {

    @Autowired
    private SaleBusinessStatisticsService service;

    @ApiOperation(value = "销售业务统计")
    @PostMapping("/querySaleBusinessStatistics")
    public PageResponse<SaleBusinessStatisticsDto> querySaleBusinessStatistics(@RequestBody PageRequest<SaleBusinessStatisticsParams> queryParams) {
        PubSystemLogService.saveInfo("进入【销售业务统计】");
        return service.querySaleBusinessStatistics(queryParams);
    }

}
