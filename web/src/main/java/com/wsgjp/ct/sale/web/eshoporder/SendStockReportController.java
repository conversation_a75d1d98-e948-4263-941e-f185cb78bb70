package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.SaleStockReportQuery;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.SendStockReportDto;
import com.wsgjp.ct.sale.biz.eshoporder.service.stock.SendStockReportService;
import com.wsgjp.ct.sale.biz.shopsale.common.PageSummary;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = {"可发货库存报表查询接口"})
@RestController
@RequestMapping("${app.id}/eshoporder/sendStockReport")
public class SendStockReportController  {

    @Autowired
    private SendStockReportService sendStockReportService;

    /**
     *  分页查询可销售库存报表
     * @param request
     * @return
     */
    @ApiOperation(value = "查询可发货库存报表")
    @PostMapping("/list")
    public PageResponse<SendStockReportDto> list(@RequestBody PageRequest<SaleStockReportQuery> request) {
        try {
            PageResponse<SendStockReportDto> result = sendStockReportService.list(request);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     *  可销售库存报表合计
     * @param request
     * @return
     */
    @ApiOperation(value = "查询可销售库存报表合计")
    @PostMapping("/list/count")
    public PageSummary listSummary(@RequestBody PageRequest<SaleStockReportQuery> request) {
        try {
            return sendStockReportService.listSummary(request);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
}

