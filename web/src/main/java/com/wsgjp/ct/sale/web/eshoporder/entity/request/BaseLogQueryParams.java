package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.sale.biz.bifrost.entity.BaseInfoLog;
import com.wsgjp.ct.support.log.annotation.Operator;
import com.wsgjp.ct.support.log.entity.QueryParams;

import java.math.BigInteger;
import java.util.Date;

public class BaseLogQueryParams extends QueryParams {

    private BigInteger objectId;

    private String objectType;


    public String getObjectType() {
        return objectType;
    }

    @Operator(symbol = "like")
    private String efullname;

    @Operator(symbol = "like")
    private String body;

    @Operator(symbol = ">=", field = "log_time")
    private Date beginTime;

    @Operator(symbol = "<=", field = "log_time")
    private Date endTime;

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getEfullname() {
        return efullname;
    }

    public void setEfullname(String efullname) {
        this.efullname = efullname;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public BigInteger getObjectId() {
        return objectId;
    }

    public void setObjectId(BigInteger objectId) {
        this.objectId = objectId;
    }

    @Override
    public Class getLogClass() {
        return BaseInfoLog.class;
    }


}
