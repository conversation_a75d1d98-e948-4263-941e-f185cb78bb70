package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.entity.refund.RefundAndDeliverMappingEntity;
import com.wsgjp.ct.sale.biz.jarvis.service.refund.JarvisEshopRefundService;
import com.wsgjp.ct.sale.biz.jarvis.service.refund.RefundAndDeliverMappingService;
import com.wsgjp.ct.sale.web.jarvis.request.refund.DeleteBillRequest;
import com.wsgjp.ct.sale.web.jarvis.request.refund.RefundAndDeliverMapRequest;
import io.swagger.annotations.Api;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api("售后相关")
@RequestMapping("/${app.id}/jarvis/refund")
@Configuration("JarvisRefundController")
public class RefundController {
    private RefundAndDeliverMappingService refundAndDeliverMappingService;
    private JarvisEshopRefundService jarvisEshopRefundService;

    public RefundController(RefundAndDeliverMappingService refundAndDeliverMappingService, JarvisEshopRefundService jarvisEshopRefundService) {
        this.refundAndDeliverMappingService = refundAndDeliverMappingService;
        this.jarvisEshopRefundService = jarvisEshopRefundService;
    }

    @PostMapping("/loadMap")
    public List<RefundAndDeliverMappingEntity> loadMap(@RequestBody RefundAndDeliverMapRequest request) {
        List<RefundAndDeliverMappingEntity> refundAndDeliverMappingEntities = refundAndDeliverMappingService.loadMap(request.getRefundId());
        return refundAndDeliverMappingEntities;
    }


    @PostMapping("/queryDeliverCostByRefundIdAndSkuId")
    public List<RefundAndDeliverMappingEntity> queryDeliverCostByRefundIdAndSkuId(@RequestBody RefundAndDeliverMapRequest request) {
        List<RefundAndDeliverMappingEntity> refundAndDeliverMappingEntities = refundAndDeliverMappingService.queryDeliverCostByRefundIdAndSkuId(request.getRefundId(), request.getSkuId(), request.isAutoFilterRepeated(), null);
        return refundAndDeliverMappingEntities;
    }

    @PostMapping("/deleteBill")
    public String deleteBill(@RequestBody DeleteBillRequest request) throws Exception {
        return jarvisEshopRefundService.deletedBill(request.getRefundVchcode(), request.getBillVchcode(), request.getBillType());
    }

    @PostMapping("/deleteOtherOutStock")
    public String deleteOtherOutStock(@RequestBody DeleteBillRequest request) throws Exception {
        return jarvisEshopRefundService.deleteOtherOutStock(request.getRefundVchcode(), request.getBillVchcode(), request.getBillType());
    }
}
