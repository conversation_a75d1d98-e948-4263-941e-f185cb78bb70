package com.wsgjp.ct.sale.web.jarvis.request.refund;

import java.math.BigInteger;

public class RefundAndDeliverMapRequest {
    /**
     * 售后单编号
    **/
    private String refundId;

    /**
     * 明细skuId
    **/
    private BigInteger skuId;

    /**
     * 是否去掉重复的skuId的相同的成本信息
     **/
    private boolean autoFilterRepeated;


    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public boolean isAutoFilterRepeated() {
        return autoFilterRepeated;
    }

    public void setAutoFilterRepeated(boolean autoFilterRepeated) {
        this.autoFilterRepeated = autoFilterRepeated;
    }
}
