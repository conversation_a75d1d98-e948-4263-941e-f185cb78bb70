package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.stockcheck.*;
import com.wsgjp.ct.sale.biz.api.request.analysiscloud.CheckPtypeSelectorQuery;
import com.wsgjp.ct.sale.biz.api.request.analysiscloud.StockCheckRecordQuery;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.shopsale.service.AnalysiscloudService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

@Api(tags = {"数据统计及报表"})
@RestController
@RequestMapping("${app.id}/shopsale/analysiscloud")
public class AnalysiscloudController {
    private AnalysiscloudService analysisService;

    public AnalysiscloudController(AnalysiscloudService analysisService) {
        this.analysisService = analysisService;
    }

    @ApiOperation("库存查询列表")
    @PostMapping("/getInventoryPositionList")
    @WebLogs
    public PageResponse<Map> getInventoryPositionList(@RequestBody PageRequest<Map> query) {
        return analysisService.getInventoryPositionList(query);
    }

    @ApiOperation("销售额查询")
    @PostMapping("/getAccountBalanceChangeDetail")
    @WebLogs
    public PageResponse<Map> getAccountBalanceChangeDetail(@RequestBody PageRequest<Map> query) {
        return analysisService.getAccountBalanceChangeDetail(query);
    }

    @ApiOperation("交接班数据")
    @PostMapping("/getPostTimeAndVchtypeList")
    @WebLogs
    public Map getPostTimeAndVchtypeList (@RequestBody Map query) {
        return analysisService.getPostTimeAndVchtypeList(query);
    }

    @ApiOperation("库存查询New")
    @PostMapping("/getNventoryGoodsDistList")
    @WebLogs
    public PageResponse<Map> getNventoryGoodsDistList(@RequestBody PageRequest<Map> query) {
        return analysisService.getNventoryGoodsDistList(query);
    }

    @ApiOperation("盘点记录")
    @PostMapping("/getListStockCheckRecord")
    public PageResponse<StockCheckRecordEntity> getListStockCheckRecord(@RequestBody PageRequest<StockCheckRecordQuery> query) {
        return analysisService.getListStockCheckRecord(query);
    }

    @ApiOperation("盘点记录删除")
    @PostMapping("/deleteCheck")
    public Integer deleteCheck(@RequestBody StockCheckRecordEntity query) {
        return analysisService.deleteCheck(query);
    }

    @ApiOperation("全仓盘点获取商品列表")
    @PostMapping("/getListStockCheckInfos")
    public PageResponse<CheckPtypeSelectorEntity> getListStockCheckInfos(@RequestBody PageRequest<CheckPtypeSelectorQuery> query) {
        return analysisService.getListStockCheckInfos(query);
    }

    @ApiOperation("盘点获取商品列表")
    @PostMapping("/getListCheckPtypeSelector")
    public PageResponse<CheckPtypeSelectorEntity> getListCheckPtypeSelector(@RequestBody PageRequest<CheckPtypeSelectorQuery> query) {
        return analysisService.getListCheckPtypeSelector(query);
    }


    @ApiOperation("盘点创建盘点信息")
    @PostMapping("/createCheck")
    public CheckResultEntity createCheck(@RequestBody StockCheckBillModelEntity query) {
        return analysisService.createCheck(query);
    }

    @ApiOperation("盘点创建盘点信息")
    @PostMapping("/getPtypes")
    public List<CheckPtypeSelectorEntity> getPtypes(@RequestBody StockCheckBillQuery query) {
        return analysisService.getPtypes(query);
    }


    @ApiOperation("盘点插入盘点信息")
    @PostMapping("/insertCheckDetail")
    public CheckResultEntity insertCheckDetail(@RequestBody StockCheckBillModelEntity query) {
        return analysisService.insertCheckDetail(query);
    }

    @ApiOperation("盘点更新已盘点信息")
    @PostMapping("/updateCheckDetail")
    void updateCheckDetail(@RequestBody CheckPtypeSelectorEntity query) {
        analysisService.updateCheckDetail(query);
    }

    @ApiOperation("盘点更新盘点人信息")
    @PostMapping("/updateCheckModel")
    void updateCheckModel(@RequestBody StockCheckBillModelEntity query) {
        analysisService.updateCheckModel(query);
    }


    @ApiOperation("删除盘点已盘点信息")
    @PostMapping("/deleteDetail")
    void deleteDetail(@RequestBody BigInteger detailId) {
        analysisService.deleteDetail(detailId);
    }

    @ApiOperation("删除盘点已盘点信息根据盘点id")
    @PostMapping("/deleteDetailByCheckId")
    void deleteDetailByCheckId(@RequestBody BigInteger checkId) {
        analysisService.deleteDetailByCheckId(checkId);
    }

    @ApiOperation("获取盘点信息")
    @PostMapping("/getCheckBillInfo")
    StockCheckBillModelEntity getCheckBillInfo(@RequestBody BigInteger checkId) {
        return analysisService.getCheckBillInfo(checkId);
    }


    @ApiOperation("扫码获取盘点信息")
    @PostMapping("/searchPtypeByScan")
    List<CheckPtypeSelectorEntity> searchPtypeByScan(@RequestBody Map query) {
        return analysisService.searchPtypeByScan(query);
    }


    @ApiOperation("获取默认单据编号信息")
    @PostMapping("/getDefaultNumber")
    String getDefaultNumber() {
        return analysisService.getDefaultNumber();
    }

    @ApiOperation("完成盘点")
    @PostMapping("/saveInout")
    public CheckResponse saveInout(@RequestBody StockCheckLogEntity checkLogEntity) {
        return analysisService.saveInout(checkLogEntity);
    }

    @ApiOperation("保存盘点记录")
    @PostMapping("/saveCheck")
    public CheckResponse saveCheck(@RequestBody StockCheckLogEntity checkLogEntity) {
        return analysisService.saveCheck(checkLogEntity);
    }

    @ApiOperation("完成盘点前验证")
    @PostMapping("/saveOverCheck")
    public SaveOverCheckEntity saveOverCheck(@RequestBody BigInteger stockCheckId) {
        return analysisService.saveOverCheck(stockCheckId);
    }


    @ApiOperation("保存出入库")
    @PostMapping("/inout")
    public PageResponse<StockCheckRecordInOut> inout(@RequestBody PageRequest<StockCheckInoutNumberQuery> query) {
        return analysisService.inout(query);
    }

}
