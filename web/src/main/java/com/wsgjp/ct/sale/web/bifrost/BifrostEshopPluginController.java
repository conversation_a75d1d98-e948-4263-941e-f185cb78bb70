package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopPluginService;
import com.wsgjp.ct.sale.platform.dto.plugin.DropDownPlugin;
import com.wsgjp.ct.sale.platform.dto.plugin.EshopBusinessConfig;
import com.wsgjp.ct.sale.platform.dto.plugin.EshopTagInfo;
import com.wsgjp.ct.sale.platform.dto.token.EshopFieldInfo;
import com.wsgjp.ct.sale.platform.entity.request.auth.EshopState;
import com.wsgjp.ct.sale.platform.entity.request.baseinfo.EshopBaseInfoRequest;
import com.wsgjp.ct.sale.platform.entity.request.plugin.CommonRequest;
import com.wsgjp.ct.sale.platform.entity.response.baseinfo.EshopBaseInfoResponse;
import com.wsgjp.ct.sale.platform.enums.CheckAuthType;
import com.wsgjp.ct.sale.platform.enums.StockState;
import com.wsgjp.ct.sale.platform.enums.SupportMappingType;
import com.wsgjp.ct.sale.platform.sdk.service.EshopBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "网店页面配置相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/plugin")
public class BifrostEshopPluginController {
    private final BifrostEshopPluginService pluginService;
    private final EshopBaseInfoService eshopBaseInfoService;

    public BifrostEshopPluginController(BifrostEshopPluginService pluginService, EshopBaseInfoService eshopBaseInfoService) {
        this.pluginService = pluginService;
        this.eshopBaseInfoService = eshopBaseInfoService;
    }

    @ApiOperation("定义网店新增（编辑）页面的展示内容")
    @PostMapping("getFiledInfo")
    public List<EshopFieldInfo> getFiledInfo(@RequestBody CommonRequest request) {
        return pluginService.getFiledInfo(request);
    }

    @ApiOperation("定义网店页面的显示标签，订购，授权等")
    @PostMapping("getEshopTag")
    public EshopTagInfo getEshopTag(@RequestBody EshopState state) {
        return pluginService.getEshopTag(state);
    }

    @ApiOperation("下载商品支持的状态(UI展示的顺序按照列表顺序展示)")
    @PostMapping("productDownloadSupport")
    public List<StockState> productDownloadSupport(@RequestBody CommonRequest request) {
        return pluginService.productDownloadSupport(request);
    }

    @ApiOperation("下载订单支持的状态(UI展示的顺序按照列表顺序展示)")
    @PostMapping("orderDownloadSupport")
    public List<DropDownPlugin> orderDownloadSupport(@RequestBody CommonRequest request) {
        return pluginService.orderDownloadSupport(request);
    }

    @ApiOperation("商品对应规则列表")
    @PostMapping("getSupportMappingTypeList")
    public List<SupportMappingType> getSupportMappingTypeList(@RequestBody CommonRequest request) {
        return pluginService.getSupportMappingTypeList(request);
    }

    @ApiOperation("检查授权是否过期的方式")
    @PostMapping("getCheckAuthType")
    public CheckAuthType getCheckAuthType(@RequestBody CommonRequest request) {
        return pluginService.getCheckAuthType(request);
    }

    @ApiOperation("获取支持的业务配置")
    @PostMapping("getEshopBusinessConfigList")
    public List<EshopBusinessConfig> getEshopBusinessConfigList(@RequestBody CommonRequest request) {
        return pluginService.getEshopBusinessConfigList(request);
    }
    @ApiOperation("查询店铺基础信息")
    @PostMapping("queryEshopBaseInfo")
    public EshopBaseInfoResponse queryEshopBaseInfo(@RequestBody EshopBaseInfoRequest request){
        return eshopBaseInfoService.queryEshopBaseInfo(request);
    }
}
