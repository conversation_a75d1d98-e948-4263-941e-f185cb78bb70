package com.wsgjp.ct.sale.web.jarvis.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.dto.gift.DeliverGiftConditionDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.gift.DeliverGiftRuleDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.gift.DeliverGiftRuleGroupDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.gift.DeliverGiftRuleSectionDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.DeliverMark;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Employee;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.Organization;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.DeliverGiftRuleLogQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverMarkService;
import com.wsgjp.ct.sale.biz.jarvis.service.GiftRuleService;
import com.wsgjp.ct.sale.biz.jarvis.state.*;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.CompareTypeEnum;
import com.wsgjp.ct.sale.web.jarvis.response.GiftRuleGetResponse;
import com.wsgjp.ct.sale.web.jarvis.response.GiftRuleListResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api("Jarvis页面Api")
@RestController
@RequestMapping("/${app.id}/jarvis/view")
public class ViewApiController {
    private final GiftRuleService giftRuleService;
    private final BaseInfoService baseInfoService;
    private BillDeliverMarkService deliverMarkService;

    public ViewApiController(GiftRuleService giftRuleService, BaseInfoService baseInfoService,BillDeliverMarkService deliverMarkService) {
        this.giftRuleService = giftRuleService;
        this.baseInfoService = baseInfoService;
        this.deliverMarkService = deliverMarkService;
    }

    @ApiOperation("获取时间类型")
    @GetMapping("modifyGiftRuleInit")
    public GiftRuleGetResponse modifyGiftRuleInit() throws Exception {
        GiftRuleGetResponse response = new GiftRuleGetResponse();
        List<DillDeliverState> list = baseInfoService.getDeliverBillStatus(GiftRuleTradeFromEnum.class);
        List<DillDeliverState> tradeFromList = new ArrayList<>();
        for (DillDeliverState state : list) {
            if (CurrentUser.getProductId() == 88 && "MANUAL".equals(state.getKey())) {
                state.setDescription("销售订单生成");
            }
            tradeFromList.add(state);
        }
        response.setTradeFromType(tradeFromList);
        List<Organization> organizations = getOrganizations();
        List<Employee> employees = baseInfoService.getETypesLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
        employees.sort((a, b) -> {
            if (a.isStop() ^ b.isStop()) {
                return a.isStop() ? 1 : -1;
            } else {
                return 0;
            }
        });
        List<DeliverMark> marks = deliverMarkService.getAllDeliverMark(CurrentUser.getProfileId());
        response.setOtypeList(organizations.stream().filter(o -> o.getOcategory() != 2 && o.getOcategory() != 3).collect(Collectors.toList()));
        response.setEmployeeList(employees);
        response.setMarkList(marks);
        response.setPtypeType(getPtypeTypes());
        response.setTimeType(baseInfoService.getDeliverBillStatus(GiftRuleTimeTypeEnum.class));
        List<DeliverGiftRuleGroupDTO> groups = giftRuleService.getGroupAndDefault();
        response.setGroups(groups.stream().filter(g->g.isStoped() == false).collect(Collectors.toList()));
        response.setCompareTypes(baseInfoService.getDeliverBillStatus(CompareTypeEnum.class));
        return response;
    }

    private List<DillDeliverState> getPtypeTypes()
    {
        List<DillDeliverState> ptypeTypes = baseInfoService.getDeliverBillStatus(GiftRulePtypeTypeEnum.class);
        ptypeTypes.removeIf(p->p.getCode().equals(2));
        DillDeliverState state = new DillDeliverState();
        state.setCode(GiftRulePtypeTypeEnum.OUT_LIST.getCode());
        state.setKey(GiftRulePtypeTypeEnum.OUT_LIST.toString());
        state.setDescription(GiftRulePtypeTypeEnum.OUT_LIST.getName());
        ptypeTypes.add(state);
        return ptypeTypes;
    }

    @ApiOperation("新增/编辑赠品规则页面初始化接口")
    @GetMapping("editGiftRuleInit")
    public GiftRuleGetResponse editGiftRuleInit(BigInteger ruleId, BigInteger logId) throws Exception {
        GiftRuleGetResponse response = new GiftRuleGetResponse();
        response.setPtypeType(getPtypeTypes());
        response.setStockType(baseInfoService.getDeliverBillStatus(GiftRuleGiveStockTypeEnum.class));
        response.setTimeType(baseInfoService.getDeliverBillStatus(GiftRuleTimeTypeEnum.class));
        response.setAmountType(baseInfoService.getDeliverBillStatus(GiftRuleAmountRuleEnum.class));
        response.setGiveContentType(baseInfoService.getDeliverBillStatus(GiftRuleContentTypeEnum.class));
        response.setGiveType(baseInfoService.getDeliverBillStatus(GiftRuleGiveTypeEnum.class));
        response.setGiveWayType(baseInfoService.getDeliverBillStatus(GiftRuleGiveWayEnum.class));
        response.setGiveWayDetailType(baseInfoService.getDeliverBillStatus(GiftRuleGiveWayDetailEnum.class));
        response.setSectionType(baseInfoService.getDeliverBillStatus(GiftRuleSectionTypeEnum.class));
        response.setRepeatTypes(baseInfoService.getDeliverBillStatus(GiftRuleRepeatTypeEnum.class));
        response.setRuleTypes(baseInfoService.getDeliverBillStatus(GiftRuleRuleTypeEnum.class));
        response.setCompareTypes(baseInfoService.getDeliverBillStatus(CompareTypeEnum.class));

        List<DillDeliverState> tradeFromList = baseInfoService.getDeliverBillStatus(GiftRuleTradeFromEnum.class);
//        SysGlobalConfig config = GlobalConfig.get(SysGlobalConfig.class);
//        for (DillDeliverState state : response.getAmountType()) {
//            if (state.getCode() == GiftRuleAmountRuleEnum.ORDER_TOTAL.getCode()) {
//                if (!config.isEnabledTax()) {
//                    state.setDescription("按单据折后金额计算");
//                }
//            }
//        }
        response.setTradeFromType(tradeFromList);
        List<Organization> organizations = getOrganizations();
        List<Employee> employees = baseInfoService.getETypesContainStopLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
        employees.sort((a, b) -> {
            if (a.isStop() ^ b.isStop()) {
                return a.isStop() ? 1 : -1;
            } else {
                return 0;
            }
        });
        List<DeliverMark> marks = deliverMarkService.getAllDeliverMark(CurrentUser.getProfileId());
        response.setOtypeList(organizations.stream().filter(o -> o.getOcategory() != 2 && o.getOcategory() != 3).collect(Collectors.toList()));
        response.setEmployeeList(employees);
        response.setMarkList(marks.stream().filter(mark->!mark.getId().equals(BigInteger.ZERO)).collect(Collectors.toList()));

        DeliverGiftRuleDTO giftRule = null;
        if (logId != null && !logId.equals(BigInteger.ZERO)) {
            PageRequest<DeliverGiftRuleLogQueryParams> logRequest = new PageRequest<>();
            DeliverGiftRuleLogQueryParams queryParams = new DeliverGiftRuleLogQueryParams();
            queryParams.setId(logId);
            logRequest.setQueryParams(queryParams);
            logRequest.setPageSize(10);
            logRequest.setPageIndex(1);
            PageResponse<Map<String, Object>> logPageResponse = LogService.query(logRequest);
            if (logPageResponse.getTotal() > 0) {
                giftRule = JsonUtils.toObject(logPageResponse.getList().get(0).get("json").toString(), DeliverGiftRuleDTO.class);
            }
            if (giftRule == null) {
                throw new RuntimeException("未查到相关历史版本");
            }
            if (Integer.valueOf(0).equals(giftRule.getGiveStockCount())) {
                giftRule.setGiveStockCount(null);
            }
        } else {
            giftRule = giftRuleService.getGiftRule(ruleId);
            if (giftRule == null) {
                giftRule = new DeliverGiftRuleDTO();
                giftRule.setProfileId(CurrentUser.getProfileId());
                giftRule.setEtypeId(CurrentUser.getEmployeeId());
                giftRule.setTitle("");
                giftRule.setPriority(giftRuleService.newPriority());
                //活动时间
                giftRule.setStartTime(DateUtils.parse(DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd 00:00:00")));
                giftRule.setEndTime(DateUtils.parse(DateUtils.formatDate(DateUtils.addDays(DateUtils.getDate(), 6), "yyyy-MM-dd 23:59:59")));
                giftRule.setRepeatType(false);

                //付款时间
                giftRule.setPayStartTime(DateUtils.parse(DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd 00:00:00")));
                giftRule.setPayEndTime(DateUtils.parse(DateUtils.formatDate(DateUtils.addDays(DateUtils.getDate(), 6), "yyyy-MM-dd 23:59:59")));
                giftRule.setTimeType(GiftRuleTimeTypeEnum.PAY_TIME);
                //前N单赠送
                giftRule.setGiveStockCount(null);
                //订单标记：包含、排除
                giftRule.setMarkIncludeType(GiftRuleIncludeType.INCLUDED);
                //活动商品：本地、线上
                giftRule.setPtypeKind(GiftRulePtypeKind.LOCAL);
                //指定商品、全部商品
                giftRule.setPtypeType(GiftRulePtypeTypeEnum.ALL);
                giftRule.setOnlinePtypeKind(GiftRuleOnlinePtypeKind.XCODE);
                giftRule.setPtypeEqual(GiftRulePtypeEqual.INCLUDED);

                //规则类型：满件、满额，随机
                giftRule.setType(GiftRuleGiveTypeEnum.QUANTITY);
                //规则计算方式：按总数量，单个数量
                giftRule.setAmountRule(GiftRuleAmountRuleEnum.PTYPE_QUANTITY);
                //赠品是否送完即止
                giftRule.setGiveStockType(GiftRuleGiveStockTypeEnum.TOP_N);
                //直接送赠品赠送方式：随机、、同品、指定
                giftRule.setGiveWay(GiftRuleGiveWayEnum.RANDOM);
                //直接送成本最低、库存最多、随机
                giftRule.setGiveContentType(GiftRuleContentTypeEnum.PRICE_LOWER_FIRST);

                giftRule.setStatus(GiftRuleStatusEnum.ING);
                giftRule.setStartPoint(BigDecimal.ZERO);
                giftRule.setEnabled(true);

                //giftRule.setRuleType(GiftRuleRuleTypeEnum.DENY);
                //giftRule.setTimeType(GiftRuleTimeTypeEnum.BUY_TIME);

//                //赠品赠送方式：随机、同品、指定
//                giftRule.setGiveWay(GiftRuleGiveWayEnum.RANDOM);
//                giftRule.setSellerMemoEnabled(false);
//                giftRule.setTradeFromEnabled(false);

//                giftRule.setPtypeRepeatEnabled(false);
//
                //giftRule.setGiveContentType(GiftRuleContentTypeEnum.PRICE_LOWER_FIRST);
              //  giftRule.setGiveWayDetail(GiftRuleGiveWayDetailEnum.BUY_X_GIVE_X);

                DeliverGiftConditionDTO conditionDTO = new DeliverGiftConditionDTO();
                conditionDTO.setStart(BigDecimal.ZERO);
                conditionDTO.setQuantity(1);

                List<DeliverGiftRuleSectionDTO> sectionDTOS = new ArrayList<>();
                DeliverGiftRuleSectionDTO sectionDTO = new DeliverGiftRuleSectionDTO();
//                sectionDTO.setStart(BigDecimal.ZERO);
               // sectionDTO.setEnd(BigDecimal.valueOf(999999));
                sectionDTO.setId(UId.newId());
                sectionDTO.setProfileId(CurrentUser.getProfileId());
                sectionDTO.setSectionType(GiftRuleSectionTypeEnum.QUANTITY);
                sectionDTO.setRuleId(giftRule.getId());
                sectionDTO.setPriority(0);
                sectionDTO.setSectionName("层级"+ (sectionDTO.getPriority()+1));
                sectionDTO.setPtypeList(new ArrayList<>());
//                sectionDTO.setQuantity(1);
                //赠品赠送方式：随机、、同品、指定
                sectionDTO.setGiveWay(GiftRuleGiveWayEnum.RANDOM);
                //成本最低、库存最多、随机
                sectionDTO.setGiveContentType(GiftRuleContentTypeEnum.PRICE_LOWER_FIRST);
                //倍送
                sectionDTO.setEnabledDoubleGift(false);
                sectionDTO.setConditionList(Arrays.asList(conditionDTO));
                sectionDTOS.add(sectionDTO);
                giftRule.setSectionList(sectionDTOS);
                response.setGiftRule(giftRule);
            } else {
                if (Integer.valueOf(0).equals(giftRule.getGiveStockCount())) {
                    giftRule.setGiveStockCount(null);
                }
            }
        }

        giftRule.setStartPoint(null);
        response.setGiftRule(giftRule);

        BigInteger groupId = giftRule.getGiftRuleGroupId();
        List<DeliverGiftRuleGroupDTO> groups = giftRuleService.getGroupAndDefault();
        response.setGroups(groups.stream().filter(g->g.isStoped() == false || g.getId().equals(groupId)).collect(Collectors.toList()));
        return response;
    }

    @ApiOperation("赠品规则查询列表")
    @GetMapping("giftRuleListInit")
    public GiftRuleListResponse giftRuleListInit() throws Exception {
        GiftRuleListResponse response = new GiftRuleListResponse();
        response.setAmountType(baseInfoService.getDeliverBillStatus(GiftRuleAmountRuleEnum.class));
        response.setGiveType(baseInfoService.getDeliverBillStatus(GiftRuleGiveTypeEnum.class));
        response.setGiveWayType(baseInfoService.getDeliverBillStatus(GiftRuleGiveWayEnum.class));
        response.setGiveWayDetailType(baseInfoService.getDeliverBillStatus(GiftRuleGiveWayDetailEnum.class));
        response.setStockType(baseInfoService.getDeliverBillStatus(GiftRuleGiveStockTypeEnum.class));
        response.setStatusType(baseInfoService.getDeliverBillStatus(GiftRuleStatusEnum.class));
        response.setRepeatTypes(baseInfoService.getDeliverBillStatus(GiftRuleRepeatTypeEnum.class));
//        SysGlobalConfig config = GlobalConfig.get(SysGlobalConfig.class);
//        for (DillDeliverState state : response.getAmountType()) {
//            if (state.getCode() == GiftRuleAmountRuleEnum.ORDER_TOTAL.getCode()) {
//                if (!config.isEnabledTax()) {
//                    state.setDescription("按单据折后金额计算");
//                }
//            }
//        }
        response.setGiveContentType(baseInfoService.getDeliverBillStatus(GiftRuleContentTypeEnum.class));
        response.setSectionType(baseInfoService.getDeliverBillStatus(GiftRuleSectionTypeEnum.class));
        response.setRuleTypes(baseInfoService.getDeliverBillStatus(GiftRuleRuleTypeEnum.class));

        response.setStart(DateUtils.formatDate(DateUtils.addDays(DateUtils.getDate(), -29), "yyyy-MM-dd 00:00:00"));
        response.setEnd(DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd 23:59:59"));
        response.setStatus(GiftRuleStatusEnum.ALL);

        List<DeliverGiftRuleGroupDTO> groups = giftRuleService.getGroupAndDefault();
        response.setGroups(groups);

        List<Organization> organizations = getOrganizations();
        response.setOtypeList(organizations.stream().filter(o -> o.getOcategory() != 2 && o.getOcategory() != 3).collect(Collectors.toList()));
        return response;
    }

    @NotNull
    private List<Organization> getOrganizations() {
        List<Organization> organizations = baseInfoService.getEshopOrganizationsLimit(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
        organizations.sort((a, b) -> {
            if (a.isEnable() ^ b.isEnable()) {
                return a.isEnable() ? -1 : 1;
            } else {
                return 0;
            }
        });
        return organizations;
    }
}
