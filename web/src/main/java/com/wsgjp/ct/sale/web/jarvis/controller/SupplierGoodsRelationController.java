package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillBusinessType;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.biz.jarvis.service.SupplierGoodsRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;

/**
 * @Description TODO
 * @Date 2024-05-17 14:45
 * @Created by lingxue
 */
@RestController
@Api(description = "代销商品")
@RequestMapping("/${app.id}/jarvis/supplierGoodsRelation")
public class SupplierGoodsRelationController {
    private SupplierGoodsRelationService supplierGoodsRelationService;

    public SupplierGoodsRelationController(SupplierGoodsRelationService supplierGoodsRelationService){
        this.supplierGoodsRelationService = supplierGoodsRelationService;
    }

    @ApiOperation(value = "", notes = "获取代销商品")
    @PostMapping("getSupplierGoods")
    public List<BillDeliverDetailDTO> getSupplierGoods(@RequestBody SupplierGoodsRelationRequest request) {
        BillDeliverDTO bill = new BillDeliverDTO();
        bill.setSupplierId(request.getSupplierId());
        bill.setBusinessType(request.getBusinessType());
        this.supplierGoodsRelationService.buildSupplierGoods(bill,request.getDetails());
        return request.getDetails();
    }
}
class SupplierGoodsRelationRequest{
    private BigInteger supplierId;
    private com.wsgjp.ct.bill.core.handle.entity.enums.BillBusinessType businessType;
    private List<BillDeliverDetailDTO> details;

    public BigInteger getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(BigInteger supplierId) {
        this.supplierId = supplierId;
    }

    public com.wsgjp.ct.bill.core.handle.entity.enums.BillBusinessType getBusinessType() {
        return businessType;
    }

    public void setBusinessType(BillBusinessType businessType) {
        this.businessType = businessType;
    }

    public List<BillDeliverDetailDTO> getDetails() {
        return details;
    }

    public void setDetails(List<BillDeliverDetailDTO> details) {
        this.details = details;
    }
}

