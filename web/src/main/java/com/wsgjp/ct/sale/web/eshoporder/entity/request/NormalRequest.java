package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BaseQuery;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/13 0013 19:19
 */
public class NormalRequest extends BaseQuery {
    private int intId;
    private String requestStr;
    private List<BigInteger> idList;
    private List<String> strList;
    private BigInteger bigIntegerId;

    public String getRequestStr() {
        return requestStr;
    }

    public void setRequestStr(String requestStr) {
        this.requestStr = requestStr;
    }

    public List<BigInteger> getIdList() {
        return idList;
    }

    public void setIdList(List<BigInteger> idList) {
        this.idList = idList;
    }

    public List<String> getStrList() {
        return strList;
    }

    public void setStrList(List<String> strList) {
        this.strList = strList;
    }

    public int getIntId() {
        return intId;
    }

    public void setIntId(int intId) {
        this.intId = intId;
    }

    public BigInteger getBigIntegerId() {
        return bigIntegerId;
    }

    public void setBigIntegerId(BigInteger bigIntegerId) {
        this.bigIntegerId = bigIntegerId;
    }
}
