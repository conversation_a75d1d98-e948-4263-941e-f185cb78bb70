package com.wsgjp.ct.sale.web.bifrost.request;

import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel("根据创建时间下载会员信息请求对象")
public class VipCustomerRequest extends BaseRequest {
    @ApiModelProperty(value = "开始时间", required = true, example = "2022-03-09 10:11:11")
    private Date beginTime;
    @ApiModelProperty(value = "结束时间", required = true, example = "2022-03-09 10:11:11")
    private Date endTime;

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
