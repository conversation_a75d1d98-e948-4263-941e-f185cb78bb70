package com.wsgjp.ct.sale.web.shopsale;

import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.shopsale.common.VersionInfo;
import com.wsgjp.ct.sale.biz.shopsale.service.AppVersionInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "${app.id}/shopsale/appVersion", tags = {"app版本信息"})
@RestController
@RequestMapping("${app.id}/shopsale/appVersion")
public class AppVersionInfoController {
    AppVersionInfoService mAppVersionInfoService;

    public AppVersionInfoController(AppVersionInfoService appVersionInfoService) {
        this.mAppVersionInfoService = appVersionInfoService;
    }

    @ApiOperation("获取app版本信息")
    @GetMapping("/getAppVersion")
    @WebLogs
    public VersionInfo getVersionInfo() throws CloneNotSupportedException {
        return mAppVersionInfoService.getAppVersionInfo();
    }

}
