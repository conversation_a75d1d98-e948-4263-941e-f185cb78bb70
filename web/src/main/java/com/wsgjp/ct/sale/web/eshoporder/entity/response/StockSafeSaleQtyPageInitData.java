package com.wsgjp.ct.sale.web.eshoporder.entity.response;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.common.entity.eshop.EshopSelectorData;

import java.util.List;

/**
 * <AUTHOR> 2023/7/28 16:05
 */
public class StockSafeSaleQtyPageInitData {
    private List<Stock> ktypes;
    private List<EshopSelectorData> eshopSource;

    private Boolean eshopSafeQtyEnabled = false;

    private Boolean productSafeQtyEnabled = false;

    private Boolean enabledProps = false;

    private Boolean showDeleted = false;

    public List<Stock> getKtypes() {
        return ktypes;
    }

    public void setKtypes(List<Stock> ktypes) {
        this.ktypes = ktypes;
    }

    public List<EshopSelectorData> getEshopSource() {
        return eshopSource;
    }

    public void setEshopSource(List<EshopSelectorData> eshopSource) {
        this.eshopSource = eshopSource;
    }

    public Boolean getEshopSafeQtyEnabled() {
        return eshopSafeQtyEnabled;
    }

    public void setEshopSafeQtyEnabled(Boolean eshopSafeQtyEnabled) {
        this.eshopSafeQtyEnabled = eshopSafeQtyEnabled;
    }

    public Boolean getProductSafeQtyEnabled() {
        return productSafeQtyEnabled;
    }

    public void setProductSafeQtyEnabled(Boolean productSafeQtyEnabled) {
        this.productSafeQtyEnabled = productSafeQtyEnabled;
    }

    public Boolean getEnabledProps() {
        return enabledProps;
    }

    public void setEnabledProps(Boolean enabledProps) {
        this.enabledProps = enabledProps;
    }

    public Boolean getShowDeleted() {
        if (showDeleted == null) {
            showDeleted = false;
        }
        return showDeleted;
    }

    public void setShowDeleted(Boolean showDeleted) {
        this.showDeleted = showDeleted;
    }
}
