package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopStoreService;
import com.wsgjp.ct.sale.platform.dto.warehouse.EshopOnlineStoreInfo;
import com.wsgjp.ct.sale.platform.dto.warehouse.EshopPlatformWareHouse;
import com.wsgjp.ct.sale.platform.dto.warehouse.EshopSupplierInfo;
import com.wsgjp.ct.sale.platform.entity.request.store.GetOnlineStoreRequest;
import com.wsgjp.ct.sale.platform.entity.request.store.GetSupplierRequest;
import com.wsgjp.ct.sale.platform.entity.request.store.PlatformWarehouseRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "网店电商仓(电商门店/网店+电商仓库)/平台仓相关接口")
@RestController
@RequestMapping("${app.id}/bifrost/store")
public class BifrostEshopStoreController {

    private final BifrostEshopStoreService storeService;

    public BifrostEshopStoreController(BifrostEshopStoreService storeService) {
        this.storeService = storeService;
    }

    @ApiOperation("获取电商仓列表(电商门店/网店+电商仓库)")
    @PostMapping("getPlatformWarehouse")
    public List<EshopPlatformWareHouse> getPlatformWarehouse(@RequestBody PlatformWarehouseRequest request) {
        return storeService.getPlatformWarehouse(request);
    }

    @ApiOperation("获取平台仓列表")
    @PostMapping("getOnlineStore")
    public List<EshopOnlineStoreInfo> getOnlineStore(@RequestBody GetOnlineStoreRequest request) {
        return storeService.getOnlineStore(request);
    }

    @ApiOperation("获取供应商列表")
    @PostMapping("getSupplier")
    public List<EshopSupplierInfo> getSupplierInfo(@RequestBody GetSupplierRequest request) {
        return storeService.getSupplierInfo(request);
    }
}
