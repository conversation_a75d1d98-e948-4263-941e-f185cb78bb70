package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopPublishProductService;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.NameConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeDownloadTask;
import com.wsgjp.ct.sale.biz.eshoporder.entity.productpublish.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryEShopParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.publish.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.publish.CheckCategoryResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.publish.CustomerPageInitData;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductPublishService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.jarvis.config.AsyncProcess;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.BatchProcessRequest;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.dto.product.ProductPicInfo;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.address.QueryPlatformAddressRequest;
import com.wsgjp.ct.sale.platform.entity.request.product.*;
import com.wsgjp.ct.sale.platform.entity.response.address.QueryPlatformAddressResponse;
import com.wsgjp.ct.sale.platform.entity.response.product.*;
import com.wsgjp.ct.sale.platform.enums.ImageFormatTypeEnum;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> 2023/12/13 17:31
 */
@Api(tags = "商品发布接口")
@RequestMapping("${app.id}/eshoporder/publish")
@RestController
public class PublishGoodsController {
    private final Logger logger = LoggerFactory.getLogger(PublishGoodsController.class);
    private final EshopProductPublishService eshopProductPublishService;

    private final BifrostEshopPublishProductService bifrostPublishProductService;
    private final EshopService eshopService;
    private final SaleBizConfig saleBizConfig;

    public PublishGoodsController(EshopProductPublishService eshopProductPublishService, BifrostEshopPublishProductService bifrostPublishProductService, EshopService eshopService, SaleBizConfig saleBizConfig) {
        this.eshopProductPublishService = eshopProductPublishService;
        this.bifrostPublishProductService = bifrostPublishProductService;
        this.eshopService = eshopService;
        this.saleBizConfig = saleBizConfig;
    }

    @RequestMapping(value = "/saveNormal", method = RequestMethod.POST)
    @ApiOperation("保存通用资料")
    public EshopProductPublishCommonResponse savePublishNormalInfo(@RequestBody EshopProductPublishCommonSpecilRequest param) {
        EshopProductPublishCommon newProductCommonInfo = param.getNewProductCommonInfo();
        EshopProductPublishCommon oldProductCommonInfo = param.getOldProductCommonInfo();
        if (newProductCommonInfo.getVolume().compareTo(new BigDecimal("999999999")) == 1) {
            throw new RuntimeException("长宽高的乘积不能大于：999999999");
        }
        if (null == newProductCommonInfo.getBasePublishId()) {
            return eshopProductPublishService.savePublishNormalInfo(newProductCommonInfo);
        } else {
            eshopProductPublishService.recordEditLogCommon(param);
            if (null == oldProductCommonInfo) {
                logger.error("参数信息不全，请检查参数");
            }
            return eshopProductPublishService.updatePublishNormalInfo(newProductCommonInfo, oldProductCommonInfo);
        }
    }


    @RequestMapping(value = "/saveJdZy", method = RequestMethod.POST)
    @ApiOperation("保存京东自营资料")
    public EshopProductPublishCustomerResponse savePublishJdZyInfo(@RequestBody EshopProductPublishCustomerSpecilRequest publishCustom) {
        EshopProductPublishCustomRequest newcustomRequest = publishCustom.getNewProductCustomInfo();
        if (newcustomRequest.getVolume().compareTo(new BigDecimal("999999999")) == 1) {
            throw new RuntimeException("长宽高的乘积不能大于：999999999");
        }
        if (newcustomRequest.getId() == null) {
            return eshopProductPublishService.savePublishJdZyInfo(newcustomRequest);
        } else {
            eshopProductPublishService.recordEditLog(publishCustom);
            return eshopProductPublishService.updatePublishJdZyInfo(newcustomRequest);
        }
    }

    @PageDataSource
    @RequestMapping(value = "/queryEshopProductPublishList", method = RequestMethod.POST)
    public PageResponse<EshopProductPublishReponse> queryEshopProductPublishList(@RequestBody PageRequest<EshopProductPublishRequest> params) {
        return PageDevice.readPage(eshopProductPublishService.queryEshopProductPublishList(params));
    }

    @RequestMapping(value = "/doDeletePublish", method = RequestMethod.POST)
    public void doDeletePublish(@RequestBody List<EshopProductPublishReponse> params) {
        eshopProductPublishService.doDeletePublish(params);
    }

    @ApiOperation("商品发布")
    @RequestMapping(value = "/doBatchPublish", method = RequestMethod.POST)
    public String doBatchPublish(@RequestBody List<EshopProductPublishReponse> params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl pLogger = new ProcessLoggerImpl(taskId);
        ThreadPool test = ThreadPoolFactory.build(NameConstantEnum.PRODUCT_PUBLISH.getName());
        test.executeAsync(invoker -> {
            try {
                pLogger.appendMsg("开始发布");
                eshopProductPublishService.doBatchPublish(params, pLogger, taskId);
            } catch (Exception ex) {
                pLogger.appendMsg(ex.getMessage());
                ex.printStackTrace();
            } finally {
                pLogger.appendMsg("发布结束");
                pLogger.doFinish();
            }
        }, "批量发布");
        return taskId;
    }

    @ApiOperation("重置发布状态")
    @RequestMapping(value = "/doResetPublishState", method = RequestMethod.POST)
    public void doResetPublishState(@RequestBody List<EshopProductPublishReponse> params) {
        eshopProductPublishService.doResetPublishState(params);
    }


    @ApiOperation("更新审核状态")
    @RequestMapping(value = "/doBatchUpdateAuditSta", method = RequestMethod.POST)
    public void doBatchUpdateAuditSta(@RequestBody List<EshopProductPublishReponse> params) {
        eshopProductPublishService.doBatchUpdateAuditSta(params);
    }

    @ApiOperation("全量更新审核状态")
    @RequestMapping(value = "/doUpdateAuditStaAll", method = RequestMethod.POST)
    @AsyncProcess(threadPoolName = AsyncProcess.ProcessName.modifyMappingtypeProcessName)
    public String doUpdateAuditStaAll(@RequestBody BatchProcessRequest<EshopProductPublishReponse> params) {
        RedisProcessMessage processMessage = new RedisProcessMessage(params.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        messageLog.appendMsg("开始更新审核状态");
        eshopProductPublishService.doUpdateAuditStaAll(messageLog,processMessage);
        processMessage.setFinish();
        return params.getProcessId();
    }

    @ApiOperation("查看发布失败和审核拒绝的原因")
    @RequestMapping(value = "/getRefusedReason", method = RequestMethod.POST)
    public String getRefusedReason(@RequestBody EshopProductPublishReponse params) {
        return eshopProductPublishService.getRefusedReason(params);
    }

    @PostMapping(value = "/getOpenUrlInfo")
    public EshopProductPublishOpenUrlReponse getOpenUrlInfo(@RequestBody EshopProductPublishReponse parameter) {
        EshopProductPublishOpenUrlReponse res = eshopProductPublishService.getOpenUrlInfo(parameter);
        return res;
    }

    @ApiOperation("查看拒绝时候的审核状态")
    @RequestMapping(value = "/queryUpdateAuditSta", method = RequestMethod.POST)
    public void queryUpdateAuditSta(@RequestBody EshopProductPublishReponse param) {
        eshopProductPublishService.queryUpdateAuditSta(param);
    }

    @ApiOperation("商品发布修改")
    @RequestMapping(value = "/doBatchPublishEdit", method = RequestMethod.POST)
    public String doBatchPublishEdit(@RequestBody List<EshopProductPublishReponse> params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl pLogger = new ProcessLoggerImpl(taskId);
        ThreadPool test = ThreadPoolFactory.build(NameConstantEnum.PRODUCT_PUBLISH.getName());
        test.executeAsync(invoker -> {
            try {
                pLogger.appendMsg("开始批量发布修改");
                eshopProductPublishService.doBatchPublishEdit(params, pLogger, taskId);
            } catch (Exception ex) {
                pLogger.appendMsg(ex.getMessage());
                ex.printStackTrace();
            } finally {
                pLogger.appendMsg("批量发布修改结束");
                pLogger.doFinish();
            }
        }, "批量发布修改");
        return taskId;
    }

    @ApiOperation("商品发布发布图片")
    @RequestMapping(value = "/doBatchPublishimage", method = RequestMethod.POST)
    public String doBatchPublishimage(@RequestBody List<EshopProductPublishReponse> params) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl pLogger = new ProcessLoggerImpl(taskId);
        ThreadPool test = ThreadPoolFactory.build(NameConstantEnum.PRODUCT_PUBLISH.getName());
        test.executeAsync(invoker -> {
            try {
                pLogger.appendMsg("开始发布");
                eshopProductPublishService.doBatchPublishimage(params, pLogger, taskId);
            } catch (Exception ex) {
                pLogger.appendMsg(ex.getMessage());
                ex.printStackTrace();
            } finally {
                pLogger.appendMsg("发布结束");
                pLogger.doFinish();
            }
        }, "批量发布修改");
        return taskId;
    }

    @ApiOperation("解绑店铺")
    @RequestMapping(value = "/doCleanEshopRelation", method = RequestMethod.POST)
    public String doCleanEshopRelation(@RequestBody List<EshopProductPublishReponse> params) {
        return eshopProductPublishService.doCleanEshopRelation(params);
    }

    @ApiOperation("绑定店铺")
    @RequestMapping(value = "/doBindEshopRelation", method = RequestMethod.POST)
    public String doBindEshopRelation(@RequestBody List<EshopProductPublishReponse> params) {
        return eshopProductPublishService.doBindEshopRelation(params);
    }

    @RequestMapping(value = "/getCommonDetailList", method = RequestMethod.POST)
    @ApiOperation("查询通用资料列表")
    public PageResponse<EshopProductPublishCommon> queryCommonDetailList(@RequestBody PageRequest<QueryCommonDetailParam> param) {
        QueryCommonDetailParam queryParams = param.getQueryParams();
        return PageDevice.readPage(eshopProductPublishService.queryEshopProductPublishCommonList(queryParams));
    }


    @RequestMapping(value = "/queryCustomerPageInitData", method = RequestMethod.POST)
    @ApiOperation("查询平台个性化资料界面初始化数据")
    public CustomerPageInitData queryBuildCustomerDetailPageInitData(@RequestBody QueryEShopParameter queryParam) {
        CustomerPageInitData initData = new CustomerPageInitData();
        CommonUtil.initLimited(queryParam);
        List<EshopInfo> eshopInfoList = eshopService.getEshopByShopTypes(queryParam);
        initData.setEshopList(eshopInfoList);
        return initData;
    }

    @RequestMapping(value = "/queryEShopListByShopType", method = RequestMethod.POST)
    @ApiOperation("店铺列表初始化数据")
    public CustomerPageInitData queryBuildCustomerDetailPageInitData(@RequestBody QueryPlatformInfoParam param) {
        CustomerPageInitData initData = new CustomerPageInitData();
        QueryEShopParameter queryParam = new QueryEShopParameter();
        queryParam.setShopType(param.getShopType());
        CommonUtil.initLimited(queryParam);
        List<EshopInfo> eshopInfoList = eshopService.getEshopByShopTypes(queryParam);
        initData.setEshopList(eshopInfoList);
        return initData;
    }


    @RequestMapping(value = "/queryPlatformCategory", method = RequestMethod.POST)
    @ApiOperation("查询平台分类列表")
    public QueryProductCategoryResponse queryPlatformCategoryList(@RequestBody QueryPlatformInfoParam param) {
        return bifrostPublishProductService.queryProductCategory(param);
    }

    @RequestMapping(value = "/queryPlatformCategoryByParent", method = RequestMethod.POST)
    @ApiOperation("根据父节点查询平台分类")
    public QueryProductCategoryResponse queryPlatformCategoryListByParent(@RequestBody QueryPlatformCateByParanetParam param) {
        return bifrostPublishProductService.queryProductCategoryListByParent(param);
    }

    @RequestMapping(value = "/checkShopSupportCategory", method = RequestMethod.POST)
    @ApiOperation("检查店铺是否存在某个类目")
    public CheckCategoryResponse checkShopSupportCategory(@RequestBody CheckCategoryParam param) {
        return bifrostPublishProductService.checkShopSupportCategory(param);
    }

    @RequestMapping(value = "/queryPlatformBrand", method = RequestMethod.POST)
    @ApiOperation("查询平台品牌列表")
    public QueryEshopBrandResponse queryPlatformBrandList(@RequestBody QueryPlatformInfoParam param) {
        return bifrostPublishProductService.queryEshopBrands(param);
    }

    @RequestMapping(value = "/queryPlatformDangerGoods", method = RequestMethod.POST)
    @ApiOperation("查询平台危险品列表")
    public QueryDangerGoodsResponse queryPlatformDangerGoods(@RequestBody QueryPlatformInfoParam param) {
        return bifrostPublishProductService.queryDangerGoods(param);
    }

    @RequestMapping(value = "/queryPlatformSaleUnits", method = RequestMethod.POST)
    @ApiOperation("查询平台销售单位列表")
    public QuerySaleUnitResponse queryPlatformSaleUnits(@RequestBody QueryPlatformInfoParam param) {
        return bifrostPublishProductService.querySaleUnits(param);
    }

    @RequestMapping(value = "/queryPlatformPurchaserList", method = RequestMethod.POST)
    @ApiOperation("查询平台采购员列表")
    public QueryPurchaserResponse queryPlatformPurchaserList(@RequestBody QueryPlatformInfoParam param) {
        return bifrostPublishProductService.queryPurchaserList(param);
    }

    //queryProductPublishApplyInfo
    @RequestMapping(value = "/queryPlatformProductPublishApply", method = RequestMethod.POST)
    @ApiOperation("查询平台商品发布申请")
    public QueryProductPublishApplyInfoResponse queryPlatformProductPublishApply(@RequestBody QueryPlatformProductAuditParam param) {
        return bifrostPublishProductService.queryProductPublishApplyInfo(param);
    }

    @RequestMapping(value = "/queryPlatformSalerInfo", method = RequestMethod.POST)
    @ApiOperation("查询根据销售员简码平台销售员")
    public QuerySalerResponse queryPlatformSalerInfo(@RequestBody QueryPlatformInfoParam param) {
        QuerySalerRequest request = new QuerySalerRequest();
        request.setSalerCode(param.getSalerCode());
        BigInteger eshopId = BigInteger.ZERO;
        if (CollectionUtils.isNotEmpty(param.getEshopIdList())) {
            eshopId = param.getEshopIdList().get(0);
        }
        request.setShopId(eshopId);
        return eshopProductPublishService.querySalerInfo(request);
    }

    @RequestMapping(value = "/queryTemplateInfo", method = RequestMethod.POST)
    public QueryTemplateInfoResponse queryTemplateInfo(@RequestBody QueryTemplateInfoRequest request) {
        return bifrostPublishProductService.queryTemplateInfo(request);
    }

    @RequestMapping(value = "/getItemDynamicFieldValidate", method = RequestMethod.POST)
    @ApiOperation("查询商品信息动态字段")
    public ItemDynamicFieldGaeaResponse getItemDynamicFieldValidate(@RequestBody QueryPlatformInfoParam param) {
        ItemDynamicFieldGaeaRequest request = new ItemDynamicFieldGaeaRequest();
        request.setCateId(param.getCateId());
        request.setItemType(param.getItemType());
        request.setMarketType(param.getMarketType());
        BigInteger eshopId = BigInteger.ZERO;
        if (CollectionUtils.isNotEmpty(param.getEshopIdList())) {
            eshopId = param.getEshopIdList().get(0);
        }
        request.setShopId(eshopId);
        request.setSpuId(param.getSpuId());
        request.setEshopProductPublishSchemas(param.getEshopProductPublishSchemas());
        return eshopProductPublishService.getItemDynamicFieldGaea(request);
    }

    @RequestMapping(value = "/getBaseMarkByCatId", method = RequestMethod.POST)
    @ApiOperation("根据三级类目id获取对应的特殊属性")
    public QueryMarkByCatIdResponse getBaseMarkByCatId(@RequestBody QueryPlatformInfoParam param) {
        QueryMarkByCatIdRequest request = new QueryMarkByCatIdRequest();
        request.setCatId(param.getCateId());
        BigInteger eshopId = BigInteger.ZERO;
        if (CollectionUtils.isNotEmpty(param.getEshopIdList())) {
            eshopId = param.getEshopIdList().get(0);
        }
        request.setShopId(eshopId);
        return eshopProductPublishService.getBaseMarkByCatId(request);
    }


    @RequestMapping(value = "/queryPlatformPropsData", method = RequestMethod.POST)
    @ApiOperation("查询平台属性列表")
    public QueryPropsDataResponse queryPlatformPropsData(@RequestBody QueryPlatformInfoParam param) {
        return bifrostPublishProductService.queryPropsData(param);
    }

    @RequestMapping(value = "/queryPlatformProductFeature", method = RequestMethod.POST)
    @ApiOperation("查询商品特殊属性列表")
    public QueryProductBizResponse queryPlatformProductFeature(@RequestBody QueryPlatformInfoParam param) {
        return bifrostPublishProductService.queryProductBiz(param);
    }

    @RequestMapping(value = "/queryEshopProductPublishCommon", method = RequestMethod.POST)
    @ApiOperation("编辑通用信息回显查询")
    public EshopProductPublishCommon queryEshopProductPublishCommon(@RequestBody QueryPublishInfoParam param) {
        return eshopProductPublishService.queryEshopProductPublishCommon(param);
    }

    @RequestMapping(value = "/queryEshopProductPublishCustom", method = RequestMethod.POST)
    @ApiOperation("编辑个性化平台信息回显")
    public List<EshopProductPublishCustomRequest> queryEshopProductPublishCustom(@RequestBody QueryPublishInfoParam param) {
        return eshopProductPublishService.queryEshopProductPublishCustom(param);
    }


    @RequestMapping(value = "/getPublishProgress", method = RequestMethod.POST)
    public PtypeDownloadTask getPublishProgress(@RequestBody EshopProductPublishRequest params) {
        return eshopProductPublishService.getPublishProgress(params);
    }

    @PostMapping(value = "/queryProductPublishLog")
    public PageResponse<EshopProductPublishLog> queryProductPublishLog(@RequestBody PageRequest<ProductPublishLogQueryParams> request) {
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        PageResponse<EshopProductPublishLog> query = LogService.query(request);
        if (query.getList() == null) {
            query.setList(new ArrayList<>());
        }
        return query;
    }

    @PostMapping(value = "/addLog")
    public void addLog(@RequestBody EshopProductPublishAddLogEntity request) {
        //todo
    }

    @PostMapping(value = "/queryPublishFieldsMapping")
    public List<PublishProductFieldsMapping> queryEshopPublishProductFieldsMapping(@RequestBody BaseRequest request) {
        return bifrostPublishProductService.queryEshopPublishProductFieldsMapping(request);
    }

    @PostMapping(value = "/queryPublishTabVisible")
    public HashMap<String, Boolean> queryPublishTabVisibleMapping() {
        HashMap<String, Boolean> productPublishSupport = saleBizConfig.getProductPublishSupport();
        //判断当前账号是否有对应的网店
        if (CollectionUtils.isEmpty(productPublishSupport)) {
            return productPublishSupport;
        }
        filterCurrentUserSupport(productPublishSupport);
        return productPublishSupport;
    }

    private void filterCurrentUserSupport(HashMap<String, Boolean> productPublishSupport) {
        BigInteger profileId = CurrentUser.getProfileId();
        Set<String> keySet = productPublishSupport.keySet();
        for (String key : keySet) {
            try {
                ShopType shopType = ShopType.getShopType(key);
                List<EshopInfo> listByShopType = eshopService.getEshopListByShopType(profileId, shopType.getCode());
                if (CollectionUtils.isEmpty(listByShopType)) {
                    productPublishSupport.put(key, false);
                }
            } catch (Exception ex) {
                productPublishSupport.put(key, false);
                logger.error("key【{}】初始化发品功能根据配 置过滤显示的网点类型时报错{}", key, ex.getMessage(), ex);
            }
        }
    }


    @RequestMapping(value = "/queryProductSchemaByCatId", method = RequestMethod.POST)
    @ApiOperation("查询产品信息匹配字段")
    public ItemDynamicFieldGaeaResponse queryProductSchemaByCatId(@RequestBody QueryPlatformInfoParam param) {
        ItemDynamicFieldGaeaRequest request = new ItemDynamicFieldGaeaRequest();
        request.setCateId(param.getCateId());
        request.setItemType(param.getItemType());
        request.setMarketType(param.getMarketType());
        BigInteger eshopId = BigInteger.ZERO;
        if (CollectionUtils.isNotEmpty(param.getEshopIdList())) {
            eshopId = param.getEshopIdList().get(0);
        }
        request.setShopId(eshopId);
        return eshopProductPublishService.queryProductSchemaByCatId(request);
    }

    @RequestMapping(value = "/matchProduct", method = RequestMethod.POST)
    @ApiOperation("查询产品信息匹配字段")
    public ItemDynamicFieldGaeaResponse matchProduct(@RequestBody MatchProductParam param) {
        MatchProductRequest request = new MatchProductRequest();
        BigInteger eshopId = BigInteger.ZERO;
        if (CollectionUtils.isNotEmpty(param.getEshopIdList())) {
            eshopId = param.getEshopIdList().get(0);
        }
        request.setShopId(eshopId);
        request.setPlatformParams(param.getPlatformParams());
        request.setCateId(param.getCateId());
        return eshopProductPublishService.matchProduct(request);
    }

    @PostMapping(value = "/queryPlatformAddress")
    public QueryPlatformAddressResponse queryPlatformAddress(@RequestBody QueryPlatformAddressRequest request) {
        return bifrostPublishProductService.queryPlatformAddress(request);
    }

    @PostMapping(value = "/querySubProps")
    public ItemDynamicFieldGaeaResponse querySubProps(@RequestBody QuerySubPropsRequest request) {
        return bifrostPublishProductService.querySubProps(request);
    }

    /**
     * 查询标品类目规则
     */
    @PostMapping(value = "/getCategoryRule")
    public GetCategoryRuleResponse getCategoryRule(@RequestBody GetCategoryRuleRequest request) {
        return bifrostPublishProductService.getCategoryRule(request);
    }

    /**
     * 通过类目、品牌查询标品的型号/货号列表
     */
    @PostMapping(value = "/getModelOrItemNumList")
    public GetModelOrItemNumListResponse getModelOrItemNumList(@RequestBody GetModelOrItemNumListRequest request) {
        return bifrostPublishProductService.getModelOrItemNumList(request);
    }

    /**
     * 标品查询
     */
    @PostMapping(value = "/standardProductSearch")
    public StandardProductSearchResponse standardProductSearch(@RequestBody StandardProductSearchRequest request) {
        return bifrostPublishProductService.standardProductSearch(request);
    }


    @RequestMapping(value = "/checkNeedPublishEshop", method = RequestMethod.POST)
    @ApiOperation("检查绑定的店铺是否符合发布条件")
    public List<EshopProductPublishErrorMessageResponse> checkNeedPublishEshop(@RequestBody CheckNeedPublishEshopRequest param) {
        return eshopProductPublishService.checkNeedPublishEshop(param);
    }

    @RequestMapping(value = "/checkIsEdit", method = RequestMethod.POST)
    @ApiOperation("检查能否编辑")
    public boolean checkIsEdit(@RequestBody EshopProductPublishReponse res) {
        return eshopProductPublishService.checkIsEdit(res.getBasePublishId());
    }

    @RequestMapping(value = "/buildPublishSchema", method = RequestMethod.POST)
    @ApiOperation("构建schema")
    public EshopProductBuildSchemaResponse buildPublishSchema(@RequestBody EshopProductPublishCustomerSpecilRequest request) {
        EshopProductPublishCustomRequest newcustomRequest = request.getNewProductCustomInfo();
        EshopBuildPublishSchemaRequest schemaRequest = new EshopBuildPublishSchemaRequest();
        BeanUtils.copyProperties(newcustomRequest, schemaRequest);
        if (CollectionUtils.isNotEmpty(newcustomRequest.getPics())) {
            schemaRequest.setMainImages(buildImages(newcustomRequest.getPics()));
        }

        if (CollectionUtils.isNotEmpty(newcustomRequest.getPics34())) {
            schemaRequest.setThreeFourImages(buildImages(newcustomRequest.getPics34()));
        }
        BigInteger eshopId = BigInteger.ZERO;
        if (CollectionUtils.isNotEmpty(newcustomRequest.getEshopIdList())) {
            schemaRequest.setShopId(newcustomRequest.getEshopIdList().get(0));
        }
        return bifrostPublishProductService.buildPublishSchema(schemaRequest);
    }

    private List<ProductPicInfo> buildImages(List<EshopProductPublishPic> pics) {
        if (CollectionUtils.isEmpty(pics)) {
            return new ArrayList<>();
        }
        List<ProductPicInfo> productPics = new ArrayList<>(pics.size());
        for (EshopProductPublishPic pic : pics) {
            ProductPicInfo productPic = new ProductPicInfo();
            productPic.setImageUrl(StringUtils.isNotEmpty(pic.getFileUrlPlatform()) ? pic.getFileUrlPlatform() : pic.getFileUrlErp());
            productPic.setImageFormatType(ImageFormatTypeEnum.getEnumByCode(pic.getFileUseType().getCode()));
            productPics.add(productPic);
        }
        return productPics;
    }


}
