package com.wsgjp.ct.sale.web.member;

import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.common.CustomResult;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.member.model.entity.store.SsVipScoreConfiguration;
import com.wsgjp.ct.sale.biz.member.service.ISsVipScoreConfigurationService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "会员积分策略")
@RequestMapping("${app.id}/member/scoreConfiguration")
@RestController
public class SsVipScoreConfigurationController {

    @Autowired
    private ISsVipScoreConfigurationService scoreConfigurationService;


    @GetMapping("/getScoreConfiguration")
    @WebLogs
    public SsVipScoreConfiguration getScoreConfiguration() {
        return scoreConfigurationService.getScoreConfiguration(null);
    }

    /**
     * 在IE上GET请求会被缓存，导致信息不能刷新，开一个post接口
     */
    @PostMapping("/getScoreConfiguration")
    @PermissionCheck(key = PermissionShopSale.MEMBER_POINTSCONFIGURATION_VIEW)
    public SsVipScoreConfiguration getScoreConfiguration1() {
        return scoreConfigurationService.getScoreConfiguration(null);
    }

    @PostMapping("/saveOrUpdate")
    @PermissionCheck(key = PermissionShopSale.MEMBER_POINTSCONFIGURATION_EDIT)
    public CustomResult saveOrUpdate(@RequestBody SsVipScoreConfiguration scoreConfiguration) {
        return scoreConfigurationService.saveOrUpdate(scoreConfiguration);
    }
}
