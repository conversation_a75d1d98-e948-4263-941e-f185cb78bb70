package com.wsgjp.ct.sale.web.member;


import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipStoreChangeDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipStoreConsumptionDTO;
import com.wsgjp.ct.sale.biz.member.model.vo.vip.VipStoreConsumptionResponse;
import com.wsgjp.ct.sale.biz.member.service.ISsCardAssertBillService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipAssertsChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;

@Api(tags = "会员资产")
@RestController
@RequestMapping("${app.id}/member/vipAsserts")
public class VipAssertsController {

    @Autowired
    private ISsVipAssertsChangeService ssVipAssertsChangeService;

    @Autowired
    private ISsCardAssertBillService  ssCardAssertBillService;

    @ApiOperation("储值消费")
    @PostMapping("/vipStoreConsumption")
    public VipStoreConsumptionResponse vipStoreConsumption(@RequestBody VipStoreConsumptionDTO dto) {
        return ssVipAssertsChangeService.vipStoreConsumption(dto);
    }

    @ApiOperation("储值退回")
    @PostMapping("/vipStoreChange")
    public void vipStoreChange(@RequestBody VipStoreChangeDTO dto) {
        ssVipAssertsChangeService.vipStoreChange(dto);
    }

    @ApiOperation("查询资产变动记录")
    @PostMapping("/selectAssertBill")
    public void selectAssertBill(@RequestBody BigInteger vchcode) {
        ssCardAssertBillService.selectAssertBill(vchcode);
    }

}
