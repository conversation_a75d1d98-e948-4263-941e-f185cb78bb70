package com.wsgjp.ct.sale.web.jarvis.response;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UploadResponse
 */
public class UploadResponse<T> extends BaseResponse {
    private T data;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <C> BaseResponse success(C t){
        UploadResponse<C> baseResponse = new UploadResponse<>();
        baseResponse.setCode("0");
        baseResponse.setData(t);
        return baseResponse;
    }

    public static BaseResponse success(){
        BaseResponse baseResponse = new UploadResponse<>();
        baseResponse.setCode("0");
        return baseResponse;
    }

    public static BaseResponse fail(String msg){
        BaseResponse baseResponse = new UploadResponse<>();
        baseResponse.setCode("-1");
        baseResponse.setMsg(msg);
        return baseResponse;
    }
}
