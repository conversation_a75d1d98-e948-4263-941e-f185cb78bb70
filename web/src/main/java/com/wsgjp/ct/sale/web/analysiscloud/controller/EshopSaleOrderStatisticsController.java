package com.wsgjp.ct.sale.web.analysiscloud.controller;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderDetailStatisticsEntity;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.page.PageSummary;
import com.wsgjp.ct.sale.biz.analysiscloud.entity.query.sale.EshopSaleOrderStatisticsQuery;
import com.wsgjp.ct.sale.biz.analysiscloud.service.EshopSaleOrderStatisticsDetailService;
import com.wsgjp.ct.sale.biz.analysiscloud.service.EshopSaleOrderStatisticsService;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryEShopParameter;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-02 14:54
 */
@Api(tags={"原单销售统计"})
@RestController
@RequestMapping("${app.id}/analysiscloud/eshopSaleOrderStatistics")
public class EshopSaleOrderStatisticsController {

    private EshopSaleOrderStatisticsService eshopSaleOrderStatisticsService;
    private EshopSaleOrderStatisticsDetailService eshopSaleOrderStatisticsDetailService;

    public EshopSaleOrderStatisticsController(EshopSaleOrderStatisticsService eshopSaleOrderStatisticsService,EshopSaleOrderStatisticsDetailService eshopSaleOrderStatisticsDetailService){
        this.eshopSaleOrderStatisticsService=eshopSaleOrderStatisticsService;
        this.eshopSaleOrderStatisticsDetailService=eshopSaleOrderStatisticsDetailService;
    }

    /**
     * 销售原单统计列表 商品维度
     * @param query
     * @return
     */
    @PostMapping("listEshopSaleOrderStatistics")
    public PageResponse<EshopSaleOrderStatisticsEntity> listEshopSaleOrderStatistics(@RequestBody PageRequest<EshopSaleOrderStatisticsQuery> query){
        BigInteger profileId = CurrentUser.getProfileId();
        query.getQueryParams().setSearchType(0);
        return eshopSaleOrderStatisticsService.list(query,profileId);
    }

    /**
     * 销售原单统计列表 商品维度 合计
     * @param query
     * @return
     */
    @PostMapping("listEshopSaleOrderStatistics/count")
    public PageSummary listEshopSaleOrderStatisticsSummary(@RequestBody PageRequest<EshopSaleOrderStatisticsQuery> query){
        BigInteger profileId = CurrentUser.getProfileId();
        query.getQueryParams().setSearchType(0);
        return eshopSaleOrderStatisticsService.summary(query,profileId);
    }
    /**
     * 销售原单统计列表 机构维度
     * @param query
     * @return
     */
    @PostMapping("listEshopSaleOrderShopStatistics")
    public PageResponse<EshopSaleOrderStatisticsEntity> listEshopSaleOrderShopStatistics(@RequestBody PageRequest<EshopSaleOrderStatisticsQuery> query){
        BigInteger profileId = CurrentUser.getProfileId();
        query.getQueryParams().setSearchShop(true);
        query.getQueryParams().setSearchType(1);
        return eshopSaleOrderStatisticsService.list(query,profileId);
    }

    /**
     * 销售原单统计列表 机构维度 合计
     * @param query
     * @return
     */
    @PostMapping("listEshopSaleOrderShopStatistics/count")
    public PageSummary listEshopSaleOrderShopStatisticsSummary(@RequestBody PageRequest<EshopSaleOrderStatisticsQuery> query){
        BigInteger profileId = CurrentUser.getProfileId();
        query.getQueryParams().setSearchShop(true);
        query.getQueryParams().setSearchType(1);
        return eshopSaleOrderStatisticsService.summary(query,profileId);
    }
    /**
     * 销售原单统计列表 机构维度
     * @param query
     * @return
     */
    @PostMapping("listEshopSaleOrderBtypeStatistics")
    public PageResponse<EshopSaleOrderStatisticsEntity> listEshopSaleOrderBtypeStatistics(@RequestBody PageRequest<EshopSaleOrderStatisticsQuery> query){
        BigInteger profileId = CurrentUser.getProfileId();
        query.getQueryParams().setSearchType(2);
        return eshopSaleOrderStatisticsService.list(query,profileId);
    }

    /**
     * 销售原单统计列表 机构维度 合计
     * @param query
     * @return
     */
    @PostMapping("listEshopSaleOrderBtypeStatistics/count")
    public PageSummary listEshopSaleOrderBtypeStatisticsSummary(@RequestBody PageRequest<EshopSaleOrderStatisticsQuery> query){
        BigInteger profileId = CurrentUser.getProfileId();
        query.getQueryParams().setSearchType(2);
        return eshopSaleOrderStatisticsService.summary(query,profileId);
    }
    /**
     * 销售原单统计列表 机构维度 明细
     * @param query
     * @return
     */
    @PostMapping("listEshopSaleOrderShopDetailStatistics")
    public PageResponse<EshopSaleOrderDetailStatisticsEntity> listEshopSaleOrderShopDetailStatistics(@RequestBody PageRequest<EshopSaleOrderStatisticsQuery> query){
        BigInteger profileId = CurrentUser.getProfileId();
        return eshopSaleOrderStatisticsDetailService.list(query,profileId);
    }

    /**
     * 销售原单统计列表 机构维度 明细 合计
     * @param query
     * @return
     */
    @PostMapping("listEshopSaleOrderShopDetailStatistics/count")
    public PageSummary listEshopSaleOrderShopDetailStatisticsSummary(@RequestBody PageRequest<EshopSaleOrderStatisticsQuery> query){
        BigInteger profileId = CurrentUser.getProfileId();
        return eshopSaleOrderStatisticsDetailService.summary(query,profileId);
    }

    /**
     * 获取销售机信息
     * @param type
     * @return
     */
    @PostMapping("getEshop")
    public List<EshopInfo> getEshop(@RequestBody int type)
    {
        QueryEShopParameter queryEShopParameter = new QueryEShopParameter();
        queryEShopParameter.setQueryVirtual(true);
        CommonUtil.initLimited(queryEShopParameter);
        List<Integer> ocategorys = new ArrayList<>();
        ocategorys.add(0);
        ocategorys.add(1);
        ocategorys.add(2);
        ocategorys.add(3);
        queryEShopParameter.setOcategorys(ocategorys);
        EshopService service = GetBeanUtil.getBean(EshopService.class);
        List<EshopInfo> result = service.getEshopByShopTypes(queryEShopParameter);
        if(type==1)
        {
            EshopInfo defaultEntity = new EshopInfo();
            defaultEntity.setOtypeId(BigInteger.valueOf(0));
            defaultEntity.setFullname("全部");
            result.add(0,defaultEntity);
        }
        return result;
    }
}
