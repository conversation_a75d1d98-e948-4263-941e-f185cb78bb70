package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopDeliverOrderService;
import com.wsgjp.ct.sale.platform.dto.order.entity.EshopDeliverOrderEntity;
import com.wsgjp.ct.sale.platform.entity.request.order.DownloadOrderRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.OrderFeedBackRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.OrderFeedbackResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 发货单相关接口
 * @author: lj
 * @create: 2022-03-11
 **/
@Api(tags = "网店发货单相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/deliverOrder")
public class BifrostEshopDeliverOrderController {

    private final BifrostEshopDeliverOrderService deliverOrderService;

    public BifrostEshopDeliverOrderController(BifrostEshopDeliverOrderService deliverOrderService) {
        this.deliverOrderService = deliverOrderService;
    }

    /**
     * 获取线上发货单
     *
     * @param request
     * @return
     */
    @ApiOperation("获取线上发货单")
    @PostMapping("getDeliverOrders")
    public List<EshopDeliverOrderEntity> getDeliverOrders(@RequestBody DownloadOrderRequest request) {
        return deliverOrderService.getDeliverOrders(request);
    }

    /**
     * 发货单自动寻仓结果反馈
     *
     * @param request
     * @return
     */
    @ApiOperation("发货单自动寻仓结果反馈")
    @PostMapping("orderFeedback")
    public List<OrderFeedbackResponse> orderFeedback(@RequestBody OrderFeedBackRequest request) {
        return deliverOrderService.orderFeedback(request);
    }
}
