package com.wsgjp.ct.sale.web.jarvis.Entity;

public class FieldSource {
    private String key;
    private String value;
    private boolean enabled;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public String toString() {
        return String.format("FieldSource{key='%s', value='%s', enabled=%s}", key, value, enabled);
    }
}

