package com.wsgjp.ct.sale.web.eshoporder.entity.response;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EnumState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.support.context.CurrentUser;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 15/6/2021 上午 9:26
 */
public class StockRulePageData {
	private BigInteger profileId;
	private List<EnumState> ruleTypeSource;
	private List<EnumState> ruleStateSource;
	private List<Stock> ktypeSource;

	public BigInteger getProfileId() {
		return CurrentUser.getProfileId();
	}

	public List<EnumState> getRuleTypeSource() {
		return ruleTypeSource;
	}

	public void setRuleTypeSource(List<EnumState> ruleTypeSource) {
		this.ruleTypeSource = ruleTypeSource;
	}

	public List<EnumState> getRuleStateSource() {
		return ruleStateSource;
	}

	public void setRuleStateSource(List<EnumState> ruleStateSource) {
		this.ruleStateSource = ruleStateSource;
	}

	public List<Stock> getKtypeSource() {
		return ktypeSource;
	}

	public void setKtypeSource(List<Stock> ktypeSource) {
		this.ktypeSource = ktypeSource;
	}
}
