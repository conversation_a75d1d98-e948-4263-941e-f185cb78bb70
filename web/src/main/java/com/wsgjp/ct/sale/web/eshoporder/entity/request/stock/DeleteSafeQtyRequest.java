package com.wsgjp.ct.sale.web.eshoporder.entity.request.stock;

import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.SafeQtyDeleteTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.SafeQtyTypeEnum;
import com.wsgjp.ct.support.context.CurrentUser;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR> 2023/8/14 16:17
 */
public class DeleteSafeQtyRequest {
    private BigInteger profileId;
    private List<BigInteger> configIdList;
    private SafeQtyTypeEnum safeQtyType;
    private SafeQtyDeleteTypeEnum deleteType;

    public BigInteger getProfileId() {
        if (profileId == null) {
            profileId = CurrentUser.getProfileId();
        }
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public List<BigInteger> getConfigIdList() {
        return configIdList;
    }

    public void setConfigIdList(List<BigInteger> configIdList) {
        this.configIdList = configIdList;
    }

    public SafeQtyTypeEnum getSafeQtyType() {
        return safeQtyType;
    }

    public void setSafeQtyType(SafeQtyTypeEnum safeQtyType) {
        this.safeQtyType = safeQtyType;
    }

    public SafeQtyDeleteTypeEnum getDeleteType() {
        if (deleteType == null) {
            deleteType = SafeQtyDeleteTypeEnum.NORMAL;
        }
        return deleteType;
    }

    public void setDeleteType(SafeQtyDeleteTypeEnum deleteType) {
        this.deleteType = deleteType;
    }
}
