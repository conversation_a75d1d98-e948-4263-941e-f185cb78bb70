package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sis.client.SisClient;
import com.wsgjp.ct.sis.client.common.SensitiveFieldEnum;
import com.wsgjp.ct.sis.client.entity.DecryptShopResponse;
import com.wsgjp.ct.sis.client.entity.FieldDecryptRequest;
import com.wsgjp.ct.sis.client.entity.FieldDecryptResponse;
import com.wsgjp.ct.sis.client.entity.SensitiveOrder;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequestMapping("/${app.id}/jarvis/sensitive")
@RestController
@Api(value = "敏感信息解密接口")
public class JarvisSensitiveController {

    @PostMapping("batchDecrypt")
    @ApiOperation(value = "批量解密接口")
    public Map<BigInteger, DecryptShopResponse> batchDecrypt(@ApiParam(value = "订单列表") @RequestBody List<SensitiveOrder> request) throws Exception {
        return SisClient.decrypt(request);
    }

    @PostMapping("decryptField")
    public List<FieldDecryptResponse> decryptField(@RequestBody FieldDecryptRequest request, HttpServletRequest httpServletRequest) throws Exception {
        request.setErpHost(CurrentUser.getClientDomain());
        return SisClient.decryptFields(request);
    }

    @PostMapping("listSensitiveFields")
    public List<SensitiveFieldEnum> listSensitiveFields() {
        return Arrays.asList(SensitiveFieldEnum.values());
    }

}
