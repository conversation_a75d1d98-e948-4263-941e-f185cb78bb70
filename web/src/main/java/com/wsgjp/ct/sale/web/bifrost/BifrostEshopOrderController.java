package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.biz.bifrost.entity.request.GetPaymentRecordRequest;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.OrderDownRecordDto;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.QueryOrderRecordParam;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopOrderDownloadRecordService;
import com.wsgjp.ct.sale.platform.entity.request.download.DownloadFileRequest;
import com.wsgjp.ct.sale.platform.entity.request.logistics.GetOrderWayBillInfoRequest;
import com.wsgjp.ct.sale.platform.entity.request.logistics.GetTransportNoRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.*;
import com.wsgjp.ct.sale.platform.entity.request.plugin.CommonRequest;
import com.wsgjp.ct.sale.platform.entity.request.plugin.GetPlatOrderUrlRequest;
import com.wsgjp.ct.sale.platform.entity.request.stock.OutOfStockCallBackRequest;
import com.wsgjp.ct.sale.platform.entity.response.BaseResponse;
import com.wsgjp.ct.sale.platform.entity.response.NormalResponse;
import com.wsgjp.ct.sale.platform.entity.response.download.DownloadFileResponse;
import com.wsgjp.ct.sale.platform.entity.response.download.DownloadFileResult;
import com.wsgjp.ct.sale.platform.entity.response.logistics.GetOrderWayBillInfoResponse;
import com.wsgjp.ct.sale.platform.entity.response.logistics.GetTransportNoResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.*;
import com.wsgjp.ct.sale.platform.entity.response.tmc.CancelOrderResponse;
import com.wsgjp.ct.sale.platform.sdk.service.EshopOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.starter.web.annotation.NotWrapper;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;




@Api(tags = "网店订单相关接口")
@RestController
@RequestMapping("${app.id}/bifrost/order")
public class BifrostEshopOrderController {

    private final BifrostEshopOrderService orderService;

    private final EshopOrderService eshopOrderService;
    private final EshopOrderDownloadRecordService recordService;

    public BifrostEshopOrderController(BifrostEshopOrderService orderService, EshopOrderService eshopOrderService, EshopOrderDownloadRecordService recordService) {
        this.orderService = orderService;
        this.eshopOrderService = eshopOrderService;
        this.recordService = recordService;
    }

    @ApiOperation("全量下载订单列表")
    @PostMapping("downloadByCreateTime")
    public OrderDownloadResponse downloadOrdersByCreateTime(@RequestBody DownloadOrderRequest request) {
        return orderService.downloadOrdersByCreateTime(request);
    }

    @ApiOperation("增量下载订单列表")
    @PostMapping("downloadByModifyTime")
    public OrderDownloadResponse downloadOrderByModifyTime(@RequestBody DownloadOrderIncreaseRequest request) {
        return orderService.downloadOrdersByModifyTime(request);
    }

    @ApiOperation("通过Id下载订单列表")
    @PostMapping("downloadByTradeIds")
    public OrderDownloadResponse downloadOrderByTradeIds(@RequestBody DownloadOrderByIdRequest request) {
        return orderService.downloadOrderByTradeIds(request);
    }

    @ApiOperation("订单关闭")
    @PostMapping("close")
    public CloseOrderResponse closeOrder(@RequestBody CloseOrderRequest request) {
        return orderService.closeOrder(request);
    }

    @ApiOperation("关闭原因")
    @PostMapping("closeReason")
    public String closeReason(@RequestBody CommonRequest request) {
        return orderService.getCloseReason(request);
    }

    @ApiOperation("修改备注")
    @PostMapping("modifyOrderMemo")
    public ModifyOrderMemoResponse modifyOrderMemo(@RequestBody ModifyMemoRequest request) {
        ModifyOrderMemoResponse response = orderService.modifyOrderMemo(request);
        return response;
    }

    @ApiOperation("修改订单收货人地址")
    @PostMapping("modifyOrderReceiver")
    public NormalResponse modifyOrderReceiver(@RequestBody ModifyOrderReceiverRequest request) {
        NormalResponse response = orderService.modifyOrderReceiver(request);
        return response;
    }

    @ApiOperation("获取跳转平台订单页面URL链接")
    @PostMapping("getPlatformOrderUrl")
    public String getPlatformOrderUrl(@RequestBody GetPlatOrderUrlRequest request) {
        String platformOrderUrl = orderService.getPlatformOrderUrl(request);
        return platformOrderUrl;
    }

    @ApiOperation("获取可合并的订单列表")
    @PostMapping("getMergeTradeIds")
    public List<String> getMergeTradeIds(@RequestBody MergeTradeRequest request) {
        return orderService.getMergeTradeIds(request);
    }

    @ApiOperation("获取账单(交易记录)")
    @PostMapping("getOnlinePaymentRecordReport")
    public OnlinePaymentRecordResponse getOnlinePaymentRecordReport(@RequestBody GetPaymentRecordRequest request) {
        return orderService.getOnlinePaymentRecordReport(request);
    }

    /**
     * 订单操作(将订单操作信息同步到平台)
     *
     * @param request
     * @return
     */
    @ApiOperation("订单操作(将订单操作信息同步到平台)")
    @PostMapping("operationOrder")
    public OperationResponse operationOrder(OperationOrderRequest request) {
        return orderService.operationOrder(request);
    }

    /**
     * 修改订单商品
     *
     * @param request
     * @return
     */
    @ApiOperation("修改订单商品")
    @PostMapping("modifyOrderItem")
    public NormalResponse modifyOrderItem(ModifyOrderItemRequest request) {
        return orderService.modifyOrderItem(request);
    }

    /**
     * 回告接单
     *
     * @param request
     * @return
     */
    @ApiOperation("回告接单")
    @PostMapping("orderReceiveFeedback")
    public BaseResponse orderReceiveFeedback(OrderReceiveFeedbackRequest request) {
        return orderService.orderReceiveFeedback(request);
    }

    /**
     * 订单缺货回告
     *
     * @param request
     * @return
     */
    @ApiOperation("订单缺货回告")
    @PostMapping("outOfStockCallback")
    public BaseResponse outOfStockCallback(OutOfStockCallBackRequest request) {
        return orderService.outOfStockCallback(request);
    }

    /**
     * 取消订单tmc消息构建为去平台化实体
     *
     * @param request
     * @return
     */
    @ApiOperation("取消订单tmc消息构建为去平台化实体")
    @PostMapping("cancelOrder")
    public CancelOrderResponse cancelOrder(PushMessageRequest request) {
        return orderService.cancelOrder(request);
    }

    /**
     * 分单
     *
     * @param request
     * @return
     */
    @ApiOperation("分单")
    @PostMapping("distribute")
    public DistributeOrderResponse distribute(DistributeOrderRequest request) {
        return orderService.distribute(request);
    }

    /**
     * 取消分单
     *
     * @param request
     * @return
     */
    @ApiOperation("取消分单")
    @PostMapping("cancelDistribute")
    public CancelDistributeResponse cancelDistribute(CancelDistributeRequest request) {
        return orderService.cancelDistribute(request);
    }

    @ApiOperation("延长虚拟号有效期")
    @PostMapping("extendSecretDays")
    public SecretExtendResponse extendSecretDays(SecretExtendRequest request) {
        return orderService.extendSecretDays(request);
    }

    /**
     * 根据核销码查询订单
     */
    @ApiOperation("根据核销码查询订单")
    @PostMapping("queryOrderBySelfFetchCode")
    public QueryOrderBySelfFetchCodeResponse queryOrderBySelfFetchCode(@RequestBody QueryOrderBySelfFetchCodeRequest request) {
        return orderService.queryOrderBySelfFetchCode(request);
    }

    /**
     * 获取运单号(快递单号)
     *
     * @param request GetTransportNoRequest
     * @return GetTransportNoResponse
     */
    @ApiOperation("获取运单号(快递单号)")
    @PostMapping("getTransportNo")
    public GetTransportNoResponse getTransportNo(@RequestBody GetTransportNoRequest request) {
        return orderService.getTransportNo(request);
    }

    /**
     * 根据运单号查询运单详情
     *
     * @param request GetOrderWayBillInfoRequest
     * @return GetOrderWayBillInfoResponse
     */
    @ApiOperation("根据运单号查询运单详情")
    @PostMapping("getOrderWayBillInfo")
    GetOrderWayBillInfoResponse getOrderWayBillInfo(@RequestBody GetOrderWayBillInfoRequest request) {
        return orderService.getOrderWayBillInfo(request);
    }

    @ApiOperation("骑手轨迹上传")
    @PostMapping("locationSync")
    LocationSyncResponse locationSync(@RequestBody LocationSyncRequest request) {
        return eshopOrderService.locationSync(request);
    }

    @ApiOperation("自配送骑手状态上传")
    @PostMapping("selfDeliveryStateSync")
    SelfDeliveryStateSyncResponse selfDeliveryStateSync(@RequestBody SelfDeliveryStateSyncRequest request) {
        return eshopOrderService.selfDeliveryStateSync(request);
    }

    @ApiOperation("下载文件")
    @PostMapping("downloadFile")
    public ResponseEntity<InputStreamResource> downloadFile(@RequestBody DownloadFileRequest request) throws IOException {
        DownloadFileResponse downloadFileResponse = eshopOrderService.downloadFile(request);
        if(!downloadFileResponse.getSuccess()){
            throw new RuntimeException(downloadFileResponse.getMessage());
        }
        List<DownloadFileResult> fileInfos = downloadFileResponse.getFileInfos();
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
            for (DownloadFileResult fileInfo : fileInfos) {
                List<Byte> fileData = fileInfo.getFileData();
                ByteArrayOutputStream _byteArrayOutputStream = new ByteArrayOutputStream();
                for (Byte b : fileData) {
                    _byteArrayOutputStream.write(b);
                }
                InputStream inputStream = new ByteArrayInputStream(_byteArrayOutputStream.toByteArray());

                ZipEntry zipEntry = new ZipEntry(fileInfo.getFileName());
                zipOutputStream.putNextEntry(zipEntry);
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) >= 0) {
                    zipOutputStream.write(buffer, 0, length);
                }

                zipOutputStream.closeEntry();
                inputStream.close();
            }
        }

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=files.zip");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

        return ResponseEntity.ok()
                .headers(headers)
                .body(new InputStreamResource(new ByteArrayInputStream(byteArrayOutputStream.toByteArray())));
    }

    @PostMapping("queryOrderDownloadRecord")
    @NotWrapper
    public List<OrderDownRecordDto> queryOrderDownloadRecord(@RequestBody QueryOrderRecordParam param){
        return recordService.queryOrderDownloadRecord(param);
    }
}
