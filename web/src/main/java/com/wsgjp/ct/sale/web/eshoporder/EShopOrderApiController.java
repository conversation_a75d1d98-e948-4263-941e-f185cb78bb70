package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.bill.core.handle.entity.enums.Vchtypes;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.CloseOrderRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.QuerySupportWriteOffRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.SaveEshopAndAuthRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.UpdateEshopAuthRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.CloseOrderResponse;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.SaveEshopAndAuthResponse;
import com.wsgjp.ct.sale.biz.eshoporder.util.AssertUtils;
import com.wsgjp.ct.sale.biz.jarvis.bill.config.PubBillSettings;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.LogLevelEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundPayStatus;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundSaleProcessEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.OrderCreateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetailBatch;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryRefundParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.ReceiveCheckInParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ManualOrderResponse;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderBaseInfoMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.EshopOrderMiddleGroundService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderManualService;
import com.wsgjp.ct.sale.biz.eshoporder.service.platformcheck.PlatformCheckService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopOrderEshopRefundService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopRefundReceiveCheckInService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.platform.factory.middlegroundfxs.sdk.request.DecryptMiddleGroundOrderRequest;
import com.wsgjp.ct.sale.platform.factory.middlegroundfxs.sdk.response.DecryptMiddleGroundOrderResponse;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.order.CreateOrderDetailEntity;
import com.wsgjp.ct.sale.web.eshoporder.entity.request.order.CreateOrderParameter;
import com.wsgjp.ct.support.business.Money;
import com.wsgjp.ct.support.business.MoneyUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "网店提供给外部使用的接口")
@RequestMapping("${app.id}/eshoporder/eshopOrderApi")
@RestController
public class EShopOrderApiController {
	private final EshopOrderEshopRefundService refundService;
	private final EshopSaleOrderManualService manualService;
	private final PlatformCheckService platformCheckService;
	private final EshopService eshopService;
	private final EshopOrderMiddleGroundService eshopOrderMiddleGroundService;
	private static final Logger LOGGER = LoggerFactory.getLogger(EShopOrderApiController.class);

	private final EshopRefundReceiveCheckInService checkInService;
	private final EshopOrderBaseInfoMapper baseInfoMapper;

	public EShopOrderApiController(EshopOrderEshopRefundService refundService, EshopSaleOrderManualService manualService, PlatformCheckService platformCheckService, EshopService eshopService, EshopOrderMiddleGroundService eshopOrderMiddleGroundService, EshopRefundReceiveCheckInService checkInService, EshopOrderBaseInfoMapper baseInfoMapper) {
		this.refundService = refundService;
		this.manualService = manualService;
		this.platformCheckService = platformCheckService;
		this.eshopService = eshopService;
		this.eshopOrderMiddleGroundService = eshopOrderMiddleGroundService;
		this.checkInService = checkInService;
		this.baseInfoMapper = baseInfoMapper;
	}

	@PostMapping(value = "/checkBillDeletedEnable")
	@ApiOperation(value = "检查单据是否可删除")
	@ApiImplicitParam(name = "request", value = "单据检查信息")
	public BillCheckResponse checkBillDeletedEnable(@RequestBody BillCheckRequest request) {
		BillCheckResponse response = new BillCheckResponse();
		try {
			if (request == null || request.getSourceId() == null) {
				response.setDeletedEnable(false);
				response.setMessage("sourceId为空，请检查！");
				return response;
			}
			LOGGER.error("====== 检查单据是否可删除 ========="+ JsonUtils.toJson(request));
			isDeletable(request, response);
		} catch (Exception ex) {
			response.setDeletedEnable(false);
			String msg = String.format("检查是否可删除单据出错：%s", ex.getMessage());
			if (request != null && request.getBillCreateType() != null) {
				msg = String.format("%s检查是否可删除单据出错：%s", request.getBillCreateType().getName(), ex.getMessage());
			}
			response.setMessage(msg);
		}
		return response;

	}

	@PostMapping(value = "/checkBillEditEnable")
	@ApiOperation(value = "检查单据是否可修改")
	@ApiImplicitParam(name = "request", value = "单据检查信息")
	public BillCheckResponse checkBillEditEnable(@RequestBody BillCheckRequest request) {
		BillCheckResponse response = new BillCheckResponse();

		try {
			if (request == null || request.getSourceId() == null) {
				response.setEditEnable(false);
				response.setMessage("sourceId为空，请检查！");
				return response;
			}
			isEditable(request, response);
		} catch (Exception ex) {
			response.setEditEnable(false);
			String msg = String.format("检查是否可修改单据出错：%s", ex.getMessage());
			if (request != null && request.getBillCreateType() != null) {
				msg = String.format("%s检查是否可修改单据出错：%s", request.getBillCreateType().getName(), ex.getMessage());
			}
			response.setMessage(msg);
		}
		return response;

	}


	private void isEditable(BillCheckRequest request, BillCheckResponse response) {
		switch (request.getBillCreateType()) {
			case FROM_ORDER_REFUND:
				refundEditable(request, response);
				break;
			case FROM_ORDER_REFUND_CHECKIN:
				dealReceiveCheckIn(request, response, true);
				break;
			default:
				platformCheckCanOperate(request, response);
		}
	}

	private void isDeletable(BillCheckRequest request, BillCheckResponse response) {
		switch (request.getBillCreateType()) {
			case FROM_ORDER_REFUND:
				refundDeletable(request, response);
				break;
			case FROM_ORDER_REFUND_CHECKIN:
				dealReceiveCheckIn(request, response, false);
				break;
			default:
				platformCheckCanOperate(request, response);
		}
	}

	private void refundEditable(BillCheckRequest request, BillCheckResponse response) {
		EshopRefundEntity refund = getRefundInfo(request.getProfileId(), request.getSourceId());
		if (refund == null) {
			throw new RuntimeException("查询不到相关售后单，请检查sourceId");
		}
		response.setSourceNumber(refund.getTradeRefundOrderNumber());
		response.setEditEnable(false);
	}

	private boolean refundDeletable(BillCheckRequest request, BillCheckResponse response) {
		EshopRefundEntity refund = getRefundInfo(request.getProfileId(), request.getSourceId());
		if (refund == null) {
			return true;
		}
		String refundNumber = refund.getTradeRefundOrderNumber();
		response.setSourceNumber(refundNumber);
		boolean canDelete = false;
		Vchtypes vchtype = request.getVchtype();
		switch (vchtype) {
			case SaleBackBill:
				canDelete = true;
				break;
			case SaleBill:
				RefundSaleProcessEnum refundProcessState = refund.getRefundProcessState();
				canDelete = refundProcessState.equals(RefundSaleProcessEnum.PROCESSED);
				break;
			case ExpenseBill:
			case PaymentBill:
				RefundPayStatus payState = refund.getPayState();
				canDelete = payState.equals(RefundPayStatus.PAYED);
				break;
			default:
				break;
		}
		return canDelete;
	}


	private boolean dealReceiveCheckIn(BillCheckRequest request, BillCheckResponse response, boolean edit) {
		ReceiveCheckInParameter parameter = new ReceiveCheckInParameter();
		parameter.setVchcode(request.getSourceId());
		List<EshopRefundReceiveCheckInEntity> checkInEntities = checkInService.queryRefundReceiveCheckInListNEWNEW(parameter);
		if (checkInEntities == null || checkInEntities.size() == 0) {
			throw new RuntimeException("没有对应的收货登记！");
		}
		EshopRefundReceiveCheckInEntity entity = checkInEntities.get(0);
		response.setSourceNumber(entity.getCheckinNumber());
		if (edit) {
			response.setEditEnable(false);
			response.setMessage(String.format("该单据由收货登记(单号：%s)生成，不允许修改。", entity.getCheckinNumber()));
			return false;
		}
		return dealDeleteReceiveCheckIn(request, response, entity, parameter);
	}


	private boolean dealDeleteReceiveCheckIn(BillCheckRequest request, BillCheckResponse response, EshopRefundReceiveCheckInEntity entity, ReceiveCheckInParameter parameter) {
		parameter.setVchtype(Vchtypes.SingleOutStockBill);
		boolean flag = false;
		switch (request.getVchtype()) {
			case SingleInStockBill: {
				parameter.setOtherStockInVchcode(request.getBillId());
				List<EshopRefundReceiveCheckInRelationEntity> relationEntities = checkInService.getLossRelationsByStockInVchcode(parameter);
				if (relationEntities == null || relationEntities.size() == 0) {
					flag = true;
					response.setDeletedEnable(flag);
					return flag;
				}
				List<String> lossNumbers = relationEntities.stream().map(EshopRefundReceiveCheckInRelationEntity::getTargetBillNumber).collect(Collectors.toList());
				response.setMessage(String.format("该单据由收货登记(单号：%s)生成,且已进行报损(单号：%s)，请先删除报损单再删除此单据。", entity.getCheckinNumber(), StringUtils.join(lossNumbers.toArray(), ",")));
				flag = false;
				response.setDeletedEnable(flag);
				break;
			}
			case StockLossBill: {
				response.setMessage(String.format("该单据由收货登记(单号：%s)生成,请确认是否删除。", entity.getCheckinNumber()));
				flag = true;
				response.setDeletedEnable(flag);
				break;
			}
			default:
				flag = true;
				response.setDeletedEnable(flag);
				break;
		}
		return flag;
	}

	private EshopRefundEntity getRefundInfo(BigInteger profileId, BigInteger vchcode) {
		QueryRefundParameter parameter = new QueryRefundParameter();
		parameter.setProfileId(profileId);
		parameter.setRefundOrderId(vchcode);
		return refundService.getRefundByRefundIdAndProfileId(parameter);
	}

	private void platformCheckCanOperate(BillCheckRequest request, BillCheckResponse response) {
		String tradeId = platformCheckService.queryTradeIdByVchcode(request.getProfileId(), request.getSourceId());
		if (StringUtils.isEmpty(tradeId)) {
			throw new RuntimeException("查询不到相关订单，请检查sourceId");
		}
		response.setSourceNumber(tradeId);
		switch (request.getBillCreateType()) {
			case FROM_ORDER_PAYMENT_FLOW: {
				response.setEditEnable(false);
				response.setDeletedEnable(true);
				break;
			}
			case FROM_ORDER_RECONCILIATION: {
				response.setEditEnable(true);
				response.setDeletedEnable(true);
				break;
			}
		}
	}

	@PostMapping(value = "/saveOrder")
	@ApiOperation(value = "手工开单接口")
	@ApiImplicitParam(name = "request", value = "手工开单接口")
	public void saveEshopOrder(@RequestBody CreateOrderParameter parameter) {
		CommonUtil.doLogByEnable(LOGGER, LogLevelEnum.ERROR,String.format("profileId:%s,saveOrder:%s",CurrentUser.getProfileId(),JsonUtils.toJson(parameter)));
		AssertUtils.doAssert (parameter.getOtypeId() == null || parameter.getOtypeId().compareTo(BigInteger.ZERO) == 0 , "网店信息错误");
		AssertUtils.doAssert (parameter.getBtypeId() == null || parameter.getBtypeId().compareTo(BigInteger.ZERO) == 0 , "往来单位信息错误");
		AssertUtils.doAssert (parameter.getKtypeId() == null || parameter.getKtypeId().compareTo(BigInteger.ZERO) == 0 , "发货仓库信息错误");
		AssertUtils.doAssert (checkEtypeEmpty(parameter), "经手人信息错误");
		AssertUtils.doAssert (CollectionUtils.isEmpty(parameter.getDetailList()), "订单明细信息为空");
		Otype otype = eshopService.getOtypeById(parameter.getOtypeId());
		if (otype == null || otype.isDeleted()) {
			throw new RuntimeException("网店已经被删除，保存订单失败！");
		}
		EshopSaleOrderEntity saleOrderEntity = toSaleOrder(parameter);
		ManualOrderResponse response = manualService.insertSaleOrder(saleOrderEntity, otype);
		if (!response.isSuccess()) {
			throw new RuntimeException(response.getMessage());
		}
	}

	private boolean checkEtypeEmpty(CreateOrderParameter parameter) {
		List<String> keys = new ArrayList<>();
		keys.add("enabledDefaultEtype");
		List<PubBillSettings> pubBillSettings = baseInfoMapper.getPubBillSettings(CurrentUser.getProfileId(), keys);
		if (CollectionUtils.isEmpty(pubBillSettings)){
			return false;
		}
		int stoped = pubBillSettings.get(0).getStoped();
		//0是开启，1是停用
		if (stoped == 0 && (parameter.getEtypeId() == null || parameter.getEtypeId().compareTo(BigInteger.ZERO) == 0)){
			return true;
		}
		return false;
	}

	private EshopSaleOrderEntity toSaleOrder(CreateOrderParameter parameter) {
		EshopSaleOrderEntity saleOrderEntity = new EshopSaleOrderEntity();
		saleOrderEntity.setTradeOrderId(parameter.getTradeId());
		saleOrderEntity.setCreateType(OrderCreateType.INPUT);
		saleOrderEntity.setKtypeId(parameter.getKtypeId());
		saleOrderEntity.setProfileId(CurrentUser.getProfileId());
		saleOrderEntity.setOtypeId(parameter.getOtypeId());
		//todo 查询etype信息获取dtypeId
		saleOrderEntity.setEtypeId(parameter.getEtypeId());
		Etype etype = baseInfoMapper.getEtypeById(CurrentUser.getProfileId(), parameter.getEtypeId());
		if (etype!=null)
		{
			saleOrderEntity.setDtypeId(etype.getDtypeId());
		}
		saleOrderEntity.setBtypeId(parameter.getBtypeId());
		saleOrderEntity.setSellerMemo(parameter.getSellerMessage());
		saleOrderEntity.setBuyerMessage(parameter.getBuyerMessage());
		saleOrderEntity.setRemark("【APP生单】" + (StringUtils.isEmpty(parameter.getRemark()) ? "" : parameter.getRemark()));
		saleOrderEntity.setLocalFreightName(parameter.getLogisticsCompany());
		saleOrderEntity.setLocalFreightBillNo(parameter.getLogisticsBillNo());
		saleOrderEntity.setLocalFreightCode(parameter.getLogisticsCode());
		buildOrderReceiver(saleOrderEntity, parameter);
		buildSaleOrderDetail(saleOrderEntity, parameter);
		return saleOrderEntity;
	}

	private void buildOrderReceiver(EshopSaleOrderEntity saleOrder, CreateOrderParameter parameter) {
		saleOrder.setEshopBuyer(new EshopBuyer());
		saleOrder.getEshopBuyer().setCustomerReceiver(parameter.getReceiver());
		saleOrder.getEshopBuyer().setCustomerShopAccount(parameter.getBuyerAccount());
		saleOrder.getEshopBuyer().setCustomerReceiverAddress(parameter.getAddress());
		saleOrder.getEshopBuyer().setCustomerReceiverProvince(parameter.getProvince());
		saleOrder.getEshopBuyer().setCustomerReceiverCity(parameter.getCity());
		saleOrder.getEshopBuyer().setCustomerReceiverDistrict(parameter.getDistrict());
		saleOrder.getEshopBuyer().setCustomerReceiverTown(parameter.getTown());
		saleOrder.getEshopBuyer().setCustomerReceiverMobile(parameter.getMobile());
	}

	private void buildSaleOrderDetail(EshopSaleOrderEntity saleOrder, CreateOrderParameter parameter) {
		List<EshopSaleOrderDetail> orderDetails = new ArrayList<>();
		List<CreateOrderDetailEntity> detailList = parameter.getDetailList();
		if (CollectionUtils.isEmpty(detailList)) {
			throw new RuntimeException("参数detailList不能为空");
		}
		for (CreateOrderDetailEntity entity : detailList) {
			if (entity.getQty().compareTo(BigDecimal.ZERO) == 0) {
				throw new RuntimeException("订单明细数量不能为0");
			}
			EshopSaleOrderDetail detail = new EshopSaleOrderDetail();
			detail.setComboRow(entity.isComboRow());
			if(detail.isComboRow()){
				detail.setId(entity.getComboRowId());
			}
			detail.setProfileId(saleOrder.getProfileId());
			detail.setKtypeId(saleOrder.getKtypeId());
			detail.setOtypeId(saleOrder.getOtypeId());
			detail.setSkuId(entity.getSkuId());
			detail.setPtypeId(entity.getPtypeId());
			detail.setPcategory(entity.getPcategory());
			detail.setUnit(entity.getUnitId());
			detail.setUnitRate(entity.getUnitRate());
			detail.setComboRowId(entity.getComboRowId());
			detail.setUnitQty(entity.getQty());
			detail.setQty(MoneyUtils.multiply(entity.getQty(), entity.getUnitRate(), Money.Qty));
			detail.setTradePrice(entity.getPrice());
			detail.setTradeTotal(entity.getTotal());
			detail.setDisedTaxedPrice(entity.getPrice());
			detail.setDisedTaxedTotal(entity.getTotal());
			detail.setTotal(MoneyUtils.add(entity.getTotal(), entity.getPreferentialTotal(), Money.Total));
			detail.setPrice(MoneyUtils.divide(detail.getTotal(), detail.getUnitQty(), Money.Price));
			detail.setPreferentialTotal(entity.getPreferentialTotal());
			detail.setPtypePreferentialTotal(entity.getSinglePreferential());
			detail.setOrderPreferentialAllotTotal(MoneyUtils.subtract(detail.getPreferentialTotal(), detail.getPtypePreferentialTotal(), Money.Total));
			detail.setDisedPrice(entity.getPrice());
			detail.setDisedTotal(entity.getTotal());
			detail.setDisedInitialPrice(entity.getPrice());
			detail.setDisedInitialTotal(entity.getTotal());

			EshopSaleOrderDetailBatch batch = new EshopSaleOrderDetailBatch();
			batch.setBatchno(entity.getBatchno());
			batch.setProduceDate(entity.getProduceDate());
			batch.setExpireDate(entity.getExpireDate());
			batch.setBatchPrice(entity.getBatchPrice());
			detail.setBatch(batch);
			orderDetails.add(detail);
		}
		saleOrder.setOrderDetails(orderDetails);
	}

	@PostMapping(value = "/saveEshopAndAuth")
	@ApiOperation(value = "新增网店")
	@ApiImplicitParam(name = "request", value = "新增网店接口")
	public SaveEshopAndAuthResponse saveEshopAndAuth(@RequestBody SaveEshopAndAuthRequest request) {
		return eshopOrderMiddleGroundService.saveEshopAndAuth(request);
	}

	@PostMapping(value = "/closeOrder")
	@ApiOperation(value = "关闭订单")
	@ApiImplicitParam(name = "request", value = "关闭订单接口")
	public List<CloseOrderResponse> closeOrder(@RequestBody List<CloseOrderRequest> requests) {
		return eshopOrderMiddleGroundService.closeOrder(requests);
	}

	@PostMapping(value = "/updateEshopAuth")
	@ApiOperation(value = "修改网店授权")
	@ApiImplicitParam(name = "request", value = "修改网店授权接口")
	public SaveEshopAndAuthResponse updateEshopAuth(@RequestBody UpdateEshopAuthRequest request) {
		return eshopOrderMiddleGroundService.updateEshopAuth(request);
	}

	@PostMapping(value = "/supportWriteOffList")
	@ApiOperation(value = "获取支持核销的平台")
	@ApiImplicitParam(name = "request", value = "获取支持核销的平台")
	public List<String> supportWriteOff() {
		return eshopOrderMiddleGroundService.supportWriteOff();
	}

	@PostMapping(value = "/getSupportWriteOffByShopType")
	@ApiOperation(value = "根据shoptype或订单号获取支持核销的平台")
	@ApiImplicitParam(name = "request", value = "根据shoptype或订单号")
	public boolean getSupportWriteOffByShopType(@RequestBody QuerySupportWriteOffRequest request) {
		return eshopOrderMiddleGroundService.getSupportWriteOffByShopType(request);
	}

	@PostMapping(value = "/decryptMiddleGround")
	@ApiOperation(value = "上下游解密接口")
	@ApiImplicitParam(name = "request", value = "上下游解密接口")
	public DecryptMiddleGroundOrderResponse decryptMiddleGround(@RequestBody DecryptMiddleGroundOrderRequest request){
		return eshopOrderMiddleGroundService.decryptMiddleGround(request);
	}
}
