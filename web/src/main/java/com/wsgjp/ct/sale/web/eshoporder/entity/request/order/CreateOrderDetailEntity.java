package com.wsgjp.ct.sale.web.eshoporder.entity.request.order;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/4/1 9:54
 */
public class CreateOrderDetailEntity {
	/**
	 * 是否套餐行
	 */
	private boolean comboRow;
	/**
	 * 套餐行ID
	 */
	private BigInteger comboRowId;
	private BigInteger ptypeId;
	private int pcategory;
	private BigInteger skuId;
	private BigInteger unitId;
	private BigDecimal unitRate;
	private BigDecimal price;
	private BigDecimal total;
	private BigDecimal qty;
	/**
	 * 优惠总金额（包含单品优惠，以及其它优惠）
	 */
	private BigDecimal preferentialTotal;

	/**
	 * 单品优惠
	 */
	private BigDecimal singlePreferential;

	/**
	 * 过期时间
	 */
	private Date expireDate;
	/**
	 * 生产日期
	 */
	private Date produceDate;
	/**
	 * 批次号
	 */
	private String batchno;

	/**
	 * 批次价格
	 */
	private BigDecimal batchPrice = BigDecimal.ZERO;

	public Date getExpireDate() {
		return expireDate;
	}

	public void setExpireDate(Date expireDate) {
		this.expireDate = expireDate;
	}

	public Date getProduceDate() {
		return produceDate;
	}

	public void setProduceDate(Date produceDate) {
		this.produceDate = produceDate;
	}

	public String getBatchno() {
		return batchno;
	}

	public void setBatchno(String batchno) {
		this.batchno = batchno;
	}

	public BigDecimal getBatchPrice() {
		return batchPrice;
	}

	public void setBatchPrice(BigDecimal batchPrice) {
		this.batchPrice = batchPrice;
	}

	public boolean isComboRow() {
		return comboRow;
	}

	public void setComboRow(boolean comboRow) {
		this.comboRow = comboRow;
	}

	public BigInteger getComboRowId() {
		return comboRowId;
	}

	public void setComboRowId(BigInteger comboRowId) {
		this.comboRowId = comboRowId;
	}

	public BigInteger getPtypeId() {
		return ptypeId;
	}

	public void setPtypeId(BigInteger ptypeId) {
		this.ptypeId = ptypeId;
	}

	public int getPcategory() {
		return pcategory;
	}

	public void setPcategory(int pcategory) {
		this.pcategory = pcategory;
	}

	public BigInteger getSkuId() {
		return skuId;
	}

	public void setSkuId(BigInteger skuId) {
		this.skuId = skuId;
	}

	public BigInteger getUnitId() {
		return unitId;
	}

	public void setUnitId(BigInteger unitId) {
		this.unitId = unitId;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getTotal() {
		return total;
	}

	public void setTotal(BigDecimal total) {
		this.total = total;
	}

	public BigDecimal getQty() {
		return qty;
	}

	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}

	public BigDecimal getPreferentialTotal() {
		return preferentialTotal;
	}

	public void setPreferentialTotal(BigDecimal preferentialTotal) {
		this.preferentialTotal = preferentialTotal;
	}

	public BigDecimal getSinglePreferential() {
		return singlePreferential;
	}

	public void setSinglePreferential(BigDecimal singlePreferential) {
		this.singlePreferential = singlePreferential;
	}

	public BigDecimal getUnitRate() {
		return unitRate;
	}

	public void setUnitRate(BigDecimal unitRate) {
		this.unitRate = unitRate;
	}
}
