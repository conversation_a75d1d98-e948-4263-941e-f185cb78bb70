package com.wsgjp.ct.sale.web.shopsale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.bill.aspect.AddLogs;
import com.wsgjp.ct.sale.biz.bill.model.dao.PrintCountDAO;
import com.wsgjp.ct.sale.biz.bill.model.dto.LogicalDeleteBillDto;
import com.wsgjp.ct.sale.biz.bill.model.request.BillUpdateFieldRequest;
import com.wsgjp.ct.sale.biz.bill.model.request.BillZyAuditEnableRequest;
import com.wsgjp.ct.sale.biz.member.aspect.WebLogs;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.SsVipBill;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.VipScoreInfo;
import com.wsgjp.ct.sale.biz.shopsale.constanst.BillSaveResultTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.base.QiniuConfigDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.*;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.*;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.AtypePayDto;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseInfoPtypeRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.GoodsDetailsAndPreferential;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PayResultInfo;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PosBill;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PubBillBatchSaveDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PtypeType;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.base.BaseResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.bill.BillAuditResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.bill.PtypeResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.ptype.BatchNo;
import com.wsgjp.ct.sale.biz.shopsale.service.BillService;
import com.wsgjp.ct.sale.sdk.payment.entity.request.AggregatePaymentRequest;
import com.wsgjp.ct.sale.sdk.payment.entity.request.PayOrderQueryRequest;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PayCallBackInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.monitor.annontaion.NgpResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Api(value = "${app.id}/shopsale/bill", tags = {"单据"})
@RestController
@RequestMapping("${app.id}/shopsale/bill")

public class BillController  {
    private static final Logger log = LoggerFactory.getLogger(BillController.class);
    @Autowired
    BillService billService;


    @ApiOperation(value = "发送短信")
    @PostMapping("/sendSMS")
    @WebLogs
    public String sendSMS(@RequestBody Map request) {
        return billService.sendSMS(request);
    }

    @ApiOperation(value = "获取商品分类")
    @PostMapping("/getBaseInfoClass")
    public List<HashMap> getBaseInfoClass() {
        return billService.getBaseInfoClass();
    }

    @ApiOperation(value = "保存商品")
    @PostMapping("/saveBaseInfoPtype")
    public BaseInfoPtypeRequest saveBaseInfoPtype(@RequestBody Map request) {
        return billService.saveBaseInfoPtype(request);
    }

    @ApiOperation(value = "获取库存")
    @PostMapping("/getStockQty")
    public List<PtypeExChangeDTO> getStockQty(@RequestBody Map<String, List<PtypeStockRequest>> stockRequest) {
        return billService.getStockQty(stockRequest);
    }


    @ApiOperation(value = "根据商品 - 获取销售机构价格本")
    @PostMapping("/getPriceFromOtypePriceList")
    @WebLogs
    public List<HashMap> getPriceFromOtypePriceList(@RequestBody PtypeOtypePriceDto param) {
        return billService.getPriceFromOtypePriceList(param);
    }

    @ApiOperation(value = "查询商品价格，包含零售价，门店价，会员价")
    @PostMapping("/getPosPtypePrice")
    @WebLogs
    List<PtypePriceDTO> getPtypePrice(@RequestBody PtypePriceRequestDTO request) {
        return billService.getPtypePrice(request);
    }

    @ApiOperation(value = "获取条码获取套餐")
    @PostMapping("/getQiniuViewConfig")
    @WebLogs
    public QiniuConfigDTO getQiniuViewConfig() {
        return billService.getQiniuViewConfig();
    }

    @ApiOperation(value = "根据序列号获取商品详情", notes = "注意，这里仅仅是查询了商品，并未处理价格、库存等。在pos端中，用于校验序列号是否存在（当使用该序列号查不到商品，说明序列号就不存在）")
    @PostMapping("/getGoodsInfoBySnWithoutPrice")
    @WebLogs
    public PtypeResponse getGoodsInfoBySnWithoutPrice(@RequestBody PtypeCodeParamDTO query) {
        return billService.getGoodsInfoBySnWithoutPrice(query);
    }

    @ApiOperation(value = "提交商品类单据")
    @PostMapping("/submitGoodsBill")
    @NgpResource(name = "shopsale.submitGoodsBill", tagStrings = "'tagA,'+0")
    @WebLogs
    public BillSaveResultDTO submitGoodsBill(@RequestBody PosBill request) {
        return billService.submitGoodsBill(request);
    }

    @ApiOperation(value = "提交商品类单据")
    @PostMapping("/submitGoodsBillList")
    @NgpResource(name = "shopsale.submitGoodsBill", tagStrings = "'tagA,'+0")
    @WebLogs
    public List<BigInteger> submitGoodsBillList(@RequestBody List<PosBill> requestParams) {
        return billService.submitGoodsBillList(requestParams);
    }

    @ApiOperation(value = "自用系统提交商品类单据")
    @PostMapping("/zySubmitGoodsBill")
    public BillSaveResultDTO zySubmitGoodsBill(@RequestBody PosBill requestParams) {
        return billService.zYsubmitGoodsBill(requestParams);
    }


    @ApiOperation(value = "单据验证")
    @PostMapping("/validationGoodsBill")
    @WebLogs
    public BillSaveResultDTO validationGoodsBill(@RequestBody LinkedHashMap<String, Object> requestParams) {
        return billService.validationGoodsBill(requestParams);
    }
//
//    @ApiOperation(value = "提交商品类单据")
//    @PostMapping("/submitGoodsBill")
//    public HashMap submitGoodsBill(@RequestBody LinkedHashMap<String, Object> requestParams) {
//        return billService.submitGoodsBill(requestParams);
//    }

    @ApiOperation(value = "提交商品类单据（调用的是JXC的接口）")
    @PostMapping("/jxcSubmitGoodsBill")
    @WebLogs
    public BillSaveResultDTO jxcSubmitGoodsBill(@RequestBody Map requestParams) {
        return billService.jxcSubmitGoodsBill(requestParams);
    }

    @ApiOperation(value = "获取套餐id(ptypeId)获取套餐")
    @PostMapping("/getComboById")
    @WebLogs
    public Map getComboById(@RequestBody BigInteger id) {
        return billService.getComboById(id);
    }

    @ApiOperation(value = "获取商品类单据以及会员电话")
    @PostMapping("/getGoodsBill")
    @WebLogs
    public GoodsBillDTO getGoodsBill(@RequestBody Map requestParams) {
        return billService.getGoodsBill(requestParams);
    }

    @ApiOperation(value = "获取商品类单据")
    @PostMapping("/getGoodsBillDTO")
    public AbstractBillDTO getGoodsBillDTO(@RequestBody LinkedHashMap<String, Object> requestParams) {
        return billService.getGoodsBillDTO(requestParams);
    }

    @ApiOperation(value = "获取商品类单据、会员电话、优惠辅助表")
    @PostMapping("/getGoodsDetailsAndPreferential")
    @WebLogs
    public GoodsDetailsAndPreferential getGoodsDetailsAndPreferential(@RequestBody BigInteger vchcode) {
        return billService.getGoodsDetailsAndPreferential(vchcode);
    }


//    @ApiOperation(value = "门店单据查询 详情")
//    @PostMapping
//    Map getBillDetailResponse = ResultHandler.result(billAPI.getBillDetail(requestDto));


    @ApiOperation(value = "结算 聚合支付接口")
    @PostMapping("/payOrder")
    @WebLogs
    public PayResultInfo payOrder(@RequestBody AggregatePaymentRequest requestParams) {
        return billService.payOrderNew(requestParams);
    }

    @ApiOperation(value = "聚合支付退款")
    @PostMapping("/payRefund")
    @WebLogs
    public PayResultInfo payRefund(@RequestBody AggregatePaymentRequest requestParams) {
        return billService.payRefundNew(requestParams);
    }

//    @GetMapping("/payResultCallbackFeiQi")
//    public String payResultCallback(HttpServletRequest request, HttpServletResponse response) {
//        return billService.payResultCallback(request, response);
//    }

    @GetMapping("/payResultCallback/{routeCode}")
    public String payResultCallbackTTG(HttpServletRequest request, HttpServletResponse response, @PathVariable(
            "routeCode") String routeCode) {
        return billService.payResultCallbackNew(request, response, routeCode);
    }

    @PostMapping("/payResultCallback/{routeCode}")
    public String payResultCallbackHuiFu(HttpServletRequest request, HttpServletResponse response, @PathVariable(
            "routeCode") String routeCode) {
        return billService.payResultCallbackNew(request, response, routeCode);
    }

    @PostMapping("/payResultCallback")
    public void payResultCallback(@RequestBody PayCallBackInfo callBackInfo) {
        billService.payResultCallback(callBackInfo);
    }

    @ApiOperation(value = "轮询new")
    @PostMapping("/queryPayStatusNew")
    public PayResultInfo queryPayStatusNew(@RequestBody PayOrderQueryRequest request) {
        return billService.queryPayStatusNew(request);
    }

    @ApiOperation(value = "查询支付状态")
    @PostMapping("/queryPayResult")
    public PayResultInfo queryPayResult(@RequestBody PayOrderQueryRequest request) {
        return billService.queryPayResult(request);
    }


    @ApiOperation(value = "结算-聚合支付/退款并保存核算new")
    @PostMapping("/payOrderAndSaveBillNew")
    @WebLogs
    public PayResultInfo payOrderAndSaveBillNew(@RequestBody LinkedHashMap<String, Object> requestParams) {
        return billService.payOrderAndSaveBillNew(requestParams);
    }

    @ApiOperation(value = "获取商品价格")
    @PostMapping("/getPtypePrice")
    public List<PtypeExChangeDTO> getPtypePrice(@RequestBody PtypePriceRequest requestParams) {
        return billService.getPtypePrice(requestParams);
    }

    @ApiOperation(value = "获取商品的批次列表")
    @PostMapping("/getGoodsBatchList")
    @WebLogs
    public PageResponse<BatchNo> getGoodsBatchList(@RequestBody Map<String, Object> requestBody) {
        return billService.getGoodsBatchList(requestBody);
    }

    @ApiOperation(value = "获取套餐列表")
    @PostMapping("/getComboList")
    @WebLogs
    public Map<String, Object> getComboList(@RequestBody LinkedHashMap<String, Object> params) {
        return billService.getComboList(params);
    }

    @ApiOperation(value = "按照名称/条码搜索商品(sku)列表")
    @PostMapping("/getSkuList")
    @WebLogs
    public PageResponse<PtypeResponse> getSkuList(@RequestBody PageRequest<Map<String, Object>> requestParams) {
        return billService.getSkuList(requestParams);
    }

    @ApiOperation(value = "记录单据打印次数")
    @PostMapping("/writePrintCount")
    @WebLogs
    public void writePrintCount(@RequestBody PrintCountDAO requestParams) {
        billService.writePrintCount(requestParams);
    }

    @ApiOperation(value = "根据仓库+ 商品+ 数量 推算出各批次和数量信息（用于自动获取批次号）")
    @PostMapping("/getPtypeBatchQty")
    @WebLogs
    public List<PtypeBatchDTO> getPtypeBatchQty(@RequestBody PtypeBatchParamQtyDTO ptypeBatchParamQtyDTO) {
        return billService.getPtypeBatchQty(ptypeBatchParamQtyDTO);
    }

    @ApiOperation(value = "查询套餐和商品列表(当页码为1时，返回列表头部会根据序列号查找商品)")
    @PostMapping("/selectPtypeAndCombo")
    @NgpResource(name = "shopsale.selectPtypeAndCombo", tagStrings = "'tagA,'+0")
    @WebLogs
    public PageResponse<PtypeResponse> selectPtypeAndCombo(@RequestBody PageRequest<SelectGoodsRequestDTO> request) {
        if (request.getQueryParams().isSearchByPtype()) {
            return billService.selectGoodsByPtype(request);
        } else {
            return billService.selectPtypeAndCombo(request);
        }
    }

    @ApiOperation(value = "获取商品所有一二级分类")
    @PostMapping("/getPtypeAllRootType")
    public List<PtypeType> getPtypeAllRootType() {
        return billService.getPtypeAllRootType();
    }

    @ApiOperation(value = "批量核算单据")
    @PostMapping("/batchPost")
    public BaseResponse batchPostBill(@RequestBody List<BigInteger> vchcodeList) {

        PubBillBatchSaveDTO pubBillBatchSaveDTO = billService.batchPostBill(vchcodeList);

        return BaseResponse.isOk(pubBillBatchSaveDTO);
    }

    @ApiOperation(value = "审核单据")
    @PostMapping("/auditBill")
    public BillAuditResponse auditBill(@RequestBody BillAuditRequest request) {
        return billService.auditBill(request);
    }


    @ApiOperation(value = "调拨入库")
    @PostMapping("/goodsTrans/in")
    @AddLogs
    @WebLogs
    public BillSaveResultDTO goodsTransInStock(@RequestBody GoodsTransInRequest request) throws Exception {
        BillSaveResultDTO rt = billService.goodsTransInStock(request);
        rt.setResultType(BillSaveResultTypeEnum.GOODSTRANS_IN_SUCCESS);
        return rt;
    }

    @ApiOperation(value = "获取记录的单据消费后关联的剩余会员储值信息")
    @PostMapping("/getVipBillByVchcode")
    @WebLogs
    public SsVipBill getVipBillByVchcode(@RequestBody String vchcode) {
        return billService.getVipBillByVchcode(vchcode);
    }

    @ApiOperation(value = "保存淘淘谷支付方式信息")
    @PostMapping("/saveTtgByOpenInfo")
    public void saveTtgByOpenInfo(@RequestBody AtypePayDto request) {
        billService.saveTtgByOpenInfo(request);
    }

    @ApiOperation(value = "修改支付状态并核算")
    @PostMapping("/updateBillPayState")
    public void updateBillPayState(@RequestBody GoodsBillDTO goodsBillDTO) {
        goodsBillDTO.setSource("人工手动修改");
        billService.updateBillPayState(goodsBillDTO, true);
    }


//    @ApiOperation(value = "提交审核")
//    @PostMapping("/submitAudit")
//    public BillAuditResponse submitAudit(@RequestBody BillAuditRequest request) {
//        return billService.submitAudit(request);
//    }
//
//    @ApiOperation(value = "单据审核检查")
//    @PostMapping("/checkAuditEnable")
//    public AuditEnableResponse checkAuditEnable(@RequestBody BillZyAuditEnableRequest request) {
//        return billService.checkAuditEnable(request);
//    }


    @ApiOperation(value = "提交审核")
    @PostMapping("/submitAudit")
    public BillAuditResponse submitAudit(@RequestBody BillAuditRequest request) {
        return billService.submitAudit(request);
    }

    @ApiOperation(value = "单据审核检查")
    @PostMapping("/checkAuditEnable")
    public AuditEnableResponse checkAuditEnable(@RequestBody BillZyAuditEnableRequest request) {
        return billService.checkAuditEnable(request);
    }


    @ApiOperation(value = "获取会员积分余额和单据本次积分")
    @PostMapping("/getBillVipScoreInfo")
    public VipScoreInfo getBillVipScoreInfo(@RequestBody HashMap dto) {
        return billService.getBillVipScoreInfo(dto);
    }

    /**
     * 逻辑删除单据
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @PostMapping("/logicalDeleteBill")
    @WebLogs
    public void logicalDeleteBill(@RequestBody LogicalDeleteBillDto dto) throws Exception {
        billService.logicalDeleteBill(dto.getVchcode(), "", dto.getOperationSource());
    }

    @PostMapping("/updateBill")
    public void updateBill(@RequestBody BillUpdateFieldRequest request) {
        billService.updateBill(request);
    }



}
