package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.config.DeliverBillConfig;
import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.service.BaseInfoService;
import com.wsgjp.ct.sale.biz.jarvis.state.DeliverBillDetailContentEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Description TODO
 * @Date 2024-06-06 9:23
 * @Created by lingxue
 */
@RestController
@RequestMapping("/${app.id}/jarvis/SysConfigController")
@Api(description = "系统配置")
public class SysConfigController {
    private final BaseInfoService baseInfoService;

    public SysConfigController(BaseInfoService baseInfoService) {
        this.baseInfoService = baseInfoService;
    }
    @ApiOperation(value = "获取主表明细展示信息", notes = "")
    @PostMapping("getDeliverBillDetailContent")
    public List<DillDeliverState> getDeliverBillDetailContent() {
        return baseInfoService.getDeliverBillStatus(DeliverBillDetailContentEnum.class);
    }

    @PostMapping("test")
    public void test(@RequestBody Map<String, Object> config) {
        for (Map.Entry<String, Object> newEntry : config.entrySet()) {
            String a = newEntry.getValue().toString();
        }
    }

    @ApiOperation(value = "保存提交开票设置", notes = "")
    @PostMapping("saveInvoiceConfig")
    public boolean saveInvoiceConfig(@RequestBody DeliverBillConfig invoiceConfig) {
        return baseInfoService.saveInvoiceConfig(invoiceConfig);
    }
    @ApiOperation(value = "重置提交开票设置", notes = "")
    @PostMapping("resetInvoiceOldConfig")
    public void resetInvoiceOldConfig() {
        baseInfoService.resetInvoiceOldConfig();
    }
}
