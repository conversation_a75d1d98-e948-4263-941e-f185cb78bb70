package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.api.response.BaseResponse;
import com.wsgjp.ct.sale.biz.jarvis.entity.template.TemplateManagementEntity;
import com.wsgjp.ct.sale.biz.jarvis.service.template.TemplateManagementService;
import com.wsgjp.ct.sale.biz.jarvis.state.TemplateCodeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;

@RestController
@Api(description = "模板管理")
@RequestMapping("/${app.id}/jarvis/template")
public class TemplateManagementController {

    private TemplateManagementService templateManagementService;
    private Logger logger = LoggerFactory.getLogger(TemplateManagementController.class);

    public TemplateManagementController(TemplateManagementService templateManagementService) {
        this.templateManagementService = templateManagementService;
    }

    @ApiOperation(value = "新增模板")
    @PostMapping("addTemplate")
    public BaseResponse addTemplate(@RequestBody TemplateManagementEntity request) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            templateManagementService.checkTemplateForName(request);
            templateManagementService.addTemplate(request);
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format("新增模板失败：%s", e.getMessage());
            response.setMessage(msg);
            logger.error(msg, e);
        }
        return response;
    }

    @ApiOperation(value = "删除模板")
    @PostMapping("deleteTemplate")
    public BaseResponse deleteTemplate(@RequestBody TemplateManagementEntity request) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            templateManagementService.deleteTemplate(request);
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format("删除模板失败：%s", e.getMessage());
            response.setMessage(msg);
            logger.error(msg, e);
        }
        return response;
    }

    @ApiOperation(value = "修改模板-主表修改")
    @PostMapping("updateTemplate")
    public BaseResponse updateTemplate(@RequestBody TemplateManagementEntity request) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            templateManagementService.updateTemplate(request);
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format("修改模板失败：%s", e.getMessage());
            response.setMessage(msg);
            logger.error(msg, e);
        }
        return response;
    }

    @ApiOperation(value = "修改模板-主表修改")
    @PostMapping("updateTemplateSimple")
    public BaseResponse updateTemplateSimple(@RequestBody TemplateManagementEntity request) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            templateManagementService.updateTemplateSimple(request);
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format("修改模板失败：%s", e.getMessage());
            response.setMessage(msg);
            logger.error(msg, e);
        }
        return response;
    }

    @ApiOperation(value = "修改模板明细")
    @PostMapping("updateTemplateDetail")
    public BaseResponse updateTemplateDetail(@RequestBody TemplateManagementEntity request) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            templateManagementService.updateTemplateDetail(request);
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format("修改模板数据失败：%s", e.getMessage());
            response.setMessage(msg);
            logger.error(msg, e);
        }
        return response;
    }

    @ApiOperation(value = "修改模板配置")
    @PostMapping("updateTemplateConfig")
    public BaseResponse updateTemplateConfig(@RequestBody TemplateManagementEntity request) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            templateManagementService.updateTemplateConfig(request);
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format("修改模板失败：%s", e.getMessage());
            response.setMessage(msg);
            logger.error(msg, e);
        }
        return response;
    }

    @ApiOperation(value = "修改模板配置-批量")
    @PostMapping("updateTemplateConfigBatch")
    public BaseResponse updateTemplateConfigBatch(@RequestBody List<TemplateManagementEntity> requestList) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            for (TemplateManagementEntity request : requestList) {
                templateManagementService.updateTemplateConfig(request);
            }
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format("修改模板失败：%s", e.getMessage());
            response.setMessage(msg);
            logger.error(msg, e);
        }
        return response;
    }
    @ApiOperation(value = "修改模板配置-批量")
    @PostMapping("updateTemplateConfigBatchNew")
    public BaseResponse updateTemplateConfigBatchNew(@RequestBody List<TemplateManagementEntity> requestList) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            templateManagementService.updateTemplateConfigBatchNew(requestList);
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format("修改模板失败：%s", e.getMessage());
            response.setMessage(msg);
            logger.error(msg, e);
        }
        return response;
    }

    @ApiOperation(value = "查询模板列表-只查询主表")
    @PostMapping("queryTemplate")
    public List<TemplateManagementEntity> queryTemplate(@RequestBody QueryTemplateRequest request) {
        List<TemplateManagementEntity> resList = templateManagementService.queryTemplate(request.getTemplateCode());
        boolean needInitConfig = false;
        int size = resList.size();
        for (int i = 0; i < size; i++) {
            TemplateManagementEntity template = resList.get(i);
            if(template.getShowIndex()>=10000){
                needInitConfig = true;
                template.setShowIndex(i);
            }
        }
        if(needInitConfig){
            updateTemplateConfigBatch(resList);
        }
        return resList;
    }

    @PostMapping("queryTemplateNew")
    public List<TemplateManagementEntity> queryTemplateNew(@RequestBody QueryTemplateRequest request) {
        return templateManagementService.queryTemplateNew(request.getTemplateCode());
    }

    @ApiOperation(value = "查询模板列表-只查询明细")
    @PostMapping("queryTemplateDetail")
    public TemplateManagementEntity queryTemplateDetail(@RequestBody QueryTemplateRequest request) {
        return templateManagementService.queryTemplateDetail(request.getTemplateId());
    }

    @ApiOperation(value = "检查模板是否重名")
    @PostMapping("checkTemplateForName")
    public BaseResponse checkTemplateForName(@RequestBody TemplateManagementEntity request) {
        BaseResponse response = new BaseResponse();
        response.setCode("200");
        try {
            templateManagementService.checkTemplateForName(request);
        } catch (Exception e) {
            response.setCode("500");
            String msg = String.format(e.getMessage());
            response.setMessage(msg);
        }
        return response;
    }

}

class QueryTemplateRequest {
    private TemplateCodeEnum templateCode;
    private BigInteger templateId;

    public TemplateCodeEnum getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(TemplateCodeEnum templateCode) {
        this.templateCode = templateCode;
    }

    public BigInteger getTemplateId() {
        return templateId;
    }

    public void setTemplateId(BigInteger templateId) {
        this.templateId = templateId;
    }
}
