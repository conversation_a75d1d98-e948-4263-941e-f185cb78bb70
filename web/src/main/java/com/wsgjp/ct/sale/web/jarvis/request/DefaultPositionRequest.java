package com.wsgjp.ct.sale.web.jarvis.request;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class DefaultPositionRequest {

    private BigInteger relationId;
    private BigInteger ktypeId;
    private Boolean defaultPositionPresent;

    public BigInteger getRelationId() {
        return relationId;
    }

    public void setRelationId(BigInteger relationId) {
        this.relationId = relationId;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public Boolean getDefaultPositionPresent() {
        return defaultPositionPresent;
    }

    public void setDefaultPositionPresent(Boolean defaultPositionPresent) {
        this.defaultPositionPresent = defaultPositionPresent;
    }
}
