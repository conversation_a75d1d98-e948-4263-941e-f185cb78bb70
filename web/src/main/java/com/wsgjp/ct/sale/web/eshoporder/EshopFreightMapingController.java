package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopOnlineFreightMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryShopFreightMappingParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopFreightMappingService;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformEshopSupport;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import ngp.utils.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "网店物流映射")
@RequestMapping("${app.id}/eshoporder/eshopfreightmapping")
@RestController
public class EshopFreightMapingController {

    private final EshopFreightMappingService service;

    public EshopFreightMapingController(EshopFreightMappingService service) {
        this.service = service;
    }

    @PostMapping(value = "/getShopTypeFreightMappings")
    public PageResponse<EshopOnlineFreightMapping> getShopTypeFreightMappings(@RequestBody PageRequest<QueryShopFreightMappingParameter> parameter) {
//        parameter.getQueryParams().setProfileId(getProfileId());
        return service.getEShopOnlineFreightMapping(parameter);
    }

    /**
     * 获取平台内置物流映射关系
     *
     * @param parameter 查询参数
     * @return List<FreightMapping>
     */
    @PostMapping(value = "/getPlatformFreightMappings")
    public PageResponse<FreightMapping> getPlatformFreightMappings(@RequestBody PageRequest<QueryShopFreightMappingParameter> parameter) {
        return service.getFreightMapping(parameter);
    }

    @PostMapping(value = "/saveFreightMapping")
    public BaseResponse saveFreightMapping(@RequestBody EshopOnlineFreightMapping mapping) {
//        if (null == mapping.getProfileId() || BigInteger.ZERO.equals(mapping.getProfileId())) {
//            mapping.setProfileId(CurrentUser.getProfileId());
//        }
        BaseResponse response = new BaseResponse();
        try {
            response.setSuccess(service.insertOrUpdateEShopOnlineFreightMapping(mapping));
            return response;
        } catch (Exception ex) {
            response.setSuccess(false);
            response.setMessage("绑定物流对应关系失败：" + ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/clearFreightMapping")
    public BaseResponse clearFreightMapping(@RequestBody QueryShopFreightMappingParameter parameter) {
        BaseResponse response = new BaseResponse();
        try {
            response.setSuccess(service.deleteEShopOnlineFreightMapping(CurrentUser.getProfileId(), parameter.getShopType()));
            return response;
        } catch (Exception ex) {
            response.setSuccess(false);
            response.setMessage("恢复默认物流对应关系失败：" + ex.getMessage());
        }
        return response;
    }

    @PostMapping(value = "/deleteFreightMappingByMapping")
    public BaseResponse deleteFreightMappingByMapping(@RequestBody EshopOnlineFreightMapping mapping) {
        BaseResponse response = new BaseResponse();
        try {
//            mapping.setProfileId(CurrentUser.getProfileId());
            response.setSuccess(service.deleteFreightMappingByMapping(mapping));
            return response;
        } catch (Exception ex) {
            response.setSuccess(false);
            response.setMessage("解绑物流对应关系失败：" + ex.getMessage());
        }
        return response;
    }


    @PostMapping(value = "/getSupportEShopTypes")
    public List<PlatformEshopSupport> getSupportEShopTypes() {
        List<PlatformEshopSupport> shopTypeSupport = EshopUtils.getSupportEshopTypes();
        if (CollectionUtils.isNotEmpty(shopTypeSupport)) {
            String qiMenFenXiao = "奇门代销";
            shopTypeSupport = shopTypeSupport.stream().filter(item -> item.getType() != ShopType.Other.getCode()
                    && item.getType() != ShopType.UserDefinedEShop.getCode()
                    && !qiMenFenXiao.equals(item.getName())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(shopTypeSupport)) {
            shopTypeSupport.sort(Comparator.comparing(PlatformEshopSupport::getType));
        }
        return shopTypeSupport;
    }

}
