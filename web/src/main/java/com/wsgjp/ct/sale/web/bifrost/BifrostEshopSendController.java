package com.wsgjp.ct.sale.web.bifrost;

import com.wsgjp.ct.sale.platform.entity.request.sendgoods.CancelSendGoodsRequest;
import com.wsgjp.ct.sale.platform.entity.request.sendgoods.WriteoffOrderRequest;
import com.wsgjp.ct.sale.platform.entity.response.sendgoods.CancelSendGoodsResponse;
import com.wsgjp.ct.sale.platform.entity.response.sendgoods.DeliveryEndConfirmResponse;
import com.wsgjp.ct.sale.platform.entity.response.sendgoods.SyncFreightBillNoResponse;
import com.wsgjp.ct.sale.platform.entity.response.sendgoods.WriteoffOrderResponse;
import com.wsgjp.ct.sale.platform.sdk.entity.request.DeliveryEndConfirmRequest;
import com.wsgjp.ct.sale.platform.sdk.entity.request.SendOrderRequest;
import com.wsgjp.ct.sale.platform.sdk.service.EshopSendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "网店发货相关接口")
@RestController
@RequestMapping("/${app.id}/bifrost/send")
public class BifrostEshopSendController {
    private final EshopSendService sendService;

    public BifrostEshopSendController(EshopSendService sendService) {
        this.sendService = sendService;
    }

    @ApiOperation("取消发货")
    @PostMapping("cancelSendGoods")
    public CancelSendGoodsResponse cancelSendGoods(CancelSendGoodsRequest request) {
        return sendService.cancelSendGoods(request);
    }

    @ApiOperation("核销订单")
    @PostMapping("writeoffOrders")
    public WriteoffOrderResponse writeoffOrders(@RequestBody WriteoffOrderRequest request) {
        return sendService.writeoffOrders(request);
    }

    @ApiOperation("同步单号接口")
    @PostMapping("sendOrder")
    public List<SyncFreightBillNoResponse> sendOrder(@RequestBody SendOrderRequest request) {
        if (CollectionUtils.isEmpty(request.getFreightBillList())) {
            throw new RuntimeException("请求参数requestList不能为空");
        }
        return sendService.sendNew(request);
    }

    @ApiOperation("订单确认送达接口")
    @PostMapping("deliveryEndConfirm")
    public DeliveryEndConfirmResponse deliveryEndConfirm(@RequestBody DeliveryEndConfirmRequest request) {
        return sendService.deliveryEndConfirm(request);
    }
}
