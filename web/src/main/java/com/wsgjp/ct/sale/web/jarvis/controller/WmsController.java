package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.UpdateDeliverTimeParamList;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.common.enums.TradeStateEnum;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-06-16
 */
@RestController
@Api(description = "提供给wms模块的接口")
@RequestMapping("/${app.id}/api/wms")
public class WmsController {
    @Autowired
    private DeliverService deliverService;

    @ApiOperation("修改交易单-线上交易状态")
    @PostMapping("/updateTradeStatus")
    public BaseResponse updateTradeStatus(@RequestBody updateTradeStatusParam param) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        try {
            boolean res = deliverService.updateTradeStatus(CurrentUser.getProfileId(), param.getVchcodes(), param.getTradeState());
            if(!res){
                response.setCode("-1");
                response.setMsg("修改交易单-线上交易状态-失败！");
            }
        } catch (Exception ex) {
            response.setCode("-1");
            response.setMsg(String.format("修改交易单-线上交易状态-失败，请重试，原因：【%s】", ex.getMessage()));
        }
        return response;
    }

    @ApiOperation("修改交易单-发货时间")
    @PostMapping("/updateDeliverTime")
    public BaseResponse updateDeliverTime(@RequestBody UpdateDeliverTimeParamList paramList) {
        BaseResponse response = new BaseResponse();
        response.setCode("0");
        try {
            boolean res = deliverService.updateDeliverTime(paramList);
            if(!res){
                response.setCode("-1");
                response.setMsg("修改交易单-发货时间-失败！");
            }
        } catch (Exception ex) {
            response.setCode("-1");
            response.setMsg(String.format("修改交易单-发货时间-失败，请重试，原因：【%s】", ex.getMessage()));
        }
        return response;
    }

}

class updateTradeStatusParam{
    private List<BigInteger> vchcodes;
    private TradeStateEnum tradeState;

    public List<BigInteger> getVchcodes() {
        return vchcodes;
    }

    public void setVchcodes(List<BigInteger> vchcodes) {
        this.vchcodes = vchcodes;
    }

    public TradeStateEnum getTradeState() {
        return tradeState;
    }

    public void setTradeState(TradeStateEnum tradeState) {
        this.tradeState = tradeState;
    }
}
