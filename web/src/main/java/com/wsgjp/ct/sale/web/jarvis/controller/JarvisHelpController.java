package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.service.help.JarvisHelpService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/${app.id}/jarvis/help")
public class JarvisHelpController {
    private JarvisHelpService jarvisHelpService;

    public JarvisHelpController(JarvisHelpService jarvisHelpService) {
        this.jarvisHelpService = jarvisHelpService;
    }

    @GetMapping("/getHelpConfig")
    public Map<String, String> getHelpConfig() {
        return jarvisHelpService.getHelpConfig();
    }

}
