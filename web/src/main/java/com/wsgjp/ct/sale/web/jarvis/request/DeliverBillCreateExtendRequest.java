package com.wsgjp.ct.sale.web.jarvis.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel(value = "发货单创建的扩展信息")
public class DeliverBillCreateExtendRequest {
    @ApiModelProperty(value = "支付单号")
    private String payNo;
    @ApiModelProperty(value = "某某类型，待确定")
    private Byte CnService;
    @ApiModelProperty(value = "买家指定物流类型")
    private Byte buyerFreightType;
    @ApiModelProperty(value = "买家指定物流名称")
    private String buyerFreightName;
    @ApiModelProperty(value = "商品核对码")
    private String ptypeCheckCode;

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public byte getCnService() {
        return CnService;
    }

    public void setCnService(Byte cnService) {
        CnService = cnService;
    }

    public byte getBuyerFreightType() {
        return buyerFreightType;
    }

    public void setBuyerFreightType(Byte buyerFreightType) {
        this.buyerFreightType = buyerFreightType;
    }

    public String getBuyerFreightName() {
        return buyerFreightName;
    }

    public void setBuyerFreightName(String buyerFreightName) {
        this.buyerFreightName = buyerFreightName;
    }

    public String getPtypeCheckCode() {
        return ptypeCheckCode;
    }

    public void setPtypeCheckCode(String ptypeCheckCode) {
        this.ptypeCheckCode = ptypeCheckCode;
    }
}
