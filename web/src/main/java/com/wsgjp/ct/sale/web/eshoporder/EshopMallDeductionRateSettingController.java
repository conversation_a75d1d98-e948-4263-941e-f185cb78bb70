package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.malldeduction.MallDeductionConfigEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryMallDeductionConfigParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryPlatformClassParameter;
import com.wsgjp.ct.sale.biz.eshoporder.service.malldeduction.EshopMallDeductionRateSettingService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.record.utils.RedisUtils;
import com.wsgjp.ct.sale.platform.dto.product.EshopProductCategoryRateClass;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商城扣点设置相关
 *
 * <AUTHOR>
 */
@Api(tags = "商城扣点设置相关")
@RequestMapping("${app.id}/eshoporder/mall/deduction")
@RestController
public class EshopMallDeductionRateSettingController {
    private final EshopMallDeductionRateSettingService mallDeductionRateSettingService;
    private final EshopProductService productService;

    public EshopMallDeductionRateSettingController(EshopMallDeductionRateSettingService mallDeductionRateSettingService, EshopProductService productService) {
        this.mallDeductionRateSettingService = mallDeductionRateSettingService;
        this.productService = productService;
    }

    @PostMapping(value = "/queryMallDeductionRateConfigList")
    public PageResponse<MallDeductionConfigEntity> queryMallDeductionRateConfigList(@RequestBody PageRequest<QueryMallDeductionConfigParameter> pageParameter) {
        return mallDeductionRateSettingService.queryMallDeductionRateConfigsPage(pageParameter);
    }


    @PostMapping(value = "/queryMallDeductionRateConfigsCount")
    public int queryMallDeductionRateConfigsCount(@RequestBody PageRequest<QueryMallDeductionConfigParameter> pageParameter) {
        return mallDeductionRateSettingService.queryMallDeductionRateConfigsCount(pageParameter);
    }

    @PostMapping(value = "/modifyMallDeductionRateConfigs")
    public boolean modifyMallDeductionRateConfigs(@RequestBody List<MallDeductionConfigEntity> mallDeductionConfigList) {
        return mallDeductionRateSettingService.modifyMallDeductionRateConfigs(mallDeductionConfigList);
    }

    @PostMapping(value = "/singleModifyMallDeductionRateConfig")
    public boolean singleModifyMallDeductionRateConfig(@RequestBody MallDeductionConfigEntity mallDeductionConfig) {
        return mallDeductionRateSettingService.singleModifyMallDeductionRateConfig(mallDeductionConfig);
    }


    @PostMapping(value = "/addMallDeductionRateConfigs")
    public boolean addMallDeductionRateConfigs(@RequestBody List<MallDeductionConfigEntity> mallDeductionConfigList) {
        return mallDeductionRateSettingService.addMallDeductionRateConfigs(mallDeductionConfigList);
    }

    @PostMapping(value = "/deleteMallDeductionRateConfigs")
    public boolean deleteMallDeductionRateConfigs(@RequestBody List<MallDeductionConfigEntity> mallDeductionConfigList) {
        return mallDeductionRateSettingService.deleteMallDeductionRateConfigs(mallDeductionConfigList);
    }

    @PostMapping(value = "/deleteSingleMallDeductionRateConfig")
    public boolean deleteSingleMallDeductionRateConfig(@RequestBody MallDeductionConfigEntity mallDeductionConfig) {
        return mallDeductionRateSettingService.deleteSingleMallDeductionRateConfig(mallDeductionConfig);
    }


    @PostMapping(value = "/queryExistMallDeductionRateConfigs")
    public boolean queryExistMallDeductionRateConfigs(@RequestBody List<MallDeductionConfigEntity> mallDeductionConfigList) {
        return mallDeductionRateSettingService.queryExistMallDeductionRateConfigs(mallDeductionConfigList);
    }


    @RequestMapping(value = "/getShopTypePlatformClassList", method = RequestMethod.POST)
    public List<EshopProductCategoryRateClass> getShopTypePlatformClassList(@RequestBody QueryPlatformClassParameter parameter) {
        if (parameter == null || parameter.getEshopType() == null || ShopType.ErrorEshop.equals(parameter.getEshopType())) {
            return new ArrayList<>();
        }
        ShopType eshopType = parameter.getEshopType();
        if (ShopType.ErrorEshop.equals(eshopType) || CollectionUtils.isEmpty(parameter.getEshopInfos())) {
            return new ArrayList<>();
        }
        List<EshopProductCategoryRateClass> list = productService.getMallFeePlatformClassList(eshopType, parameter.getEshopInfos());
        if (StringUtils.isNotEmpty(parameter.getClassName()) && CollectionUtils.isNotEmpty(list)) {
            List<EshopProductCategoryRateClass> queryNodes = list.stream().filter(x -> x.getPlatformClassName().toLowerCase().contains(parameter.getClassName())).collect(Collectors.toList());
            mallDeductionRateSettingService.buildQueryClassNodes(eshopType, queryNodes);
            return queryNodes;
        }
        if (StringUtils.isNotBlank(parameter.getParPlatformClassId())) {
            if (parameter.getParPlatformClassId().equals("0")) {
                return list.stream().filter(x -> StringUtils.isBlank(x.getParPlatformClassId()) || x.getParPlatformClassId().equals(parameter.getParPlatformClassId())).collect(Collectors.toList());
            } else {
                return list.stream().filter(x -> x.getParPlatformClassId().equals(parameter.getParPlatformClassId())).collect(Collectors.toList());
            }
        }
        return list;
    }
}
