package com.wsgjp.ct.sale.web.jarvis.response;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className CommonResponse
 */
public class CommonResponse<T> extends BaseResponse <T> {
    private T data;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <C> BaseResponse<C> success(C c){
        CommonResponse<C> baseResponse = new CommonResponse<>();
        baseResponse.setCode("0");
        baseResponse.setData(c);
        return baseResponse;
    }

    public static BaseResponse success(){
        BaseResponse baseResponse = new CommonResponse<>();
        baseResponse.setCode("0");
        return baseResponse;
    }

    public static BaseResponse fail(String msg){
        BaseResponse baseResponse = new CommonResponse<>();
        baseResponse.setCode("-1");
        baseResponse.setMsg(msg);
        return baseResponse;
    }
}
