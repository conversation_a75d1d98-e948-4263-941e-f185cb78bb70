package com.wsgjp.ct.sale.web.jarvis.request.sensitive;

import com.wsgjp.ct.sis.client.common.SensitiveFieldEnum;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class DecryptFieldRequest {
    private BigInteger shopId;
    private BigInteger orderId;
    private String di;
    private SensitiveFieldEnum field;

    public BigInteger getShopId() {
        return shopId;
    }

    public void setShopId(BigInteger shopId) {
        this.shopId = shopId;
    }

    public BigInteger getOrderId() {
        return orderId;
    }

    public void setOrderId(BigInteger orderId) {
        this.orderId = orderId;
    }

    public String getDi() {
        return di;
    }

    public void setDi(String di) {
        this.di = di;
    }

    public SensitiveFieldEnum getField() {
        return field;
    }

    public void setField(SensitiveFieldEnum field) {
        this.field = field;
    }
}
