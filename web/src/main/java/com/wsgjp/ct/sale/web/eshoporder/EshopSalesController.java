package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.openapi.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.PtypeSaleTaskDetailNew;
import com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.PtypeSaleTaskNew;
import com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.QuerySaleTaskDetailsParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.QuerySaleTaskParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskState.QuerySaleTaskStateParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriod;
import com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodGroup;
import com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.log.PlSalePeriodGroupLog;
import com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.log.PlSalePeriodLogQueryParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.log.SaleTaskLogQueryParams;
import com.wsgjp.ct.sale.biz.eshoporder.service.sales.PlSalePeriodGroupService;
import com.wsgjp.ct.sale.biz.eshoporder.service.sales.PlSalePeriodService;
import com.wsgjp.ct.sale.biz.eshoporder.service.sales.saletask.NewSaleTaskService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;
/**
 * 销售时段控制器
 *
 * <AUTHOR>
 */
@Api(tags = "销售任务管理接口")
@RequestMapping("${app.id}/eshoporder/sales")
@RestController
public class EshopSalesController {
    private final PlSalePeriodGroupService plSalePeriodGroupService;
    private final PlSalePeriodService plSalePeriodService;
    private NewSaleTaskService newSaleTaskService;
    public EshopSalesController(PlSalePeriodGroupService plSalePeriodGroupService, PlSalePeriodService plSalePeriodService,
                                NewSaleTaskService newSaleTaskService) {
        this.plSalePeriodGroupService = plSalePeriodGroupService;
        this.plSalePeriodService = plSalePeriodService;
        this.newSaleTaskService = newSaleTaskService;
    }
    /**
     * 查询时段组列表
     *
     * @param pageRequest
     * @return
     */
    @PostMapping(value = "/listPlSalePeriodGroupList")
    @PageDataSource
    public PageResponse<PlSalePeriodGroup> listPlSalePeriodGroupList(@RequestBody PageRequest<PlSalePeriodGroup> pageRequest) {
        PlSalePeriodGroup group = pageRequest.getQueryParams();
        group.setProfileId(CurrentUser.getProfileId());
        PageDevice.initPage(pageRequest);
        return PageDevice.readPage(plSalePeriodGroupService.listPlSalePeriodGroup(group));
    }
    /**
     * 查询时段列表
     *
     * @param groupId
     * @return
     */
    @GetMapping(value = "/listPlSalePeriodList/{groupId}")
    public List<PlSalePeriod> listPlSalePeriodList(@PathVariable String groupId) {
        return plSalePeriodService.listPlSalePeriod(new BigInteger(groupId), CurrentUser.getProfileId());
    }
    /**
     * 查询时段组是否被使用
     *
     * @param groupId
     * @return
     */
    @GetMapping(value = "/isUsed/{groupId}")
    public boolean isUsed(@PathVariable BigInteger groupId) {
        return plSalePeriodGroupService.isUsed(groupId);
    }
    /**
     * 新增一个时段组
     *
     * @param plSalePeriodGroup
     * @return
     */
    @PostMapping("/addPlSalePeriodGroup")
    public BaseResponse addPlSalePeriodGroup(@RequestBody PlSalePeriodGroup plSalePeriodGroup) {
        return plSalePeriodGroupService.addPlSalePeriodGroup(plSalePeriodGroup);
    }
    /**
     * 更新一个时段组
     *
     * @param plSalePeriodGroup
     * @return
     */
    @PostMapping(value = "/updateById")
    public BaseResponse updateById(@RequestBody PlSalePeriodGroup plSalePeriodGroup) {
        return plSalePeriodGroupService.updateById(plSalePeriodGroup);
    }
    /**
     * 删除一个时段组
     *
     * @param groupId
     * @return
     */
    @GetMapping(value = "/deleteById/{groupId}")
    public BaseResponse deleteById(@PathVariable BigInteger groupId) {
        return plSalePeriodGroupService.deleteById(groupId);
    }
    /**
     * 启用或者停用时段组
     *
     * @param ids
     * @return
     */
    @GetMapping(value = "/onOrClose/{ids}")
    public boolean onOrClose(@PathVariable String ids) {
        return plSalePeriodGroupService.onOrClose(ids) > 0;
    }
    /**
     * 查询时段组操作日志
     *
     * @param request
     * @return
     */
    @PostMapping("/listLog")
    public PageResponse<PlSalePeriodGroupLog> listLog(@RequestBody PageRequest<PlSalePeriodLogQueryParams> request) {
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        return LogService.query(request);
    }
    /**
     * ---------------------------------------------------------------------------------------------------------------------------------
     */
    @PostMapping(value = "/listSaleTaskNew")
    public PageResponse<PtypeSaleTaskNew> listSaleTaskNew(@RequestBody PageRequest<QuerySaleTaskParam> param) {
        return newSaleTaskService.querySaleTask(param);
    }
    @PostMapping(value = "/listSaleTaskDetails")
    public List<PtypeSaleTaskDetailNew> listSaleTaskDetails(@RequestBody QuerySaleTaskDetailsParam param) {
        // saleTaskService = map.get(1);
        return newSaleTaskService.querySaleTaskDetail(param);
    }
    //删除销售任务
    @GetMapping(value = "/deleteSaleTask/{id}")
    public void deleteSaleTask(@PathVariable BigInteger id) {
        newSaleTaskService.deleteTask(id);
    }
    @PostMapping(value = "/saveSaleTask")
    public void saveSaleTask(@RequestBody PtypeSaleTaskNew ptypeSaleTaskNew) {
        newSaleTaskService.addSaleTask(ptypeSaleTaskNew);
    }
    @PostMapping(value = "/checkExist")
    public boolean checkExist(@RequestBody PtypeSaleTaskNew ptypeSaleTaskNew) {
        return newSaleTaskService.checkExist(ptypeSaleTaskNew.getId(), ptypeSaleTaskNew.getTaskName(), ptypeSaleTaskNew.getTaskCode());
    }
    @PostMapping(value = "/getBillTotal")
    public PageResponse<PtypeSaleTaskDetailNew> getBillTotal(@RequestBody PageRequest<QuerySaleTaskStateParam> param) {
        PageResponse<PtypeSaleTaskDetailNew> ptypeSaleTaskDetailNewPageResponse = newSaleTaskService.querySaleTaskDetailCondition(param);
        return ptypeSaleTaskDetailNewPageResponse;
    }
    //查询销售任务日志
    @PostMapping("/listSaleTaskLog")
    public PageResponse<PlSalePeriodGroupLog> listSaleTaskLog(@RequestBody PageRequest<SaleTaskLogQueryParams> request) {
        request.getQueryParams().setProfileId(CurrentUser.getProfileId());
        return LogService.query(request);
    }
}