package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.redis.process.message.bll.RedisProcessMessage;
import com.wsgjp.ct.sale.biz.common.threads.ThreadUtils;
import com.wsgjp.ct.sale.biz.common.threads.ThreadUtils;
import com.wsgjp.ct.sale.biz.common.threads.groups.BillGroup;
import com.wsgjp.ct.sale.biz.jarvis.config.NeedProcessMsg;
import com.wsgjp.ct.sale.biz.jarvis.dto.*;
import com.wsgjp.ct.sale.biz.jarvis.dto.common.ResultDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.detailupdate.*;
import com.wsgjp.ct.sale.biz.jarvis.permission.PermissionSysConst;
import com.wsgjp.ct.sale.biz.jarvis.service.BillDeliverDetailUpdateService;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.biz.jarvis.service.common.Md5Service;
import com.wsgjp.ct.sale.biz.jarvis.service.eshopDetail.EshopDetailOperate;
import com.wsgjp.ct.sale.biz.jarvis.service.warehouseTaskDetail.WarehouseTaskDetailModifyService;
import com.wsgjp.ct.sale.biz.jarvis.strategy.StrategyUtils;
import com.wsgjp.ct.sale.biz.jarvis.strategy.entity.StrategyProcessLog;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillExStatusAsyncHandleHelper;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

@RestController
@Api(description = "发货单明細接口")
@RequestMapping("/${app.id}/jarvis/deliver/detail")
public class DeliverBillDetailController {
    private WarehouseTaskDetailModifyService warehouseTaskDetailModifyService;
    private BillDeliverDetailUpdateService billDeliverDetailUpdateService;
    private Md5Service deliverDetailsCommonService;
    private DeliverService deliverService;
    private Logger logger = LoggerFactory.getLogger(getClass());
    private BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper;
    private EshopDetailOperate eshopDetailOperate;

    public DeliverBillDetailController(WarehouseTaskDetailModifyService warehouseTaskDetailModifyService,
            BillDeliverDetailUpdateService billDeliverDetailUpdateService,
                                       Md5Service deliverDetailsCommonService,
                                       DeliverService deliverService,
                                       BillExStatusAsyncHandleHelper billExStatusAsyncHandleHelper,
                                       EshopDetailOperate eshopDetailOperate) {
        this.warehouseTaskDetailModifyService = warehouseTaskDetailModifyService;
        this.deliverDetailsCommonService = deliverDetailsCommonService;
        this.deliverService = deliverService;
        this.billExStatusAsyncHandleHelper = billExStatusAsyncHandleHelper;
        this.eshopDetailOperate = eshopDetailOperate;
    }

    @ApiOperation(value = "修改明细", notes = "（）")
    @PostMapping("updateBillDetail")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_MODIFY_DETAIL)
    public BillSaveResultDTO updateBillDetail(@RequestBody UpdateBillDetailDTO updateBillDetail) {
        updateBillDetail.setProfileId(CurrentUser.getProfileId());
        updateBillDetail.setScene(BaseUpdateDTO.Scene.HandModify);
        BillSaveResultDTO res = new BillSaveResultDTO();
        try {
            BillDeliverDTO bill = warehouseTaskDetailModifyService.updateDetail(updateBillDetail);
            res.setMessage("修改成功！");
            res.setDetails(bill.getDeliverDetail());
            res.setBill(bill);
            res.setResultType(ResultTypeEnum.SUCCESS);
        } catch (Exception e) {
            logger.error(MessageFormat.format("账套{0} 修改明细失败！{1} 消息：{2}", CurrentUser.getProfileId(), updateBillDetail.getWarehouseTaskId().toString(), e.getMessage()),e);
            if (e.getMessage() != null && e.getMessage().contains("Out of range value for column")) {
                res.setMessage(String.format("修改失败！有字段超过数据库最大限制！请优先检查商品体积及重量。"));
            } else {
                res.setMessage(String.format("修改失败！%s", e.getMessage()));
            }
            res.setResultType(ResultTypeEnum.ERROR);
        } finally {
            logger.info("修改明细重算徽标， vchcode:{}", updateBillDetail.getWarehouseTaskId());
        }
        return res;
    }

    @ApiOperation(value = "删除明细标记", notes = "（）")
    @PostMapping("deleteMarks")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_MODIFY_DETAIL)
    public void deleteMarks(@RequestBody DetailMarkUpdateDTO updateBillDetail) {
        updateBillDetail.setProfileId(CurrentUser.getProfileId());
        try {
            warehouseTaskDetailModifyService.deleteMarks(updateBillDetail.getWarehouseTaskId(),
                    updateBillDetail.getDetailId(),updateBillDetail.getMarks());
        } catch (Exception e) {
            logger.error(MessageFormat.format("账套{0} 修改明细标记失败！{1} 消息：{2}", CurrentUser.getProfileId(), updateBillDetail.getWarehouseTaskId().toString(), e.getMessage()),e);
        }
    }

    @ApiOperation(value = "批量删除明细", notes = "（）")
    @PostMapping("batchDeletedDetail")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_BATCH_DEL_PTYPE)
    public BillSaveResultDTO batchDeletedDetail(@RequestBody BatchDeleteUpdateDTO batchDeleteUpdateDTO) {
        RedisProcessMessage processMessage = new RedisProcessMessage(batchDeleteUpdateDTO.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();

        BillSaveResultDTO res = new BillSaveResultDTO();
        try {
            List<ResultDTO<Boolean>> errorList = warehouseTaskDetailModifyService.deletedDetails(batchDeleteUpdateDTO, processMessage);
            for (ResultDTO error:errorList) {
                messageLog.appendMsg(String.format("单据编号【%s】执行错误：%s",error.getBillNumber(),error.getMessage()));
            }
            res.setErrorList(errorList);
            res.setResultType(ResultTypeEnum.SUCCESS);
        } catch (Exception e) {
            logger.info(MessageFormat.format("账套{0} 批量删除明细失败！{1} 消息：{2}", CurrentUser.getProfileId(), batchDeleteUpdateDTO.getWarehouseTaskIds().toString(), e.getStackTrace()));
            res.setMessage(String.format("批量删除明细失败！%s", e.getMessage()));
            res.setResultType(ResultTypeEnum.ERROR);
        }
        finally {
            processMessage.setFinish();
        }
        return res;
    }

    @ApiOperation(value = "批量删除明细", notes = "（）")
    @PostMapping("asyncBatchDeletedDetail")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_BATCH_DEL_PTYPE)
    @NeedProcessMsg(threadPoolName = NeedProcessMsg.ProcessName.deleteDetailProcessName,redirectUrl = "sale/jarvis/deliver/detail/asyncBatchDeletedDetail",serviceName = "asyncBatchDeletedDetail")
    public BillSaveResultDTO asyncBatchDeletedDetail(@RequestBody BatchDeleteUpdateDTO batchDeleteUpdateDTO) {
        RedisProcessMessage processMessage = new RedisProcessMessage(batchDeleteUpdateDTO.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();

        BillSaveResultDTO res = new BillSaveResultDTO();
        try {
            List<ResultDTO<Boolean>> errorList = warehouseTaskDetailModifyService.deletedDetails(batchDeleteUpdateDTO, processMessage);
            for (ResultDTO error:errorList) {
                messageLog.appendMsg(String.format("单据编号【%s】执行错误：%s",error.getBillNumber(),error.getMessage()));
            }
            res.setErrorList(errorList);
            res.setResultType(ResultTypeEnum.SUCCESS);
        } catch (Exception e) {
            logger.info(MessageFormat.format("账套{0} 批量删除明细失败！{1} 消息：{2}", CurrentUser.getProfileId(), batchDeleteUpdateDTO.getWarehouseTaskIds().toString(), e.getStackTrace()));
            res.setMessage(String.format("批量删除明细失败！%s", e.getMessage()));
            res.setResultType(ResultTypeEnum.ERROR);
        }
        finally {
            processMessage.setFinish();
        }
        return res;
    }

//    @Deprecated
//    @ApiOperation(value = "批量删除退款明细,未找到使用，不再维护", notes = "（）")
//    @PostMapping("batchDeletedRefundDetail")
//    @PermissionCheck(key = PermissionSysConst.DELIVER_BATCH_DELETE_REFUND_DETAIL)
//    public BillSaveResultDTO batchDeletedRefundDetail(@RequestBody BaseUpdateDTO baseUpdateDTO) {
//        baseUpdateDTO.setProfileId(CurrentUser.getProfileId());
//        BillSaveResultDTO res = new BillSaveResultDTO();
//        try {
//            List<BigInteger> hasDeletedDetailVchcode = billDeliverDetailUpdateService.batchDeletedRefundDetail(baseUpdateDTO);
//            logger.info("批量删除退款明细重算徽标，vchcode: {}", hasDeletedDetailVchcode);
//            billExStatusAsyncHandleHelper.logoComprehensiveByFace(ErrorFace.看我看我别忘了改(hasDeletedDetailVchcode));
//            res.setResultType(ResultTypeEnum.SUCCESS);
//        } catch (Exception e) {
//            logger.info(MessageFormat.format("账套{0} 批量删除退款明细失败！{1} 消息：{2}", CurrentUser.getProfileId(), baseUpdateDTO.getWarehouseTaskIds().toString(), e.getStackTrace()));
//            res.setMessage(String.format("批量删除退款明细失败,%s", e.getMessage()));
//            res.setResultType(ResultTypeEnum.ERROR);
//        }
//        return res;
//    }

    @ApiOperation(value = "批量添加明细", notes = "（）")
    @PostMapping("batchAddDetail")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_BATCH_ADD_PTYPE)
    public BillSaveResultDTO batchAddDetail(@RequestBody BatchAddUpdateDTO batchAddUpdateDTO) {
        RedisProcessMessage processMessage = new RedisProcessMessage(batchAddUpdateDTO.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        BillSaveResultDTO res = new BillSaveResultDTO();
        try {
            batchAddUpdateDTO.setSaveLog(true);
            List<ResultDTO<Boolean>> result = warehouseTaskDetailModifyService.addDetails(batchAddUpdateDTO, processMessage);
            List<StrategyProcessLog> errors = new ArrayList<>(result.size());
            for (ResultDTO error:result) {
                if(!error.getResultType().equals(ResultTypeEnum.SUCCESS))
                {
                    StrategyProcessLog log = new StrategyProcessLog();
                    log.setTradeOrderId(error.getTradeId());
                    log.setBillNumber(error.getBillNumber());
                    log.setWarehouseTaskId(error.getWarehouseTaskId());
                    log.setTaskNumber(error.getTaskNumber());
                    log.setForceAudit(error.getResultType().equals(ResultTypeEnum.CONFIRM));
                    errors.add(log);
                }
            }
            StrategyUtils.cacheProcessMessage(batchAddUpdateDTO.getProcessId(), errors);
            res.setResultType(ResultTypeEnum.SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();

            logger.info(MessageFormat.format("账套{0} 批量添加明细失败！{1} 消息：{2}", CurrentUser.getProfileId(), batchAddUpdateDTO.getWarehouseTaskIds().toString(), e.getStackTrace()));
            if (e.getMessage() != null && e.getMessage().contains("Out of range value for column")) {
                res.setMessage(String.format("批量添加明细失败！有字段超过数据库最大限制！请优先检查商品体积及重量。"));
            } else {
                res.setMessage(String.format("批量添加明细失败！" + e.getMessage()));
            }
            messageLog.appendMsg(res.getMessage());
            res.setResultType(ResultTypeEnum.ERROR);
        }
        finally {
            processMessage.setFinish();
        }
        return res;
    }

    @ApiOperation(value = "批量添加明细", notes = "（）")
    @PostMapping("asyncBatchAddDetail")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_BATCH_ADD_PTYPE)
    @NeedProcessMsg(threadPoolName = NeedProcessMsg.ProcessName.addDetailProcessName,redirectUrl = "sale/jarvis/deliver/detail/asyncBatchAddDetail",serviceName = "asyncBatchAddDetail")
    public BillSaveResultDTO asyncBatchAddDetail(@RequestBody BatchAddUpdateDTO batchAddUpdateDTO) {
        RedisProcessMessage processMessage = new RedisProcessMessage(batchAddUpdateDTO.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();
        BillSaveResultDTO res = new BillSaveResultDTO();
        try {
            batchAddUpdateDTO.setSaveLog(true);
            List<ResultDTO<Boolean>> result = warehouseTaskDetailModifyService.addDetails(batchAddUpdateDTO, processMessage);
            List<StrategyProcessLog> errors = new ArrayList<>(result.size());
            for (ResultDTO error:result) {
                if(!error.getResultType().equals(ResultTypeEnum.SUCCESS))
                {
                    StrategyProcessLog log = new StrategyProcessLog();
                    log.setTradeOrderId(error.getTradeId());
                    log.setBillNumber(error.getBillNumber());
                    log.setWarehouseTaskId(error.getWarehouseTaskId());
                    log.setTaskNumber(error.getTaskNumber());
                    log.setForceAudit(error.getResultType().equals(ResultTypeEnum.CONFIRM));
                    log.setContent(error.getMessage());
                    errors.add(log);
                }
            }
            StrategyUtils.cacheProcessMessage(batchAddUpdateDTO.getProcessId(), errors);
            res.setResultType(ResultTypeEnum.SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info(MessageFormat.format("账套{0} 批量添加明细失败！{1} 消息：{2}", CurrentUser.getProfileId(), batchAddUpdateDTO.getWarehouseTaskIds().toString(), e.getStackTrace()));
            if (e.getMessage() != null && e.getMessage().contains("Out of range value for column")) {
                res.setMessage(String.format("批量添加明细失败！有字段超过数据库最大限制！请优先检查商品体积及重量。"));
            } else {
                res.setMessage(String.format("批量添加明细失败！" + e.getMessage()));
            }
            messageLog.appendMsg(res.getMessage());
            res.setResultType(ResultTypeEnum.ERROR);
        }
        finally {
            processMessage.setFinish();
        }
        return res;
    }

    @ApiOperation(value = "批量替换明细", notes = "（）")
    @PostMapping("batchUpdateDetail")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_BATCH_UPDATE_PTYPE)
    public BillSaveResultDTO batchUpdateDetail(@RequestBody BatchChangeUpdateDTO batchChangeUpdateDTO) {
        RedisProcessMessage processMessage = new RedisProcessMessage(batchChangeUpdateDTO.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();

        BillSaveResultDTO res = new BillSaveResultDTO();
        try {
            batchChangeUpdateDTO.setThreadName(ThreadUtils.modifyDetail);
            List<ResultDTO<Boolean>> errorList = warehouseTaskDetailModifyService.updateDetails(batchChangeUpdateDTO, processMessage);
            for (ResultDTO error:errorList) {
                messageLog.appendMsg(String.format("单据编号【%s】执行错误：%s",error.getBillNumber(),error.getMessage()));
            }
        } catch (Exception e) {
            logger.info(MessageFormat.format("账套{0} 批量替换明细失败！{1} 消息：{2}", CurrentUser.getProfileId(), batchChangeUpdateDTO.getWarehouseTaskIds().toString(), e.getStackTrace()));
            if (e.getMessage() != null && e.getMessage().contains("Out of range value for column")) {
                res.setMessage(String.format("批量替换明细失败！有字段超过数据库最大限制！请优先检查商品体积及重量。"));
            } else {
                res.setMessage(String.format("批量替换明细失败！" + e.getMessage()));
            }
            messageLog.appendMsg(res.getMessage());
            res.setResultType(ResultTypeEnum.ERROR);
        }
        finally {
            processMessage.setFinish();
        }
        return res;
    }

    @ApiOperation(value = "批量替换明细", notes = "（）")
    @PostMapping("asyncBatchUpdateDetail")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_BATCH_UPDATE_PTYPE)
    @NeedProcessMsg(threadPoolName = NeedProcessMsg.ProcessName.updateDetailProcessName,redirectUrl = "sale/jarvis/deliver/detail/asyncBatchUpdateDetail",serviceName = "asyncBatchUpdateDetail")
    public BillSaveResultDTO asyncBatchUpdateDetail(@RequestBody BatchChangeUpdateDTO batchChangeUpdateDTO) {
        RedisProcessMessage processMessage = new RedisProcessMessage(batchChangeUpdateDTO.getProcessId());
        RedisProcessMessage.MsgLogger messageLog = processMessage.getMsgLogger();

        BillSaveResultDTO res = new BillSaveResultDTO();
        try {
            batchChangeUpdateDTO.setThreadName(ThreadUtils.modifyDetail);
            List<ResultDTO<Boolean>> errorList = warehouseTaskDetailModifyService.updateDetails(batchChangeUpdateDTO, processMessage);
            for (ResultDTO error:errorList) {
                messageLog.appendMsg(String.format("单据编号【%s】执行错误：%s",error.getBillNumber(),error.getMessage()));
            }
        } catch (Exception e) {
            logger.info(MessageFormat.format("账套{0} 批量替换明细失败！{1} 消息：{2}", CurrentUser.getProfileId(), batchChangeUpdateDTO.getWarehouseTaskIds().toString(), e.getStackTrace()));
            if (e.getMessage() != null && e.getMessage().contains("Out of range value for column")) {
                res.setMessage(String.format("批量替换明细失败！有字段超过数据库最大限制！请优先检查商品体积及重量。"));
            } else {
                res.setMessage(String.format("批量替换明细失败！" + e.getMessage()));
            }
            messageLog.appendMsg(res.getMessage());
            res.setResultType(ResultTypeEnum.ERROR);
        }
        finally {
            processMessage.setFinish();
        }
        return res;
    }

    @ApiOperation(value = "序列号检查", notes = "（）")
    @PostMapping("checkSnno")
    @PermissionCheck(key = PermissionSysConst.DELIVER_AUDIT_MODIFY_DETAIL)
    public List<CheckSnnoResultDTO> checkSnno(@RequestBody UpdateBillDetailDTO updateBillDetail) throws Exception {
        return warehouseTaskDetailModifyService.checkSnno(CurrentUser.getProfileId(), updateBillDetail);
    }

//    @ApiOperation(value = "修改主表商家整单优惠金额", notes = "（）")
//    @PostMapping("modifySellerPreferentialTotal")
//    public BillDeliverDTO modifySellerPreferentialTotal(@RequestBody BillDeliverDTO billDeliverDTO) throws Exception {
//        BillDeliverFilterParams filterParams = new BillDeliverFilterParams();
//        filterParams.filterDetail();
//        List<BillDeliverDTO> billInfos = deliverService.getDeliverListByVchcodes(CurrentUser.getProfileId(), Arrays.asList(billDeliverDTO.getVchcode()), filterParams);
//        if (billInfos.size() == 0) {
//            throw new RuntimeException(Language.get("notBill", "没有单据有相同明细需要删除！"));
//        }
//        List<BillDeliverDetailDTO> details = deliverService.listBillDeliverDetails(CurrentUser.getProfileId(), billDeliverDTO.getVchcode(),
//                false, billDeliverDTO.getVchtype());
//        if (!deliverDetailsCommonService.checkDetailMd5(CurrentUser.getProfileId(),billDeliverDTO.getVchcode(),billDeliverDTO.getDetailsMd5(),Md5CheckEnum.DETAIL)) {
//            throw new RuntimeException("明细已被修改，请重新查询！");
//        }
//        billDeliverDetailUpdateService.modifySellerPreferentialTotal(billDeliverDTO, details, billInfos.get(0));
//        return billDeliverDTO;
//    }

//    @Deprecated
//    @ApiOperation(value = "填写序列号及批次，未找到使用，不再维护", notes = "（）")
//    @PostMapping("fillBatchAndSnno")
//    public ResultDTO<Boolean> fillBatchAndSnno(@RequestBody List<DeliverDetailBatchAndSnnoDTO> detailBatchAndSnnos) throws Exception {
//        ResultDTO<Boolean> res = billDeliverDetailUpdateService.fillBatchAndSnno(detailBatchAndSnnos, OperationEnum.INTERFACE_MODIFY);
//        return res;
//    }

    @ApiOperation(value = "仓储修改批次号", notes = "（）")
    @PostMapping("modifyBatch")
    public ResultDTO<WarehouseTaskUpdateDTO> modifyBatch(@RequestBody WarehouseTaskBatchUpdateDTO request) {
        logger.error("仓储修改明细：{}",JsonUtils.toJson(request));
        return warehouseTaskDetailModifyService.modifyBatch(request);
    }

    @ApiOperation(value = "修改平台商品对应关系", notes = "（）")
    @PostMapping("modifyRelation")
    public void modifyRelation(@RequestBody EshopRelationUpdateDTO request) {
        eshopDetailOperate.modifyEshopDetailRelation(request.getDetails(),request.getBill());
    }
}
