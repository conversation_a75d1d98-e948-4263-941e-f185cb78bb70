package com.wsgjp.ct.sale.web.print;

import com.wsgjp.ct.sale.biz.jarvis.entity.templatecenter.*;
import com.wsgjp.ct.sale.biz.jarvis.state.TemplateTypeEnum;
import com.wsgjp.ct.sale.biz.print.service.TemplateCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Api("模板中心获取各个服务模板信息接口")
@RequestMapping("/${app.id}/getServiceTemplateInfos")
public class GetServiceTemplateInfoController {
    private TemplateCenterService templateCenterService;

    public GetServiceTemplateInfoController(TemplateCenterService templateCenterService) {
        this.templateCenterService = templateCenterService;
    }

    @ApiOperation(value = "获取各个服务的具体业务服务类型", notes = "获取各个服务的具体业务服务类型")
    @PostMapping("/getBusinessTemplateTypes")
    public GetBusinessTypeCodeResult getBusinessTemplateTypes(@RequestBody TemplateTypeParam templateTypeEnum) {
        GetBusinessTypeCodeResult businessTypeCodes = new GetBusinessTypeCodeResult();
        if (templateTypeEnum.getTemplateTypeEnum() == TemplateTypeEnum.FREIGHT_TEMPLATE) {
            return businessTypeCodes;

        } else if (templateTypeEnum.getTemplateTypeEnum() == TemplateTypeEnum.Bill_TEMPLATE) {
            return templateCenterService.getBillTemplateTypes();

        } else if (templateTypeEnum.getTemplateTypeEnum() == TemplateTypeEnum.Barcode_TEMPLATE) {
            return templateCenterService.getBarcodeTemplateTypes();

        }
        return businessTypeCodes;
    }

    @ApiOperation(value = "获取具体业务服务类型对应的模板字段信息", notes = "获取具体业务服务类型对应的模板字段信息")
    @PostMapping("/getBusinessTemplateTypeInfo")
    public GetBusinessTemplateTypeInfoResult getBusinessTemplateTypeInfo(@RequestBody BusinessTemplateTypeCode businessTemplateTypeCode) {
        return templateCenterService.getBusinessTemplateTypeInfo(businessTemplateTypeCode);
    }

    @ApiOperation(value = "获取物流所需要自定义字段", notes = "获取具体业务服务类型对应的模板字段信息")
    @PostMapping("/getConfigDtos")
    public GetConfigDtosResult getConfigDtos() {
        return templateCenterService.getConfigDtos();
    }
}
