package com.wsgjp.ct.sale.web.jarvis.response;

import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.BillDeliverState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: chenjunhong on 2022-03-03 11:30
 */
@ApiModel(value = "仓储流程单据查询响应")
public class WarehouseCheckBillResponse  implements Serializable {
    @ApiModelProperty(value = "存在发货单")
    private boolean hasBill;
    @ApiModelProperty(value = "发货单响应集合")
    private List<WarehouseBill> bills;

    public boolean isHasBill() {
        return hasBill;
    }

    public void setHasBill(boolean hasBill) {
        this.hasBill = hasBill;
    }

    public List<WarehouseBill> getBills() {
        return bills;
    }

    public void setBills(List<WarehouseBill> bills) {
        this.bills = bills;
    }

    public void putDeliverBills(List<BillDeliverDTO> bills) {
        if(CollectionUtils.isEmpty(bills)){
            return;
        }
        this.bills=new ArrayList<>();
        this.bills.addAll(bills.stream().map(p->{
            WarehouseBill bill=new WarehouseBill();
            bill.setBillDate(p.getBillDate());
            bill.setBillNumber(p.getBillNumber());
            bill.setState(p.getState());
            bill.setSubmitTime(p.getSubmitTime());
            bill.setTradeOrderId(p.getTradeOrderId());
            bill.setVchcode(p.getVchcode());
            return bill;
        }).collect(Collectors.toList()));
    }
}

class WarehouseBill implements Serializable{
    /**
     * 单据编号
     */
    private String billNumber;
    /**
     * 录单日期
     */
    private Date billDate;
    private BigInteger vchcode;
    /**
     * 提交时间
     */
    private Date submitTime;
    /**
     * 网上订单号
     */
    private String tradeOrderId;
    private BillDeliverState state;

    public String getBillNumber() {
        return billNumber;
    }

    public void setBillNumber(String billNumber) {
        this.billNumber = billNumber;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public BigInteger getVchcode() {
        return vchcode;
    }

    public void setVchcode(BigInteger vchcode) {
        this.vchcode = vchcode;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public BillDeliverState getState() {
        return state;
    }

    public void setState(BillDeliverState state) {
        this.state = state;
    }
}

