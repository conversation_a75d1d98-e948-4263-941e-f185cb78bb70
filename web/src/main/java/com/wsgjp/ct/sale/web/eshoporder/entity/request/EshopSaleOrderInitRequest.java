package com.wsgjp.ct.sale.web.eshoporder.entity.request;

import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BaseQuery;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2020/2/12 0012 17:10
 */
public class EshopSaleOrderInitRequest extends BaseQuery {
    private String modeStr;
    private BigInteger id;
    private BigInteger btypeId;

    public BigInteger getBtypeId() {
        return btypeId;
    }

    public void setBtypeId(BigInteger btypeId) {
        this.btypeId = btypeId;
    }

    public String getModeStr() {
        return modeStr;
    }

    public void setModeStr(String modeStr) {
        this.modeStr = modeStr;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }
}
