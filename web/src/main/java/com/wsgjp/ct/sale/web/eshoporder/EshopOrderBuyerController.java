package com.wsgjp.ct.sale.web.eshoporder;

import com.wsgjp.ct.sale.biz.eshoporder.entity.SaveBuyerResult;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.ModifyBuyerParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryBuyerRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.EshopOrderApiService;
import com.wsgjp.ct.sale.biz.eshoporder.service.receiver.EshopBuyerService;
import io.swagger.annotations.Api;
import ngp.utils.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/20 0020 9:52
 */
@Api(tags = "买家账号信息相关")
@RequestMapping("${app.id}/eshoporder/buyer")
@RestController
public class EshopOrderBuyerController {

    private final EshopBuyerService service;
    private final EshopOrderApiService apiService;

    public EshopOrderBuyerController(EshopBuyerService service, EshopOrderApiService apiService) {
        this.service = service;
        this.apiService = apiService;
    }

    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public List<EshopBuyer> getBuyerByAccount(@RequestBody QueryBuyerRequest request) {
        String text = request.getText();
        if (text == null || "".equals(text)) {
            return new ArrayList<>();
        }
        return service.getBuyerList(text, request.getFilterType());
    }

    @RequestMapping(value = "/getById", method = RequestMethod.POST)
    public EshopBuyer getBuyerById(@RequestBody QueryBuyerRequest request) {
        if (request.getBuyerId() == null) {
            throw new RuntimeException("查询条件buyerId不能为空");
        }
        return service.queryBuyer(request);
    }

    @RequestMapping(value = "/getByIds", method = RequestMethod.POST)
    public List<EshopBuyer> getBuyerByIds(@RequestBody QueryBuyerRequest request) {
        if (request.getBuyerIdList() == null || request.getBuyerIdList().size() == 0) {
            throw new RuntimeException("查询条件buyerIdList不能为空");
        }
        return service.queryBuyerList(request);
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public SaveBuyerResult saveBuyer(@RequestBody EshopBuyer buyer) {
        SaveBuyerResult result = new SaveBuyerResult();
        try {
            service.saveBuyer(buyer);
            result.setBuyer(buyer);
        } catch (RuntimeException ex) {
            result.setSuccess(false);
            result.setErrorMsg(ex.getMessage());
        }
        return result;
    }

    @RequestMapping(value = "/modifyById", method = RequestMethod.POST)
    public boolean modifyBuyer(@RequestBody EshopBuyer buyer) {
        return service.modifyBuyerById(buyer);
    }

    @RequestMapping(value = "/modifyOnline", method = RequestMethod.POST)
    public SaveBuyerResult saveBuyerOnline(@RequestBody ModifyBuyerParameter parameter) {
        SaveBuyerResult result = new SaveBuyerResult();
        EshopBuyer buyer = parameter.getBuyer();
        if (buyer == null) {
            result.setSuccess(false);
            result.setErrorMsg("买家实体｛buyer｝为空");
            return result;
        }
        if(StringUtils.isEmpty(parameter.getTradeId())){
            result.setSuccess(false);
            result.setErrorMsg("订单号为空");
            return result;
        }
        return apiService.saveBuyerOnline(parameter);
    }
}
