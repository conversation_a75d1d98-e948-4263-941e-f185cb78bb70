package com.wsgjp.ct.sale.web.jarvis.response;

import com.wsgjp.ct.sale.biz.jarvis.dto.DeliverCustomerQueryDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.DillDeliverState;
import com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public class WmsOutManagerInitResponse {
    private String startTime;
    private String endTime;
    private List<DeliverCustomerQueryDTO> quickFilters;
    private List<DillDeliverState> filterTypes;
    private List<Organization> eshops;
    private List<Stock> ktypes;
    private List<Freight> freights;
    private List<DillDeliverState> billTypes;
    private List<Employee> employees;
    private List<DillDeliverState> processStates;
    private List<DillDeliverState> orderTypes;
    private List<Btype> btypes;
    private boolean propsEnabled;
    private int qtyMaxLength;
    private boolean batchEnabled;
    private boolean serialNumberEnabled;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<DeliverCustomerQueryDTO> getQuickFilters() {
        return quickFilters;
    }

    public void setQuickFilters(List<DeliverCustomerQueryDTO> quickFilters) {
        this.quickFilters = quickFilters;
    }

    public List<DillDeliverState> getFilterTypes() {
        return filterTypes;
    }

    public void setFilterTypes(List<DillDeliverState> filterTypes) {
        this.filterTypes = filterTypes;
    }

    public List<Organization> getEshops() {
        return eshops;
    }

    public void setEshops(List<Organization> eshops) {
        this.eshops = eshops;
    }

    public List<Stock> getKtypes() {
        return ktypes;
    }

    public void setKtypes(List<Stock> ktypes) {
        this.ktypes = ktypes;
    }

    public List<Freight> getFreights() {
        return freights;
    }

    public void setFreights(List<Freight> freights) {
        this.freights = freights;
    }

    public List<DillDeliverState> getBillTypes() {
        return billTypes;
    }

    public void setBillTypes(List<DillDeliverState> billTypes) {
        this.billTypes = billTypes;
    }

    public List<Employee> getEmployees() {
        return employees;
    }

    public void setEmployees(List<Employee> employees) {
        this.employees = employees;
    }

    public List<DillDeliverState> getProcessStates() {
        return processStates;
    }

    public void setProcessStates(List<DillDeliverState> processStates) {
        this.processStates = processStates;
    }

    public List<DillDeliverState> getOrderTypes() {
        return orderTypes;
    }

    public void setOrderTypes(List<DillDeliverState> orderTypes) {
        this.orderTypes = orderTypes;
    }

    public List<Btype> getBtypes() {
        return btypes;
    }

    public void setBtypes(List<Btype> btypes) {
        this.btypes = btypes;
    }

    public boolean isPropsEnabled() {
        return propsEnabled;
    }

    public void setPropsEnabled(boolean propsEnabled) {
        this.propsEnabled = propsEnabled;
    }

    public int getQtyMaxLength() {
        return qtyMaxLength;
    }

    public void setQtyMaxLength(int qtyMaxLength) {
        this.qtyMaxLength = qtyMaxLength;
    }

    public boolean isBatchEnabled() {
        return batchEnabled;
    }

    public void setBatchEnabled(boolean batchEnabled) {
        this.batchEnabled = batchEnabled;
    }

    public boolean isSerialNumberEnabled() {
        return serialNumberEnabled;
    }

    public void setSerialNumberEnabled(boolean serialNumberEnabled) {
        this.serialNumberEnabled = serialNumberEnabled;
    }
}
