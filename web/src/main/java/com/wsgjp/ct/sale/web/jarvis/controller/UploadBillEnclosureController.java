package com.wsgjp.ct.sale.web.jarvis.controller;

import com.wsgjp.ct.sale.biz.jarvis.dto.upload.UploadBillEnclosureDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.upload.UploadBillEnclosureRequest;
import com.wsgjp.ct.sale.biz.jarvis.service.upload.UploadBillEnclosureService;
import com.wsgjp.ct.sale.biz.jarvis.state.EnclosureTypeEnum;
import com.wsgjp.ct.sale.biz.jarvis.state.UploadEnclosureTypeEnum;
import com.wsgjp.ct.sale.web.jarvis.response.BaseResponse;
import com.wsgjp.ct.sale.web.jarvis.response.UploadResponse;
import com.wsgjp.ct.support.context.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.loadbalancer.context.RouteThreadLocal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UploadBillEnclosure
 */
@Api(value = "/upload", tags = {"附件上传接口"})
@RestController
@RequestMapping("/${app.id}/jarvis/upload")
public class UploadBillEnclosureController {

    @Autowired
    private UploadBillEnclosureService uploadBillEnclosure;

    @ApiOperation(value = "附件上传")
    @PostMapping(value = {"/uploadBillEnclosure"})
    public BaseResponse uploadBillEnclosure(@RequestParam("file") MultipartFile file ,
                                            @RequestParam("vchcode") BigInteger vchcode,
                                            @RequestParam("warehouseTaskId") BigInteger warehouseTaskId,
                                            @RequestParam("eshopId") BigInteger eshopId,
                                            @RequestParam("enclosureType") EnclosureTypeEnum enclosureType,
                                            @RequestParam("uploadEnclosureType") UploadEnclosureTypeEnum uploadEnclosureType) {
        UploadBillEnclosureRequest request = new UploadBillEnclosureRequest();
        request.setProfileId(RouteThreadLocal.getRoute().getProfileId());
        request.setWarehouseTaskId(warehouseTaskId);
        request.setVchcode(vchcode);
        request.setEshopId(eshopId);
        request.setEnclosureType(enclosureType);
        request.setUploadEnclosureType(uploadEnclosureType);
        uploadBillEnclosure.uploadBillEnclosure(file,request);
        return UploadResponse.success();
    }

    @ApiOperation(value = "附件备注修改")
    @PostMapping(value = {"/updateEnclosureMemo"})
    public BaseResponse updateEnclosureMemo(@RequestBody UploadBillEnclosureDTO uploadBillEnclosureDTO) {
        uploadBillEnclosure.updateEnclosureMemo(uploadBillEnclosureDTO);
        return UploadResponse.success();
    }

    @ApiOperation(value = "删除附件")
    @PostMapping(value = {"/deleteEnclosureById"})
    public BaseResponse deleteEnclosureById(@RequestParam("id") BigInteger id,
                                            @RequestParam("uploadEnclosureType") UploadEnclosureTypeEnum uploadEnclosureType) {
        UploadBillEnclosureRequest request = new UploadBillEnclosureRequest();
        request.setProfileId(RouteThreadLocal.getRoute().getProfileId());
        request.setId(id);
        request.setUploadEnclosureType(uploadEnclosureType);
        uploadBillEnclosure.deleteById(request);
        return UploadResponse.success();
    }

    @ApiOperation(value = "检查附件是否被删除")
    @PostMapping(value = {"/checkEnclosure"})
    public BaseResponse checkEnclosure(@RequestParam("id") BigInteger id) {
        return UploadResponse.success(uploadBillEnclosure.checkEnclosure(id,CurrentUser.getProfileId()));
    }

    @ApiOperation(value = "附件列表查询")
    @GetMapping(value = {"/enclosureList"})
    public BaseResponse enclosureList(@RequestParam("vchcode") BigInteger vchcode,@RequestParam("warehouseTaskId") BigInteger warehouseTaskId) {
        if(null!=warehouseTaskId&&warehouseTaskId.longValue()>0){
            return UploadResponse.success(uploadBillEnclosure.enclosureListByTaskIds(Arrays.asList(warehouseTaskId),CurrentUser.getProfileId()));
        }else{
            return UploadResponse.success(uploadBillEnclosure.enclosureListByVchcodes(Arrays.asList(vchcode),CurrentUser.getProfileId()));
        }
    }

    @ApiOperation(value = "附件类型修改")
    @PostMapping(value = {"/updateEnclosureType"})
    public BaseResponse updateEnclosureType(@RequestBody UploadBillEnclosureDTO request) {
        uploadBillEnclosure.updateEnclosureType(request);
        return UploadResponse.success();
    }

    @ApiOperation(value = "同步附件")
    @PostMapping(value = {"/syncEnclosure"})
    public BaseResponse syncEnclosure(@RequestBody UploadBillEnclosureDTO request) {
        uploadBillEnclosure.syncEnclosure(request);
        return UploadResponse.success();
    }

}
