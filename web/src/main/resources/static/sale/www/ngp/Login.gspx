<?xml version="1.0" encoding="UTF-8"?>
<Page xmlns="Craba.UI" Title="${modName}登录" ActionType="${modName}.LoginAction, ${modName}/Login.js" DataSource='${formData}'>
    <FlexColumn CssClass='FlexCenter MainBg'>
        <FlowPanel Caption='Ngp模块登录' CssClass='VertItem' LayoutDirection='Vert' ItemLabelWidth='70' ItemCssClass='FlexAuto'>
            <TextEdit Label='公司名称:' DataField='companyName' Required="true" />
            <TextEdit Label='职员名称:' DataField='employeeName' Required="true" />
            <DropDownEdit Label='指定产品:' DataField='productId' DataSource='${formData.products}' DataTextField='name' DataValueField='id' Required="true" />
            <HBlock Label='服务版本:'>
                <TextEdit DataField='deploy' CssClass='FlexAuto' NullDisplayText='不填写则使用默认账套账本' />
                <DropDownEdit DataField='productDeploy' />
            </HBlock>
            <TextEdit Label='本地端口:' DataField='localPort' Required="true" />

            <Grid ID='grid' DataField="routes" AutoMaxRowCount="8" AllowConfig='false' ReadOnly='false'>
                <TextColumn Caption="模块名称" DataField="serverName" AllowStretch='true' NullDisplayText='微服务名称' />
                <TextColumn Caption="Ip" DataField="serverIp" AllowStretch='true'  NullDisplayText='微服务IP地址' />
                <TextColumn Caption="端口" DataField="serverPort" AllowStretch='true'  NullDisplayText='微服务端口号' />
                <RowDeleteColumn Caption="操作"/>
            </Grid>

            <FlexBlock CssClass='BottomBlock FlexRight'>
                <Button Text='添加路由' OnClick='addRoute' />
                <Button Text='登录' CssClass='SpecialButton' OnClick="doLogin" />
            </FlexBlock>
        </FlowPanel>
    </FlexColumn>

    <Style>
        .MainBg{background-color: #374979;}
        .MainBg .FlowPanel{border-radius:8px;box-shadow:0 0 18px #111;width:600px;}
        .MainBg .FlowPanel .FlowItem{padding:10px 20px 10px 0;}
        .MainBg .GridBlock{margin:10px 20px}
    </Style>
</Page>