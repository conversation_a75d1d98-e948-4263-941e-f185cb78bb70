<!DOCTYPE html>
<html lang="zh-cn" translate="no">

<head>
    <title>${modName}登录</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <link rel="shortcut icon" type="image/x-icon" href="${modName}/favicon.ico" />
</head>

<body>
    <script>
        var modeName = '${modName}'; // 按各组的模块名称修改

        function startPage() { // 供agency内部调用
            var authorization = $common.getCookie('ngp-authorization');
            var ngp_route = $common.getCookie('ngp-route');
            if (authorization && ngp_route) {
                $ms.router = {//设置请求路由
                    debugMs: JSON.parse($common.getCookie('debugMs')),
                    'ngp-authorization': 'jwt',
                    'ngp-router': 'ngprt'
                };

                var routes = localStorage.getItem("routes");
                if (routes) {
                    routes = JSON.parse(routes);
                    if (routes.length > 0) {
                        var server = {};
                        for (var i = 0; i < routes.length; i++) {
                            var r = routes[i];
                            server[r.serverName] = r.serverIp + ":" + r.serverPort;
                        }
                        $ms.server = server;
                    }
                }

                $skin.loadCss(modeName + '/skins/craba.min.css,shell/skins/shell.css', 'login', function() {
                    $craba.run(modeName + '/Main.gspx', modeName);

                    createLoginBtn();
                });
            } else {
                $skin.loadCss(modeName + '/skins/craba.min.css', 'login', function() {
                    $craba.run(modeName + '/Login.gspx', modeName);
                });
            }
        }

        function createLoginBtn() {
            var btn = $common.createClassDiv('Button');
            btn.innerText = '重新登录';
            btn.style.cssText = 'z-index: 9999;font-weight: bold;background-color:#03abf5;border:none;position:fixed;bottom:5px;right:5px;';
            document.body.appendChild(btn);
            $common.addClickHandler(btn, function() {
                localStorage.removeItem("route");
                $common.removeCookie('ngp-authorization');
                location.reload();
            });
        }

        function loadScript(src, cb) {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.onload = script.onreadystatechange = function() {
                if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete") {
                    script.onload = script.onreadystatechange = script.onerror = null;
                    cb();
                }
            };
            script.onerror = function() {
                script.onload = script.onreadystatechange = script.onerror = null;
                cb();
            };
            script.src = src;
            document.getElementsByTagName('HEAD')[0].appendChild(script);
        }

        var agencyUrl = 'js/agency.js?vc=' + (new Date()).getTime(); // 加时间戳防止浏览器缓存
        loadScript(agencyUrl, function() {
            $agency.load(['js/craba.min.js',
                'js/crabaEx.min.js',
                'js/math.min.js',
                'js/crabaNgp.js'
            ], {}, startPage); // 这里写死首页脚本,agency内部使用。所有脚本由agency内部版本号防止浏览器缓存
        });
    </script>
</body>

</html>