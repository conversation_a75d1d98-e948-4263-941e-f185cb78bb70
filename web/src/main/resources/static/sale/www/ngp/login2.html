<!DOCTYPE html>
<html lang="en">

<head>
    <title>...</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico"/>
    <link href="http://************:56789/bootstrap.min.css" rel="stylesheet"/>
    <link type="text/css" rel="stylesheet" href="skins\craba.min.css"/>
    <link type="text/css" rel="stylesheet" href="skins\blueSky\skin.css"/>

    <style type="text/css">
        .reload_button {
            z-index: 2;
            cursor: pointer;
            font-weight: bold;
            background-color: #03abf5;
            border: none;
            position: fixed;
            bottom: 5px;
            right: 5px;
            font-size: 12px;
        }

        .modal {
            z-index: 1;
            display: block;
            opacity: 1;
        }

        .modal-dialog {
            top: 50%;
            transform: translateY(-55%) !important;
        }


        .login_div {
            position: fixed;
            background-color: #374979;
            height: 100%;
            width: 100%;
        }

        .control-label {
            padding-right: 0 !important;
        }
    </style>
</head>
<body>
<div id="app">
    <input type="button" id='reload' class="reload_button" value="跳转到登录" v-on:click="jumpToLoad"/>

    <div id="login" class="login_div">
        <div class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">NGP模块登录</h4>
                    </div>
                    <div class="modal-body ">
                        <div v-if="!isDone" class="progress">
                            <div class="progress-bar  progress-bar-success progress-bar-striped active"
                                 role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100"
                                 :style="progress">
                                <span class="sr-only">45% Complete</span>
                            </div>
                        </div>
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">公司名称：</label>
                                <div class="col-sm-10">
                                    <input type='text' class="form-control" id="profileId" v-model="companyName"
                                           placeholder="公司名称"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">职员名称：</label>
                                <div class="col-sm-10">
                                    <input type='text' class="form-control" id="employeeId" v-model="employeeName"
                                           placeholder="职员登录名"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">指定产品：</label>
                                <div class="col-sm-10">
                                    <select type='text' id="productId" class="form-control" v-model="productId"
                                            v-on:change="productChange">
                                        <option v-for="product in products" :value="product.id">{{product.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">服务版本：</label>
                                <div class="col-sm-5">
                                    <input type='text' class="form-control" id="deploy" v-model="deploy"
                                           placeholder="不填写则使用账套版本"/>
                                </div>
                                <div class="col-sm-5">
                                    <select type='text' class="form-control" v-model="productDeploy">
                                        <option v-for="pd in productDeploys" :value="pd.deployName">{{pd.deployName}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">本地端口：</label>
                                <div class="col-sm-10">
                                    <input type='text' class="form-control" id="port" v-model="localPort"
                                           placeholder="你启动的本地后台服务端口"/>
                                </div>

                            </div>

                        </div>

                        <div>
                            <table class="table table-bordered">
                                <caption>自定义路由</caption>
                                <thead>
                                <tr>
                                    <td>模块名称</td>
                                    <td>IP</td>
                                    <td>端口</td>
                                    <td>操作</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="(route,index) in routes">
                                    <td><input type="text" class="form-control" placeholder="微服务名称"
                                               v-model="route.serverName"/></td>
                                    <td><input type="text" class="form-control" placeholder="微服务IP"
                                               v-model="route.serverIp"/></td>
                                    <td><input type="text" class="form-control" placeholder="微服务端口号"
                                               v-model="route.serverPort"/></td>
                                    <td>
                                        <button class="btn btn-danger" v-on:click="deleteRoute" :data-index="index">删除
                                        </button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" v-on:click="addRoute">添加路由</button>
                        <button type="button" class="btn btn-primary" v-on:click="doLogin">登录</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<script src="http://************:56789/jquery.min.js"></script>
<script src="http://************:56789/bootstrap.min.js"></script>
<script src="http://************:56789/vue.min.js"></script>
<script type="text/javascript" src="js/agency.js?v=3"></script>

<script>
    var app = new Vue({
        el: "#app",
        data: {
            companyName: "",
            employeeName: "",
            productId: "0",
            deploy: "",
            localPort: "",
            routes: [],
            progress: "width: 20%",
            isDone: false,
            products: [],
            deploys: {},
            productDeploys: [],
            productDeploy: ""
        },
        created: function () {
            var agencyConfig = {}; // 参考文档末尾的对象说明
            $agency.load(['js/craba.min.js?v=3',
                'js/crabaEx.min.js?v=3',
                'js/math.min.js?v=3',
                'js/crabaNgp.js?v=3'], agencyConfig, function () {
                var routes = localStorage.getItem("routes");
                if (routes != undefined && routes != "") {
                    routes = JSON.parse(routes);
                    app.routes = routes;
                }
                var user = localStorage.getItem("user");
                if (user != undefined && user != "") {
                    user = JSON.parse(user);
                    app.companyName = user.companyName;
                    app.employeeName = user.employeeName;
                    app.productId = user.productId;
                    app.deploy = user.deploy;
                    app.localPort = user.port || "";
                    app.productDeploy = user.productDeploy || "";
                }
                var route = localStorage.getItem("route");
                if (route != undefined && route != "") {
                    route = JSON.parse(route);
                    app.setRoute(route);
                    app.login();
                } else {
                    document.querySelector(".login_div").style.display = "block";
                    $.ajax({
                        url: "http://************:56789/ngp/auth/init",
                        type: "get",
                        success: function (data) {
                            setTimeout(function () {
                                app.progress = "width:70%"
                            }, 200);
                            if (data.code != "200") {
                                setTimeout(function () {
                                    app.progress = "width:100%";
                                    app.isDone = true;
                                }, 200);
                                $common.alert("初始化信息失败");
                            } else {
                                setTimeout(function () {
                                    app.progress = "width:100%";
                                    app.isDone = true;
                                }, 200);
                                app.deploys = data.data.deploys;
                                app.products = data.data.products;
                                console.log(app.productId);
                                app.productDeploys = data.data.deploys[app.productId];
                            }

                        },
                        error: function () {
                            setTimeout(function () {
                                app.progress = "width:100%";
                                app.isDone = true;
                            }, 200);
                            $common.alert("网络请求失败");
                        }

                    });
                }

            });
            setTimeout(function () {
                app.progress = "width:45%"
            }, 400);

        },
        methods: {
            setRoute: function (route) {
                route.deploy = this.deploy || (this.productDeploy || route.deploy);
                route.productId = (this.productId == "0") ? route.productId : this.productId;
                $ms.router = {//设置请求路由
                    debugMs: {
                        aloneDeploy: "${modName}",
                        profileId: route.profileId,
                        employeeId: route.employeeId,
                        serverId: route.serverId,
                        gateway: "http://gateway.ngp.wsgjp.com.cn",
                        aloneServer: "http://" + location.hostname + ":" + this.localPort,
                        deploy: route.deploy,
                        productId: route.productId,
                        adminStatus: route.adminStatus,
                        routes: route.routes
                    },
                    'ngp-authorization': 'jwt',
                    'ngp-router': 'ngprt'
                };
                if (this.routes.length > 0) {
                    var server = {};
                    for (var i = 0; i < this.routes.length; i++) {
                        var r = this.routes[i];
                        server[r.serverName] = r.serverIp + ":" + r.serverPort;
                    }
                    $ms.server = server;
                }
                var cookieStr = JSON.stringify(route);
                document.cookie += "; ngp-route=" + escape(cookieStr);
                localStorage.setItem("route", JSON.stringify(route));
            },
            jumpToLoad: function () {
                localStorage.setItem("route", "");
                window.location.href = "/${modName}/login.html";
                document.querySelector(".login_div").style.display = "block";
            },
            login: function () {
                document.querySelector(".login_div").style.display = "none";
                $removeNode($get('bootCss')); // 移除boot三方样式
                $common.loadScript('shell/js/init.js', function () {
                    $fc.loadMainPage('${modName}/Main.gspx', 'shell');
                });
            },
            doLogin: function () {
                var user = {
                    companyName: this.companyName,
                    employeeName: this.employeeName,
                    productId: this.productId,
                    deploy: this.deploy,
                    port: this.localPort,
                    productDeploy: this.productDeploy
                };
                localStorage.setItem("user", JSON.stringify(user));
                localStorage.setItem("routes", JSON.stringify(this.routes));
                $.ajax({
                    url: "http://************:56789/ngp/auth/login",
                    data: JSON.stringify({
                        companyName: user.companyName,
                        userName: user.employeeName
                    }),
                    headers: {
                        "Content-Type": "application/json"
                    },
                    type: "post",
                    success: function (data) {
                        if (data.code != "200") {
                            $common.alert("登录失败，请检查公司名称和职员名称是否正确");
                        } else {
                            app.setRoute(data.data);
                            app.login();
                            var link = document.getElementById('bootCss');
                            $removeNode(link);
                        }
                    },
                    error: function () {
                        $common.alert("网络请求失败");
                    }

                });
            },
            addRoute: function () {
                this.routes.push({});
            },
            deleteRoute: function (eventArgs) {
                var newRoutes = [];
                var deleteIndex = eventArgs.target.attributes["data-index"].value;
                for (var index = 0; index < this.routes.length; index++) {
                    if (deleteIndex != index) {
                        var route = this.routes[index];
                        newRoutes.push(route);
                    }
                }
                this.routes = newRoutes;
            },
            productChange: function () {
                this.productDeploy = "";
                this.productDeploys = this.deploys[this.productId];

            }
        }
    });
</script>
</body>
</html>