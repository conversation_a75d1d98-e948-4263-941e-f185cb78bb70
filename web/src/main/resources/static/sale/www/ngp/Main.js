Type.registerNamespace('${modName}');
var pin;
${modName}.MainAction = function () {
    ${modName}.MainAction.initializeBase(this);
};

${modName}.MainAction.prototype = {
    context: function (cb) {
        var action = this;
        this.get_service().post('shell/pm/getMenu', {all: true}, function (value) {
            if (value.code != 200) {
                $common.alert(value.message);
                cb({menus: []});
                return;
            }
            var temp = action.getMenuTemplate(true);
            var menus = value.data;
            if (menus) {
                menus = value.data.menus;
            }
            action.buildMenuFields(temp, menus, action.dealMenuEventDelegate(${modName}.MainAction));
            cb({menus: temp})
        });
    },
    dealMenuEventDelegate: function (action) {
        return function (node) {
            if (node.onClick) {
                if (action.prototype[node.onClick]) {
                    return;
                }
                action.prototype[node.onClick] = function (sender, args) {
                    $shell.menuMotionBus.publish(node.onClick, sender, args);
                }
            }
        }
    },
    getMenuTemplate: function (enableVisible) {
        return {
            "Text": "",
            "Icon": "",
            "Param": "",
            "OnClick": "",
            "ShowPage": "",
            "CssClass": "",
            "NavigateUrl": "",
            "BeginGroup": false,
            "Shortcut": "",
            "Enabled": enableVisible,
            "Visible": enableVisible,
            ChildNodes: []
        }
    },
    buildMenuFields: function (root, childNodes, backSet) {
        if (!childNodes) {
            return;
        }
        for (var i = 0; i < childNodes.length; i++) {
            var node = childNodes[i];
            backSet && backSet(node);
            var data = this.getMenuTemplate(false);
            for (var item in root) {
                var first = item.substr(0, 1).toLowerCase();
                var key = first + item.substr(1);
                if (key == 'childNodes') {
                    this.buildMenuFields(data, node[key], backSet);
                    continue;
                }
                data[item] = node[key]
            }
            if (!root.ChildNodes) {
                root.ChildNodes = [];
            }
            root.ChildNodes.push(data);
        }
    },

    initialize: function MainAction$initialize() {
        ${modName}.MainAction.callBaseMethod(this, 'initialize');
        pin = "dengcl";
        window.setTimeout(function () {
            try {
                getJdEid(function (eid, fp, udfp) {
                    $common.setCookie("deviceId", eid);
                });
            } catch (e) {
            }
        }, 1000);
    },

    dispose: function () {
        ${modName}.MainAction.callBaseMethod(this, 'dispose');
    },
    doTreeMenuNodeClick: function () {

    },

    Jump: function (sender) {
        alert("111");
        return;
    },

    buttonClick: function (sender, args) {
        this.get_service('recordsheet').post('/index/create', {dot: 'test'}, function (rt) {
            $debug.traceDump(rt);
        })
    },
    getDouble: function (sender, args) {
        this.get_service().get('/shell/double', function (value) {
            $debug.traceDump(value);
        })
    },
    getExpend: function (data) {
        if (!data) {
            return data;
        }
        try {
            return JSON.parse(data.replaceAll("\'", "\""));
        } catch (e) {
            return data;
        }
    },
    showPage: function (sender, args) {
        var data = sender.get_param();
        this.afterShowPage(sender, data);
    },
    afterShowPage: function (sender, data) {
        if (data) {
            $common.showPage(sender, data.path, this.getExpend(data.extend))
        }
    },
    showModal: function (sender, args) {
        var data = sender.get_param();

        this.afterShowModal(sender, data);
    },
    afterShowModal: function (sender, data) {
        if (data) {
            $common.showModalForm(sender, data.path, this.getExpend(data.extend))
        }
    },
    winOpen: function (sender, args) {
        var data = sender.get_param();
        $common.openwin(data.get_param().path);
    }
};
${modName}.MainAction.registerClass('${modName}.MainAction', Sys.UI.PageAction);
