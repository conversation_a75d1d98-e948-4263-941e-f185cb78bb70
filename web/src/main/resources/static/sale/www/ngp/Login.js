Type.registerNamespace('${modName}');
${modName}.LoginAction = function() {
    ${modName}.LoginAction.initializeBase(this);
    this.authHost = 'http://************:56789';
};

${modName}.LoginAction.prototype = {
    context: function(cb) {
        this.initData(cb);
    },

    initialize: function LoginAction$initialize() {
        ${modName}.LoginAction.callBaseMethod(this, 'initialize');
    },

    initData: function(cb) {
        var data = {
            companyName: "",
            employeeName: "",
            productId: "0",
            deploy: "",
            localPort: "",
            routes: [],
            products: [],
            deploys: {},
            productDeploys: [],
            productDeploy: ""
        };
        var formData = { formData: data }; // 窗体数据

        var routes = localStorage.getItem("routes");
        if (routes) {
            routes = JSON.parse(routes);
            data.routes = routes;
        }

        var user = localStorage.getItem("user");
        if (user) {
            user = JSON.parse(user);
            data.companyName = user.companyName;
            data.employeeName = user.employeeName;
            data.productId = user.productId;
            data.deploy = user.deploy;
            data.localPort = user.port || user.localPort || "";
            data.productDeploy = user.productDeploy || "";
        }

        var route = localStorage.getItem("route");
        if (route) {
            route = JSON.parse(route);
            this.setRoute(route, data);
            this.login();
            this.end(); // 不继续处理Login.gspx
            return;
        }

        var that = this;
        $common.ajax({
            url: this.authHost + '/ngp/auth/init',
            type: 'GET',
            success: function(res) {
                if (res.code != "200") {
                    $common.alertError("初始化信息失败");
                    that.end();
                    return;
                }

                data.deploys = res.data.deploys;
                data.products = res.data.products;
                console.log(data.productId);
                data.productDeploys = res.data.deploys[data.productId];
                cb(formData);
            },
            error: function() {
                $common.alertError("网络请求失败");
                that.end();
            }
        });
    },

    dispose: function() {
        ${modName}.LoginAction.callBaseMethod(this, 'dispose');
    },

    setRoute: function(route, data) {
        route.deploy = data.deploy || (data.productDeploy || route.deploy);
        route.productId = (data.productId == "0") ? route.productId : data.productId;
        $ms.router = {//设置请求路由
            debugMs: {
                aloneDeploy: "${modName}",
                profileId: route.profileId,
                employeeId: route.employeeId,
                serverId: route.serverId,
                gateway: "http://gateway.ngp.wsgjp.com.cn",
                aloneServer: "http://" + location.hostname + ":" + data.localPort,
                deploy: route.deploy,
                productId: route.productId,
                adminStatus: route.adminStatus,
                routes: route.routes
            },
            'ngp-authorization': 'jwt',
            'ngp-router': 'ngprt'
        };
        if (data.routes.length > 0) {
            var server = {};
            for (var i = 0; i < data.routes.length; i++) {
                var r = data.routes[i];
                server[r.serverName] = r.serverIp + ":" + r.serverPort;
            }
            $ms.server = server;
        }
        var cookieStr = JSON.stringify(route);
        $common.setCookieNoEscape("ngp-route", cookieStr);
        localStorage.setItem("route", JSON.stringify(route));
    },
    login: function() {
        $common.setCookieNoEscape('ngp-authorization', 'jwt');
        $common.setCookieNoEscape('ngp-router', 'ngprt');
        $common.setCookieNoEscape('debugMs', JSON.stringify($ms.router.debugMs));

        location.reload();
    },
    doLogin: function(sender) {
        var user = this.get_form().saveData();
        var data = {
            companyName: user.companyName,
            userName: user.employeeName
        };
        localStorage.setItem("user", JSON.stringify(user));
        localStorage.setItem("routes", JSON.stringify(user.routes));

        var that = this;
        $common.ajax({
            url: this.authHost + "/ngp/auth/login",
            data: data,
            success: function(res) {
                if (res.code != "200") {
                    $common.alertError("登录失败，请检查公司名称和职员名称是否正确");
                } else {
                    that.setRoute(res.data, user);
                    that.login();
                }
            },
            error: function() {
                $common.alertError("网络请求失败");
            }

        });
    },
    addRoute: function(sender) {
        this.get_form().grid.appendRowData({});
    }
};
${modName}.LoginAction.registerClass('${modName}.LoginAction', Sys.UI.PageAction);