<!DOCTYPE html>
<html lang="en">

<head>
    <title>...</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico" />

    <link type="text/css" rel="stylesheet" href="skins\craba.min.css" />
    <link type="text/css" rel="stylesheet" href="skins\blueSky\skin.css" />
    <style type="text/css">
        .login_div {
            text-align: center;
        }

        .login_div_item {
            margin: 10px;
        }

        .reload {
            /*display: none;*/
            border: 0px;
            position: absolute;
            right: 10px;
            top: 10px;
            width: 100px;
            height: 50px;
            z-index: 999;
        }

        .reload_button {
            font-weight: bold;
            font-size: 16px;
            background-color: #879ced;
        }
    </style>
</head>

<body class="blueSky">
    <div id='LoadingCarpa' class='LoadingCarpa'>
        <div class='ProgressBar BlackBg ValueFloat radius14' id='progress' style='width: 300px; height: 20px; '>
            <div class='ProgressValue transition2' id='LoadingValue' style='width: 0; '></div>
            <span class='ProgressHighText' style='line-height: 12px; left: 1%; ' id='LoadingText'>正在请求服务中心</span>
        </div>
    </div>
    <div id="reload" class="reload">
        <input type="button" class="reload_button" value="跳转到登录" onClick="jumpToLoad();" />
    </div>
    <div id="login" class="login_div">
        <div class="login_div_item">
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;调试模块：</span>
            <input type='text' id="aloneDeploy" value='eshoporder' />
        </div>
        <div class="login_div_item">
            <span>后端API地址：</span>
            <input type='text' id="aloneServer" value='http://localhost:10002' />
        </div>
        <div class="login_div_item">
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;serverId：</span>
            <input type='text' id="serverId" value='10008' />
        </div>
        <div class="login_div_item">
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;gateway：</span>
            <input type='text' id="gateway" value='http://************:8081' />
        </div>
        <div class="login_div_item">
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;profileId：</span>
            <input type='text' id="profileId" value='111' />
        </div>
        <div class="login_div_item">
            <span>&nbsp;employeeId：</span>
            <input type='text' id="employeeId" value='2332' />
        </div>
        <div class="login_div_item">
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;deploy：</span>
            <input type='text' id="deploy" value='main' />
        </div>
        <div class="login_div_item">
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;productId：</span>
            <input type='text' id="productId" value='88' />
        </div>
        <div class="login_div_item">
            <input type='checkbox' id="remmber">记住设置</input>
            <input type='button' onClick="load();" value="点击登录" />
        </div>
    </div>

    <script type="text/javascript" src="js/agency.js?v=3"></script>
    <script>
        function startPage() { // 供agency内部调用

            var profileId = $common.getCookie('profileId');
            if (profileId) {
                document.getElementById('profileId').value = profileId;
            }
            var employeeId = $common.getCookie('employeeId');
            if (employeeId) {
                document.getElementById('employeeId').value = employeeId;
            }
            var aloneDeploy = $common.getCookie('aloneDeploy');
            if (aloneDeploy) {
                document.getElementById('aloneDeploy').value = aloneDeploy;
            }
            var serverId = $common.getCookie('serverId');
            if (serverId) {
                document.getElementById('serverId').value = serverId;
            }
            var gateway = $common.getCookie('gateway');
            if (gateway) {
                document.getElementById('gateway').value = gateway;
            }
            var aloneServer = $common.getCookie('aloneServer');
            if (aloneServer) {
                document.getElementById('aloneServer').value = aloneServer;
            }
            var deploy = $common.getCookie('deploy');
            if (deploy) {
                document.getElementById('deploy').value = deploy;
            }
            if ($common.getCookie('loaded')) {
                load();
            }
        }

        var agencyConfig = {}; // 参考文档末尾的对象说明
        
        $agency.main(['js/craba.min.js?v=3',
            'js/crabaEx.min.js?v=3',
            'js/math.min.js?v=3',
            'js/crabaNgp.js?v=3'], agencyConfig, startPage); // 这里写死首页脚本,agency内部使用
    </script>
    <script>
        function jumpToLoad() {
            $common.setCookie('loaded', '');
            window.location.href = window.location.origin;
        }

        function load() {
            var profileId = document.getElementById('profileId').value;
            var employeeId = document.getElementById('employeeId').value;
            var aloneDeploy = document.getElementById('aloneDeploy').value;
            var serverId = document.getElementById('serverId').value;
            var gateway = document.getElementById('gateway').value;
            var aloneServer = document.getElementById('aloneServer').value;
            var deploy = document.getElementById('deploy').value;
            var productId = document.getElementById('productId').value;
            var remmber = document.getElementById('remmber').checked;
            if (remmber) {
                $common.setCookie('profileId', profileId);
                $common.setCookie('employeeId', employeeId);
                $common.setCookie('aloneDeploy', aloneDeploy);
                $common.setCookie('serverId', serverId);
                $common.setCookie('gateway', gateway);
                $common.setCookie('aloneServer', aloneServer);
                $common.setCookie('deploy', deploy);
                $common.setCookie('productId', productId);
            }
            $ms.router = {//设置请求路由
                debugMs: {
                    aloneDeploy: aloneDeploy,
                    profileId: profileId,
                    employeeId: employeeId,
                    serverId: serverId,
                    gateway: gateway,
                    aloneServer: aloneServer,
                    deploy: deploy,
                    productId: productId
                },
                authorization: 'jwt',
                ngprouter: 'ngprt'
            }
            document.getElementById('login').style.display = 'none';
            document.getElementById('reload').style.display = '';
            $common.setCookie('loaded', 1);
            $fc.loadMainPage(aloneDeploy + '/Main.gspx');
        }
    </script>
</body>

</html>