// 业务控件也有基类的情况，先实现基类
Type.registerNamespace('erp');
erp.treeBase = function(control) {
    erp.treeBase.initializeBase(this, [control]);
}
erp.treeBase.prototype = {
    doEditorShow: function(sender, args) {
        if (this._isShow) return;
        if (args && args.cancel) args.cancel(); // 少了這句

        this._createPopup();
        this.get_form().popupSender = sender; // 同一个界面不同控件绑定的同一个doSelected
        this._bindValue(sender);
        this._getPopup().popupAt(this._getPopupAt());
        var elt = this._getPopup().get_element();
        elt.style.cssText = 'bottom:0;height:50%;width:100%;display:flex;border:none;padding:0;flex-direction:column;';

        this._clearSearch();
        this._isShow = true;
    },
    doEditorHide: function() {
        var popup = this._getPopup();
        if (!popup) return;
        popup.hide();
    },
    _getTree: function() {
        return this.get_form()[this.treeId];
    },
    doSelected: function(sender) {
        if (this.showCheckBox) return; // 多选模式下，点确定才是选择

        var tree = this._getTree();
        var node = tree.get_selectedNode();
        if (node && node.hasChildren()) {
            return;// this._getPopup().repos(); // 面板内部发生改变，重新计算位置
        }

        var nodes = tree.get_selectedNodes();
        var textArr = [];
        Array.forEach(nodes, function(node) {
            textArr.push(node.get_text()); // 遍历选择节点，获取节点数据或文本。或其它操作；
        });
        var newText = textArr.join(this.splitChar);
        this._setTextAndChange(newText);
        this.hide(); // 必须最后关闭面板，不然OnHide里面关闭编辑器了无法保存
    },
    doSelectedOk: function(sender) {
        var tree = this._getTree();
        var node = tree.get_rootNode();
        var checkdNodes = [];
        this._getCheckedNodes(node, checkdNodes);

        var texts = [];
        var splitChar = this.splitChar;
        checkdNodes.forEach(function(child) {
            var text = [];
            while (child.get_level() > -1) {
                text.push(child.get_text());
                child = child.get_parent();
            }
            text.reverse();
            texts.push(text.join(splitChar));
        });
        this.hide();

        var newText = texts.join(this.multiChar);
        this._setTextAndChange(newText);
    },
    _setTextAndChange: function(newText) {
        var control = this.get_form().popupSender; // 这里一定不能用this.control, 因为popup是公用的，它关联的业务控件和control可能已经不存在了
        this.set_text(newText, control);

        if (control.doChange) {
            if (this._oldText == newText) return;
            this._oldText = newText;
            control.doChange();
        }
    },

    /* 仅获取勾选的节点，且如果上级勾选了就不获取下级，上级如果是半选情况不能获取 */
    _getCheckedNodes: function(node, checkdNodes) {
        for (var i = 0; i < node.get_count(); i++) {
            var child = node.getChild(i);
            if (!child.get_partialChecked() && child.get_checked()) {
                checkdNodes.push(child);
            } else {
                this._getCheckedNodes(child, checkdNodes);
            }
        }
    },
    _bindValue: function(sender) {
        var text = '';
        if (sender.get_grid) {
            var grid = sender.get_grid();
            var rowData = grid.get_selectedRowData();
            if (rowData && this.control.get_dataField()) {
                text = rowData[this.control.get_dataField()] || '';
            }
            sender.set_hasPopup(true);
        } else {
            text = sender.get_text();
        }

        if (this.showCheckBox) {
            this._checkedAddress(text); // 勾选情况
        } else {
            this._selectAddress(text); // 选中情况
        }
    },
    _checkedAddress: function(text) {
        var tree = this._getTree();
        tree.get_rootNode().uncheck(); // 每次需要重新赋值，全部取消勾选
        if (!text) return;

        var that = this;
        var areas = text.split(this.multiChar);
        areas.forEach(function(area) {
            that._selectAddress(area, true);
        });
    },
    _selectAddress: function(text, check) {
        if (text && text[text.length - 1] == this.splitChar) {
            text = text.substr(0, text.length - 1);
        }

        var form = this.get_form();
        var tree = form[this.treeId];

        var arr = [''];
        if (text) {
            arr = text.split(this.splitChar);
        } else if (this.addressText) {
            arr = this.addressText.split(this.splitChar);
        }

        var lastText = arr[arr.length - 1];
        if (!lastText) {
            return tree.clearSelect(true);
        }

        var lastLevel = arr.length - (tree.get_showRootNode() ? 0 : 1);
        //tree.locateNode('name', lastText); // 选中最后一个节点，自动级联选择上级(四川省/成都市/高新区，陕西省/长治市/高新区....太多高新区了)
        var checkOrSelect = check ? 'check' : null;
        for (var i = arr.length - 1; i >= 0; i--) {
            lastText = arr[i];
            var node = tree.locateNode(function(node) {
                var txt = node.get_text();
                if (txt != lastText) return false;
                if (node.get_level() != lastLevel) return false;
                var pNode = node.get_parent();
                if (pNode == tree.get_rootNode()) return true;
                return pNode.get_text() == arr[lastLevel - 1]; // 至少要比较两层节点相等
            }, checkOrSelect);

            if (node) {
                return !node.hasChildren();
            }
        }
    },
    _getPopup: function() {
        var form = this.get_form();
        var popup = form[this.popupId];
        return popup;
    },
    _clearSearch: function() {
        var form = this.get_form();
        var edit = form[this.searchId];
        edit.set_text('');
        edit.asyncFocus();
        this._searchText = '';
    },
    _getPopupAt: function() {
        var target = this.control;
        if (target.get_grid) {
            target = target.get_grid().get_activeCell();
        }
        return target;
    },
    _popupHide: function() {
        this._isShow = false;
        if (!this.control.get_grid) return;
        var grid = this.control.get_grid();
        grid.endEdit(); // 面板关闭的时候，关闭表格的编辑框
    },
    _createPopup: function() {
        var form = this.get_form();
        if (form[this.popupId]) return;

        var pb = $createControl('PopupBlock', {
            ID: this.popupId,
            CssClass: 'AreaPopup',
            OnHide: $createDelegate(this, this._popupHide)
        }, form);

        $createControl('DropDownEdit', {
            ID: this.searchId,
            NullDisplayText: '可输入关键词查询或定位，如:成都市',
            DataTextField: 'text',
            DataValueField: 'value',
            DropDownStyle: 'DropDownEdit',
            CssClass: 'FlexAuto',
            AllowHtml: true,
            OuterCssClass: 'FlexShrink0 HideButton',
            OnFocus: $createDelegate(this, this.doShowSearch),
            OnDelayChanged: $createDelegate(this, this.doSearch),
            OnChange: $createDelegate(this, this.doSearchSelected)
        }, form, pb);

        $createControl('TreeView', {
            ID: this.treeId,
            ShowLevel: this.showLevel,/*配置只显示3级数据，如果绑定了4级或5级超出部分不显示*/
            OnTreeNodeClick: $createDelegate(this, this.doSelected),
            ShowColumns: this.showColumns,
            ShowCheckBoxes: this.showCheckBox,
            DataSource: $common.toTreeData($areaList, 'name', 'id', 'parent_id', this.rootId)
        }, form, pb);

        if (this.showCheckBox) {
            var hb = $createControl('HBlock', {
                CssClass: 'dflex BottomBlock'
            }, form, pb);
            $createControl('Block', {
                CssClass: 'Flex1'
            }, form, hb);
            $createControl('Button', {
                Text: '全选',
                CssClass: 'SpecialButton',
                Tag: true,
                OnClick: $createDelegate(this, this.doSelecteAll)
            }, form, hb);
            $createControl('Button', {
                Text: '取消全选',
                CssClass: 'SpecialButton',
                Tag: false,
                OnClick: $createDelegate(this, this.doSelecteAll)
            }, form, hb);
            $createControl('Button', {
                Text: '确定',
                CssClass: 'SpecialButton',
                OnClick: $createDelegate(this, this.doSelectedOk)
            }, form, hb);
            $createControl('Button', {
                Text: '取消',
                OnClick: $createDelegate(this, this.hide)
            }, form, hb);
        }
        pb.appendTo(document.body);
        //this._getTree().set_dataSource($common.toTreeData($areaList, 'name', 'id', 'parent_id', this.rootId));
    },
    _bindData: function(province, city, district, street, noBindText) {
        if (!province) return;
        this.Province = province;
        this.City = city;
        this.District = district;

        if (this.showLevel == 3) noBindText = street; // 兼容之前的3层数据参数
        else this.Street = street;

        if (!noBindText) {
            this._bindText(this.get_form());
        }
    },
    _bindText: function(form) {
        this.hide();
        var address = this._makeAddress(this.Province, this.City, this.District, this.Street);
        this.set_text(address);
    },
    set_text: function(address, control) {
        var c = control || this.control;
        if (c.get_grid) {
            var grid = c.get_grid();
            var rowData = grid.get_selectedRowData();
            if (c.get_dataField()) {
                rowData = rowData ? rowData : {};
                if (rowData[c.get_dataField()] != address) {
                    c._changed = true;
                    c._control.set_value(address);
                    c._hideEditor();
                }
            }
        } else {
            c.set_text(address);
        }
    },
    hide: function() {
        this._isShow = false;
        var form = this.get_form();
        if (form[this.popupId]) {
            form[this.popupId].hide();
        }
    },
    clear: function() {
        this.addressText = '';
    },
    doSelecteAll: function(sender) {
        var all = sender.get_tag();
        var tree = this._getTree();
        if (all) {
            tree.checkAll();
        } else {
            tree.uncheckAll();
        }
    },
    doShowSearch: function(sender) {
        if (!sender.get_text()) return;
        sender.dropDown();
    },
    doSearch: function(sender) {
        var text = sender.get_text();
        if (text === this._searchText) return;
        this._searchText = text;
        if (!text) {
            sender.set_items([], true);
            return;
        }

        var tree = this._getTree();
        var newData = [];
        var rootNode = tree.get_rootNode();
        var that = this;
        var searchFun = function(node, txt, list) {
            node.get_children().forEach(function(child, i) {
                var rowData = child.get_data();
                if (rowData.name.indexOf(txt) > -1) {
                    var level = child.get_level();
                    if (level == 0) {
                        list.push({
                            value: rowData.name,
                            text: that._replaceText(rowData.name, txt)
                        });
                    } else if (level == 1) {
                        var nodeText = that._makeAddress(child.get_parent().get_data().name, rowData.name);
                        list.push({
                            value: nodeText,
                            text: that._replaceText(nodeText, txt)
                        });
                    } else if (level == 2) {
                        var nodeText = that._makeAddress(child.get_parent().get_parent().get_data().name, child.get_parent().get_data().name, rowData.name);
                        list.push({
                            value: nodeText,
                            text: that._replaceText(nodeText, txt)
                        });
                    } else if (level == 3) {
                        var nodeText = that._makeAddress(child.get_parent().get_parent().get_parent().get_data().name, child.get_parent().get_parent().get_data().name, child.get_parent().get_data().name, rowData.name);
                        list.push({
                            value: nodeText,
                            text: that._replaceText(nodeText, txt)
                        });
                    }
                }
                searchFun(child, txt, list);
            });
        }
        searchFun(rootNode, text, newData);

        sender.set_items(newData, true);
        sender.dropDown();
    },
    doSearchSelected: function(sender) {
        var index = sender.get_selectedIndex();
        if (Object.isUndefinedOrNull(index) || index < 0) return;

        var address = sender.get_value();
        var last = this._selectAddress(address);
        sender.set_text(this._searchText);

        // 仅选择到最后一层地址才关闭面板
        if (last) {
            this.doSelected();
        } else {
            var tree = this._getTree();
            var node = tree.get_selectedNode();
            if (node) {
                node.expand();
            }
        }
    },
    _replaceText: function(source, c) {
        return source.replaceAll(c, '<font color="#ff0000">' + c + '</font>')
    },
    addressChange: function(selectedData) {
        var text = this._makeAddress(selectedData.province, selectedData.city, selectedData.district, selectedData.street);
        this.control.set_text(text);
    },
    _makeAddress: function(province, city, district, street) {
        var arr = [];
        if (province) arr.push(province);
        if (city) arr.push(city);
        if (district) arr.push(district);
        if (street) arr.push(street);
        return arr.join(this.splitChar);
    },
    get_value: function() {
        var text = this.control.get_value();
        var arr = text.split(this.splitChar);
        var rt = {
            Province: '',
            City: '',
            District: '',
            Street: ''
        }
        switch (arr.length) {
            case 1:
                rt.Province = arr[0];
                break;
            case 2:
                rt.Province = arr[0];
                rt.City = arr[1];
                break;
            case 3:
                rt.Province = arr[0];
                rt.City = arr[1];
                rt.District = arr[2];
                break;
            case 4:
                rt.Province = arr[0];
                rt.City = arr[1];
                rt.District = arr[2];
                rt.Street = arr[3];
                break;
        }
        return rt;
    }
}
erp.treeBase.registerClass('erp.treeBase', Sys.BusinessControl); // 继承平台的基类


// 注册并实现业务控件
$app.registerBusiness('erp.popupArea, erp.treeBase', {
    controlInfo: function() {
        this.showColumns = true; // 基类要用
        this.showLevel = 3; /*配置只显示3级数据(默认显示3层），如果绑定了4级或5级超出部分不显示； 需要显示第4层截到那就配置4*/
        this.showCheckBox = false;
        this.splitChar = '/'; // 省市区分隔符
        this.multiChar = ','; // 多选的分隔符

        var idpix = '';
        var data = this.get_businessData();
        if (data) {
            if (data.level) {
                this.showLevel = data.level;
                idpix += '_1';
            }
            if (data.showCheckBox) {
                this.showCheckBox = true;
                idpix += '_2';
            }
            if (data.splitChar) {
                this.splitChar = data.splitChar;
                idpix += '_3';
            }
            if (data.multiChar) {
                this.multiChar = data.multiChar;
                idpix += '_4';
            }
        }

        // 同一个界面不同层级，使用不同id和面板，因为面板只创建一次
        this.popupId = '__erppopupArea' + idpix;
        this.searchId = '__searchEdit' + idpix;
        this.treeId = '__areaTree' + idpix;
        this.rootId = -1;

        $common.loadScript('sale/js/area.js');

        return {
            property: {
                readOnly: true,
                allowDelIcon: true /*强制只读的编辑框也显示清空按钮，但不可输入*/
            },
            events: {
                "OnChange": this.doEditorChange,
                "OnFocus": this.doEditorShow,
                "OnClick": this.doEditorShow // ie下面只读的编辑框没有focus，所以还需要增加click事件
            }
        }
    },

    doEditorChange: function(sender) {
        //$debug.traceDump('erp.popupArea.doEditorChange');
    }
}); // 继承基类，逗号分隔
