$settings.GspxResolveCache=!0,$settings.AjaxInterceptOption={isOpenToFusing:!1,urlWhiteList:[],intervalTime:5e3,count:2,errorStatusCode:[],maxErrorCount:3,message:"服务器异常，请刷新后再试"},window.$initMs=function(e){var j,x,r;$settings.UseBigNumber=!0,$settings.UseServiceExport=!0,$ms=((j=function(e){e&&!e.endsWith("/")&&(e+="/"),this._url=e,this._requestInterceptor=null,this._responseInterceptor=null,this._enableShowWaiting=!0,this._isCallBackOnDisposed=$settings.IsCallbackOnDispose,this._reponseGlobalErrorInterceptor=null,$common._waitCounter===undefined&&($common._waitCounter=0)}).prototype={getUrl:function(){return this._url||""},setUrl:function(e){this._url=e},setToken:function(e){localStorage.setItem("Authorization",e)},getToken:function(){return localStorage.getItem("Authorization")},setResponseInterceptor:function(e){this._responseInterceptor=e},set_isCallBackOnDisposed:function(e){this._isCallBackOnDisposed=e},getResponseInterceptor:function(e,t,r){return this._responseInterceptor},setReponseGlobalErrorInterceptor:function(e){this._reponseGlobalErrorInterceptor=e},getReponseGlobalErrorInterceptor:function(){return this._reponseGlobalErrorInterceptor},set_owner:function(e){this._owner=e},get_enableShowWaiting:function(){return this._enableShowWaiting},set_enableShowWaiting:function(e){this._enableShowWaiting=e},showWaiting:function(e,t){this._enableShowWaiting=!0,this._waitingText=t,this.set_owner(e),$common._waitCounter++},_showWaiting:function(){var e=this._owner;if(!($common._waitCounter<=0)&&this._waitingText){var t=null;if(e==window)t=window;else if(void 0!==e)if(Sys.UI.CustomForm.isInstanceOfType(e)&&e.get_isDisposed())return;$common.showWaiting(t,this._waitingText)}},hideWaiting:function(e){e&&clearTimeout(e),this.get_enableShowWaiting()&&0<$common._waitCounter&&($common._waitCounter--,$common._waitCounter<1)&&$common.hideWaiting(!0),this._setOwnerEnabled(!0)},_setOwnerEnabled:function(e){var t=this._owner;t&&t.set_enabled&&t.get_isDisposed&&!t.get_isDisposed()&&(t.set_enabled(e),e)&&(this._owner=null)},_startWaitingTimer:function(){var e;return this._enableShowWaiting&&!$common.isWaiting()?(e=this,setTimeout(function(){e._showWaiting()},$settings.WaitingTimerValue)):null},ajax:function(e){return this._convert(e),$common.ajax(e)},ajaxSync:function(e){return this._convert(e),$common.ajaxSync(e)},_convert:function(e){var t=e.url;$common.checkAlert(!t,"get_servcie().ajax必须传url参数"),e.router="ngp",e.waiting=e.waiting||this._waitingText,e.owner=e.owner||this._owner,e.form=e.form||this.action?this.action.get_form(!0):this._owner?this._owner.get_form(!0):null,e.dispose=e.dispose||this._isCallBackOnDisposed},get:function(e,t,r,n){if("function"==typeof t)r=t;else{e.indexOf("?")<0&&(e+="?");var o,i="";for(o in t)i+="&"+o+"="+t[o];e+=0<i.length?i.substr(1):""}e={url:e,type:"GET",headers:t?t.headers:null,"async":!0,success:r,error:n};return this.ajax(e)},post:function(e,t,r,n){t&&"function"==typeof t&&(r=t,t={});e={url:e,data:t,type:"POST",headers:t?t.headers:null,"async":!0,success:r,error:n};return this.ajax(e)},customHeaders:function(){return{"content-type":"application/json"}},bindHeaders:function(e){e=e||{};for(var t=Object.keys(e),r=0;r<t.length;r++)if("content-type"==t[r].toLowerCase())return e;return e["Content-Type"]="application/json",e},apiHeaders:function(){return this.bindHeaders(this.customHeaders())}},x={_specialKeys:["enabled","visible","render","rightIconVisible","activeRender"],append:function(e,r){if(e&&r){if("function"!=typeof e)throw Error.invalidOperation("error： 被附加对象cl，请传入函数对象"+e);for(var n in e.prototype){if("function"==typeof t&&r[n])throw Error.invalidOperation("error： 不能追加已有的属性 "+n);r[n]=e.prototype[n]}e.prototype=r}},getServerName:function(e){return e},getServerNameUrl:function(e,t,r){return t?t.startsWith("http")?t+e:($common.getPathParam(e,$settings.StaticVersionName)||t==$settings.cwModName||(e=$common.addUrlVersion(e,t)),$craba.joinPath(x.getServerName(t,r),e)):"~/"+e},injectBusinessControl:function(e,t){},getAPIServiceUrl:function(e){return e},getRealUrlAndServerName:function(e,t){var r,n=(e=(e=(e=x.interceptServerName?x.interceptServerName(e):e).replace(/\\/g,"/")).startsWith("/")?e.substring(1):e).indexOf("/");return n<0?{url:e,serverName:null}:(r=e.substring(0,n),n={url:e=e.substring(n),serverName:r},$fc.inited&&!t&&x.loadServerInit({serverName:n.serverName,url:e}),n)},loadServerInit:function(e){if(!$ms.router)return!0;var t=e.serverName;if(t.startsWith("http"))return!0;if(t===$settings.cwModName&&(t=$settings.cwjavaName||"cwjava"),x.initMap||(x.initMap={}),x.initMap[t])return!0;var r,n,o="/js/init.js";if(1==$common.getUrlParam("debugJs2")&&(o="/js/init_source.js"),e.url&&"shell"!=t){if(e.url.indexOf(".gspx")<0)return!1;if(e.url==o)return!1}return x.initMap[t]=!0,$ms.noInitServerNames&&Array.contains($ms.noInitServerNames,t)||(r="blueSky","shell"==t&&(r=$ms.router["ngp-skinName"]||"blueSky"),$skin.loadCss(x.getServerNameUrl("/skins/"+r+"/skin.css",t),t,function(){o=x.getServerNameUrl(o,t),$common.loadScript(o,function(){e.cb&&e.cb(e.businessName,e.businessData),$notify.emit("serverInited",e)},!1,!0)},function(e,t){t=new _Z_215(!1,t.get_json());n=t.get_message()+"\r\n"+e},!0),n&&(x.initMap[t]=!1,console.error(n))),!1},getBusinessInfo:function(e,t){var r=e,n={};if("object"==typeof e&&(r=e.Name,n=e),r.startsWith("{")){if(e=e.replace(new RegExp("'","gm"),'"'),!(n=JSON.parse(e)).Name)throw new Error("采用json格式传递业务控件，请设置业务控件的Name");r=n.Name}var e=r.indexOf("."),o="";return(o=0<=e?r.substr(0,e):o)?x.loadServerInit({serverName:o,cb:t,businessName:r,businessData:n})&&t&&t(r,n):t&&t(r,n),{business:r,serverName:o,businessData:n}},loadVersions:function(n,e){var t=$craba.getLangPro("请稍等，正在拉取模块信息..");$common.ajax({url:"shell/version/get",waiting:'<div style="color:#2288fc;text-align:center;position:fixed;left:50%;top:46%;transform: translateX(-50%);">'+t+"</div>",router:"ngp",timeout:10,"async":!e,success:function(e){var t,r;200==e.code&&e.data?(r=(t=e.data).versions||[],$ms._modList||($ms._modList=[]),Array.forEach(r,function(e){var t=e.appId;t&&((e.loadInitJs||Object.isUndefinedOrNull(e.loadInitJs))&&$ms._modList.push(t),e.time)&&(e=Date.strToDateTime(e.time).getTime(),$craba.addVersion({name:t,version:e}))}),n&&n(t)):(console.error("初始化失败:"+e.message),n&&n(e.data))},error:function(e){$ms._modList=[],console.error("拉取各模块的版本信息失败"),n&&n()}})},getLocalAndRemotePrinters:function(e){var t=this.getLocalPrinters(),e=this.getRemotePrinters(e),r=[];return Array.addRange(r,t),Array.addRange(r,e),r},getLocalPrinters:function(){var e,t,r=[];return $print.checkRun()&&(e=$print.getLocalPrinters(),t=this,e.forEach(function(e){e=t._buildPrinterNameText(e.clientId,e.printerName,!1,!0,!0);e&&r.push(e)})),r},getRemotePrinters:function(e){var t=[],e=$common.ajaxSync({url:"eprint/config/printer",data:e,type:"GET",router:"ngp"});if(e&&e.code&&200==e.code){var r=e.data;if(r&&0!=r.length)for(var n=0;n<r.length;n++){var o=r[n],i=o.clientId,s=o.printerName,a=o.printerAlias,u=o.useful,o=o.verdorType,i=this._buildPrinterNameText(i,s,a,!1,u,o);i&&t.push(i)}}return t},_buildPrinterNameText:function(e,t,r,n,o,i){var s;return Object.isUndefinedOrNull(e)||!t?null:(s={},n?s.clientId=t:(e=e+"|"+t,Object.isUndefinedOrNull(i)||(e+="|"+i),s.clientId=e),s.printerName=(r||t)+(n?"(本地)":"(远程)")+(o?"(在线)":"(离线)"),s.enable=!!o,s)}},function(){var i=_Z_275.prototype.checkConfig,t=(_Z_275.prototype.checkConfig=function(e,t,r,n){if(n){var o=n.getBaseForm().get_action(!0);if(o&&Object.getTypeName(o).startsWith($settings.cwDefaultName))return!1}return i.apply(this,arguments)},Sys.UI.PageAction.prototype.get_service),u=(Sys.UI.PageAction.prototype.get_service=function(){var e=Object.getTypeName(this);return e.startsWith($settings.cwDefaultName)?t.apply(this,arguments):(e=x.getServerName(e),(e=x.getAPIServiceUrl?x.getAPIServiceUrl(e):e)&&!e.endsWith("/")&&(e+="/"),(e=new j(e)).action=this,e)},Craba.UI.Page.prototype.set_actionType),r=(Craba.UI.Page.prototype.set_actionType=function(e){if(!e)return u.apply(this,[e]);if((e=e.trim()).startsWith($settings.cwDefaultName))return u.apply(this,arguments);var t,r=e.split(","),n=[],o=(n.push(r[0]),this.Context.scriptPath);(o=o||"").toLowerCase().startsWith("http")||(t=x.getRealUrlAndServerName(o));for(var i=1;i<r.length;i++){var s=r[i].trim(),a=x.getRealUrlAndServerName(s);!t||t.serverName==a.serverName||$craba.hasMod(a.serverName)?(s=x.getServerNameUrl(a.url,a.serverName),n.push(s)):x.getServerNameUrl(s,t.serverName)}e=n.join(","),u.apply(this,[e])},Craba.UI.Page.prototype.initActionType),n=(Craba.UI.Page.prototype.initActionType=function(){var e,t;this._actionType&&this._actionType.startsWith($settings.cwDefaultName)&&(e=$settings.cwModName,this.host&&(e=this.host=$craba.joinPath(this.host,e)),t=this.Context.scriptIncludePath.trim(),this.Context.scriptIncludePath=$craba.joinPath(e,t),this.Context.webMethodBasePath)&&(this.Context.webMethodBasePath=$craba.joinPath(e,this.Context.webMethodBasePath)),r.apply(this,arguments)},Sys.UI.CustomForm.prototype.convertUrl),o=(Sys.UI.CustomForm.prototype.convertUrl=function(e){return e&&!e.trimStartChar("/").startsWith($settings.cwModName)&&(e=n.apply(this,arguments),e=$common.convertCWURL(this,e)),e},Sys.UI.CustomForm.prototype.doInit),s=(Sys.UI.CustomForm.prototype.doInit=function(e){e&&!e._init&&(e._init=!0,e._path=$common.convertCWURL(this,e._path),e._path.startsWith($settings.cwModName))?(e._enableUnsafeHeader=!0,this.modName=$settings.cwModName):this.modName="",o.apply(this,arguments)},_Z_58.prototype.addUrlVersion),a=(_Z_58.prototype.addUrlVersion=function(e,t){if($settings.cwModName){if(t==$settings.cwModName)return e;if(!e)return e;if(1<e.indexOf("/"+$settings.cwModName+"/"))return e}return $common.hasParam(e,$settings.StaticVersionName)?e:(t=t||x.getRealUrlAndServerName(e,!0).serverName,s.apply(this,[e,t]))},_Z_58.prototype.loadScript),c=(_Z_58.prototype.loadScript=function(){var r,n,e=arguments[0];return"object"==typeof e&&e.form&&(r=e.form,(n="string"==typeof(n=e.srcs)?n.split(","):n).forEach(function(e,t){e=$common.convertCWURL(r,e),n[t]=e}),e.srcs=n),a.apply(this,arguments)},$common.getAjaxInterceptData=function(e){if(!e)throw Error.abort("options不能为空，可以参考ajax的参数");if(!e.url)throw Error.abort("url不能为空");"function"!=typeof e.success&&(e.success=function(e){e=e.data||e;$settings.AjaxInterceptOption=Object.copyTo($settings.AjaxInterceptOption,e)}),$common.ajax(e)},$common.convertCWURL=function(e,t){var r;return t=e&&t&&!t.startsWith("http")&&((r=(r=e.get_action(!0))||e.getBaseForm().get_action(!0))&&Object.getTypeName(r).startsWith($settings.cwDefaultName)||!r&&e.get_url()&&e.get_url().startsWith("/cw/"))&&!(t=t.trimStartChar("~").trimStartChar("/")).startsWith($settings.cwModName)?$craba.joinPath($settings.cwModName,t):t},Craba.UI.ReportAction.prototype.typeName=function(){var e=this.get_rootPage();return(!e.actionType||!e.actionType.startsWith($settings.cwDefaultName))&&this._useWebTool?"ReportPrintWeb":this._useService?"ReportPrintService":"ReportPrintClound"},Craba.UI.Grid.prototype.get_autoPagerPageSize=function(){var e=this.get_rootPage();return(!e.actionType||!e.actionType.startsWith($settings.cwDefaultName))&&this._autoPagerPageSize},Sys.Net.WebServiceProxy.prototype._getPath),p=(Sys.Net.WebServiceProxy.prototype._getPath=function(){var e=c.apply(this,arguments);return e.trimStartChar("/").startsWith("Craba.Web")&&$settings.cwModName&&(e=$craba.joinPath($settings.cwModName,e),this._enableUnsafeHeader=!0),e},Sys.UI.Form.prototype.showModal),l=(Sys.UI.Form.prototype.showModal=function(e,t){e?(e=this._parentForm?this._parentForm.convertUrl(e,!0):$common.convertCWURL(this,e),e=x.getRealUrlAndServerName(e),p.apply(this,[x.getServerNameUrl(e.url,e.serverName),t])):p.apply(this)},_Z_58.prototype.showPage),g=(_Z_58.prototype.showPage=function(e,t,r,n,o){if(t){var i=t,s=e.findForm();if(s){if("object"==typeof i&&!(i=t.url))return;i=s.convertUrl(i,!0)}s=x.getRealUrlAndServerName(i),i=x.getServerNameUrl(s.url,s.serverName);return"object"==typeof t?t.url=i:t=i,l.apply(this,[e,t,r,n,o])}},_Z_58.prototype.ajax),f=(_Z_58.prototype.ajax=function(n){var o,e=$settings.AjaxInterceptOption.errorUrlQueue;if(e&&Object.keys(e).length){e=e[n.url];if(e&&e.count){var t=e.endTime-e.startTime,r=$settings.AjaxInterceptOption.intervalTime||5e3,i=$settings.AjaxInterceptOption.count||2,s=e.count;if(($settings.AjaxInterceptOption.maxErrorCount||3)<=s||t<=r&&e.count>=i)throw Error.abort($settings.AjaxInterceptOption.message)}}return"ngp"!=n.router?g.apply(this,arguments):((s=n.url).indexOf("://")<0&&(o=x.getRealUrlAndServerName(s),n.url=x.getServerNameUrl(o.url,o.serverName,!0),n.responseInterceptor=function(e){var t=e,r=$common.getMSAPI().getResponseInterceptor(o.serverName,o.url,e);return t=r?r(e,n.url):t}),g.apply(this,[n]))},_Z_58.prototype.getMSAPI=function(){return this._msAPI||(this._msAPI=new j)},_Z_58.prototype.getSysUIBaseURI=function(e){return $craba.joinPath($getRootURI(!0),this.getSysUIPath(e))},_Z_89.prototype.doShowStartPage),m=(_Z_89.prototype.doShowStartPage=function(){var e;this._showPage&&(this._showPage.startsWith("http")&&(this._showPage=$common.getUrlPath(this._showPage)),e=x.getRealUrlAndServerName(this._showPage),this._showPage=x.getServerNameUrl(e.url,e.serverName)),f.apply(this)},Sys.UI.Controls.TabPage.prototype._initFrozenPage),e=(Sys.UI.Controls.TabPage.prototype._initFrozenPage=function(){var e;this._cacheInfo&&((e=this._cacheInfo.url).startsWith("http")&&(e=$common.getUrlPath(e),e=x.getRealUrlAndServerName(e),this._cacheInfo.url=x.getServerNameUrl(e.url,e.serverName)),m.apply(this,arguments))},_Z_231.prototype.set_business=function(e){e&&(this._business="object"==typeof e?e:e.trim())},_Z_231.prototype.get_business=function(){return this._business},_Z_231.prototype.endUpdate),h=(_Z_231.prototype.endUpdate=function(){e.apply(this),this._updating=!0;var r,n=this.findForm();x.injectBusinessControl&&n&&this.get_business()&&n.get_action()&&x.getBusinessInfo((r=this).get_business(),function(e,t){x.injectBusinessControl.apply(r,[e,n.get_action(),t])}),this._updating=!1},Craba.UI.Image.prototype.set_serverName=function(e){this.serverName=e},Sys.UI.Controls.Image.set_serverName=function(e){this.serverName=e},Sys.UI.Controls.Image.get_serverName=function(){return this.serverName},Sys.UI.Controls.FileUpload.prototype.set_webMethodPath),d=(Sys.UI.Controls.FileUpload.prototype.set_webMethodPath=function(e){var t=x.getRealUrlAndServerName(e);e=x.getServerNameUrl(t.url,t.serverName,!0),h.apply(this,[e])},Sys.UI.Controls.Column.prototype._initEditorControl),y=(Sys.UI.Controls.Column.prototype._initEditorControl=function(e){d.apply(this,[e]);var r=this.findForm(),n=this.get_control(),e=this.get_business();r&&n&&e&&!n[e]&&r.get_action()&&x.getBusinessInfo(this.get_business(),function(e,t){x.injectBusinessControl.apply(n,[e,r.get_action(),t])})},_Z_1.prototype._todoData),v=(_Z_1.prototype._todoData=function(e,t){y.apply(this,[e,t]),e&&("object"!=typeof e.queryParams&&(e.queryParams={}),delete e.queryParams.gridFilter,e.filter&&e.filter.items&&(e.queryParams.gridFilter=e.filter.items),delete e.filter)},Sys.Net.WebRequest.prototype.isCrabaAPI=function(e){if(!e.startsWith("http"))return!0;var t=$common.getHostUrl(),t=e.startsWith(t);if(t)return!0;if($ms.router&&$ms.router.debugMs){if(t=e.startsWith($ms.router.debugMs.aloneServer))return!0;t=e.startsWith($ms.router.debugMs.gateway)}return t},Sys.Net.WebRequest.prototype.get_headers),_=(Sys.Net.WebRequest.prototype.get_headers=function(e){var t;return this._setedHeaders||(t=(new j).apiHeaders())&&this._headers&&(Object.copyTo(t,this._headers),this._setedHeaders=!0),v.apply(this,arguments)},Sys.Net.WebRequest.prototype.checkStatus=function(e,t){var r=t.get_statusCode(),n=(new Date).getTime(),o=-1,e=(e&&e.code&&(o=e.code),$settings.AjaxInterceptOption),i=e.urlWhiteList||[],s=e.errorStatusCode||[],s=[404].concat(s);(e.isOpenToFusing&&s.includes(r)||s.includes(o))&&(r=t.get_webRequest().get_url(),i.includes(r)||(e.errorUrlQueue=e.errorUrlQueue||{},e.errorUrlQueue[r]?(e.errorUrlQueue[r].endTime=n,e.errorUrlQueue[r].count+=1):e.errorUrlQueue[r]={errorApi:r,startTime:n,count:1}))},Sys.Net.WebRequest._resolveUrl),b=(Sys.Net.WebRequest._resolveUrl=function(e,t){var r;return 0<=e.indexOf("//")?e:(r=x.getRealUrlAndServerName(e),e=x.getServerNameUrl(r.url,r.serverName),r=_.apply(this,[e,t]),0<=e.indexOf("://")?r:(0<=(r=r.substr(0,r.indexOf(e))).indexOf("/")&&(t=r.indexOf("://"),r=r.substr(0,t)+"://"+(r=0<=(t=(r=r.substr(t+3)).indexOf("/"))?r.substr(0,t):r)),$craba.joinPath(r,e)))},Sys.Net.PageRequest.prototype._resolveBody=function(e){var t=e.url;t.startsWith("/"+$settings.cwModName+"/")||t.startsWith($settings.cwModName+"/")||-1<t.indexOf("/"+$settings.cwModName+"/")||(e.body=null)},_Z_138.prototype.customResolveProp=function(e,t){if(x._specialKeys.includes(e)&&t.indexOf&&0<=t.indexOf(".")&&!t.trim().startsWith("$")){if(t.indexOf(".")<0)throw new Error("请指定"+e+'="'+t+'"的模块名称【key='+e+"】");t=!!x.getPower&&x.getPower(t)}return t},Sys.UI.Controls._MDIChild.prototype.set_caption),w=(Sys.UI.Controls._MDIChild.prototype.set_caption=function(e,t){!t&&this._isSetCaption&&this.get_isLoading()||t&&this._isSetCaption&&this._clickMenuItem||(t&&(this._clickMenuItem=!Object.isUndefinedOrNull($craba._curMenuItem),this._isSetCaption=!0),b.apply(this,arguments))},Craba.UI.Page.prototype.getCrabaResourceUrl),$=(Craba.UI.Page.prototype.getCrabaResourceUrl=function(e,t){var r;return this.page?(r=this.page.actionType)&&r.startsWith($settings.cwDefaultName)?(e=$settings.cwModName+$craba.joinPath("/R.axd?v="+this.Context.version+"&r=aprac",e),$common.addUrlVersion(e,$settings.cwjavaName)):(window.$shell&&t?r=$app.isLocal()?$ms.router.debugMs.aloneDeploy:"shell":this.Context.scriptPath&&$ms.router&&$ms.router.crabaConfigConvertNames&&0<$ms.router.crabaConfigConvertNames.length?r=this.Context.scriptPath.split("/")[1]:this.page.xmlNode?r=this.page.xmlNode.Attributes.ActionType:this.page.action&&(r=this.page.action),$craba.joinPath(x.getServerName(r),e)):w.apply(this,[e])},Craba.UI.Page.prototype.convertUrl),N=(Craba.UI.Page.prototype.convertUrl=function(e,t){if((e=$.apply(this,[e]))&&!e.startsWith("http")){if(this.page&&this.page.actionType&&this.page.actionType.startsWith($settings.cwDefaultName))return $craba.joinPath($settings.cwModName,e);!t||e.indexOf("/")<0||(t=x.getRealUrlAndServerName(e),e=x.getServerNameUrl(t.url,t.serverName))}return e},Sys.UI.CustomForm.prototype.saveData),S=(Sys.UI.CustomForm.prototype.saveData=function(e){var t=N.apply(this,[e]),r=this._controls;if(r)for(var n=0;n<r.length;n++){var o=r[n];if(o.get_business&&o.get_business()&&o[o.get_business()]&&(a=o[o.get_business()])&&a.bindData&&a.bindData(t),"Sys.UI.Controls.Grid"==Object.getTypeName(o))for(var i=o.get_columns(),s=0;s<i.length;s++){var a,u=i[s];u.get_business&&u.get_business()&&u[u.get_business()]&&(a=u[u.get_business()])&&a.bindData&&a.bindData(t)}}return t},Sys.UI.Controls.ImageColumn.prototype.initialize);function I(e,t){var r,t=t.tipInfo;e&&((r=e.get_hint()).length?(t=t.replace(t,'&nbsp;<span style="font-weight:700;color:red;">$&</span>'),e.set_hint(r+t)):e.set_hint(t))}function C(e){var t;if(e)return(e=0<=(t=e.indexOf("?"))?e.substring(0,t):e).startsWith("/")?e:"/"+e}Sys.UI.Controls.ImageColumn.prototype.initialize=function(){S.apply(this,arguments);var e=$settings.ImageColumnImageSrc;e==this._imageUrl&&e&&!e.startsWith("http")&&(e=x.getRealUrlAndServerName(e),this._imageUrl=x.getServerNameUrl(e.url,e.serverName,!0))},$craba._getPageInternalId=function(e){var t,r;return $ms.router&&$ms.router.crabaConfigConvertNames&&0<$ms.router.crabaConfigConvertNames.length&&(r=(t=e.split("/"))[1],$ms.router.crabaConfigConvertNames.includes(r))&&(t.splice(1,1),e=t.join("/")),$craba.getHashCode(e)};var U=[I,function(e,t){var r;(r=e)&&r.get_enabled()&&r.set_enabled(!1),I(e,t)},function(e,t){var r=t.tipInfo;t.maxNum=t.maxNum||0,0!==r.length&&t.maxNum<3&&($common.showInfo(r,{title:"提示",position:"top-right"}),t.maxNum++)}],P=Sys.UI.CustomForm.prototype.doLoadedControls;Sys.UI.CustomForm.prototype.doLoadedControls=function(e){if(P.apply(this,[e]),this.get_scriptPath()){var t=$cache.get("_func_limit")||{};if(t)for(var r=C(this.get_scriptPath()),n=t[r]||[],o=n.length,i=0;i<o;i++){var s,a,u=n[i];C(u.url)===r&&(a=u.target,s=u.tipType,a=this[a],0<=s)&&(U[s](a,u),$cache.set("_func_limit",t))}}},_Z_235.prototype.getlimitHeader=function(e){var t,r=this.getResponseHeader("Function-Limit");r&&(r=decodeURIComponent(r),t=$cache.get("_func_limit")||{},r?(r=JSON.parse(r)).length&&r[0].url&&(t[C(r[0].url)]=r):delete t[C(e)],$cache.set("_func_limit",t))}}.call(x),(e=e(r={loadVersions:function(e,t){x.loadVersions(e,t)},getLocalAndRemotePrinters:function(e){return x.getLocalAndRemotePrinters(e)},getRemotePrinters:function(e){return x.getRemotePrinters(e)},getLocalPrinters:function(e){return x.getLocalPrinters(e)},get_service:function(){return $common.getMSAPI()},get_url:function(e,t){var r=x.getRealUrlAndServerName(e);return e=x.getServerNameUrl(r.url,r.serverName,t)},getPower:function(e){return!!x.getPower&&x.getPower(e)},getUtil:function(){return x},getAPI:function(){return new j}}))&&("function"==typeof e.apiHeaders&&(j.prototype.customHeaders=e.apiHeaders),"function"==typeof e.responseInterceptor&&(j.prototype.getResponseInterceptor=e.responseInterceptor),"function"==typeof e.globalErrorInterceptor&&(j.prototype.getReponseGlobalErrorInterceptor=e.globalErrorInterceptor),"function"==typeof e.serverName&&(x.getServerName=e.serverName),"function"==typeof e.interceptServerName&&(x.interceptServerName=e.interceptServerName),"function"==typeof e.injectBusinessControl&&(x.injectBusinessControl=e.injectBusinessControl),"function"==typeof e.getAPIServiceUrl&&(x.getAPIServiceUrl=e.getAPIServiceUrl),"function"==typeof e.getSaveGridConfigUrl&&(x.getSaveGridConfigUrl=e.getSaveGridConfigUrl),"function"==typeof e.getPower)&&(x.getPower=e.getPower),r)};var configLoadedItems={},configLoadingItems={};$initMs(function(c){var t,o,a,p;if(c)return(t={forEach:function(e,t){if(e){var r=typeof e;if("function"!=r)if("string"==r)for(var n=0;n<e.length&&!t(e.substr(n,1),n);n++);else if("object"==r)if(e.length===undefined)for(var o=Object.keys(e),n=0;n<o.length&&!t(o[n],n);n++);else for(n=0;n<e.length&&!t(e[n],n);n++);return e}}}).forEach(t,function(e){c[e]=t[e]}),c.setBusinessControlFullNameFun=function(e,t){if("function"!=typeof t)throw new Error("第二个参数getBusinessControlFullFun(传入业务控件名称)必须是一个返回字符串的函数");c.businessFun||(c.businessFun={}),c.businessFun[e]=t},c.ngpConfig={unlockConfig:function(e){},saveConfig:function(e,t){var r=$ms.ngpConfig["_"+e];if(e&&0<=e.indexOf("."))throw new Error("暂不支持带.的配置保存");if(!r)throw new Error("未指定"+e+"对应的保存地址");if(200!=(t=$common.ajaxSync({url:r,data:t,router:"ngp"})).code)throw r=t.message||"保存配置出错，请重试",Error.abort(r);$ms.ngpConfig[e]=t},setConfigAsync:function(t,e,r,n,o,i,s){!l(t,e,i)||configLoadingItems[t]||(configLoadingItems[t]=!0,$common.ajax({url:e,data:r,router:"ngp",waiting:s,complete:function(){delete configLoadingItems[t]},success:function(e){u(t,n,e,o)}}))},setConfig:function(e,t,r,n,o,i){if(l(e,t,i)){i=$common.ajaxSync({url:t,data:r,router:"ngp"});if(!i)throw Error.abort("setConfig发生异常，异常地址:"+t);if(200!=i.code)throw r=t+" "+(i.message||"服务配置异常，请联系管理员处理。"),Error.abort(r);u(e,n,i,o)}}},c.bus=(o={},{add:function(e,t){if("function"!=typeof t)throw new Error("总线通知第二个参数当前只支持传入函数");var r=o[e];r||(o[e]=[],r=o[e]),r.push(t)},notifyOnce:function(e,t){if(o[e]){for(var r=o[e],n=0;n<r.length;n++)(0,r[n])(t);delete o[e]}}}),c.register={businessControl:function(e,t,r){if(!e)throw new Error("请先设置业务控件的名");e=e.trim(),t?"string"!=typeof t&&(r=t,t="Sys.Control.BusinessControl"):t="Sys.Control.BusinessControl";t=new Function(("%s=function(){%s.initializeBase(this);};%s.prototype = {};%s.registerClass('%s', "+t+"); return %s;").replaceAll("%s",e))();$craba.copyPrototype(r,t)}},c.power={},a={},c.create=function(n,e,t){if(!n)throw new Error("模块名不能为空");if(a[n])throw new Error("重复定义模块名称"+n);return a[n]={loadedTask:[]},"string"==typeof e&&setTimeout(function(){!function r(e){var t;!e||e.length<=0?setTimeout(function(){for(var e=0;e<a[n].loadedTask.length;e++){var t=a[n].loadedTask[e];t.cb&&t.cb.apply(t.cb,t.params)}delete a[n].loadedTask}):0==(t=e.splice(0,1)[0]).trim().length?r(e):$common.loadScript(t,function(){r(e)},null,!0)}(e.split(","))}),t},c.setResponseInterceptorFun=function(e,t){if("function"!=typeof t)throw new Error("第二个参数getResponseInterceptorFun(传入API拦截器)必须是一个的函数");c.responseInterceptor||(c.responseInterceptor={}),c.responseInterceptor[e]=t},p=c,{apiHeaders:function(){var e,t,r;return p.router?(r={},r=(t=(e=p.router).debugMs)?{"ngp-authorization":e["ngp-authorization"],"ngp-router":JSON.stringify(t),isdebug:!0}:{"ngp-authorization":e["ngp-authorization"],"ngp-router":e["ngp-router"]},$ms.server&&(r["ngp-debug-router"]=JSON.stringify($ms.server)),r):{}},serverName:function(e,t){var r,n=p.router;return n?(n=n.debugMs,r=g(e),n?r==n.aloneDeploy?t?$craba.joinPath(n.aloneServer,r):$craba.joinPath($common.getHostUrl(),r):n.gateway?$craba.joinPath(n.gateway,r):$craba.joinPath($common.getHostUrl(),r):$craba.joinPath($common.getHostUrl(),r)):0<=e.indexOf(".")?e.substr(0,e.indexOf(".")):e},injectBusinessControl:function(e,t,o){var r,n=e.split("."),i=e.indexOf("."),i=(0<=i?(n=e.substr(0,i),e=e.substr(i+1)):n=0<(i=e).indexOf(".")||(i=String.format("shell.control.{0}",i),!$app.businessList[i])?g(t):"shell",p.businessFun?p.businessFun[n]:"");if("function"==typeof i){if(!(r=i(e)))return}else r=n+"."+e;if("shell"==n&&$app.initBusinessType(r),window[n]){i=null;try{i=evil2(r)}catch(s){return void f(r,n)}i?(this[e]=evil2("new "+r+"();"),this[e].bindActionAndControl(t,this,o)):a[n]&&a[n].loadedTask&&(i={params:[e,r,t,this],cb:function(e,t,r,n){n[e]=evil2("new "+t+"();"),n[e].bindActionAndControl(r,n,o)}},a[n].loadedTask.push(i))}else f(r,n)},getAPIServiceUrl:function(e){return!e&&p&&p.router&&p.router.debugMs&&p.router.debugMs.aloneDeploy?p.router.debugMs.aloneServer+"/":(e&&p&&p.router&&p.router.debugMs&&(p.router.debugMs.aloneServer&&e==p.router.debugMs.aloneDeploy?e=p.router.debugMs.aloneServer+"/"+e+"/":p.router.debugMs.gateway?e=p.router.debugMs.gateway+"/"+e+"/":console.log("调试模式下，未设置gateway地址")),e)},responseInterceptor:function(e,t,r){if(t&&0<=t.indexOf("pm/getMenu")&&r&&"200"==r.code){var n=r.data.permissions,o={};p.power=$ms.power={};for(var i=0;i<n.length;i++){var s=n[i],a=s.serverKey.split(".")[0],u=p.power[a];u||(o[a]={},Object.defineProperty(p.power,a,{configurable:!1,enumerable:!1,writable:!1,value:o[a]}),u=o[a]),u[s.serverKey]||Object.defineProperty(u,s.serverKey,{configurable:!1,enumerable:!1,writable:!1,value:{name:s.name,value:s.value,msg:s.msg}})}}if(p.responseInterceptor)return p.responseInterceptor[e]},getPower:function(e){if(Object.isUndefinedOrNull(e)||"string"!=typeof e)throw new Error("权限获取格式必须是：模块名.关键字，字符串类型");var t=e.indexOf(".");if(t<=0)throw new Error("权限获取格式必须是：模块名.关键字");var r=e.substr(0,t);if(e.substr(t+1))return!!(p.power[r]&&p.power[r][e]&&p.power[r][e].value);throw new Error("权限key不能为空")},interceptServerName:function(e){var t,r;return e&&(t="shell",window.__rootRouter&&(t=window.__rootRouter),0<=(r=e.indexOf("_Sys/UI/")))&&e.indexOf(t+"/_Sys/UI/")<0&&(e=e.substr(r),e=$craba.joinPath(t,e)),e}};function u(e,t,r,n){configLoadedItems[e]=r.data;for(var o=e.split("."),i=c.ngpConfig,s=0;s<o.length-1;s++){var a=o[s];i[a]||(i[a]={}),i=i[a]}var u,r=o.pop();i[r]||(u=e,Object.defineProperty(i,r,{configurable:!1,enumerable:!1,get:function(){return configLoadedItems&&configLoadedItems[u]?configLoadedItems[u]:t||{}}})),"function"==typeof n&&n(i[r])}function l(e,t,r){if(t)return r&&($ms.ngpConfig["_"+e]=r),1;throw new Error("请配置"+e+"的请求地址！")}function g(e){return e?("string"==typeof e?e:e.constructor.__typeName).split(".")[0]:""}function f(e,t){e=e+"业务组件解析失败，相关业务功能将失效，因为"+t+"模块未初始化或未定义";console.error(e),$common.showError(e)}}),$ms.appendFunToMainAction=function(e,t){var r=Sys.Application._forms;if(0==r.length)throw new Error("未找到页面");r=r[0].get_action();if(!r)throw new Error("未找到页面对应的action");if(!r[e])throw new Error(r+"已定义方法"+e);r[e]=t},delete window.$initMs;
Sys.UI.PageAction.prototype.get_businessModel=function(){return this.$businessModel||(this.$businessModel=new Sys.Control.BusinessModel(this)),this.$businessModel},Type.registerNamespace("Sys.Control"),Sys.Control.BusinessModel=function(t){(this.action=t).$businessControls||(t.$businessControls=[])},Sys.Control.BusinessModel.prototype={dispose:function(){},bus:function(t,s,n,e){var i=t,o={};if(this.action){var t=this.action.get_root(),r=t.$businessControls;if(r){e=e&&e.length&&"string"!=typeof e?e:[];for(var l=t.get_form(),u=0;u<e.length;u++){var a=e[u];if(l[a]){var c=l[a];if(c&&c.get_visible())if(c.get_business()){var g=c[this._getBusinessName(c.get_business())];if(g[i]&&!1===g[i](s,n,o))return o}else console.log("未验证控件"+a)}}for(var h=0;h<r.length;h++){g=r[h];if(g[i]&&(g.control&&g.control.get_visible())){if(g.control){var f=g.control.get_id();if(0<=e.indexOf(f))continue}if(!1===g[i](s,n,o))return o}}}}return o},_findControl:function(t,s,n){var e,i;return s?(e=s[t],(i=t.indexOf("."))<0?!e&&n&&s.get_isChildForm()?this._findControl(t.substring(i+1),s.get_parentForm(),!1):e:(s=s[(n=t.split("."))[0]])?(Sys.UI.CustomForm.isInstanceOfType(s)?e=this._findControl(t.substring(i+1),s,!1):Sys.UI.Controls.Grid.isInstanceOfType(s)&&(e=s.findColumn(n[1])),e):void 0):null},_getBusinessName:function(t){var s=null;try{t=t.replaceAll("'",'"'),s=JSON.parse(t).Name}catch(n){s=t}t=s.indexOf(".");return t<0?s:s.substr(t+1)},limit:function(t,s,n){if(t)for(var e=this.action.get_form(),i=Object.keys(t),o=0;o<i.length;o++){var r=i[o],l=this._findControl(r,e,!0);l&&l.get_business()&&(l=l[this._getBusinessName(l.get_business())])&&(r=l[t[r]])&&r.apply(l,[s,n])}},registerToModel:function(t){this.action.$businessControls.push(t);var s=this.action.get_form();s&&s.get_isChildForm()&&(s=s.get_parentForm())&&(s=s.get_action(!0))&&(s.$businessControls||(s.$businessControls=[]),s.$businessControls.push(t))}},Sys.Control.BusinessControl=function(){Sys.Control.BusinessControl.initializeBase(this),this.control=null,this.businessModel=null,this._bindedEvents={},this._actionEvents=[],this.businessData={}},Sys.Control.BusinessControl.prototype={bindActionAndControl:function(t,s,n){((this.control=s)._bizControl=this).businessModel=t.get_businessModel(),this.businessModel.registerToModel(this),this.businessData=n,Sys.Control.BusinessControl.callBaseMethod(this,"initialize")},determine:function(t,s,n){return this.businessModel.limit(t,s,n)},notify:function(t,s,n,e){return this.businessModel.bus(t,s,n,e)},getEventPre:function(t){var s="$";return this.control.get_grid&&this.control.get_grid()&&(s+=this.control.get_grid().get_id()+"$"),this.control.get_id()&&(s+=this.control.get_id()+"$"),this.control.get_dataField&&this.control.get_dataField()&&(s+=this.control.get_dataField().replace(/\./g,"_")+"$"),"_"+s+"_"+t},bindControlEvent:function(t){var s,n;if(this[t]&&"function"==typeof this[t])return(s=this.get_form().get_action())[n=this.getEventPre(t)]||(s[n]=$createDelegate(this,t),this._actionEvents.push(n)),n;throw new Error(Object.getTypeName(this)+" 业务控件未找到方法 "+t)},get_service:function(){var t=this.get_form();return t?t.get_action().get_service():null}},Sys.Control.BusinessControl.registerClass("Sys.Control.BusinessControl",Sys.BusinessControl);