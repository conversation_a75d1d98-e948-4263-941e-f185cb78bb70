<?xml version="1.0" encoding="utf-8"?>
<Page xmlns="Craba.UI" Title="列配置" CssClass="plr0" ActiveControl="btnExit" ActionType="Craba.Web.UI.Grid.GridConfigAction, _Sys/UI/Grid/GridConfig.js" AllowResize="true" MinWidth="800" MinHeight="640" OnClose="doClose">

  <FlexBlock CssClass="GridConfigPage pl10">
    <FlexColumn>
      <Grid ID="grid" AllowEditCaption='false' AllowDragHeight='false' AllowDragColumn='false' AllowFilterRows='false' ReadOnly="false" LazyPageSize="0" AllowPopup="false" OnContextMenu="doContextMenu" PrimaryKey="name" ModifyOnly="true" AllowConfig="false" AllowFilter="false" SaveConfig="false" OnSelectionChanged="doSelectionChanged" OnCellRendering="doCellRendering" OnCellBeginEdit="doCellEdit" DefaultRowCount="1" WordWrap="false" DragField="designCaption" OnRowDblClick="#{btnVisible.click()}" AllowDrag="true" OnDrop="doDrop" MinHeight="360">
        <IconColumn Caption="锁定" Visible="true" AllowResize="false" DataField="lockUrl" Width="40" HeaderAlign="Center" />
        <TextColumn Caption="默认名称" ReadOnly="true" DataField="designCaption" Width="200" AllowStretch="true" />
        <NumberColumn Caption="列宽" DataField="width" Width='60' MinValue="10" MaxValue="999" NumberType="PositiveInt" ShowCalculator="false" AllowFilter="false" OnChange="doWidthChange" />
        <TextColumn Caption="显示名称" DataField="caption" Width="200" AllowStretch="true" AllowFilter="false" OnChange='doModifyCaption' MaxLength="10" />
        <CheckBoxColumn Caption="显示" ReadOnly="true" DataField="visible" Width="60" AllowFilter="false" />
      </Grid>

      <FlowPanel ID='panel' CssStyle="background-color:#fafafa;padding:10px 10px 0 10px">
        <NumberEdit ShowCalculator="false" DataField="headerHeight" Label="表头行高:" OnChange="doChangeStyle" MinValue="20" MaxValue="200" ShowEmpty="true" NumberType="PositiveInt" />
        <NumberEdit ShowCalculator="false" DataField="rowHeight" Label="表体行高:" OnChange="doChangeStyle" MinValue="20" MaxValue="200" ShowEmpty="true" NumberType="PositiveInt" />
        <CheckBox Label="列名加粗:" DataField="captionBold" OnChange="doChangeStyle" />
        <CheckBox Label='排序开关:' ID='btnSort' OnChange="doModifySort" />
      </FlowPanel>
    </FlexColumn>

    <Block ID="right" CssClass="ConfigButtonsBlock pr10">
      <Button ID="btnTop" Text="置顶" OnClick="doTop" Icon="aicon-vertical-align-top" />
      <Button ID="btnDown" Text="置底" OnClick="doDown" Icon="aicon-vertical-align-botto" />
      <Button ID="btnVisible" Text="显示/隐藏" OnClick="doToggleVisible" Icon="aicon-yincang" />
      <Button ID="btnAll" Text="一键显示" Hint="一键显示当前所有隐藏的列" OnClick="doVisibleAll" Icon="aicon-kejian" />
      <BadgeButton ID="btnReset" Text="还原配置" OnClick="doReset" Icon='aicon-qingkong1' CssClass="SpecialButton" />
      <Block CssClass="Flex1" />
      <CancelButton ID="btnExit" CssClass="ConfigCloseButton" Text="关闭/退出" />
    </Block>
  </FlexBlock>
  <Style>
    .Grid .GridBodyRow.active{font-weight:bold;}
    .BadgeButton.Button{margin-right:0}
  </Style>
</Page>