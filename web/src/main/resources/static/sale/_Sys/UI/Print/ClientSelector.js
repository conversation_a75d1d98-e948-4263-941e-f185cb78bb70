Type.registerNamespace('Craba.Web.UI.Print');

Craba.Web.UI.Print.ClientSelectorAction = function() {
  Craba.Web.UI.Print.ClientSelectorAction.initializeBase(this);
};

Craba.Web.UI.Print.ClientSelectorAction.prototype = {
  initialize: function() {
    Craba.Web.UI.Print.ClientSelectorAction.callBaseMethod(this, 'initialize');

    if ($settings.HideAccessCode) {
      this.get_form().accessFlex.set_visible(false);
      this.get_form().accessPanel.set_visible(false);
    }
    this.loadClients();
    this.selectMap = {};
  },

  dispose: function() {
    $common.hideTips();

    Craba.Web.UI.Print.ClientSelectorAction.callBaseMethod(this, 'dispose');
  },

  loadClients: function() {
    var that = this;
    var form = that.get_form();
    $print.getPrintClients(function(data) {
      if (!data) data = [];
      if (data.length == 0) {
        if (form.infoBox.get_element().childNodes.length == 0) {
          $common.showInfo('<span>您还没有授权远程打印机或远程打印机没有启动无法访问。<br/>请先获取授权码，然后将授权码发送给安装有Teemo版本打印机进行授权。<a href="https://help.wsgjp.com/5e03/086a/89c0/9497/1a9e" target="_blank">查看授权方法</a></span>', {
            only: true,
            timer: 0,
            className: 'f12 Info aicon-tishi5',
            target: form.infoBox
          });
        } else {
          $common.showError('远程打印机没有启动或无法访问');
        }
      } else {
        form.infoBox.get_element().innerHTML = '';
      }

      form.listClient.set_items(data);
      form.listClient.set_selectedIndex(0);
      that.doClientChanged(form.listClient);
      that.doBindSelected();
    });
  },

  doClientChanged: function(sender) {
    var grid = this.get_form().gridPrint;
    var index = sender.get_selectedIndex();
    var items = sender.get_items();
    if (items.length === 0) {
      grid.refresh(null);
      return;
    }

    var selectClient = items[index];
    var printerNames = [];
    for (var i = 0; i < selectClient.printerNames.length; i++) {
      printerNames.push({
        PrinterName: selectClient.printerNames[i].printerName
      });
    }
    grid.refresh(printerNames);
  },

  doBindSelected: function() {
    if (!$print._getRemoteClients) return;
    var selectedList = $print._getRemoteClients();
    if (!selectedList || selectedList.length < 1) return;

    Array.forEach(selectedList, function(item) {
      this.selectMap[item.clientToken] = item;
    }, this);
    this.refreshItems();
  },

  doSelect: function(sender) {
    var form = sender.get_form();
    var printer = form.gridPrint.get_selectedRowData();
    if (!printer) return;

    var clientToken = form.listClient.get_value();
    var clientTagName = form.listClient.get_text();
    var printerName = printer.PrinterName;
    var text = clientTagName + "(" + printerName + ")";
    var item = {
      text: text,
      clientToken: clientToken,
      printerName: printerName
    };

    this.selectMap[item.clientToken] = item;
    this.refreshItems();
  },

  refreshItems: function() {
    var btnsSelect = this.get_form().btnsSelect;
    var items = this.getSelectedData();
    btnsSelect.set_items(items);
  },

  doRefresh: function(sender) {
    var grid = this.get_form().gridPrint;
    grid.dataBind([]);
    this.loadClients();
    this.selectMap = {};
    this.refreshItems();
  },

  doGetAccessCode: function(sender) {
    $print.getAccessCode(function(result) {
      if (!result) {
        $common.showTips('授权码获取异常');
        return;
      }
      sender.get_form().teAccessCode.set_text(result.accessCode);
      $common.copyText(result.accessCode);
    });
  },

  doRemove: function(sender, args) {
    var clientToken = args.get_data().clientToken;
    delete this.selectMap[clientToken];
    this.refreshItems();
  },

  doSure: function(sender) {
    if (Object.isEmpty(this.selectMap)) {
      $common.showTips('请先双击打印机列表选择打印机');
      return;
    }
    var items = this.getSelectedData();
    var form = sender.get_form();
    form.result = items;
    if ($print._saveRemoteClients) $print._saveRemoteClients(items); // 统一缓存+保存配置
    form.doOk();
  },

  getSelectedData: function() {
    var items = [];
    for (var name in this.selectMap) {
      items.push(this.selectMap[name]);
    }
    return items;
  }
};
Craba.Web.UI.Print.ClientSelectorAction.registerClass('Craba.Web.UI.Print.ClientSelectorAction', Sys.UI.PageAction);