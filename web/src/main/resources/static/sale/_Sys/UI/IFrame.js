Type.registerNamespace('Craba.Web.UI');

Craba.Web.UI.IFrameAction = function() {
  Craba.Web.UI.IFrameAction.initializeBase(this);
}

Craba.Web.UI.IFrameAction.prototype = {
  context: function(cb) {
    var form = this.get_form();
    var params = form.get_pageParams();
    var contextData = {};
    contextData.Title = params.Title;
    contextData.Url = params.Url;
    contextData.ID = '__mainIFrame';
    cb(contextData);
  },

  initialize: function() {
    Craba.Web.UI.IFrameAction.callBaseMethod(this, 'initialize');
    var form = this.get_form();
    var iframe = document.createElement("iframe");
    iframe.frameBorder = "0";

    var id = this.get_context('ID');
    if (id) iframe.id = id;

    var src = this.get_context('Url');
    if (src) iframe.src = src;
    form.iframeDiv.get_element().appendChild(iframe);

    try {
      $addHandler(iframe.contentWindow, 'keydown', function(e) {
        if (e.keyCode == Sys.UI.Key.esc) {
          $app._doHotkey(e);
        }
      });
    } catch (ex) {
      // 跨域了
    }
  }
}
Craba.Web.UI.IFrameAction.registerClass('Craba.Web.UI.IFrameAction', Sys.UI.PageAction);