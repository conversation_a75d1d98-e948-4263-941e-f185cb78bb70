$jarviscommon =
    {
        _getUnitQty: function (unitList, qty) {
            unitList = $jarviscommon._bubbleSort(unitList);
            var unitQty = "";
            if (qty == "0")
                return unitQty;
            if (unitList != null && unitList.length > 0) {
                for (var j = 0; j < unitList.length; j++) {
                    var unitName = unitList[j];
                    if (j < unitList.length - 1) {
                        var tempQty = Math.floor(qty / unitName.unitRate);
                        if (tempQty > 0) {

                            unitQty += tempQty + unitName.unitName;
                            qty = $jarvismoney._doCalculateQty(qty - (tempQty * unitName.unitRate));
                        }
                    } else {
                        if (qty > 0)
                            unitQty += (qty / unitName.unitRate) + unitName.unitName;
                    }
                }
            }
            return unitQty;
        },
        _bubbleSort: function (arr) {
            var len = arr.length;
            if (len >= 1) {
                for (var i = 0; i < len - 1; i++) {
                    for (var j = 0; j < len - 1 - i; j++) {
                        if (arr[j].unitRate < arr[j + 1].unitRate) {
                            var temp = arr[j + 1];
                            arr[j + 1] = arr[j];
                            arr[j] = temp;
                        }
                    }
                }

            }
            return arr;
        },
        /**
         * 刷新合计
         * @param that
         * @param inData
         * @param failedCallback
         * @param succeededCallback
         * @private
         */
        _refreshSummary: function (url, that, inData, succeededCallback, failedCallback) {
            var postData = Object.clone(inData);
            // 获取查询条件
            $jarvisUtils.post(url, postData, function (data) {
                if (!data) {
                    failedCallback && failedCallback($language.get("summererror", "合计失败！"));
                    return;
                }
                if (data.data) {
                    var result = {};
                    that.event('beforeSummaryDataBind', [data.data, postData.queryParams]);
                    for (var i = 0; i < data.data.name.length; i++) {
                        result[data.data.name[i]] = data.data.pageSummary[i];
                    }
                    succeededCallback(result);
                } else {
                    failedCallback($language.get("summererror", "合计失败！"));
                }
            }, function () {
                failedCallback && failedCallback($language.get("summererror", "合计失败！"));
            });
        },
        // 用于ajax 异步处理表格刷新事件
        gridFailedCallback: function (message) {
            // throw Error(message);
            var that = this;
            return function () {
                // that.grid.findPager().endLoading();
            };
        },
        _refreshGrid: function (url, that, inData, succeededCallback, failedCallback) {
            var postData = Object.clone(inData);
            succeeded = function (res) {
                that.event('beforeDataBind', [res, postData.queryParams]);
                succeededCallback(res);
            };
            $jarviscommon.refresh(url, postData, succeeded, failedCallback);

        },
        refresh: function (uri, data, call, failed) {
            if (!uri || !data || !call || !failed) {
                throw new Error(this.Langage.NotNull);
            }
            var postData = Object.clone(data);
            $jarvisUtils.post(uri, postData, function (data) {
                if (!data || !data.data || !data.data.list || data.data.list.length==0) {
                    call && call({itemList: [], itemCount: 0})
                }else{
                    call && call({itemList: data.data.list, itemCount: data.data.total});
                }
            }, failed);
        },

        _shareTotalToDetail: function ( bill,succeededCallback) {
            // 获取查询条件
            $jarvisUtils.post("sale/jarvis/deliver/detail/sharePlatformPreferentialTotal", bill, function (data) {
                if (!data) {
                    return null;
                }
                if (data.data) {
                    succeededCallback(data.data);
                } else {
                    return null;
                }
            }, function () {
                return null;
            });
        },
        _getDisedTaxedTotalColumnName:function () {
            var filedNmae = $ms.ngpConfig.Sys.sysGlobalEnabledTax ? "折后含税金额" : "折后金额";
            return filedNmae;
        },
        _getDisedTaxedPriceColumnName:function () {
            var filedNmae = $ms.ngpConfig.Sys.sysGlobalEnabledTax ? "折后含税单价" : "折后单价";
            return filedNmae;
        },

        setCookie:function (name,value){
            var Days = 30;
            var exp = new Date();
            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString();
        },

        getCookie:function (name){
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
                return unescape(arr[2]);
            else
                return null;
        },

        pddRiskControl:function (){
            if($jarviscommon.getCookie("pati")){
                return;
            }
            $jarvisUtils.post("sale/jarvis/pinduoduoController/getPageCode", function (result) {
                if (result != null && result.code=="200" && result.data) {
                    $jarviscommon.setCookie("pageCode", result.data.pageCode);
                    PDD_OPEN_init({
                        code: result.data.pageCode
                    }, function () {
                        window.PDD_OPEN_getPati().then(
                            function (pati) {
                                var Days = 30;
                                var exp = new Date();
                                exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
                                document.cookie = "pati=" + escape(pati) + ";expires=" + exp.toGMTString();
                            }).catch(function (err) {
                            console.log(err);
                        })
                    });
                }
            });
        },
        _getFullNameText:function (rowData,str) {
            var text = "<div  class=\"dflex pr10 overhidden\"><span  class=\"ellipsis\">" + str + "</span>";
            if (rowData && rowData.gift) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipZeng">赠</span>';
            }
            var ptype = null;
            if(rowData.ptype){
                ptype = rowData.ptype;
            }else{
                ptype = rowData;
            }
            if (rowData && rowData.combo) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipTao">套</span>';
            }
            if (rowData && ptype.batchenabled) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipPi">批</span>';
            }
            if (rowData && ptype.snEnabled) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipXu">序</span>';
            }
            if (rowData && rowData.propFormat) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipShu">属</span>';
            }
            else if (rowData && rowData.propEnabled) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipShu">属</span>';
            }
            if (rowData && (ptype.pcategory == "VIRTUAL" || ptype.detailPcategory=="1" || ptype.pcategory=="1")) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipFu">服</span>';
            }
            text += "</div>";
            return text;
        }
    }
