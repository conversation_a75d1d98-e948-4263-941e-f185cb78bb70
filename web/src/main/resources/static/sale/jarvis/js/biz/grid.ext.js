//绑定基础配置中的支持商品自定义字段
Sys.UI.Controls.Grid.prototype.enableDynamicColumnForJarvis = function (options, afterFunc) {
    var columns = this.get_columns();
    var self = this;
    var form = this.get_rootForm() || this.get_form();
    var action = form.get_action();
    action.printNotifyName = Object.getTypeName(action);

    options = Object.assign({append: false, mapping: {}}, options);

    function modifyColumn(index, field) {
        var column = columns[index]

        //如果 allowConfig 是 false 不做任何修改
        if (column.get_allowConfig()) {
            if (field.title && field.title != field.oldTitle) {
                column.set_caption(field.title)
            }
            column.set_allowConfig(field.hasOwnProperty('allowConfig') ? field.allowConfig : field.visible)
            column.set_visibleWithConfig(field.visible)
            column.set_readOnly(field.readOnly)
        }
    }

    function buildColumn(field) {
        var existsIndex = columns.map(function (item) {
            //expandClounm的dataField会被替换为haschild
            if (item._dataField == '__haschild') {
                return item._displayField
            }
            //如果修改了，才配置发货单明细grid
            return item._dataField
        }).indexOf(field.field)
        if (existsIndex > -1) {
            return modifyColumn(existsIndex, field)
        }

        if (!options.append) {
            return
        }

        var column = {
            caption: field.title,
            dataField: field.field,
            properties: {
                visible: false,
                //新增的自定义字段，需要在商品信息、客户/供应商的列配置中，默认显示，允许用户手工配置
                allowConfig: field.visible,
                readOnly: field.readOnly,
            },
            events: {
                // dblClick: Function.createDelegate(this, this.doDblClick),
                // change: Function.createDelegate(this, this.doChange)
            },
            refresh: true
        };
        if (options.templateUse == true) {
            column.properties.reportVisible = field.visible;
        }
        // console.log(column.caption + '--------------' + column.properties.allowConfig)

        if (field.type == 1) {
            Object.assign(column, {type: Sys.UI.Controls.TextColumn});
        } else if (field.type == 2) {
            Object.assign(column, {type: Sys.UI.Controls.NumberColumn});
        } else if (field.type == 3) {
            Object.assign(column, {type: Sys.UI.Controls.DateColumn});
        }
        // else if(field.type == 4){
        //     Object.assign(column,{type: Sys.UI.Controls.DropDownColumn},
        //         {properties: {
        //             items: [{ value: 1, text: "下拉1" }, { value: 2, text: "下拉2" }, { value: 3, text: "下拉3" }],
        //             // dropDownStyle: Sys.UI.Controls.DropDownStyle.dropDownSearch,
        //             // filterType: Sys.UI.Controls.DataFilterType.range
        //         }});
        // }
        return column;
    }

    function task(data) {
        // type 1=文本，2=数值，3=日期
        var fields = []
        data.forEach(function (item) {
            var retVal = {
                type: item.dataType,
                field: item.dataField,
                title: item.displayName,
                oldTitle: item.fieldName,
                require: item.required,
                readOnly: true,
                visible: item.visible
            };
            var mapping = options.mapping[item.dataField];
            if (mapping instanceof Array) {
                for (var i in mapping) {
                    var _retVal = Object.assign({}, retVal);
                    if (mapping[i] instanceof Object) {
                        _retVal = Object.assign({}, retVal, mapping[i]);
                    } else {
                        _retVal.field = mapping[i];
                    }
                    fields.push(_retVal)
                }
            } else {
                if (mapping) {
                    if (mapping instanceof Object) {
                        retVal = Object.assign({}, retVal, mapping)
                    } else {
                        retVal.field = mapping;
                    }
                }
                fields.push(retVal)
            }
        })

        options = Object.assign({}, options, {fields: fields});

        if (typeof options.creatingFunc == 'function') {
            options.creatingFunc(options.fields);
        }

        var result = options.fields
            .map(function (item) {
                return buildColumn(item)
            })
            .filter(function (item) {
                return item
            })
            .map(function (item) {
                return self.appendColumn(item);
            })

        if (typeof options.createdFunc == 'function') {
            options.createdFunc(options.fields, result);
        }
        if (options.templateUse == true) {
            if (options.isLast == true) {
                //处理完毕之后通知打印组件 解决 bug#72834 的问题
                $notify.emitOnce(action.printNotifyName);
            }
            if (afterFunc && typeof afterFunc == 'function') {
                afterFunc();
            }
        } else {
            //处理完毕之后通知打印组件 解决 bug#72834 的问题
            $notify.emitOnce(action.printNotifyName);
        }
    }

    var reqData = {
        businessType: 1,
        subType: options.subType,
        usedTypes: (options.usedType instanceof Array ? options.usedType.join(',') : options.usedType)
    }

    $common.ajax({url: "jxc/baseinfo/customFields/list?timestamp=" +new Date().getTime(),data: reqData,router: 'ngp',success: function (res) {
            if(form.get_isDisposed()){
                return;
            }
            if (res.code == 200) {
                var data = []
                if(res && res.data) {
                    for (var key in res.data) {
                        var value = res.data[key];
                        if (value instanceof Array) {
                            data = data.concat(res.data[key]);
                        }
                    }
                    if(data.length){
                        data = data.filter(function(item){
                            var usedTypes = [];
                            usedTypes = usedTypes.concat(options.usedType)
                            return usedTypes.indexOf(item.usedType) > -1;
                        })
                        try{
                            task(data)
                        }catch(e){
                            console.error(e)
                        }
                    }
                }
            }
        }
    })
};


Sys.UI.Controls.FlowPanel.prototype.enableDynamicFieldForJarvis = function (options) {
    var self = this;
    var form = this.get_form();

    options = Object.assign({
        LabelCssClass: 'dynamicLabel',
        // Width: 120,
        labelSuffix: ':',
        append: false,
    }, options);

    var dataFields = form.get_controls()
        .filter(function (item) {
            try {
                return item.get_idPart() || item.get_dataField()
            } catch (e) {
            }
            return false;
        })
        .map(function (item) {
            var value;
            try {
                value = item.get_dataField();
            } catch (e) {
            }
            return {id: item.get_idPart(), field: value, node: item}
        });

    function modifyField(index, field) {
        var node = dataFields[index].node;
        try {
            //只有 label 显示，才会进行修改。
            if (field.title && node.get_showLabel()) {
                node.set_label(field.title + options.labelSuffix);
                node.set_requiredLabel(field.title + options.labelSuffix);
            }
        } catch (e) {
        }

        if (field.require) {
            node.set_labelCssClass((node.get_labelCssClass() || '') + ' MustCharLeft');
        } else {
            node.remove_labelCssClass('MustCharLeft');
        }
        node.set_required(field.require);
        node.set_visible(field.visible);
    }

    function buildField(field) {
        // var existsIndex = dataFields.map(function (item) {
        //     return item.field
        // }).indexOf(field.field)

        var existsIndex = -1;
        for (var i in dataFields) {
            var _id = dataFields[i].id;
            var _field = dataFields[i].field;

            if ((_field && _field == field.field) || (_id && _id == field.field)) {
                existsIndex = i;
                break;
            }
        }
        if (existsIndex > -1) {
            return modifyField(existsIndex, field);
        }

        if (!options.append) {
            return
        }

        var LabelCssClass = options.LabelCssClass || ''
        if (field.require) {
            if (LabelCssClass) {
                LabelCssClass += " MustCharLeft"
            } else {
                LabelCssClass = "MustCharLeft"
            }
        }

        var params = {
            Label: field.title + options.labelSuffix,
            Visible: field.visible,
            Width: options.Width,
            LabelCssClass: LabelCssClass,
            Required: field.require,
            DataField: field.field,
            RequiredLabel: field.title + options.labelSuffix,
            MaxLength: 200
            // OnChange: "doChange",
        };

        if (!params.Width) {
            delete params.Width
            params.CssClass = 'FlexAuto';
        }

        if (field.type === 1) {
            return $createControl('TextEdit', params, form)
        }

        if (field.type === 2) {
            return $createControl('NumberEdit', params, form)
        }

        if (field.type === 3) {
            return $createControl('DateEdit', params, form)
        }

        // if(field.type == 4){
        //     Object.assign(params,{
        //         DataValueField: "value",
        //         DataTextField: "text",
        //         DropDownStyle: "DropDownEdit",
        //         DataSource: [{ value: 1, text: "下拉1" }, { value: 2, text: "下拉2" }, { value: 3, text: "下拉3" }],
        //     });
        //     return $createControl('DropDownEdit',params, target.get_form())
        // }
    }

    function task(data) {
        var fields = data.filter(function (item) {
            if (options && options.mapping && options.mapping[item.dataField]) {
                return !(options.mapping[item.dataField] instanceof Array);
            }
            return true;
        }).map(function (item) {
            return {
                type: item.dataType,
                field: options && options.mapping && options.mapping[item.dataField] ? options.mapping[item.dataField] : item.dataField,
                title: item.displayName,
                require: item.required,
                visible: item.visible
            }
        })
        data.filter(function (item) {
            return options && options.mapping && options.mapping[item.dataField] instanceof Array;
        }).forEach(function (item) {
            options.mapping[item.dataField].filter(function (f) {
                return f
            }).forEach(function (f) {
                fields.push({
                    type: item.dataType,
                    field: f,
                    title: item.displayName,
                    require: item.required,
                    visible: item.visible
                })
            })
        })
        options = Object.assign({}, options, {fields: fields});

        if (typeof options.creatingFunc == 'function') {
            options.creatingFunc(options.fields);
        }

        var result = options.fields
            .map(function (item) {
                return buildField(item)
            })
            .filter(function (item) {
                return item
            })
            .map(function (item) {
                return self.appendUIControl(item)
            });

        form.dataBind(form.get_dataSource())
        if (typeof options.createdFunc == 'function') {
            options.createdFunc(options.fields, result);
        }
    }

    var reqData = {
        businessType: 1,
        subType: options.subType,
        usedTypes: (options.usedType instanceof Array ? options.usedType.join(',') : options.usedType)
    }
    $common.ajax({url: "jxc/baseinfo/customFields/list?timestamp=" +new Date().getTime(),data: reqData,router: 'ngp',success: function (res) {
            if(form.get_isDisposed()){
                return;
            }
            if (res.code == 200) {
                var data = []
                if(res && res.data) {
                    for (var key in res.data) {
                        var value = res.data[key];
                        if (value instanceof Array) {
                            data = data.concat(res.data[key]);
                        }
                    }
                    if(data.length){
                        data = data.filter(function(item){
                            var usedTypes = [];
                            usedTypes = usedTypes.concat(options.usedType)
                            return usedTypes.indexOf(item.usedType) > -1;
                        })
                        try{
                            task(data)
                        }catch(e){
                            console.error(e)
                        }
                    }
                }
            }
        }
    })
};

Sys.UI.Controls.FlowPanel.prototype.enableDynamicFieldForJarvis = function (options) {
    var self = this;
    var form = this.get_form();

    options = Object.assign({
        LabelCssClass: 'dynamicLabel',
        // Width: 120,
        labelSuffix: ':',
        append: false,
    }, options);

    var dataFields = form.get_controls()
        .filter(function (item) {
            try {
                return item.get_idPart() || item.get_dataField()
            } catch (e) {
            }
            return false;
        })
        .map(function (item) {
            var value;
            try {
                value = item.get_dataField();
            } catch (e) {
            }
            return {id: item.get_idPart(), field: value, node: item}
        });

    function modifyField(index, field) {
        var node = dataFields[index].node;
        try{
            //只有 label 显示，才会进行修改。
            if (field.title && node.get_showLabel()) {
                node.set_label(field.title + options.labelSuffix);
                node.set_requiredLabel(field.title + options.labelSuffix);
            }
        }catch(e){
        }

        if(field.require){
            node.set_labelCssClass((node.get_labelCssClass() || '') + ' MustCharLeft');
        }else{
            node.remove_labelCssClass('MustCharLeft');
        }
        node.set_required(field.require);
        node.set_visible(field.visible);
    }

    function buildField(field) {
        // var existsIndex = dataFields.map(function (item) {
        //     return item.field
        // }).indexOf(field.field)

        var existsIndex = -1;
        for(var i in dataFields){
            var _id = dataFields[i].id;
            var _field = dataFields[i].field;

            if((_field && _field == field.field) || (_id && _id == field.field)){
                existsIndex = i;
                break;
            }
        }
        if (existsIndex > -1) {
            return modifyField(existsIndex, field);
        }

        if(!options.append){
            return
        }

        var LabelCssClass = options.LabelCssClass || ''
        if(field.require){
            if(LabelCssClass){
                LabelCssClass += " MustCharLeft"
            }else{
                LabelCssClass = "MustCharLeft"
            }
        }

        var params = {
            Label: field.title + options.labelSuffix,
            Visible: field.visible,
            Width:  options.Width,
            LabelCssClass: LabelCssClass,
            Required: field.require,
            DataField: field.field,
            RequiredLabel: field.title + options.labelSuffix,
            MaxLength: 200
            // OnChange: "doChange",
        };

        if(!params.Width){
            delete params.Width
            params.CssClass = 'FlexAuto';
        }

        if (field.type == 1) {
            return $createControl('TextEdit', params, form)
        }

        if (field.type == 2) {
            return $createControl('NumberEdit', params, form)
        }

        if (field.type == 3) {
            return $createControl('DateEdit', params, form)
        }

        // if(field.type == 4){
        //     Object.assign(params,{
        //         DataValueField: "value",
        //         DataTextField: "text",
        //         DropDownStyle: "DropDownEdit",
        //         DataSource: [{ value: 1, text: "下拉1" }, { value: 2, text: "下拉2" }, { value: 3, text: "下拉3" }],
        //     });
        //     return $createControl('DropDownEdit',params, target.get_form())
        // }
    }

    function task(data) {
        var fields = data.filter(function(item){
            if(options && options.mapping && options.mapping[item.dataField]){
                return !(options.mapping[item.dataField] instanceof Array);
            }
            return true;
        }).map(function (item) {
            return {
                type: item.dataType,
                field: options && options.mapping && options.mapping[item.dataField] ? options.mapping[item.dataField] : item.dataField,
                title: item.displayName,
                require: item.required,
                visible: item.visible
            }
        })
        data.filter(function(item) {
            return options && options.mapping && options.mapping[item.dataField] instanceof Array;
        }).forEach(function(item){
            options.mapping[item.dataField].filter(function(f) {return f}).forEach(function(f){
                fields.push({
                    type: item.dataType,
                    field: f,
                    title: item.displayName,
                    require: item.required,
                    visible: item.visible
                })
            })
        })
        options = Object.assign({},options,{fields: fields});

        if (typeof options.creatingFunc == 'function') {
            options.creatingFunc(options.fields);
        }

        var result = options.fields
            .map(function (item) {
                return buildField(item)
            })
            .filter(function (item) {
                return item
            })
            .map(function (item) {
                return self.appendUIControl(item)
            });

        form.dataBind(form.get_dataSource())
        if (typeof options.createdFunc == 'function') {
            options.createdFunc(options.fields, result);
        }
    }
    var reqData = {
        businessType: 1,
        subType: options.subType,
        usedTypes: (options.usedType instanceof Array ? options.usedType.join(',') : options.usedType)
    }

    $common.ajax({url: "jxc/baseinfo/customFields/list?timestamp=" +new Date().getTime(),data: reqData,router: 'ngp',success: function (res) {
            if(form.get_isDisposed()){
                return;
            }
            if (res.code == 200) {
                var data = []
                if(res && res.data){
                    for(var key in res.data){
                        var value = res.data[key];
                        if(value instanceof Array){
                            data = data.concat(res.data[key]);
                        }
                    }
                    if(data.length){
                        data = data.filter(function(item){
                            var usedTypes = [];
                            usedTypes = usedTypes.concat(options.usedType)
                            return usedTypes.indexOf(item.usedType) > -1;
                        })
                        try{
                            task(data)
                        }catch(e){
                            console.error(e)
                        }
                    }
                }
            }
        },error: function(){},async: true});


}
