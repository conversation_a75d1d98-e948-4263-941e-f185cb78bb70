$jarvis.register.commonContrl('popupArea', {
    controlInfo: function () {
        this.CurrentCountry = 'China';
        this.popupId = '__popupArea';
        this.treeId = '__areaTree';
        this.splitChar = '/';

        var that = this;
        if (!$ms.ngpConfig.Area) {
            $ms.ngpConfig.setConfig('Area.China', 'shell/config/getChinaArea', {}, {}, function (data) {
                that.areaData = $ms.ngpConfig.Area[that.CurrentCountry];
            });
        } else {
            that.areaData = $ms.ngpConfig.Area[that.CurrentCountry];
        }

        if (this.control.get_grid) {
            return {
                property: {
                    //readOnly:true, // popupArea当在grid下面不能只读，只读了就不能触发OnEditorShow事件了
                    allowDelIcon: true /*强制只读的编辑框也显示清空按钮，但不可输入*/
                },
                events: {
                    "OnEditorShow": this.doEditorShow
                }
            }
        } else {
            return {
                property: {
                    readOnly: true,
                    allowDelIcon: true
                },
                events: {
                    "OnFocus": this.doEditorShow
                }
            }
        }
    },
    doEditorShow: function (sender) {
        this._createPopup();
        this._bindValue(sender);
        this.get_form().popupSender = sender; // 同一个界面不同控件绑定的同一个doSelected
        this._getPopup().popupAt(this._getPopupAt());
    },
    doSelected: function (sender) {
        var node = sender.get_selectedNode();
        if (node.hasChildren()) {
            return this._getPopup().repos(); // 面板内部发生改变，重新计算位置
        }
        this.hide();

        var nodes = sender.get_selectedNodes();
        var textArr = [];
        Array.forEach(nodes, function (node) {
            textArr.push(node.get_text()); // 遍历选择节点，获取节点数据或文本。或其他操作；
        });
        var newText = textArr.join(this.splitChar);

        var control = this.get_form().popupSender; // 这里一定不能用this.control, 因为popup是公用的，它关联的业务控件和control可能已经不存在了
        this.set_text(newText, control);
        if (control.doChange) {
            control.doChange();
        }
    },
    _bindValue: function (sender) {
        var text = '';
        if (sender.get_grid) {
            var grid = sender.get_grid();
            var rowData = grid.get_selectedRowData();
            if (rowData && this.control.get_dataField()) {
                text = rowData[this.control.get_dataField()] || '';
            }
            sender.set_hasPopup(true);
        } else {
            text = sender.get_text();
        }

        if (text && text[text.length - 1] == this.splitChar) {
            text = text.substr(0, text.length - 1);
        }
        var arr = [''];
        if (text) {
            arr = text.split(this.splitChar);
        } else if (this.addressText) {
            arr = this.addressText.split(this.splitChar);
        }
        var lastText = arr[arr.length - 1];

        var form = this.get_form();
        var tree = form[this.treeId];
        if (!lastText) {
            tree.clearSelect(true);
        } else {
            //tree.locateNode('name', lastText); // 选中最后一个节点，自动级联选择上级(四川省/成都市/高新区，陕西省/长治市/高新区....太多高新区了)

            for (var i = arr.length - 1; i >= 0; i--) {
                lastText = arr[i];
                var node = tree.locateNode(function (node, v) {
                    var text = node.get_text();
                    if (text == lastText) {
                        var pNode = node.get_parent();
                        var level = pNode.get_level();
                        if (level > 0 && pNode) {
                            var pText = arr[level];
                            if (pText == pNode.get_text()) {
                                return true;
                            }
                        } else {
                            return true;
                        }
                    }
                    return false;
                }, lastText);
                if (node) break;
            }
        }
    },
    _getPopup: function () {
        var form = this.get_form();
        var popup = form[this.popupId];
        return popup;
    },
    _getPopupAt: function () {
        var target = this.control;
        if (target.get_grid) {
            target = target.get_grid().get_activeCell();
        }
        return target;
    },
    _createPopup: function () {
        var form = this.get_form();
        if (form[this.popupId]) return;

        var pb = $createControl('PopupBlock', {
            ID: this.popupId,
            CssClass: 'AreaPopup'
        }, form);

        $createControl('TreeView', {
            ID: this.treeId,
            OnTreeNodeClick: this.bindControlEvent('doSelected'),
            ShowColumns: true,
            DataSource: $common.toTreeData(this.areaData, 'name', 'id', 'parent_id', 1),
        }, form, pb);
        pb.appendTo(document.body);
    },
    _bindData: function (province, city, district, street, noBindText) {
        if (province == undefined) return;
        this.addressText = this._makeAddress(province, city, district, street);
        if (!noBindText) {
            this._bindText(this.addressText);
        }
    },
    _bindText: function (address) {
        this.hide();
        this.set_text(address);
    },
    set_text: function (address, control) {
        var c = control || this.control;
        if (c.get_grid) {
            var grid = c.get_grid();
            var rowData = grid.get_selectedRowData();
            if (c.get_dataField()) {
                rowData = rowData ? rowData : {};
                if (rowData[c.get_dataField()] != address) {
                    c._changed = true;
                    c._control.set_value(address);
                    c._hideEditor();
                }
            }
        } else {
            c.set_text(address);
        }
    },
    hide: function () {
        var form = this.get_form();
        if (form[this.popupId]) {
            form[this.popupId].hide();
        }
    },
    clear: function () {
        this.addressText = '';
    },
    addressChange: function (selectedData) {
        var text = this._makeAddress(selectedData.province, selectedData.city, selectedData.district);
        this.control.set_text(text);
    },
    _makeAddress: function (province, city, district, street) {
        var arr = [];
        if (province != undefined) arr.push(province);
        if (city != undefined) arr.push(city);
        if (district != undefined) arr.push(district);
        if (street != undefined) arr.push(street);
        var address = district == undefined ? "" : arr.join(this.splitChar);
        if (address.length > 0 && address.charAt(address.length - 1) == "/") {
            address = address.substring(0, address.length - 1);
        }
        return address;
    },
    get_value: function () {
        var text = this.control.get_value();
        var arr = text.split(this.splitChar);
        var rt = {
            Province: '',
            City: '',
            District: '',
            Street: ''
        }
        switch (arr.length) {
            case 1:
                rt.Province = arr[0];
                break;
            case 2:
                rt.Province = arr[0];
                rt.City = arr[1];
                break;
            case 3:
                rt.Province = arr[0];
                rt.City = arr[1];
                rt.District = arr[2];
                break;
            case 4:
                rt.Province = arr[0];
                rt.City = arr[1];
                rt.District = arr[2];
                rt.Street = arr[3];
                break;
        }
        return rt;
    }
});