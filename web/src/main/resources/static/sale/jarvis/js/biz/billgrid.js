$jarvis.register.commonContrl('billgrid', {

    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    validation: function (bill) {
        //根据不同的单据类型 执行不同的验证逻辑
        var  vchtype = bill.vchtype;
        // 不在这判断
        return true;
    },

    _checkNull: function(bill, erroMsg) {
        if (bill.detail.length == 0) {
            erroMsg = "请录入商品明细！";
            $recordsheet._alertMsg(erroMsg);
        }
        return true;
    },

    refresh: function () {
        this.control.dataBind(null);
    }

});