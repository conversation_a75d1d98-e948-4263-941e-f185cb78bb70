/**
 * 商家单品优惠
 */
$jarvis.register.columnControl('sellerPtypePreferentialTotalColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalTotal ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalTotal
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var sellerPtypePreferentialTotal = rowData.sellerPtypePreferentialTotal ? rowData.sellerPtypePreferentialTotal : 0;
        rowData.sellerPtypePreferentialTotal = sellerPtypePreferentialTotal;
    },

    sellerPtypePreferentialTotalChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                // 1、折后含税金额=平台金额-商家单品优惠-商家整单优惠分摊
                // 2、折后含税单价=折后含税金额/数量
                // 3、折后不含税金额=折后含税金额/(1+税率)
                // 4、折后不含税单价=折后不含税金额/数量
                // 5、税额=折后含税金额-折后不含税金额
                // 备注 这里的3、4、5 可以合并成一条 即 税额=折后含税金额-（折后含税金额/(1+税率)）
                resMaps[gridId + '.disedTaxedTotal'] = 'currencyDisedTaxedTotalValueChangeByDiscount';
                resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceValueChange';
                resMaps[gridId + '.taxTotal'] = 'currencyTaxTotalValueChangeByRate';//(4\5\6\)
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },
    sellerPtypePreferentialTotalValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
               // 商家单品优惠 = 平台金额-折后含税金额-商家整单优惠分摊
               newData.rowData.sellerPtypePreferentialTotal = $sale.$jarvisdetail._getSellerPtypePreferentialTotal(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                this._limitData(newData);
            }
        }
    },

});