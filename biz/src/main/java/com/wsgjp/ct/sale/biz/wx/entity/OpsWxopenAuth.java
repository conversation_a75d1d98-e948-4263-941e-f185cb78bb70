package com.wsgjp.ct.sale.biz.wx.entity;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * 开放平台授权信息表
 * @TableName ops_wxopen_auth
 */
public class OpsWxopenAuth implements Serializable {
    /**
     * 主键
     */
    private BigInteger id;

    /**
     * 账套id
     */
    private BigInteger profileId;

    /**
     * 公众号/小程序 appId
     */
    private String appId;

    /**
     * 授权类型：0公众号；(其他预留)
     */
    private Integer appType;

    /**
     * 公众号/小程序 refreshToken
     */
    private String refreshToken;

    /**
     * 授权状态：0取消授权、1已授权
     */
    private Boolean authStatus;

    /**
     * 自动同步状态：0未开启、1已开启
     */
    private Boolean autoSync;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Boolean getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(Boolean authStatus) {
        this.authStatus = authStatus;
    }

    public Boolean getAutoSync() {
        return autoSync;
    }

    public void setAutoSync(Boolean autoSync) {
        this.autoSync = autoSync;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}