package com.wsgjp.ct.sale.biz.wx.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import me.chanjar.weixin.mp.bean.card.Abstract;
import me.chanjar.weixin.mp.bean.card.TextImageList;
import me.chanjar.weixin.mp.bean.card.TimeLimit;
import me.chanjar.weixin.mp.bean.card.UseCondition;

import java.util.List;

public class WxAdvancedInfo {

    @JsonProperty("use_condition")
    private UseCondition useCondition;
    @JsonProperty("abstract")
    private Abstract abstractInfo;
    @JsonProperty("text_image_list")
    private List<TextImageList> textImageList;
    @JsonProperty("business_service")
    private List<String> businessServiceList;
    @JsonProperty("time_limit")
    private List<TimeLimit> timeLimits;
    @JsonProperty("share_friends")
    private Boolean shareFriends;

    @JsonProperty("consume_share_card_list")
    private List<String> consumeShareCardList;

    public UseCondition getUseCondition() {
        return useCondition;
    }

    public void setUseCondition(UseCondition useCondition) {
        this.useCondition = useCondition;
    }

    public Abstract getAbstractInfo() {
        return abstractInfo;
    }

    public void setAbstractInfo(Abstract abstractInfo) {
        this.abstractInfo = abstractInfo;
    }

    public List<TextImageList> getTextImageList() {
        return textImageList;
    }

    public void setTextImageList(List<TextImageList> textImageList) {
        this.textImageList = textImageList;
    }

    public List<String> getBusinessServiceList() {
        return businessServiceList;
    }

    public void setBusinessServiceList(List<String> businessServiceList) {
        this.businessServiceList = businessServiceList;
    }

    public List<TimeLimit> getTimeLimits() {
        return timeLimits;
    }

    public void setTimeLimits(List<TimeLimit> timeLimits) {
        this.timeLimits = timeLimits;
    }

    public Boolean getShareFriends() {
        return shareFriends;
    }

    public void setShareFriends(Boolean shareFriends) {
        this.shareFriends = shareFriends;
    }

    public List<String> getConsumeShareCardList() {
        return consumeShareCardList;
    }

    public void setConsumeShareCardList(List<String> consumeShareCardList) {
        this.consumeShareCardList = consumeShareCardList;
    }
}
