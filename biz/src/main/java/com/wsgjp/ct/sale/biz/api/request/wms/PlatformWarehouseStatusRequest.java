package com.wsgjp.ct.sale.biz.api.request.wms;

import java.math.BigInteger;
import java.util.List;

public class PlatformWarehouseStatusRequest {
    private List<BigInteger> vchcodes;
    private List<BigInteger> warehouseTaskIds;
    private List<BigInteger> eshopOrderIds;
    private PlatformWarehouseStatusEnum statusEnum;

    public List<BigInteger> getEshopOrderIds() {
        return eshopOrderIds;
    }

    public void setEshopOrderIds(List<BigInteger> eshopOrderIds) {
        this.eshopOrderIds = eshopOrderIds;
    }

    public List<BigInteger> getVchcodes() {
        return vchcodes;
    }

    public void setVchcodes(List<BigInteger> vchcodes) {
        this.vchcodes = vchcodes;
    }

    public PlatformWarehouseStatusEnum getStatusEnum() {
        return statusEnum;
    }

    public void setStatusEnum(PlatformWarehouseStatusEnum statusEnum) {
        this.statusEnum = statusEnum;
    }

    public List<BigInteger> getWarehouseTaskIds() {
        return warehouseTaskIds;
    }

    public void setWarehouseTaskIds(List<BigInteger> warehouseTaskIds) {
        this.warehouseTaskIds = warehouseTaskIds;
    }
}
