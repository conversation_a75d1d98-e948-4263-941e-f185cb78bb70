package com.wsgjp.ct.sale.biz.api.response;

import com.wsgjp.ct.sale.biz.jarvis.utils.cache.entity.CacheEntity;

/**
 * @Description TODO
 * @Date 2021-03-13 14:37
 * @Created by lingxue
 */
public class AddressMapping extends CacheEntity<AddressMapping> {
    private int shopType;
    private String onlineProvince;
    private String onlineCity;
    private String onlineDistrict;
    private String onlineTown;
    private String localProvince;
    private String localCity;
    private String localDistrict;
    private String localTown;

    public int getShopType() {
        return shopType;
    }

    public void setShopType(int shopType) {
        this.shopType = shopType;
    }

    public String getOnlineProvince() {
        return onlineProvince;
    }

    public void setOnlineProvince(String onlineProvince) {
        this.onlineProvince = onlineProvince;
    }

    public String getOnlineCity() {
        return onlineCity;
    }

    public void setOnlineCity(String onlineCity) {
        this.onlineCity = onlineCity;
    }

    public String getOnlineDistrict() {
        return onlineDistrict;
    }

    public void setOnlineDistrict(String onlineDistrict) {
        this.onlineDistrict = onlineDistrict;
    }

    public String getOnlineTown() {
        return onlineTown;
    }

    public void setOnlineTown(String onlineTown) {
        this.onlineTown = onlineTown;
    }

    public String getLocalProvince() {
        return localProvince;
    }

    public void setLocalProvince(String localProvince) {
        this.localProvince = localProvince;
    }

    public String getLocalCity() {
        return localCity;
    }

    public void setLocalCity(String localCity) {
        this.localCity = localCity;
    }

    public String getLocalDistrict() {
        return localDistrict;
    }

    public void setLocalDistrict(String localDistrict) {
        this.localDistrict = localDistrict;
    }

    public String getLocalTown() {
        return localTown;
    }

    public void setLocalTown(String localTown) {
        this.localTown = localTown;
    }
}
