package com.wsgjp.ct.sale.biz.wx.common;


import com.wsgjp.ct.sale.biz.member.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

public enum WxTemplateMessageEnum {

    /**
     * 充值成功
     */
    SUCCESSFUL_RECHARGE("47067", "储值成功提醒", "储值卡号", "character_string1", "储值金额", "amount4", "赠送金额", "amount6", "当前余额", "amount5", "变动时间", "time10"),

    /**
     * 消费成功
     */
    SUCCESSFUL_CONSUMPTION("45789", "会员消费成功通知", "卡号", "character_string15", "交易金额", "amount4", "消费时间", "time11", "账户余额", "amount5", "积分余额", "number6");

    /**
     * 模板消息code
     */
    private final String code;

    /**
     * 标题
     */
    private final String title;

    private final String valueName1;

    private final String value1;

    private final String valueName2;

    private final String value2;

    private final String valueName3;

    private final String value3;

    private final String valueName4;

    private final String value4;

    private final String valueName5;

    private final String value5;

    public String getCode() {
        return code;
    }

    public String getTitle() {
        return title;
    }

    public String getValueName1() {
        return valueName1;
    }

    public String getValue1() {
        return value1;
    }

    public String getValueName2() {
        return valueName2;
    }

    public String getValue2() {
        return value2;
    }

    public String getValueName3() {
        return valueName3;
    }

    public String getValue3() {
        return value3;
    }

    public String getValueName4() {
        return valueName4;
    }

    public String getValue4() {
        return value4;
    }

    public String getValueName5() {
        return valueName5;
    }

    public String getValue5() {
        return value5;
    }

    WxTemplateMessageEnum(String code, String title, String valueName1, String value1, String valueName2, String value2, String valueName3, String value3, String valueName4, String value4, String valueName5, String value5) {
        this.code = code;
        this.title = title;
        this.valueName1 = valueName1;
        this.value1 = value1;
        this.valueName2 = valueName2;
        this.value2 = value2;
        this.valueName3 = valueName3;
        this.value3 = value3;
        this.valueName4 = valueName4;
        this.value4 = value4;
        this.valueName5 = valueName5;
        this.value5 = value5;
    }

    public static List<String> getValueNameList(WxTemplateMessageEnum templateMessageEnum) {
        List<String> list = new ArrayList<>();
        String valueName1 = templateMessageEnum.getValueName1();
        list.add(valueName1);
        String valueName2 = templateMessageEnum.getValueName2();
        list.add(valueName2);
        String valueName3 = templateMessageEnum.getValueName3();
        list.add(valueName3);
        if (StringUtils.isNotBlank(templateMessageEnum.getValueName4())) {
            String valueName4 = templateMessageEnum.getValueName4();
            list.add(valueName4);
        }
        if (StringUtils.isNotBlank(templateMessageEnum.getValueName5())) {
            String valueName5 = templateMessageEnum.getValueName5();
            list.add(valueName5);
        }
        return list;
    }

}
