package com.wsgjp.ct.sale.biz.bill.service;

//import com.wsgjp.ct.bill.core.handle.entity.enums.PayStateEnum;

import com.wsgjp.ct.sale.biz.bill.model.entity.BillLoadRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.BillSaveResultDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsBillDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PosBill;

import java.math.BigInteger;

public interface BillInterface {
    /**
     * 提交库存类单据
     * @param bill
     * @return
     */
    BillSaveResultDTO submitBill(GoodsBillDTO bill, PosBill request);

    BillSaveResultDTO validationGoodsBill(GoodsBillDTO bill);

    GoodsBillDTO getBillByVchcode(BillLoadRequest request) throws Exception;

    void updateBillPayState(GoodsBillDTO bill);

    void cancelBill(BigInteger vchcode);
}
