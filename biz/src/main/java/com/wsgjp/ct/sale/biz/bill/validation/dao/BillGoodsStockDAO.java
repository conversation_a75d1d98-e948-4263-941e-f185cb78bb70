package com.wsgjp.ct.sale.biz.bill.validation.dao;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

public class BillGoodsStockDAO {

    /**
     * 仓库
     */
    private BigInteger ktypeId;
    /**
     * 明细Id
     */
    private BigInteger detailId;

    /**
     * 商品名
     */
    private String kfullname;

    /**
     * 商品
     */
    private BigInteger ptypeId;

    /**
     * 商品
     */
    private BigInteger skuId;

    /**
     * 属性名称
     */
    private String skuName;

    public String getFullbarcode() {
        return fullbarcode;
    }

    public void setFullbarcode(String fullbarcode) {
        this.fullbarcode = fullbarcode;
    }

    /**
     * 商品名
     */
    private String pfullname;
    /**
     * 条码
     */
    private String fullbarcode;

    private String ptypeUserCode;

    /**
     * 单据数量
     */
    private BigDecimal qty;

    /**
     * 单据辅助单位数量
     */
    private BigDecimal unitQty;


    /**
     * 单位转换率
     */
    private BigDecimal unitRate;


    /**
     * 成本均价
     */
    private BigDecimal price;

    /**
     * 成本金额
     */
    private BigDecimal total;
    /**
     * 是否启用属性管理
     */
    private Boolean propEnabled;

    public BigInteger getDetailId() {
        return detailId;
    }

    public void setDetailId(BigInteger detailId) {
        this.detailId = detailId;
    }

    public Integer getProtectDays() {
        return protectDays;
    }

    public void setProtectDays(Integer protectDays) {
        this.protectDays = protectDays;
    }

    /**
     * 是否启用保质期
     */
    private Boolean batchEnabled;
    private Date produceDate;
    private Date expireDate;
    private Integer protectDays;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 批次计价 成本ID
     */
    private BigInteger costId;
    /**
     * 批次单价
     */
    private BigDecimal batchPrice;
    /**
     * 存货点id
     */
    private BigInteger ktypePointId;
    /**
     * 存货点类型
     */
    private Integer ktypePointType;

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public BigInteger getCostId() {
        return costId;
    }

    public void setCostId(BigInteger costId) {
        this.costId = costId;
    }

    private Integer costMode;

    public Integer getCostMode() {
        return costMode;
    }

    public void setCostMode(Integer costMode) {
        this.costMode = costMode;
    }

    public BillGoodsStockDAO() {
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public String getKfullname() {
        return kfullname;
    }

    public void setKfullname(String kfullname) {
        this.kfullname = kfullname;
    }

    public BigInteger getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public String getPfullname() {
        return pfullname;
    }

    public void setPfullname(String pfullname) {
        this.pfullname = pfullname;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getUnitQty() {
        return unitQty;
    }

    public void setUnitQty(BigDecimal unitQty) {
        this.unitQty = unitQty;
    }

    public BigDecimal getUnitRate() {
        return unitRate;
    }

    public void setUnitRate(BigDecimal unitRate) {
        this.unitRate = unitRate;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }


    public Boolean getBatchEnabled() {
        return batchEnabled;
    }

    public void setBatchEnabled(Boolean batchEnabled) {
        this.batchEnabled = batchEnabled;
    }

    public Boolean getPropEnabled() {
        return propEnabled;
    }

    public void setPropEnabled(Boolean propEnabled) {
        this.propEnabled = propEnabled;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public Date getProduceDate() {
        return produceDate;
    }

    public void setProduceDate(Date produceDate) {
        this.produceDate = produceDate;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public String getPtypeUserCode() {
        return ptypeUserCode;
    }

    public void setPtypeUserCode(String ptypeUserCode) {
        this.ptypeUserCode = ptypeUserCode;
    }

    public BigDecimal getBatchPrice() {
        return batchPrice;
    }

    public void setBatchPrice(BigDecimal batchPrice) {
        this.batchPrice = batchPrice;
    }

    public BigInteger getKtypePointId() {
        return ktypePointId;
    }

    public void setKtypePointId(BigInteger ktypePointId) {
        this.ktypePointId = ktypePointId;
    }

    public Integer getKtypePointType() {
        return ktypePointType;
    }

    public void setKtypePointType(Integer ktypePointType) {
        this.ktypePointType = ktypePointType;
    }
}
