package com.wsgjp.ct.sale.biz.bill.validation.validator;

import com.wsgjp.ct.sale.biz.bill.model.entity.ValidationActionParam;
import com.wsgjp.ct.sale.biz.bill.utils.VerifyUtils;
import com.wsgjp.ct.sale.biz.bill.validation.BaseValidator;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.AbstractBillDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsBillDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsDetailDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * @Author: lailai
 * @Date: 2020-07-09 17:37
 */
@Service
public class BaseGoodBillValidator extends BaseInfoValidator implements BaseValidator<GoodsBillDTO> {



    @Override
    public <TBill extends AbstractBillDTO> void checkBill(TBill bill, ValidationActionParam validationParam) {
        GoodsBillDTO goodsBillDTO = (GoodsBillDTO) bill;
        List<GoodsDetailDTO> detailDTOS = buildDetails(bill);
        // 检验套餐
        List<GoodsDetailDTO> comboRows = detailDTOS.stream().filter(e -> e.getComboRow()).filter(distinctByKey(e -> e.getPtypeId())).collect(Collectors.toList());
        if (comboRows != null && comboRows.size() > 0) {
            for (GoodsDetailDTO comboRow : comboRows) {
                checkComBo(comboRow.getPtypeId(), validationParam, comboRow.getpFullName());
            }
        }
        //检查商品
        List<GoodsDetailDTO> ptypes = detailDTOS.stream().filter(e -> e.getComboRow() == false).collect(Collectors.toList());
        for (GoodsDetailDTO detailDTO : ptypes) {
            checkPtype(detailDTO.getPtypeId(), validationParam, detailDTO.getpFullName());
            List<String> errList = VerifyUtils.verify(detailDTO);
            if (errList.size() > 0) {
                int rowIndex = detailDTO.get__rowIndex() + 1;
                String error = StringUtils.isEmpty(detailDTO.getpFullName()) ? String.format("第【%s】行商品异常：", rowIndex) : String.format("第【%s】行商品【%s】异常：", rowIndex,detailDTO.getpFullName());
                error += StringUtils.join(errList, "、") + "小数位与系统配置不符";
                validationParam.writeErrorInfo(BillValidationContext.createErrorWithMessage(error));
            }
        }
    }

    @Override
    public void validatorAction(ValidationActionParam validationParam, GoodsBillDTO bill) throws Exception {
        if (validationEnable()) {
            checkNomalInfo(validationParam, bill);
        }
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t ->
                seen.putIfAbsent(
                        keyExtractor.apply(t),
                        Boolean.TRUE) == null;
    }



}
