package com.wsgjp.ct.sale.biz.api.request.eprint;

import java.math.BigInteger;
import java.util.List;

public class GetPrintStyleNewRequest {
    /**
     * 物流模板类型
     */
    private String companyType;
    /**
     * 物流公司编码
     */
    private String expressType;
    /**
     * 条码类型
     */
    private String barcodeType;
    /**
     * 单据样式大类
     */
    private String templateTypeEnum;
    /**
     * 单据样式小类
     */
    private String templateType;
    /**
     * 样式ID
     */
    private List<BigInteger> templateIdList ;


    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getExpressType() {
        return expressType;
    }

    public void setExpressType(String expressType) {
        this.expressType = expressType;
    }

    public String getBarcodeType() {
        return barcodeType;
    }

    public void setBarcodeType(String barcodeType) {
        this.barcodeType = barcodeType;
    }

    public String getTemplateTypeEnum() {
        return templateTypeEnum;
    }

    public void setTemplateTypeEnum(String templateTypeEnum) {
        this.templateTypeEnum = templateTypeEnum;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public List<BigInteger> getTemplateIdList() {
        return templateIdList;
    }

    public void setTemplateIdList(List<BigInteger> templateIdList) {
        this.templateIdList = templateIdList;
    }
}
