package com.wsgjp.ct.sale.biz.wx.common;

import bf.datasource.typehandler.CodeEnum;

public enum WxTemplateCodeTypeEnum implements CodeEnum {

    CODE_TYPE_TEXT(0, "文本", "CODE_TYPE_TEXT"),
    CODE_TYPE_BARCODE(1,"条形码", "CODE_TYPE_BARCODE"),
    CODE_TYPE_QRCODE(2,"二维码", "CODE_TYPE_QRCODE");


    private Integer codeType;
    private final String name;
    private final String wxType;

    public Integer getCodeType() {
        return codeType;
    }

    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    @Override
    public int getCode() {
        return this.getCodeType();
    }

    public String getName() {
        return name;
    }

    public String getWxType() {
        return wxType;
    }

    WxTemplateCodeTypeEnum(Integer codeType, String name, String wxType) {
        this.codeType = codeType;
        this.name = name;
        this.wxType = wxType;
    }

}
