package com.wsgjp.ct.sale.biz.bill.service.impl;


import com.wsgjp.ct.bill.core.handle.entity.enums.BillPostState;
import com.wsgjp.ct.bill.core.handle.entity.enums.PayStateEnum;
import com.wsgjp.ct.bill.core.handle.entity.enums.Vchtypes;
import com.wsgjp.ct.framework.enums.AssertsChangeType;
import com.wsgjp.ct.sale.biz.bill.service.VipAssertsService;
import com.wsgjp.ct.sale.biz.member.common.ScoreStrategyType;
import com.wsgjp.ct.sale.biz.member.mapper.SsPreferentialBillMapper;
import com.wsgjp.ct.sale.biz.member.mapper.SsPreferentialGoodsDetailMapper;
import com.wsgjp.ct.sale.biz.member.model.entity.preferential.SsPreferentialBill;
import com.wsgjp.ct.sale.biz.member.model.entity.preferential.SsPreferentialGoodsDetail;
import com.wsgjp.ct.sale.biz.member.model.entity.recharge.SsVipAssertsServicePtype;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.SsVip;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.SsVipBill;
import com.wsgjp.ct.sale.biz.member.service.ISsCardService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipAssertsChangeService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipLevelService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipService;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsBillDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsDetailDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.VipAssert;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PosBill;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.preferential.PreferentialDto;
import com.wsgjp.ct.sale.sdk.pos.biz.MemberAssertsChangeService;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssert;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssertsChange;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.VipAsserts;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */

@Service

public class VipAssertsServiceImpl implements VipAssertsService {

    private static final Logger logger = LoggerFactory.getLogger(VipAssertsServiceImpl.class);

    @Autowired
    private ISsVipService vipService;

    @Autowired
    private ISsVipAssertsChangeService vipAssertsChangeService;

    @Autowired
    private MemberAssertsChangeService memberAssertsChangeService;

    @Autowired
    private ISsCardService cardService;

    @Autowired
    private ISsVipLevelService vipLevelService;

    @Autowired
    private SsPreferentialBillMapper preferentialBillMapper;

    @Autowired
    private SsPreferentialGoodsDetailMapper preferentialGoodsDetailMapper;

    @Override
    public void saveVipAsserts(PosBill request) {
        // 保存会员资产
        afterSubmitSaleBillAsset(request);
    }

    private void afterSubmitSaleBillAsset(PosBill request) {
        BigInteger vipId = request.getGoodsBill().getVipCardId();
        GoodsBillDTO goodsBill = request.getGoodsBill();
        boolean saveVipAsserts = request.getSaveVipAsserts();

        if (BillPostState.UNCONFIRMED != goodsBill.getPostState() && saveVipAsserts) {
            // 校验该会员是否存在
            SsVip vip = vipService.selectVipById(vipId);
            if (vip == null || vip.getId() == null) {
                throw new RuntimeException("会员" + vipId + "不存在");
            }
            MemberAssertsChange dto = new MemberAssertsChange();
            // 整单实付金额
            // 负值换货单要整单实付金额 - 赠金
            BigDecimal payMoney = goodsBill.getCurrencyBillTotal().add(goodsBill.getCurrencyGivePreferentialTotal() == null ?
                    BigDecimal.ZERO :
                    goodsBill.getCurrencyGivePreferentialTotal());
            //可以赠送积分的金额
            BigDecimal giveScoreMoney = goodsBill.getGiveScoreMoney();

            // 出库单、换货单要计算赠送积分、成长值
            doCompute(giveScoreMoney, vipId, goodsBill);

            List<VipAsserts> vipAsserts = new ArrayList<>();
            VipAsserts vipAssert = new VipAsserts();
            vipAssert.setVipId(vipId);
            vipAssert.setMoney(payMoney);
            List<MemberAssert> memberAssertList = Optional.ofNullable(goodsBill.getVipAsserts())
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .map(anAssert -> MemberAssert.createData(
                            anAssert.getTyped(),
                            anAssert.getQty(),
                            anAssert.getMemo(),
                            anAssert.getAssertId(),
                            anAssert.getCardTemplateId(),
                            anAssert.getChangeType()
                    ))
                    .collect(Collectors.toList());

            vipAssert.setVipAssert(memberAssertList);
            vipAsserts.add(vipAssert);
            dto.setVipAsserts(vipAsserts);
            dto.setVchcode(goodsBill.getVchcode());
            dto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(goodsBill.getVchtype().getCode()));
            dto.setBillNumber(goodsBill.getNumber());

            // 服务商品
//            List<SsVipAssertsServicePtype> vipAssertsServicePtypes = new ArrayList<>();
//            saveServicePtypes(goodsBill.getOutDetail(), vipAssertsServicePtypes, vipId);
//            if (!vipAssertsServicePtypes.isEmpty()) {
//                dto.setOnceCardPtypeRelation(vipAssertsServicePtypes);
//                dto.setAllowProperty(goodsBill.isProperty());
//            }

            dto.setMemo(goodsBill.getSourceOperation().getName());
            dto.setSourceOperation(goodsBill.getSourceOperation());
            dto.setSaveVipAsserts(goodsBill.getPayState() == PayStateEnum.Paied);
            dto.setOperationSource(request.getOperationSource());
            // 退货、换货（负值换货单）不再参与积分保护期策略，直接送成可用积分
            if ((goodsBill.getVchtype() == Vchtypes.SaleChangeBill && goodsBill.getCurrencyBillTotal().compareTo(BigDecimal.ZERO) < 0) ||
                    goodsBill.getVchtype() == Vchtypes.SaleBackBill) {
                dto.setExecuteProtectionStrategy(false);
            }
            memberAssertsChangeService.vipAssertsChange(dto);
            // 会员升降级
            vipLevelService.vipLevelUpdate(vipId);
        }
    }

    private void doCompute(BigDecimal giveScoreMoney, BigInteger vipId, GoodsBillDTO goodsBill) {
        // 出库单、换货单要计算赠送积分、成长值
        if (goodsBill.getVchtype() == Vchtypes.SaleBill || goodsBill.getVchtype() == Vchtypes.SaleChangeBill) {
            // 资产变动类型要根据单据类型来区分
            // 出库单、 正值换货单 = 消费获得、消费使用
            // 负值换货单 = 退款扣除、退款退回
            boolean isConsume = goodsBill.getVchtype() == Vchtypes.SaleBill ||
                    (goodsBill.getVchtype() == Vchtypes.SaleChangeBill && goodsBill.getCurrencyBillTotal().compareTo(BigDecimal.ZERO) > 0);

            // 这里只计算出库明细，所以金额应该取出库明细的 折后含税金额 + 赠金优惠 之和
            BigDecimal disedTaxedTotal = goodsBill.getOutDetail().stream().map(GoodsDetailDTO::getCurrencyDisedTaxedTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal givePreferentialTotal = goodsBill.getOutDetail().stream().map(GoodsDetailDTO::getCurrencyGivePreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal total = disedTaxedTotal.add(givePreferentialTotal);
            // 赠送积分
            if (giveScoreMoney != null && giveScoreMoney.compareTo(BigDecimal.ZERO) != 0) {
                Integer sendScore = vipAssertsChangeService.scoreCompute(vipId, giveScoreMoney, ScoreStrategyType.consumption);
                if (sendScore != 0) {
                    // 换货单计算出赠送积分后要减去资产列表中换入的积分
                    // 出库单的抵扣积分在做换货单时不会再传来了，只有出库单的赠送积分会传来，到这里是要扣出库单的赠送积分
                    Optional<VipAssert> any = goodsBill.getVipAsserts().stream().filter(v -> v.getTyped() == 0).findAny();
                    if (goodsBill.getVchtype() == Vchtypes.SaleChangeBill && any.isPresent()) {
                        VipAssert vipAssert = any.get();
                        // 移除
                        goodsBill.getVipAsserts().remove(vipAssert);
                        // 重新计算积分并赋值
                        BigDecimal score = vipAssert.getQty().add(BigDecimal.valueOf(sendScore));
                        vipAssert.setQty(score);
                        vipAssert.setTyped(0);
                        vipAssert.setMemo(score.compareTo(BigDecimal.ZERO) > 0 ? "赠送积分" : "扣减积分");
                        AssertsChangeType changeType = getChangeType(isConsume, score.compareTo(BigDecimal.ZERO) > 0);
                        vipAssert.setChangeType(changeType);
                        goodsBill.getVipAsserts().add(vipAssert);
                    } else {
                        // 出库单直接使用赠送积分 、 换货单没有传来积分信息也直接赠送积分
                        VipAssert score = new VipAssert();
                        score.setMemo("赠送积分");
                        score.setQty(new BigDecimal(sendScore));
                        score.setTyped(0);
                        score.setChangeType(AssertsChangeType.CONSUME_GET);
                        goodsBill.getVipAsserts().add(score);
                    }
                }
            }
            // 成长值
            Integer sendGrowth = vipAssertsChangeService.growthCompute(vipId, total, ScoreStrategyType.consumption);
            if (sendGrowth != 0) {
                // 换货单会传来要扣的成长值，所以这里要加上
                Optional<VipAssert> any = goodsBill.getVipAsserts().stream().filter(v -> v.getTyped() == 3).findAny();
                if (goodsBill.getVchtype() == Vchtypes.SaleChangeBill && any.isPresent()) {
                    VipAssert vipAssert = any.get();
                    // 移除
                    goodsBill.getVipAsserts().remove(vipAssert);
                    // 重新计算成长值并赋值
                    BigDecimal growth = vipAssert.getQty().add(BigDecimal.valueOf(sendGrowth));
                    vipAssert.setQty(growth);
                    vipAssert.setTyped(3);
                    vipAssert.setMemo("成长值");
                    AssertsChangeType changeType = getChangeType(isConsume, growth.compareTo(BigDecimal.ZERO) > 0);
                    vipAssert.setChangeType(changeType);
                    goodsBill.getVipAsserts().add(vipAssert);
                } else {
                    VipAssert growth = new VipAssert();
                    growth.setMemo("成长值");
                    growth.setQty(new BigDecimal(sendGrowth));
                    growth.setTyped(3);
                    growth.setChangeType(AssertsChangeType.CONSUME_GET);
                    goodsBill.getVipAsserts().add(growth);
                }
            }
            // 累计成长值
            Integer accumulatedGrowth = vipAssertsChangeService.accumulatedGrowthCompute(vipId, ScoreStrategyType.consumption, goodsBill.getVchtype() == Vchtypes.SaleChangeBill);
            if (accumulatedGrowth != 0) {
                VipAssert growth = new VipAssert();
                growth.setMemo("累计成长值");
                growth.setQty(new BigDecimal(accumulatedGrowth));
                growth.setTyped(99);
                growth.setChangeType(AssertsChangeType.ACCUMULATED_GIFT);
                goodsBill.getVipAsserts().add(growth);
            }
        }
    }

    /**
     * 开单时，提交优惠辅助表信息
     */
    @Override
    public void addPreferentialInfo(PosBill request, boolean saleBill) {
        GoodsBillDTO goods = request.getGoodsBill();
        if (!request.getDeletePreferential()) {
            // 不清除优惠辅助表，就不允许新增或修改数据
            return;
        }
        // 清理库里该vchcode的优惠辅助信息
        preferentialBillMapper.deletePreferentialBills(goods.getVchcode(), CurrentUser.getProfileId());
        preferentialBillMapper.deletePreferentialGoodsList(goods.getVchcode(), CurrentUser.getProfileId());

        //添加订单促销
        List<PreferentialDto> preferentialList = goods.getPreferentialList();
        List<SsPreferentialBill> list = new ArrayList<>();
        if (preferentialList != null && !preferentialList.isEmpty()) {
            for (PreferentialDto preferentialDto : preferentialList) {
                if (BigDecimal.ZERO.compareTo(preferentialDto.getTotal()) != 0 && preferentialDto.getTypeId() != null) {
                    SsPreferentialBill preferentialBill = new SsPreferentialBill();
                    preferentialBill.setId(UId.newId());
                    preferentialBill.setProfileId(CurrentUser.getProfileId());
                    preferentialBill.setVchcode(goods.getVchcode());
                    preferentialBill.setPreferentialType(preferentialDto.getType());
                    preferentialBill.setPreferentialTypeId(preferentialDto.getTypeId());
                    preferentialBill.setPreferentialTotal(preferentialDto.getTotal());
                    preferentialBill.setPreferentialValue(preferentialDto.getValue());
                    preferentialBill.setMemo("");
                    list.add(preferentialBill);
                }
            }
        }

        if (!list.isEmpty()) {
            preferentialBillMapper.insertPreferentialBills(list);
        }

        //添加商品级别促销
        List<GoodsDetailDTO> goodsDetails;
        if (saleBill) {
            goodsDetails = goods.getOutDetail();
        } else {
            goodsDetails = goods.getInDetail();
        }

        List<SsPreferentialGoodsDetail> goodsPreferentialHelpList = new ArrayList<>();
        for (GoodsDetailDTO goodsDetail : goodsDetails) {
            List<PreferentialDto> preferentialList1 = goodsDetail.getPreferentialList();
            if (preferentialList1 != null && !preferentialList1.isEmpty()) {
                for (PreferentialDto preferentialDto : preferentialList1) {
                    if (BigDecimal.ZERO.compareTo(preferentialDto.getTotal()) != 0 && preferentialDto.getTypeId() != null) {
                        SsPreferentialGoodsDetail preferentialGoods = new SsPreferentialGoodsDetail();
                        preferentialGoods.setId(UId.newId());
                        preferentialGoods.setDetailId(goodsDetail.getDetailId());
                        preferentialGoods.setProfileId(CurrentUser.getProfileId());
                        preferentialGoods.setVchcode(goods.getVchcode());
                        preferentialGoods.setPreferentialType(preferentialDto.getType());
                        preferentialGoods.setPreferentialTypeId(preferentialDto.getTypeId());
                        preferentialGoods.setPreferentialTotal(preferentialDto.getTotal());
                        preferentialGoods.setStatisticsTotal(BigDecimal.ZERO.equals(preferentialDto.getDiscount()));
                        preferentialGoods.setMemo("");
                        goodsPreferentialHelpList.add(preferentialGoods);
                    }
                }
            }
        }
        if (!goodsPreferentialHelpList.isEmpty()) {
            preferentialGoodsDetailMapper.insertPreferentialGoodsList(goodsPreferentialHelpList);
        }
    }

    void saveServicePtypes(List<GoodsDetailDTO> detail, List<SsVipAssertsServicePtype> vipAssertsServicePtypes,
                           BigInteger vipId) {
        if (detail != null && !detail.isEmpty()) {
            detail.forEach(goodsDetailDTO -> {
                if (goodsDetailDTO.getPcategory() == 1) {
                    SsVipAssertsServicePtype vipAssertsServicePtype = new SsVipAssertsServicePtype(null,
                            goodsDetailDTO.getPtypeId(), goodsDetailDTO.getSkuId(), goodsDetailDTO.getUnitId(),
                            goodsDetailDTO.getCpaId(), CurrentUser.getProfileId());
                    vipAssertsServicePtype.setVipId(vipId);
                    vipAssertsServicePtype.setQty(goodsDetailDTO.getUnitQty().intValue());
                    vipAssertsServicePtype.setTotalAmount(goodsDetailDTO.getCurrencyDisedTotal());
                    vipAssertsServicePtype.setProductId(goodsDetailDTO.getProductId());
                    vipAssertsServicePtype.setProductTimeType(goodsDetailDTO.getProductTimeType());
                    vipAssertsServicePtype.setUsedCount(goodsDetailDTO.getUsedCount());
                    vipAssertsServicePtype.setRemainCount(goodsDetailDTO.getRemainCount());
                    vipAssertsServicePtype.setStartDate(goodsDetailDTO.getServiceStartTime());
                    vipAssertsServicePtype.setLostDate(goodsDetailDTO.getServiceEndTime());
                    vipAssertsServicePtypes.add(vipAssertsServicePtype);
                }
            });
        }
    }

    @Override
    public void insertVipBill(BigInteger vipId, String cashierId, String vchtype, BigInteger vchcode,
                              BigDecimal assertTotal) {
        SsVipBill ssVipBill = new SsVipBill();
        ssVipBill.setVipId(vipId);
        ssVipBill.setVchcode(vchcode.toString());
        ssVipBill.setVchtype(vchtype);
        ssVipBill.setCashierId(cashierId);
        ssVipBill.setAssertTotal(assertTotal);
        vipService.insertOrUpdateVipBill(ssVipBill);
    }

    @NotNull
    private static AssertsChangeType getChangeType(boolean isConsume, boolean isPositive) {
        return isConsume
                ? (isPositive ? AssertsChangeType.CONSUME_GET : AssertsChangeType.CONSUME_USE)
                : (isPositive ? AssertsChangeType.REFUND_RETURN : AssertsChangeType.REFUND_DEDUCTION);
    }
}
