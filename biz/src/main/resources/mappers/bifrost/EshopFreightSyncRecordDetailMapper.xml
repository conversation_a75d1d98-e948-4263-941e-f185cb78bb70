<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.bifrost.mapper.EshopFreightSyncRecordDetailMapper">
    <insert id="add">
        insert into pl_eshop_freight_sync_record_detail(id, record_id, profile_id, sale_order_id, sale_order_detail_id,
        oid,
        deliver_order_id, deliver_order_detail_id, warehouse_task_id,
        warehouse_task_detail_id, trade_id, freight_code, freight_bill_no,
        sync_type, sync_qty, call_status, sync_status, sync_message,
        platform_freight_id)
        values
        <foreach collection="details" item="detail" separator=",">
            (#{detail.id}, #{detail.recordId}, #{detail.profileId}, #{detail.saleOrderId}, #{detail.saleOrderDetailId},
            #{detail.oid},
            #{detail.deliverOrderId}, #{detail.deliverDetailId}, #{detail.warehouseTaskId},
            #{detail.warehouseTaskDetailId}, #{detail.tradeId}, #{detail.freightCode}, #{detail.freightBillNo},
            #{detail.syncType}, #{detail.syncQty}, #{detail.callStatus}, #{detail.syncStatus}, #{detail.syncMessage},
            #{detail.platformFreightId})
        </foreach>
    </insert>
    <update id="update">
        <foreach collection="updateInfos" separator=";" item="updateInfo">
            update pl_eshop_freight_sync_record_detail
            set sync_status=#{updateInfo.status},
            sync_time=now(),
            sync_message=#{updateInfo.message},
            platform_freight_id=#{updateInfo.platformFreightId}
            where profile_id=#{profileId} and id=#{updateInfo.id}
        </foreach>
    </update>
    <select id="list" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecordDetail">
        select * from pl_eshop_freight_sync_record_detail
        where profile_id=#{profileId}
        and record_id in
        <foreach collection="recordIds" close=")" open="(" separator="," item="recordId">
            #{recordId}
        </foreach>
    </select>
    <select id="listByTradeIds" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopFreightSyncRecordDetail">
        select * from pl_eshop_freight_sync_record_detail
        where profile_id=#{profileId}
        and  trade_id in
        <foreach collection="tradeIds" close=")" open="(" separator="," item="tradeId">
            #{tradeId}
        </foreach>
    </select>
</mapper>