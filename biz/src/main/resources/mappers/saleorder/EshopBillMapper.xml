<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopBillMapper">
    <update id="updateSaleOrderProcessState">
        UPDATE `pl_eshop_sale_order`
        SET process_state=#{processState}
        WHERE profile_id = #{profileId}
          AND id = #{id}
    </update>

    <update id="batchUpdateSaleOrderProcessState">
        <foreach collection="list" item="billChange" separator=";">
            UPDATE `pl_eshop_sale_order`
            SET process_state=#{billChange.processStatus}
            WHERE profile_id = #{billChange.profileId}
            AND id = #{billChange.eshopOrderId}
        </foreach>
    </update>

    <update id="updateSaleOrderDetails">
        UPDATE `pl_eshop_sale_order_detail` set deleted=1 where profile_id=#{profileId}
        and id IN
        <foreach collection="detailIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
    </update>
    <update id="updateSaleOrder">
        UPDATE `pl_eshop_sale_order`
        SET deleted=1,
            process_state=0
        WHERE profile_id = #{profileId}
          AND id = #{id}
    </update>
    <update id="updateOrderDetailProcessState">
         UPDATE `pl_eshop_sale_order_detail`
        SET  deleted=0
        WHERE profile_id = #{profileId}
          AND eshop_order_id = #{eshopOrderId}
    </update>
    <delete id="deletePrimaryKey">
        update td_bill_primary_key set deleted=1 where profile_id=#{profileId} and otype_id=#{otypeId} and trade_order_id=#{tradeId} and batch_id=0
    </delete>
    <select id="querySaleOrderDetails" resultType="com.wsgjp.ct.sale.common.entity.dto.EshopOrderDetailDTO">
        select deleted, qty, sku_id, ktype_id
        from pl_eshop_sale_order_detail
        WHERE profile_id = #{profileId}
          AND eshop_order_id = #{eshopOrderId}
          AND mapping_state = 1
          AND local_refund_state != 4
          AND platform_detail_trade_state IN (0, 1, 4)
          and deliver_required = 1
    </select>

    <select id="querySaleOrder" resultType="com.wsgjp.ct.sale.common.entity.dto.EshopSaleOrder">
        select o.profile_id,
               o.id,
               o.deleted,
               o.local_trade_state,
               o.self_delivery_mode,
               o.local_refund_state,
               o.deleted,
               o.otype_id,
               o.trade_order_id
        from pl_eshop_sale_order o
        where o.profile_id = #{profileId}
          and o.id = #{id}
    </select>

    <select id="queryAdvanceOrder" resultType="com.wsgjp.ct.sale.common.entity.dto.EshopSaleOrder">
        select c.profile_id,
               c.vchcode as id,
               c.deleted,
               p.local_trade_state,
               c.self_delivery_mode,
               p.local_refund_state,
               c.otype_id,
               p.trade_id,
               c.platform_parent_order_id
        from td_orderbill_core c
            LEFT JOIN td_orderbill_platform p ON p.profile_id=`c`.profile_id
                AND p.vchcode=`c`.vchcode
        where c.profile_id = #{profileId}
          and c.vchcode = #{id}
        <if test="submitBatchId!=null and submitBatchId != 0">
            AND p.submit_batch_id = #{submitBatchId}
        </if>
    </select>

    <select id="queryAdvanceOrderList" resultType="com.wsgjp.ct.sale.common.entity.dto.EshopSaleOrder">
        select c.profile_id,
        c.vchcode as id,
        c.deleted,
        p.local_trade_state,
        c.self_delivery_mode,
        p.local_refund_state,
        c.otype_id,
        p.trade_id,
        c.platform_parent_order_id,
        p.submit_batch_id
        from td_orderbill_core c
        LEFT JOIN td_orderbill_platform p ON p.profile_id=`c`.profile_id
        AND p.vchcode=`c`.vchcode
        where c.profile_id = #{profileId}
        and c.vchcode IN
        <foreach collection="idList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        <if test="submitBatchIdList!=null and submitBatchIdList.size()>0">
            and p.submit_batch_id IN
            <foreach collection="submitBatchIdList" close=")" open="(" separator="," item="submitBatchId">
                #{submitBatchId}
            </foreach>
        </if>
    </select>

    <update id="updateAdvanceOrderProcessState">
        UPDATE `td_orderbill_core`
        SET submit_send_state=1,
            deleted=0
        WHERE profile_id = #{profileId}
          AND vchcode = #{eshopOrderId}
    </update>

    <update id="updateCyclePurchaseAdvanceOrderProcessState">
        UPDATE `td_orderbill_core`
        SET submit_send_state=#{processState},
            deleted=0
        WHERE profile_id = #{profileId}
          AND vchcode = #{vchcode}
    </update>

    <update id="batchUpdateAdvanceOrderProcessState">
        <foreach collection="list" item="order" separator=";">
            UPDATE `td_orderbill_core`
            SET submit_send_state=1,
            deleted=0
            WHERE profile_id = #{order.profileId}
            AND vchcode = #{order.eshopOrderId}
        </foreach>
    </update>

    <update id="updateAdvanceOrderDetails">
        UPDATE `td_orderbill_detail_platform` set deleted=1 where profile_id=#{profileId}
        and detail_id IN
        <foreach collection="detailIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
    </update>

    <update id="updateAdvanceOrder">
        UPDATE `td_orderbill_core`
        SET deleted=1,
            submit_send_state=0
        WHERE profile_id = #{profileId}
          AND id = #{eshopOrderId}
    </update>

    <update id="updateAdvanceOrderDetailProcessState">
         UPDATE `td_orderbill_detail_platform`
        SET  deleted=0
        WHERE profile_id = #{profileId}
          AND vchcode = #{eshopOrderId}
    </update>

    <update id="batchUpdateAdvanceOrderDetailProcessState">
        UPDATE `td_orderbill_detail_platform` set process_state=#{processState} where profile_id=#{profileId}
        and vchcode = #{eshopOrderId}
        <if test="processState != 0">
            and process_state = 0
        </if>
        <if test="detailIds!=null and detailIds.size()>0">
            and detail_id IN
            <foreach collection="detailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </update>

    <update id="batchUpdateSaleOrderDetailProcess">
        UPDATE `pl_eshop_sale_order_detail` set process_state=#{processState} where profile_id=#{profileId}
        and eshop_order_id = #{eshopOrderId}
        <if test="processState != 0">
            and process_state = 0
        </if>
        <if test="detailIds!=null and detailIds.size()>0">
            and id IN
            <foreach collection="detailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </update>

    <insert id="insertOrderSubmitBatch">
        INSERT INTO pl_eshop_submit_batch
                (`submit_batch_id`, `profile_id`, `eshop_order_id`, `otype_id`, `detail_count`,
                    `etype_id`, `submit_batchno`, `deleted`,`submit_source_type`)
                value (#{submitBatchId}, #{profileId}, #{eshopOrderId}, #{otypeId}, #{detailCount},
                #{etypeId}, #{submitBatchno}, #{deleted}, #{submitSourceType})
    </insert>

    <insert id="batchInsertOrderSubmitBatch">
        INSERT INTO pl_eshop_submit_batch
        (`submit_batch_id`, `profile_id`, `eshop_order_id`, `otype_id`, `detail_count`,
         `etype_id`, `submit_batchno`, `deleted`,`submit_source_type`)
        VALUES
        <foreach collection="list" item="batch" separator=",">
            (#{batch.submitBatchId}, #{batch.profileId}, #{batch.eshopOrderId}, #{batch.otypeId}, #{batch.detailCount},
            #{batch.etypeId}, #{batch.submitBatchno}, #{batch.deleted}, #{batch.submitSourceType})
        </foreach>
    </insert>

    <insert id="insertOrderSubmitBatchDetail">
        INSERT INTO pl_eshop_submit_batch_detail
                (`id`, `submit_batch_id`, `profile_id`, `eshop_order_id`, `otype_id`,
                 `eshop_order_detail_id`, `trade_order_detail_id`, `qty`,`deleted`)
                value (#{id}, #{submitBatchId}, #{profileId}, #{eshopOrderId}, #{otypeId},
                #{eshopOrderDetailId}, #{tradeOrderDetailId}, #{qty}, #{deleted})
    </insert>

    <insert id="insertEshopOrderSubmitDetailPrimaryKey">
        INSERT INTO pl_eshop_order_submit_detail_primary_key
        (`id`, `profile_id`, `otype_id`, `trade_order_id`, `oid`,
         `batch_id`, `qty`, `md5_key`)
            value (#{id}, #{profileId}, #{otypeId}, #{tradeOrderId}, #{oid},
                   #{batchId}, #{qty}, #{md5Key})
    </insert>

    <insert id="batchInsertEshopOrderSubmitDetailPrimaryKey">
        INSERT INTO pl_eshop_order_submit_detail_primary_key
        (`id`, `profile_id`, `otype_id`, `trade_order_id`, `oid`,
         `batch_id`, `qty`, `md5_key`)
            VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.profileId}, #{item.otypeId}, #{item.tradeOrderId}, #{item.oid},
            #{item.batchId}, #{item.qty}, #{item.md5Key})
        </foreach>
    </insert>


    <insert id="batchInsertOrderSubmitBatchDetail">
        INSERT INTO pl_eshop_submit_batch_detail
        (`id`, `submit_batch_id`, `profile_id`, `eshop_order_id`, `otype_id`,
         `eshop_order_detail_id`, `trade_order_detail_id`, `qty`,`deleted`)
        VALUES
        <foreach collection="list" item="batch" separator=",">
            (#{batch.id}, #{batch.submitBatchId}, #{batch.profileId}, #{batch.eshopOrderId}, #{batch.otypeId},
            #{batch.eshopOrderDetailId}, #{batch.tradeOrderDetailId}, #{batch.qty}, #{batch.deleted})
        </foreach>
    </insert>

    <delete id="deleteOrderSubmitBatchDetail">
        delete
        from pl_eshop_submit_batch_detail where profile_id = #{profileId}
        AND submit_batch_id = #{submitBatchId} and eshop_order_id=#{eshopOrderId}
    </delete>

    <delete id="deleteEshopOrderSubmitDetailPrimaryKey">
        delete
        from pl_eshop_order_submit_detail_primary_key where profile_id = #{profileId}
        AND otype_id = #{otypeId} and trade_order_id=#{tradeOrderId}
        <if test="batchId != null and batchId != '' and batchId != 0">
            and batch_id=#{batchId}
        </if>
    </delete>

    <delete id="deleteOrderSubmitBatch">
        delete
        from pl_eshop_submit_batch where profile_id = #{profileId}
                                    AND submit_batch_id = #{submitBatchId}
                                     and eshop_order_id=#{eshopOrderId}
    </delete>

    <select id="queryOrderSubmitBatchDetail"
            resultType="com.wsgjp.ct.sale.common.entity.dto.OrderSubmitBatchDetail">
        select *
        from pl_eshop_submit_batch_detail
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=''">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="submitBatchIds!=null and submitBatchIds.size()>0">
            AND submit_batch_id IN
            <foreach collection="submitBatchIds" close=")" open="(" separator="," item="batchId">
                #{batchId}
            </foreach>
        </if>
    </select>


    <select id="queryOrderSubmitBatch"
            resultType="com.wsgjp.ct.sale.common.entity.dto.OrderSubmitBatch">
        select *
        from pl_eshop_submit_batch
        where profile_id = #{profileId}
            and eshop_order_id = #{eshopOrderId}
            AND submit_batch_id = #{submitBatchId} limit 1
    </select>

    <select id="queryOrderSubmitBatchByEshopOrderId"
            resultType="com.wsgjp.ct.sale.common.entity.dto.OrderSubmitBatch">
        select *
        from pl_eshop_submit_batch
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId} order by submit_batchno desc
    </select>
    <select id="queryOrderSubmitBatchByEshopOrderIdList"
            resultType="com.wsgjp.ct.sale.common.entity.dto.OrderSubmitBatch">
        select *
        from pl_eshop_submit_batch
        where profile_id = #{profileId}
          and eshop_order_id in
        <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
        order by submit_batchno desc
    </select>

    <select id="queryDetails"
            resultType="com.wsgjp.ct.sale.common.entity.dto.EshopOrderDetailDTO">
        select d.profile_id,d.eshop_order_id,d.ktype_id,d.platform_detail_trade_state,d.local_refund_process_state,d.deleted,
        d.sku_id,d.qty,d.stock_sync_rule_id,d.id,d.deliver_required,d.process_state,
        d.local_refund_state,d.platform_detail_trade_state,d.mapping_state,d.order_sale_type
        from pl_eshop_sale_order_detail d
        where d.profile_id=#{profileId}
        and d.eshop_order_id = #{eshopOrderId}
        <if test="detailIds!=null and detailIds.size()>0">
            and d.id in
            <foreach collection="detailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>

        <if test="oids!=null and oids.size()>0">
            and d.trade_order_detail_id in
            <foreach collection="oids" close=")" open="(" separator="," item="oidKey">
                #{oidKey}
            </foreach>
        </if>
        <if test="tradeStates !=null and tradeStates.size()>0">
            and d.platform_detail_trade_state in
            <foreach collection="tradeStates" close=")" open="(" separator="," item="platformDetailTradeState">
                #{platformDetailTradeState}
            </foreach>
        </if>
    </select>

<!--    <select id="queryAdvanceDetails"-->
<!--            resultType="com.wsgjp.ct.sale.common.entity.dto.EshopOrderDetailDTO">-->
<!--        select d.profile_id,d.eshop_order_id,d.ktype_id,d.platform_detail_trade_state,d.local_refund_process_state,d.deleted,-->
<!--        d.sku_id,d.qty,d.stock_sync_rule_id,d.id,d.deliver_required,-->
<!--        d.local_refund_state,d.mapping_state,d.order_sale_type-->
<!--        from pl_eshop_sale_order_advance_detail d-->
<!--        where d.profile_id=#{profileId}-->
<!--        and d.eshop_order_id = #{eshopOrderId}-->
<!--        <if test="detailIds!=null and detailIds.size()>0">-->
<!--            and d.id in-->
<!--            <foreach collection="detailIds" close=")" open="(" separator="," item="key">-->
<!--                #{key}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="oids!=null and oids.size()>0">-->
<!--            and d.trade_order_detail_id in-->
<!--            <foreach collection="oids" close=")" open="(" separator="," item="oidKey">-->
<!--                #{oidKey}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </select>-->
    <update id="updateSubmitTimes">
        UPDATE `pl_eshop_sale_order_extend` SET submit_times=0
        WHERE profile_id = #{profileId} AND eshop_order_id = #{eshopOrderId}
    </update>

    <update id="batchUpdateSubmitTimes">
        <foreach collection="list" item="order" separator=";">
            UPDATE `pl_eshop_sale_order_extend`
            SET submit_times=0
            WHERE profile_id = #{order.profileId}
            AND eshop_order_id = #{order.eshopOrderId}
        </foreach>
    </update>

    <update id="updateSaleOrderDraft">
        UPDATE `pl_eshop_sale_order_extend` SET is_draft=0
        WHERE profile_id = #{profileId} AND eshop_order_id = #{eshopOrderId}
    </update>

    <update id="batchUpdateSaleOrderDraft">
        <foreach collection="list" item="order" separator=";">
            UPDATE `pl_eshop_sale_order_extend`
            SET is_draft=0
            WHERE profile_id = #{order.profileId}
            AND eshop_order_id = #{order.eshopOrderId}
        </foreach>
    </update>

</mapper>