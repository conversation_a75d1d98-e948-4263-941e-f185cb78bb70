<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.BaseKtypeMapper">

    <sql id="main_columns">
        `id`
        ,
	`profile_id`,
	`typeid`,
	`partypeid`,
	`usercode`,
	`fullname`,
	`shortname`,
	`namepy`,
	`scategory`,
	`classed`,
	`stoped`,
	`deleted`,
	`rowindex`,
	`memo`,
	`sysrow`,
	`create_time`,
	 update_time,
    if(scategory=2,true,false) as 'isWms'
    </sql>

    <select id="getKtypeInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Ktype">
        select
        <include refid="main_columns"/>
        from base_ktype
        where profile_id=#{profileId}
        and id in
        <foreach collection="ktypeIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getKtypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Ktype">
        select
        <include refid="main_columns"/>
        from base_ktype
        where id =#{id}
    </select>
    <select id="getKtypeByKtypeName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Ktype">
        select
        <include refid="main_columns"/>
        from base_ktype
        where fullname =#{ktypeName}
        and profile_id = #{profileId}
    </select>
    <select id="getKtypeByIdAndProfileId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Ktype">
        select
        <include refid="main_columns"/>
        from base_ktype
        where id =#{id} and profile_id = #{profileId}
    </select>

</mapper>