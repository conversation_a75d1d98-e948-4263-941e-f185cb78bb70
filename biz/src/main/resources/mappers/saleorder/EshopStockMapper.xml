<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopStockMapper">

    <select id="queryLockRule" resultType="com.wsgjp.ct.sale.common.entity.dto.StockRuleDetailDTO">
        select rd.profile_id,rd.rule_id,rd.ktype_id,r.rule_type
        from pl_eshop_stock_sync_rule_detail rd
        join pl_eshop_stock_sync_rule r on r.profile_id=rd.profile_id and r.id=rd.rule_id
        where rd.profile_id=#{profileId}
        and r.state=0
        and r.deleted=0
        and r.rule_type=3
        and rd.rule_id in
        <foreach collection="ruleIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        union all
        select r.profile_id,r.id as ruleId,0 as ktypeId,r.rule_type
        from pl_eshop_stock_sync_rule r
        where r.profile_id=#{profileId}
        and r.state=0
        and r.deleted=0
        and r.rule_type =4
        and r.id in
        <foreach collection="ruleIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getEshopInfo" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select e.otype_id                          as eshopId,
               e.profile_id,
               e.fullname,
               if(c.mapping_type = 1, true, false) as openXcode
        from pl_eshop e
                 left join pl_eshop_config c on c.profile_id = e.profile_id and c.eshop_id = e.otype_id
        where e.profile_id = #{profileId}
          and e.otype_id = #{eshopId}
    </select>

    <select id="queryStockRuleListByComboXcode" resultType="com.wsgjp.ct.sale.common.entity.dto.StockRuleDTO">
        select ps.profile_id,
        rule.id as ruleId,
        rule.xcode,
        rule.pcategory,
        rule.rule_name,
        0 as skuId,
        p.id as ptypeId
        from pl_eshop_product_sku_mapping ps
        left join pl_eshop_stock_sync_rule rule
        on ps.profile_id = rule.profile_id and ps.platform_xcode = rule.xcode
        left join base_ptype p on ps.profile_id = p.profile_id and p.usercode = ps.platform_xcode and p.pcategory=2
        where ps.profile_id = #{profileId}
        and ps.eshop_id = #{eshopId}
        and ps.platform_num_id = #{platformNumId}
        and ps.platform_xcode = #{xcode}
        and rule.pcategory = 2
        and rule.deleted=0
        and rule.state = 0
        <if test="warehouseCode!=null and warehouseCode!=''">
            and rule.target_type=1
        </if>
        <if test="warehouseCode=null or warehouseCode=''">
            and rule.target_type=0
        </if>
    </select>

    <select id="queryStockRuleListBySku" resultType="com.wsgjp.ct.sale.common.entity.dto.StockRuleDTO">
        select ps.profile_id,
        cfg.rule_id as ruleId,
        rule.xcode,
        rule.pcategory,
        rule.rule_name,
        ps.ptype_id,
        ps.sku_id
        from pl_eshop_product_sku_mapping ps
        left join pl_eshop_product_sku_rule_config cfg
        on cfg.profile_id=ps.profile_id and ps.platform_num_id=cfg.platform_num_id
        and cfg.platform_properties=ps.platform_properties_name
        left join pl_eshop_stock_sync_rule rule
        on cfg.profile_id = rule.profile_id and cfg.rule_id = rule.id

        where ps.profile_id = #{profileId}
        and ps.eshop_id = #{eshopId}
        and ps.platform_num_id = #{platformNumId}
        and ps.platform_sku_id = #{platformSkuId}
        and rule.state = 0
        and rule.deleted=0
        <if test="warehouseCode!=null and warehouseCode!=''">
            and cfg.warehouse_code=#{warehouseCode}
            and rule.target_type=1
        </if>
        <if test="warehouseCode=null or warehouseCode=''">
            and rule.target_type=0
        </if>
    </select>

    <select id="queryStockRuleListBySkuXcode" resultType="com.wsgjp.ct.sale.common.entity.dto.StockRuleDTO">
        select ps.profile_id,
        rule.id as ruleId,
        rule.xcode,
        rule.pcategory,
        rule.rule_name,
        bpx.sku_id,
        bpx.ptype_id
        from pl_eshop_product_sku_mapping ps
        left join pl_eshop_stock_sync_rule rule
        on ps.profile_id = rule.profile_id and ps.platform_xcode = rule.xcode  and rule.deleted=0
        left join base_ptype_xcode bpx on ps.profile_id = bpx.profile_id and ps.platform_xcode = bpx.xcode
        where ps.profile_id = #{profileId}
        and ps.eshop_id = #{eshopId}
        and ps.platform_num_id = #{platformNumId}
        and ps.platform_xcode = #{xcode}
        and rule.pcategory != 2
        and rule.state = 0
        <if test="warehouseCode!=null and warehouseCode!=''">
            and rule.target_type=1
        </if>
        <if test="warehouseCode=null or warehouseCode=''">
            and rule.target_type=0
        </if>
    </select>


</mapper>