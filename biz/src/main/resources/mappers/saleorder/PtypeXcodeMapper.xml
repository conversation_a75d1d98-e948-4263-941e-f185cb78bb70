<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.PtypeXcodeMapper">
    <select id="getPtyeXcodeByXcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeXCodeEntity">
        select *, bp.pcategory, bp.fullname as ptypeName
        from base_ptype_xcode px
                 left join base_ptype bp on bp.id = px.ptype_id and bp.profile_id = px.profile_id
        where px.profile_id = #{profileId}
          and px.xcode = #{xCode};
    </select>

    <select id="querySkuListByBarcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeXCodeEntity">
        select bpf.id,px.xcode,bp.pcategory,bp.fullname as ptypeName,
        bpf.fullbarcode as barcode,bpf.profile_id,bpf.sku_id,bpf.unit_id,bpf.ptype_id
        from base_ptype_fullbarcode bpf
        LEFT JOIN base_ptype_xcode px on bpf.profile_id=px.profile_id and px.ptype_id=bpf.ptype_id and
        bpf.sku_id=px.sku_id and bpf.unit_id=px.unit_id
        left join base_ptype bp on bp.id = bpf.ptype_id and bp.profile_id = bpf.profile_id
        where bpf.profile_id=#{profileId} and bpf.fullbarcode in
        <foreach collection="barcodeList" open="(" close=")" separator="," item="code">
            #{code}
        </foreach>
    </select>
       <select id="getPtypeXcodeBySku" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeXCodeEntity">
        select *,bp.pcategory,bp.fullname as ptypeName from base_ptype_xcode px
        left join base_ptype bp  on bp.id = px.ptype_id and bp.profile_id = px.profile_id
        where px.profile_id=#{profileId}
        <if test="null != skuId and '' != skuId">
            and px.sku_id=#{skuId}
        </if>
        <if test="null != unitId and '' !=unitId">
            and px.unit_Id=#{unitId}
        </if>

    </select>


    <select id="getComboPtyeXcodeInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeXCodeEntity">
        select px.*, bp.pcategory, bp.fullname as ptypeName
        from base_ptype_xcode px
                 left join base_ptype bp on bp.id = px.ptype_id and bp.profile_id = px.profile_id
        where px.profile_id = #{profileId}
          and px.ptype_id = #{ptypeId} and px.info_type=#{infoType} and defaulted=1 limit 1
    </select>

    <select id="getComboByXcodeAndInfoType" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeXCodeEntity">
        select px.*, bp.pcategory, bp.fullname as ptypeName
        from base_ptype_xcode px
                 left join base_ptype bp on bp.id = px.ptype_id and bp.profile_id = px.profile_id
        where px.profile_id = #{profileId} and bp.deleted=0 and bp.stoped=0
          and px.xcode = #{xcode} and px.info_type=#{infoType} and defaulted=1 limit 1
    </select>
    <select id="getIdForComboByXcodeAndInfoType" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSingleSkuInfo">
        select px.profile_id,px.ptype_id,px.sku_id,px.unit_id
        from base_ptype_xcode px
                 left join base_ptype bp on bp.id = px.ptype_id and bp.profile_id = px.profile_id
        where px.profile_id = #{profileId} and bp.deleted=0 and bp.stoped=0
          and px.xcode = #{xcode} and px.info_type=#{infoType}  limit 1
    </select>

 <select id="getIdForbarcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSingleSkuInfo">
        select px.profile_id,px.ptype_id,px.sku_id,px.unit_id
        from base_ptype_fullbarcode px
                 left join base_ptype bp on bp.id = px.ptype_id and bp.profile_id = px.profile_id
        where px.profile_id = #{profileId} and bp.deleted=0 and bp.stoped=0
          and px.fullbarcode = #{barcode} and px.defaulted=1 limit 1
    </select>

</mapper>