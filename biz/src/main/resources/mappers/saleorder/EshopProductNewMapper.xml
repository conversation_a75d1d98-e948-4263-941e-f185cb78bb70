<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductNewMapper">

    <sql id="whereSql">
        where pro.profile_id=#{profileId}
        <if test="otypeIdList!=null and otypeIdList.size()>0">
            and pro.eshop_id in
            <foreach collection="otypeIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="uniqueIds!=null and uniqueIds.size()>0">
            and sk.unique_id in
            <foreach collection="uniqueIds" close=")" open="(" separator="," item="uniqueId">
                #{uniqueId}
            </foreach>
        </if>
        <if test="eshopProductSkuIds!=null and eshopProductSkuIds.size()>0">
            and sk.id in
            <foreach collection="eshopProductSkuIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="localInfo!=null and localInfo!=''">
            and (bp.fullname like CONCAT('%',#{localInfo},'%') or bp.usercode like CONCAT('%',#{localInfo},'%') or
            bpx.xcode like CONCAT('%',#{localInfo},'%')),
        </if>
        <if test="platformInfo!=null and platformInfo!=''">
            and (sk.platform_xcode like CONCAT('%',#{platformInfo},'%')
            or sk.platform_properties_name like CONCAT('%',#{platformInfo},'%')
            or pro.platform_fullname like CONCAT('%',#{platformInfo},'%')
            or sk.platform_num_id = #{platformInfo}
            or sk.platform_sku_id = #{platformInfo})
        </if>
        <if test="xcodeQueryType==1">
            and sk.platform_xcode=''
        </if>
        <if test="xcodeQueryType==2">
            and sk.platform_xcode!=''
        </if>
        <if test="xcodeQueryType==3">
            and sk.platform_xcode in
            (select platform_xcode
            from pl_eshop_product_sku
            where profile_id=#{profileId}
            and platform_xcode!=''
            <if test="otypeIdList!=null and otypeIdList.size()>0">
                and sk.eshop_id in
                <foreach collection="otypeIdList" close=")" open="(" separator="," item="id">
                    #{id}
                </foreach>
            </if>
            group by platform_xcode
            having count(1)>1)
        </if>
        <if test="xcodeQueryType==4">
            and sk.platform_xcode! = bpx.xcode
        </if>
        <if test="stockState>1">
            and pro.platform_stock_state = #{stockState}
        </if>
        <if test="mappingState==1">
            and bp.id > 0
        </if>
        <if test="mappingState==2">
            and ifnull(bp.id, 0) = 0
        </if>

        <if test="eshopName!=null and eshopName!=''">
            and pe.fullname like CONCAT('%',#{eshopName},'%')
        </if>
        <if test="platformNumId!=null and platformNumId!=''">
            and sk.platform_num_id = #{platformNumId}
        </if>
        <if test="platformFullName!=null and platformFullName!=''">
            and pro.platform_fullname like CONCAT('%',#{platformFullName},'%')
        </if>
        <if test="platformXcode!=null and platformXcode!=''">
            and sk.platform_xcode like CONCAT('%',#{platformXcode},'%')
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and sk.platform_properties_name like CONCAT('%',#{platformPropertiesName},'%')
        </if>
        <if test="platformUnitName!=null and platformUnitName!=''">
            and sk.platform_unit_name like CONCAT('%',#{platformUnitName},'%')
        </if>
        <if test="platformSkuId!=null and platformSkuId!=''">
            and sk.platform_sku_id = #{platformSkuId}
        </if>
        <if test="platformStockState!=null and platformStockState>0">
            and pro.platform_stock_state = #{platformStockState}
        </if>
        <if test="ptypeName!=null and ptypeName!=''">
            and bp.fullname = like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and bpx.xcode = like CONCAT('%',#{xcode},'%')
        </if>
        <if test="propValueNames!=null and propValueNames!=''">
            and bps.propvalue_names = like CONCAT('%',#{propValueNames},'%')
        </if>
        <if test="standard!=null and standard!=''">
            and bp.standard = like CONCAT('%',#{standard},'%')
        </if>
        <if test="ptypeType!=null and ptypeType!=''">
            and bp.ptype_type= like CONCAT('%',#{ptypeType},'%')
        </if>
        <if test="brandName!=null and brandName!=''">
            and band.brand_name= like CONCAT('%',#{brandName},'%')
        </if>
    </sql>
    <delete id="deleteMultiDetailsByRuleId">
        delete
        from pl_eshop_multi_stock_sync_detail
        where profile_id = #{profileId}
          and rule_id = #{ruleId}
    </delete>

    <delete id="deleteProduct">
        delete
        pro
        from pl_eshop_product pro
        left join pl_eshop_product_sku sku on pro.profile_id=sku.profile_id and pro.eshop_id=sku.eshop_id and pro.platform_num_id=sku.platform_num_id
        where pro.profile_id=#{profileId} and pro.eshop_id=#{eshopId} and sku.id is null
    </delete>
    <delete id="deleteNotUseSku">
        delete sk,ex,map,mk,md
        from pl_eshop_product_sku sk
        left join pl_eshop_product_sku_expand ex on sk.unique_id=ex.unique_id
        left join pl_eshop_product_sku_mapping map on map.profile_id=sk.profile_id and map.unique_id=sk.unique_id
        left join pl_eshop_product_mark mk on mk.profile_id=sk.profile_id and mk.eshop_id=sk.eshop_id and
        mk.unique_id=sk.unique_id
        left join pl_eshop_product_mark_data md on mk.profile_id=md.profile_id and md.mark_id=mk.id
        where sk.profile_id=#{profileId}
        and sk.eshop_id=#{eshopId}
        and sk.unique_id in
        <foreach collection="uniqueIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteProductMarks">
        delete mk,md
        from pl_eshop_product_mark mk
        left join pl_eshop_product_mark_data md on mk.profile_id=md.profile_id and mk.id=md.mark_id
        where mk.profile_id=#{profileId}
        and mk.unique_id in
        <foreach collection="uniqueIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </delete>

    <select id="querySkuList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuPageData">
        select sk.id as eshopProductSkuId,sk.profile_id, pro.platform_fullname,pe.otype_id,
        sk.platform_num_id,sk.platform_xcode,sk.platform_properties_name,sk.platform_sku_id,sk.platform_full_properties,
        if(sk.platform_pic_url!='',sk.platform_pic_url, pro.platform_pic_url) as platform_pic_url,
        sk.platform_unit_name,sk.platform_price,sk.unique_id,sk.platform_properties,
        ifnull(datediff(now(),sk.update_time),-1) as refreshProductIntervalDay,
        ifnull(datediff(now(),ex.last_new_order_time),-1) as downloadOrderIntervalDay,
        pe.fullname as eshopName,pe.eshop_type,
        bpf.fullbarcode as skuBarcode,
        bp.fullname as ptypeName,bp.pcategory,bp.ptype_type,bp.standard,bp.cost_price,bp.pcategory,
        bpx.xcode as xcode, bps.id as sku_id, bp.id as ptype_id, bpx.unit_id as unitId,
        ex.auto_sync_enabled,ex.mapping_type,
        pro.has_properties ,pro.platform_stock_state ,pro.platform_xcode as pmXcode,sk.platform_json
        <if test="queryStockRuleEnabled">
            ,map.sync_rule_id,
            if(map.sync_rule_id>0, ru.warehouse_stock_sync_enabled, dfru.warehouse_stock_sync_enabled) as
            warehouse_stock_sync_enabled,
            if(map.sync_rule_id>0, ru.eshop_multi_stock_sync_enabled, dfru.eshop_multi_stock_sync_enabled) as
            eshop_multi_stock_sync_enabled
            ,ifnull(ru.rule_name,'(默)网店默认规则') as rule_name
        </if>
        <if test="queryLocalPicEnabled or queryLocalPropEnabled">
            ,bps.pic_url as localPicUrl ,bps.prop_names,bps.propvalue_names as propValueNames
        </if>
        <if test="queryLocalBrand">
            ,band.brand_name
        </if>
        <if test="queryUnitNameEnabled">
            ,bpu.unit_name
        </if>
        from pl_eshop_product_sku sk
        left join pl_eshop_product pro on sk.profile_id = pro.profile_id and sk.eshop_id = pro.eshop_id and sk.platform_num_id = pro.platform_num_id
        left join pl_eshop_product_sku_expand ex on sk.unique_id=ex.unique_id and sk.profile_id=ex.profile_id
        left join pl_eshop_product_sku_mapping map on map.profile_id=sk.profile_id and map.unique_id=sk.unique_id
        left join base_ptype bp on bp.profile_id=sk.profile_id and bp.id=map.ptype_id
        left join base_ptype_sku bps on bps.profile_id=map.profile_id and bps.id=map.sku_id
        <if test="queryStockRuleEnabled">
            left join pl_eshop_stock_sync_rule ru on ru.profile_id=map.profile_id and ru.id = map.sync_rule_id and  ru.deleted=0
            left join pl_eshop_stock_sync_default_rule dfru on dfru.profile_id=map.profile_id and dfru.eshop_id=map.eshop_id
        </if>
        left join base_ptype_xcode bpx on bpx.profile_id=sk.profile_id and bpx.sku_id=map.sku_id and
        bpx.unit_id=map.unit_id and bpx.defaulted=1
        left join base_ptype_fullbarcode bpf on bpf.profile_id=map.profile_id and bpf.sku_id=map.sku_id and bpf.unit_id
        = map.unit_id and bpf.defaulted=1
        left join pl_eshop pe on pe.profile_id=pro.profile_id and pe.otype_id=pro.eshop_id
        <if test="queryUnitNameEnabled">
            left join base_ptype_unit bpu on bpu.profile_id=map.profile_id and bpu.id=map.unit_id
        </if>
        <if test="queryLocalBrand">
            LEFT JOIN base_brandtype band ON band.profile_id=bp.profile_id AND band.id=bp.brand_id
        </if>
        <include refid="whereSql"/>
        <if test="orderCase!=null and orderCase!=''">
            order by #{orderCase} #{orderTag}
        </if>
    </select>

    <select id="querySkuListCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_product_sku sk
        left join pl_eshop_product pro on sk.profile_id = pro.profile_id and sk.platform_num_id = pro.platform_num_id
        left join pl_eshop_product_sku_mapping map on map.profile_id=sk.profile_id and map.unique_id=sk.unique_id
        left join base_ptype_xcode bpx on bpx.profile_id=sk.profile_id and bpx.sku_id=map.sku_id and
        bpx.unit_id=map.unit_id and bpx.defaulted=1
        left join base_ptype bp on bp.profile_id=bpx.profile_id and bp.id=bpx.ptype_id
        <include refid="whereSql"/>
    </select>

    <select id="querySkuMappingListForRepairRelationState"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select map.id,
        sk.profile_id,sk.eshop_id,sk.platform_num_id,sk.platform_sku_id,sk.platform_xcode,sk.platform_properties_name,sk.platform_properties,
        map.xcode,map.ptype_id,map.pcategory,map.sku_id,map.unit_id,map.sync_rule_id,sk.unique_id,
        bp.id as realPtypeId,bpx.sku_id as realSkuId,bpx.unit_id as realUnitId,bpx.xcode as realXcode,
        bp.pcategory as realPcategory
        from pl_eshop_product_sku sk
        left join pl_eshop_product_sku_expand ex on sk.profile_id=ex.profile_id and sk.unique_id=ex.unique_id
        left join pl_eshop_product_sku_mapping map on sk.profile_id=map.profile_id and sk.unique_id=map.unique_id
        left join base_ptype_xcode bpx on sk.profile_id=bpx.profile_id and sk.platform_xcode=bpx.xcode
        left join base_ptype bp on bpx.profile_id=bp.profile_id and bpx.ptype_id=bp.id
        where sk.profile_id=#{profileId}
        and sk.eshop_id=#{otypeId}
        and ex.mapping_type=1
        <if test="pcategory==2">
            and bpx.info_type=1
        </if>
        <if test="pcategory!=2">
            and bpx.info_type=0
        </if>
        limit #{pageIndex},#{pageSize}
    </select>

    <select id="querySkuMappingListForRepairRelationStateCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_product_sku sk
        left join pl_eshop_product_sku_expand ex on sk.profile_id=ex.profile_id and sk.unique_id=ex.unique_id
        left join pl_eshop_product_sku_mapping map on sk.profile_id=map.profile_id and sk.unique_id=map.unique_id
        left join base_ptype_xcode bpx on sk.profile_id=bpx.profile_id and sk.platform_xcode=bpx.xcode
        where sk.profile_id=#{profileId}
        and sk.eshop_id=#{otypeId}
        and ex.mapping_type=1
        <if test="pcategory==2">
            and bpx.info_type=1
        </if>
        <if test="pcategory!=2">
            and bpx.info_type=0
        </if>

    </select>

    <select id="querySkuMappingForLocalModify"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select map.id,
        sk.profile_id,sk.eshop_id,sk.platform_num_id,sk.platform_sku_id,sk.platform_xcode,sk.platform_properties_name,sk.platform_properties,
        map.xcode,map.ptype_id,map.pcategory,map.sku_id,map.unit_id,map.sync_rule_id,sk.unique_id,
        bpx.ptype_id as realPtypeId,bpx.sku_id as realSkuId,bpx.unit_id as realUnitId,bpx.xcode as realXcode,
        bp.pcategory as realPcategory
        from pl_eshop_product_sku sk
        left join pl_eshop_product_sku_expand ex on sk.profile_id=ex.profile_id and sk.unique_id=ex.unique_id
        left join pl_eshop_product_sku_mapping map on sk.profile_id=map.profile_id and sk.unique_id=map.unique_id
        left join base_ptype_xcode bpx on map.profile_id=bpx.profile_id and map.ptype_id=bpx.ptype_id and
        map.sku_id=bpx.sku_id and bpx.unit_id=map.unit_id and bpx.defaulted=1
        left join base_ptype_sku sku on sku.profile_id=bpx.profile_id and bpx.sku_id=sku.id
        left join base_ptype bp on bpx.profile_id=bp.profile_id and bpx.ptype_id=bp.id
        where sk.profile_id=#{profileId}
        and sk.eshop_id=#{otypeId}
        and bp.deleted=0
        and bp.stoped=0
        and sku.deleted=0
        and ex.mapping_type=0
        <if test="xcodeList!=null and xcodeList.size()>0">
            and map.xcode in
            <foreach collection="xcodeList" item="xcode" open="(" separator="," close=")">
                #{xcode}
            </foreach>
        </if>
        <if test="ptypeIdList!=null and ptypeIdList.size()>0">
            and map.ptype_id in
            <foreach collection="ptypeIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        union all
        select map.id,
        sk.profile_id,sk.eshop_id,sk.platform_num_id,sk.platform_sku_id,sk.platform_xcode,sk.platform_properties_name,sk.platform_properties,
        map.xcode,map.ptype_id,map.pcategory,map.sku_id,map.unit_id,map.sync_rule_id,sk.unique_id,
        bpx.ptype_id as realPtypeId,bpx.sku_id as realSkuId,bpx.unit_id as realUnitId,bpx.xcode as realXcode,
        bp.pcategory as realPcategory
        from pl_eshop_product_sku sk
        left join pl_eshop_product_sku_expand ex on sk.profile_id=ex.profile_id and sk.unique_id=ex.unique_id
        left join pl_eshop_product_sku_mapping map on sk.profile_id=map.profile_id and sk.unique_id=map.unique_id
        left join base_ptype_xcode bpx on map.profile_id=bpx.profile_id and sk.platform_xcode=bpx.xcode
        left join base_ptype_sku sku on sku.profile_id=bpx.profile_id and bpx.sku_id=sku.id
        left join base_ptype bp on bpx.profile_id=bp.profile_id and bpx.ptype_id=bp.id
        where sk.profile_id=#{profileId}
        and sk.eshop_id=#{otypeId}
        and bp.deleted=0
        and bp.stoped=0
        and sku.deleted=0
        and ex.mapping_type=1
        <if test="xcodeList!=null and xcodeList.size()>0">
            and map.xcode in
            <foreach collection="xcodeList" item="xcode" open="(" separator="," close=")">
                #{xcode}
            </foreach>
        </if>
        <if test="ptypeIdList!=null and ptypeIdList.size()>0">
            and map.ptype_id in
            <foreach collection="ptypeIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="countEshopProductMarkByMarkCode" resultType="java.lang.Integer">
        select count(1) from pl_eshop_product_mark where profile_id=#{profileId} AND mark_code=#{markCode} and eshop_id
        = #{eshopId}
        <if test="uniqueId != null and uniqueId != ''">
            AND unique_id=#{uniqueId}
        </if>
        <if test="platformNumId != null and platformNumId != ''">
            AND platform_num_id=#{platformNumId}
        </if>
    </select>

    <select id="querySkuAttrRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuAttrRelation">
        select id, profile_id, eshop_id, platform_prop, local_prop, prop_id, row_index, prop_group_count
        from pl_eshop_product_attr_relation
        where profile_id=#{profileId}
        <if test="eshopId != null">
            and eshop_id=#{eshopId}
        </if>
    </select>

    <select id="queryEshopProductMarksByNumIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select profile_id,eshop_id,platform_num_id,mark_code,platform_sku_id,unique_id from pl_eshop_product_mark
        where profile_id=#{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumIds != null and platformNumIds.size() >0 ">
            AND platform_num_id in
            <foreach collection="platformNumIds" item="numid" close=")" open="(" separator=",">
                #{numid}
            </foreach>
        </if>
    </select>

    <select id="loadLocalMappingList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select map.id, map.profile_id, map.eshop_id, platform_num_id, platform_sku_id, platform_properties_name,
        platform_xcode,
        ptype_id, unit_id, sku_id,map.unique_id, sync_rule_id,pcategory, xcode,
        ex.mapping_type,ifnull(map.ptype_id,0)>0 as isBind
        from pl_eshop_product_sku_mapping map
        left join pl_eshop_product_sku_expand ex on ex.profile_id=map.profile_id and map.unique_id=ex.unique_id
        where map.profile_id=#{profileId}
        <if test="otypeIds!=null and otypeIds.size()>0">
            and map.eshop_id in
            <foreach collection="otypeIds" item="otypeId" index="i" separator="," open="(" close=")">
                #{otypeId}
            </foreach>
        </if>
        <if test="platformNumIdList!=null and platformNumIdList.size()>0">
            and map.platform_num_id in
            <foreach collection="platformNumIdList" item="numId" index="i" separator="," open="(" close=")">
                #{numId}
            </foreach>
        </if>
        <if test="uniqueIdList!=null and uniqueIdList.size()>0">
            and map.unique_id in
            <foreach collection="uniqueIdList" item="uniqueId" index="i" separator="," open="(" close=")">
                #{uniqueId}
            </foreach>
        </if>

    </select>
    <select id="queryLocalXcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode">
        select bpx.ptype_id,bpx.sku_id,bpx.unit_id,bpx.xcode,bpx.info_type,bp.fullname as ptypeName,sk.propvalue_names
        from base_ptype_xcode bpx
            left join base_ptype bp on bpx.profile_id=bp.profile_id and bpx.ptype_id=bp.id
            left join base_ptype_sku sk on sk.profile_id=bpx.profile_id and sk.id=bpx.sku_id
        where bpx.profile_id=#{profileId}
        and bpx.xcode in
        <foreach collection="xcodeList" item="xcode" open="(" separator="," close=")">
            #{xcode}
        </foreach>
    </select>
    <select id="queryMultiDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.MultiStockSyncDetail">
        select id,
               profile_id,
               rule_id,
               platform_multi_type,
               platform_multi_id,
               sync_type,
               platform_multi_name,
               sync_cron,
               fix_qty
        from pl_eshop_multi_stock_sync_detail
        where profile_id = #{profileId}
          and rule_id = #{ruleId}
    </select>

    <select id="queryNeedDeleteList" resultType="java.lang.String">
        select ex.unique_id
        from pl_eshop_product_sku_expand ex
        where ex.profile_id = #{profileId}
          and ex.eshop_id = #{eshopId}
          and ex.update_time <![CDATA[<=]]> #{clearDate}
    </select>


    <select id="querySkusByEshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_xcode       as pmplatformXcode,
               m.platform_fullname    as platfullname,
               sku.id,
               m.platform_price       as price,
               m.has_properties,
               m.platform_pic_url     as platformPicUrl,
               sku.profile_Id,
               expand.mark,
               sku.eshop_id,
               expand.mapping_type,
               sku.unique_id,
               expand.download_enable,
               sku.platform_sku_id,
               sku.platform_Num_Id,
               m.default_sku_id,
               sku.platform_properties_Name,
               sku.platform_properties,
               0                      as isredundant,
               sku.platform_xcode,
               sku.platform_xcode     as
                                         platxcode,
               sku.platform_full_properties,
               sku.qty,
               sku.platform_modified_time,
               m.platform_stock_state as
                                         stock_state,
               expand.ready_sku_name,
               expand.ready_sku_xcode,
               expand.ready_pfullname,
               expand.ready_pusercode,
               sku.platform_pic_url   as picUrl,
               pepsrc.rule_id         as sync_rule_id,
               m.category_id
        from pl_eshop_product_sku sku
                 join pl_eshop_product m
                      on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
                         m.eshop_id = sku.eshop_id
                 left join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id =
                                                                                                      expand.profile_id
                 left join pl_eshop_product_sku_rule_config pepsrc
                           on pepsrc.profile_id = sku.profile_id and pepsrc.eshop_id = sku.eshop_id
                               and pepsrc.platform_num_id = sku.platform_num_id
                               and pepsrc.platform_properties = sku.platform_properties_name
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and sku.storage_type = 0
    </select>

    <select id="querySkusByUniqueIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_xcode as pmplatformXcode,
        m.platform_fullname as platfullname,
        sku.id,
        m.platform_price as price,
        m.has_properties,
        m.platform_pic_url as platformPicUrl,
        sku.profile_Id,
        expand.mark,
        sku.eshop_id,
        expand.mapping_type,
        sku.unique_id,
        expand.download_enable,
        sku.platform_sku_id,
        sku.platform_Num_Id,
        m.default_sku_id,
        sku.platform_properties_Name,sku.platform_properties,0 as isredundant,sku.platform_xcode,sku.platform_xcode as
        platxcode,sku.platform_full_properties,sku.qty,
        sku.platform_modified_time,m.platform_stock_state as
        stock_state,expand.ready_sku_name,expand.ready_sku_xcode,expand.ready_pfullname,
        expand.ready_pusercode,sku.platform_pic_url as picUrl,pepsrc.rule_id as sync_rule_id,m.category_id
        from pl_eshop_product_sku sku
        join pl_eshop_product m on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        left join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=
        expand.profile_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=sku.profile_id and pepsrc.eshop_id=sku.eshop_id
        and pepsrc.platform_num_id=sku.platform_num_id
        and pepsrc.platform_properties=sku.platform_properties_name
        where sku.profile_id = #{profileId}
        and sku.unique_id in
        <foreach collection="uniqueIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        and sku.storage_type=0
    </select>
    <select id="getProductMarkList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select mk.id,mk.profile_id,mk.eshop_id,
        mk.mark_code,mk.memo,md.big_data as product_mark_big_data,mk.bubble,
        mk.platform_num_id,mk.platform_sku_id,mk.unique_id
        from pl_eshop_product_mark mk
        left join pl_eshop_product_mark_data md on md.profile_id=mk.profile_id and md.mark_id=mk.id
        where mk.profile_id=#{profileId}
        and mk.unique_id in
        <foreach collection="uniqueIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>


    <insert id="doFillSkuMappingFullInfo">
        replace
        into pl_eshop_product_sku_mapping(id,profile_id,eshop_id,platform_num_id,platform_sku_id,platform_properties_name,platform_xcode,unique_id)
        select sk.id,
               sk.profile_id,
               sk.eshop_id,
               sk.platform_num_id,
               sk.platform_sku_id,
               sk.platform_properties_name,
               sk.platform_xcode,
               sk.unique_id
        from pl_eshop_product_sku sk
                 left join pl_eshop_product_sku_mapping map
                           on sk.profile_id = map.profile_id and sk.eshop_id = map.eshop_id and
                              map.unique_id = sk.unique_id
        where sk.profile_id = #{profileId}
          and sk.eshop_id = #{otypeId}
          and map.id is null;

        update pl_eshop_product_sku_mapping map
            left join pl_eshop_product_sku sk
        on map.profile_id=sk.profile_id and sk.eshop_id= map.eshop_id and sk.unique_id= map.unique_id
            set map.platform_num_id=sk.platform_num_id, map.platform_sku_id=sk.platform_sku_id, map.platform_xcode=sk.platform_xcode, map.platform_properties_name=sk.platform_properties_name
        where map.profile_id=#{profileId}
          and map.eshop_id=#{otypeId}
          and map.platform_num_id ='';
    </insert>
    <insert id="saveDefaultRule">
        REPLACE
        INTO `pl_eshop_stock_sync_default_rule`
        (`id`, rule_cron, zero_qty_sync_enabled, eshop_id, profile_id, ktype_ids, auto_sync_enabled,auto_sync_when_mapping_changed,
        warehouse_stock_sync_enabled,eshop_multi_stock_sync_enabled)
        VALUES (
        #{rule.id},
        #{rule.ruleCron},
        ifnull
        (
        #{rule.zeroQtySyncEnabled},
        0
        ),
        #{rule.otypeId},
        #{profileId},
        #{rule.ktypeIds},
        ifnull
        (
        #{rule.autoSyncEnabled},
        0
        ),
        ifnull
        (
        #{rule.autoSyncWhenMappingChanged},
        0
        ),
        ifnull
        (
        #{rule.warehouseStockSyncEnabled},
        0
        ),
        ifnull
        (
        #{rule.eshopMultiStockSyncEnabled},
        0
        )
        );
    </insert>
    <update id="updateDefaultRule">
        update pl_eshop_stock_sync_default_rule
        set rule_cron=#{rule.ruleCron},
            zero_qty_sync_enabled=ifnull(#{rule.zeroQtySyncEnabled}, 0),
            ktype_ids=#{rule.ktypeIds},
            auto_sync_when_mapping_changed=ifnull(#{rule.autoSyncWhenMappingChanged}, 0),
            warehouse_stock_sync_enabled= ifnull(#{rule.warehouseStockSyncEnabled}, 0),
            eshop_multi_stock_sync_enabled= ifnull(#{rule.eshopMultiStockSyncEnabled}, 0)
        where profile_id = #{profileId}
          and id = #{rule.id}
    </update>
    <insert id="batchInsertMultiStockSyncDetails">
        REPLACE
        INTO `pl_eshop_multi_stock_sync_detail`
        (`profile_id`,`id`, rule_id,platform_multi_type, platform_multi_id, platform_multi_name,sync_type, sync_cron,
        fix_qty)
        VALUES
        <foreach collection="multiStockSyncDetailList" item="item" separator=",">
            (#{profileId},#{item.id},#{item.ruleId},ifnull(#{item.platformMultiType},0),#{item.platformMultiId},#{item.platformMultiName},#{item.syncType},#{item.syncCron},#{item.fixQty})
        </foreach>
    </insert>

    <update id="updateSkuMapping">
        update pl_eshop_product_sku_mapping
        <set>
            <trim prefix="ptype_id=CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id=#{item.id} THEN #{item.realPtypeId}
                </foreach>
            </trim>
            <trim prefix="sku_id=CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id=#{item.id} THEN #{item.realSkuId}
                </foreach>
            </trim>
            <trim prefix="unit_id=CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id=#{item.id} THEN #{item.realUnitId}
                </foreach>
            </trim>
            <trim prefix="pcategory=CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id=#{item.id} THEN #{item.realPcategory}
                </foreach>
            </trim>
            <trim prefix="xcode=CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id=#{item.id} THEN #{item.realXcode}
                </foreach>
            </trim>
        </set>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>

    </update>

    <update id="bindSkuMapping">
        insert into pl_eshop_product_sku_mapping
        (id, profile_id, eshop_id, platform_num_id, platform_sku_id, platform_properties_name, platform_xcode, ptype_id,
        sku_id, unit_id, xcode, pcategory, unique_id)
        values
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id},#{item.profileId},#{item.eshopId},#{item.platformNumId},#{item.platformSkuId},#{item.platformPropertiesName},#{item.platformXcode},#{item.ptypeId},#{item.skuId},#{item.unitId},#{item.xcode},#{item.pcategory},#{item.uniqueId}
        </foreach>
        ON DUPLICATE KEY UPDATE
        ptype_id = VALUES(ptype_id),
        sku_id = VALUES(sku_id),
        unit_id = VALUES(unit_id),
        xcode = VALUES(xcode),
        pcategory = VALUES(pcategory),
        platform_sku_id = VALUES(platform_sku_id),
        platform_xcode = VALUES(platform_xcode)
    </update>

    <update id="bindSkuMappingExpand">
        <foreach collection="list" item="item" separator=";">
            update pl_eshop_product_sku_expand
            set mapping_type = #{item.mappingType}
            where profile_id=#{item.profileId} and eshop_id=#{item.eshopId} and unique_id=#{item.uniqueId}
        </foreach>
    </update>

    <update id="updateSkuExpandMappingType">
        update pl_eshop_product_sku_expand set mapping_type=#{mappingTypeState}
        where profile_id=#{profileId}
        and unique_id in
        <foreach collection="uniqueIds" item="item" index="i" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateProductRelationState">
        update pl_eshop_product pro
        join (select count(1) relationTotal, platform_num_id
        from pl_eshop_product_sku_mapping
        where profile_id=#{profileId}
        and eshop_id=#{eshopId}
        and platform_num_id in
        <foreach collection="platformNumIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and ptype_id>0
        group by platform_num_id) as relation on relation.platform_num_id=pro.platform_num_id
        join (select count(1) total, platform_num_id
        from pl_eshop_product_sku_mapping
        where profile_id=#{profileId}
        and eshop_id=#{eshopId}
        and platform_num_id in
        <foreach collection="platformNumIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by platform_num_id) as al on al.platform_num_id=pro.platform_num_id
        set pro.relation_state = (case when relation.relationTotal=0 then 0 when al.total > relation.relationTotal then
        1 else 2 end)
        where pro.profile_id=#{profileId} and pro.eshop_id=#{eshopId} and pro.platform_num_id in
        <foreach collection="platformNumIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateProductSkuMappingXcodeStatus">
        update pl_eshop_product_sku_expand
        set update_xcode_status=#{xCodeStatus}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </update>
    <update id="updateProductMappingXcode">
        update pl_eshop_product
        set platform_xcode=#{xCode}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_Num_Id = #{platformNumId}
    </update>
    <update id="updateProductSkuMappingXcode">
        update pl_eshop_product_sku
        set platform_xcode=#{xCode}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_Num_Id = #{platformNumId}
          and platform_sku_id = #{platformSkuId}
    </update>
    <update id="batchUpdateStockSyncState">
        update pl_eshop_product_sku_expand
        set auto_sync_enabled=#{isOpen}
        where profile_id = #{profileId}
        and unique_id in
        <foreach collection="skuPageList" item="item" open="(" separator="," close=")">
            #{item.uniqueId}
        </foreach>
    </update>
    <update id="batchUpdateProductMappingXcode">
        <foreach collection="skuPageList" item="item" separator=";">
            UPDATE pl_eshop_product SET platform_xcode=#{item.platformXcode}
            where profile_id = #{profileId}
            and eshop_id = #{item.otypeId}
            and platform_Num_Id = #{item.platformNumId}
        </foreach>
    </update>
    <update id="batchUpdateProductSkuMappingXcode">
        <foreach collection="skuPageList" item="item" separator=";">
            UPDATE pl_eshop_product_sku
            SET platform_xcode=#{item.platformXcode}
            where profile_id = #{profileId}
            and eshop_id = #{item.otypeId}
            and platform_Num_Id = #{item.platformNumId}
            and platform_sku_id = #{item.platformSkuId}
        </foreach>
    </update>
    <update id="bindRuleId">
        update pl_eshop_product_sku_mapping
        set sync_rule_id=#{ruleId}
        where profile_id = #{profileId}
        and unique_id in
        <foreach collection="uniqueIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <insert id="insertProductSkuAttrRelation">
        insert into pl_eshop_product_attr_relation
        (id,
        profile_id,
        eshop_id,
        platform_prop,
        row_index,
        prop_group_count,
        local_prop,
        prop_id)
        values
        <foreach collection="skuAttrRelationList" item="item" index="index" separator=",">
            (#{item.id}, #{item.profileId},
            #{item.eshopId},#{item.platformProp},#{item.rowIndex},#{item.propGroupCount},#{item.localProp},#{item.propId})
        </foreach>
        ON DUPLICATE KEY
        UPDATE
        local_prop= values(local_prop),
        prop_id=values(prop_id)
    </insert>

    <insert id="insertIgnoreProductSkuAttrRelation">
        INSERT IGNORE INTO pl_eshop_product_attr_relation
        (id,
        profile_id,
        eshop_id,
        platform_prop,
        row_index,
        prop_group_count,
        local_prop,
        prop_id)
        values
        <foreach collection="skuAttrRelationList" item="item" index="index" separator=",">
            (#{item.id}, #{item.profileId},
            #{item.eshopId},#{item.platformProp},#{item.rowIndex},#{item.propGroupCount},#{item.localProp},#{item.propId})
        </foreach>
    </insert>

    <insert id="batchInsertOrUpdateProductSkuMapping">
        insert into pl_eshop_product_sku_mapping
        (id,
        eshop_id,
        profile_id,
        pcategory,
        ptype_id,
        sku_id,
        unit_id,
        xcode,
        unique_id,
        platform_full_properties_name,
        platform_num_id,
        platform_sku_id,
        platform_properties_name,
        platform_xcode,
        platform_properties,
        platform_modified_time)
        VALUES
        <foreach item="item" index="index" collection="skuMappings" separator=",">
            (#{item.id}, #{item.eshopId}, #{item.profileId}, #{item.pcategory}, #{item.ptypeId}, #{item.skuId},
            #{item.unitId}, #{item.xcode},
            #{item.uniqueId}, #{item.platformPropertiesName},
            #{item.platformNumId},#{item.platformSkuId},#{item.platformPropertiesName},
            #{item.platformXcode},#{item.platformProperties},#{item.platformModifiedTime})
        </foreach>

        ON DUPLICATE KEY
        UPDATE pcategory= VALUES(pcategory),
        ptype_id= VALUES(ptype_id),
        sku_id= VALUES(sku_id),
        unit_id= VALUES(unit_id),
        xcode= VALUES(xcode),
        platform_full_properties_name = VALUES(platform_full_properties_name),
        platform_num_id = VALUES(platform_num_id),
        platform_sku_id = VALUES(platform_sku_id),
        platform_properties_name = VALUES(platform_properties_name),
        platform_xcode = VALUES(platform_xcode),
        platform_properties = VALUES(platform_properties),
        platform_modified_time = VALUES(platform_modified_time)
    </insert>

    <delete id="batchDeleteSkuMapping">
        update pl_eshop_product_sku_mapping
        set ptype_id=0,
            sku_id=0,
            unit_id=0,
            xcode=''
        where profile_id = #{profileId}
        and unique_id in
        <foreach item="uniqueId" index="index" collection="uniqueIds" separator="," open="(" close=")">
            #{uniqueId}
        </foreach>
    </delete>

    <select id="queryEshopSkuMappingCount"
            resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_product_sku sku
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = sku.profile_id and mapping.eshop_id = sku.eshop_id and
                              mapping.unique_id = sku.unique_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and mapping.ptype_id > 0
    </select>
    <select id="queryEshopSkuMappingByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.*,
               mapping.ptype_id,
               mapping.sku_id,
               mapping.unit_id,
               mapping.pcategory,
               mapping.xcode,
               mapping.platform_full_properties_name
        from pl_eshop_product_sku sku
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = sku.profile_id and mapping.eshop_id = sku.eshop_id and
                              mapping.unique_id = sku.unique_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and mapping.ptype_id > 0
            limit #{currentIndex}, #{pageSize}
    </select>

    <select id="queryEshopProductMarkCount"
            resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_product_mark mk
                 left join pl_eshop_product_mark_data md on md.profile_id=mk.profile_id and md.mark_id=mk.id
        where mk.profile_id = #{profileId}
          and mk.eshop_id = #{eshopId}
    </select>
    <select id="queryEshopProductMarkByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select mk.id,mk.profile_id,mk.eshop_id,
               mk.mark_code,mk.memo,mk.bubble,
               mk.platform_num_id,mk.platform_sku_id,mk.unique_id,mk.show_type,mk.is_send_by_days,mk.is_send_on_date,
               mk.pre_send_by_days,mk.pre_send_on_date,mk.frequency_type,mk.frequency_blank_num,mk.frequency_blank_cycle,
               mk.frequency_blank_count,mk.frequency_assign_cycle,mk.frequency_assign_count,mk.frequency_assign_days,
               mk.frequency_phase,mk.is_sale_qty_need_to_occupy,mk.platform_properties_name,md.id as mark_data_id,md.big_data as product_mark_big_data
        from pl_eshop_product_mark mk
                 left join pl_eshop_product_mark_data md on md.profile_id=mk.profile_id and md.mark_id=mk.id
        where mk.profile_id = #{profileId}
          and mk.eshop_id = #{eshopId}
            limit #{currentIndex}, #{pageSize}
    </select>
</mapper>