<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.BalanceMapper">
    <select id="getDetail" resultType="com.wsgjp.ct.bill.core.handle.entity.dao.BillBalanceDetailDao">
        select
            0 as id,
            0 as vchcode,
            bill_date,
            profile_id,
            vchcode as business_vchcode,
            business_type as balance_business_type,
            btype_id,
            currency_id,
            0 as business_atype_id,
            1 as detail_type,
            (currency_bill_total*-1) as currency_payment_total,
            0 as currencyDepositTotal,
            0 as currencyAdvanceTotal,
            0 as currencyPreferentialTotal
        from acc_bill_core where profile_id=#{profileId} and vchcode=#{vchcode};
    </select>
</mapper>