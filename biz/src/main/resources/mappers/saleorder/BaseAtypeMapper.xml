<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.BaseAtypeMapper">

    <select id="getAtypeByName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Atype">
        select *
        from base_atype
        where fullname = #{arg0}
          and profile_id = #{arg1}
    </select>
</mapper>