<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.SyncOrderMapper">
    <insert id="insertTaskLog">
        insert into pl_eshop_sale_order_sync_task
        (`id`, `profile_id`, `eshop_id`, `sync_state`, `sync_type`, `sync_task_begin_time`,
         `sync_task_end_time`, `sync_task_time_delay`, `sync_exec_begin_time`, `sync_exec_end_time`,
         `sync_exec_time_consuming`, `sync_order_count`, `sync_order_insert_count`, `sync_order_update_count`,
         `error_message`, `retry_count`, `parent_id`, `error_code`)
            value
            (#{id},#{profileId},#{eshopId},#{syncState},#{syncType},#{syncTaskBeginTime},#{syncTaskEndTime},#{syncTaskTimeDelay},
            #{syncExecBeginTime},#{syncExecEndTime},#{syncExecTimeConsuming},#{syncOrderCount},#{syncOrderInsertCount},#{syncOrderUpdateCount},
            #{errorMessage},#{retryCount}, #{parentId},#{errorCode})
    </insert>

    <update id="modifyTaskLog">
        UPDATE`pl_eshop_sale_order_sync_task`
        SET
        `sync_state` = #{syncState},
        `sync_type` = #{syncType},
        `sync_task_time_delay` = #{syncTaskTimeDelay},
        <if test="syncExecBeginTime!=null">
            `sync_exec_begin_time` = #{syncExecBeginTime},
        </if>
        <if test="syncExecEndTime!=null">
            `sync_exec_end_time` = #{syncExecEndTime},
        </if>
        `sync_exec_time_consuming` = #{syncExecTimeConsuming},
        `sync_order_count` = #{syncOrderCount},
        `sync_order_insert_count` = #{syncOrderInsertCount},
        `sync_order_update_count` = #{syncOrderUpdateCount},
        `error_message` = #{errorMessage},
        `retry_count` = #{retryCount}
        WHERE id = #{id} and profile_id=#{profileId}
    </update>

    <select id="getNeedRetryTaskLogs"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.log.EshopSaleOrderSyncTaskLog">
        SELECT `id`,
               `profile_id`,
               `eshop_id`,
               `sync_state`,
               `sync_type`,
               `sync_task_begin_time`,
               `sync_task_end_time`,
               `sync_task_time_delay`,
               `sync_exec_begin_time`,
               `sync_exec_end_time`,
               `sync_exec_time_consuming`,
               `sync_order_count`,
               `sync_order_insert_count`,
               `sync_order_update_count`,
               `error_message`,
               `retry_count`,
               `parent_id`,
               `error_code`
        from `pl_eshop_sale_order_sync_task`
        where `profile_id` = #{profileId}
          and `eshop_id` = #{eshopId}
          and parent_id!=null
          and parent_id!=0
          and `sync_type` = 5
          and `retry_count` &gt; 0
          and error_code != 0
        and update_time &gt;= #{lastTime}
    </select>
</mapper>