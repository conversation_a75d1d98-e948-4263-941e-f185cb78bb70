<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderEshopRefundMapper">
    <sql id="refundInfo">
        refund
        .
        id
        ,
        refund.trade_refund_order_id,
        refund.trade_refund_order_number,
        refund.profile_id,
        refund.otype_id,
        refund.buyer_id,
        refund.create_time,
        refund.refund_create_time,
        refund.refund_modify_time,
        refund.trade_create_time,
        refund.trade_finish_time,
        refund.trade_pay_time,
        refund.order_fee,
        case
        when refund.refund_type = 0 and refund.refund_phase = 0 then 5
        else refund.refund_type
        end
        as refund_type,
        refund.create_type,
        refund.confirm_state,
        refund.confirm_time,
        refund.confirm_remark,
        refund.confirm_etype_id,
        refund.receive_state,
        refund.receive_buyer_id,
        refund.receive_remark,
        refund.refund_process_state,
        refund.refund_process_time,
        refund.receive_time,
        refund.pay_state,
        refund.pay_account,
        refund.pay_time,
        refund.pay_etype_id,
        refund.pay_number,
        refund.receive_account,
        refund.refund_apply_total,
        refund.refund_apply_taxed_total,
        refund.refund_apply_tax_total,
        refund.refund_apply_freight_fee,
        refund.refund_apply_mall_fee,
        refund.refund_apply_service_fee,
        refund.update_time,
        refund.ktype_id,
        refund.refund_state,
        refund.refund_phase,
        refund.refund_statement,
        refund.trade_status,
        refund.deleted,
        refund.trade_order_id,
        refund.eshop_order_id,
        refund.no_detail,
        refund.bill_vchcode,
        refund.bill_poseted,
        refund.`refund_apply_taxed_total` as refundTotal,
        refund.refund_type                as 'refund_type_enum',
        refund.refund_finish_time
    </sql>
    <sql id="refundListColumn">
        distinct refund.id,
        refund.trade_refund_order_id,
        refund.trade_refund_order_number,
        refund.profile_id,
        refund.otype_id,
        refund.buyer_id,
        refund.create_time as create_time,
        refund.cost_state,
        refund.etype_id,
        e.fullname as etype_name,
        refund.refund_create_time,
        refund.refund_modify_time,
        refund.trade_create_time,
        refund.trade_finish_time,
        refund.has_submit_to_wms,
        refund.trade_pay_time,
        refund.order_fee,
        refund.business_type,
        ba.id as atypeId,
        ba.fullname as atypeName,
        case
        when refund.refund_type = 0 and refund.refund_phase = 0 then 5
        else refund.refund_type
        end
        as refund_type,
        refund.create_type,
        refund.confirm_state,
        refund.confirm_time,
        refund.confirm_remark,
        refund.confirm_etype_id,
        refund.receive_state,
        refund.receive_buyer_id,
        refund.receive_remark,
        refund.refund_process_state,
        refund.refund_process_time,
        refund.receive_time,
        refund.pay_state,
        refund.pay_account,
        refund.pay_time,
        refund.pay_etype_id,
        refund.pay_number,
        refund.receive_account,
        refund.refund_apply_total,
        refund.refund_apply_taxed_total,
        refund.refund_apply_tax_total,
        refund.refund_apply_freight_fee,
        refund.refund_apply_mall_fee,
        refund.refund_apply_service_fee,
        refund.update_time,
        refund.ktype_id,
        refund.old_ktype_id,
        refund.refund_state,
        refund.refund_phase,
        refund.refund_statement,
        refund.trade_status,
        refund.deleted,
        refund.trade_order_id,
        refund.eshop_order_id,
        refund.no_detail,
        refund.bill_vchcode,
        refund.bill_poseted,
        refund.`refund_apply_taxed_total` as refundTotal,
        case
        when refund.btype_id = 0 or refund.btype_id is null
        then order.btype_id
        else refund.btype_id
        end
        as btypeId,
        refund.refund_type as 'refund_type_enum',
        IFNULL(reason.refund_reason, refund.refund_reason) as refund_reason,
        IFNULL(reason.id, refund.refund_reason) as reason_id,
        refund.has_edit,
        refund.mapping_state,
        case refund.bill_total
        when 0 then refund.refund_apply_taxed_total
        else refund.bill_total
        end
        as bill_total,
        case refund.bill_service_fee
        when 0 then refund.refund_apply_service_fee
        else refund.bill_service_fee
        end
        as bill_service_fee,
        refund.order_etype_id,
        e.fullname as orderEtypeName,
        ifnull(e.fullname, '') as order_etype_name,
        ifnull(receiveBuyer.ai, buyer.ai) as 'buyer.ai',
        ifnull(receiveBuyer.addri, buyer.addri) as 'buyer.addri',
        ifnull(receiveBuyer.ri, buyer.ri) as 'buyer.ri',
        buyer.di as 'buyer.di',
        ifnull(receiveBuyer.customer_receiver, buyer.customer_receiver) as 'buyer.customer_receiver',
        ifnull(receiveBuyer.customer_receiver_mobile, buyer.customer_receiver_mobile) as 'buyer.customer_receiver_mobile',
        ifnull(receiveBuyer.customer_receiver_country, buyer.customer_receiver_country) as 'buyer.customer_receiver_country',
        ifnull(receiveBuyer.customer_receiver_city, buyer.customer_receiver_city) as 'buyer.customer_receiver_city',
        ifnull(receiveBuyer.customer_receiver_province, buyer.customer_receiver_province) as 'buyer.customer_receiver_province',
        ifnull(receiveBuyer.customer_receiver_district, buyer.customer_receiver_district) as 'buyer.customer_receiver_district',
        ifnull(receiveBuyer.customer_receiver_town, buyer.customer_receiver_town) as 'buyer.customer_receiver_town',
        ifnull(receiveBuyer.customer_receiver_address, buyer.customer_receiver_address) as 'buyer.customer_receiver_address',
        ifnull(receiveBuyer.customer_receiver_full_address, buyer.customer_receiver_full_address) as 'buyer.customer_receiver_full_address',
        ifnull(receiveBuyer.customer_shop_account, buyer.customer_shop_account) as 'buyer.customer_shop_account',
        receiveBuyer.customer_shop_account as 'receiveBuyer.customer_shop_account',
        receiveBuyer.ai as 'receiveBuyer.ai',
        receiveBuyer.di as 'receiveBuyer.di',
        receiveBuyer.addri as 'receiveBuyer.addri',
        receiveBuyer.ri as 'receiveBuyer.ri',
        receiveBuyer.customer_receiver as 'receiveBuyer.customer_receiver',
        receiveBuyer.customer_receiver_mobile as 'receiveBuyer.customer_receiver_mobile',
        receiveBuyer.customer_receiver_country as 'receiveBuyer.customer_receiver_country',
        receiveBuyer.customer_receiver_city as 'receiveBuyer.customer_receiver_city',
        receiveBuyer.customer_receiver_province as 'receiveBuyer.customer_receiver_province',
        receiveBuyer.customer_receiver_district as 'receiveBuyer.customer_receiver_district',
        receiveBuyer.customer_receiver_town as 'receiveBuyer.customer_receiver_town',
        receiveBuyer.customer_receiver_address as 'receiveBuyer.customer_receiver_address',
        receiveBuyer.customer_receiver_full_address as 'receiveBuyer.customer_receiver_full_address',
        eshop.fullname as otype_name,
        eshop.fullname as 'eshopInfo.fullname',
        eshop.eshop_type as 'eshopInfo.eshopType',
        order.seller_memo as 'saleOrder.seller_memo',
        order.buyer_message as 'saleOrder.buyer_message',
        order.seller_flag as 'saleOrder.seller_flag',
        order.mapping_state as `saleOrder.mapping_state`,
        order.platform_store_id as 'saleOrder.platform_store_id',
        order.ktype_id as `saleOrder.ktype_id`,
        order.deliver_process_type as `saleOrder.deliver_process_type`,
        order.dised_taxed_total as `disedTaxedTotal`,
        refund.platform_parent_order_id,
        refund.eshop_order_id as eshopOrderId,
        ktype1.fullname as 'ktype_name',
        ktype2.fullname as 'oldKtypeName',
        freight.freight_name as 'checkinFreightName',
        freight.freight_no as 'checkinFreightBillNo',
        refund.memo as memo,
        freight.freight_name as refundCompany,
        freight.freight_no as refundCompanyNumber,
        freight.freight_btype_id as freightBtypeId,
        freight.freight_code as freightCode,
        has_commit_to_distributor,
        refund.platform_refund_state,
        refund.platform_confirm_state,
        refund.platform_change_state,
        refund.platform_return_state,
        refund.supplier_id,
        e.dtype_id,
        timing.id as 'refundTiming.id',
        timing.profile_id as 'refundTiming.profileId',
        timing.eshop_id as 'refundTiming.eshopId',
        timing.sys_promised_confirm_time as 'refundTiming.sysPromisedConfirmTime',
        timing.promised_confirm_time as 'refundTiming.promisedConfirmTime',
        timing.promised_agree_time as 'refundTiming.promisedAgreeTime',
        timing.promised_deliver_time as 'refundTiming.promisedDeliverTime',
        timing.promised_receive_time as 'refundTiming.promisedReceiveTime',
        timing.refund_id as 'refundTiming.refundId',
        refund.refund_duty_ids,
        e2.fullname as confirmEtypeName,
        refund.refund_finish_time,
        exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and profile_id = refund.profile_id) as related_checkin,
        refund_extend.platform_refund_version,refund_extend.platform_auto_intercept_agree,
        refund_extend.receive_etype_id,refund_extend.process_etype_id,
        refund_extend.order_detail_total,
        perdb.buyer_dised_taxed_total as distributionDisedTaxedTotal,
        refund_extend.return_sync_state,
        if(timing.promised_receive_time &lt; if( timing.promised_deliver_time &lt; if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)),timing.promised_deliver_time,if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time))),timing.promised_receive_time,        if( timing.promised_deliver_time &lt; if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)),timing.promised_deliver_time,if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)))) as order_time_tag,
        reasonPlatform.refund_reason as platformRefundReasonStr,
        reasonPlatform.id as platformRefundReason,
        refund_extend.platform_refund_type as platformRefundType,
        refund_extend.order_type,
        if(ktype1.scategory = 2,true,false) as toWms,
        refund_extend.platform_sign_status,
        btype.fullname as 'btypeName',
        btype_supplier.fullname as 'supplierName',
        refund_extend.refund_order_detail_summary as refundOrderDetailSummaryId,
        big_data.big_data as refundOrderDetailSummaryGet,
        refund_extend.cycle,
        refund_extend.refund_save_total as 'refundSaveTotal',
        refund_extend.refund_save_present_total as 'refundSavePresentTotal',
        refund_extend.refund_save_principal_total as 'refundSavePrincipalTotal',
        refund_extend.refund_advance_total as 'refundAdvanceTotal'
        ,refund_extend.refund_national_subsidy_total
        ,refund_extend.refund_buyer_apply_total
        ,refund_extend.refund_platform_amount
        ,refund_extend.original_refund_platform_amount
        ,refund_extend.original_refund_national_subsidy_total,
        if(refund_extend.refund_pay_type=2,true,false) as 'advancePay',
        refund_extend.refund_pay_type,
        refund_extend.refund_advance_total
        ,refund_extend.system_refund_state,
        if(ktype1.scategory = 2,true,false) as 'wmsKtype'
    </sql>
    <sql id="detailColumnFormOrder">
        detail
        .
        eshop_order_id
        ,
        detail.trade_order_detail_id,
        sorder.trade_order_id,
        sorder.profile_id,
        detail.trade_order_detail_id,
        detail.platform_ptype_id                                                                       as platformNumId,
        detail.platform_sku_id,
        detail.platform_ptype_name                                                                     AS platform_full_name,
        detail.platform_properties_name,
        detail.platform_ptype_xcode                                                                    as platformXcode,
        detail.ptype_id,
        detail.sku_id,
        detail.qty,
        detail.unit_qty,
        detail.sub_qty,
        detail.unit,
        detail.trade_total,
        detail.total                                                                                   AS total,
        detail.total                                                                                   as taxedTotal,
        detail.ptype_service_fee,
        detail.combo_row_id,
        ptype.fullname                                                                                 AS ptype_name,
        ptype.ptype_type,
        ptype.ptype_area,
        ptype.shortname                                                                                AS ptypeShortName,
        ptype.usercode                                                                                 AS ptype_code,
        ptype.ptype_width,
        ptype.ptype_length,
        ptype.ptype_height,
        ptype.weight                                                                                   AS ptype_weight,
        ptype.length_unit,
        ptype.weight_unit,
        ptype.standard                                                                                 AS ptype_standard,
        ptype.propenabled,
        ptype.pcategory,
        ptype.cost_mode,
        ptype.sub_unit                                                                                 as subUnit,
        xcode.xcode,
        fcode.fullbarcode                                                                              AS barcode,
        sku.prop_name1,
        sku.propvalue_name1,
        sku.prop_id1,
        sku.propvalue_id1,
        sku.prop_name2,
        sku.propvalue_name2,
        sku.prop_id2,
        sku.propvalue_id2,
        sku.prop_name3,
        sku.propvalue_name3,
        sku.prop_id3,
        sku.propvalue_id3,
        sku.prop_name4,
        sku.propvalue_name4,
        sku.prop_id4,
        sku.propvalue_id4,
        sku.prop_name5,
        sku.propvalue_name5,
        sku.prop_id5,
        sku.propvalue_id5,
        sku.prop_name6,
        sku.propvalue_name6,
        sku.prop_id6,
        sku.propvalue_id6,
        unit.unit_name,
        unit.unit_rate,
        price.retail_price,
        price.min_sale_price,
        bsunit.unit_name                                                                               AS base_unit_name,
        pic.pic_url,
        ptype.protect_days,
        ptype.protect_days_unit,ptype.protect_days_view,
        detail.price                                                                                   as unit_price,
        detail.price                                                                                   AS sub_price,
        detail.dised_taxed_total                                                                       AS apply_refund_taxed_total,
        (ifnull(detail.dised_taxed_total, 0) - ifnull(detail.tax_total, 0))                            AS apply_refund_total,
        detail.tax_total                                                                               AS apply_refund_tax_total,
        detail.tax_total,
        detail.qty                                                                                     AS apply_refund_qty,
        detail.sub_qty                                                                                 AS subQty,
        detail.unit_qty                                                                                AS apply_refund_unit_qty,
        detail.sub_qty                                                                                 as apply_refund_sub_qty,
        0                                                                                              AS freightFee,
        detail.ptype_service_fee                                                                       AS applyRefundServiceFee,
        0                                                                                              AS applyRefundFreightFee,
        ''                                                                                             as batch_no,
        ''                                                                                             as produce_date,
        ''                                                                                             as expire_date,
        detail.price                                                                                   as taxed_price,
        detail.dised_taxed_price                                                                       AS dised_taxed_price,
        detail.dised_taxed_total,
        detail.tax_rate,
        detail.combo_share_scale,
        if(ifnull(detail.platform_ptype_pic_url, '') = '', pic.pic_url, detail.platform_ptype_pic_url) as platform_pic_url,
        detail.id                                                                                      as eshop_order_detail_id,
        1                                                                                              as calprice,
        ptype.snenabled,
        ptype.batchenabled,
        detail_purchase.purchase_dised_taxed_price as purchasePrice,
        detail_purchase.purchase_dised_taxed_total as purchaseTotal,
        detail_distribution.buyer_dised_taxed_price distributionDisedTaxedPrice,
        detail_distribution.buyer_dised_taxed_total distributionDisedTaxedTotal,
        brand.brand_name
    </sql>
    <sql id="detailJoinFromOrder">
        LEFT JOIN base_ptype ptype
        ON ptype.profile_id=detail.profile_id AND detail.ptype_id=ptype.id
            left join base_brandtype ptype_brand on ptype.profile_id = ptype_brand.profile_id and ptype.brand_id = ptype_brand.id
            LEFT JOIN base_ptype_xcode xcode ON xcode.profile_id=detail.profile_id AND xcode.sku_id=detail.sku_id AND xcode.unit_id=detail.unit AND xcode.defaulted=1
            LEFT JOIN base_ptype_sku sku ON sku.profile_id=detail.profile_id AND sku.id=detail.sku_id
            LEFT JOIN base_ptype_pic pic ON pic.profile_id=detail.profile_id AND detail.ptype_id=pic.ptype_id AND pic.rowindex=1
            LEFT JOIN base_ptype_fullbarcode fcode ON fcode.profile_id=detail.profile_id AND fcode.ptype_id=detail.ptype_id AND
            fcode.sku_id=detail.sku_id AND fcode.unit_id=detail.unit and fcode.defaulted=1
            LEFT JOIN base_ptype_unit unit ON unit.profile_id=detail.profile_id AND unit.id=detail.unit
            LEFT JOIN base_ptype_price price ON unit.id=price.unit_id and unit.profile_id=price.profile_id and price.sku_id=sku.id
            LEFT JOIN base_ptype_unit bsunit ON bsunit.profile_id=detail.profile_id AND bsunit.ptype_id=detail.ptype_id AND bsunit.unit_code=1
    </sql>
    <sql id="orderComboColumn">
        combo
        .
        eshop_order_detail_combo_row_id
        AS id,
        combo.profile_id,
        combo.eshop_order_id,
        combo.combo_id,
        combo.qty,
        combo.qty                                                         AS subQty,
        combo.price                                                       AS total,
        combo.trade_order_detail_id,
        combo.tax_total,
        combo.dised_taxed_total                                           AS taxed_total,
        combo.trade_total,
        combo.ptype_service_fee,
        sorder.trade_order_id,
        ptype.fullname                                                    AS ptype_name,
        ptype.id                                                          AS ptypeId,
        ptype.barcode,
        ptype.propenabled,
        ptype.weight_unit,
        ptype.weight                                                      AS ptype_weight,
        pic.pic_url,
        ptype.usercode                                                    as ptype_code,
        combo.dised_taxed_price                                           AS price,
        combo.dised_taxed_price                                           AS unit_price,
        combo.dised_taxed_price                                           AS sub_price,
        0                                                                 AS freightFee,
        combo.ptype_service_fee                                           AS applyRefundServiceFee,
        0                                                                 AS applyRefundFreightFee,
        combo.dised_taxed_total                                           AS apply_refund_taxed_total,
        (ifnull(combo.dised_taxed_total, 0) - ifnull(combo.tax_total, 0)) AS apply_refund_total,
        combo.tax_total                                                   AS apply_refund_tax_total,
        combo.qty                                                         AS apply_refund_qty,
        0                                                                 AS apply_refund_sub_qty,
        ''                                                                as batch_no,
        null                                                              as produce_date,
        null                                                              as expire_date,
        combo.total,
        combo.dised_taxed_price,
        combo.dised_taxed_total,
        combo.combo_id                                                    AS eshop_order_detail_id,
        1                                                                 as calprice,
        combo_purchase.purchase_dised_taxed_price as purchasePrice,
        combo_purchase.purchase_dised_taxed_total as purchaseTotal,
        combo_distribution.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        combo_distribution.buyer_dised_taxed_total distributionDisedTaxedTotal
    </sql>
    <sql id="sendDetailColumn">
        id
        ,
        refund_order_id,
        profile_id,
        ptype_id,
        sku_id,
        unit,
        qty,
        unit_qty,
        sub_qty,
        create_time,
        update_time,
        batchno,
        expire_date,
        produce_date,
        price,
        total,
        taxed_price,
        taxed_total,
        dised_taxed_price,
        dised_taxed_total
    </sql>
    <sql id="sendComboColumn">
        id
        ,
       refund_order_id,
       profile_id,
       combo_id,
       qty,
       price,
       total,
       taxed_price,
       taxed_total,
       dised_taxed_price,
       dised_taxed_total,
       purchase_price,
       purchase_total
    </sql>
    <sql id="saleorderColumn">
        trade_order_id
        ,
        platform_parent_order_id,
        order.buyer_id,
        order.trade_create_time,
        order.trade_finish_time,
        order.business_type,
        order.trade_pay_time,
        order.seller_memo as saleorderSellerMemo,
        order.seller_memo as 'saleorder.sellerMemo',
        4                                    AS create_type,
        order.id                           AS eshop_order_id,
        0                                    AS refund_type,
        1                                    AS 'refundPhase',
        order.local_trade_state            AS trade_status,
        order.otype_id,
        order.profile_id,
        order.ktype_id,
        order.etype_id,
        order_buyer_freight_fee              as refundApplyFreightFee,
        buyer.customer_shop_account          as 'buyer.customer_shop_account',
        buyer.ai                             as 'buyer.ai',
        buyer.di                             as 'buyer.di',
        buyer.customer_receiver              as 'buyer.customer_receiver',
        buyer.customer_receiver_mobile       as 'buyer.customer_receiver_mobile',
        buyer.customer_receiver_country      as 'buyer.customer_receiver_countryF',
        buyer.customer_receiver_city         as 'buyer.customer_receiver_city',
        buyer.customer_receiver_province     as 'buyer.customer_receiver_province',
        buyer.customer_receiver_district     as 'buyer.customer_receiver_district',
        buyer.customer_receiver_town         as 'buyer.customer_receiver_town',
        buyer.customer_receiver_address      as 'buyer.customer_receiver_address',
        buyer.customer_receiver_full_address as 'buyer.customer_receiver_full_address',
        buyer.addri                          as 'buyer.addri',
        buyer.ri                             as 'buyer.ri',
        order.etype_id                     as order_etype_id,
        etype.fullname as etype_name,
        order.buyer_id,
        ''                                   as id,
        3                                    as refundState,
        0                                    as refundApplyFreightFee,
        eshop.eshop_type                     as 'eshopInfo.eshopType',
        eshop.fullname                       as 'otype_name',
        extend.supplier_id,
        order.platform_store_id,
        order.btype_id,
        order.buyer_message as buyMessage,
        if(order.distribution_dised_taxed_total = 0 or order.distribution_dised_taxed_total is null,order.dised_taxed_total, order.distribution_dised_taxed_total) as distribution_dised_taxed_total
    </sql>
    <sql id="deliverDetailColumn">
        bdd
        .
        vchcode
        as refundOrderId
                ,
        bdd.trade_order_id,
        bdd.detail_id                                  AS id,
        bdd.profile_id,
        bdd.online_detail_id                           AS tradeOrderDetailId,
        bdd.online_ptype_id                            AS platform_num_id,
        bdd.online_sku_id                              AS platform_sku_id,
        bdd.online_ptype_name                          AS platform_full_name,
        bdd.online_sku_name                            AS platform_properties_name,
        bdd.online_ptype_xcode                         AS platform_xcode,
        bdd.online_ptype_picurl                        AS platform_pic_url,
        core.ptype_id,
        core.sku_id,
        bda.unit_id                                    AS unit,
        ABS(bda.unit_qty * ifnull(bda.unit_rate, 1))   AS qty,
        0                                              AS sub_qty,
        ABS(bda.unit_qty)                              AS unit_qty,
        ABS(bda.currency_total * tdcore.exchange_rate) AS total,
        ABS(bda.currency_total)                        AS trade_total,
        ABS(bda.currency_total)                        AS total,
        ABS(bda.currency_dised_taxed_total)            AS taxed_total,
        ABS(bda.currency_ptype_service_fee)            as service_fee,
        core.combo_detail_id                           AS combo_row_id,
        bp.fullname                                    AS ptype_name,
        bp.ptype_type,
        bp.ptype_area,
        bp.shortname                                   AS ptypeShortName,
        bp.usercode                                    AS ptype_code,
        bp.ptype_width,
        bp.ptype_length,
        bp.ptype_height,
        bp.weight                                      AS ptype_weight,
        bp.length_unit,
        bp.weight_unit,
        bp.standard                                    AS ptype_standard,
        bp.propenabled,
        bpx.xcode                                      AS xcode,
        bpf.fullbarcode                                AS barcode,
        ABS(bda.currency_dised_taxed_total)            AS apply_refund_taxed_total,
        ABS(bda.currency_dised_total)                  AS apply_refund_total,
        ABS(bda.currency_tax_total)                    AS apply_refund_tax_total,
        ABS(bda.unit_qty * bda.unit_rate)              AS apply_refund_qty,
        ABS(bda.unit_qty)                              AS apply_refund_unit_qty,
        0                                              AS apply_refund_sub_qty,
        ABS(bda.currency_tax_total)                    AS tax_total,
        bps.prop_name1,
        bps.propvalue_name1,
        bps.prop_id1,
        bps.propvalue_id1,
        bps.prop_name2,
        bps.propvalue_name2,
        bps.prop_id2,
        bps.propvalue_id2,
        bps.prop_name3,
        bps.propvalue_name3,
        bps.prop_id3,
        bps.propvalue_id3,
        bps.prop_name4,
        bps.propvalue_name4,
        bps.prop_id4,
        bps.propvalue_id4,
        bps.prop_name5,
        bps.propvalue_name5,
        bps.prop_id5,
        bps.propvalue_id5,
        bps.prop_name6,
        bps.propvalue_name6,
        bps.prop_id6,
        bps.propvalue_id6,
        unit.unit_name,
        unit.unit_rate,
        ABS(bda.currency_price)                        AS unit_price,
        ABS(bda.currency_price)                        AS sub_price,
        bsunit.unit_name                               AS base_unit_name,
        pic.pic_url,
        bp.protect_days,
        assinfo.currency_order_buyer_freight_fee       AS freightFee,
        ABS(bda.currency_ptype_service_fee)            AS applyRefundServiceFee,
        0                                              AS applyRefundFreightFee,
        core.batchno,
        core.produce_date,
        core.expire_date,
        ABS(bda.currency_dised_taxed_price)            AS taxed_price,
        ABS(bda.currency_dised_taxed_price)            AS dised_taxed_price,
        ABS(bda.currency_dised_taxed_total)            AS dised_taxed_total,
        core.tax_rate,
        core.cost_id,
        bdd.order_detail_id                            as eshop_order_detail_id,
        1                                              as calprice,
        bp.snenabled,
        bp.batchenabled,
        core.cost_type,
        core.cost_period,
        core.vchcode                                   as sourceVchcode,
        bdd.detail_id                                  as sourceDetailId,
        core.batch_price                               as batchPrice,
        bda.combo_share_scale,
        if(core.business_type=204,purchase.currency_purchase_dised_taxed_price,null) as costPrice,
        bdd.platform_parent_order_id,
        deliver.platform_parent_order_id               as mainPlatformParentOrderId,
        core.etype_id,
        tdcore.ktype_id
    </sql>
    <sql id="deliverDetailJoin">
        LEFT JOIN base_ptype bp
        ON bp.profile_id=bdd.profile_id AND core.ptype_id=bp.id
            LEFT JOIN base_ptype_xcode bpx ON bpx.profile_id=core.profile_id AND bpx.sku_id=core.sku_id AND bpx.unit_id=bda.unit_id AND bpx.defaulted=1
            LEFT JOIN base_ptype_sku bps ON bps.profile_id=core.profile_id AND bps.id=core.sku_id
            LEFT JOIN base_ptype_pic pic ON pic.profile_id=core.profile_id AND core.ptype_id=pic.ptype_id AND pic.rowindex=1
            LEFT JOIN base_ptype_fullbarcode bpf ON bpf.profile_id=core.profile_id AND bpf.ptype_id=core.ptype_id AND bpf.sku_id=core.sku_id AND bpf.unit_id=bda.unit_id and bpf.defaulted=1
            LEFT JOIN base_ptype_unit unit ON unit.profile_id=core.profile_id AND unit.id=bda.unit_id
            LEFT JOIN base_ptype_unit bsunit ON bsunit.profile_id=core.profile_id AND bsunit.ptype_id=core.ptype_id AND bsunit.unit_code=1
    </sql>
    <sql id="deliverComboColumn">
        combo
        .
        vchcode
        AS refundOrderId,
        deliver.trade_order_id,
        combo.id,
        combo.profile_id,
        deliver.online_detail_id                           AS tradeOrderDetailId,
        deliver.online_ptype_id                            AS platform_num_id,
        deliver.online_sku_id                              AS platform_sku_id,
        deliver.online_ptype_name                          AS platform_full_name,
        deliver.online_sku_name                            AS platform_properties_name,
        deliver.online_ptype_xcode                         AS platform_xcode,
        combo.combo_id                                     AS ptype_id,
        0                                                  AS sku_id,
        ABS(combo.qty)                                     AS qty,
        0                                                  AS sub_qty,
        ABS(combo.qty)                                     AS unit_qty,
        0                                                  AS unit,
        ABS(assinfo.currency_total * tdcore.exchange_rate) AS total,
        ABS(assinfo.currency_total * tdcore.exchange_rate) AS trade_total,
        ABS(assinfo.currency_dised_taxed_price)            AS taxed_price,
        ABS(assinfo.currency_dised_taxed_price)            AS dised_taxed_price,
        ABS(assinfo.currency_dised_taxed_total)            AS dised_taxed_total,
        ABS(assinfo.currency_dised_taxed_total)            AS taxed_total,
        ABS(assinfo.currency_dised_taxed_total)            AS apply_refund_taxed_total,
        ABS(assinfo.currency_dised_total)                  AS apply_refund_total,
        ABS(assinfo.currency_tax_total)                    AS apply_refund_tax_total,
        ABS(assinfo.currency_tax_total)                    AS tax_total,
        ABS(combo.qty)                                     AS apply_refund_qty,
        ABS(combo.qty)                                     AS apply_refund_unit_qty,
        0                                                  AS apply_refund_sub_qty,
        ABS(assinfo.currency_ptype_service_fee)            AS service_fee,
        combo.id                                           AS combo_row_id,
        ptype.fullname                                     AS ptype_name,
        ptype.ptype_type,
        ptype.ptype_area,
        ptype.shortname                                    AS ptypeShortName,
        ptype.usercode                                     AS ptype_code,
        ptype.ptype_width,
        ptype.ptype_length,
        ptype.ptype_height,
        ptype.weight                                       AS ptype_weight,
        ptype.length_unit,
        ptype.weight_unit,
        ptype.standard                                     AS ptype_standard,
        ptype.barcode,
        ptype.propenabled,
        ''                                                 AS prop_name1,
        ''                                                 AS propvalue_name1,
        0                                                  AS prop_id1,
        0                                                  AS propvalue_id1,
        ''                                                 AS prop_name2,
        ''                                                 AS propvalue_name2,
        0                                                  AS prop_id2,
        0                                                  AS propvalue_id2,
        ''                                                 AS prop_name3,
        ''                                                 AS propvalue_name3,
        0                                                  AS prop_id3,
        0                                                  AS propvalue_id3,
        ''                                                 AS prop_name4,
        ''                                                 AS propvalue_name4,
        0                                                  AS prop_id4,
        0                                                  AS propvalue_id4,
        ''                                                 AS prop_name5,
        ''                                                 AS propvalue_name5,
        0                                                  AS prop_id5,
        0                                                  AS propvalue_id5,
        ''                                                 AS prop_name6,
        ''                                                 AS propvalue_name6,
        0                                                  AS prop_id6,
        0                                                  AS propvalue_id6,
        ''                                                 AS unitName,
        1                                                  AS unitRate,
        assinfo.currency_price                             AS unit_price,
        assinfo.currency_price                             AS sub_price,
        ''                                                 AS base_unit_name,
        pic.pic_url                                        AS pic_url,
        ptype.protect_days,
        ABS(assinfo.currency_ptype_service_fee)            AS applyRefundServiceFee,
        0                                                  AS applyRefundFreightFee,
        ''                                                 AS batch_no,
        ''                                                 AS produce_date,
        ''                                                 AS expire_date,
        0                                                  AS tax_rate,
        deliver.order_detail_id                            as eshop_order_detail_id,
        1                                                  as calprice,
        deliver.platform_parent_order_id,
        tddeliver.platform_parent_order_id                 as mainPlatformParentOrderId,
        tdcore.etype_id,
        tdcore.ktype_id
    </sql>

    <!--    <select id="queryRefundList_COUNT" resultType="java.lang.Integer">-->
    <!--        select 1-->
    <!--    </select>-->

    <select id="queryRefundListCount" resultType="java.lang.Integer">
        select count(1) from(
        select refund.id from pl_eshop_refund refund
        LEFT JOIN pl_eshop_sale_order `order` on `order`.profile_id=refund.profile_id and
        `order`.id=refund.eshop_order_id
        LEFT JOIN pl_buyer buyer on buyer.profile_id=refund.profile_id and buyer.buyer_id = refund.buyer_id
        LEFT JOIN pl_eshop eshop on eshop.profile_id=refund.profile_id and eshop.otype_id=refund.otype_id
        LEFT JOIN pl_eshop_refund_config_reason reason on reason.profile_id=refund.profile_id and
        refund.refund_reason=reason.id
        LEFT JOIN base_etype e on e.profile_id=refund.profile_id and e.id=refund.etype_id
        LEFT JOIN `base_otype` o ON o.profile_id = refund.profile_id AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id=refund.profile_id and
        freight.refund_order_id=refund.id and freight.freight_type != 1
        LEFT JOIN base_ktype ktype1 on refund.profile_id = ktype1.profile_id and refund.ktype_id = ktype1.id
        LEFT JOIN base_ktype ktype2 on refund.profile_id = ktype2.profile_id and refund.old_ktype_id = ktype2.id
        LEFT JOIN pl_eshop_refund_timing timing on refund.profile_id = timing.profile_id and refund.id =
        timing.refund_id and refund.otype_id = timing.eshop_id
        LEFT JOIN pl_eshop_refund_extend refund_extend on refund.profile_id = refund_extend.profile_id and refund.id =
        refund_extend.refund_order_id
        LEFT JOIN pl_eshop_refund_config_reason reasonPlatform on reasonPlatform.profile_id = refund.profile_id and
        refund_extend.platform_refund_reason = reasonPlatform.id
        LEFT JOIN mark_data big_data on refund_extend.refund_order_detail_summary = big_data.id and
        refund_extend.profile_id = big_data.profile_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <include refid="refundListWhere"/>
        ) tmp
    </select>
    <select id="queryRefundListQuick" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="refundListColumn"/>
        FROM pl_eshop_refund refund
        LEFT JOIN ${tableName} on order.profile_id = refund.profile_id and order.id = refund.eshop_order_id
        LEFT JOIN pl_buyer buyer on buyer.profile_id = refund.profile_id and buyer.buyer_id = refund.buyer_id
        LEFT JOIN pl_buyer receiveBuyer on receiveBuyer.buyer_id = refund.receive_buyer_id and
        receiveBuyer.profile_id = refund.profile_id
        LEFT JOIN pl_eshop eshop on eshop.profile_id = refund.profile_id and eshop.otype_id = refund.otype_id
        LEFT JOIN `base_otype` o ON o.profile_id = refund.profile_id AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_config_reason reason on reason.profile_id = refund.profile_id and
        refund.refund_reason = reason.id
        LEFT JOIN base_etype e on e.profile_id = refund.profile_id and e.id = refund.etype_id
        LEFT JOIN base_etype e2 on e2.profile_id = refund.profile_id and e2.id = refund.confirm_etype_id
        LEFT JOIN base_atype ba on ba.profile_id = refund.profile_id and ba.id = o.atype_id
        LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id = refund.profile_id and
        freight.refund_order_id = refund.id and freight.freight_type != 1
        LEFT JOIN base_ktype ktype1 on refund.profile_id = ktype1.profile_id and refund.ktype_id = ktype1.id
        LEFT JOIN base_ktype ktype2 on refund.profile_id = ktype2.profile_id and refund.old_ktype_id = ktype2.id
        LEFT JOIN pl_eshop_refund_timing timing on refund.profile_id = timing.profile_id and refund.id =
        timing.refund_id and refund.otype_id = timing.eshop_id
        LEFT JOIN pl_eshop_refund_extend refund_extend on refund.profile_id = refund_extend.profile_id and refund.id =
        refund_extend.refund_order_id
        LEFT JOIN pl_eshop_refund_config_reason reasonPlatform on reasonPlatform.profile_id = refund.profile_id and
        refund_extend.platform_refund_reason = reasonPlatform.id
        LEFT JOIN pl_eshop_refund_distribution_buyer perdb on perdb.profile_id = refund.profile_id and
        perdb.refund_order_id = refund.id
        left join base_btype btype on btype.profile_id = refund.profile_id and btype.id = refund.btype_id
        left join base_btype btype_supplier on btype_supplier.profile_id = refund.profile_id and btype_supplier.id = refund.supplier_id
        LEFT JOIN mark_data big_data on refund_extend.refund_order_detail_summary = big_data.id and refund_extend.profile_id = big_data.profile_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <include refid="refundListWhere"/>
    </select>
    <select id="queryRefundList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="refundListColumn"/>
        FROM pl_eshop_refund refund
        LEFT JOIN ${tableName} on order.profile_id = refund.profile_id and order.id = refund.eshop_order_id
        LEFT JOIN pl_buyer buyer on buyer.profile_id = refund.profile_id and buyer.buyer_id = refund.buyer_id
        LEFT JOIN pl_buyer receiveBuyer on receiveBuyer.buyer_id = refund.receive_buyer_id and
        receiveBuyer.profile_id = refund.profile_id
        LEFT JOIN pl_eshop eshop on eshop.profile_id = refund.profile_id and eshop.otype_id = refund.otype_id
        LEFT JOIN `base_otype` o ON o.profile_id = refund.profile_id AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_config_reason reason on reason.profile_id = refund.profile_id and
        refund.refund_reason = reason.id
        LEFT JOIN base_etype e on e.profile_id = refund.profile_id and e.id = refund.etype_id
        LEFT JOIN base_etype e2 on e2.profile_id = refund.profile_id and e2.id = refund.confirm_etype_id
        LEFT JOIN base_atype ba on ba.profile_id = refund.profile_id and ba.id = o.atype_id
        LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id = refund.profile_id and
        freight.refund_order_id = refund.id and freight.freight_type != 1
        LEFT JOIN base_ktype ktype1 on refund.profile_id = ktype1.profile_id and refund.ktype_id = ktype1.id
        LEFT JOIN base_ktype ktype2 on refund.profile_id = ktype2.profile_id and refund.old_ktype_id = ktype2.id
        LEFT JOIN pl_eshop_refund_timing timing on refund.profile_id = timing.profile_id and refund.id =
        timing.refund_id and refund.otype_id = timing.eshop_id
        LEFT JOIN pl_eshop_refund_extend refund_extend on refund.profile_id = refund_extend.profile_id and refund.id =
        refund_extend.refund_order_id
        LEFT JOIN pl_eshop_refund_config_reason reasonPlatform on reasonPlatform.profile_id = refund.profile_id and
        refund_extend.platform_refund_reason = reasonPlatform.id
        LEFT JOIN pl_eshop_refund_distribution_buyer perdb on perdb.profile_id = refund.profile_id and
        perdb.refund_order_id = refund.id
        left join base_btype btype on btype.profile_id = refund.profile_id and btype.id = refund.btype_id
        left join base_btype btype_supplier on btype_supplier.profile_id = refund.profile_id and btype_supplier.id = refund.supplier_id
        LEFT JOIN mark_data big_data on refund_extend.refund_order_detail_summary = big_data.id and
        refund_extend.profile_id = big_data.profile_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <include refid="refundListWhere"/>
    </select>


    <select id="queryRefundListSimple" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="refundListColumn"/>
        FROM pl_eshop_refund refund
        LEFT JOIN ${tableName} on order.profile_id = refund.profile_id and order.id = refund.eshop_order_id
        LEFT JOIN pl_buyer buyer on buyer.profile_id = refund.profile_id and buyer.buyer_id = refund.buyer_id
        LEFT JOIN pl_buyer receiveBuyer on receiveBuyer.buyer_id = refund.receive_buyer_id and
        receiveBuyer.profile_id = refund.profile_id
        LEFT JOIN pl_eshop eshop on eshop.profile_id = refund.profile_id and eshop.otype_id = refund.otype_id
        LEFT JOIN `base_otype` o ON o.profile_id = refund.profile_id AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_config_reason reason on reason.profile_id = refund.profile_id and
        refund.refund_reason = reason.id
        LEFT JOIN base_etype e on e.profile_id = refund.profile_id and e.id = refund.etype_id
        LEFT JOIN base_etype e2 on e2.profile_id = refund.profile_id and e2.id = refund.confirm_etype_id
        LEFT JOIN base_atype ba on ba.profile_id = refund.profile_id and ba.id = o.atype_id
        LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id = refund.profile_id and
        freight.refund_order_id = refund.id and freight.freight_type != 1
        LEFT JOIN base_ktype ktype1 on refund.profile_id = ktype1.profile_id and refund.ktype_id = ktype1.id
        LEFT JOIN base_ktype ktype2 on refund.profile_id = ktype2.profile_id and refund.old_ktype_id = ktype2.id
        LEFT JOIN pl_eshop_refund_timing timing on refund.profile_id = timing.profile_id and refund.id =
        timing.refund_id and refund.otype_id = timing.eshop_id
        LEFT JOIN pl_eshop_refund_extend refund_extend on refund.profile_id = refund_extend.profile_id and refund.id =
        refund_extend.refund_order_id
        LEFT JOIN pl_eshop_refund_config_reason reasonPlatform on reasonPlatform.profile_id = refund.profile_id and
        refund_extend.platform_refund_reason = reasonPlatform.id
        LEFT JOIN pl_eshop_refund_distribution_buyer perdb on perdb.profile_id = refund.profile_id and
        perdb.refund_order_id = refund.id
        left join base_btype btype on btype.profile_id = refund.profile_id and btype.id = refund.btype_id
        left join base_btype btype_supplier on btype_supplier.profile_id = refund.profile_id and btype_supplier.id = refund.supplier_id
        LEFT JOIN mark_data big_data on refund_extend.refund_order_detail_summary = big_data.id and
        refund_extend.profile_id = big_data.profile_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <include refid="refundListWhere"/>
    </select>

    <sql id="refundListWhere">
        WHERE refund.profile_id=#{profileId} AND (timing.id=0 or timing.id!=0 or timing.id is null or timing.id is not null) and refund.otype_id not in(select id from `base_otype`
        where
        profile_id=#{profileId} and ocategory=2 )
        <if test="agRefund == 1">
            AND refund_extend.ag_refund = 1 and refund.deleted = 0 and refund.refund_state not in (4,5,6)
        </if>
        <if test="unHandleRefund == 1">
            <if test="limitTime == 12">
                and NOW() > DATE_ADD(refund_create_time, interval 12 hour)
                and NOW() &lt; DATE_ADD(refund_create_time, interval 24 hour)
            </if>
            <if test="limitTime == 24">
                and NOW() > DATE_ADD(refund_create_time, interval 24 hour)
                and NOW() &lt; DATE_ADD(refund_create_time, interval 48 hour)
            </if>
            <if test="limitTime == 48">
                and NOW() > DATE_ADD(refund_create_time, interval 48 hour)
                and NOW() &lt; DATE_ADD(refund_create_time, interval 72 hour)
            </if>
            <if test="limitTime == 72">
                and NOW() > DATE_ADD(refund_create_time, interval 72 hour)
            </if>
            and refund.deleted = 0 and refund.refund_state not in (4,5,6) and refund.create_type = 1
        </if>
        <if test="specialType!=null">
            <if test="specialType == 0">
                and refund_extend.platform_refund_type = 5 and refund.refund_state = 1 and refund.deleted = 0
            </if>
            <if test="specialType == 1">
                and refund_extend.platform_refund_type = 0 and refund.refund_state = 1 and refund.deleted = 0
            </if>
            <if test="specialType == 2">
                and refund_extend.platform_refund_type = 1 and refund.refund_state = 1 and refund.deleted = 0
            </if>
            <if test="specialType == 3">
                and refund_extend.platform_refund_type = 1 and refund.refund_state = 2 and refund.deleted = 0
            </if>
            <if test="specialType == 4">
                and refund_extend.platform_refund_type = 1 and freight.freight_no !='' and freight.freight_name !='' and
                refund.deleted = 0 and refund.refund_state not in (4,5,6)
            </if>
            <if test="specialType == 100">
                and refund.refund_type in (0,1,2,3,4) and refund.confirm_state = 0 and refund.deleted = 0 and
                refund.refund_state not in (4,6)
            </if>
            <if test="specialType == 101">
                and refund.refund_type in (1,2) and refund.receive_state = 1 and refund.confirm_state = 1 and
                refund.deleted = 0 and refund.refund_state not in (4,6)
            </if>
            <if test="specialType == 102">
                and refund.refund_type in (0,1,6,7) and refund.pay_state = 1 and refund.confirm_state = 1 and
                refund.deleted = 0 and refund.refund_state not in (4,6)
            </if>
        </if>
        <if test="isSystemConfirm">
            and (refund.confirm_etype_id = 0 and refund.confirm_state in (1,2))
        </if>
        <if test="confirmEtypeIds!=null and confirmEtypeIds.size()>0">
            and refund.confirm_etype_id in
            <foreach collection="confirmEtypeIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="platformInterceptStatus!=null and platformInterceptStatus.size()>0">
            and refund.id in (select refund_order_id from pl_eshop_refund_freight where profile_id=#{profileId} and
            freight_intercept_status
            in
            <foreach collection="platformInterceptStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="markCondition==0">
            <if test="(markType!=null and markType.size()>0) and (specialMarkType==null or specialMarkType.size()==0)">
                and (
                <foreach collection="markType" item="item" separator="or">
                    <include refid="forMarkType"></include>
                </foreach>
                <if test="noMark == true">
                    <include refid="notMark"/>
                </if>
                )
            </if>
            <if test="(markType==null or markType.size()==0) and (specialMarkType!=null and specialMarkType.size()>0)">
                AND
                (
                refund.id in (
                select mark.order_id from pl_eshop_order_mark mark where mark.profile_id = #{profileId} and
                mark.order_type
                = 2 AND mark.create_time &gt;=#{beginTime}
                AND mark.create_time &lt;=#{endTime}
                <if test="specialMarkType != null and specialMarkType.size() > 0">
                    and mark.mark_code in
                    <foreach item="type" collection="specialMarkType" open="(" separator="," close=")">
                        #{type}
                    </foreach>
                </if>
                )
                <if test="noMark == true">
                    <include refid="notMark"/>
                </if>
                )
            </if>
            <if test="(markType!=null and markType.size()>0) and (specialMarkType!=null and specialMarkType.size()>0)">
                and (((
                <foreach collection="markType" item="item" separator="and">
                    <include refid="forMarkType">
                    </include>
                </foreach>
                )
                OR
                refund.id in (
                select mark.order_id from pl_eshop_order_mark mark where mark.profile_id = #{profileId} and
                mark.order_type
                = 2 AND mark.create_time &gt;=#{beginTime}
                AND mark.create_time &lt;=#{endTime}
                <if test="specialMarkType != null and specialMarkType.size() > 0">
                    and mark.mark_code in
                    <foreach item="type" collection="specialMarkType" open="(" separator="," close=")">
                        #{type}
                    </foreach>
                </if>
                ))
                <if test="noMark == true">
                    <include refid="notMarkOnly"/>
                </if>
                )
            </if>
        </if>
        <if test="markCondition==1">
            <if test="(markType!=null and markType.size()>0) and (specialMarkType==null or specialMarkType.size()==0)">
                and !(
                <foreach collection="markType" item="item" separator="or">
                    <include refid="forMarkType"></include>
                </foreach>
                <if test="noMark == true">
                    <include refid="notMark"/>
                </if>
                )
            </if>
            <if test="(markType==null or markType.size()==0) and (specialMarkType!=null and specialMarkType.size()>0)">
                AND
                !(
                refund.id in (
                select mark.order_id from pl_eshop_order_mark mark where mark.profile_id = #{profileId} and
                mark.order_type
                = 2 AND mark.create_time &gt;=#{beginTime}
                AND mark.create_time &lt;=#{endTime}
                <if test="specialMarkType != null and specialMarkType.size() > 0">
                    and mark.mark_code in
                    <foreach item="type" collection="specialMarkType" open="(" separator="," close=")">
                        #{type}
                    </foreach>
                </if>
                )
                <if test="noMark == true">
                    <include refid="notMark"/>
                </if>
                )
            </if>
            <if test="(markType!=null and markType.size()>0) and (specialMarkType!=null and specialMarkType.size()>0)">
                and !(((
                <foreach collection="markType" item="item" separator="or">
                    <include refid="forMarkType">
                    </include>
                </foreach>
                ) OR
                refund.id in (
                select mark.order_id from pl_eshop_order_mark mark where mark.profile_id = #{profileId} and
                mark.order_type
                = 2 AND mark.create_time &gt;=#{beginTime}
                AND mark.create_time &lt;=#{endTime}
                <if test="specialMarkType != null and specialMarkType.size() > 0">
                    and mark.mark_code in
                    <foreach item="type" collection="specialMarkType" open="(" separator="," close=")">
                        #{type}
                    </foreach>
                </if>
                ))
                <if test="noMark == true">
                    <include refid="notMark"/>
                </if>
                )
            </if>
        </if>

        <if test="refundNoDetail != null">
            <if test="refundNoDetail.getCode() == 0">
                and refund.no_detail=0
            </if>
            <if test="refundNoDetail.getCode() == 2">
                and refund.no_detail>=0
            </if>
        </if>
        <if test="refundOrderId != null and refundOrderId != '' and refundOrderId > 0">
            AND refund.id=#{refundOrderId}
        </if>
        <if test="tradeOrderId != null and tradeOrderId != ''">
            AND refund.trade_order_id=#{tradeOrderId}
        </if>
        <if test="tradeIdList != null">
            AND refund.trade_order_id in
            <foreach collection="tradeIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="eshopOrderId != null and eshopOrderId!=0">
            AND refund.eshop_order_id = #{eshopOrderId}
        </if>
        <if test="relatedCheckin == 0">
            AND not exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and
            profile_id = refund.profile_id)
        </if>
        <if test="relatedCheckin == 1">
            AND exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and profile_id
            = refund.profile_id)
        </if>
        <if test="otypeId != null and otypeId > 0">
            AND refund.otype_id=#{otypeId}
        </if>
        <if test="tradeRefundOrderNumber != null and tradeRefundOrderNumber != ''">
            AND refund.trade_refund_order_number=#{tradeRefundOrderNumber}
        </if>
        <if test="refundIds != null and refundIds.size > 0">
            AND refund.id IN
            <foreach collection="refundIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="refundIdsForPass != null and refundIdsForPass.size > 0">
            AND refund.id IN
            <foreach collection="refundIdsForPass" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="refundIdsForPtype != null and refundIdsForPtype.size > 0">
            <choose>
                <when test="ptypeType == 0 or ptypeType == 1">
                    AND refund.id IN
                    <foreach collection="refundIdsForPtype" close=")" open="(" separator="," item="id">
                        #{id}
                    </foreach>
                </when>
                <when test="ptypeType == 2">
                    AND refund.id NOT IN
                    <foreach collection="refundIdsForPtype" close=")" open="(" separator="," item="id">
                        #{id}
                    </foreach>
                </when>
            </choose>
        </if>
        <if test="businessTypes != null and businessTypes.size > 0">
            AND refund.business_type IN
            <foreach collection="businessTypes" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryRefundOnlyOnSaleEnum != null and queryRefundOnlyOnSaleEnum.getCode() != 2">
            AND refund.refund_phase=#{queryRefundOnlyOnSaleEnum}
        </if>
        <if test="quickFilterType != null">
            <choose>
                <when test='quickFilterType == "0"'>
                    AND extend.platform_refund_type=0
                </when>
                <when test='quickFilterType == "101"'>
                    AND extend.platform_refund_type=0
                    AND refund.confirm_state=0
                </when>
                <when test='quickFilterType == "102"'>
                    AND refund.refund_type=0
                    AND refund.pay_state=1
                </when>
                <when test='quickFilterType == "103"'>
                    AND refund.refund_type=0
                    AND refund.confirm_state=1
                    AND refund.pay_state=2
                </when>
                <when test='quickFilterType == "1"'>
                    AND refund.refund_type=1
                </when>
                <when test='quickFilterType == "111"'>
                    AND refund.refund_type=1
                    AND refund.confirm_state=0
                </when>
                <when test='quickFilterType == "112"'>
                    AND refund.refund_type=1
                    AND refund.receive_state=1
                </when>
                <when test='quickFilterType == "113"'>
                    AND refund.refund_type=1
                    AND refund.pay_state=1
                </when>
                <when test='quickFilterType == "114"'>
                    AND refund.refund_type=1
                    AND refund.confirm_state=1
                    AND refund.receive_state=3
                    AND
                    refund.pay_state=2
                </when>

                <when test='quickFilterType == "2"'>
                    AND refund.refund_type=2
                </when>
                <when test='quickFilterType == "121"'>
                    AND refund.refund_type=2
                    AND refund.confirm_state=0
                </when>
                <when test='quickFilterType == "122"'>
                    AND refund.refund_type=2
                    AND refund.receive_state=1
                </when>
                <when test='quickFilterType == "123"'>
                    AND refund.refund_type=2
                    AND refund.refund_process_state=0
                </when>
                <when test='quickFilterType == "124"'>
                    AND refund.refund_type=2
                    AND refund.confirm_state=1
                    AND refund.receive_state=3
                    AND
                    refund.refund_process_state=1
                </when>
                <when test='quickFilterType == "3"'>
                    AND refund.refund_type=3
                </when>
                <when test='quickFilterType == "131"'>
                    AND refund.refund_type=3
                    AND refund.confirm_state=0
                </when>
                <when test='quickFilterType == "132"'>
                    AND refund.refund_type=3
                    AND refund.refund_process_state=0
                </when>
                <when test='quickFilterType == "133"'>
                    AND refund.refund_type=3
                    AND refund.confirm_state=1
                    AND refund.refund_process_state=1
                </when>
                <when test='quickFilterType == "4"'>
                    AND refund.refund_type=4
                </when>
                <when test='quickFilterType == "141"'>
                    AND refund.refund_type=4
                    AND refund.confirm_state=0
                </when>
                <when test='quickFilterType == "142"'>
                    AND refund.refund_type=4
                    AND refund.refund_process_state=0
                </when>
                <when test='quickFilterType == "143"'>
                    AND refund.refund_type=4
                    AND refund.confirm_state=1
                    AND refund.refund_process_state=1
                </when>
                <when test='quickFilterType == "5"'>
                    AND refund.refund_phase=0
                    AND refund.refund_type=5
                </when>
                <when test='quickFilterType == "6"'>
                    AND refund.deleted=1
                </when>
                <when test='quickFilterType == "7"'>
                    AND refund.business_type=2
                </when>
            </choose>
        </if>
        <if test="timeType != null">
            <choose>
                <when test="timeType.getCode() == 4">
                    AND refund.refund_finish_time &gt;=#{beginTime}
                    AND refund.refund_finish_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 3">
                    AND refund.refund_create_time &gt;=#{beginTime}
                    AND refund.refund_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 2">
                    AND refund.create_time &gt;=#{beginTime}
                    AND refund.create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 0">
                    AND refund.trade_create_time &gt;=#{beginTime}
                    AND refund.trade_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 1">
                    AND refund.trade_pay_time &gt;=#{beginTime}
                    and refund.trade_pay_time &lt;=#{endTime}
                </when>
            </choose>
        </if>
        <if test="refundTypes != null and refundTypes.size() > 0">
            AND refund.refund_type IN
            <foreach collection="refundTypes" close=")" open="(" separator="," item="refundTypeEnum">
                #{refundTypeEnum}
            </foreach>
        </if>
        <if test="otypeIds != null and otypeIds.size() > 0">
            AND refund.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="auditStatus != null and auditStatus.size() > 0">
            AND refund.confirm_state in
            <foreach collection="auditStatus" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="receiveStatuses != null and receiveStatuses.size() > 0">
            AND refund.receive_state IN
            <foreach collection="receiveStatuses" close=")" open="(" separator="," item="receivedStatus">
                #{receivedStatus}
            </foreach>
        </if>
        <if test="refundStatuses != null and refundStatuses.size() > 0">
            AND refund.refund_state IN
            <foreach collection="refundStatuses" close=")" open="(" separator="," item="refundStatus">
                #{refundStatus}
            </foreach>
        </if>
        <if test="payStates != null and payStates.size() > 0">
            AND refund.pay_state IN
            <foreach collection="payStates" close=")" open="(" separator="," item="payState">
                #{payState}
            </foreach>
        </if>
        <if test="refundProcessStates != null and refundProcessStates.size() > 0">
            AND refund.refund_process_state IN
            <foreach collection="refundProcessStates" close=")" open="(" separator="," item="refundProcessState">
                #{refundProcessState}
            </foreach>
        </if>
        <if test="deleted != null and deleted.getCode() >= 0">
            AND refund.deleted=#{deleted}
        </if>
        <if test="refundAuditStatuses != null and refundAuditStatuses.size() > 0">
            and refund.confirm_state in
            <foreach collection="refundAuditStatuses" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="freightNos!=null and freightNos.size()>0">
            AND (
            freight.freight_no in
            <foreach collection="freightNos" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
            <if test="freightIds!=null and freightIds.size()>0">
                OR refund.trade_order_id in
                <foreach collection="freightIds" close=")" open="(" separator="," item="id">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="freightNo!=null and freightNo != ''">
            AND freight.freight_no = #{freightNo}
        </if>
        <if test="freightNosForCheckin!=null and freightNosForCheckin.size()>0">
            AND freight.freight_no in
            <foreach collection="freightNosForCheckin" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="freightCompanyType!=null and freightCompanyType == 0 and freightNameText!=''">
            AND freight.freight_name = #{freightNameText}
        </if>
        <if test="batchQueryType != null and batchQueryKeyList != null and batchQueryKeyList.size() > 0">
            <choose>
                <when test="batchQueryType.getCode() == 0">
                    AND refund.trade_refund_order_number in
                    <foreach collection="batchQueryKeyList" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                </when>
                <when test="batchQueryType.getCode() == 1">
                    AND refund.trade_order_id in
                    <foreach collection="batchQueryKeyList" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                </when>
                <when test="batchQueryType.getCode() == 2">
                    AND freight.freight_no in
                    <foreach collection="batchQueryKeyList" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                </when>
                <when test="batchQueryType.getCode() == 3">
                    AND (buyer.ai in
                    <foreach collection="batchQueryKeyList" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                    or buyer.customer_shop_account in
                    <foreach collection="batchQueryEncryptKeyList" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                    <if test="newTradeIdList!=null and newTradeIdList.size()>0">
                        or refund.trade_order_id IN
                        <foreach collection="newTradeIdList" close=")" open="(" separator="," item="key">
                            #{key}
                        </foreach>
                    </if>
                    )
                </when>
                <when test="batchQueryType.getCode() == 4">
                    AND
                    <foreach collection="batchQueryKeyList" item="item" index="index" open="(" separator="or" close=")">
                        freight.freight_name LIKE CONCAT('%', #{item}, '%')
                    </foreach>
                </when>
                <when test="batchQueryType.getCode() == 5">
                    AND refund.platform_parent_order_id in
                    <foreach collection="batchQueryKeyList" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                </when>
            </choose>
        </if>
        <if test="fuzzyKeyWord != null and fuzzyKeyWord != ''">
            <choose>
                <when test="fuzzyQueryType.getCode() == 0">
                    AND refund.trade_refund_order_number like CONCAT('%', #{fuzzyKeyWord}, '%')
                </when>
                <when test="fuzzyQueryType.getCode() == 1">
                    AND EXISTS (SELECT 1 FROM `pl_eshop_refund_freight` AS f
                    WHERE f.refund_order_id = refund.id
                    AND f.profile_id=refund.profile_id
                    AND f.freight_type != 1
                    AND f.freight_no = #{fuzzyKeyWord})
                </when>
                <when test="fuzzyQueryType.getCode() == 2">
                    AND (EXISTS (SELECT 1 FROM `pl_eshop_refund_freight` AS f
                    WHERE f.refund_order_id = refund.id
                    AND f.freight_type != 1
                    AND f.profile_id=refund.profile_id
                    AND f.freight_no = #{fuzzyKeyWord})
                    OR refund.trade_refund_order_number like CONCAT('%', #{fuzzyKeyWord}, '%') )
                </when>
            </choose>
        </if>
        <if test="totalDate != null">
            and date_sub(CURDATE(), INTERVAL 2 DAY) &lt;= DATE (refund.create_time)
        </if>
        <if test="mappingState != null and mappingState == true">
            and order.mapping_state=1
        </if>
        <if test="allowEtypeIds != null and allowEtypeIds.size() > 0 and etypeLimited">
            and refund.order_etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="scategoryListNotIn != null and scategoryListNotIn.size() > 0">
            and ktype1.scategory not in
            <foreach collection="scategoryListNotIn" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="!notDistribution">
            AND refund.business_type = 203
        </if>
        <if test="createType!=null and createType.size() > 0">
            and refund.create_type in
            <foreach collection="createType" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="refundKtype!=null and refundKtype.size() > 0">
            and refund.ktype_id in
            <foreach collection="refundKtype" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filter != null">
            <if test="filter.refundStates != null and filter.refundStates.size()>0">
                and refund.refund_state in
                <foreach collection="filter.refundStates" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
            <if test="filter.systemRefundStates != null and filter.systemRefundStates.size() > 0">
                AND refund_extend.system_refund_state IN
                <foreach collection="filter.systemRefundStates" close=")" open="(" separator="," item="refundStatus">
                    #{refundStatus}
                </foreach>
            </if>
            <if test="filter.eshopName != null and filter.eshopName != ''">
                and eshop.fullname like CONCAT('%', #{filter.eshopName}, '%')
            </if>
            <if test="filter.otypeIds != null and filter.otypeIds.size() > 0 ">
                and refund.otype_id in
                <foreach collection="filter.otypeIds" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
            <if test="filter.platformParentOrderId != null and filter.platformParentOrderId != ''">
                AND refund.platform_parent_order_id=#{filter.platformParentOrderId}
            </if>
            <if test="filter.tradeRefundOrderNumber != null and filter.tradeRefundOrderNumber != ''">
                and refund.trade_refund_order_number = #{filter.tradeRefundOrderNumber}
            </if>
            <if test="filter.refundTypes != null and filter.refundTypes.size()>0">
                and refund.refund_type in
                <foreach collection="filter.refundTypes" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
            <if test="filter.tradeOrderId != null and filter.tradeOrderId != ''">
                and refund.trade_order_id = #{filter.tradeOrderId}
            </if>
            <if test="filter.createTimeBegin != null and filter.createTimeEnd != null">
                and refund.create_time between #{filter.createTimeBegin}
                and #{filter.createTimeEnd}
            </if>
            <if test="filter.refundFinishTimeBegin != null and filter.refundFinishTimeEnd != null">
                and refund.refund_finish_time between #{filter.refundFinishTimeBegin}
                and #{filter.refundFinishTimeEnd}
            </if>
            <if test="filter.refundCreateTimeBegin != null and filter.refundCreateTimeEnd != null">
                and refund.refund_create_time between #{filter.refundCreateTimeBegin}
                and #{filter.refundCreateTimeEnd}
            </if>
            <if test="filter.tradeStatus != null and filter.tradeStatus.size()>0">
                and order.local_trade_state in
                <foreach collection="filter.tradeStatus" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
            <if test="filter.confirmState != null">
                <if test="filter.confirmState.code != -1">
                    and refund.confirm_state = #{filter.confirmState}
                </if>
            </if>
            <if test="filter.receiveState != null">
                and refund.receive_state = #{filter.receiveState}
            </if>
            <if test="filter.refundReason != null and filter.refundReason != ''">
                and reason.refund_reason like CONCAT('%', #{filter.refundReason}, '%')
            </if>
            <if test="filter.refundProcessState != null">
                and refund.refund_process_state = #{filter.refundProcessState}
            </if>
            <if test="filter.refundProcessTimeBegin != null and filter.refundProcessTimeEnd != null">
                and refund.refund_process_time between #{filter.refundProcessTimeBegin} and
                #{filter.refundProcessTimeEnd}
            </if>
            <if test="filter.payState != null">
                and refund.pay_state = #{filter.payState}
            </if>
            <if test="filter.confirmTimeBegin != null and filter.confirmTimeEnd != null">
                and refund.confirm_time between #{filter.confirmTimeBegin} and #{filter.confirmTimeEnd}
            </if>
            <if test="filter.receiveTimeBegin != null and filter.receiveTimeEnd != null">
                and refund.receive_time between #{filter.receiveTimeBegin} and #{filter.receiveTimeEnd}
            </if>
            <if test="filter.payTimeBegin != null and filter.payTimeEnd != null">
                and refund.pay_time between #{filter.payTimeBegin} and #{filter.payTimeEnd}
            </if>
            <if test="filter.deleted != null">
                and refund.deleted = #{filter.deleted.code}
            </if>
            <if test="filter.etypeName != null and filter.etypeName != ''">
                and e.fullname like CONCAT('%', #{filter.etypeName}, '%')
            </if>
            <if test="filter.refundApplyFreightFeeMin != null and filter.refundApplyFreightFeeMax == null">
                and refund.refund_apply_freight_fee &gt;= #{filter.refundApplyFreightFeeMin}
            </if>
            <if test="filter.refundApplyFreightFeeMin == null and filter.refundApplyFreightFeeMax != null">
                and refund.refund_apply_freight_fee &lt;= #{filter.refundApplyFreightFeeMax}
            </if>
            <if test="filter.refundApplyFreightFeeMin != null and filter.refundApplyFreightFeeMax != null">
                and refund.refund_apply_freight_fee between #{filter.refundApplyFreightFeeMin} and
                #{filter.refundApplyFreightFeeMax}
            </if>
            <if test="filter.refundApplyTaxedTotalMin != null and filter.refundApplyTaxedTotalMax == null">
                and refund.refund_apply_total &gt;= #{filter.refundApplyTaxedTotalMin}
            </if>
            <if test="filter.refundApplyTaxedTotalMin == null and filter.refundApplyTaxedTotalMax != null">
                and refund.refund_apply_total &lt;= #{filter.refundApplyTaxedTotalMax}
            </if>
            <if test="filter.refundApplyTaxedTotalMin != null and filter.refundApplyTaxedTotalMax != null">
                and refund.refund_apply_total between #{filter.refundApplyTaxedTotalMin} and
                #{filter.refundApplyTaxedTotalMax}
            </if>
            <if test="filter.orderDetailTotalMin != null and filter.orderDetailTotalMax == null">
                and refund_extend.order_detail_total &gt;= #{filter.orderDetailTotalMin}
            </if>
            <if test="filter.orderDetailTotalMin == null and filter.orderDetailTotalMax != null">
                and refund_extend.order_detail_total &lt;= #{filter.orderDetailTotalMax}
            </if>
            <if test="filter.orderDetailTotalMin != null and filter.orderDetailTotalMax != null">
                and refund_extend.order_detail_total between #{filter.orderDetailTotalMin} and
                #{filter.orderDetailTotalMax}
            </if>
            <if test="filter.refundApplyServiceFeeMin != null and filter.refundApplyServiceFeeMax == null">
                and refund.refund_apply_service_fee &gt;= #{filter.refundApplyServiceFeeMin}
            </if>
            <if test="filter.refundApplyServiceFeeMin == null and filter.refundApplyServiceFeeMax != null">
                and refund.refund_apply_service_fee &lt;= #{filter.refundApplyServiceFeeMax}
            </if>
            <if test="filter.refundApplyServiceFeeMin != null and filter.refundApplyServiceFeeMax != null">
                and refund.refund_apply_service_fee between #{filter.refundApplyServiceFeeMin} and
                #{filter.refundApplyServiceFeeMax}
            </if>

            <if test="filter.disedTaxedTotalMin != null and filter.disedTaxedTotalMax == null">
                and order.dised_taxed_total &gt;= #{filter.disedTaxedTotalMin}
            </if>
            <if test="filter.disedTaxedTotalMin == null and filter.disedTaxedTotalMax != null">
                and order.dised_taxed_total &lt;= #{filter.disedTaxedTotalMax}
            </if>
            <if test="filter.disedTaxedTotalMin != null and filter.disedTaxedTotalMax != null">
                and order.dised_taxed_total between #{filter.disedTaxedTotalMin} and #{filter.disedTaxedTotalMax}
            </if>
            <if test="filter.customerShopAccountDics != null and filter.customerShopAccountDics.size() > 0">
                and (buyer.ai in
                <foreach collection="filter.customerShopAccountDics" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
                or buyer.customer_shop_account in
                <foreach collection="filter.customerShopAccountDics" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
                <if test="newTradeIdList!=null and newTradeIdList.size()>0">
                    or refund.trade_order_id IN
                    <foreach collection="newTradeIdList" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                </if>
                )
            </if>
            <if test="filter.customerReceiverMobileDics != null and filter.customerReceiverMobileDics.size() > 0">
                and buyer.mi in
                <foreach collection="filter.customerReceiverMobileDics" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
            <if test="filter.buyerMessage != null and filter.buyerMessage != ''">
                and order.buyer_message like CONCAT('%', #{filter.buyerMessage}, '%')
            </if>
            <if test="filter.refundStatement != null and filter.refundStatement != ''">
                and refund.refund_statement like CONCAT('%', #{filter.refundStatement}, '%')
            </if>
            <if test="filter.sellerMemo != null and filter.sellerMemo != ''">
                and order.seller_memo like CONCAT('%', #{filter.sellerMemo}, '%')
            </if>
            <if test="filter.sellerFlag != null and filter.sellerFlag.size()>0">
                and order.seller_flag in
                <foreach collection="filter.sellerFlag" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
            <if test="filter.refundCostState != null">
                and refund.cost_state = #{filter.refundCostState}
            </if>
            <if test="filter.refundKtypeName != null and filter.refundKtypeName != ''">
                and ktype1.fullname like CONCAT('%', #{filter.refundKtypeName}, '%')
            </if>
            <if test="filter.ktypeIds!=null and filter.ktypeIds.size()>0">
                and ktype1.id in
                <foreach collection="filter.ktypeIds" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
            <if test="filter.memo != null and filter.memo != ''">
                and refund.memo like CONCAT('%', #{filter.memo}, '%')
            </if>
            <if test="filter.hasSubmitToWms != null">
                and refund.has_submit_to_wms = #{filter.hasSubmitToWms}
            </if>
            <if test="filter.relatedCheckin == false">
                AND not exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and
                profile_id = refund.profile_id)
            </if>
            <if test="filter.relatedCheckin == true">
                AND exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and
                profile_id = refund.profile_id)
            </if>
            <if test="filter.refundCompanyNumber != null and filter.refundCompanyNumber != ''">
                AND freight.freight_no like CONCAT('%', #{filter.refundCompanyNumber}, '%')
            </if>
            <if test="filter.refundCompany != null and filter.refundCompany != ''">
                AND freight.freight_name like CONCAT('%', #{filter.refundCompany}, '%')
            </if>
            <if test="filter.businessTypes != null and filter.businessTypes.size()>0">
                AND refund.business_type in
                <foreach collection="filter.businessTypes" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>

            <if test="filter.platformConfirmState != null and filter.platformConfirmState.code != -1">
                AND refund.platform_confirm_state = #{filter.platformConfirmState}
            </if>
            <if test="filter.platformRefundState != null and  filter.platformRefundState.code != -1">
                AND refund.platform_refund_state = #{filter.platformRefundState}
            </if>
            <if test="filter.platformReturnState != null and  filter.platformReturnState.code != -1">
                AND refund.platform_return_state = #{filter.platformReturnState}
            </if>
            <if test="filter.platformChangeState != null and filter.platformChangeState.code != -1">
                AND refund.platform_change_state = #{filter.platformChangeState}
            </if>
            <if test="filter.createType!=null ">
                and refund.create_type=#{filter.createType}
            </if>
            <if test="filter.etypeId!=null ">
                and refund.etype_id=#{filter.etypeId}
            </if>
            <if test="filter.platformAutoInterceptAgree!=null ">
                and refund_extend.platform_auto_intercept_agree=#{filter.platformAutoInterceptAgree}
            </if>
            <if test="filter.receiveEtypeId!=null ">
                and refund_extend.receive_etype_id=#{filter.receiveEtypeId}
            </if>
            <if test="filter.processEtypeId!=null ">
                and refund_extend.process_etype_id=#{filter.processEtypeId}
            </if>
            <if test="filter.confirmEtypeId!=null ">
                and refund.confirm_etype_id=#{filter.confirmEtypeId}
            </if>
            <if test="filter.platformRefundTypes!=null and  filter.platformRefundTypes.size() >0">
                AND refund_extend.platform_refund_type in
                <foreach collection="filter.platformRefundTypes" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
            <if test="filter.platformRefundReasonStr != null and filter.platformRefundReasonStr != ''">
                AND reasonPlatform.refund_reason like CONCAT('%', #{filter.platformRefundReasonStr}, '%')
            </if>
            <if test="filter.platformSignStatus != null">
                AND refund_extend.platform_sign_status =#{filter.platformSignStatus}
            </if>
        </if>
        <if test="refundDutyIds!=null and refundDutyIds.size() > 0">
            <foreach collection="refundDutyIds" close=")" open="AND (" separator="or" item="key">
                INSTR(refund.refund_duty_ids, #{key}) >0
            </foreach>
        </if>
        <if test="refundDutyIds!=null and refundDutyIds.size() > 0">
            <foreach collection="refundDutyIds" close=")" open="AND (" separator="or" item="key">
                INSTR(refund.refund_duty_ids, #{key}) >0
            </foreach>
        </if>
        group by refund.id
    </sql>
    <sql id="notMark">
        OR
        (refund.id not in (select order_id
            from pl_eshop_order_mark
            where profile_id = #{profileId}
            and order_type = 2)
            AND
            !(
            (now() > timing.sys_promised_confirm_time AND refund.confirm_state = 0 AND refund.refund_type != 5 AND refund.refund_state not in (0,4,6,5))
            OR (now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type!=0 and
            refund.refund_type!=5
            and refund.refund_state != 0 AND refund.refund_state not in (0,4,5,6))
            OR (now() > timing.promised_confirm_time AND refund.platform_refund_state = 0 AND refund.refund_type != 2 AND refund.refund_type != 3 AND refund.refund_type != 4 AND refund.refund_state not in (0,4,6,5) AND refund.create_type = 1)
            OR (now() > timing.promised_deliver_time AND refund.refund_process_state = 0 AND refund.refund_state not in (0,4,6,5) AND refund.create_type = 1)
            OR (now() > timing.promised_agree_time AND refund.refund_state =1 AND refund.create_type = 1)
            ))
    </sql>
    <sql id="notMarkOnly">
        OR
        (refund.id not in (select order_id
            from pl_eshop_order_mark
            where profile_id = #{profileId}
            and order_type = 2)
            AND
            !(
            (now() > timing.sys_promised_confirm_time AND refund.confirm_state = 0 AND refund.refund_type != 5 AND refund.refund_state not in (0,4,6,5))
            OR (now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type!=0 and
            refund.refund_type!=5
            and refund.refund_state != 0 AND refund.refund_state not in (0,4,5,6))
            OR (now() > timing.promised_confirm_time AND refund.platform_refund_state = 0 AND refund.refund_type != 2 AND refund.refund_type != 3 AND refund.refund_type != 4 AND refund.refund_state not in (0,4,6,5) AND refund.create_type = 1)
            OR (now() > timing.promised_deliver_time AND refund.refund_process_state = 0 AND refund.refund_state not in (0,4,6,5) AND refund.create_type = 1)
            OR (now() > timing.promised_agree_time AND refund.refund_state =1 AND refund.create_type = 1)
            ))
    </sql>

    <sql id="forMarkType">
        <if test="item == 200073">
            (now() > timing.sys_promised_confirm_time and refund.confirm_state = 0 and refund.refund_type!=5
            and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND
            refund.refund_state != 6)
        </if>
        <if test="item == 200074">
            (now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type!=0 and
            refund.refund_type!=5
            and refund.refund_state != 0 AND refund.refund_state not in (0,4,5,6))
        </if>
        <if test="item == 200075">
            (now() > timing.promised_confirm_time AND refund.refund_type != 2 and refund.refund_type != 3 and
            refund.refund_type != 4 and refund.create_type = 1
            and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND
            refund.refund_state != 6)
        </if>
        <if test="item == 200076">
            (now() > timing.promised_deliver_time and refund.refund_process_state = 0
            and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND
            refund.refund_state != 6)
        </if>
        <if test="item == 200077">
            (now() > timing.promised_agree_time AND refund.refund_type != 2 AND refund.refund_type!=0 and
            refund.refund_type!=5 and refund.refund_type!=3 and refund.refund_type!=4
            and
            refund.create_type = 1 and refund.refund_state = 1 and refund.refund_state != 0 AND refund.refund_state != 4
            AND refund.refund_state != 5 AND refund.refund_state != 6
            AND refund.platform_return_state = 0)
        </if>
    </sql>
    <select id="queryRefundDetailList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        select 0 AS selected, rd.*,
        bp.fullname as ptype_name,bp.ptype_type,bp.ptype_area,
        bp.shortname as ptypeShortName,
        bp.usercode as ptype_code,bp.pcategory as pcategory,
        /* bp.barcode,*/
        bp.ptype_width,bp.ptype_length,bp.ptype_height,bp.weight as
        ptype_weight,bp.length_unit,bp.weight_unit,bp.standard as ptype_standard,
        bpx.xcode as xcode,bpf.fullbarcode as barcode,bp.sub_unit,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name,unit.unit_rate,price.retail_price,price.min_sale_price,
        bsunit.unit_rate,bsunit.unit_name as base_unit_name,
        pic.pic_url,bp.protect_days, bp.cost_price,bp.snenabled,bp.snenabled as
        snenabledFromDb,bp.batchenabled,bp.propenabled,bp.protect_days_unit,bp.protect_days_view,
        bp.cost_mode,bp.sub_unit as subUnit,
        rd.cost_period,rd.cost_state,rd.cost_price,
        rd.sub_qty AS subQty,
        refund.confirm_state,refund.receive_state as receive_status,refund.deleted,refund.refund_type,
        ifnull(rd.refund_state,refund.refund_state) as refund_state,refund.trade_refund_order_number,
        refund.otype_id,
        ifnull(rd.platform_pic_url,'') as platform_pic_url,rd.batchNo,rd.batch_price as batchPrice,
        rrcd.goods_state,rd.actual_receipt_number as actualReceiptNumber,
        rrcd.vchcode as checkinVchcode,rrcd.id as checkinDetailId,
        rd.gift,refund.refund_phase as refundPhase,refund.refund_type as refundTypeEnum,
        rd.dised_taxed_total as disedTaxedTotal,rd.sku_id,rd.unit,
        rd.re_send_state,
        rd.memo, rd.order_detail_qty,
        peraddb.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        peraddb.buyer_dised_taxed_total as distributionDisedTaxedTotal,
        brand.brand_name
        ,ex.eshop_order_id
        ,ex.refund_save_principal_total,ex.refund_save_present_total,ex.refund_save_total,ex.refund_preference_total,ex.refund_preference_allot_total
        from pl_eshop_refund_apply_detail rd
        left join pl_eshop_refund_apply_detail_extend ex on ex.profile_id = rd.profile_id and ex.refund_detail_id = rd.id
        left join pl_eshop_refund_apply_detail_combo as rdc on rdc.profile_id=rd.profile_id and
        rd.combo_row_id=rdc.id
        join pl_eshop_refund refund on refund.id=rd.refund_order_id and refund.profile_id=rd.profile_id
        left join base_ptype bp on bp.profile_id=rd.profile_id and rd.ptype_id=bp.id
        left join base_ptype_xcode bpx on bpx.profile_id=rd.profile_id and bpx.sku_id=rd.sku_id and bpx.unit_id=rd.unit
        and bpx.defaulted=1
        left join base_ptype_sku bps on bps.profile_id=rd.profile_id and bps.id=rd.sku_id
        left join base_ptype_pic pic on pic.profile_id=rd.profile_id and rd.ptype_id=pic.ptype_id and pic.rowindex=1
        left join base_ptype_fullbarcode bpf on bpf.profile_id=rd.profile_id and bpf.ptype_id=rd.ptype_id and
        bpf.sku_id=rd.sku_id and bpf.unit_id=rd.unit and bpf.defaulted=1
        left join base_ptype_unit unit on unit.profile_id=rd.profile_id and unit.id=rd.unit
        left join base_ptype_price price on price.profile_id=rd.profile_id and price.unit_id=unit.id and
        unit.ptype_id=rd.ptype_id and price.sku_id = rd.sku_id
        left join base_ptype_unit bsunit on bsunit.profile_id=rd.profile_id and bsunit.ptype_id=rd.ptype_id and
        bsunit.unit_code=1
        left join pl_eshop_refund_receive_checkin_detail rrcd on rd.check_detail_id=rrcd.id and rd.profile_id =
        rrcd.profile_id
        left join pl_eshop_refund_apply_detail_distribution_buyer peraddb on peraddb.profile_id = rd.profile_id and
        peraddb.refund_order_detail_id = rd.id
        left join base_brandtype brand on bp.profile_id = brand.profile_id and bp.brand_id = brand.id
        where rd.profile_id=#{profileId}
        <if test="refundOrderId != null and refundOrderId > 0">
            and rd.refund_order_id=#{refundOrderId}
        </if>
        <if test="refundOrderIdList != null and refundOrderIdList.size > 0">
            and rd.refund_order_id in
            <foreach collection="refundOrderIdList" separator="," open="(" close=")" item="detailId" index="0">
                #{detailId}
            </foreach>
        </if>
        <if test="checkOtherDetailForSameOrder == true">
            <if test="notEqualVchcode != null">
                and refund.id!=#{notEqualVchcode}
            </if>
            <if test="tradeOrderId != null and tradeOrderId != ''">
                and refund.trade_order_id=#{tradeOrderId}
            </if>
            <if test="otypeId != null and otypeId > 0">
                and refund.otype_id=#{otypeId}
            </if>
            <if test="refundDeleteStateEnum != null and refundDeleteStateEnum.getCode != -1">
                and refund.deleted=#{refundDeleteStateEnum}
            </if>
            <if test="notEqualConfirmState != null">
                and refund.confirm_state!=#{notEqualConfirmState}
            </if>
            <if test="eshopOrderDetailId != null and eshopOrderDetailId.size > 0">
                and rd.eshop_order_detail_id in
                <foreach collection="eshopOrderDetailId" separator="," open="(" close=")" item="detailId" index="0">
                    #{detailId}
                </foreach>
            </if>
        </if>
    </select>


    <select id="queryRefundComboDetailList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetailCombo">
        select rdc.*,
        ptype.fullname as ptype_name,ptype.id as ptypeId,ptype.usercode as ptype_code,
        ptype.barcode,ptype.sub_unit,ptype.weight_unit,ptype.weight as ptype_weight,ptype.propenabled,
        pic.pic_url,ptype.cost_price,rdc.price as unit_price,rdc.qty as unit_qty,
        rdc.actual_receipt_number as actualReceiptNumber,1 as unit_rate,bpcd.gifted as gift,rdc.order_detail_qty,
        distrbution_apply_combo.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        distrbution_apply_combo.buyer_dised_taxed_total as distributionDisedTaxedTotal
        from pl_eshop_refund_apply_detail_combo rdc
        left join base_ptype ptype on ptype.profile_id=rdc.profile_id and rdc.combo_id=ptype.id
        left join base_ptype_combo_detail bpcd on bpcd.profile_id=ptype.profile_id and ptype.id = bpcd.ptype_id
        left join base_ptype_pic pic on pic.profile_id=rdc.profile_id and rdc.combo_id=pic.ptype_id and pic.rowindex=1
        left join pl_eshop_refund_apply_detail_combo_distribution_buyer distrbution_apply_combo on
        distrbution_apply_combo.profile_id = rdc.profile_id and
        distrbution_apply_combo.refund_order_combo_detail_id = rdc.id and distrbution_apply_combo.refund_order_id =
        rdc.refund_order_id
        where rdc.profile_id=#{profileId}
        <if test="refundOrderId != null and refundOrderId > 0">
            and rdc.refund_order_id=#{refundOrderId}
        </if>
        <if test="refundOrderIdList != null and refundOrderIdList.size > 0">
            and rdc.refund_order_id in
            <foreach collection="refundOrderIdList" separator="," open="(" close=")" item="detailId" index="0">
                #{detailId}
            </foreach>
        </if>
    </select>

    <select id="queryComboPtypeId" resultType="java.math.BigInteger">
        SELECT id
        FROM base_ptype
        WHERE profile_id = #{profileId}
          AND pcategory = 2
    </select>

    <sql id="commonFromQueryRefundDetail">
        FROM pl_eshop_refund r
        <if test="detailParameter.ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=r.profile_id and blsk.object_type=2 and
            r.ktype_id=blsk.object_id and blsk.etype_id = #{detailParameter.etypeId}
        </if>
        <if test="detailParameter.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=r.profile_id and bls.object_type=3 and
            r.otype_id=bls.object_id and bls.etype_id = #{detailParameter.etypeId}
        </if>

        LEFT JOIN pl_eshop_refund_apply_detail rd ON rd.profile_id = r.profile_id AND rd.refund_order_id = r.id
        left join base_ptype ptype on ptype.id = rd.ptype_id and ptype.profile_id = rd.profile_id
        <if test="detailParameter.orderFilter == 2 or detailParameter.orderFilter == 0 and detailParameter.keyWord != ''">
            LEFT JOIN pl_eshop_sale_order eso ON eso.id = r.eshop_order_id AND eso.profile_id = r.profile_id
        </if>
        left join pl_eshop_refund_checkin_relation rel
        on rel.refund_order_id = r.id and rel.profile_id = r.profile_id
        left join pl_eshop_refund_receive_checkin checkin
        on checkin.vchcode = rel.checkin_id and checkin.profile_id = rel.profile_id
        where r.profile_id=#{profileId}
        and r.refund_type in (0,1,2,3,4)
        <if test="detailParameter.refundReason!=null and detailParameter.refundReason!=''">
            and r.refund_reason = #{detailParameter.refundReason}
        </if>
        <if test="detailParameter.refundDutyIds!=null and detailParameter.refundDutyIds.size()>0">
            and
            <foreach collection="detailParameter.refundDutyIds" item="duty" close=")" open="(" separator="or">
                r.refund_duty_ids like CONCAT('%', #{duty}, '%')
            </foreach>
        </if>
        # 1=创建时间,2=平台创建时间,3=售后审核时间,4=退货入库时间,5=退款支付时间
        <if test="detailParameter.timeType == 1">
            <if test="detailParameter.refundcreateTime != null">
                and r.create_time &gt;= #{detailParameter.refundcreateTime}
            </if>
            <if test="detailParameter.refundEndTime != null">
                and r.create_time &lt;= #{detailParameter.refundEndTime}
            </if>
        </if>
        <if test="detailParameter.timeType == 2">
            <if test="detailParameter.refundcreateTime != null">
                and r.refund_create_time &gt;= #{detailParameter.refundcreateTime}
            </if>
            <if test="detailParameter.refundEndTime != null">
                and r.refund_create_time &lt;= #{detailParameter.refundEndTime}
            </if>
        </if>
        <if test="detailParameter.timeType == 3">
            <if test="detailParameter.refundcreateTime != null">
                and r.confirm_time &gt;= #{detailParameter.refundcreateTime}
            </if>
            <if test="detailParameter.refundEndTime != null">
                and r.confirm_time &lt;= #{detailParameter.refundEndTime}
            </if>
        </if>
        <if test="detailParameter.timeType == 4">
            <if test="detailParameter.refundcreateTime != null">
                and r.receive_time &gt;= #{detailParameter.refundcreateTime}
            </if>
            <if test="detailParameter.refundEndTime != null">
                and r.receive_time &lt;= #{detailParameter.refundEndTime}
            </if>
        </if>
        <if test="detailParameter.timeType == 5">
            <if test="detailParameter.refundcreateTime != null">
                and r.pay_time &gt;= #{detailParameter.refundcreateTime}
            </if>
            <if test="detailParameter.refundEndTime != null">
                and r.pay_time &lt;= #{detailParameter.refundEndTime}
            </if>
        </if>
        <if test="detailParameter.timeType == 6">
            <if test="detailParameter.refundcreateTime != null">
                and checkin.checkin_time &gt;= #{detailParameter.refundcreateTime}
            </if>
            <if test="detailParameter.refundEndTime != null">
                and checkin.checkin_time &lt;= #{detailParameter.refundEndTime}
            </if>
        </if>

        <if test="detailParameter.eshops != null and detailParameter.eshops.size() > 0">
            and r.otype_id in
            <foreach collection="detailParameter.eshops" close=")" open="(" separator="," item="eshop">
                #{eshop}
            </foreach>
        </if>
        <if test="detailParameter.stocks != null and detailParameter.stocks.size() > 0">
            and r.ktype_id in
            <foreach collection="detailParameter.stocks" close=")" open="(" separator="," item="stock">
                #{stock}
            </foreach>
        </if>
        <if test="detailParameter.receivingGoods != 4">
            and r.receive_state = #{detailParameter.receivingGoods}
        </if>
        <if test="detailParameter.confirmState == 1">
            and r.confirm_state = 1
        </if>
        <if test="detailParameter.payState != 3">
            and r.pay_state = #{detailParameter.payState}
        </if>
        <if test="detailParameter.orderFilter == 1 and detailParameter.keyWord != ''">
            and r.trade_refund_order_number = #{detailParameter.keyWord}
        </if>
        <if test="detailParameter.orderFilter == 2 and detailParameter.keyWord != ''">
            and eso.trade_order_id = #{detailParameter.keyWord}
        </if>
        <if test="detailParameter.orderFilter == 0 and detailParameter.keyWord != ''">
            and (r.trade_refund_order_number = #{detailParameter.keyWord} or eso.trade_order_id =
            #{detailParameter.keyWord})
        </if>
        <if test="detailParameter.comboPtypeIdList != null and detailParameter.comboPtypeIdList.size() > 0">
            AND rd.ptype_id NOT IN
            <foreach collection="detailParameter.comboPtypeIdList" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="detailParameter.type.code == 0 and detailParameter.skuIds.size() > 0 ">
            AND rd.sku_id in
            <foreach collection="detailParameter.skuIds" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="detailParameter.type.code == 1 and detailParameter.ptypeWord!=''">
            AND (rd.platform_full_name like CONCAT('%', #{detailParameter.ptypeWord}, '%') or rd.platform_xcode like
            CONCAT('%', #{detailParameter.ptypeWord}, '%') or rd.platform_properties_name like CONCAT('%',
            #{detailParameter.ptypeWord}, '%'))
        </if>

        <if test="detailParameter.type.code == 2 and detailParameter.ptypeLabelIds.size() > 0 ">
            <!--            and label.labelfield_value_id in-->
            <!--            <foreach collection="detailParameter.ptypeLabelIds" close=")" open="(" separator="," item="labelId">-->
            <!--                #{labelId}-->
            <!--            </foreach>-->
            AND exists (
            select * from cf_data_label_ptype where profile_id = #{profileId} and
            labelfield_value_id in
            <foreach collection="detailParameter.ptypeLabelIds" close=")" open="(" separator="," item="labelId">
                #{labelId}
            </foreach>
            )
        </if>

        <if test="detailParameter.type.code == 3 and detailParameter.ptypeClassIds.size() > 0 ">
            and ptype.partypeid in
            <foreach collection="detailParameter.ptypeClassIds" close=")" open="(" separator="," item="labelId">
                #{labelId}
            </foreach>
        </if>

        <if test="detailParameter.refundType != null and detailParameter.refundType.size() >0">
            AND r.refund_type in
            <foreach collection="detailParameter.refundType" close=")" open="(" separator="," item="item">
                #{item.flag}
            </foreach>
        </if>
        <!--        <if test="detailParameter.ids != null and detailParameter.ids.size() > 0">-->
        <!--            AND-->
        <!--            <if test="detailParameter.type.code == 0">-->
        <!--                rd.ptype_id in-->
        <!--                <foreach collection="detailParameter.ids" close=")" open="(" separator="," item="item">-->
        <!--                    #{item}-->
        <!--                </foreach>-->
        <!--            </if>-->
        <!--            <if test="detailParameter.type.code == 1">-->
        <!--                (-->
        <!--                rd.platform_num_id in-->
        <!--                <foreach collection="detailParameter.ids" close=")" open="(" separator="," item="item">-->
        <!--                    #{item}-->
        <!--                </foreach>-->
        <!--                or-->
        <!--                rd.platform_sku_id in-->
        <!--                <foreach collection="detailParameter.ids" close=")" open="(" separator="," item="item">-->
        <!--                    #{item}-->
        <!--                </foreach>-->
        <!--                )-->
        <!--            </if>-->
        <!--        </if>-->
        <!--        <if test="detailParameter.refundType != null and detailParameter.refundType.size() >0">-->
        <!--            AND r.refund_type in-->
        <!--            <foreach collection="detailParameter.refundType" close=")" open="(" separator="," item="item">-->
        <!--                #{item.flag}-->
        <!--            </foreach>-->
        <!--        </if>-->
    </sql>

    <select id="queryRefundDetailVchcodesCount" resultType="java.lang.Long">
        SELECT count(distinct rd.id)
        <include refid="commonFromQueryRefundDetail"/>
    </select>

    <select id="queryRefundDetailVchcodes" resultType="java.math.BigInteger">
        SELECT r.id
        <include refid="commonFromQueryRefundDetail"/>
        <!--        <include refid="query-order">-->
        <!--            <property name="_order" value="sorts"/>-->
        <!--        </include>-->
    </select>

    <sql id="query-order">
        <if test="${_order} != null">
            <trim prefix=" order by ">
                <foreach collection="${_order}" item="item" separator=",">
                    ${item.dataField}
                    <choose>
                        <when test="item.ascending">
                            asc
                        </when>
                        <otherwise>
                            desc
                        </otherwise>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>

    <select id="queryRefundDetailInfoListNew"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetailInfo">
        SELECT r.id as refundOrderId,
        r.trade_refund_order_id,
        r.trade_refund_order_number,
        r.profile_id,
        r.otype_id,
        r.buyer_id,
        r.create_time,
        r.refund_create_time,
        r.refund_modify_time,
        r.trade_create_time,
        r.trade_finish_time,
        r.trade_pay_time,
        r.refund_reason,
        r.create_type,
        r.confirm_state,
        r.confirm_time,
        r.confirm_remark,
        r.confirm_etype_id,
        r.receive_state,
        r.receive_buyer_id,
        r.receive_remark,
        r.receive_time,
        r.pay_state,
        r.pay_account,
        r.pay_time,
        r.pay_etype_id,
        r.pay_number,
        r.receive_account,
        r.refund_apply_total,
        r.refund_apply_taxed_total,
        r.refund_apply_tax_total,
        r.refund_apply_freight_fee,
        r.refund_apply_mall_fee,
        r.refund_apply_service_fee,
        r.update_time,
        r.ktype_id,
        r.refund_state,
        r.refund_phase,
        r.refund_statement,
        r.trade_status,
        r.deleted,
        r.trade_order_id,
        r.eshop_order_id,
        r.no_detail,
        r.bill_vchcode,
        r.bill_poseted,
        r.bill_total,
        r.bill_service_fee,
        r.order_etype_id,
        r.has_edit,
        r.order_fee,
        r.refund_process_state,
        r.refund_process_time,
        r.mapping_state,
        r.cost_state,
        r.old_ktype_id,
        r.etype_id,
        r.refund_type as 'refundTypeEnum',
        rd.qty,
        rd.price,
        rd.apply_refund_qty,
        rd.platform_full_name,
        rd.platform_num_id,
        rd.platform_sku_id,
        rd.platform_properties_name,
        rd.platform_xcode,
        rd.apply_refund_taxed_total,
        rd.ptype_id,
        rd.produce_date,
        rd.expire_date,
        rd.batchNo AS 'batchCode',
        rd.apply_refund_taxed_total AS refund_price,
        rd.id as 'detailId',
        rd.apply_refund_freight_fee,
        rd.apply_refund_service_fee,
        IF(rd.dised_taxed_price = 0, 1, 0) as 'gift',
        rd.check_detail_id,
        rd.apply_refund_unit_qty,
        rd.sub_qty,
        bp.sub_unit,
        bps.prop_name1,
        bps.propvalue_name1,
        bps.prop_id1,
        bps.propvalue_id1,
        bps.prop_name2,
        bps.propvalue_name2,
        bps.prop_id2,
        bps.propvalue_id2,
        bps.prop_name3,
        bps.propvalue_name3,
        bps.prop_id3,
        bps.propvalue_id3,
        bps.prop_name4,
        bps.propvalue_name4,
        bps.prop_id4,
        bps.propvalue_id4,
        bps.prop_name5,
        bps.propvalue_name5,
        bps.prop_id5,
        bps.propvalue_id5,
        bps.prop_name6,
        bps.propvalue_name6,
        bps.prop_id6,
        bps.propvalue_id6,
        bp.fullName,
        bp.usercode,
        bp.protect_days as 'shelfLifeDay',
        bp.protect_days_unit,bp.protect_days_view,
        bp.snenabled,
        bp.batchenabled,
        bp.pcategory,
        bp.propenabled,
        ifnull(rea.refund_reason, r.refund_reason) as 'refundReason',
        bpf.fullbarcode AS skuBarcode,
        bpx.xcode as 'skuCode',
        u.unit_name AS unit,
        e.fullname AS eshops,
        rd.memo,
        rrcd.goods_state,rd.actual_receipt_number as actualReceiptNumber,
        r.refund_duty_ids,
        GROUP_CONCAT(distinct if(checkin.freight_name != '', checkin.freight_name, freight.freight_name)) as refundCompany,
        GROUP_CONCAT(distinct if(checkin.freight_bill_no != '', checkin.freight_bill_no, freight.freight_no)) as refundCompanyNumber,
        max(checkin.checkin_time) as checkinTime
        FROM pl_eshop_refund r
        left join pl_eshop_refund_checkin_relation rel on rel.refund_order_id = r.id and rel.profile_id = r.profile_id
        left join pl_eshop_refund_receive_checkin checkin on checkin.vchcode = rel.checkin_id and checkin.profile_id = rel.profile_id
        LEFT JOIN pl_eshop_refund_config_reason rea ON rea.id = r.refund_reason AND rea.profile_id = r.profile_id
        LEFT JOIN pl_eshop e ON e.otype_id = r.otype_id AND e.profile_id = r.profile_id
        LEFT JOIN pl_eshop_refund_apply_detail rd ON rd.profile_id = r.profile_id AND rd.refund_order_id = r.id
        left join pl_eshop_refund_receive_checkin_detail rrcd on rd.check_detail_id=rrcd.id and rd.profile_id =
        rrcd.profile_id
        LEFT JOIN base_ptype_unit u ON u.id = rd.unit AND u.profile_id = rd.profile_id
        LEFT JOIN base_ptype bp ON bp.id = rd.ptype_id AND bp.profile_id = rd.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.id = rd.sku_id AND bps.profile_id = rd.profile_id
        LEFT JOIN base_ptype_xcode bpx
        ON bpx.profile_id = rd.profile_id AND bpx.sku_id = rd.sku_id AND bpx.unit_id = rd.unit AND
        bpx.defaulted = 1
        LEFT JOIN base_ptype_fullbarcode bpf
        ON bpf.profile_id = rd.profile_id AND bpf.sku_id = rd.sku_id AND bpf.ptype_id = rd.ptype_id AND
        bpf.unit_id = rd.unit AND bpf.defaulted = 1
        LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id=r.profile_id and freight.refund_order_id=r.id and freight.freight_type != 1
        where r.profile_id=#{profileId}
        <if test="refundOrderIds != null and refundOrderIds.size() > 0">
            AND r.id IN
            <foreach collection="refundOrderIds" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="type.code == 0 and skuIds.size()>0">
            AND rd.sku_id in
            <foreach collection="skuIds" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="type.code == 1 and ptypeWord!=''">
            AND (rd.platform_full_name like CONCAT('%', #{ptypeWord}, '%') or rd.platform_xcode like CONCAT('%',
            #{ptypeWord}, '%') or rd.platform_properties_name like CONCAT('%', #{ptypeWord}, '%'))
        </if>
        <if test="type.code == 2 and ptypeLabelIds.size() > 0 ">
            <!--            and label.labelfield_value_id in-->
            <!--            <foreach collection="ptypeLabelIds" close=")" open="(" separator="," item="labelId">-->
            <!--                #{labelId}-->
            <!--            </foreach>-->
            AND exists (
            select * from cf_data_label_ptype where profile_id = #{profileId} and
            labelfield_value_id in
            <foreach collection="ptypeLabelIds" close=")" open="(" separator="," item="labelId">
                #{labelId}
            </foreach>
            )
        </if>
        <if test="type.code == 3 and ptypeClassIds.size() > 0 ">
            and bp.partypeid in
            <foreach collection="ptypeClassIds" close=")" open="(" separator="," item="labelId">
                #{labelId}
            </foreach>
        </if>
        group by r.id,rd.id
        <include refid="query-order">
            <property name="_order" value="sorts"/>
        </include>
        limit #{size}
    </select>

    <select id="queryRefundReceiveDetailInfoList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetailGoodsInfo">
        select r.*,inout_detail.unit_qty as'receiveTotalNum',u.unit_name as unit,bp.usercode,r.refund_type as
        'refundTypeEnum',
        bp.fullname,checkin_detail.produce_date,checkin_detail.expire_date,checkin_detail.batchno as 'batchCode',
        ck.checkin_number as 'receiveNo', ck.vchcode as 'ckVchcode',bpx.xcode as 'skuCode',bp.protect_days as
        'shelfLifeDay',checkin_detail.apply_refund_taxed_total,
        ck.checkin_time,e.fullname as eshops,bpf.fullbarcode as 'skuBarcode',checkin_detail.id as 'detailId',
        checkin_detail.apply_refund_freight_fee,checkin_detail.apply_refund_service_fee,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6
        from pl_eshop_refund r
        left join pl_eshop_refund_receive_checkin ck on ck.refund_order_id = r.id and ck.profile_id = r.profile_id
        left join pl_eshop_refund_receive_checkin_detail checkin_detail on ck.profile_id = checkin_detail.profile_id and
        ck.vchcode = checkin_detail.vchcode
        LEFT JOIN td_bill_inout_detail inout_detail ON inout_detail.profile_id = checkin_detail.profile_id and
        inout_detail.inout_detail_id = checkin_detail.inout_detail_id
        LEFT JOIN pl_eshop_sale_order eso ON eso.id = r.eshop_order_id AND eso.profile_id = r.profile_id
        left join base_ptype_unit u on inout_detail.unit_id = u.id and r.profile_id = u.profile_id
        left join pl_eshop e on r.otype_id = e.otype_id and r.profile_id = e.profile_id
        left join base_ptype bp on bp.id=inout_detail.ptype_id and bp.profile_id = inout_detail.profile_id
        left join base_ptype_sku bps on bps.profile_id=inout_detail.profile_id and bps.id=inout_detail.sku_id
        LEFT JOIN base_ptype_xcode bpx on bpx.profile_id=inout_detail.profile_id and bpx.sku_id=inout_detail.sku_id and
        bpx.unit_id=inout_detail.unit_id
        and bpx.defaulted=1
        left join base_ptype_fullbarcode bpf on bpf.profile_id=inout_detail.profile_id and
        bpf.ptype_id=inout_detail.ptype_id and
        bpf.sku_id=inout_detail.sku_id and bpf.unit_id=inout_detail.unit_id and bpf.defaulted=1
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=r.profile_id and bls.object_type=3 and
            r.otype_id=bls.object_id and bls.etype_id = #{etypeId}
        </if>
        <if test="etypeLimited">
            inner join base_limit_scope blse on blse.profile_id=r.profile_id and blse.object_type=1 and
            r.etype_id = blse.object_id and blse.etype_id = #{etypeId}
        </if>
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=r.profile_id and blsk.object_type=2 and
            r.ktype_id=blsk.object_id and blsk.etype_id = #{etypeId}
        </if>
        where r.profile_id=#{profileId} and r.confirm_state = 1 and bp.pcategory &lt;&gt; 2
        <if test="refundInfocreateTime != null">
            and r.create_time between #{refundInfocreateTime} and #{refundInfoEndTime}
        </if>
        <if test="infoEshops != null and infoEshops.size() > 0">
            and r.otype_id in
            <foreach collection="infoEshops" close=")" open="(" separator="," item="eshop">
                #{eshop}
            </foreach>
        </if>
        <if test="refundPaystate != 3">
            and r.pay_state = #{refundPaystate}
        </if>
        <if test="receiveRegister != 3">
            and r.receive_state = #{receiveRegister}
        </if>
        <if test="quickFilter == 1 and orderNo != ''">
            and r.trade_refund_order_number = #{orderNo}
        </if>
        <if test="quickFilter == 2 and orderNo != ''">
            and eso.trade_order_id = #{orderNo}
        </if>
        <if test="quickFilter == 0 and orderNo != ''">
            and r.trade_refund_order_number = #{orderNo} or eso.trade_order_id = #{orderNo}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <select id="queryRefundDetailsByRefundNumber" resultType="integer">
        SELECT count(*)
        from pl_eshop_refund_apply_detail rd
        WHERE rd.refund_order_id = #{refundOrderId}
          AND rd.profile_id = #{profileId}
    </select>

    <select id="queryRefundSendDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundSendDetail">
        SELECT
        detail.id, detail.profile_id, detail.ptype_id, detail.sku_id, detail.unit, detail.sub_unit, detail.qty,
        detail.unit_qty, detail.sub_qty, detail.goods_state, detail.create_time, detail.update_time, detail.batchno,
        detail.expire_date, detail.produce_date, detail.price, detail.total,
        detail.taxed_price, detail.taxed_total, detail.dised_taxed_price, detail.dised_taxed_total, detail.combo_row_id,
        ptype.tax_rate, detail.combo_share_scale, detail.cost_id, detail.refund_order_id, detail.batch_price,
        detail.platform_num_id, detail.platform_sku_id, detail.platform_full_name,
        detail.platform_properties_name, detail.platform_xcode, detail.mapping_state,
        ptype.pcategory,
        ptype.fullname AS ptype_name,ptype.ptype_type,ptype.ptype_area,ptype.shortname AS ptypeShortName,
        ptype.usercode AS ptype_code, ptype.ptype_width,ptype.ptype_length,ptype.ptype_height,
        ptype.weight AS ptype_weight,ptype.length_unit,ptype.weight_unit,ptype.propenabled,
        ptype.standard AS ptype_standard, xcode.xcode,fcode.fullbarcode AS barcode,
        sku.prop_name1,sku.propvalue_name1,sku.prop_id1,sku.propvalue_id1,
        sku.prop_name2,sku.propvalue_name2,sku.prop_id2,sku.propvalue_id2,
        sku.prop_name3,sku.propvalue_name3,sku.prop_id3,sku.propvalue_id3,
        sku.prop_name4,sku.propvalue_name4,sku.prop_id4,sku.propvalue_id4,
        sku.prop_name5,sku.propvalue_name5,sku.prop_id5,sku.propvalue_id5,
        sku.prop_name6,sku.propvalue_name6,sku.prop_id6,sku.propvalue_id6,
        unit.unit_name,unit.unit_rate,price.retail_price,price.min_sale_price,
        bsunit.unit_name AS
        base_unit_name,pic.pic_url,ptype.protect_days,ptype.protect_days_unit,ptype.protect_days_view,
        ptype.snenabled,ptype.batchenabled,ptype.cost_mode,ptype.sub_unit as subUnit,
        detail.gift,
        detail_distribution.buyer_dised_taxed_total as distributionDisedTaxedTotal,
        detail_distribution.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        ptype_brand.brand_name
        from pl_eshop_refund_send_detail detail
        left join pl_eshop_refund_send_detail_distribution_buyer detail_distribution on detail_distribution.profile_id =
        detail.profile_id and
        detail_distribution.refund_order_detail_id = detail.id and detail_distribution.refund_order_id =
        detail.refund_order_id
        left join pl_eshop_refund_send_detail_combo as combo on combo.profile_id=detail.profile_id and
        detail.combo_row_id=combo.id
        <include refid="detailJoinFromOrder"/>
        WHERE detail.profile_id=#{profileId} AND detail.refund_order_id=#{refundOrderId}
    </select>

    <select id="queryRefundSendDetailCombos"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundSendDetailCombo">
        select combo.*,
               ptype.fullname                             as ptype_name,
               ptype.id                                   as ptypeId,
               ptype.usercode                             as ptype_code,
               ptype.barcode,
               ptype.weight_unit,
               ptype.weight                               as ptype_weight,
               pic.pic_url,
               ptype.cost_price,
               ptype.propenabled,
               combo.qty                                  as unit_qty,
               1                                          as unit_rate,
               true                                       as comboRow,
               combo_distribution.buyer_dised_taxed_total as distributionDisedTaxedTotal,
               combo_distribution.buyer_dised_taxed_price as distributionDisedTaxedPrice
        from pl_eshop_refund_send_detail_combo combo
                 left join pl_eshop_refund_send_detail_combo_distribution_buyer combo_distribution
                           on combo_distribution.profile_id = combo.profile_id and
                              combo_distribution.refund_order_id = combo.refund_order_id and
                              combo_distribution.refund_order_combo_detail_id = combo.id
                 left join base_ptype ptype on ptype.profile_id = combo.profile_id and combo.combo_id = ptype.id
                 left join base_ptype_pic pic
                           on pic.profile_id = combo.profile_id and combo.combo_id = pic.ptype_id and pic.rowindex = 1
        where combo.profile_id = #{profileId}
          AND combo.refund_order_id = #{refundOrderId}
    </select>
    <select id="queryRefundDetailSerialInfos"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundDetailSerialNo">
        select
        id, profile_id, snno, sn1, sn2, sn3, sn_memo, create_time, update_time, refund_order_id,
        <if test="tableName == 'pl_eshop_refund_send_detail_serialno'">
            refund_order_send_detail_id as detailId
        </if>
        <if test="tableName == 'pl_eshop_refund_apply_detail_serialno'">
            refund_order_detail_id as detailId
        </if>
        from ${tableName}
        where profile_id = #{profileId}
        and refund_order_id = #{refundOrderId}
        <if test="tableName == 'pl_eshop_refund_send_detail_serialno'">
            and refund_order_send_detail_id = #{detailId}
        </if>
        <if test="tableName == 'pl_eshop_refund_apply_detail_serialno'">
            and refund_order_detail_id = #{detailId}
        </if>
    </select>

    <select id="getRefundInfoFromOrder" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="saleorderColumn"/>
        FROM pl_eshop_sale_order `order`
        LEFT JOIN pl_eshop_sale_order_extend extend ON extend.profile_id = `order`.profile_id AND extend.eshop_order_id
        = `order`.id
        LEFT JOIN pl_eshop eshop on eshop.profile_id=order.profile_id and order.otype_id=eshop.otype_id
        LEFT JOIN pl_buyer buyer on buyer.profile_id=order.profile_id and buyer.buyer_id = order.buyer_id
        LEFT JOIN base_etype etype on etype.profile_id = order.profile_id and order.etype_id = etype.id
        where order.profile_id=#{profileId} and order.trade_order_id=#{tradeOrderId} and order.otype_id=#{otypeId}

        <!--UNION-->

        <!--SELECT-->
        <!--<include refid="saleorderColumn"/>-->
        <!--FROM cold_pl_eshop_sale_order order-->
        <!--LEFT JOIN pl_eshop eshop on eshop.profile_id=order.profile_id and order.otype_id=eshop.otype_id-->
        <!--LEFT JOIN pl_buyer buyer on buyer.profile_id=order.profile_id and buyer.buyer_id = order.buyer_id-->
        <!--where order.profile_id=#{profileId} and order.trade_order_id=#{tradeOrderId} and order.otype_id=#{otypeId}-->
    </select>

    <select id="queryDeliverRefundDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        SELECT
        <include refid="deliverDetailColumn"/>
        FROM td_bill_detail_deliver bdd
        LEFT JOIN td_bill_deliver deliver ON bdd.profile_id = deliver.profile_id and bdd.vchcode = deliver.vchcode
        LEFT JOIN td_bill_assinfo assinfo ON deliver.profile_id = assinfo.profile_id and deliver.vchcode =
        assinfo.vchcode
        JOIN td_bill_detail_core core ON core.profile_id=bdd.profile_id AND core.vchcode=bdd.vchcode AND
        core.detail_id= bdd.detail_id
        JOIN td_bill_detail_assinfo bda ON bda.profile_id=bdd.profile_id AND bda.vchcode=bdd.vchcode AND
        bda.detail_id= bdd.detail_id
        JOIN td_bill_core tdcore ON tdcore.profile_id=bdd.profile_id AND tdcore.vchcode=bdd.vchcode AND tdcore.deleted=0
        left join acc_bill_detail_purchase purchase on bdd.profile_id = purchase.profile_id and bdd.detail_id =
        purchase.detail_id
        <include refid="deliverDetailJoin"/>
        WHERE bdd.trade_order_id=#{tradeOrderId} AND bdd.profile_id=#{profileId}
        and tdcore.create_type != 2
    </select>

    <select id="queryDeliverDetailSerialNos"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundDetailSerialNo">
        select *, vchcode as refundOrderId
        from td_bill_detail_serialno
        where profile_id = #{profileId}
          and vchcode = #{refundOrderId}
          and detail_id = #{detailId}
    </select>

    <select id="quereyDeliverRefundComboDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        SELECT
        <include refid="deliverComboColumn"/>
        FROM td_bill_detail_combo combo
        JOIN td_bill_detail_combo_deliver deliver ON deliver.profile_id=combo.profile_id AND
        deliver.vchcode=combo.vchcode AND deliver.id=combo.id
        left join td_bill_detail_combo_assinfo assinfo on assinfo.combo_detail_id = combo.id and assinfo.profile_id =
        combo.profile_id
        left join td_bill_detail_combo_deliver_expand deliverComboExpand on deliver.id=deliverComboExpand.id and
        deliver.profile_id=deliverComboExpand.profile_id
        JOIN td_bill_core tdcore ON tdcore.profile_id=combo.profile_id AND tdcore.vchcode=combo.vchcode AND
        tdcore.deleted=0
        JOIN td_bill_deliver tddeliver on tddeliver.profile_id=combo.profile_id AND tddeliver.vchcode=combo.vchcode
        LEFT JOIN base_ptype ptype ON ptype.profile_id=combo.profile_id AND combo.combo_id=ptype.id
        LEFT JOIN base_ptype_pic pic ON pic.profile_id=combo.profile_id AND combo.combo_id=pic.ptype_id AND
        pic.rowindex=1
        WHERE deliver.trade_order_id=#{tradeOrderId} AND combo.profile_id= #{profileId}
        and tdcore.create_type != 2
    </select>
    <select id="queryEshopRefundDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        SELECT
        <include refid="detailColumnFormOrder"/>
        FROM pl_eshop_sale_order sorder
        JOIN pl_eshop_sale_order_detail detail ON sorder.profile_id=detail.profile_id AND
        sorder.id=detail.eshop_order_id
        left join base_ptype bp on bp.profile_id=detail.profile_id and detail.ptype_id=bp.id
        LEFT JOIN base_brandtype brand on bp.profile_id = brand.profile_id and bp.brand_id = brand.id
        LEFT JOIN pl_eshop_sale_order_detail_distribution_buyer detail_distribution on detail_distribution.profile_id =
        detail.profile_id
        and detail_distribution.eshop_order_detail_id = detail.id
        LEFT JOIN pl_eshop_sale_order_detail_purchase detail_purchase on detail_purchase.profile_id = detail.profile_id
        and detail_purchase.eshop_order_detail_id = detail.id
        left join pl_eshop_sale_order_detail_combo as combo on combo.profile_id=detail.profile_id and
        detail.combo_row_id=combo.eshop_order_detail_combo_row_id
        <include refid="detailJoinFromOrder"/>
        WHERE sorder.trade_order_id=#{tradeOrderId} AND sorder.profile_id=#{profileId} and sorder.otype_id=#{otypeId}
        and sorder.id=#{eshopOrderId} and detail.mapping_state!=0
        <!--UNION-->

        <!--SELECT <include refid="detailColumnFormOrder"/>-->
        <!--FROM cold_pl_eshop_sale_order sorder-->
        <!--JOIN cold_pl_eshop_sale_order_detail detail ON sorder.profile_id=detail.profile_id AND sorder.vchcode=detail.vchcode-->
        <!--<include refid="detailJoinFromOrder"/>-->
        <!--WHERE sorder.trade_order_id=#{tradeOrderId} AND sorder.profile_id=#{profileId}-->
    </select>
    <select id="queryEshopRefundComboDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetailCombo">
        SELECT
        <include refid="orderComboColumn"/>
        FROM pl_eshop_sale_order sorder
        JOIN pl_eshop_sale_order_detail_combo combo ON combo.eshop_order_id = sorder.id AND
        combo.profile_id=sorder.profile_id
        LEFT JOIN pl_eshop_sale_order_detail_combo_distribution_buyer combo_distribution on
        combo_distribution.profile_id = combo.profile_id
        and combo_distribution.eshop_order_combo_row_id = combo.eshop_order_detail_combo_row_id
        LEFT JOIN pl_eshop_sale_order_detail_combo_purchase combo_purchase on combo_purchase.profile_id =
        combo.profile_id and combo_purchase.eshop_order_combo_row_id = combo.eshop_order_detail_combo_row_id
        LEFT JOIN base_ptype ptype ON ptype.profile_id=combo.profile_id AND combo.combo_id=ptype.id
        LEFT JOIN base_ptype_pic pic ON pic.profile_id=combo.profile_id AND combo.combo_id=pic.ptype_id AND
        pic.rowindex=1
        WHERE sorder.profile_id=#{profileId} AND sorder.trade_order_id=#{tradeOrderId}
        and sorder.otype_id=#{otypeId}
        and sorder.id=#{eshopOrderId}
        <!--UNION-->

        <!--SELECT <include refid="orderComboColumn"/>-->
        <!--FROM cold_pl_eshop_sale_order sorder-->
        <!--JOIN cold_pl_eshop_sale_order_detail_combo combo ON combo.vchcode = sorder.vchcode AND combo.profile_id=sorder.profile_id-->
        <!--LEFT JOIN base_ptype ptype ON ptype.profile_id=combo.profile_id AND combo.combo_id=ptype.id-->
        <!--LEFT JOIN base_ptype_pic pic ON pic.profile_id=combo.profile_id AND combo.combo_id=pic.ptype_id AND pic.rowindex=1-->
        <!--WHERE sorder.profile_id=#{profileId} AND sorder.trade_order_id=#{tradeOrderId}-->
    </select>

    <select id="quereyRefundConfig" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfig">
        select cfg.*, a.fullname as atype_name, k.fullname as ktype_name
        from pl_eshop_refund_config cfg
                 left join base_ktype k on k.profile_id = cfg.profile_id and k.id = cfg.default_ktype_id
                 left join base_atype a on a.profile_id = cfg.profile_id and a.id = cfg.atype_id
        where cfg.profile_id = #{profileId}
    </select>
    <select id="quereyRefundConfigReason"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        select * from pl_eshop_refund_config_reason where profile_id=#{profileId} and deleted=0 and refund_reason != ''
        <if test="reasonType != null and reasonType.getCode() == 3">
            and reason_type in (0,2)
        </if>
        <if test="reasonType != null and reasonType.getCode() == 1">
            and reason_type in (1)
        </if>
        <if test="reasonId != null and reasonId != '' and reasonId != '0'">
            and id=#{reasonId}
        </if>
        order by create_time
    </select>

    <update id="modifyRefundConfigReason">
        update pl_eshop_refund_config_reason
        set custome_default_type=#{customeDefaultType}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <select id="quereyRefundPayDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundPayDetail">
        select * from pl_eshop_refund_pay_detail where profile_id=#{profileId} and refund_order_id=#{refundOrderId}
        <if test="detailId != null and detailId > 0">
            and refund_order_apply_detail_id =#{detailId}
        </if>
    </select>
    <select id="queryRefundFreights" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundFreight">
        SELECT frt.id,
               frt.profile_id,
               frt.refund_order_id,
               frt.freight_template_id,
               frt.freight_no,
               frt.create_time,
               frt.update_time,
               CASE IFNULL(frt.freight_btype_id, 0)
                   WHEN 0 THEN lg.freight_btype_id
                   ELSE frt.freight_btype_id END AS freight_btype_id,
               temp.template_name                AS freight_template_name,
               frt.freight_name,
               frt.freight_code,
               frt.freight_type,
               frt.freight_intercept_status
        FROM pl_eshop_refund_freight frt
                 LEFT JOIN td_template temp ON temp.profile_id = frt.profile_id AND temp.id = frt.freight_template_id
                 LEFT JOIN td_logistics_template lg
                           ON temp.`id` = lg.`template_id` AND temp.`profile_id` = lg.`profile_id`
                 LEFT JOIN base_btype btype ON btype.profile_id = frt.profile_id AND btype.id = lg.freight_btype_id
                 LEFT JOIN base_btype btype1 ON btype1.profile_id = frt.profile_id AND btype1.id = frt.freight_btype_id
        WHERE frt.profile_id = #{profileId}
          and frt.refund_order_id = #{refundOrderId}
    </select>

    <insert id="insertRefundInfo">
        INSERT INTO pl_eshop_refund (id, trade_refund_order_id, trade_refund_order_number, profile_id, otype_id,
                                     buyer_id,
                                     refund_create_time, refund_modify_time, trade_create_time,
                                     trade_finish_time, trade_pay_time, refund_type, refund_reason, create_type,
                                     confirm_state, confirm_time, confirm_remark, confirm_etype_id, receive_state,
                                     receive_buyer_id, receive_remark, receive_time, pay_state, pay_account,
                                     pay_time, pay_etype_id, pay_number, receive_account, refund_apply_total,
                                     refund_apply_taxed_total, refund_apply_tax_total, refund_apply_freight_fee,
                                     refund_apply_mall_fee, refund_apply_service_fee, ktype_id, refund_state,
                                     refund_phase, refund_statement, trade_status, deleted, trade_order_id,
                                     eshop_order_id, no_detail,
                                     bill_vchcode, bill_poseted, bill_service_fee, order_etype_id, order_fee,
                                     refund_process_state, mapping_state, etype_id, old_ktype_id, memo, business_type,
                                     platform_parent_order_id, supplier_id, btype_id, refund_duty_ids,
                                     refund_finish_time)
        VALUES (#{id}, #{tradeRefundOrderId}, #{tradeRefundOrderNumber}, #{profileId}, #{otypeId}, #{buyerId},
                #{refundCreateTime},
                #{refundModifyTime}, #{tradeCreateTime},
                #{tradeFinishTime}, #{tradePayTime}, #{refundTypeEnum}, #{refundReason}, #{createType}, #{confirmState},
                #{confirmTime}, #{confirmRemark},
                #{confirmEtypeId}, #{receiveState}, #{receiveBuyerId}, #{receiveRemark}, #{receiveTime}, #{payState},
                #{payAccount}, #{payTime}, #{payEtypeId},
                #{payNumber}, #{receiveAccount}, #{refundApplyTotal}, #{refundApplyTaxedTotal}, #{refundApplyTaxTotal},
                #{refundApplyFreightFee},
                #{refundApplyMallFee}, #{refundApplyServiceFee}, #{ktypeId}, #{refundState}, #{refundPhase},
                #{refundStatement}, #{tradeStatus},
                #{deleted}, #{tradeOrderId}, #{eshopOrderId}, #{noDetail}, #{billVchcode}, #{billPoseted},
                #{billServiceFee}, #{orderEtypeId}, #{orderFee}, #{refundProcessState}, #{mappingState}, #{etypeId},
                #{oldKtypeId}, #{memo}, #{businessType}, #{platformParentOrderId}, #{supplierId}, #{btypeId},
                #{refundDutyIds}, #{refundFinishTime})
        ON duplicate key
            update refund_state=#{refundState},
                   id=#{id},
                   buyer_id=#{buyerId},
                   refund_reason=#{refundReason}
    </insert>
    <insert id="insertRefundDetails">
        INSERT INTO pl_eshop_refund_apply_detail
        (refund_order_id, id, profile_id, trade_order_id, trade_order_detail_id, platform_num_id, platform_sku_id,
        platform_full_name,
        platform_properties_name, platform_xcode,
        ptype_id, sku_id, unit, qty, sub_qty, price, unit_price, sub_price, trade_total, total, taxed_total, tax_total,
        freight_fee, mall_fee,
        service_fee, apply_refund_qty, apply_refund_unit_qty, apply_refund_sub_qty, apply_refund_total,
        apply_refund_taxed_total, apply_refund_tax_total,
        apply_refund_freight_fee, apply_refund_mall_fee, apply_refund_service_fee, combo_row_id,
        taxed_price,dised_taxed_price,dised_taxed_total,batchno,batch_price,expire_date,produce_date,tax_rate,
        eshop_order_detail_id, refund_state, platform_pic_url, check_detail_id, mapping_state, create_type,
        combo_share_scale, cost_id, cost_type, cost_state, cost_period, cost_price,
        order_detail_total,
        order_detail_price,order_detail_qty,source_vchcode,source_detail_id,
        purchase_price,purchase_total,re_send_state,memo,actual_receipt_number,gift,task_vchcode,task_detail_id)
        VALUES
        <foreach collection="details" index="index" item="item" open=" " separator="," close=" ">
            ( #{item.refundOrderId}, #{item.detailId}, #{item.profileId}, #{item.tradeOrderId},
            #{item.tradeOrderDetailId}, #{item.platformNumId},
            #{item.platformSkuId}, #{item.platformFullName}, #{item.platformPropertiesName},
            #{item.platformXcode}, #{item.ptypeId}, #{item.skuId}, #{item.unit}, #{item.qty}, #{item.subQty},
            #{item.price}, #{item.unitPrice}, #{item.subPrice}, #{item.tradeTotal}, #{item.total}, #{item.taxedTotal},
            #{item.taxTotal}, #{item.freightFee}, #{item.mallFee}, #{item.serviceFee}, #{item.applyRefundQty},
            #{item.applyRefundUnitQty}, #{item.applyRefundSubQty}, #{item.applyRefundTotal},
            #{item.applyRefundTaxedTotal}, #{item.applyRefundTaxTotal}, #{item.applyRefundFreightFee},
            #{item.applyRefundMallFee}, #{item.applyRefundServiceFee},
            #{item.comboRowId}, #{item.taxedPrice}, #{item.disedTaxedPrice}, #{item.disedTaxedTotal}, #{item.batchno},
            #{item.batchPrice},
            #{item.expireDate},#{item.produceDate},#{item.taxRate},#{item.eshopOrderDetailId},#{item.refundState},
            #{item.platformPicUrl},#{item.checkDetailId},#{item.mappingState},#{item.createType},#{item.comboShareScale},#{item.costId},#{item.costType},
            #{item.costState},#{item.costPeriod},#{item.costPrice},#{item.orderDetailTotal},#{item.orderDetailPrice},#{item.orderDetailQty},#{item.sourceVchcode},#{item.sourceDetailId},
            #{item.purchasePrice},#{item.purchaseTotal},#{item.reSendState},#{item.memo},#{item.actualReceiptNumber},#{item.gift}
            ,#{item.taskVchcode},#{item.taskDetailId})
        </foreach>
    </insert>
    <insert id="insertRefundComboDetails">
        INSERT INTO pl_eshop_refund_apply_detail_combo
        (refund_order_id, id, profile_id, trade_order_id, trade_order_detail_id, combo_id, qty, price, trade_total,
        total, taxed_total, tax_total,
        freight_fee, mall_fee,
        service_fee, apply_refund_qty, apply_refund_total, apply_refund_taxed_total, apply_refund_tax_total,
        apply_refund_freight_fee,
        apply_refund_mall_fee, apply_refund_service_fee, closed,
        taxed_price,dised_taxed_price,dised_taxed_total,
        eshop_order_detail_id,check_combo_row_id,order_detail_total,order_detail_price,order_detail_qty,source_vchcode,source_detail_id,
        purchase_price,purchase_total,actual_receipt_number,task_vchcode,task_detail_id)
        VALUES
        <foreach collection="combos" index="index" item="item" open="" separator="," close="">
            (#{item.refundOrderId}, #{item.id}, #{item.profileId}, #{item.tradeOrderId}, #{item.tradeOrderDetailId},
            #{item.comboId},
            #{item.qty},
            #{item.price}, #{item.tradeTotal}, #{item.total}, #{item.taxedTotal}, #{item.taxTotal},
            #{item.freightFee}, #{item.mallFee}, #{item.serviceFee}, #{item.applyRefundQty}, #{item.applyRefundTotal},
            #{item.applyRefundTaxedTotal}, #{item.applyRefundTaxTotal},
            #{item.applyRefundFreightFee}, #{item.applyRefundMallFee}, #{item.applyRefundServiceFee}, #{item.closed},
            #{item.taxedPrice},#{item.disedTaxedPrice},#{item.disedTaxedTotal},#{item.eshopOrderDetailId},#{item.checkComboRowId},
            #{item.orderDetailTotal},#{item.orderDetailPrice},#{item.orderDetailQty},#{item.sourceVchcode},#{item.sourceDetailId},
            #{item.purchasePrice},#{item.purchaseTotal},#{item.actualReceiptNumber},#{item.taskVchcode},#{item.taskDetailId})
        </foreach>
    </insert>

    <insert id="insertRefundSendDetails">
        <if test="details != null and details.size > 0">
            insert into pl_eshop_refund_send_detail (
            id, refund_order_id, profile_id, ptype_id, sku_id,
            unit, qty, unit_qty, sub_qty,
            batchno,batch_price, expire_date, produce_date, price,
            total, taxed_price, taxed_total,
            dised_taxed_price, dised_taxed_total,tax_rate,
            combo_row_id,combo_share_scale,platform_num_id,platform_sku_id,
            platform_full_name,platform_properties_name,platform_xcode,mapping_state,
            purchase_price,purchase_total,gift)
            values
            <foreach collection="details" index="index" item="item" open="" separator="," close="">
                (#{item.id},
                #{item.refundOrderId},
                #{item.profileId},
                #{item.ptypeId},
                #{item.skuId},
                #{item.unit},
                #{item.qty},
                #{item.unitQty},
                #{item.subQty},
                #{item.batchno},
                #{item.batchPrice},
                #{item.expireDate},
                #{item.produceDate},
                #{item.price},
                #{item.total},
                #{item.taxedPrice},
                #{item.taxedTotal},
                #{item.disedTaxedPrice},
                #{item.disedTaxedTotal},
                #{item.taxRate},
                #{item.comboRowId},
                #{item.comboShareScale},
                #{item.platformNumId},
                #{item.platformSkuId},
                #{item.platformFullName},
                #{item.platformPropertiesName},
                #{item.platformXcode},
                #{item.mappingState},
                #{item.purchasePrice},
                #{item.purchaseTotal},
                #{item.gift})
            </foreach>
        </if>
    </insert>
    <insert id="insertRefundSendDetailCombos">
        <if test="combos != null and combos.size > 0">
            insert into pl_eshop_refund_send_detail_combo (<include refid="sendComboColumn"/>)
            values
            <foreach collection="combos" index="index" item="combo" open="" separator="," close="">
                ( #{combo.id}, #{combo.refundOrderId}, #{combo.profileId}, #{combo.comboId}, #{combo.qty},
                #{combo.price},
                #{combo.total},
                #{combo.taxedPrice}, #{combo.price}, #{combo.disedTaxedPrice}, #{combo.disedTaxedTotal},
                #{combo.purchasePrice},#{combo.purchaseTotal})
            </foreach>
        </if>
    </insert>
    <insert id="insertRefundDetailSerials">
        <if test="serials != null and serials.size > 0">
            insert into ${tableName} (id, refund_order_id,
            <if test="tableName == 'pl_eshop_refund_send_detail_serialno'">
                refund_order_send_detail_id
            </if>
            <if test="tableName == 'pl_eshop_refund_apply_detail_serialno'">
                refund_order_detail_id
            </if>
            , profile_id, snno, sn1, sn2, sn3 , sn_memo)
            values
            <foreach collection="serials" index="index" item="sno" open="" separator="," close="">
                ( #{sno.id},
                #{sno.refundOrderId},
                #{sno.detailId},
                #{sno.profileId},
                #{sno.snno},
                #{sno.sn1},
                #{sno.sn2},
                #{sno.sn3},
                #{sno.snMemo})
            </foreach>
        </if>
    </insert>

    <update id="updateRefund">
        UPDATE pl_eshop_refund
        SET confirm_time=#{confirmTime},order_etype_id=#{orderEtypeId}
        <if test="refundPhase != null">
            ,refund_phase = #{refundPhase}
        </if>
        <if test="tradeOrderId != null and tradeOrderId != ''">
            ,trade_order_id=#{tradeOrderId}
        </if>
        <if test="buyerId != null and buyerId != ''">
            ,buyer_id=#{buyerId}
        </if>
        <if test="refundTypeEnum != null">
            ,refund_type=#{refundTypeEnum}
        </if>
        <if test="refundReason != null and refundReason != ''">
            ,refund_reason=#{refundReason}
        </if>
        <!--<if test="createType!=null">-->
        <!--,create_type=#{createType}-->
        <!--</if>-->
        <if test="confirmState != null">
            ,confirm_state=#{confirmState}
        </if>
        <if test="confirmTime != null">
            ,confirm_time=#{confirmTime}
        </if>
        <if test="confirmRemark != null">
            ,confirm_remark=#{confirmRemark}
        </if>
        <if test="confirmEtypeId != null and confirmEtypeId > 0">
            ,confirm_etype_id=#{confirmEtypeId}
        </if>
        <if test="receiveState != null">
            ,receive_state = if(receive_state = 3, receive_state, #{receiveState})
        </if>
        <if test="orderFee != null">
            ,order_fee=#{orderFee}
        </if>
        <if test="receiveBuyerId != null">
            ,receive_buyer_id=#{receiveBuyerId}
        </if>
        <if test="receiveRemark != null">
            ,receive_remark=#{receiveRemark}
        </if>
        <if test="receiveTime != null">
            ,receive_time=#{receiveTime}
        </if>
        <if test="payState != null">
            ,pay_state=#{payState}
        </if>
        <if test="payAccount != null">
            ,pay_account=#{payAccount}
        </if>
        <if test="payTime != null">
            ,pay_time=#{payTime}
        </if>
        <if test="payEtypeId != null">
            ,pay_etype_id=#{payEtypeId}
        </if>
        <if test="payNumber != null">
            ,pay_number=#{payNumber}
        </if>
        <if test="receiveAccount != null">
            ,receive_account=#{receiveAccount}
        </if>
        <if test="refundApplyTotal != null">
            ,refund_apply_total=#{refundApplyTotal}
        </if>
        <if test="refundApplyTaxedTotal != null">
            ,refund_apply_taxed_total=#{refundApplyTaxedTotal}
        </if>
        <if test="refundApplyTaxTotal != null">
            ,refund_apply_tax_total=#{refundApplyTaxTotal}
        </if>
        <if test="refundApplyFreightFee != null">
            ,refund_apply_freight_fee=#{refundApplyFreightFee}
        </if>
        <if test="refundApplyMallFee != null">
            ,refund_apply_mall_fee=#{refundApplyMallFee}
        </if>
        <if test="refundApplyServiceFee != null">
            ,refund_apply_service_fee=#{refundApplyServiceFee}
        </if>
        <!--        <if test="ktypeId != null">-->
        <!--            ,ktype_id=#{ktypeId}-->
        <!--        </if>-->
        <if test="refundState != null">
            ,refund_state=#{refundState}
        </if>
        <if test="refundStatement != null">
            ,refund_statement=#{refundStatement}
        </if>
        <if test="tradeStatus != null">
            ,trade_status=#{tradeStatus}
        </if>
        <if test="billVchcode != null and billVchcode > 0">
            ,bill_vchcode=#{billVchcode}
        </if>
        <if test="billPoseted != null and billPoseted.getCode() > 0">
            ,bill_poseted=#{billPoseted}
        </if>
        <if test="billTotal != null and billTotal > 0">
            ,bill_total=#{billTotal}
        </if>
        <if test="billServiceFee != null and billServiceFee > 0">
            ,bill_service_fee=#{billServiceFee}
        </if>
        <if test="billServiceFee != null and billServiceFee > 0">
            ,bill_service_fee=#{billServiceFee}
        </if>
        <if test="orderEtypeId != null and orderEtypeId > 0">
            ,order_etype_id=#{orderEtypeId}
        </if>
        <if test="refundCreateTime != null">
            ,refund_create_time=#{refundCreateTime}
        </if>
        <if test="refundModifyTime != null">
            ,refund_modify_time=#{refundModifyTime}
        </if>
        <if test="tradeCreateTime != null">
            ,trade_create_time=#{tradeCreateTime}
        </if>
        <if test="tradeFinishTime != null">
            ,trade_finish_time=#{tradeFinishTime}
        </if>
        <if test="tradePayTime != null">
            ,trade_pay_time=#{tradePayTime}
        </if>
        <if test="noDetail != null">
            , no_detail=#{noDetail}
        </if>
        <if test="hasEdit == true">
            ,has_edit=1
        </if>
        <if test="refundProcessState != null">
            ,refund_process_state=#{refundProcessState}
        </if>
        <if test="refundProcessTime != null">
            ,refund_process_time=#{refundProcessTime}
        </if>
        <if test="mappingState != null">
            ,mapping_state=#{mappingState}
        </if>
        <if test="etypeId != null">
            ,etype_id=#{etypeId}
        </if>
        <if test="memo != null">
            ,memo=#{memo}
        </if>
        <if test="deleted != null">
            ,deleted=#{deleted}
        </if>
        <if test="platformParentOrderId != null">
            ,platform_parent_order_id=#{platformParentOrderId}
        </if>
        <if test="businessType != null">
            ,business_type=#{businessType}
        </if>
        <if test="btypeId != null">
            ,btype_id = #{btypeId}
        </if>
        <if test="platformReturnState != null">
            ,platform_return_state = #{platformReturnState}
        </if>
        <if test="platformRefundState != null">
            ,platform_refund_state = #{platformRefundState}
        </if>
        <if test="platformChangeState != null">
            ,platform_change_state = #{platformChangeState}
        </if>
        <if test="platformConfirmState != null">
            ,platform_confirm_state = #{platformConfirmState}
        </if>
        <if test="refundDutyIds != null and refundDutyIds != ''">
            ,refund_duty_ids = #{refundDutyIds}
        </if>
        <if test="refundFinishTime != null">
            ,refund_finish_time = #{refundFinishTime}
        </if>
        <if test="ktypeId != null and saveFromPage == true">
            ,ktype_id = #{ktypeId}
        </if>
        WHERE
        id = #{id} and profile_id= #{profileId};
    </update>
    <update id="updateRefundWithPostInfo">
        UPDATE pl_eshop_refund
        SET update_time=NOW()
        <if test="billVchcode != null and billVchcode > 0">
            ,bill_vchcode=#{billVchcode}
        </if>
        <if test="billPoseted != null and billPoseted.getCode() > 0">
            ,bill_poseted=#{billPoseted}
        </if>
        ,bill_total = #{billTotal}
        ,bill_service_fee = #{billServiceFee}
        WHERE
        id = #{id} and profile_id= #{profileId};
    </update>
    <update id="deleteRefunds">
        update pl_eshop_refund set deleted=1 where profile_id=#{profileId}
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>
    <update id="updateReceiveStateForDeleteRefunds">
        update pl_eshop_refund set receive_state=(CASE receive_state WHEN 2 THEN 1 ELSE receive_state
        END),receive_time=null where
        profile_id=#{profileId}
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ;
        update pl_eshop_refund_extend set receive_etype_id=0 where
        profile_id=#{profileId}
        <if test="ids != null and ids.size() > 0">
            and pl_eshop_refund_extend.refund_order_id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>
    <update id="refundConfirmCheckIn">
        update pl_eshop_refund set
        receive_state=#{receiveState},
        <if test="receiveBuyerId != null and receiveBuyerId > 0">
            receive_buyer_id=#{receiveBuyerId},
        </if>
        <if test="receiveRemark != null and receiveRemark != ''">
            receive_remark=#{receiveRemark},
        </if>
        <if test="receiveTime != null">
            receive_time=#{receiveTime},
        </if>
        update_time=NOW()
        where profile_id=#{profileId} and id=#{id}
    </update>

    <update id="updateRefundDetails">
        <foreach collection="details" index="index" item="item" open=" " separator=";" close=" ">
            UPDATE pl_eshop_refund_apply_detail
            SET
            platform_num_id=#{item.platformNumId},
            platform_sku_id=#{item.platformSkuId},
            platform_full_name=#{item.platformFullName},
            platform_properties_name=#{item.platformPropertiesName},
            platform_xcode=#{item.platformXcode},
            ptype_id=#{item.ptypeId},
            sku_id=#{item.skuId},
            unit=#{item.unit},
            qty=#{item.qty},
            sub_qty=#{item.subQty},
            price=#{item.price},
            unit_price=#{item.unitPrice},
            sub_price=#{item.subPrice},
            trade_total=#{item.tradeTotal},
            total=#{item.total},
            taxed_total=#{item.taxedTotal},
            tax_total=#{item.taxTotal},
            freight_fee=#{item.freightFee},
            mall_fee=#{item.mallFee},
            service_fee=#{item.serviceFee},
            apply_refund_qty=#{item.applyRefundQty},
            apply_refund_unit_qty=#{item.applyRefundUnitQty},
            apply_refund_sub_qty=#{item.applyRefundSubQty},
            apply_refund_total=#{item.applyRefundTotal},
            apply_refund_taxed_total=#{item.applyRefundTaxedTotal},
            apply_refund_tax_total=#{item.applyRefundTaxTotal},
            apply_refund_freight_fee=#{item.applyRefundFreightFee},
            apply_refund_mall_fee=#{item.applyRefundMallFee},
            apply_refund_service_fee=#{item.applyRefundServiceFee},
            closed=#{item.closed},
            combo_row_id=#{item.comboRowId},
            mapping_state=#{item.mappingState},
            memo = #{item.memo}
            WHERE
            id=#{item.detailId} and refund_order_id=#{item.refundOrderId}
            and profile_id=#{item.profileId}
            and trade_order_id=#{item.tradeOrderId}
            and trade_order_detail_id=#{item.tradeOrderDetailId}
        </foreach>
    </update>
    <update id="updateRefundSendDetail">
        <foreach collection="details" index="index" item="item" open=" " separator=";" close=" ">
            UPDATE pl_eshop_refund_Send_detail
            SET
            platform_num_id=#{item.platformNumId},
            platform_sku_id=#{item.platformSkuId},
            platform_full_name=#{item.platformFullName},
            platform_properties_name=#{item.platformPropertiesName},
            platform_xcode=#{item.platformXcode},
            ptype_id=#{item.ptypeId},
            unit = #{item.unit},
            sku_id=#{item.skuId},
            qty=#{item.qty},
            sub_qty=#{item.subQty},
            price=#{item.price},
            total=#{item.total},
            taxed_total=#{item.taxedTotal},
            combo_row_id=#{item.comboRowId},
            mapping_state=#{item.mappingState}
            WHERE
            id=#{item.id} and refund_order_id=#{item.refundOrderId}
            and profile_id=#{item.profileId}
        </foreach>
    </update>
    <update id="updateRefundComboDetails">
        <foreach collection="combos" index="index" item="item" open=" " separator=";" close="">
            UPDATE pl_eshop_refund_apply_detail_combo
            SET
            trade_order_detail_id=#{item.tradeOrderDetailId},
            combo_id=#{item.comboId},
            qty=#{item.qty},
            price=#{item.price},
            trade_total=#{item.tradeTotal},
            total=#{item.total},
            taxed_total=#{item.taxedTotal},
            tax_total=#{item.taxTotal},
            freight_fee=#{item.freightFee},
            mall_fee=#{item.mallFee},
            service_fee=#{item.serviceFee},
            apply_refund_qty=#{item.applyRefundQty},
            apply_refund_total=#{item.applyRefundTotal},
            apply_refund_taxed_total=#{item.applyRefundTaxedTotal},
            apply_refund_tax_total=#{item.applyRefundTaxTotal},
            apply_refund_freight_fee=#{item.applyRefundFreightFee},
            apply_refund_mall_fee=#{item.applyRefundMallFee},
            apply_refund_service_fee=#{item.applyRefundServiceFee},
            closed=#{item.closed},
            update_time=#{item.updateTime}
            WHERE
            id=#{item.id} and refund_order_id=#{item.refundOrderId} and id=#{item.id}
            and profile_id=#{item.profileId} and trade_order_id=#{item.tradeOrderId}
        </foreach>
    </update>
    <update id="cancelRefundReceived">
        update pl_eshop_refund_receive_checkin set trade_refund_order_number='',receive_trigger_bill_id = '',
        refund_order_id=0,deleted=IF(create_type=2,1,0)
        where profile_id=#{profileId} and
        refund_order_id in
        <foreach collection="refundIds" close=")" open="(" separator="," item="refundOrderId">
            #{refundOrderId}
        </foreach>;
    </update>

    <update id="clearCheckinInfo">
        update pl_eshop_refund_receive_checkin set trade_refund_order_number='',receive_trigger_bill_id = '',
        refund_order_id=0,deleted=IF(create_type=2,1,0)
        where profile_id=#{profileId} and
        vchcode in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">
            #{vchcode}
        </foreach>;
        update pl_eshop_refund_receive_checkin_detail set
        apply_refund_unit_qty=0
        where profile_id=#{profileId} and
        vchcode in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">
            #{vchcode}
        </foreach>;
        update pl_eshop_refund_receive_checkin_detail_combo set
        apply_refund_unit_qty=0
        where profile_id=#{profileId} and
        vchcode in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">
            #{vchcode}
        </foreach>;
    </update>

    <delete id="deleteCheckinDetails">
        <if test="vchcodes != null and vchcodes.size() > 0">
            <foreach collection="vchcodes" index="0" item="vchcode" close=" " open=" " separator=" ">
                DELETE FROM pl_eshop_refund_receive_checkin_detail_serialno WHERE profile_id=#{profileId} and
                vchcode=#{vchcode};
                DELETE FROM pl_eshop_refund_receive_checkin_detail_combo WHERE profile_id=#{profileId} and
                vchcode=#{vchcode};
                DELETE FROM pl_eshop_refund_receive_checkin_detail WHERE profile_id=#{profileId} and vchcode=#{vchcode};
                DELETE FROM pl_eshop_refund_receive_checkin WHERE profile_id=#{profileId} and vchcode=#{vchcode};
            </foreach>
        </if>
    </delete>

    <delete id="deleteRefundInfo">
        delete
        from pl_eshop_refund
        where profile_id = #{profileId}
          and id = #{id}
    </delete>
    <delete id="deleteRefundDetails">
        DELETE
        FROM pl_eshop_refund_apply_detail
        WHERE profile_id = #{profileId}
          and refund_order_id = #{refundOrderId}
    </delete>
    <delete id="deleteRefundComboDetails">
        DELETE
        FROM pl_eshop_refund_apply_detail_combo
        WHERE profile_id = #{profileId}
          and refund_order_id = #{refundOrderId}
    </delete>
    <delete id="deleteRefundSendDetails">
        DELETE
        FROM pl_eshop_refund_send_detail
        WHERE profile_id = #{profileId}
          and refund_order_id = #{refundOrderId}
    </delete>
    <delete id="deleteRefundSendCombos">
        DELETE
        FROM pl_eshop_refund_send_detail_combo
        WHERE profile_id = #{profileId}
          and refund_order_id = #{refundOrderId}
    </delete>
    <delete id="deleteRefundSerials">
        DELETE
        FROM pl_eshop_refund_apply_detail_serialno
        WHERE profile_id = #{profileId}
          and refund_order_id = #{refundOrderId};
        DELETE
        FROM pl_eshop_refund_send_detail_serialno
        WHERE profile_id = #{profileId}
          and refund_order_id = #{refundOrderId};
    </delete>

    <select id="queryRefundDetailSerialno"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsSerialEntity">
        select s.refund_order_id        as vchcode,
               s.snno,
               s.profile_id,
               s.refund_order_detail_id as detail_id,
               s.sn1,
               s.sn2,
               s.sn3,
               s.sn_memo
        from pl_eshop_refund_apply_detail_serialno s
        where s.profile_id = #{profileId}
          and s.refund_order_id = #{vchcode}
          and refund_order_detail_id = #{detailId}
    </select>

    <select id="queryRefundDetailSerialnos"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsSerialEntity">
        select s.refund_order_id as vchcode, s.snno, s.profile_id, s.refund_order_detail_id as detail_id, s.sn1,
        s.sn2,s.sn3,
        s.sn_memo
        from pl_eshop_refund_apply_detail_serialno s
        where s.profile_id = #{profileId}
        and s.refund_order_id IN
        <foreach collection="refundOrderIds" item="refundOrderId" open="(" close=")" separator=",">
            #{refundOrderId}
        </foreach>
    </select>

    <select id="queryRefundcheckinDetailSerialno"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsSerialEntity">
        select s.vchcode,
               s.snno,
               s.profile_id,
               s.detail_id,
               s.sn1,
               s.sn2,
               s.sn3,
               s.sn_memo
        from pl_eshop_refund_receive_checkin_detail_serialno s
        where s.profile_id = #{profileId}
          and s.vchcode = #{vchcode}
          and detail_id = #{detailId}
    </select>

    <insert id="insertRefundFreights">
        INSERT INTO pl_eshop_refund_freight
        (id,
        profile_id,
        refund_order_id,
        freight_template_id,
        freight_no,
        freight_btype_id,
        freight_name,
        freight_code,
        freight_type,
        freight_intercept_status
        )
        VALUES
        <foreach collection="freights" index="index" item="item" open="" separator="," close="">
            (#{item.id},
            #{item.profileId},
            #{item.refundOrderId},
            #{item.freightTemplateId},
            #{item.freightNo},
            #{item.freightBtypeId},
            #{item.freightName},
            #{item.freightCode},
            #{item.freightType},
            #{item.freightInterceptStatus}
            )
        </foreach>
    </insert>
    <delete id="deleteRefundFreights">
        DELETE
        FROM pl_eshop_refund_freight
        WHERE profile_id = #{profileId}
          and refund_order_id = #{refundOrderId}
    </delete>

    <insert id="insertRefundConfig">
        INSERT INTO pl_eshop_refund_config
        (id,
         profile_id,
         enable_goods_checkin,
         default_ktype_id,
         atype_id,
         ktype_type)
        VALUES (#{id},
                #{profileId},
                #{enableGoodsCheckin},
                #{defaultKtypeId},
                #{atypeId},
                #{ktypeType});
    </insert>
    <insert id="insertRefundConfigReason">
        INSERT INTO pl_eshop_refund_config_reason
        (id,
         profile_id,
         reason_type,
         refund_reason,
         deleted)
        VALUES (#{reason.id},
                #{reason.profileId},
                #{reason.reasonType},
                #{reason.refundReason},
                0)
    </insert>
    <insert id="insertRefundConfigReasonBySingle">
        INSERT INTO pl_eshop_refund_config_reason
        (id,
         profile_id,
         reason_type,
         refund_reason,
         deleted)
        VALUES (#{id},
                #{profileId},
                #{reasonType},
                #{refundReason},
                0)
    </insert>
    <select id="checkReasonExist" resultType="java.math.BigInteger">
        select id
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and refund_reason = #{reason}
        order by reason_type
        limit 1
    </select>
    <delete id="deleteRefundConfig">
        delete
        from pl_eshop_refund_config
        where profile_id = #{profileId}
    </delete>

    <insert id="replaceIntoRefundConfig">
        replace
            INTO pl_eshop_refund_config
        (profile_id,
         enable_goods_checkin,
         default_ktype_id,
         atype_id,
         ktype_type,
         auto_audit_download,
         auto_audit_add,
         use_current_cost,
         auto_audit_after_receive_checkin,
         auto_stock_in_after_audited,
         auto_pay_after_audited,
         auto_resend_after_audited,
         auto_update)
        VALUES (#{profileId},
                #{enableGoodsCheckin},
                #{defaultKtypeId},
                #{atypeId},
                #{ktypeType},
                #{autoAuditDownload},
                #{autoAuditAdd},
                #{useCurrentCost},
                #{autoAuditAfterReceiveCheckin},
                #{autoStockInAfterAudited},
                #{autoPayAfterAudited},
                #{autoResendAfterAudited},
                #{autoUpdate});
    </insert>

    <delete id="deleteRefundReason">
        delete
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and reason_type != 1
    </delete>

    <delete id="deleteSingleRefundReason">
        delete
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and id = #{id}
    </delete>

    <update id="updateRefundConfig">
        UPDATE pl_eshop_refund_config
        SET enable_goods_checkin = #{enableGoodsCheckin},
            default_ktype_id     = #{defaultKtypeId},
            atype_id             = #{atypeId},
            ktype_type           = #{ktypeType},
            auto_update          = #{autoUpdate}
        WHERE id = #{id}
          and profile_id = #{profileId}
    </update>
    <update id="updateRefundConfigReason">
        UPDATE pl_eshop_refund_config_reason
        SET refund_reason = #{refundReason},
            stoped        = #{stoped}
        WHERE id = #{id}
          and profile_id = #{profileId};
    </update>
    <update id="updateRefundConfigReasons"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        UPDATE pl_eshop_refund_config_reason
        SET refund_reason = #{reason.refundReason}
        WHERE id = #{reason.id}
          and profile_id = #{reason.profileId}
    </update>

    <insert id="insertRefundPayDetails">
        INSERT INTO pl_eshop_refund_pay_detail
        (id,
        profile_id,
        refund_order_id,
        refund_order_apply_detail_id ,
        refund_total,
        refund_taxed_total,
        refund_tax_total,
        refund_freight_fee,
        refund_mall_fee,
        refund_service_fee
        )
        VALUES

        <foreach collection="payDetails" index="index" item="item" open=" " separator="," close=" ">
            (#{item.id},
            #{item.profileId},
            #{item.refundOrderId},
            #{item.refundOrderApplyDetailId},
            #{item.refundTotal},
            #{item.refundTaxedTotal},
            #{item.refundTaxTotal},
            #{item.refundFreightFee},
            #{item.refundMallFee},
            #{item.refundServiceFee})
        </foreach>
    </insert>
    <insert id="insertRefundPayDetail">
        INSERT INTO pl_eshop_refund_pay_detail
        (id,
         profile_id,
         refund_order_id,
         refund_order_apply_detail_id,
         refund_total,
         refund_taxed_total,
         refund_tax_total,
         refund_freight_fee,
         refund_mall_fee,
         refund_service_fee)
        VALUES (#{id},
                #{profileId},
                #{refundOrderId},
                #{refundOrderApplyDetailId},
                #{refundTotal},
                #{refundTaxedTotal},
                #{refundTaxTotal},
                #{refundFreightFee},
                #{refundMallFee},
                #{refundServiceFee});
    </insert>
    <insert id="insertRefundDetailToCostTable">
        insert into pl_eshop_refund_detail_original_cost(id, refund_order_detail_id, refund_order_id, sub_qty,
        batch_price, cost_period, profile_id)
        values
        <foreach collection="details" item="detail" separator=",">
            (
            #{detail.uId},#{detail.id},#{detail.refundOrderId},#{detail.qty},#{detail.batchPrice},#{detail.costPeriod},#{profileId}
            )
        </foreach>
    </insert>
    <insert id="inUpRefundTiming" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundTiming">
        insert into pl_eshop_refund_timing(id, profile_id, eshop_id, sys_promised_confirm_time, promised_confirm_time,
                                           promised_agree_time, promised_deliver_time, promised_receive_time,
                                           create_time, update_time, refund_id)
        values (#{id}, #{profileId}, #{eshopId}, #{sysPromisedConfirmTime}, #{promisedConfirmTime},
                #{promisedAgreeTime}, #{promisedDeliverTime}, #{promisedReceiveTime},
                #{createTime}, #{updateTime}, #{refundId})
        ON DUPLICATE KEY
            UPDATE sys_promised_confirm_time = #{sysPromisedConfirmTime},
                   promised_confirm_time     = #{promisedConfirmTime},
                   promised_agree_time       = #{promisedAgreeTime},
                   promised_deliver_time     = #{promisedDeliverTime},
                   promised_receive_time     = #{promisedReceiveTime}
    </insert>
    <insert id="insertRelation" parameterType="com.wsgjp.ct.bill.core.handle.entity.dao.BillRelation">
        INSERT INTO `td_bill_relation` (id,
                                        profile_id,
                                        source_vchcode,
                                        target_vchcode,
                                        target_vchtype,
                                        source_vchtype,
                                        target_business_type,
                                        source_business_type)
        VALUES (#{id},
                #{profileId},
                #{sourceVchcode},
                #{targetVchcode},
                #{targetVchtype},
                #{sourceVchtype},
                #{targetBusinessType},
                #{sourceBusinessType})
    </insert>

    <insert id="insertRelationBatch" parameterType="com.wsgjp.ct.bill.core.handle.entity.dao.BillRelation">
        INSERT INTO `td_bill_relation` (
        id,
        profile_id,
        source_vchcode,
        target_vchcode,
        target_vchtype,
        source_vchtype,
        target_business_type,
        source_business_type)
        VALUES
        <foreach collection="relations" item="relation" separator=",">
            (#{relation.id},
            #{relation.profileId},
            #{relation.sourceVchcode},
            #{relation.targetVchcode},
            #{relation.targetVchtype},
            #{relation.sourceVchtype},
            #{relation.targetBusinessType},
            #{relation.sourceBusinessType})
        </foreach>
    </insert>
    <update id="updateRefundPayDetail">
        UPDATE pl_eshop_refund_pay_detail
        SET refund_order_id=#{refundOrderId},
            refund_order_apply_detail_id =#{refundOrderApplyDetailId},
            refund_total=#{refundTotal},
            refund_taxed_total=#{refundTaxedTotal},
            refund_tax_total=#{refundTaxTotal},
            refund_freight_fee=#{refundFreightFee},
            refund_mall_fee=#{refundMallFee},
            refund_service_fee=#{refundServiceFee}

        WHERE id = #{id}
          AND profile_id = #{profileId}
    </update>


    <select id="getGoodsBatch" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsBatchEntity">
        select *
        from acc_goodsstock_batchlife
        where profile_id = #{profileId}
          and ktype_id = #{ktypeId}
          and batchno = #{batchno}
        limit 1;
    </select>

    <select id="getPostedDeliverBillVchcodes" resultType="java.math.BigInteger">
        SELECT DISTINCT vchcode FROM td_bill_deliver_state WHERE profile_id=#{profileId} and process_state>=35
        <if test="vchcodes != null and vchcodes.size > 0">
            and vchcode in
            <foreach collection="vchcodes" index="index" open="(" separator="," close=")" item="vchcode">
                #{vchcode}
            </foreach>
        </if>
        UNION
        SELECT DISTINCT vchcode FROM acc_bill_deliver_state WHERE profile_id=#{profileId} and process_state>=35
        <if test="vchcodes != null and vchcodes.size > 0">
            and vchcode in
            <foreach collection="vchcodes" index="index" open="(" separator="," close=")" item="vchcode">
                #{vchcode}
            </foreach>
        </if>
    </select>

    <select id="getPostedDeliverBillVchcodesByTradeId" resultType="java.math.BigInteger">
        select distinct core.vchcode
        from td_bill_core core
                 join td_bill_detail_deliver bdd on core.profile_id = bdd.profile_id and core.vchcode = bdd.vchcode
        where core.profile_id = #{profileId}
          and core.post_state >= 600
          and bdd.trade_order_id = #{tradeOrderId}
        union
        select distinct core.vchcode
        from acc_bill_core core
                 join acc_bill_detail_deliver bdd on core.profile_id = bdd.profile_id and core.vchcode = bdd.vchcode
        where core.profile_id = #{profileId}
          and core.post_state >= 600
          and bdd.trade_order_id = #{tradeOrderId}
    </select>


    <select id="getNeedReleaseQtyRefundList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select * from pl_eshop_refund WHERE refund_type in (0,1,2) and deleted=1
        and refund_state!=5
        <if test="refundIds != null and refundIds.size() > 0">
            and id in (
            <foreach collection="refundIds" item="id" open="" close="" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </select>

    <select id="getRefundByBillVchcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select *
        from pl_eshop_refund
        WHERE profile_id = #{profileId}
          and deleted = 0
          and bill_vchcode = #{billVchcode}
    </select>

    <select id="checkRefundReasonUsed" resultType="java.lang.Boolean">
        select IFNULL(id, 0) > 0
        from pl_eshop_refund
        where profile_id = #{profileId}
          and refund_reason = #{reason}
        limit 1
    </select>

    <select id="queryUnRelationDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        select d.* ,r.otype_id,ex.eshop_order_id
        from pl_eshop_refund_apply_detail d
        LEFT join pl_eshop_refund r on r.id = d.refund_order_id and r.profile_id = d.profile_id
        LEFT JOIN pl_eshop_refund_apply_detail_extend ex on ex.refund_detail_id = d.id and ex.profile_id = d.profile_id
        where
        d.refund_order_id = r.id
        and d.ptype_id = 0
        and d.sku_id = 0
        and d.profile_id = #{profileId}
        and r.otype_id = #{eshopId}
        <if test="platformNumId != null and platformNumId.size() > 0">
            and d.platform_num_id in
            <foreach collection="platformNumId" close=")" open="(" separator="," item="numId">
                #{numId}
            </foreach>
        </if>
        <if test="platformSkuId != null and platformSkuId.size() > 0">
            and d.platform_sku_id in
            <foreach collection="platformSkuId" close=")" open="(" separator="," item="skuId">
                #{skuId}
            </foreach>
        </if>
        <if test="platformProperties != null and platformProperties.size() > 0">
            and d.platform_properties_name in
            <foreach collection="platformProperties" close=")" open="(" separator="," item="prop">
                #{prop}
            </foreach>
        </if>
        and d.create_time &gt; #{startDate} and d.create_time &lt;= #{endDate}
        order by d.create_time desc
        limit #{pageSize}
    </select>

    <update id="updateRefundDetailFreightFee">
        UPDATE pl_eshop_refund_apply_detail
        SET apply_refund_freight_fee=#{applyRefundFreightFee}
        WHERE id = #{detailId}
          and refund_order_id = #{refundOrderId}
          and profile_id = #{profileId}
    </update>
    <update id="updateRefundComboFreightFee">
        UPDATE pl_eshop_refund_apply_detail_combo
        SET apply_refund_freight_fee=#{applyRefundFreightFee}
        WHERE refund_order_id = #{refundOrderId}
          and id = #{detailId}
          and profile_id = #{profileId}
    </update>
    <update id="updateRefundConfirmState">
        update pl_eshop_refund set
        confirm_state=0,bill_poseted=0,bill_vchcode=0,confirm_time=null,confirm_remark='',confirm_etype_id = 0
        where
        profile_id=#{profileId}
        and id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="updateRefundDeleted">
        update pl_eshop_refund set deleted=0
        where
        profile_id=#{profileId}
        and id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="checkOrderExistRefund" resultType="boolean">
        select count(0) > 0
        from pl_eshop_refund
        where profile_id = #{profileId}
          and trade_order_id = #{tradeOrderId}
          and otype_id = #{otypeId}
          and no_detail = 0
          and deleted = 0
          and refund_state != 6
          and no_detail = 0
    </select>

    <select id="queryEshopSaleOrderDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select *
        from pl_eshop_sale_order_detail
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </select>
    <select id="queryTempEshopSaleOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select trade_order_id, otype_id, business_type
        from pl_eshop_sale_order
        where profile_id = #{profileId}
          and otype_id = #{eshopId}
          and trade_order_id = #{tradeOrderId}
    </select>
    <select id="getSaleorderDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select * from pl_eshop_sale_order_detail where
        profile_id=#{profileId} and
        eshop_order_id=#{eshopOrderId}
        <if test="eshopOrderDetailIds.size() > 0">
            and id in
            <foreach collection="eshopOrderDetailIds" item="detailId" index="index" open="(" close=")" separator=",">
                #{detailId}
            </foreach>
        </if>
    </select>
    <select id="getRefundByRefundIdAndProfileIdAndEshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="refundInfo"/>
        FROM pl_eshop_refund `refund`
        where `refund`.profile_id=#{profileId} and `refund`.trade_refund_order_id=#{tradeRefundOrderId} and
        `refund`.otype_id=#{otypeId} limit
        1
    </select>
    <select id="getRefundByRefundIdAndProfileId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="refundInfo"/>
        FROM pl_eshop_refund `refund`
        where `refund`.profile_id=#{profileId} and `refund`.id=#{refundOrderId}
    </select>
    <select id="getRefundByRefundIdListAndProfileId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="refundInfo"/>
        FROM pl_eshop_refund `refund`
        where `refund`.profile_id=#{profileId} and `refund`.id in
        <foreach collection="refundOrderIdList" item="refundOrderId" index="index" open="(" close=")" separator=",">
            #{refundOrderId}
        </foreach>

    </select>
    <select id="queryrefundSaleOrderVchocode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopSaleOrderVchcodeEntity">
        SELECT t1.id saleOrderVchcode,t1.seller_memo as saleorderSellerMemo,t1.remark as saleorderRemark,
        t2.id refundVchcode
        FROM pl_eshop_sale_order t1
        LEFT JOIN pl_eshop_refund t2 ON t1.profile_id = t2.profile_id
        AND t1.otype_id = t2.otype_id
        AND t1.trade_order_id = t2.trade_order_id
        WHERE t2.id in
        <foreach collection="refundVchcodeList" item="refundOrderId" index="index" open="(" close=")" separator=",">
            #{refundOrderId}
        </foreach>
    </select>
    <select id="queryOrderPorcessStatesByorderIdList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.OrderProcessStatesEntity">
        SELECT t2.trade_order_id tradeOrderId,t2.seller_memo as billSellerMemo,tbc.memo as sysMemo,
        MAX(t1.process_state) processState
        FROM `td_bill_deliver_state` t1
        LEFT JOIN td_bill_deliver t2 ON t1.vchcode = t2.vchcode and t1.profile_id = t2.profile_id
        left join td_bill_core tbc on tbc.vchcode=t2.vchcode and tbc.profile_id=t2.profile_id
        WHERE t2.profile_id=#{profileId} and
        t2.trade_order_id in
        <foreach collection="tradeIdList" item="tradeOrderId" index="index" open="(" close=")" separator=",">
            #{tradeOrderId}
        </foreach>
        GROUP BY t2.trade_order_id
        union
        SELECT t2.trade_order_id tradeOrderId,t2.seller_memo as billSellerMemo,abc.memo as sysMemo,
        MAX(t1.process_state) processState
        FROM `td_bill_deliver_state` t1
        LEFT JOIN td_bill_deliver t2 ON t1.vchcode = t2.vchcode and t1.profile_id = t2.profile_id
        left join acc_bill_core abc on abc.vchcode=t2.vchcode and abc.profile_id=t2.profile_id
        WHERE t2.profile_id=#{profileId} and
        t2.trade_order_id in
        <foreach collection="tradeIdList" item="tradeOrderId" index="index" open="(" close=")" separator=",">
            #{tradeOrderId}
        </foreach>
        GROUP BY t2.trade_order_id
    </select>
    <select id="queryCheckInVchcodeByCheckInDetailIdList" resultType="java.math.BigInteger">
        SELECT distinct t1.vchcode
        FROM pl_eshop_refund_receive_checkin_detail t1
        WHERE t1.id in
        <foreach collection="checkInDetaiIdlList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


    <update id="updateRefundDetailCheckDetailId">
        UPDATE pl_eshop_refund_apply_detail
        SET check_detail_id=#{checkInDetailId}
        <if test="batchNo != null and batchNo != ''">
            , batchNo = #{batchNo}
        </if>
        <if test="expireDate != null">
            , expire_date = #{expireDate}
        </if>
        <if test="produceDate != null">
            , produce_date = #{produceDate}
        </if>
        <if test="actualReceiptNumber != null">
            , actual_receipt_number = actual_receipt_number + #{actualReceiptNumber}
        </if>
        WHERE id = #{refundDetailId} and profile_id = #{profileId}
    </update>
    <update id="updateRefundApplyDetailCheckDetailId">
        update pl_eshop_refund_apply_detail set check_detail_id=0
        where profile_id=#{profileId} and
        refund_order_id in
        <foreach collection="refundIds" close=")" open="(" separator="," item="refundOrderId">
            #{refundOrderId}
        </foreach>
    </update>
    <select id="queryApplyDetailIdByComboRowIdList" resultType="java.math.BigInteger">
        select t1.id
        from pl_eshop_refund_apply_detail t1
        where t1.profile_id=#{profileId} and combo_row_id in
        <foreach collection="comboRowIdList" close=")" open="(" separator="," item="comboRowId">
            #{comboRowId}
        </foreach>
    </select>

    <select id="queryCheckInDetailIdByComboRowIdList" resultType="java.math.BigInteger">
        select t1.id
        from pl_eshop_refund_receive_checkin_detail t1
        where t1.profile_id=#{profileId} and combo_row_id in
        <foreach collection="comboRowIdList" close=")" open="(" separator="," item="comboRowId">
            #{comboRowId}
        </foreach>
    </select>
    <select id="queryRefundcheckinDetailSerialnoById"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsSerialEntity">
        select s.vchcode,
               s.snno,
               s.profile_id,
               s.detail_id,
               s.sn1,
               s.sn2,
               s.sn3,
               s.sn_memo
        from pl_eshop_refund_receive_checkin_detail_serialno s
        where s.profile_id = #{profileId}
          and detail_id = #{checkInDetailId}
    </select>
    <select id="getSaleOrderMappingState" resultType="java.lang.Boolean">
        SELECT mapping_state
        FROM pl_eshop_sale_order
        WHERE trade_order_id = #{tradeOrderId}
          AND profile_id = #{profileId}
          and otype_id = #{otypeId}
        limit 1
    </select>
    <select id="getReceiveCheckInDetailList" resultType="java.lang.String">
        SELECT perrc.checkin_number
        FROM pl_eshop_refund_receive_checkin AS perrc
                 LEFT JOIN pl_eshop_refund_checkin_relation rel on rel.profile_id = perrc.profile_id and rel.checkin_id = perrc.vchcode
        WHERE rel.refund_order_id = #{refundOrderId}
          AND perrc.profile_id = #{profileId}
          AND perrc.deleted = 0
    </select>
    <select id="queryCheckInDetaiIdlListByComboIdList" resultType="java.math.BigInteger">
        SELECT id FROM `pl_eshop_refund_receive_checkin_detail` WHERE combo_row_id in
        <foreach collection="comboRowIdList" close=")" open="(" separator="," item="comboRowId">
            #{comboRowId}
        </foreach>
    </select>
    <select id="partialRefreshRefundList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="refundListColumn"/>
        FROM pl_eshop_refund refund
        LEFT JOIN pl_eshop_refund_timing timing on refund.profile_id = timing.profile_id and refund.id =
        timing.refund_id and refund.otype_id = timing.eshop_id
        LEFT JOIN pl_eshop_sale_order `order` on order.profile_id=refund.profile_id and
        order.id=refund.eshop_order_id
        LEFT JOIN pl_buyer buyer on buyer.profile_id=order.profile_id and buyer.buyer_id = order.buyer_id
        LEFT JOIN pl_buyer receiveBuyer on receiveBuyer.buyer_id=refund.receive_buyer_id and
        receiveBuyer.profile_id=refund.profile_id
        LEFT JOIN pl_eshop eshop on eshop.profile_id=refund.profile_id and eshop.otype_id=refund.otype_id
        LEFT JOIN pl_eshop_refund_config_reason reason on reason.profile_id=refund.profile_id and
        refund.refund_reason=reason.id
        LEFT JOIN base_etype e on e.profile_id=refund.profile_id and e.id=refund.etype_id
        LEFT JOIN base_etype e2 on e2.profile_id = refund.profile_id and e2.id = refund.confirm_etype_id
        LEFT JOIN `base_otype` o ON o.profile_id=refund.profile_id and o.id=eshop.otype_id
        LEFT JOIN base_atype ba on ba.profile_id=refund.profile_id and ba.id=o.atype_id
        LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id=refund.profile_id and
        freight.refund_order_id=refund.id and freight.freight_type != 1
        LEFT JOIN base_ktype ktype1 on refund.profile_id = ktype1.profile_id and refund.ktype_id = ktype1.id
        LEFT JOIN base_ktype ktype2 on refund.profile_id = ktype2.profile_id and refund.old_ktype_id = ktype2.id
        LEFT JOIN pl_eshop_refund_extend refund_extend on refund.profile_id = refund_extend.profile_id and refund.id =
        refund_extend.refund_order_id
        LEFT JOIN pl_eshop_refund_config_reason reasonPlatform on reasonPlatform.profile_id = refund.profile_id and
        refund_extend.platform_refund_reason = reasonPlatform.id
        LEFT JOIN pl_eshop_refund_distribution_buyer perdb on perdb.profile_id = refund.profile_id and
        perdb.refund_order_id = refund.id
        left join base_btype btype on btype.profile_id = refund.profile_id and btype.id = refund.btype_id
        left join base_btype btype_supplier on btype_supplier.profile_id = refund.profile_id and btype_supplier.id = refund.supplier_id
        LEFT JOIN mark_data big_data on refund_extend.refund_order_detail_summary = big_data.id and
        refund_extend.profile_id = big_data.profile_id
        WHERE refund.profile_id=#{profileId} AND o.ocategory!=2
        <if test="ids != null and ids.size > 0">
            AND refund.id IN
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <update id="editRefundType">
        UPDATE pl_eshop_refund
        SET refund_type=1,
            refund_process_state= -1,
            receive_state=1,
            has_edit=1
        where profile_id = #{profileId}
          and id = #{id}
    </update>


    <select id="queryMergeApplyDetailInfoByRefundIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopMergeRefundApplyDetailInfo">
        SELECT id, combo_row_id,create_type
        from pl_eshop_refund_apply_detail
        where profile_id = #{profileId} and
        refund_order_id IN
        <foreach collection="refundIds" close=")" open="(" separator="," item="refundOrderId">
            #{refundOrderId}
        </foreach>
    </select>


    <delete id="cancelRefundApplyDetailByCheckDetail">
        delete
        from pl_eshop_refund_apply_detail
        where profile_id=#{profileId} and create_type=2 and
        id in
        <foreach collection="idList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </delete>
    <delete id="cancelRefundApplyDetailComboByCheckDetail">
        delete
        from pl_eshop_refund_apply_detail_combo
        where profile_id=#{profileId} and
        id in
        <foreach collection="comboRowIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAndInsertRefundDetailCost">
        delete
        from pl_eshop_refund_detail_original_cost
        where profile_id = #{profileId}
          and refund_order_detail_id = #{detail.detailId};
        INSERT INTO pl_eshop_refund_detail_original_cost
        (id,
         refund_order_detail_id,
         refund_order_id,
         sub_qty,
         batch_price,
         cost_period,
         profile_id)
        VALUES (#{detail.id},
                #{detail.detailId},
                #{detail.refundOrderId},
                #{detail.subQty},
                #{detail.batchPrice},
                #{detail.costPeriod},
                #{profileId});
    </delete>
    <delete id="deleteRefundCostTableRecordByRefundVchCode">
        delete
        from pl_eshop_refund_detail_original_cost
        where profile_id = #{profileId}
        and refund_order_id in
        <foreach collection="RefundVchCodes" close=")" open="(" separator="," item="refundOrderId">
            #{refundOrderId}
        </foreach>
    </delete>

    <update id="updateRefundApplyCheckComboRowId">
        UPDATE pl_eshop_refund_apply_detail_combo
        SET check_combo_row_id=#{checkInComboId}
        <if test="actualReceiptNumber != null">
            , actual_receipt_number = actual_receipt_number + #{actualReceiptNumber}
        </if>
        where profile_id = #{profileId}
        and id = #{applyComboId}
    </update>
    <update id="updateRefundComboDetailIdByCombowRowId">
        UPDATE pl_eshop_refund_apply_detail_combo
        SET check_combo_row_id=0
        where profile_id = #{profileId} and
        id in
        <foreach collection="normalComboRowIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>
    <update id="updateRefundCheckDetailIdById">
        UPDATE pl_eshop_refund_apply_detail
        SET check_detail_id=0
        where profile_id = #{profileId} and
        id in
        <foreach collection="normalIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>
    <update id="updateRefundState">
        UPDATE pl_eshop_refund
        SET refund_state=#{refundState}
        where profile_id = #{profileId}
          and id = #{id};
        UPDATE pl_eshop_refund_extend
        SET system_refund_state=#{systemRefundState}
        where profile_id = #{profileId}
          and refund_order_id = #{id};
    </update>

    <select id="queryRefundInfoByBillVchcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT refund.id, refund.profile_id, refund.otype_id
        FROM pl_eshop_refund refund
        where refund.profile_id = #{profileId}
        <choose>
            <when test="refundNumber != null and refundNumber != ''">
                and refund.trade_refund_order_number = #{refundNumber}
            </when>
            <otherwise>
                and refund.id in
                <foreach item="item" collection="sourceVchcodes" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        limit 1
    </select>

    <select id="queryRefundListByVchcodeList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select *
        from pl_eshop_refund refund
        WHERE
        profile_id=#{profileId} and
        refund.id IN
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <update id="updateRefundMappingState">
        UPDATE pl_eshop_refund
        SET mapping_state=#{mappingState}
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateRefundDetailState">
        UPDATE pl_eshop_refund_apply_detail
        SET refund_state=#{refundState}
        where profile_id = #{profileId}
          and refund_order_id = #{id}
    </update>
    <update id="updateRefundDetailByDetailId">
        UPDATE pl_eshop_refund_apply_detail
        SET update_time=NOW()
        <if test="detail.costPeriod != null and detail.costPeriod != 0">
            , cost_period = #{detail.costPeriod}
        </if>
        <if test="detail.costState != null">
            , cost_state = #{detail.costState}
        </if>
        <if test="detail.batchPrice != null and detail.batchPrice != 0">
            , batch_price = #{detail.batchPrice}
        </if>
        <if test="detail.batchNo != null and detail.batchNo != ''">
            , batchNo = #{detail.batchNo}
        </if>
        <if test="detail.expireDate != null">
            , expire_date = #{detail.expireDate}
        </if>
        <if test="detail.produceDate != null">
            , produce_date = #{detail.produceDate}
        </if>
        WHERE profile_id = #{profileId} and id = #{detail.detailId}
    </update>
    <update id="updateRefundCostState">
        UPDATE pl_eshop_refund
        SET cost_state=#{costState}
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateRefundDetailCostState">
        <foreach collection="details" item="detail" index="index" open="" close="" separator=";">
            update pl_eshop_refund_apply_detail
            <set>
                cost_state=#{detail.costState}
            </set>
            where profile_id = #{profileId} and id = #{detail.detailId}
        </foreach>
    </update>
    <update id="updateRefundDetailCostInfo">
        <foreach collection="details" item="detail" index="index" open="" close="" separator=";">
            update pl_eshop_refund_apply_detail
            <set>
                batch_price=#{detail.batchPrice},cost_period=#{detail.costPeriod}
            </set>
            where profile_id = #{profileId} and id = #{detail.detailId}
        </foreach>
    </update>
    <update id="updateRefundAndDetailsCostInfoByVchcodes">
        UPDATE pl_eshop_refund
        SET cost_state=0
        where profile_id = #{profileId}
        and id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>;
        UPDATE pl_eshop_refund_apply_detail
        set cost_state=0
        where profile_id = #{profileId}
        and refund_order_id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>;
    </update>
    <update id="updateCheckinDetailByDetailId">
        UPDATE pl_eshop_refund_receive_checkin_detail
        SET update_time=NOW()
        <if test="detail.costPeriod != null and detail.costPeriod != 0">
            , cost_period = #{detail.costPeriod}
        </if>
        <if test="detail.batchPrice != null and detail.batchPrice != 0">
            , batch_price = #{detail.batchPrice}
        </if>
        <if test="detail.batchNo != null and detail.batchNo != ''">
            , batchno = #{detail.batchNo}
        </if>
        <if test="detail.expireDate != null">
            , expire_date = #{detail.expireDate}
        </if>
        <if test="detail.produceDate != null">
            , produce_date = #{detail.produceDate}
        </if>
        WHERE profile_id = #{profileId} and id = #{detail.detailId}
    </update>
    <update id="updateRefundKTypeByVchcode">
        UPDATE pl_eshop_refund
        SET ktype_id=#{ktypeId}
        where profile_id = #{profileId}
        and id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>;
    </update>

    <update id="closeOnlineRefund">
        update pl_eshop_refund
        set refund_state=6
        where profile_id = #{profileId}
        and trade_order_id=#{tradeId}
        and otype_id = #{eshopId}
        and create_type != 0
        and trade_refund_order_number not in
        <foreach collection="refundIds" open="(" close=")" separator="," item="tid">
            #{tid}
        </foreach>;
        update pl_eshop_refund_apply_detail d
        left join pl_eshop_refund r on d.profile_id=r.profile_id and d.refund_order_id=r.id
        set d.refund_state=4
        where d.profile_id = #{profileId}
        and r.otype_id = #{eshopId}
        and r.trade_order_id=#{tradeId}
        and r.create_type != 0
        and r.trade_refund_order_number not in
        <foreach collection="refundIds" open="(" close=")" separator="," item="tid">
            #{tid}
        </foreach>;
    </update>
    <update id="updateReceiveState">
        UPDATE pl_eshop_refund
        SET receive_state=#{refundReceiveStatus},
            receive_time=#{receivetime},
            receive_buyer_id=#{receiveBuyerId}
        where profile_id = #{profileId}
          and id = #{id}
          and refund_type != 0
    </update>
    <update id="updateRefundSubmitToWmsStatus">
        UPDATE pl_eshop_refund
        SET has_submit_to_wms=#{hasSubmitToWms}
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateRefundSendDetails">
        update pl_eshop_refund_send_detail
        SET ptype_id=#{ptypeId},
            sku_id=#{skuId},
            mapping_state= true
        where platform_num_id = #{platformNumId}
          and platform_properties_name = #{platformPropertiesName}
    </update>

    <select id="getRefundConfigReasonListForEditRefund"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        select *
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and deleted = 0
          and (stoped = 0 or id = #{reasonId})
          and reason_type in (0, 2)
        order by create_time
    </select>
    <select id="getRefundConfigReasonListByRefund"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        select distinct t1.refund_reason,
                        t1.profile_id
        from pl_eshop_refund t1
        where t1.profile_id = #{profileId}
          and t1.deleted = 0
          and t1.id = #{id}
          and t1.refund_reason is not null
    </select>
    <select id="queryDefaultKtypeId" resultType="java.math.BigInteger">
        select default_ktype_id
        from pl_eshop_refund_config
        where profile_id = #{profileId}
    </select>
    <select id="queryRefundDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        select d.* ,r.otype_id
        from pl_eshop_refund_apply_detail d
        left join pl_eshop_refund r on d.profile_id=r.profile_id and d.refund_order_id=r.id
        where d.profile_id=#{profileId}
        <if test="tradeOrderId != null and tradeOrderId != ''">
            and d.trade_order_id=#{tradeOrderId}
        </if>
        <if test="tradeIdList != null and tradeIdList.size() > 0">
            and d.trade_order_id in
            <foreach collection="tradeIdList" open="(" close=")" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
    </select>

    <select id="checkRefundReasonIsDefault" resultType="java.lang.Boolean">
        select count(1)
        from pl_eshop_refund_config_reason t1
        where t1.profile_id = #{profileId}
          and id = #{id}
          and reason_type = 0
    </select>
    <select id="queryRefundByVchcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select refund.profile_id
             , refund.otype_id
             , refund.buyer_id
             , refund.create_time
             , refund.refund_create_time
             , refund.refund_modify_time
             , refund.trade_create_time
             , refund.trade_finish_time
             , refund.trade_pay_time
             , refund.refund_type
             , refund.create_type
             , refund.confirm_state
             , refund.confirm_time
             , refund.confirm_remark
             , refund.confirm_etype_id
             , refund.receive_state
             , refund.receive_buyer_id
             , refund.receive_remark
             , refund.receive_time
             , refund.pay_state
             , refund.pay_account
             , refund.pay_time
             , refund.pay_etype_id
             , refund.pay_number
             , refund.receive_account
             , refund.refund_apply_total
             , refund.refund_apply_taxed_total
             , refund.refund_apply_tax_total
             , refund.refund_apply_freight_fee
             , refund.refund_apply_mall_fee
             , refund.refund_apply_service_fee
             , refund.update_time
             , refund.ktype_id
             , refund.refund_state
             , refund.refund_phase
             , refund.refund_statement
             , refund.trade_status
             , refund.deleted
             , refund.no_detail
             , refund.bill_vchcode
             , refund.bill_poseted
             , refund.bill_total
             , refund.bill_service_fee
             , refund.order_etype_id
             , refund.has_edit
             , refund.order_fee
             , refund.refund_process_state
             , refund.refund_process_time
             , refund.mapping_state
             , refund.cost_state
             , refund.old_ktype_id
             , refund.etype_id
             , refund.memo
             , refund.business_type
             , refund.trade_order_id
             , refund.trade_refund_order_id
             , refund.trade_refund_order_number
             , refund.eshop_order_id
             , refund.id
             , refund.has_submit_to_wms
             , refund.flow_pay_state
             , refund.platform_parent_order_id
             , refund.supplier_id
             , refund.has_commit_to_distributor
             , refund.btype_id
             , refund.platform_refund_state
             , refund.platform_return_state
             , refund.platform_change_state
             , refund.platform_confirm_state
             , refund.id
             , refund.profile_id
             , reason.reason_type
             , refund.deleted
             , refund.create_time
             , refund.update_time
             , reason.custome_default_type
             , refund.refund_type                                 as refundTypeEnum
             , IFNULL(reason.refund_reason, refund.refund_reason) as refund_reason
             , IFNULL(reason.id, refund.refund_reason)            as reason_id
             , refund.refund_finish_time
             , exists  (select *
                        from pl_eshop_refund_checkin_relation
                        where refund_order_id = refund.id
                          and profile_id = refund.profile_id)     as related_checkin

        from pl_eshop_refund refund
                 left join pl_eshop_refund_config_reason reason
                           on refund.refund_reason = reason.id and refund.profile_id = reason.profile_id
        WHERE refund.profile_id = #{profileId}
          and refund.id = #{id}
    </select>
    <select id="queryKTypeIdByRefundNumber" resultType="java.math.BigInteger">
        select ktype_id
        from pl_eshop_refund refund
        WHERE refund.profile_id = #{profileId}
          and refund.id = #{id}
    </select>


    <select id="checkBillIfPosted" resultType="java.lang.Boolean">
        select count(1) > 0
        from acc_bill_core
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
          and post_state = #{postState};
    </select>
    <select id="querySelectedDetailCost"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.api.response.deliver.RefundAndDeliverMappingEntity">
        select id as selectId, sub_qty as selectQty, batch_price as batchPrice, cost_period as costPeriod
        from pl_eshop_refund_detail_original_cost
        WHERE profile_id = #{profileId}
          and refund_order_detail_id = #{detailId}
    </select>
    <select id="queryRefundLogInfoByVchcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select refund.trade_refund_order_number, refund.refund_state, refund.id, refund.otype_id, refund.profile_id
        from pl_eshop_refund refund
        WHERE refund.profile_id = #{profileId}
          and refund.id = #{id}
    </select>
    <select id="queryRefundCount" resultType="java.lang.Integer">
        SELECT count(1) from
        pl_eshop_refund refund
        LEFT JOIN `base_otype` o ON o.profile_id=refund.profile_id and o.id=refund.otype_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        WHERE refund.profile_id=#{profileId} and refund.otype_id not in(
        select id from
        `base_otype`
        where profile_id = #{profileId} and ocategory=2) and refund.confirm_state=0
        and refund.refund_state IN (1, 2, 3, 4, 5) and refund.deleted=0
        <!--        <choose>-->
        <!--            <when test="notDistribution != null and notDistribution == false">-->
        <!--                AND refund.business_type = 2-->
        <!--            </when>-->
        <!--            <otherwise>-->
        <!--                AND refund.business_type != 22-->
        <!--            </otherwise>-->
        <!--        </choose>-->

        <if test="timeType != null">
            <choose>
                <when test="timeType.getCode() == 2">
                    AND refund.create_time &gt;=#{beginTime} AND refund.create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 0">
                    AND refund.trade_create_time &gt;=#{beginTime} AND refund.trade_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 1">
                    AND refund.trade_pay_time &gt;=#{beginTime} and refund.trade_pay_time &lt;=#{endTime}
                </when>
            </choose>
        </if>
        <if test="refundTypes != null and refundTypes.size() > 0">
            AND refund.refund_type IN
            <foreach collection="refundTypes" close=")" open="(" separator="," item="refundTypeEnum">
                #{refundTypeEnum}
            </foreach>
        </if>
        <if test="deleted != null and deleted.getCode() >= 0">
            AND refund.deleted=#{deleted}
        </if>
        <if test="allowEtypeIds != null and allowEtypeIds.size() > 0 and etypeLimited">
            and refund.order_etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="refundNoDetail != null">
            <if test="refundNoDetail.getCode() == 0">
                and refund.no_detail=0
            </if>
            <if test="refundNoDetail.getCode() == 2">
                and refund.no_detail>=0
            </if>
        </if>
        <if test="queryRefundOnlyOnSaleEnum != null and queryRefundOnlyOnSaleEnum.getCode() != 2">
            AND refund.refund_phase=#{queryRefundOnlyOnSaleEnum}
        </if>
    </select>

    <select id="queryRefundCountAll" resultType="java.lang.Integer">
        SELECT count(1)
        from pl_eshop_refund refund
        LEFT JOIN
        pl_eshop eshop
        on eshop.profile_id = refund.profile_id
        and eshop.otype_id = refund.otype_id
        LEFT JOIN
        `base_otype` o
        ON o.profile_id = refund.profile_id
        and o.id = eshop.otype_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=peso.profile_id and blsk.object_type=2 and
            peso.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=peso.profile_id and bls.object_type=3 and
            peso.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        WHERE refund.profile_id = #{profileId}
        and o.ocategory != 2
        and refund.no_detail = 0
        AND date_sub(CURDATE(), INTERVAL 90 DAY) &lt;= refund.create_time
        AND refund.confirm_state = 0
        AND refund.refund_state IN (
        1, 2, 3, 4, 5, 6
        )
        AND refund.business_type != 2
    </select>

    <select id="queryEveryDateLocalRefundCount" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.DayRefundCount">
        select count(0) as `count`, DATE(refund_create_time) as `date`
        from pl_eshop_refund
        where profile_id = #{profileId}
          and create_type = 1
          and otype_id = #{otypeId}
          and refund_create_time between #{startDate} and #{endDate}
        group by DATE(refund_create_time)
        order by refund_create_time desc
    </select>


    <select id="getDefaultEtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype">
        SELECT t1.id, t1.fullname
        FROM base_etype t1
        WHERE t1.profile_id = #{profileId}
          AND t1.id = #{employeeId}
    </select>
    <select id="getDefaultKtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Ktype">
        SELECT t1.id, t1.profile_id, t1.fullname, t1.scategory
        FROM base_ktype t1
        LEFT JOIN base_limit_scope bls ON t1.profile_id=bls.profile_id AND bls.object_type=2 AND t1.id=bls.object_id
        WHERE t1.profile_id =#{profileId}
        AND t1.sysrow = 1
        AND t1.scategory = 0
        <if test="employeeId != null and employeeId != 0">
            AND bls.etype_id =#{employeeId}
        </if>
        LIMIT 1
    </select>
    <select id="queryRefundFreightLists"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundFreight">
        SELECT frt.id,
        frt.profile_id,
        frt.refund_order_id,
        frt.freight_template_id,
        frt.freight_no,
        frt.create_time,
        frt.update_time,
        CASE IFNULL(frt.freight_btype_id, 0)
        WHEN 0 THEN lg.freight_btype_id
        ELSE frt.freight_btype_id END AS freight_btype_id,
        temp.template_name AS freight_template_name,
        frt.freight_name,
        frt.freight_code,
        frt.freight_type,
        frt.freight_intercept_status,
        btype.id as freight_btype_id
        FROM pl_eshop_refund_freight frt
        LEFT JOIN td_template temp ON temp.profile_id = frt.profile_id AND temp.id = frt.freight_template_id
        LEFT JOIN td_logistics_template lg
        ON temp.`id` = lg.`template_id` AND temp.`profile_id` = lg.`profile_id`
        LEFT JOIN base_btype btype ON btype.profile_id = frt.profile_id AND btype.id = lg.freight_btype_id
        LEFT JOIN base_btype btype1 ON btype1.profile_id = frt.profile_id AND btype1.id = frt.freight_btype_id
        WHERE frt.profile_id = #{profileId}
        and frt.refund_order_id in
        <foreach collection="refundOrderIds" close=")" open="(" separator="," item="refundOrderId">
            #{refundOrderId}
        </foreach>;
    </select>

    <select id="queryDeliverRefundFreightLists"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundFreight">
        select freight.freight_billno as freightNo,freight.freight_btype_id,b.fullname AS 'freightBTypeName',b.usercode
        as freightCode from
        td_deliver_freight_info freight
        LEFT JOIN base_btype b ON freight.profile_id=b.profile_id AND freight.freight_btype_id=b.id
        where freight.profile_id=#{profileId} and freight.vchcode in
        <foreach collection="vchcodes" item="vchcode" open="(" close=")" separator=",">
            #{vchcode}
        </foreach>
        order by freight.create_time
    </select>
    <select id="queryRefundDetailListByVchcodes"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        select 0 AS selected, rd.*,
        bp.fullname as ptype_name,bp.ptype_type,bp.ptype_area,
        bp.shortname as ptypeShortName,
        bp.usercode as ptype_code,bp.pcategory as pcategory,
        /* bp.barcode,*/
        bp.ptype_width,bp.ptype_length,bp.ptype_height,bp.weight as
        ptype_weight,bp.length_unit,bp.weight_unit,bp.standard as ptype_standard,
        bpx.xcode as xcode,bpf.fullbarcode as barcode,bp.sub_unit,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name,unit.unit_rate,price.retail_price,price.min_sale_price,
        bsunit.unit_rate,bsunit.unit_name as base_unit_name,
        pic.pic_url, bp.cost_price,bp.snenabled,bp.batchenabled,bp.propenabled,
        bp.cost_mode,bp.protect_days_view,bp.protect_days_unit,
        rd.batch_price,rd.cost_period,rd.cost_state,rd.cost_price,
        refund.confirm_state,refund.receive_state as receive_status,refund.deleted,refund.refund_type,
        ifnull(rd.refund_state,refund.refund_state) as refund_state,refund.trade_refund_order_number,
        refund.otype_id,refund.refund_type as 'refundTypeEnum',
        ifnull(rd.platform_pic_url,'') as platform_pic_url,rd.batchNo,
        rrcd.goods_state,rd.actual_receipt_number as actualReceiptNumber,rrcd.vchcode as checkinVchcode,rrcd.id as
        checkinDetailId,
        rd.gift,
        0 as dised_taxed_total,
        brand.brand_name,
        rd.re_send_state,
        rd.memo,
        peraddb.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        peraddb.buyer_dised_taxed_total as distributionDisedTaxedTotal,
        ex.eshop_order_id
        ,ex.refund_save_principal_total,ex.refund_save_present_total,ex.refund_save_total,ex.refund_preference_total,ex.refund_preference_allot_total
        from pl_eshop_refund_apply_detail rd
        left join pl_eshop_refund_apply_detail_extend ex on ex.profile_id = rd.profile_id and ex.refund_detail_id = rd.id
        left join pl_eshop_refund_apply_detail_combo as rdc on rdc.profile_id=rd.profile_id and
        rd.combo_row_id=rdc.id
        join pl_eshop_refund refund on refund.id=rd.refund_order_id and refund.profile_id=rd.profile_id
        left join base_ptype bp on bp.profile_id=rd.profile_id and rd.ptype_id=bp.id
        left join base_brandtype brand on bp.profile_id = brand.profile_id and bp.brand_id = brand.id
        left join base_ptype_xcode bpx on bpx.profile_id=rd.profile_id and bpx.sku_id=rd.sku_id and bpx.unit_id=rd.unit
        and bpx.defaulted=1
        left join base_ptype_sku bps on bps.profile_id=rd.profile_id and bps.id=rd.sku_id
        left join base_ptype_pic pic on pic.profile_id=rd.profile_id and rd.ptype_id=pic.ptype_id and pic.rowindex=1
        left join base_ptype_fullbarcode bpf on bpf.profile_id=rd.profile_id and bpf.ptype_id=rd.ptype_id and
        bpf.sku_id=rd.sku_id and bpf.unit_id=rd.unit and bpf.defaulted=1
        left join base_ptype_unit unit on unit.profile_id=rd.profile_id and unit.id=rd.unit
        left join base_ptype_price price on price.profile_id=rd.profile_id and price.unit_id=unit.id and
        unit.ptype_id=rd.ptype_id and price.sku_id = rd.sku_id
        left join base_ptype_unit bsunit on bsunit.profile_id=rd.profile_id and bsunit.ptype_id=rd.ptype_id and
        bsunit.unit_code=1
        left join pl_eshop_refund_receive_checkin_detail rrcd on rd.check_detail_id=rrcd.id and rd.profile_id =
        rrcd.profile_id
        left join pl_eshop_refund_apply_detail_distribution_buyer peraddb on peraddb.profile_id = rd.profile_id and
        peraddb.refund_order_detail_id = rd.id
        where rd.profile_id=#{profileId} and rd.refund_order_id in
        <foreach collection="refundOrderIds" separator="," open="(" close=")" item="refundOrderId" index="0">
            #{refundOrderId}
        </foreach>
        <if test="detailCreateType != null and detailCreateType != 0">
            and rd.create_type = #{detailCreateType}
        </if>
    </select>
    <select id="queryRefundComboDetailListByVchcodes"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetailCombo">
        select rdc.*,
        ptype.fullname as ptype_name,ptype.id as ptypeId,ptype.usercode as ptype_code,
        ptype.barcode,ptype.sub_unit,ptype.weight_unit,ptype.weight as ptype_weight,ptype.propenabled,
        pic.pic_url,ptype.cost_price,rdc.price as unit_price,rdc.qty as unit_qty,
        rdc.actual_receipt_number as actualReceiptNumber,perrcdc.goods_state,perrc.checkin_number,
        1 as unit_rate,
        bpcd.gifted as gift,0 as dised_taxed_total, null as protectDays,
        dcd.buyer_dised_taxed_price as 'distributionDisedTaxedPrice',
        dcd.buyer_dised_taxed_total as 'distributionDisedTaxedTotal'
        from pl_eshop_refund_apply_detail_combo rdc
        left join base_ptype ptype on ptype.profile_id=rdc.profile_id and rdc.combo_id=ptype.id
        left join base_ptype_combo_detail bpcd on bpcd.profile_id=ptype.profile_id and ptype.id = bpcd.ptype_id
        left join base_ptype_pic pic on pic.profile_id=rdc.profile_id and rdc.combo_id=pic.ptype_id and pic.rowindex=1
        left join pl_eshop_refund_receive_checkin_detail_combo perrcdc on rdc.check_combo_row_id=perrcdc.combo_row_id
        left join pl_eshop_refund_receive_checkin perrc on perrc.vchcode=perrcdc.vchcode
        left join pl_eshop_refund_apply_detail_combo_distribution_buyer dcd on dcd.profile_id = rdc.profile_id and
        dcd.refund_order_combo_detail_id = rdc.id
        where rdc.profile_id=#{profileId} and rdc.refund_order_id in
        <foreach collection="refundOrderIds" separator="," open="(" close=")" item="refundOrderId" index="0">
            #{refundOrderId}
        </foreach>
    </select>
    <select id="queryRefundDetailSerialInfosByVchcodes"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundDetailSerialNo">
        select id, profile_id, snno, sn1, sn2, sn3, sn_memo, create_time, update_time, refund_order_id,
        refund_order_detail_id as detailId
        from ${tableName}
        where profile_id = #{profileId}
        and refund_order_id in
        <foreach collection="refundOrderIds" separator="," open="(" close=")" item="refund_order_id" index="0">
            #{refund_order_id}
        </foreach>
    </select>
    <select id="getRefundIdsByTradeId"
            resultType="com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundInfoParam">
        select trade_order_id, trade_refund_order_id
        from pl_eshop_refund
        where profile_id = #{profileId}
          and trade_order_id = #{tradeId}
    </select>
    <select id="getBillTasklPlanInfoByRefund"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.BillTasklPlanInfo">
        SELECT bill.vchcode, t.task_number AS taskNumber, t.etype_id AS driverId, t.car_id AS carId
        FROM pl_eshop_sale_order s
                 LEFT JOIN `td_bill_deliver` bill ON bill.order_id = s.id AND bill.profile_id = s.profile_id
                 LEFT JOIN base_vtask_bill v ON v.vchcode = bill.vchcode AND v.profile_id = bill.profile_id
                 LEFT JOIN base_vtask t ON t.id = v.task_id AND t.profile_id = v.profile_id
        WHERE s.profile_id = #{profileId}
          and s.trade_order_id = #{tradeOrderId}
        limit 1
    </select>
    <select id="listRefundFreights" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundFreight"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select *
        from pl_eshop_refund_freight
        where profile_id = #{profileId}
          and refund_order_id = #{id}
    </select>
    <select id="getUndeletedRefundCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(id)
        from pl_eshop_refund
        where trade_order_id = #{tradeOrderId}
          and profile_id = #{profileId}
          and deleted = 0
    </select>
    <select id="getRefundIdByVchcode" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.WriteBackRefundStateParameter">
        select source_vchcode
        from td_bill_relation
        where target_vchcode = #{vchcode}
          and profile_id = #{profileId}
          and source_vchtype = 9801
    </select>

    <select id="getRefundIdByTargetVchcode" resultType="java.math.BigInteger">
        select source_vchcode
        from td_bill_relation
        where target_vchcode = #{targetVchcode}
          and profile_id = #{profileId}
          and source_vchtype = 9801
          and target_vchtype = 9802
    </select>

    <select id="querySendUnRelationDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundSendDetail">
        select d.* ,r.otype_id
        from pl_eshop_refund_send_detail d
        ,pl_eshop_refund r
        where
        d.refund_order_id = r.id
        and d.ptype_id = 0
        and d.sku_id = 0
        and d.profile_id = #{profileId}
        and r.otype_id = #{eshopId}
        <if test="platformNumId != null and platformNumId.size() > 0">
            and d.platform_num_id in
            <foreach collection="platformNumId" close=")" open="(" separator="," item="numId">
                #{numId}
            </foreach>
        </if>
        <if test="platformSkuId != null and platformSkuId.size() > 0">
            and d.platform_sku_id in
            <foreach collection="platformSkuId" close=")" open="(" separator="," item="skuId">
                #{skuId}
            </foreach>
        </if>
        <if test="platformProperties != null and platformProperties.size() > 0">
            and d.platform_properties_name in
            <foreach collection="platformProperties" close=")" open="(" separator="," item="prop">
                #{prop}
            </foreach>
        </if>
    </select>
    <select id="queryRefundDetailsByRefund"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        select detail.*,ptype.fullname as ptypeName from pl_eshop_refund_apply_detail detail
        left join base_ptype ptype on ptype.id = detail.ptype_id and ptype.profile_id = detail.profile_id
        where detail.profile_id=#{profileId} and detail.refund_order_id in
        <foreach collection="refundEntities" close=")" open="(" separator="," item="refundItm">
            #{refundItm.id}
        </foreach>
    </select>
    <select id="queryRefundSendDetailsByRefund"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundSendDetail">
        select * from pl_eshop_refund_send_detail
        where profile_id=#{profileId} and refund_order_id in
        <foreach collection="refundEntities" close=")" open="(" separator="," item="refundItm">
            #{refundItm.id}
        </foreach>
    </select>

    <update id="updateReceiveStateOnly">
        UPDATE pl_eshop_refund
        SET receive_state= #{refundReceiveStatus},
            receive_time = #{receiveTime}
        where profile_id = #{profileId}
          and id = #{id};
        UPDATE pl_eshop_refund_extend
        SET receive_etype_id= #{receiveEtypeId}
        where profile_id = #{profileId}
          and refund_order_id = #{id};
    </update>

    <update id="updateReceiveStateOnlyBatch">
        UPDATE pl_eshop_refund
        SET receive_state= #{refundReceiveStatus},
        receive_time = #{receiveTime}
        where profile_id = #{profileId}
        and id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>;
        UPDATE pl_eshop_refund_extend
        SET receive_etype_id= #{receiveEtypeId}
        where profile_id = #{profileId}
        and refund_order_id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>;
    </update>

    <update id="updateDetailRefundState"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        update pl_eshop_refund_apply_detail
        set refund_state = #{refundState}
        where profile_id = #{profileId}
          and refund_order_id = #{id}
    </update>

    <update id="updateCommitDistributor">
        UPDATE pl_eshop_refund
        SET has_commit_to_distributor=#{hasCommitToDistributor}
        where profile_id = #{profileId}
        <if test="refundList.size() > 0">
            and id in
            <foreach item="refund" collection="refundList" open="(" separator="," close=")">
                #{refund.id}
            </foreach>
        </if>
    </update>
    <update id="notifyRefundSendState">
        <foreach item="item" collection="requests" index="index">
            update pl_eshop_refund_apply_detail
            set re_send_state = #{item.reSendState}
            where profile_id = #{item.profileId}
            and refund_order_id = #{item.refundOrderId};
        </foreach>
    </update>
    <update id="changePlatformState">
        update pl_eshop_refund
        <set>
            <if test="platformConfirmState!=null">
                platform_confirm_state = #{platformConfirmState},
            </if>
            <if test="platformRefundState!=null">
                platform_refund_state = #{platformRefundState},
            </if>
            <if test="platformChangeState!=null">
                platform_change_state = #{platformChangeState},
            </if>
            <if test="platformReturnState!=null">
                platform_return_state = #{platformReturnState},
            </if>
        </set>
        where profile_id = #{profileId} and id = #{id}
    </update>
    <update id="updateRefundApplyDetailMemo">
        update pl_eshop_refund_apply_detail set memo = '',actual_receipt_number = 0
        where profile_id=#{profileId} and check_detail_id != 0 and
        refund_order_id in
        <foreach collection="refundIds" close=")" open="(" separator="," item="refundOrderId">
            #{refundOrderId}
        </foreach>
    </update>

    <delete id="deleteRefundDetailsByDetail">
        DELETE
        FROM pl_eshop_refund_apply_detail
        WHERE profile_id = #{profileId}
          and id = #{id}
    </delete>
    <delete id="deleteRefundDuty" parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty">
        delete
        from pl_eshop_refund_duty
        where profile_id = #{profileId}
          and id = #{id}
    </delete>
    <delete id="deleteRefundDetailsByIds">
        DELETE
        FROM pl_eshop_refund_apply_detail
        WHERE profile_id = #{profileId}
        and id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteRefundDetailCombosByIds">
        DELETE
        FROM pl_eshop_refund_apply_detail_combo
        WHERE profile_id = #{profileId}
        and id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteEshopRefundCheckinRelation"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        delete
        from pl_eshop_refund_checkin_relation
        where profile_id = #{profileId}
          and refund_order_id = #{refundOrderId}
          and checkin_id = #{vchcode}
    </delete>
    <delete id="deleteEshopRefundCheckinRelationBatch">
        delete from pl_eshop_refund_checkin_relation where profile_id = #{profileId}
        and refund_order_id=#{refundId}
        and checkin_id in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">
            #{vchcode}
        </foreach>
    </delete>
    <delete id="deletePlEshopRefundApplyDetailDistributionBuyer"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        delete
        from pl_eshop_refund_apply_detail_distribution_buyer
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </delete>
    <delete id="deletePlEshopRefundApplyDetailComboDistributionBuyer"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        delete
        from pl_eshop_refund_apply_detail_combo_distribution_buyer
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </delete>
    <delete id="deletePlEshopRefundSendDetailDistributionBuyer"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        delete
        from pl_eshop_refund_send_detail_distribution_buyer
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </delete>
    <delete id="deletePlEshopRefundSendDetailComboDistributionBuyer"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        delete
        from pl_eshop_refund_send_detail_combo_distribution_buyer
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </delete>
    <delete id="deleteRefundReasonById">
        delete
        from pl_eshop_refund_config_reason
        where id = #{id}
          and profile_id = #{profileId}
    </delete>
    <delete id="deleteSnapshot">
        delete
        from pl_eshop_refund_snapshot
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </delete>
    <delete id="deleteCheckinDetailAndInoutRecord">
        delete
        from pl_eshop_refund_receive_checkin_detail
        where vchcode = #{vchcode}
          and profile_id = #{profileId};
        delete
        from pl_eshop_refund_receive_checkin_detail_combo
        where vchcode = #{vchcode}
          and profile_id = #{profileId};
        delete
        from pl_eshop_refund_receive_checkin_detail_serialno
        where vchcode = #{vchcode}
          and profile_id = #{profileId};
        delete
        from td_bill_inout_record
        where inout_id = #{vchcode}
          and profile_id = #{profileId};
        delete
        from td_bill_inout_detail
        where inout_id = #{vchcode}
          and profile_id = #{profileId};
        delete
        from td_bill_inout_detail_combo
        where inout_id = #{vchcode}
          and profile_id = #{profileId};
        delete
        from td_bill_detail_serialno
        where inout_id = #{vchcode}
          and profile_id = #{profileId};
        delete
        from td_bill_detail_batch
        where inout_id = #{vchcode}
          and profile_id = #{profileId};
    </delete>
    <delete id="deleteRefundIndex">
        delete
        from pl_eshop_refund_order_index
        where bill_vchcode = #{billVchcode}
          and profile_id = #{profileId}
    </delete>
    <delete id="deleteRefundDetailsExtend">
        delete
        from pl_eshop_refund_apply_detail_extend
        where profile_id = #{profileId}
          and refund_order_id = #{id}
    </delete>

    <select id="getPlatformParentOrderId" resultType="java.lang.String">
        select platform_parent_order_id
        from pl_eshop_sale_order
        where trade_order_id = #{tradeOrderId}
          and profile_id = #{profileId}
          and otype_id = #{otypeId}
    </select>

    <select id="getIdsByPtypeInfo" resultType="string">
        select detail.refund_order_id
        from base_ptype ptype
        left join pl_eshop_refund_apply_detail detail
        on ptype.id = detail.ptype_id and ptype.profile_id = detail.profile_id
        left join pl_eshop_refund refund on detail.refund_order_id = refund.id and detail.profile_id = refund.profile_id
        where (ptype.fullname like CONCAT('%', #{param.ptypeWord}, '%') or ptype.usercode like CONCAT('%',
        #{param.ptypeWord}, '%'))
        and ptype.profile_id = #{param.profileId}
        <if test="param.refundcreateTime != null">
            and refund.create_time &gt;= #{param.refundcreateTime}
        </if>
        <if test="param.refundEndTime != null">
            and refund.create_time &lt;= #{param.refundEndTime}
        </if>
    </select>

    <select id="getNumIdsByBabyInfo" resultType="string">
        select platform_num_id
        from pl_eshop_product product
        where product.platform_fullname like CONCAT('%', #{ptypeWord}, '%')
          and product.profile_id = #{profileId}
    </select>

    <select id="getSkuIdsByBabyInfo" resultType="string">
        select platform_sku_id
        from pl_eshop_product_sku sku
        where (sku.platform_full_properties like CONCAT('%', #{ptypeWord}, '%') or
               sku.platform_xcode like CONCAT('%', #{ptypeWord}, '%'))
          and sku.profile_id = #{profileId}
    </select>
    <select id="getRefundIdsByMark" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryRefundParameter">
        select order_id from pl_eshop_order_mark where profile_id = #{profileId} and order_type = 2
        <if test="refundParameter.markType != null and refundParameter.markType.size() > 0">
            and mark_code in
            <foreach item="type" collection="refundParameter.markType" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
    </select>
    <select id="queryRefundedDetail" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        select
        0 AS selected, rd.*,
        bp.fullname as ptype_name,bp.ptype_type,bp.ptype_area,
        bp.shortname as ptypeShortName,
        bp.usercode as ptype_code,bp.pcategory as pcategory,
        /* bp.barcode,*/
        bp.ptype_width,bp.ptype_length,bp.ptype_height,bp.weight as
        ptype_weight,bp.length_unit,bp.weight_unit,bp.standard as ptype_standard,
        bpx.xcode as xcode,bpf.fullbarcode as barcode,bp.sub_unit,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name,unit.unit_rate,price.retail_price,price.min_sale_price,
        bsunit.unit_rate,bsunit.unit_name as base_unit_name,
        pic.pic_url,bp.protect_days, bp.cost_price,bp.snenabled,bp.snenabled as
        snenabledFromDb,bp.batchenabled,bp.propenabled,
        bp.cost_mode,bp.sub_unit as subUnit,
        rd.cost_period,rd.cost_state,rd.cost_price,
        rd.sub_qty AS subQty,
        refund.confirm_state,refund.receive_state as receive_status,refund.deleted,refund.refund_type,
        ifnull(rd.refund_state,refund.refund_state) as refund_state,refund.trade_refund_order_number,
        refund.otype_id,
        ifnull(rd.platform_pic_url,'') as platform_pic_url,rd.batchNo,rd.batch_price as batchPrice,
        rd.actual_receipt_number as actualReceiptNumber,
        rrcd.vchcode as checkinVchcode,rrcd.id as checkinDetailId,
        rd.gift,refund.refund_phase as refundPhase,refund.refund_type as refundTypeEnum,
        0 as disedTaxedTotal,rd.sku_id,rd.unit,
        rd.re_send_state,
        rd.memo,
        ex.eshop_order_id
        ,ex.refund_save_principal_total,ex.refund_save_present_total,ex.refund_save_total,ex.refund_preference_total,ex.refund_preference_allot_total
        from pl_eshop_refund_apply_detail rd
        left join pl_eshop_refund_apply_detail_extend ex on ex.profile_id = rd.profile_id and ex.refund_detail_id = rd.id
        left join pl_eshop_refund_apply_detail_combo as rdc on rdc.profile_id=rd.profile_id and
        rd.combo_row_id=rdc.id
        join pl_eshop_refund refund on refund.id=rd.refund_order_id and refund.profile_id=rd.profile_id
        left join base_ptype bp on bp.profile_id=rd.profile_id and rd.ptype_id=bp.id
        left join base_ptype_xcode bpx on bpx.profile_id=rd.profile_id and bpx.sku_id=rd.sku_id and bpx.unit_id=rd.unit
        and bpx.defaulted=1
        left join base_ptype_sku bps on bps.profile_id=rd.profile_id and bps.id=rd.sku_id
        left join base_ptype_pic pic on pic.profile_id=rd.profile_id and rd.ptype_id=pic.ptype_id and pic.rowindex=1
        left join base_ptype_fullbarcode bpf on bpf.profile_id=rd.profile_id and bpf.ptype_id=rd.ptype_id and
        bpf.sku_id=rd.sku_id and bpf.unit_id=rd.unit and bpf.defaulted=1
        left join base_ptype_unit unit on unit.profile_id=rd.profile_id and unit.id=rd.unit
        left join base_ptype_price price on price.profile_id=rd.profile_id and price.unit_id=unit.id and
        unit.ptype_id=rd.ptype_id and price.sku_id = rd.sku_id
        left join base_ptype_unit bsunit on bsunit.profile_id=rd.profile_id and bsunit.ptype_id=rd.ptype_id and
        bsunit.unit_code=1
        left join pl_eshop_refund_receive_checkin_detail rrcd on rd.check_detail_id=rrcd.id and rd.profile_id =
        rrcd.profile_id
        where rd.profile_id = #{profileId} and refund.deleted = 0
        and rd.trade_order_id = #{tradeOrderId}
        <if test="refundApplyDetails.size() > 0 ">
            and rd.ptype_id in
            <foreach collection="refundApplyDetails" close=")" open="(" separator="," item="item">
                #{item.ptypeId}
            </foreach>
            and rd.sku_id in
            <foreach collection="refundApplyDetails" close=")" open="(" separator="," item="item">
                #{item.skuId}
            </foreach>
            and rd.unit in
            <foreach collection="refundApplyDetails" close=")" open="(" separator="," item="item">
                #{item.unit}
            </foreach>
        </if>
    </select>
    <select id="getIdsByProductInfo" resultType="string">
        select detail.refund_order_id
        from pl_eshop_refund_apply_detail detail
        left join pl_eshop_refund refund on detail.refund_order_id = refund.id and detail.profile_id = refund.profile_id
        where (detail.platform_full_name like CONCAT('%', #{param.ptypeWord}, '%') or detail.platform_xcode like
        CONCAT('%', #{param.ptypeWord}, '%') or detail.platform_properties_name like CONCAT('%', #{param.ptypeWord},
        '%'))
        and detail.profile_id = #{param.profileId}
        <if test="param.refundcreateTime != null">
            and refund.create_time &gt;= #{param.refundcreateTime}
        </if>
        <if test="param.refundEndTime != null">
            and refund.create_time &lt;= #{param.refundEndTime}
        </if>
    </select>
    <select id="getEtypeNameById" resultType="java.lang.String">
        select fullname
        from base_etype
        where id = #{etypeId}
          and profile_id = #{profileId}
    </select>

    <select id="getRefundNumberByTradeIds" resultType="java.lang.String">
        select trade_refund_order_number
        from pl_eshop_refund
        where profile_id=#{profileId} and otype_id=#{eshopId}
        and trade_order_id in
        <foreach collection="tradeIds" close=")" open="(" separator="," item="tid">
            #{tid}
        </foreach>
    </select>
    <select id="getPlatformStoreId" resultType="java.lang.String">
        SELECT platform_stock_id
        FROM pl_eshop_sale_order
        where id = #{eshopOrderId}
          and profile_id = #{profileId}
    </select>
    <select id="getRefundCombowQty" resultType="java.math.BigDecimal">
        select qty
        from pl_eshop_refund_apply_detail_combo
        where profile_id = #{profileId}
          and id = #{comboRowId}
    </select>
    <select id="getOrderCombowQty" resultType="java.math.BigDecimal">
        select qty
        from pl_eshop_sale_order_detail_combo
        where profile_id = #{profileId}
          and eshop_order_detail_combo_row_id = #{comboRowId}
    </select>

    <update id="updateRefundDetailQty">
        <foreach separator=";" collection="refundApplyDetails" item="item" index="index">
            update pl_eshop_refund_apply_detail
            <set>
                qty = #{item.qty},
                apply_refund_unit_qty = #{item.applyRefundUnitQty},
                apply_refund_qty = #{item.applyRefundQty},
                sub_qty = #{item.subQty},
                apply_refund_sub_qty = #{item.applyRefundSubQty}
            </set>
            where profile_id = #{item.profileId}
            and id = #{item.id}
        </foreach>
    </update>
    <update id="updateRefundComboQty">
        <foreach separator=";" collection="eshopRefundApplyDetailCombos" item="item" index="index">
            update pl_eshop_refund_apply_detail_combo
            <set>
                qty = #{item.qty},
                apply_refund_qty = #{item.applyRefundQty}
            </set>
            where profile_id = #{item.profileId}
            and id = #{item.id}
        </foreach>
    </update>

    <update id="updateRefundPorcessState"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        update pl_eshop_refund
        set refund_process_state=#{refundProcessState},
            refund_process_time = #{refundProcessTime}
        where id = #{id}
          and profile_id = #{profileId};
        update pl_eshop_refund_extend
        set process_etype_id = #{processEtypeId}
        where refund_order_id = #{id}
          and profile_id = #{profileId};
    </update>
    <update id="updateRefundDuty"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.UpdateRefundDutyRequest">
        update pl_eshop_refund
        <set>
            <if test="refundDutyIdsCheck">
                refund_duty_ids = #{refundDutyIds},
            </if>
            <if test="refundReasonCheck">
                refund_reason = #{reasonId},
            </if>
            <if test="refundRemarkCheck">
                refund_statement = #{remark},
            </if>
            <if test="edCommentCheck">
                memo = #{memo},
            </if>
        </set>
        where profile_id = #{profileId} and id in
        <foreach item="id" collection="vchcodes" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateRefundCheckinDetailActualNumber">
        <foreach collection="refundApplyDetails" separator=";" item="item">
            update pl_eshop_refund_apply_detail set actual_receipt_number = #{item.actualReceiptNumber}
            where profile_id=#{item.profileId} and id = #{item.id}
        </foreach>
    </update>
    <update id="updateRefundFinishTime">
        UPDATE pl_eshop_refund
        SET refund_finish_time=#{refundFinishTime}
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateRefundCheckinComboActualNumber">
        <foreach collection="refundApplyDetails" separator=";" item="item">
            update pl_eshop_refund_apply_detail_combo set actual_receipt_number = #{item.actualReceiptNumber}
            where profile_id=#{item.profileId} and id = #{item.id}
        </foreach>
    </update>


    <select id="listRefundSimple" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryRefundParameter">
        select
        refund.profile_id, refund.otype_id, refund.buyer_id, refund.create_time,
        refund.refund_create_time, refund.refund_modify_time, refund.trade_create_time,
        refund.trade_finish_time, refund.trade_pay_time, refund.refund_type as refundTypeEnum,
        refund.refund_reason, refund.create_type, refund.confirm_state, refund.confirm_time,
        refund.confirm_remark, refund.confirm_etype_id, refund.receive_state, refund.receive_buyer_id,
        refund.receive_remark, refund.receive_time, refund.pay_state, refund.pay_account, refund.pay_time,
        refund.pay_etype_id, refund.pay_number, refund.receive_account, refund.refund_apply_total,
        refund.refund_apply_taxed_total, refund.refund_apply_tax_total, refund.refund_apply_freight_fee,
        refund.refund_apply_mall_fee, refund.refund_apply_service_fee, refund.update_time, refund.ktype_id,
        refund.refund_state, refund.refund_phase,
        refund.refund_statement, refund.trade_status,refund.deleted, refund.no_detail, refund.bill_vchcode,
        refund.bill_poseted, refund.bill_total,
        refund.bill_service_fee, refund.order_etype_id, refund.has_edit, refund.order_fee, refund.refund_process_state,
        refund.refund_process_time,
        refund.mapping_state, refund.cost_state, refund.old_ktype_id, refund.etype_id, refund.memo,
        refund.business_type, refund.trade_order_id,
        refund.trade_refund_order_id, refund.trade_refund_order_number, refund.eshop_order_id, refund.id,
        refund.has_submit_to_wms, refund.flow_pay_state,
        refund.platform_parent_order_id,
        ktype.fullname as ktypeName,
        freight.freight_name as refundCompany,freight.freight_no as refundCompanyNumber
        from pl_eshop_refund refund
        left join base_ktype ktype on ktype.profile_id = refund.profile_id and refund.ktype_id = ktype.id
        left join pl_eshop_refund_freight freight on freight.profile_id = refund.profile_id and
        freight.refund_order_id=refund.id
        where refund.profile_id = #{param.profileId}
        and refund.deleted = 0
        and refund.no_detail = 0
        and refund.trade_order_id in
        <foreach item="id" collection="param.tradeIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getRefundTiming" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select *
        from pl_eshop
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
    </select>
    <select id="getRefundAllCount" resultType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsEntity"
            parameterType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsRequest">
        SELECT refund.otype_id, COUNT(refund.id) as refundAllCount
        FROM pl_eshop_refund refund
        LEFT JOIN base_otype o
        ON o.profile_id = refund.profile_id
        AND o.id = refund.otype_id
        <if test="request.ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{request.etypeId}
        </if>
        <if test="request.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{request.etypeId}
        </if>
        WHERE refund.profile_id = #{request.profileId}
        AND o.ocategory != 2
        AND refund.no_detail = 0
        AND refund.create_time &gt;= #{request.beginTime}
        AND refund.create_time &lt;= #{request.endTime}
        AND refund.refund_type IN (0, 1, 2, 3, 4, 5, 6)
        AND refund.refund_state IN (1, 2, 3, 4, 5)
        AND refund.deleted = 0
        group by refund.otype_id
    </select>

    <sql id="queryTimeTypeSql">
        <if test="request.queryTimeType == @com.wsgjp.ct.sale.biz.jarvis.state.QueryTimeTypeEnum@TRADE_CREATE_TIME">
            and refund.create_time between #{request.beginTime} and #{request.endTime}
        </if>
        <if test="request.queryTimeType == @com.wsgjp.ct.sale.biz.jarvis.state.QueryTimeTypeEnum@PAY_TIME ">
            and refund.confirm_time between #{request.beginTime} and #{request.endTime}
        </if>
        <if test="request.otypeIds!=null and request.otypeIds.size >0">
            and refund.otype_id in
            <foreach collection="request.otypeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="queryTimeTypeSqlForRefund">
        and refund.create_time between #{request.beginTime} and #{request.endTime}
        <if test="request.otypeIds!=null and request.otypeIds.size >0">
            and refund.otype_id in
            <foreach collection="request.otypeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getOverTimeRefundCount" resultType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsDAO"
            parameterType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsRequest">
        SELECT refund.otype_id, refund.id as 'warehouseTaskId', refund.ktype_id
        FROM pl_eshop_refund refund
        LEFT JOIN base_otype o
        ON o.profile_id = refund.profile_id
        AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_receive_checkin checkin
        ON refund.profile_id = checkin.profile_id
        AND refund.id = checkin.refund_order_id
        LEFT JOIN pl_eshop_refund_timing timing
        ON refund.profile_id = timing.profile_id
        AND refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
        <if test="request.ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{request.etypeId}
        </if>
        <if test="request.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{request.etypeId}
        </if>
        WHERE refund.profile_id = #{request.profileId}
        AND o.ocategory != 2
        AND refund.no_detail = 0
        <include refid="queryTimeTypeSqlForRefund"/>
        AND refund.refund_type IN (0, 1, 2, 3, 4, 6)
        AND refund.refund_state not in (0,4,6)
        AND refund.deleted = 0
        and ((now() > timing.sys_promised_confirm_time and refund.confirm_state = 0 and refund.refund_type != 5 and
        refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND
        refund.refund_state != 6) or
        (now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type != 0 and
        refund.refund_type != 5 and refund.refund_state != 0 AND refund.refund_state not in (0, 4, 5, 6)) or
        (now() > timing.promised_confirm_time AND refund.refund_type != 2 and refund.refund_type != 3 and
        refund.refund_type != 4 and refund.create_type = 1 and refund.refund_state != 0 AND
        refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state != 6) or
        (now() > timing.promised_deliver_time and refund.refund_process_state = 0 and refund.refund_state != 0 AND
        refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state != 6) or
        (now() > timing.promised_agree_time AND refund.refund_type != 2 AND refund.refund_type != 0 and
        refund.refund_type != 5 and refund.refund_type != 3 and refund.refund_type != 4 and
        refund.create_type = 1 and refund.refund_state = 1 and refund.refund_state != 0 AND
        refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state != 6 AND
        refund.platform_return_state = 0))

    </select>
    <select id="getAboutToOverTimeRefundCount"
            resultType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsDAO"
            parameterType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsRequest">
        SELECT refund.otype_id, refund.id as 'warehouseTaskId', refund.ktype_id
        FROM pl_eshop_refund refund
        LEFT JOIN base_otype o
        ON o.profile_id = refund.profile_id
        AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_receive_checkin checkin
        ON refund.profile_id = checkin.profile_id
        AND refund.id = checkin.refund_order_id
        LEFT JOIN pl_eshop_refund_timing timing
        ON refund.profile_id = timing.profile_id
        AND refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
        <if test="request.ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{request.etypeId}
        </if>
        <if test="request.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{request.etypeId}
        </if>
        WHERE refund.profile_id = #{request.profileId}
        AND o.ocategory != 2
        AND refund.no_detail = 0
        <include refid="queryTimeTypeSqlForRefund"/>
        AND refund.refund_type IN (0, 1, 2, 3, 4, 5, 6)
        AND refund.refund_state not in (0,4,6)
        AND refund.deleted = 0
        AND ((DATE_SUB(timing.sys_promised_confirm_time, INTERVAL 2 HOUR) &lt; now() and now() &lt;
        timing.sys_promised_confirm_time
        AND refund.confirm_state = 0)
        OR (DATE_SUB(timing.promised_receive_time, INTERVAL 2 HOUR) &lt; now() and now() &lt;
        timing.promised_receive_time
        AND refund.receive_state = 1
        AND if(checkin.checkin_number IS NOT NULL, 1, 0) = 0
        AND refund.refund_type != 1
        AND refund.refund_type != 5)
        OR (DATE_SUB(timing.promised_confirm_time, INTERVAL 2 HOUR) &lt; now() and now() &lt;
        timing.promised_confirm_time
        AND refund.platform_refund_state = 0
        AND refund.refund_type != 2
        AND refund.refund_type != 3
        AND refund.refund_type != 4)
        OR (DATE_SUB(timing.promised_deliver_time, INTERVAL 2 HOUR) &lt; now() and now() &lt;
        timing.promised_deliver_time
        AND refund.refund_process_state = 0)
        OR (DATE_SUB(timing.promised_agree_time, INTERVAL 2 HOUR) &lt; now() and now() &lt; timing.promised_agree_time))

    </select>
    <select id="getNotAuditRefundBillCount"
            resultType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsDAO"
            parameterType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsRequest">
        SELECT refund.otype_id, refund.id as 'warehouseTaskId', refund.ktype_id
        FROM pl_eshop_refund refund
        LEFT JOIN base_otype o
        ON o.profile_id = refund.profile_id
        AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_receive_checkin checkin
        ON refund.profile_id = checkin.profile_id
        AND refund.id = checkin.refund_order_id
        LEFT JOIN pl_eshop_refund_timing timing
        ON refund.profile_id = timing.profile_id
        AND refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
        <if test="request.ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{request.etypeId}
        </if>
        <if test="request.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{request.etypeId}
        </if>

        WHERE refund.profile_id = #{request.profileId}
        AND o.ocategory != 2
        AND refund.no_detail = 0
        <include refid="queryTimeTypeSqlForRefund"/>
        AND refund.refund_type IN (0, 1, 2, 3, 4, 6)
        AND refund.refund_state not in (0,4,6)
        AND refund.deleted = 0
        AND refund.confirm_state = 0
    </select>
    <select id="getAuditRefundBillCount" resultType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsDAO"
            parameterType="com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsRequest">
        SELECT refund.otype_id, refund.id as 'warehouseTaskId', refund.ktype_id
        FROM pl_eshop_refund refund
        LEFT JOIN base_otype o
        ON o.profile_id = refund.profile_id
        AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_receive_checkin checkin
        ON refund.profile_id = checkin.profile_id
        AND refund.id = checkin.refund_order_id
        LEFT JOIN pl_eshop_refund_timing timing
        ON refund.profile_id = timing.profile_id
        AND refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
        <if test="request.ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{request.etypeId}
        </if>
        <if test="request.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{request.etypeId}
        </if>
        WHERE refund.profile_id = #{request.profileId}
        AND o.ocategory != 2
        AND refund.no_detail = 0
        <include refid="queryTimeTypeSqlForRefund"/>
        AND refund.refund_type IN (0, 1, 2, 3, 4, 5, 6)
        AND refund.refund_state not in (0,4,6)
        AND refund.deleted = 0
        AND refund.confirm_state != 0
    </select>

    <select id="getOnlineAddressId" resultType="java.lang.String">
        select online_address_id
        from pl_eshop_address_mapping
        where profile_id = #{profileId}
          and refund_address_id = #{addressId}
        limit 1
    </select>

    <select id="quereyRefundConfigDefaultReason"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        select *
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and deleted = 0
          and custome_default_type = true
    </select>
    <select id="getRelationCount" resultType="java.lang.Integer"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select count(*)
        from td_bill_relation
        where source_vchcode = #{id}
          and profile_id = #{profileId}
          and source_vchtype = 9801
          and target_vchtype = 9802
    </select>
    <select id="getHistoryRefundNumbers" resultType="java.lang.String"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetHistoryRefundNumbersRequest">
        select trade_refund_order_number
        from pl_eshop_refund
        where profile_id = #{profileId}
          and trade_order_id = #{tradeOrderId}
          and deleted = 0
          and refund_state != 6
          and no_detail = 0
    </select>
    <select id="getRefundDuty" resultType="com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty"
            parameterType="java.math.BigInteger">
        select *
        from pl_eshop_refund_duty
        where profile_id = #{profileId}
    </select>
    <select id="getRefundDuties" resultType="java.lang.String">
        SELECT GROUP_CONCAT(refund_duty) FROM pl_eshop_refund_duty where profile_id = #{profileId} and id in
        <foreach item="id" collection="refundDutyIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getRefundDutyIdsByName" resultType="java.math.BigInteger">
        SELECT id
        FROM pl_eshop_refund_duty
        where profile_id = #{profileId}
          and refund_duty like CONCAT('%', #{refundDuty}, '%')
    </select>
    <select id="getTiming" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundTiming"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select *
        from pl_eshop_refund_timing
        where profile_id = #{profileId}
          and refund_id = #{id}
    </select>
    <select id="getPostedTradeId" resultType="java.lang.String">
        select distinct bdd.trade_order_id
        from td_bill_core core
        join td_bill_detail_deliver bdd on core.profile_id = bdd.profile_id and core.vchcode = bdd.vchcode
        where core.profile_id = #{profileId}
        and bdd.trade_order_id in
        <foreach collection="tradeIdList" close=")" open="(" separator="," item="tid">
            #{tid}
        </foreach>
        union
        select distinct bdd.trade_order_id
        from acc_bill_core core
        join acc_bill_detail_deliver bdd on core.profile_id = bdd.profile_id and core.vchcode = bdd.vchcode
        where core.profile_id = #{profileId}
        and bdd.trade_order_id in
        <foreach collection="tradeIdList" close=")" open="(" separator="," item="tid">
            #{tid}
        </foreach>
    </select>

    <select id="getRefundIdsBySourceDetailId" resultType="java.math.BigInteger">
        select refund.id
        from pl_eshop_refund refund
        left join pl_eshop_refund_apply_detail detail on detail.refund_order_id = refund.id and detail.profile_id =
        refund.profile_id
        where detail.source_detail_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        and refund.profile_id = #{profileId}
    </select>
    <select id="getEtypeIdsByEtypeName" resultType="java.math.BigInteger">
        select id
        from base_etype
        where profile_id = #{profileId}
          and fullname like CONCAT('%', #{fullname}, '%')
    </select>

    <!--<insert id="addRefundDuty" parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty">
        insert into pl_eshop_refund_duty(id, profile_id, refund_duty, create_time, update_time)
        values (#{id}, #{profileId}, #{refundDuty}, #{createTime}, #{updateTime})
        on duplicate key update refund_duty =#{refundDuty}
    </insert>
    <select id="getRefundDuty" resultType="com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty" parameterType="java.math.BigInteger">
        select *
        from pl_eshop_refund_duty
        where profile_id = #{profileId}
    </select>
    <select id="getRefundDuties" resultType="java.lang.String">
        SELECT GROUP_CONCAT(refund_duty) FROM pl_eshop_refund_duty where profile_id = #{profileId} and id in
        <foreach item="id" collection="refundDutyIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getRefundDutyIdsByName" resultType="java.math.BigInteger">
        SELECT id
        FROM pl_eshop_refund_duty
        where profile_id = #{profileId}
          and refund_duty like CONCAT('%', #{refundDuty}, '%')
    </select>-->
    <select id="getRefundDutyByIds" resultType="java.lang.String"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.UpdateRefundDutyRequest">
        select refund_duty
        from pl_eshop_refund_duty
        where profile_id = #{profileId}
        and id in
        <foreach item="id" collection="refundDutyIdsList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getRefundDutyNameById" resultType="java.lang.String">
        select refund_duty
        from pl_eshop_refund_duty
        where profile_id = #{profileId}
          and id = #{id}
    </select>
    <select id="dutyBeUsed" resultType="java.lang.Integer"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty">
        select count(1)
        from pl_eshop_refund
        where profile_id = #{profileId}
          and refund_duty_ids like CONCAT('%', #{id}, '%')
    </select>
    <select id="hasOtherBill" resultType="java.lang.Boolean">
        select count(*) > 0
        from td_bill_relation
        where source_vchcode = #{id}
          and profile_id = #{profileId}
          and target_vchcode != #{vchcode}
          and target_vchtype = 2100
          and source_vchtype = 9801;
    </select>
    <select id="getRefundReasonList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        select * from pl_eshop_refund_config_reason where profile_id = #{profileId} and refund_reason != '' and id in
        <foreach item="id" collection="sysRefundReasonIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getRefundReasonIdList" resultType="java.math.BigInteger">
        select sys_refund_reason_id
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and id = #{id}
    </select>
    <select id="getSaleBackBillVchcode" resultType="java.math.BigInteger">
        select vchcode
        from td_bill_core
        where vchcode in (select target_vchcode
                          from td_bill_relation
                          where source_vchcode = #{id}
                            and profile_id = #{profileId}
                            and target_vchtype = 2100
                            and source_vchtype = 9801)
          and profile_id = #{profileId}
        union
        select vchcode
        from acc_bill_core
        where vchcode in (select target_vchcode
                          from td_bill_relation
                          where source_vchcode = #{id}
                            and profile_id = #{profileId}
                            and target_vchtype = 2100
                            and source_vchtype = 9801)
          and profile_id = #{profileId}
    </select>
    <select id="getRefundDutyEntityByIds" resultType="com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty">
        select *
        from pl_eshop_refund_duty
        where profile_id = #{profileId}
        and id in
        <foreach item="id" collection="refundDutyIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getSourceDetailIds" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select source_detail_id
        from pl_eshop_refund_apply_detail
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </select>

    <select id="getSourceDetailIdsBatch" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select source_detail_id
        from pl_eshop_refund_apply_detail
        where refund_order_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and profile_id = #{profileId}
    </select>


    <select id="getRefundReasonIdListForSys" resultType="java.math.BigInteger">
        select id
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and sys_refund_reason_id = #{id}
    </select>
    <select id="checkRefundReasonRelated" resultType="java.lang.Boolean">
        select count(0) > 0
        from pl_eshop_refund_config_reason
        where sys_refund_reason_id = #{id}
          and profile_id = #{profileId}
    </select>
    <select id="getPayBillVchcode" resultType="java.math.BigInteger">
        select vchcode
        from td_bill_core
        where vchcode = (select target_vchcode
                         from td_bill_relation
                         where source_vchcode = #{id}
                           and profile_id = #{profileId}
                           and target_vchtype = 4002
                           and source_vchtype = 9801)
          and profile_id = #{profileId}
        union
        select vchcode
        from acc_bill_core
        where vchcode = (select target_vchcode
                         from td_bill_relation
                         where source_vchcode = #{id}
                           and profile_id = #{profileId}
                           and target_vchtype = 4002
                           and source_vchtype = 9801)
          and profile_id = #{profileId}
    </select>
    <select id="getOtypdIdByRefundId" resultType="java.math.BigInteger">
        select otype_id
        from pl_eshop_refund
        where profile_id = #{profileId}
          and id = #{id}
    </select>

    <select id="getCheckinIdsByRefund" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select checkin_id
        from pl_eshop_refund_checkin_relation
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </select>

    <insert id="addRefundDuty" parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty">
        insert into pl_eshop_refund_duty(id, profile_id, refund_duty, create_time, update_time)
        values (#{id}, #{profileId}, #{refundDuty}, #{createTime}, #{updateTime})
        on duplicate key
            update refund_duty =#{refundDuty}
    </insert>

    <insert id="insertRefundInfoExtend"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        insert into pl_eshop_refund_extend(
        refund_order_id, profile_id, platform_refund_version, create_time,
        update_time, creator_id, platform_auto_intercept_agree, ag_refund,
        order_detail_total,platform_refund_reason,
        platform_refund_type,order_type,platform_sign_status,refund_order_detail_summary,cycle
        ,refund_advance_total,refund_save_present_total,refund_save_principal_total,refund_save_total
        ,refund_national_subsidy_total,refund_buyer_apply_total,refund_platform_amount,original_refund_platform_amount,original_refund_national_subsidy_total,refund_pay_type
        ,system_refund_state
        )
        values (#{id}, #{profileId}, #{platformRefundVersion}, #{createTime}, #{updateTime}, #{creatorId},
        #{platformAutoInterceptAgree}, #{agRefund},
        #{orderDetailTotal},#{platformRefundReason},
        #{platformRefundType},#{orderType},#{platformSignStatus},#{refundOrderDetailSummaryId},#{cycle},
        #{refundAdvanceTotal},#{refundSavePresentTotal},#{refundSavePrincipalTotal},#{refundSaveTotal}
        ,#{refundNationalSubsidyTotal},#{refundBuyerApplyTotal},#{refundPlatformAmount},#{originalRefundPlatformAmount},#{originalRefundNationalSubsidyTotal},#{refundPayType}
        ,#{systemRefundState}
        )
        on duplicate key update
        platform_refund_version =#{platformRefundVersion},
        platform_auto_intercept_agree=#{platformAutoInterceptAgree},
        ag_refund = #{agRefund},
        order_detail_total = #{orderDetailTotal},
        platform_refund_type = #{platformRefundType},
        cycle = #{cycle},
        refund_advance_total=#{refundAdvanceTotal},
        refund_save_present_total=#{refundSavePresentTotal},
        refund_save_principal_total=#{refundSavePrincipalTotal},
        refund_save_total=#{refundSaveTotal}
        <if test="originalRefundPlatformAmount!=null">
            ,original_refund_platform_amount = #{originalRefundPlatformAmount}
        </if>
        <if test="originalRefundNationalSubsidyTotal!=null">
            ,original_refund_national_subsidy_total = #{originalRefundNationalSubsidyTotal}
        </if>
        <if test="refundNationalSubsidyTotal!=null">
            ,refund_national_subsidy_total = #{refundNationalSubsidyTotal}
        </if>
        <if test="refundBuyerApplyTotal!=null">
            ,refund_buyer_apply_total = #{refundBuyerApplyTotal}
        </if>
        <if test="refundPlatformAmount!=null">
            ,refund_platform_amount = #{refundPlatformAmount}
        </if>
        <if test="platformRefundReason!=null and platformRefundReason!=0">
            ,platform_refund_reason = #{platformRefundReason}
        </if>
        <if test="platformSignStatus!=null">
            ,platform_sign_status = #{platformSignStatus}
        </if>
        <if test="refundAdvanceTotal!=null">
            ,refund_advance_total = #{refundAdvanceTotal}
        </if>
        <if test="refundPayType!=null">
            ,refund_pay_type = #{refundPayType}
        </if>
        <if test="refundOrderDetailSummary!=null">
            ,refund_order_detail_summary = #{refundOrderDetailSummaryId}
        </if>
        <if test="systemRefundState!=null">
            ,system_refund_state = #{systemRefundState}
        </if>
    </insert>
    <insert id="insertEshopRefundCheckinRelation"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundCheckinRelation">
        insert into pl_eshop_refund_checkin_relation(id, profile_id, refund_order_id, refund_detaild_id, checkin_id,
                                                     checkin_detail_id, actual_received_qty, relation_type,
                                                     create_time, update_time)
        values (#{id}, #{profileId}, #{refundOrderId}, #{refundDetaildId}, #{checkinId}, #{checkinDetailId},
                #{actualReceivedQty}, #{relationType}, now(), now());
    </insert>
    <insert id="insertEshopRefundCheckinRelationBatch"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundCheckinRelation">
        insert into pl_eshop_refund_checkin_relation(id, profile_id, refund_order_id, refund_detaild_id, checkin_id,
        checkin_detail_id, actual_received_qty, relation_type,
        create_time, update_time)
        values
        <foreach item="item" collection="list" open="(" separator="," close=")">
            (#{item.id}, #{item.profileId}, #{item.refundOrderId}, #{item.refundDetaildId}, #{item.checkinId}, #{item.checkinDetailId},
            #{item.actualReceivedQty}, #{item.relationType}, now(), now());
        </foreach>
    </insert>

    <insert id="batchInsertPlEshopRefundDistributionBuyer">
        insert into pl_eshop_refund_distribution_buyer(refund_order_id, profile_id, buyer_total,
        buyer_dised_initial_total, buyer_dised_taxed_total, buyer_tax_total,
        buyer_dised_total, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.refundOrderId}, #{item.profileId}, #{item.buyerTotal}, #{item.buyerDisedInitialTotal},
            #{item.buyerDisedTaxedTotal}, #{item.buyerTaxTotal},
            #{item.buyerDisedTotal}, #{item.createTime}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        buyer_total = VALUES(buyer_total),
        buyer_dised_initial_total = VALUES(buyer_dised_initial_total),
        buyer_dised_taxed_total = VALUES(buyer_dised_taxed_total),
        buyer_tax_total = VALUES(buyer_tax_total),
        buyer_dised_total = VALUES(buyer_dised_total)
    </insert>
    <insert id="batchInsertPlEshopRefundApplyDetailDistributionBuyer">
        insert into pl_eshop_refund_apply_detail_distribution_buyer(refund_order_detail_id, profile_id, refund_order_id,
        buyer_price, buyer_total, buyer_dised_initial_price,
        buyer_dised_initial_total, buyer_dised_taxed_price, buyer_dised_taxed_total, buyer_tax_rate, buyer_tax_total,
        buyer_dised_price, buyer_dised_total, update_time, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.refundOrderDetailId},#{item.profileId},#{item.refundOrderId},#{item.buyerPrice},#{item.buyerTotal},#{item.buyerDisedInitialPrice},#{item.buyerDisedInitialTotal},
            #{item.buyerDisedTaxedPrice},#{item.buyerDisedTaxedTotal},#{item.buyerTaxRate},#{item.buyerTaxTotal},#{item.buyerDisedPrice},#{item.buyerDisedTotal},#{item.updateTime},
            #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        buyer_price = values(buyer_price),
        buyer_total = values(buyer_total),
        buyer_dised_initial_price = values(buyer_dised_initial_price),
        buyer_dised_initial_total = values(buyer_dised_initial_total),
        buyer_dised_taxed_price = values(buyer_dised_taxed_price),
        buyer_dised_taxed_total = values(buyer_dised_taxed_total),
        buyer_tax_rate = values(buyer_tax_rate),
        buyer_tax_total = values(buyer_tax_total),
        buyer_dised_price = values(buyer_dised_price),
        buyer_dised_total = values(buyer_dised_total)
    </insert>

    <insert id="batchInsertPlEshopRefundSendDetailDistributionBuyer">
        insert into pl_eshop_refund_send_detail_distribution_buyer(refund_order_detail_id, profile_id, refund_order_id,
        buyer_price, buyer_total, buyer_dised_initial_price,
        buyer_dised_initial_total, buyer_dised_taxed_price, buyer_dised_taxed_total, buyer_tax_rate, buyer_tax_total,
        buyer_dised_price, buyer_dised_total, update_time, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.refundOrderDetailId},#{item.profileId},#{item.refundOrderId},#{item.buyerPrice},#{item.buyerTotal},#{item.buyerDisedInitialPrice},#{item.buyerDisedInitialTotal},
            #{item.buyerDisedTaxedPrice},#{item.buyerDisedTaxedTotal},#{item.buyerTaxRate},#{item.buyerTaxTotal},#{item.buyerDisedPrice},#{item.buyerDisedTotal},#{item.updateTime},
            #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        buyer_price = values(buyer_price),
        buyer_total = values(buyer_total),
        buyer_dised_initial_price = values(buyer_dised_initial_price),
        buyer_dised_initial_total = values(buyer_dised_initial_total),
        buyer_dised_taxed_price = values(buyer_dised_taxed_price),
        buyer_dised_taxed_total = values(buyer_dised_taxed_total),
        buyer_tax_rate = values(buyer_tax_rate),
        buyer_tax_total = values(buyer_tax_total),
        buyer_dised_price = values(buyer_dised_price),
        buyer_dised_total = values(buyer_dised_total)
    </insert>
    <insert id="batchInsertPlEshopRefundSendDetailComboDistributionBuyer">
        insert into pl_eshop_refund_send_detail_combo_distribution_buyer(refund_order_combo_detail_id, profile_id,
        refund_order_id, buyer_price, buyer_total,
        buyer_dised_initial_price, buyer_dised_initial_total, buyer_dised_taxed_price, buyer_dised_taxed_total,
        buyer_tax_rate, buyer_tax_total, buyer_dised_price, buyer_dised_total, update_time, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.refundOrderComboDetailId}, #{item.profileId}, #{item.refundOrderId}, #{item.buyerPrice},
            #{item.buyerTotal},#{item.buyerDisedInitialPrice},
            #{item.buyerDisedInitialTotal},#{item.buyerDisedTaxedPrice},
            #{item.buyerDisedTaxedTotal},#{item.buyerTaxRate}, #{item.buyerTaxTotal}, #{item.buyerDisedPrice},
            #{item.buyerDisedTotal}, #{item.updateTime},#{item.createTime})
        </foreach>
    </insert>

    <insert id="batchInsertPlEshopRefundApplyDetailComboDistributionBuyer">
        insert into pl_eshop_refund_apply_detail_combo_distribution_buyer(refund_order_combo_detail_id, profile_id,
        refund_order_id, buyer_price, buyer_total,
        buyer_dised_initial_price, buyer_dised_initial_total, buyer_dised_taxed_price, buyer_dised_taxed_total,
        buyer_tax_rate, buyer_tax_total, buyer_dised_price, buyer_dised_total, update_time, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.refundOrderComboDetailId}, #{item.profileId}, #{item.refundOrderId}, #{item.buyerPrice},
            #{item.buyerTotal},#{item.buyerDisedInitialPrice},
            #{item.buyerDisedInitialTotal},#{item.buyerDisedTaxedPrice},
            #{item.buyerDisedTaxedTotal},#{item.buyerTaxRate}, #{item.buyerTaxTotal}, #{item.buyerDisedPrice},
            #{item.buyerDisedTotal}, #{item.updateTime},#{item.createTime})
        </foreach>
    </insert>
    <insert id="saveOrUpdateRefundSnapshot"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntitySnapshot">
        insert into pl_eshop_refund_snapshot(profile_id, refund_order_id, refund_type, refund_reason,
                                             refund_apply_total, apply_detail_md5, send_detail_md5, create_time,
                                             update_time, refund_md5)
        values (#{profileId}, #{refundOrderId}, #{refundType}, #{refundReason}, #{refundApplyTotal}, #{applyDetailMd5},
                #{sendDetailMd5}, #{createTime}, #{updateTime}, #{refundMd5})
        ON duplicate key
            update refund_type        = #{refundType},
                   refund_reason      = #{refundReason},
                   refund_apply_total = #{refundApplyTotal},
                   apply_detail_md5   = #{applyDetailMd5},
                   send_detail_md5    = #{sendDetailMd5},
                   refund_md5         = #{refundMd5}
    </insert>
    <insert id="insertChangeInfo"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntityChangeInfo">
        insert into pl_eshop_refund_order_change_info(id, profile_id, refund_order_id, change_type, data_id, otype_id,
                                                      sub_change_type, unique_key, create_time, update_time)
        values (#{id}, #{profileId}, #{refundOrderId}, #{changeType}, #{dataId}, #{otypeId}, #{subChangeType},
                #{uniqueKey}, NOW(), NOW())
    </insert>
    <insert id="insertChangeInfoBatch">
        insert into pl_eshop_refund_order_change_info(id, profile_id, refund_order_id, change_type, data_id, otype_id,
        sub_change_type, unique_key, create_time, update_time)
        values
        <foreach item="item" index="index" collection="changeInfoList" separator=",">
            (#{item.id}, #{item.profileId}, #{item.refundOrderId}, #{item.changeType}, #{item.dataId}, #{item.otypeId},
            #{item.subChangeType}, #{item.uniqueKey}, NOW(), NOW())
        </foreach>
    </insert>
    <insert id="insertRefundIndex">
        insert pl_eshop_refund_order_index(id, profile_id, otype_id, refund_order_id, ktype_id, order_type, create_time,
                                           update_time, index_hash, bill_vchcode)
        values (#{id}, #{profileId}, #{otypeId}, #{refundOrderId}, #{ktypeId}, #{refundProduceOrderTypeEnum},
                #{createTime}, #{updateTime}, #{indexHash}, #{billVchcode})

    </insert>
    <insert id="insertRefundDetailsExtend">
        INSERT INTO pl_eshop_refund_apply_detail_extend
        (refund_detail_id, profile_id, refund_save_total, refund_save_principal_total, refund_save_present_total, eshop_order_id,
        refund_order_id,refund_preference_allot_total,refund_preference_total)
        VALUES
        <foreach collection="details" index="index" item="item" open=" " separator="," close=" ">
            (#{item.id},#{item.profileId},#{item.refundSaveTotal},#{item.refundSavePrincipalTotal},#{item.refundSavePresentTotal},#{item.eshopOrderId}
            ,#{item.refundOrderId},#{item.refundPreferenceAllotTotal},#{item.refundPreferenceTotal})
        </foreach>
    </insert>

    <update id="saveRelateReason"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        update pl_eshop_refund_config_reason
        set sys_refund_reason_id = #{id}
        where profile_id= #{profileId} and id in
        <foreach item="id" collection="sysRefundReasonIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="clearRelateReason"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        update pl_eshop_refund_config_reason
        set sys_refund_reason_id = 0
        where profile_id = #{profileId}
          and sys_refund_reason_id = #{id}
    </update>
    <update id="updateRefundExtend" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        update pl_eshop_refund_extend
        set creator_id       = #{creatorId},
            process_etype_id = #{creatorId}
        where refund_order_id = #{id}
    </update>
    <update id="updateRefundForPay" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        UPDATE pl_eshop_refund
        SET pay_state=#{payState},
            bill_poseted = #{billPoseted},
            pay_time = now()
        WHERE id = #{id}
          and profile_id = #{profileId};
    </update>
    <update id="updateCheckinDetail">
        update pl_eshop_refund_receive_checkin_detail
        set source_vchcode   = #{detail.sourceVchcode},
            source_detail_id = #{detail.sourceDetailId}
        where id = #{vo.checkInDetailId}
          and profile_id = #{vo.profileId}
    </update>
    <update id="updateRefundCombo">

    </update>

    <select id="getReportDataForRefundType"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundReportDomain">
        select count(id) as value, refund_type as name
        from pl_eshop_refund
        where profile_id = #{profileId} and deleted = 0 and refund_state != 4 and refund_state != 6 and no_detail = 0
        <if test="otypeId !=null and  otypeId.size() > 0">
            and otype_id in
            <foreach item="id" collection="otypeId" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and create_time &lt;= #{endTime}
        </if>
        group by refund_type
    </select>
    <select id="getReportDataForRefundReason"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundReportDomain"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest">
        select count(refund.id) as value,ifnull(reason2.refund_reason, reason1.refund_reason) as name
        from pl_eshop_refund refund
        left join pl_eshop_refund_config_reason reason1 on refund.profile_id = reason1.profile_id and
        refund.refund_reason = reason1.id
        left join pl_eshop_refund_config_reason reason2 on reason1.profile_id = reason2.profile_id and
        reason1.sys_refund_reason_id = reason2.id
        where refund.profile_id = #{profileId} and refund.deleted = 0 and refund.refund_state != 4 and
        refund.refund_state != 6 and no_detail = 0
        <if test="otypeId !=null and  otypeId.size() > 0">
            and refund.otype_id in
            <foreach item="id" collection="otypeId" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and refund.create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and refund.create_time &lt;= #{endTime}
        </if>
        group by ifnull(reason2.refund_reason, reason1.refund_reason);
    </select>
    <select id="getAlreadyNumber" resultType="java.lang.String"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest">
        select count(refund.id)
        from pl_eshop_refund refund
        LEFT JOIN base_otype o
        ON o.profile_id = refund.profile_id
        AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_timing timing
        ON refund.profile_id = timing.profile_id
        AND refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
        WHERE refund.profile_id = #{profileId}
        AND o.ocategory != 2
        AND refund.no_detail = 0
        AND refund.refund_state IN (1, 2, 3, 5)
        AND refund.deleted = 0
        and (
        (now() > timing.sys_promised_confirm_time and refund.confirm_state = 0 and refund.refund_type!=5
        and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state
        != 6)
        or
        (now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type!=0 and
        refund.refund_type!=5
        and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state
        != 6)
        or
        (now() > timing.promised_confirm_time AND refund.refund_type != 2 and refund.refund_type != 3 and
        refund.refund_type != 4 and refund.create_type = 1
        and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state
        != 6)
        or
        (now() > timing.promised_deliver_time and refund.refund_process_state = 0
        and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state
        != 6)
        or
        (now() > timing.promised_agree_time AND refund.refund_type != 2 AND refund.refund_type!=0 and
        refund.refund_type!=5 and refund.refund_type!=3 and refund.refund_type!=4 and
        refund.create_type = 1 and refund.refund_state = 1 and refund.refund_state != 0 AND refund.refund_state != 4 AND
        refund.refund_state != 5 AND refund.refund_state != 6 AND
        refund.platform_return_state = 0)

        )
        <if test="otypeId !=null and  otypeId.size() > 0">
            and refund.otype_id in
            <foreach item="id" collection="otypeId" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and refund.create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and refund.create_time &lt;= #{endTime}
        </if>
    </select>
    <select id="getAlmostNumber" resultType="java.lang.String"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest">
        SELECT COUNT(refund.id)
        FROM pl_eshop_refund refund
        LEFT JOIN base_otype o
        ON o.profile_id = refund.profile_id
        AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_timing timing
        ON refund.profile_id = timing.profile_id
        AND refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
        WHERE refund.profile_id = #{profileId}
        AND o.ocategory != 2
        AND refund.no_detail = 0
        AND refund.refund_state IN (1, 2, 3, 5)
        AND refund.deleted = 0
        AND ((DATE_SUB(timing.sys_promised_confirm_time, INTERVAL 2 HOUR) &lt; now() and now() &lt;
        timing.sys_promised_confirm_time
        AND refund.confirm_state = 0)
        OR (DATE_SUB(timing.promised_receive_time, INTERVAL 2 HOUR) &lt; now() and now() &lt;
        timing.promised_receive_time
        AND refund.receive_state = 1
        AND refund.refund_type != 1
        AND refund.refund_type != 5)
        OR (DATE_SUB(timing.promised_confirm_time, INTERVAL 2 HOUR) &lt; now() and now() &lt;
        timing.promised_confirm_time
        AND refund.platform_refund_state = 0
        AND refund.refund_type != 2
        AND refund.refund_type != 3
        AND refund.refund_type != 4)
        OR (DATE_SUB(timing.promised_deliver_time, INTERVAL 2 HOUR) &lt; now() and now() &lt;
        timing.promised_deliver_time
        AND refund.refund_process_state = 0)
        OR (DATE_SUB(timing.promised_agree_time, INTERVAL 2 HOUR) &lt; now() and now() &lt; timing.promised_agree_time))
        <if test="otypeId !=null and  otypeId.size() > 0">
            and refund.otype_id in
            <foreach item="id" collection="otypeId" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and refund.create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and refund.create_time &lt;= #{endTime}
        </if>
    </select>
    <select id="getNoneNumber" resultType="java.lang.String"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest">
        select count(refund.id)
        from pl_eshop_refund refund
        LEFT JOIN base_otype o
        ON o.profile_id = refund.profile_id
        AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_timing timing
        ON refund.profile_id = timing.profile_id
        AND refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
        WHERE refund.profile_id = #{profileId}
        AND o.ocategory != 2
        AND refund.no_detail = 0
        AND refund.refund_state IN (1, 2, 3, 5)
        AND refund.deleted = 0
        and
        !(now() > timing.sys_promised_confirm_time and refund.confirm_state = 0 and refund.refund_type!=5
        and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state
        != 6)
        and
        !(now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type!=0 and
        refund.refund_type!=5
        and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state
        != 6)
        and
        !(now() > timing.promised_confirm_time AND refund.refund_type != 2 and refund.refund_type != 3 and
        refund.refund_type != 4 and refund.create_type = 1
        and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state
        != 6)
        and
        !(now() > timing.promised_deliver_time and refund.refund_process_state = 0
        and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND refund.refund_state
        != 6)
        and
        !(now() > timing.promised_agree_time AND refund.refund_type != 2 AND refund.refund_type!=0 and
        refund.refund_type!=5 and refund.refund_type!=3 and refund.refund_type!=4 and
        refund.create_type = 1 and refund.refund_state = 1 and refund.refund_state != 0 AND refund.refund_state != 4 AND
        refund.refund_state != 5 AND refund.refund_state != 6 AND
        refund.platform_return_state = 0)
        <if test="otypeId !=null and  otypeId.size() > 0">
            and refund.otype_id in
            <foreach item="id" collection="otypeId" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and refund.create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and refund.create_time &lt;= #{endTime}
        </if>
    </select>
    <select id="getReportDataForRefundDuty" resultType="java.lang.String"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest">
        select refund.refund_duty_ids
        from pl_eshop_refund refund
        left join pl_eshop_refund_config_reason reason on refund.profile_id = reason.profile_id and refund.refund_reason
        = reason.id
        where refund.profile_id = #{profileId} and refund.refund_duty_ids!=''
        and refund.deleted = 0 and refund.refund_state != 4 and refund.refund_state != 6 and no_detail = 0
        <if test="otypeId !=null and  otypeId.size() > 0">
            and refund.otype_id in
            <foreach item="id" collection="otypeId" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and refund.create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and refund.create_time &lt;= #{endTime}
        </if>
    </select>
    <select id="getReturnListByQty" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundReportDomain"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest">
        select ROUND(sum(detail.apply_refund_unit_qty),0) as value, ptype.fullname as name
        from pl_eshop_refund refund
        left join pl_eshop_refund_apply_detail detail on refund.profile_id = detail.profile_id and refund.id =
        detail.refund_order_id
        left join base_ptype ptype on ptype.profile_id = detail.profile_id and ptype.id = detail.ptype_id
        where refund.profile_id = #{profileId} and detail.ptype_id is not null and detail.ptype_id != 0
        and refund.deleted = 0 and refund.confirm_state != 2 and refund.refund_state != 4 and refund.refund_state != 6
        <if test="otypeId !=null and  otypeId.size() > 0">
            and refund.otype_id in
            <foreach item="id" collection="otypeId" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and refund.create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and refund.create_time &lt;= #{endTime}
        </if>
        group by detail.ptype_id
        order by ROUND(sum(detail.apply_refund_unit_qty),0) desc limit 20
    </select>
    <select id="getReturnListByTotal" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundReportDomain"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest">
        select ROUND(sum(detail.apply_refund_taxed_total),2) as value, ptype.fullname as name
        from pl_eshop_refund refund
        left join pl_eshop_refund_apply_detail detail on refund.profile_id = detail.profile_id and refund.id =
        detail.refund_order_id
        left join base_ptype ptype on ptype.profile_id = detail.profile_id and ptype.id = detail.ptype_id
        where refund.profile_id = #{profileId} and detail.ptype_id is not null and detail.ptype_id != 0
        and refund.deleted = 0 and refund.confirm_state != 2 and refund.refund_state != 4 and refund.refund_state != 6
        <if test="otypeId !=null and  otypeId.size() > 0">
            and refund.otype_id in
            <foreach item="id" collection="otypeId" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and refund.create_time &gt;= #{beginTime}
        </if>
        <if test="endTime!=null">
            and refund.create_time &lt;= #{endTime}
        </if>
        group by detail.ptype_id
        order by sum(detail.apply_refund_taxed_total) desc limit 20
    </select>
    <select id="getBtypeIdByOtypeId" resultType="java.math.BigInteger">
        select btype_id
        from base_otype
        where id = #{otypeId}
          and profile_id = #{profileId}
    </select>
    <select id="getRefundConfigReasonListNew"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason">
        select *
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and deleted = 0
          and stoped = 0
          and (reason_type in (0, 2) or id = #{reasonId})
        order by create_time
    </select>
    <select id="getPlatformStoreIdByKtypeId" resultType="java.lang.String">
        select platform_store_stock_id
        from pl_eshop_platform_store_mapping
        where ktype_id = #{ktypeId}
          and profile_id = #{profileId}
          and eshop_id = #{otypeId}
        limit 1
    </select>

    <select id="queryDeliverByTradeId" resultType="com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO">
        select tbc.vchcode,
               tbd.trade_order_id,
               tbc.post_state,
               tbc.otype_id,
               tbc.post_failure,
               tbc.profile_id,
               tbc.deliver_process_type as eshopDeliverProcessType
        from td_bill_core tbc
                 left join td_bill_deliver_state tbds on tbc.profile_id = tbds.profile_id and tbc.vchcode = tbds.vchcode
                 left join td_bill_deliver tbd on tbc.profile_id = tbd.profile_id and tbc.vchcode = tbd.vchcode
        where tbc.profile_id = #{profileId}
          and tbc.otype_id = #{otypeId}
          and tbc.deleted IN (0, 2, 3, 4)
          and tbc.vchcode in (SELECT DISTINCT(vchcode)
                              FROM td_bill_detail_deliver
                              WHERE profile_id = #{profileId}
                                AND trade_order_id = #{tradeId}
                                and deleted in (0, 2, 3, 4))
    </select>

    <select id="queryFrightPrintState" resultType="java.lang.Boolean">
        select freight_print_state
        from td_deliver_freight_info
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </select>

    <select id="queryWmsSendState" resultType="boolean">
        select sync_status
        from td_wms_bill_core
        where profile_id = #{profileId}
          and order_vchcode = #{vchcode}
    </select>
    <select id="getReceiveBuyer" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer">
        select *
        from pl_buyer
        where profile_id = #{profileId}
          and buyer_id = #{buyerId}
    </select>
    <select id="getRefundDetail" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundDetailAndCheckInVO">
        select *
        from pl_eshop_refund_apply_detail
        WHERE id = #{refundDetailId}
          and profile_id = #{profileId}
    </select>
    <select id="getRefundDetailCombo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetailCombo"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundComboDetailAndCheckInVO">
        select *
        from pl_eshop_refund_apply_detail_combo
        where profile_id = #{profileId}
          and id = #{applyComboId}
    </select>
    <select id="queryCheckinListByRefundId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select *
        from pl_eshop_refund_receive_checkin checkin
                 left join pl_eshop_refund_checkin_relation rel on checkin.vchcode = rel.checkin_id
        where rel.refund_order_id = #{id}
          and checkin.profile_id = #{profileId}
    </select>
    <select id="getCheckinVchcodesByRefundId" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select checkin_id
        from pl_eshop_refund_checkin_relation
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </select>
    <select id="getRefundOrderIdByCheckinVchcode" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        select distinct refund_order_id
        from pl_eshop_refund_checkin_relation
        where profile_id = #{profileId}
          and checkin_id = #{vchcode}
          and relation_type = 0
    </select>
    <select id="getCheckinVchcodebyFreight" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundQueryCheckInListByLogistics">
        select vchcode
        from pl_eshop_refund_receive_checkin
        where profile_id = #{profileId}
          and freight_bill_no = #{freightNo}
          and freight_name = #{freightName}
    </select>
    <select id="getRefundIdByPtypeDetail" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryRefundParameter">
        select distinct refund_order_id from pl_eshop_refund_apply_detail d
        left join pl_eshop_refund refund on d.profile_id = refund.profile_id and d.refund_order_id = refund.id
        where d.profile_id = #{profileId}
        and d.sku_id in
        <foreach item="id" collection="skuIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="timeType != null">
            <choose>
                <when test="timeType.getCode() == 4">
                    AND refund.refund_finish_time &gt;=#{beginTime}
                    AND refund.refund_finish_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 3">
                    AND refund.refund_create_time &gt;=#{beginTime}
                    AND refund.refund_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 2">
                    AND refund.create_time &gt;=#{beginTime}
                    AND refund.create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 0">
                    AND refund.trade_create_time &gt;=#{beginTime}
                    AND refund.trade_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 1">
                    AND refund.trade_pay_time &gt;=#{beginTime}
                    and refund.trade_pay_time &lt;=#{endTime}
                </when>
            </choose>
        </if>
    </select>
    <select id="getRefundIdByPtypeCombo" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryRefundParameter">
        select distinct refund_order_id from pl_eshop_refund_apply_detail_combo d
        left join pl_eshop_refund refund on d.profile_id = refund.profile_id and d.refund_order_id = refund.id
        where d.profile_id = #{profileId}
        and d.combo_id in
        <foreach item="id" collection="skuIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="timeType != null">
            <choose>
                <when test="timeType.getCode() == 4">
                    AND refund.refund_finish_time &gt;=#{beginTime}
                    AND refund.refund_finish_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 3">
                    AND refund.refund_create_time &gt;=#{beginTime}
                    AND refund.refund_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 2">
                    AND refund.create_time &gt;=#{beginTime}
                    AND refund.create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 0">
                    AND refund.trade_create_time &gt;=#{beginTime}
                    AND refund.trade_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 1">
                    AND refund.trade_pay_time &gt;=#{beginTime}
                    and refund.trade_pay_time &lt;=#{endTime}
                </when>
            </choose>
        </if>
    </select>
    <select id="getOtypeTypeByTradeId" resultType="com.wsgjp.ct.common.enums.core.enums.ShopType">
        select eshop.eshop_type
        from pl_eshop_sale_order o
                 left join pl_eshop eshop on eshop.otype_id = o.otype_id and eshop.profile_id = o.profile_id
        where o.trade_order_id = #{tradeId}
          and o.profile_id = #{profileId}
    </select>
    <select id="showDoudianApply" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo"
            parameterType="java.math.BigInteger">
        select *
        from pl_eshop
        where profile_id = #{profileId}
          and eshop_type = 52
          and deleted = 0
    </select>
    <select id="getCheckinKtypeIdByRefundId" resultType="java.math.BigInteger"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT checkin.ktype_id
        FROM pl_eshop_refund_receive_checkin checkin
                 LEFT JOIN pl_eshop_refund_checkin_relation rel on rel.checkin_id = checkin.vchcode
        WHERE checkin.profile_id = #{profileId}
          and rel.refund_order_id = #{id}
    </select>
    <select id="getBtypeIdByBtypeName" resultType="java.math.BigInteger">
        select id
        from base_btype
        where profile_id = #{profileId}
          and fullname = #{freightName}
    </select>
    <select id="getPlatformReason" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundConfigReason"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select *
        from pl_eshop_refund_config_reason
        where profile_id = #{profileId}
          and id = #{platformRefundReason}
    </select>
    <select id="getRefundSnapshot"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntitySnapshot">
        select *
        from pl_eshop_refund_snapshot
        where profile_id = #{profileId}
          and refund_order_id = #{id}
    </select>
    <select id="getChangeInfo" resultType="com.wsgjp.ct.sale.biz.jarvis.entity.BigData">
        select mg.id, mg.big_data as data, mg.profile_id, mg.create_time, mg.update_time
        from pl_eshop_refund_order_change_info ci
        left join mark_data mg on ci.data_id = mg.id and ci.profile_id = mg.profile_id
        where ci.profile_id = #{profileId}
        and ci.refund_order_id = #{id}
        <if test="type!=null">
            and ci.sub_change_type = #{type}
        </if>
        order by mg.create_time desc
    </select>

    <select id="getCheckinDetailCostPrice" resultType="java.math.BigDecimal"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        select if(p.sku_price = 0, p.cost_price, s.cost_price)
        from base_ptype p
                 left join base_ptype_sku s on s.profile_id = p.profile_id and s.ptype_id = p.id and s.id = #{skuId}
        where p.id = #{ptypeId}
          and p.profile_id = #{profileId};
    </select>
    <select id="getCheckinDetailRetailPrice" resultType="java.math.BigDecimal"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        select retail_price
        from base_ptype_price
        where ptype_id = #{ptypeId}
          and profile_id = #{profileId};
    </select>
    <select id="getSysData" resultType="java.lang.String">
        select sub_value
        from sys_data
        where profile_id = #{profileId}
          and sub_name = #{subName}
    </select>
    <select id="getRefundEntityList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryRefundParameter">
        select deleted, confirm_state
        from pl_eshop_refund
        where profile_id = #{profileId}
          and id = #{refundOrderId}
    </select>
    <select id="needNextOperation" resultType="java.lang.String">
        SELECT DISTINCT d.erp_order_code AS source_bill_number
        FROM `pl_eshop_refund` b
                 LEFT JOIN `pl_eshop_refund_apply_detail` f
                           ON b.id = f.refund_order_id
                               AND f.profile_id = b.profile_id
                 INNER JOIN td_wms_bill_core d
                            ON (d.vchcode = f.task_vchcode or d.vchcode = f.source_vchcode)
                                AND d.profile_id = f.profile_id
        WHERE b.profile_id = #{profileId}
          AND b.id = #{vchcode}
        limit 1
    </select>
    <select id="queryInoutByVchcode" resultType="com.wsgjp.ct.bill.core.handle.entity.BillInoutEntity">
        select *
        from acc_bill_inout_record
        where vchcode = #{vchcode}
          and profile_id = #{profileId}
    </select>
    <select id="queryInoutDetailByVchcode" resultType="com.wsgjp.ct.bill.core.handle.entity.BillInoutDetail">
        select *
        from td_bill_inout_detail
        where inout_id = #{inoutId}
          and profile_id = #{profileId}
        UNION
        select *
        from acc_bill_inout_detail
        where inout_id = #{inoutId}
          and profile_id = #{profileId}
    </select>
    <select id="queryInoutDetailComboByVchcode" resultType="com.wsgjp.ct.bill.core.handle.entity.BillInoutDetailCombo">
        select *
        from td_bill_detail_combo
        where vchcode = #{vchcode}
          and profile_id = #{profileId}
        UNION
        select *
        from acc_bill_detail_combo
        where vchcode = #{vchcode}
          and profile_id = #{profileId}
        limit 1
    </select>
    <select id="queryInoutBatchComboByVchcode" resultType="com.wsgjp.ct.bill.core.handle.entity.BillDetailBatch">
        select *
        from acc_bill_detail_batch
        where inout_id = #{inoutId}
          and profile_id = #{profileId}
    </select>
    <select id="queryInoutDetailSerialyVchcode" resultType="com.wsgjp.ct.bill.core.handle.entity.BillSerialno">
        select *
        from acc_bill_detail_serialno
        where inout_id = #{inoutId}
          and profile_id = #{profileId}
    </select>
    <select id="batchNoRequired" resultType="java.lang.Boolean" parameterType="java.math.BigInteger">
        select stoped, setting_value
        from pub_bill_settings
        where profile_id = #{profileId}
          and setting_key = 'batchNoRequired'
        limit 1
    </select>
    <select id="getEshopIds" resultType="java.lang.String" parameterType="java.math.BigInteger">
        select otype_id
        from pl_eshop
        where profile_id = #{profileId}
          and eshop_type in (0, 1)
          and deleted = 0
          and stoped = 0
    </select>
    <select id="queryBillDetailComboByVchcode" resultType="com.wsgjp.ct.bill.core.handle.entity.BillCombo">
        select *
        from acc_bill_detail_combo
        where vchcode = #{vchcode}
          and profile_id = #{profileId}
    </select>
    <select id="queryBillDetailByVchcode" resultType="com.wsgjp.ct.bill.core.handle.entity.BillDetail">
        select *
        from acc_bill_detail_core_sale
        where combo_detail_id = #{id}
          and profile_id = #{profileId}
    </select>
    <select id="getOrderDetail" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail">
        select * from base_ptype_combo_detail
        where profile_id = #{profileId} and combo_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getRefundPlatformType" resultType="com.wsgjp.ct.sale.platform.enums.RefundTypeEnum">
        select platform_refund_type as refundType
        from pl_eshop_refund_extend
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </select>
    <select id="getRefundType" resultType="com.wsgjp.ct.sale.platform.enums.RefundTypeEnum">
        select refund_type as refundTypeEnum
        from pl_eshop_refund
        where refund_order_id = #{id}
          and profile_id = #{profileId}
    </select>
    <select id="getBtypeListByProfile" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select *
        from base_btype
        where profile_id = #{profileId}
    </select>
    <select id="queryRefundSerialInfos"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundDetailSerialNo">
        select *
        from pl_eshop_refund_apply_detail_serialno
        where refund_order_id = #{parameter.refundOrderId}
          and profile_id = #{parameter.profileId}
          and refund_order_detail_id = #{parameter.detailId}
    </select>
    <select id="queryRefundListForSaveCheckin"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select
        r.id,
        r.trade_refund_order_number,
        r.profile_id,
        r.refund_type as refundTypeEnum,
        r.trade_order_id,
        r.otype_id,
        r.refund_apply_taxed_total,
        extend.order_type,
        extend.cycle
        from pl_eshop_refund r
        left join pl_eshop_refund_extend extend on r.profile_id = extend.profile_id and r.id = extend.refund_order_id
        where r.profile_id = #{parameter.profileId}
        <if test="parameter.tradeOrderId!=null and parameter.tradeOrderId!=''">
            and r.trade_order_id = #{parameter.tradeOrderId}
        </if>
        <if test="parameter.refundOrderId!=null and parameter.refundOrderId!=0">
            and r.id = #{parameter.refundOrderId}
        </if>
        <if test="parameter.refundTypes!=null and parameter.refundTypes.size() > 0">
            and r.refund_type in
            <foreach item="item" collection="parameter.refundTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parameter.tradeIdList!=null and parameter.tradeIdList.size() > 0">
            and r.trade_order_id in
            <foreach item="item" collection="parameter.tradeIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parameter.receiveStatuses!=null and parameter.receiveStatuses.size()>0">
            and r.receive_state in
            <foreach item="item" collection="parameter.receiveStatuses" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parameter.deleted!=null">
            and r.deleted = #{parameter.deleted}
        </if>
        <if test="parameter.mappingState!=null">
            and r.mapping_state = #{parameter.mappingState}
        </if>
    </select>
    <select id="getRefundRelationByTargetVchcode"
            resultType="com.wsgjp.ct.bill.core.handle.entity.dao.BillRelation">
        select * from
        td_bill_relation
        where target_vchcode in
        <foreach item="item" collection="targetVchcodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        and profile_id = #{profileId}
        and source_vchtype = 9801
        and target_vchtype = 9802
    </select>
    <select id="getNotInRefundIdByPtypeDetail" resultType="java.math.BigInteger">
        select distinct refund_order_id from pl_eshop_refund_apply_detail d
        left join pl_eshop_refund refund on d.profile_id = refund.profile_id and d.refund_order_id = refund.id
        where d.profile_id = #{profileId}
        and d.sku_id NOT in
        <foreach item="id" collection="skuIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="timeType != null">
            <choose>
                <when test="timeType.getCode() == 4">
                    AND refund.refund_finish_time &gt;=#{beginTime}
                    AND refund.refund_finish_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 3">
                    AND refund.refund_create_time &gt;=#{beginTime}
                    AND refund.refund_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 2">
                    AND refund.create_time &gt;=#{beginTime}
                    AND refund.create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 0">
                    AND refund.trade_create_time &gt;=#{beginTime}
                    AND refund.trade_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 1">
                    AND refund.trade_pay_time &gt;=#{beginTime}
                    and refund.trade_pay_time &lt;=#{endTime}
                </when>
            </choose>
        </if>
    </select>
    <select id="getNotRefundIdByPtypeCombo" resultType="java.math.BigInteger">
        select distinct refund_order_id from pl_eshop_refund_apply_detail_combo d
        left join pl_eshop_refund refund on d.profile_id = refund.profile_id and d.refund_order_id = refund.id
        where d.profile_id = #{profileId}
        and d.combo_id NOT in
        <foreach item="id" collection="skuIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="timeType != null">
            <choose>
                <when test="timeType.getCode() == 4">
                    AND refund.refund_finish_time &gt;=#{beginTime}
                    AND refund.refund_finish_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 3">
                    AND refund.refund_create_time &gt;=#{beginTime}
                    AND refund.refund_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 2">
                    AND refund.create_time &gt;=#{beginTime}
                    AND refund.create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 0">
                    AND refund.trade_create_time &gt;=#{beginTime}
                    AND refund.trade_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 1">
                    AND refund.trade_pay_time &gt;=#{beginTime}
                    and refund.trade_pay_time &lt;=#{endTime}
                </when>
            </choose>
        </if>
    </select>
    <select id="getRefundIdByPtypeComboForOnly" resultType="java.math.BigInteger">
        select distinct refund_order_id from pl_eshop_refund_apply_detail_combo d
        left join pl_eshop_refund refund on d.profile_id = refund.profile_id and d.refund_order_id = refund.id
        where d.profile_id = #{profileId}
        and d.combo_id in
        <foreach item="id" collection="skuIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="timeType != null">
            <choose>
                <when test="timeType.getCode() == 4">
                    AND refund.refund_finish_time &gt;=#{beginTime}
                    AND refund.refund_finish_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 3">
                    AND refund.refund_create_time &gt;=#{beginTime}
                    AND refund.refund_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 2">
                    AND refund.create_time &gt;=#{beginTime}
                    AND refund.create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 0">
                    AND refund.trade_create_time &gt;=#{beginTime}
                    AND refund.trade_create_time &lt;=#{endTime}
                </when>
                <when test="timeType.getCode() == 1">
                    AND refund.trade_pay_time &gt;=#{beginTime}
                    and refund.trade_pay_time &lt;=#{endTime}
                </when>
            </choose>
        </if>
        AND not exists (select *
        from pl_eshop_refund_apply_detail dd
        where dd.refund_order_id = refund.id
        and dd.combo_row_id = 0
        and dd.profile_id = #{profileId})
    </select>
    <select id="getCheckInFreightInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        select checkin.freight_bill_no, checkin.freight_btype_id, checkin.freight_name, checkin.create_type
        from pl_eshop_refund refund
                 left join pl_eshop_refund_checkin_relation rel on rel.refund_order_id = refund.id and rel.profile_id = refund.profile_id
                 left join pl_eshop_refund_receive_checkin checkin on checkin.vchcode = rel.checkin_id and rel.profile_id = checkin.profile_id
        where refund.id = #{refund.id}
          and refund.profile_id = #{refund.profileId}
    </select>
    <select id="hasSaleReturnBill" resultType="java.lang.Boolean">
        select count(*) > 0
        from td_bill_relation
        where source_vchcode = #{id}
          and profile_id = #{profileId}
          and source_vchtype = 9801
          and target_vchtype = 2100
    </select>
    <select id="hasPayBill" resultType="java.lang.Boolean">
        select count(*) > 0
        from td_bill_relation
        where source_vchcode = #{id}
          and profile_id = #{profileId}
          and source_vchtype = 9801
          and target_vchtype = 4002
    </select>
    <select id="listRefundWithDetailIdBatch"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundWithDetailId">
        select refund_order_id,source_detail_id
        from pl_eshop_refund_apply_detail
        where refund_order_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and profile_id = #{profileId}
    </select>
    <select id="getOtypeDefaultKtype" resultType="java.math.BigInteger">
        select ktype_id
        from base_otype
        where profile_id = #{profileId}
          and id = #{otypeId}
    </select>
    <select id="getCheckinKtypeId" resultType="java.math.BigInteger">
        select c.ktype_id
        from pl_eshop_refund r
                 left join pl_eshop_refund_checkin_relation rel on r.profile_id = rel.profile_id and r.id = rel.refund_order_id
                 left join pl_eshop_refund_receive_checkin c on c.profile_id = rel.profile_id and rel.checkin_id = c.vchcode
        where r.profile_id = #{profileId}
          and r.id = #{refundOrderId}
    </select>
    <update id="updateRefundActualQtyByAuto">
        update pl_eshop_refund_apply_detail
        set actual_receipt_number = apply_refund_unit_qty
        where profile_id = #{profileId}
          and refund_order_id = #{id};
        update pl_eshop_refund_apply_detail_combo
        set actual_receipt_number = qty
        where profile_id = #{profileId}
          and refund_order_id = #{id};
    </update>
    <update id="clearRefundActQty">
        update pl_eshop_refund_apply_detail
        set actual_receipt_number = 0
        where profile_id = #{profileId}
          and refund_order_id = #{refundOrderId};
        update pl_eshop_refund_apply_detail_combo
        set actual_receipt_number = 0
        where profile_id = #{profileId}
          and refund_order_id = #{refundOrderId};
    </update>
    <update id="updateRefundKtypeId" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        update pl_eshop_refund
        set ktype_id = #{ktypeId}
        where id = #{id}
          and profile_id = #{profileId}
    </update>
    <update id="updateCheckInComboApplyQty"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInCombo">
        update pl_eshop_refund_receive_checkin_detail_combo
        set apply_refund_unit_qty = #{applyRefundUnitQty}
        where profile_id = #{profileId}
          and combo_row_id = #{comboRowId}
    </update>
    <update id="updateCheckInDetailApplyQty"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        update pl_eshop_refund_receive_checkin_detail
        set apply_refund_unit_qty = #{applyRefundUnitQty}
        where profile_id = #{profileId}
          and id = #{id};
        update td_bill_inout_detail
        set memo = #{memo}
        where profile_id = #{profileId}
          and inout_detail_id = #{inoutDetailId};
        update acc_bill_inout_detail
        set memo = #{memo}
        where profile_id = #{profileId}
          and inout_detail_id = #{inoutDetailId};
    </update>
    <update id="updateRefundForMoneyOnly"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        update pl_eshop_refund
        set refund_type   = 1,
            receive_state = 1
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="changeRefundKtypeId" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        update pl_eshop_refund
        set ktype_id = #{ktypeId},
            has_edit=1
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateRefundReturnSyncState">
        update pl_eshop_refund_extend
        set return_sync_state = #{state}
        where profile_id = #{profileId}
          and refund_order_id = #{id}
    </update>
    <update id="updateSnapshotRefundType"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntitySnapshot">
        update pl_eshop_refund_snapshot
        set refund_type = #{refundType}
        where profile_id = ${profileId}
          and refund_order_id = #{refundOrderId}
    </update>
    <update id="updateRefundRefundYAN" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        update pl_eshop_refund
        set refund_statement = #{refundStatement},
            refund_reason    = #{refundReason}
        where id = #{id}
          and profile_id = #{profileId}
    </update>
    <update id="updateRefundEdit" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        update pl_eshop_refund
        set has_edit = 0
        where id = #{id}
          and profile_id = #{profileId}
    </update>
    <update id="noPay">
        update pl_eshop_refund set pay_state = 0 where profile_id = #{profileId} and id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="noNoPay">
        update pl_eshop_refund set pay_state = 1 where profile_id = #{profileId} and id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="confirmUpdateRefund">
        <foreach item="refund" collection="refundList">
            update pl_eshop_refund set confirm_state=#{refund.confirmState},confirm_etype_id = #{confirmEtypeId},confirm_time=now()
            where profile_id = #{refund.profileId} and id = #{refund.id};
        </foreach>
    </update>
    <update id="updateRefundDetailByDetailIdBatch">
        <foreach collection="details" item="detail" separator=";">
            UPDATE pl_eshop_refund_apply_detail
            SET
            update_time = NOW()
            <if test="detail.costPeriod != null and detail.costPeriod != 0">
                , cost_period = #{detail.costPeriod}
            </if>
            <if test="detail.costState != null">
                , cost_state = #{detail.costState}
            </if>
            <if test="detail.batchPrice != null and detail.batchPrice != 0">
                , batch_price = #{detail.batchPrice}
            </if>
            <if test="detail.batchNo != null and detail.batchNo != ''">
                , batchNo = #{detail.batchNo}
            </if>
            <if test="detail.expireDate != null">
                , expire_date = #{detail.expireDate}
            </if>
            <if test="detail.produceDate != null">
                , produce_date = #{detail.produceDate}
            </if>
            WHERE profile_id = #{profileId} AND id = #{detail.detailId}
        </foreach>
    </update>
    <update id="updateRefundExtendRefundOrderDetailSummary">
        update pl_eshop_refund_extend
        set refund_order_detail_summary = #{refundOrderDetailSummaryId}
        where profile_id = #{profileId}
          and refund_order_id = #{id}
    </update>
    <update id="updateRefundCostStateBatch">
        <foreach item="item" collection="refundList">
            UPDATE pl_eshop_refund
            SET cost_state=#{item.costState}
            where profile_id = #{item.profileId}
            and id = #{item.id};
        </foreach>
    </update>
    <update id="editRefundTypeList">
        UPDATE pl_eshop_refund
        SET refund_type=1,
        refund_process_state= -1,
        receive_state=1,
        has_edit=1
        where profile_id = #{profileId}
        and id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getSysDataForDelete" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData">
        select *
        from sys_data
        where profile_id = #{profileId}
          and sub_name = #{subName}
    </select>
    <select id="listDeleteBillRequest" resultType="com.wsgjp.ct.sale.common.entity.request.DeleteBillRequest">
        select c.vchcode as billVchcode,r.id as refundVchcode from acc_bill_core c
        left join pl_eshop_refund r on r.profile_id = c.profile_id and r.trade_refund_order_number = c.source_number
        where c.profile_id=#{profileId}
        and c.vchcode in
        <foreach item="item" collection="billVchcodes" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        union
        select c.vchcode as billVchcode,r.id as refundVchcode from td_bill_core c
        left join pl_eshop_refund r on r.profile_id = c.profile_id and r.trade_refund_order_number = c.source_number
        where c.profile_id=#{profileId}
        and c.vchcode in
        <foreach item="item" collection="billVchcodes" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getDeliverVchcodes" resultType="java.math.BigInteger">
        select detailD.vchcode
        from acc_bill_detail_deliver detailD
                 join acc_bill_warehouse_task_detail detailT on detailD.detail_id = detailT.detail_id and detailD.profile_id = detailT.profile_id
                 join acc_bill_warehouse_task task on detailT.warehouse_task_id = task.warehouse_task_id and detailT.profile_id = task.warehouse_task_id
        where detailD.trade_order_id = #{tradeOrderId}
          and detailD.profile_id = #{profileId}
          and detailD.deleted = 0
          and task.deleted = 0
        union
        select detailD.vchcode
        from td_bill_detail_deliver detailD
                 join td_bill_warehouse_task_detail detailT on detailD.detail_id = detailT.detail_id and detailD.profile_id = detailT.profile_id
                 join td_bill_warehouse_task task on detailT.warehouse_task_id = task.warehouse_task_id and detailT.profile_id = task.warehouse_task_id
        where detailD.trade_order_id = #{tradeOrderId}
          and detailD.profile_id = #{profileId}
          and detailD.deleted = 0
          and task.deleted = 0
    </select>
    <select id="queryCheckinListByRefundList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        select *
        from pl_eshop_refund_receive_checkin checkin
        left join pl_eshop_refund_checkin_relation rel on checkin.vchcode = rel.checkin_id
        where rel.refund_order_id in
        <foreach collection="refundIds" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        and checkin.profile_id = #{profileId}
    </select>
    <delete id="deleteSysData">
        delete
        from sys_data
        where profile_id = #{profileId}
          and sub_name = #{subName}
          and id = #{id}
    </delete>
    <delete id="deleteCheckinRelationBatch">
        delete from pl_eshop_refund_checkin_relation where profile_id = #{profileId}
        and checkin_id in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">
            #{vchcode}
        </foreach>
    </delete>
    <delete id="deliverHasAccount">
        select count(id) > 0
        from acc_bill_balance_detail
        where profile_id = #{profileId}
        and detail_type = 1
        and business_vchcode in
        <foreach collection="vchcodes" item="vchcode" open="(" close=")" separator=",">
            #{vchcode}
        </foreach>
    </delete>


    <sql id="billReleationInfo">
        t1.id,t1.source_vchcode as refundOrderId,t1.target_vchcode as billVchcode,
        CASE
           WHEN t1.target_vchtype = 2101 OR t1.target_vchtype = 2100 THEN 1
           WHEN t1.target_vchtype = 4005 THEN 2
           WHEN t1.target_vchtype = 2001 OR t1.target_vchtype = 2000 THEN 3
           WHEN t1.target_vchtype = 3301 THEN 5
           WHEN t1.target_vchtype = 4001  THEN 6
           ELSE 4 END AS billType
        ,t1.create_time,t1.update_time
    </sql>

    <select id="getBillReleationList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundBillReleationEntity">
        SELECT
        t3.business_type,
        t3.create_type,
        t3.post_state,
        t3.process_type,
        ass.freight_btype_name as 'logisticsCompany',
        ass.freight_billno as 'logisticsNumber',
        <include refid="billReleationInfo"/>
        FROM td_bill_relation t1
        LEFT JOIN td_bill_core t3 on t1.target_vchcode=t3.vchcode and t1.profile_id=t3.profile_id and t3.deleted = 0
        left join acc_bill_assinfo ass on ass.vchcode = t3.vchcode and ass.profile_id = t3.profile_id
        where t1.source_vchcode=#{id} and t1.profile_id=#{profileId} and t3.vchcode is not null
        union all
        SELECT
        t2.business_type,
        t2.create_type,
        t2.post_state,
        t2.process_type,
        ass.freight_btype_name as 'logisticsCompany',
        ass.freight_billno as 'logisticsNumber',
        <include refid="billReleationInfo"/>
        FROM td_bill_relation t1
        LEFT JOIN acc_bill_core t2 on t1.target_vchcode=t2.vchcode and t1.profile_id=t2.profile_id and t2.deleted = 0
        left join acc_bill_assinfo ass on ass.vchcode = t2.vchcode and ass.profile_id = t2.profile_id
        where t1.source_vchcode=#{id} and t1.profile_id=#{profileId} and t2.vchcode is not null
    </select>
    <select id="checkTdBillRelation" resultType="java.lang.Integer">
        select count(*)
        from td_bill_relation
        where profile_id = #{profileId}
          and source_vchcode = #{sourceVchcode}
          and target_vchcode = #{targetVchcode}
    </select>
    <select id="queryAfterSaleList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        distinct refund.id,
        refund.trade_refund_order_id,
        refund.trade_refund_order_number,
        refund.profile_id,
        refund.otype_id,
        refund.buyer_id,
        refund.create_time,
        refund.cost_state,
        refund.etype_id,
        refund.refund_create_time,
        refund.refund_modify_time,
        refund.trade_create_time,
        refund.trade_finish_time,
        refund.has_submit_to_wms,
        refund.trade_pay_time,
        refund.order_fee,
        refund.business_type,
        e.fullname as etype_name,
        ba.id as atypeId,
        ba.fullname as atypeName,
        refund.create_type,
        refund.confirm_state,
        refund.confirm_time,
        refund.confirm_remark,
        refund.confirm_etype_id,
        refund.receive_state,
        refund.receive_buyer_id,
        refund.receive_remark,
        refund.refund_process_state,
        refund.refund_process_time,
        refund.receive_time,
        refund.pay_state,
        refund.pay_account,
        refund.pay_time,
        refund.pay_etype_id,
        refund.pay_number,
        refund.receive_account,
        refund.refund_apply_total,
        refund.refund_apply_taxed_total,
        refund.refund_apply_tax_total,
        refund.refund_apply_freight_fee,
        refund.refund_apply_mall_fee,
        refund.refund_apply_service_fee,
        refund.update_time,
        refund.ktype_id,
        refund.old_ktype_id,
        refund.refund_state,
        refund.refund_phase,
        refund.refund_statement,
        refund.trade_status,
        refund.deleted,
        refund.trade_order_id,
        refund.eshop_order_id,
        refund.no_detail,
        refund.bill_vchcode,
        refund.bill_poseted,
        refund.`refund_apply_taxed_total` as refundTotal,
        IF(refund.btype_id = 0, sale_order.btype_id, refund.btype_id) as btypeId,
        refund.refund_type as 'refund_type_enum',
        IFNULL(reason.refund_reason, refund.refund_reason) as refund_reason,
        IFNULL(reason.id, refund.refund_reason) as reason_id,
        refund.has_edit,
        refund.mapping_state,
        IF(refund.bill_total = 0, refund.refund_apply_taxed_total, refund.bill_total) as bill_total,
        IF(refund.bill_service_fee = 0, refund.refund_apply_service_fee, refund.bill_service_fee) as bill_service_fee,
        refund.order_etype_id,
        e.fullname as orderEtypeName,
        ifnull(e.fullname, '') as order_etype_name,
        ifnull(receiveBuyer.ai, buyer.ai) as 'buyer.ai',
        ifnull(receiveBuyer.addri, buyer.addri) as 'buyer.addri',
        ifnull(receiveBuyer.ri, buyer.ri) as 'buyer.ri',
        buyer.di as 'buyer.di',
        ifnull(receiveBuyer.customer_receiver, buyer.customer_receiver) as 'buyer.customer_receiver',
        ifnull(receiveBuyer.customer_receiver_mobile, buyer.customer_receiver_mobile) as 'buyer.customer_receiver_mobile',
        ifnull(receiveBuyer.customer_receiver_country, buyer.customer_receiver_country) as 'buyer.customer_receiver_country',
        ifnull(receiveBuyer.customer_receiver_city, buyer.customer_receiver_city) as 'buyer.customer_receiver_city',
        ifnull(receiveBuyer.customer_receiver_province, buyer.customer_receiver_province) as 'buyer.customer_receiver_province',
        ifnull(receiveBuyer.customer_receiver_district, buyer.customer_receiver_district) as 'buyer.customer_receiver_district',
        ifnull(receiveBuyer.customer_receiver_town, buyer.customer_receiver_town) as 'buyer.customer_receiver_town',
        ifnull(receiveBuyer.customer_receiver_address, buyer.customer_receiver_address) as 'buyer.customer_receiver_address',
        ifnull(receiveBuyer.customer_receiver_full_address, buyer.customer_receiver_full_address) as 'buyer.customer_receiver_full_address',
        ifnull(receiveBuyer.customer_shop_account, buyer.customer_shop_account) as 'buyer.customer_shop_account',
        receiveBuyer.customer_shop_account as 'receiveBuyer.customer_shop_account',
        receiveBuyer.ai as 'receiveBuyer.ai',
        receiveBuyer.di as 'receiveBuyer.di',
        receiveBuyer.addri as 'receiveBuyer.addri',
        receiveBuyer.ri as 'receiveBuyer.ri',
        receiveBuyer.customer_receiver as 'receiveBuyer.customer_receiver',
        receiveBuyer.customer_receiver_mobile as 'receiveBuyer.customer_receiver_mobile',
        receiveBuyer.customer_receiver_country as 'receiveBuyer.customer_receiver_country',
        receiveBuyer.customer_receiver_city as 'receiveBuyer.customer_receiver_city',
        receiveBuyer.customer_receiver_province as 'receiveBuyer.customer_receiver_province',
        receiveBuyer.customer_receiver_district as 'receiveBuyer.customer_receiver_district',
        receiveBuyer.customer_receiver_town as 'receiveBuyer.customer_receiver_town',
        receiveBuyer.customer_receiver_address as 'receiveBuyer.customer_receiver_address',
        receiveBuyer.customer_receiver_full_address as 'receiveBuyer.customer_receiver_full_address',
        eshop.fullname as otype_name,
        eshop.fullname as 'eshopInfo.fullname',
        eshop.eshop_type as 'eshopInfo.eshopType',
        sale_order.seller_memo as 'saleOrder.seller_memo',
        sale_order.buyer_message as 'saleOrder.buyer_message',
        sale_order.seller_flag as 'saleOrder.seller_flag',
        sale_order.mapping_state as `saleOrder.mapping_state`,
        sale_order.platform_store_id as 'saleOrder.platform_store_id',
        sale_order.ktype_id as `saleOrder.ktype_id`,
        sale_order.deliver_process_type as `saleOrder.deliver_process_type`,
        sale_order.dised_taxed_total as `disedTaxedTotal`,
        refund.platform_parent_order_id,
        refund.eshop_order_id as eshopOrderId,
        ktype1.fullname as 'ktype_name',
        ktype2.fullname as 'oldKtypeName',
        freight.freight_name as 'checkinFreightName',
        freight.freight_no as 'checkinFreightBillNo',
        refund.memo as memo,
        freight.freight_name as refundCompany,
        freight.freight_no as refundCompanyNumber,
        freight.freight_btype_id as freightBtypeId,
        freight.freight_code as freightCode,
        refund.has_commit_to_distributor,
        refund.platform_refund_state,
        refund.platform_confirm_state,
        refund.platform_change_state,
        refund.platform_return_state,
        refund.supplier_id,
        e.dtype_id,
        timing.id as 'refundTiming.id',
        timing.profile_id as 'refundTiming.profileId',
        timing.eshop_id as 'refundTiming.eshopId',
        timing.sys_promised_confirm_time as 'refundTiming.sysPromisedConfirmTime',
        timing.promised_confirm_time as 'refundTiming.promisedConfirmTime',
        timing.promised_agree_time as 'refundTiming.promisedAgreeTime',
        timing.promised_deliver_time as 'refundTiming.promisedDeliverTime',
        timing.promised_receive_time as 'refundTiming.promisedReceiveTime',
        timing.refund_id as 'refundTiming.refundId',
        refund.refund_duty_ids,
        e2.fullname as confirmEtypeName,
        refund.refund_finish_time,
        exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and profile_id = refund.profile_id) as related_checkin,
        refund_extend.platform_refund_version,refund_extend.platform_auto_intercept_agree,
        refund_extend.receive_etype_id,refund_extend.process_etype_id,
        refund_extend.order_detail_total,
        perdb.buyer_dised_taxed_total as distributionDisedTaxedTotal,
        refund_extend.return_sync_state,
        if(timing.promised_receive_time &lt; if( timing.promised_deliver_time &lt; if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt;
        timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt;
        timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)),timing.promised_deliver_time,if(timing.promised_agree_time &lt;
        if(timing.sys_promised_confirm_time &lt;
        timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt;
        timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time))),timing.promised_receive_time, if( timing.promised_deliver_time &lt;
        if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt;
        timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt;
        timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)),timing.promised_deliver_time,if(timing.promised_agree_time &lt;
        if(timing.sys_promised_confirm_time &lt;
        timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt;
        timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)))) as order_time_tag,
        reasonPlatform.refund_reason as platformRefundReasonStr,
        reasonPlatform.id as platformRefundReason,
        refund_extend.platform_refund_type as platformRefundType,
        refund_extend.order_type,
        if(ktype1.scategory = 2,true,false) as toWms,
        refund_extend.platform_sign_status,
        btype.fullname as 'btypeName',
        btype_supplier.fullname as 'supplierName',
        refund_extend.refund_order_detail_summary as refundOrderDetailSummaryId,
        big_data.big_data as refundOrderDetailSummaryGet,
        refund_extend.cycle,
        refund_extend.refund_save_total as 'refundSaveTotal',
        refund_extend.refund_save_present_total as 'refundSavePresentTotal',
        refund_extend.refund_save_principal_total as 'refundSavePrincipalTotal',
        refund_extend.refund_advance_total as 'refundAdvanceTotal'
        ,refund_extend.refund_national_subsidy_total
        ,refund_extend.refund_buyer_apply_total
        ,refund_extend.refund_platform_amount
        ,refund_extend.original_refund_platform_amount
        ,refund_extend.original_refund_national_subsidy_total,
        if(refund_extend.refund_pay_type=2,true,false) as 'advancePay',
        refund_extend.refund_pay_type,
        refund_extend.refund_advance_total
        ,refund_extend.system_refund_state,
        if(ktype1.scategory = 2,true,false) as 'wmsKtype'
        FROM pl_eshop_refund refund
        LEFT JOIN pl_eshop_sale_order sale_order on sale_order.profile_id = refund.profile_id and sale_order.id = refund.eshop_order_id
        LEFT JOIN pl_buyer buyer on buyer.profile_id = refund.profile_id and buyer.buyer_id = refund.buyer_id
        LEFT JOIN pl_buyer receiveBuyer on receiveBuyer.buyer_id = refund.receive_buyer_id and receiveBuyer.profile_id = refund.profile_id
        LEFT JOIN pl_eshop eshop on eshop.profile_id = refund.profile_id and eshop.otype_id = refund.otype_id
        LEFT JOIN `base_otype` o ON o.profile_id = refund.profile_id AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_config_reason reason on reason.profile_id = refund.profile_id and refund.refund_reason = reason.id
        LEFT JOIN base_etype e on e.profile_id = refund.profile_id and e.id = refund.etype_id
        LEFT JOIN base_etype e2 on e2.profile_id = refund.profile_id and e2.id = refund.confirm_etype_id
        LEFT JOIN base_atype ba on ba.profile_id = refund.profile_id and ba.id = o.atype_id
        LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id = refund.profile_id and freight.refund_order_id = refund.id and freight.freight_type != 1
        LEFT JOIN base_ktype ktype1 on refund.profile_id = ktype1.profile_id and refund.ktype_id = ktype1.id
        LEFT JOIN base_ktype ktype2 on refund.profile_id = ktype2.profile_id and refund.old_ktype_id = ktype2.id
        LEFT JOIN pl_eshop_refund_timing timing on refund.profile_id = timing.profile_id and refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
        LEFT JOIN pl_eshop_refund_extend refund_extend on refund.profile_id = refund_extend.profile_id and refund.id = refund_extend.refund_order_id
        LEFT JOIN pl_eshop_refund_config_reason reasonPlatform on reasonPlatform.profile_id = refund.profile_id and refund_extend.platform_refund_reason = reasonPlatform.id
        LEFT JOIN pl_eshop_refund_distribution_buyer perdb on perdb.profile_id = refund.profile_id and perdb.refund_order_id = refund.id
        LEFT JOIN base_btype btype on btype.profile_id = refund.profile_id and btype.id = refund.btype_id
        LEFT JOIN base_btype btype_supplier on btype_supplier.profile_id = refund.profile_id and btype_supplier.id = refund.supplier_id
        LEFT JOIN mark_data big_data on refund_extend.refund_order_detail_summary = big_data.id and refund_extend.profile_id = big_data.profile_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        WHERE refund.profile_id = #{profileId}
        <if test="refundBatchQueryType!=null">
            <choose>
                <when test="refundBatchQueryType.code == 0  and tradeOrderIds!=null and tradeRefundOrderNumbers!=null and tradeOrderIds.size() > 0  and tradeRefundOrderNumbers.size() > 0">
                    AND
                    (refund.trade_order_id in
                    <foreach collection="tradeOrderIds" item="tradeOrderId" open="(" close=")" separator=",">
                        #{tradeOrderId}
                    </foreach>
                    OR
                    refund.trade_refund_order_number in
                    <foreach collection="tradeRefundOrderNumbers" item="tradeRefundOrderNumber" open="(" close=")" separator=",">
                        #{tradeRefundOrderNumber}
                    </foreach>
                    )
                </when>
                <when test="refundBatchQueryType.code == 1 || refundBatchQueryType.code == 5">
                    <if test="freightNumbers.size() > 0 and sourceDetailIds.size() == 0">
                        AND
                        (freight.freight_no in
                        <foreach collection="freightNumbers" item="freightNumber" open="(" close=")" separator=",">
                            #{freightNumber}
                        </foreach>)
                    </if>
                    <if test="freightNumbers.size() == 0 and sourceDetailIds.size() > 0">
                        AND refund.id in
                        (select d.refund_order_id from pl_eshop_refund_apply_detail d where d.profile_id = #{profileId} and d.source_detail_id in
                        <foreach collection="sourceDetailIds" item="sourceDetailId" open="(" close=")" separator=",">
                            #{sourceDetailId}
                        </foreach>)
                    </if>
                    <if test="freightNumbers.size() > 0 and sourceDetailIds.size() > 0">
                        AND (
                        refund.id in
                        (select d.refund_order_id from pl_eshop_refund_apply_detail d where d.profile_id = #{profileId} and d.source_detail_id in
                        <foreach collection="sourceDetailIds" item="sourceDetailId" open="(" close=")" separator=",">
                            #{sourceDetailId}
                        </foreach>)
                        OR
                        (freight.freight_no in
                        <foreach collection="freightNumbers" item="freightNumber" open="(" close=")" separator=",">
                            #{freightNumber}
                        </foreach>)
                        )
                    </if>
                </when>
                <when test="refundBatchQueryType.code == 2">
                    AND (
                    buyer.customer_shop_account in
                    <foreach collection="batchQueryEncryptKeyList" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                    <if test="batchQueryKeyList!=null and batchQueryKeyList.size()>0">
                        or buyer.ai in
                        <foreach collection="batchQueryKeyList" close=")" open="(" separator="," item="key">
                            #{key}
                        </foreach>
                    </if>

                    <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
                        or refund.trade_order_id IN
                        <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tradeOrderId">
                            #{tradeOrderId}
                        </foreach>
                    </if>
                    )
                </when>
                <when test="refundBatchQueryType.code == 3 and parentTradeOrderIds.size()>0">
                    AND refund.platform_parent_order_id IN
                    <foreach collection="parentTradeOrderIds" close=")" open="(" separator="," item="parentTradeOrderId">
                        #{parentTradeOrderId}
                    </foreach>
                </when>
                <when test="refundBatchQueryType.code == 4 and freightCompanyNames.size() > 0">
                    <foreach item="freightCompanyName" collection="freightCompanyNames" open="AND (" separator=" OR " close=")">
                        freight.freight_name LIKE CONCAT('%', #{freightCompanyName}, '%')
                    </foreach>
                </when>

            </choose>
        </if>
        <if test="timeType!=null">
            <choose>
                <when test="timeType == 2">
                    AND refund.create_time &gt;=#{beginTime}
                    AND refund.create_time &lt;=#{endTime}
                </when>
                <when test="timeType == 3">
                    AND refund.refund_create_time &gt;=#{beginTime}
                    AND refund.refund_create_time &lt;=#{endTime}
                </when>
                <when test="timeType == 4">
                    AND refund.refund_finish_time &gt;=#{beginTime}
                    AND refund.refund_finish_time &lt;=#{endTime}
                </when>
            </choose>
        </if>
        <if test="otypeIds!=null and otypeIds.size()>0">
            and refund.otype_id in
            <foreach collection="otypeIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="refundStatuses!=null and refundStatuses.size()>0">
            and refund.refund_state in
            <foreach collection="refundStatuses" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="platRefundTypes!=null and platRefundTypes.size()>0">
            and refund_extend.platform_refund_type in
            <foreach collection="platRefundTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ktypes!=null and ktypes.size()>0">
            and refund.ktype_id in
            <foreach collection="ktypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="marks!=null and marks.size()>0">
            and refund.id in
            (
            select order_id from pl_eshop_order_mark where e.profile_id = #{profileId} and mark_code in
            <foreach collection="marks" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="localRefundTypes!=null and localRefundTypes.size()>0">
            and refund.refund_type in
            <foreach collection="localRefundTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="relatedCheckinInteger!=null">
            <if test="relatedCheckinInteger==0">
                AND NOT EXISTS (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and profile_id = refund.profile_id)
            </if>
            <if test="relatedCheckinInteger==1">
                AND EXISTS (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and profile_id = refund.profile_id)
            </if>
        </if>
        <if test="receiveStatuses!=null and receiveStatuses.size()>0">
            and refund.receive_state in
            <foreach collection="receiveStatuses" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="refundProcessStates!=null and refundProcessStates.size()>0">
            and refund.refund_process_state in
            <foreach collection="refundProcessStates" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="deleted!=null and deleted.size()>0">
            and refund.deleted in
            <foreach collection="deleted" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="notifyWmsStatus!=null and notifyWmsStatus.size()>0">
            and refund_extend.notify_wms_status in
            <foreach collection="notifyWmsStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessTypes!=null and businessTypes.size()>0">
            and refund.business_type in
            <foreach collection="businessTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="refundCreateType!=null and refundCreateType.size()>0">
            and refund.create_type in
            <foreach collection="refundCreateType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="payStates!=null and payStates.size()>0">
            and refund.pay_state in
            <foreach collection="payStates" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="localRefundStatus!=null and localRefundStatus.size()>0">
            and refund.refund_state in
            <foreach collection="localRefundStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="handleButtonIndex!=0">
            <choose>
                <when test="handleButtonIndex == 1000">
                    AND (
                    (refund.refund_type = 5)
                    OR
                    (refund.refund_type in (0,6,7) AND pay_state != 2)
                    OR
                    (refund_extend.freight_intercept_status in (2,3) AND refund.receive_state = 1)
                    OR
                    (refund.refund_type = 1 AND refund.refund_state = 3)
                    OR
                    (refund.refund_type in (1,2) AND refund.refund_state = 3 AND refund.receive_state != 3 AND exists(select id from pl_eshop_refund_freight where refund_order_id =
                    refund.id and profile_id = refund.profile_id and freight_type = 2))
                    )
                </when>
                <when test="handleButtonIndex == 1001">
                    AND refund.refund_type = 5
                </when>
                <when test="handleButtonIndex == 1002">
                    AND refund.refund_type in (0,6,7)
                    AND pay_state != 2
                </when>
                <when test="handleButtonIndex == 1003">
                    AND refund_extend.freight_intercept_status in (2,3)
                    AND refund.receive_state = 1
                </when>
                <when test="handleButtonIndex == 1004">
                    AND refund.refund_type = 1
                    AND refund.refund_state = 3
                </when>
                <when test="handleButtonIndex == 1005">
                    AND refund.refund_type in (1,2)
                    AND refund.refund_state = 3
                    AND refund.receive_state != 3
                    AND exists(select id from pl_eshop_refund_freight where refund_order_id = refund.id and profile_id = refund.profile_id and freight_type = 2)
                </when>
                <when test="handleButtonIndex == 1006">
                    AND (
                    (refund_extend.platform_refund_type in (0,1,4,5) AND refund.refund_state = 1)
                    OR
                    ( refund_extend.platform_refund_type = 1 AND refund.refund_state = 1)
                    )
                </when>
                <when test="handleButtonIndex == 1007">
                    AND refund_extend.platform_refund_type in (0,1,4,5)
                    AND refund.refund_state = 1
                </when>
                <when test="handleButtonIndex == 1008">
                    AND refund_extend.platform_refund_type = 1
                    AND refund.refund_state = 1
                </when>
            </choose>
        </if>
    </select>

    <select id="queryAfterSaleListCount" resultType="int">
        select count(a.id)
        from (SELECT distinct refund.id
              FROM pl_eshop_refund refund
                       LEFT JOIN pl_eshop_sale_order sale_order on sale_order.profile_id = refund.profile_id and sale_order.id = refund.eshop_order_id
                       LEFT JOIN pl_buyer buyer on buyer.profile_id = refund.profile_id and buyer.buyer_id = refund.buyer_id
                       LEFT JOIN pl_buyer receiveBuyer on receiveBuyer.buyer_id = refund.receive_buyer_id and receiveBuyer.profile_id = refund.profile_id
                       LEFT JOIN pl_eshop eshop on eshop.profile_id = refund.profile_id and eshop.otype_id = refund.otype_id
                       LEFT JOIN `base_otype` o ON o.profile_id = refund.profile_id AND o.id = refund.otype_id
                       LEFT JOIN pl_eshop_refund_config_reason reason on reason.profile_id = refund.profile_id and refund.refund_reason = reason.id
                       LEFT JOIN base_etype e on e.profile_id = refund.profile_id and e.id = refund.etype_id
                       LEFT JOIN base_etype e2 on e2.profile_id = refund.profile_id and e2.id = refund.confirm_etype_id
                       LEFT JOIN base_atype ba on ba.profile_id = refund.profile_id and ba.id = o.atype_id
                       LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id = refund.profile_id and freight.refund_order_id = refund.id and freight.freight_type != 1
                       LEFT JOIN base_ktype ktype1 on refund.profile_id = ktype1.profile_id and refund.ktype_id = ktype1.id
                       LEFT JOIN base_ktype ktype2 on refund.profile_id = ktype2.profile_id and refund.old_ktype_id = ktype2.id
                       LEFT JOIN pl_eshop_refund_timing timing on refund.profile_id = timing.profile_id and refund.id = timing.refund_id and refund.otype_id = timing.eshop_id
                       LEFT JOIN pl_eshop_refund_extend refund_extend on refund.profile_id = refund_extend.profile_id and refund.id = refund_extend.refund_order_id
                       LEFT JOIN pl_eshop_refund_config_reason reasonPlatform
                                 on reasonPlatform.profile_id = refund.profile_id and refund_extend.platform_refund_reason = reasonPlatform.id
                       LEFT JOIN pl_eshop_refund_distribution_buyer perdb on perdb.profile_id = refund.profile_id and perdb.refund_order_id = refund.id
                       LEFT JOIN base_btype btype on btype.profile_id = refund.profile_id and btype.id = refund.btype_id
                       LEFT JOIN base_btype btype_supplier on btype_supplier.profile_id = refund.profile_id and btype_supplier.id = refund.supplier_id
                       LEFT JOIN mark_data big_data on refund_extend.refund_order_detail_summary = big_data.id and refund_extend.profile_id = big_data.profile_id) a
    </select>
</mapper>
