<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.SaleTaskMapper">
    <delete id="deleteSaleTaskNew">
        delete from pl_sale_task where profile_id=#{profileId} and id=#{taskId};
        delete from pl_sale_task_filed_collection where profile_id=#{profileId} and sale_task_id=#{taskId};
        delete from pl_sale_task_detail where profile_id=#{profileId} and sale_task_id=#{taskId};
        delete from pl_sale_task_detail_good where profile_id=#{profileId} and sale_task_id=#{taskId};
        <if test="taskDetailIds != null and taskDetailIds.size>0">
            delete from pl_sale_task_period_income where profile_id=#{profileId} and task_detail_id in
            <foreach collection="taskDetailIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            ;
        </if>
    </delete>
    <select id="getAllowBtypeLimited" resultType="java.math.BigInteger">
        select object_id
        from base_limit_scope
        where object_type = 4
          and profile_id = #{profileId}
          and etype_id = #{employeeId}
    </select>
    <select id="getAllowPtypeLimited" resultType="java.math.BigInteger">
        select ptype_id
        from base_ptype_limit_scope
        where profile_id = #{profileId}
          and etype_id = #{employeeId}
    </select>
    <select id="getSaleTaskByParamNew"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.PtypeSaleTaskNew">
        select
        pst.id,pst.profile_id,pst.task_name,pst.task_code,pst.period_group_id,pst.period_group_name,pst.task_clazz,pst.task_total,pst.create_time,pst.update_time,pst.creator_id,pst.remark,pst.task_type
        from pl_sale_task pst
        where pst.profile_id = #{param.profileId}
        and pst.deleted = 0
        <if test="param.saleTaskName!=null and param.saleTaskName !='' ">
            and pst.task_name LIKE CONCAT('%',#{param.saleTaskName},'%')
        </if>
        <if test="param.saleTaskClazzCodes!=null and param.saleTaskClazzCodes.size>0">
            and exists
            (select 1 from pl_sale_task_filed_collection pstfc where pst.profile_id = pstfc.profile_id and pst.id =
            pstfc.sale_task_id and pstfc.task_field in
            <foreach collection="param.saleTaskClazzCodes" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="param.saleTaskCode!=null and param.saleTaskCode !='' ">
            and pst.task_code LIKE CONCAT('%',#{param.saleTaskCode},'%')
        </if>
        <if test="param.filter!=null">
            <if test="param.filter.periodGroupName!=null and param.filter.periodGroupName!=''">
                and pst.period_group_name LIKE CONCAT('%',#{param.filter.periodGroupName},'%')
            </if>
            <if test="param.filter.saleTaskTotalBegin!=null and param.filter.saleTaskTotalBegin!='' and param.filter.saleTaskTotalEnd!=null and param.filter.saleTaskTotalEnd!='' ">
                and pst.task_total between #{param.filter.saleTaskTotalBegin} and #{param.filter.saleTaskTotalEnd}
            </if>
            <if test="param.filter.remark!=null and param.filter.remark!=''">
                and pst.remark LIKE CONCAT('%',#{param.filter.remark},'%')
            </if>
            <if test="param.filter.saleTaskTotalBegin!=null and param.filter.saleTaskTotalBegin !='' ">
                and pst.task_total &gt;= #{param.filter.saleTaskTotalBegin}
            </if>
            <if test="param.filter.saleTaskTotalEnd!=null and param.filter.saleTaskTotalEnd !='' ">
                and pst.task_total &lt;= #{param.filter.saleTaskTotalEnd}
            </if>
            <if test="param.filter.taskType!=null">
                and pst.task_type =#{param.filter.taskType}
            </if>
        </if>
    </select>
    <select id="getTaskClazzListByTaskId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskFieldCollection">
        select id,profile_id,sale_task_id,task_field,create_time,update_time from pl_sale_task_filed_collection where
        profile_id=#{profileId} and sale_task_id in (
        <foreach collection="taskIds" item="taskId" separator=",">
            #{taskId}
        </foreach>
        )
    </select>
    <select id="getSaleTaskDetailsByParam"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.PtypeSaleTaskDetailNew">
        select pstd.id,
               pstd.ptype_info_type,
               ''                as   ptypeMessage,
               (case
                    when pstd.btype_info_type = 0 then concat('往来单位:', b.fullname)
                    when pstd.btype_info_type = 1 then concat('客户标签:', v.labelfield_value)
                    when pstd.btype_info_type = 2 then concat('客户所属地区:', a.fullname)
                    else '' end) as   btypeMessage,
               pstd.btype_id,
               pstd.btype_mark_id,
               pstd.area_id,
               pstd.btype_info_type,
               pstd.etype_id,
               pstd.dtype_id,
               pstd.otype_id,
               pstd.shop_type,
               pstd.detail_total,
               ifnull(b.fullname, '') btypeName,
               ifnull(a.fullname, '') areaName,
               ifnull(et.fullname, '') etypeName,
               ifnull(dt.fullname, '') dtypeName,
               ifnull(ot.fullname, '') otypeName,
               pstd.sale_task_id
        from pl_sale_task_detail pstd
                 left join base_btype b
                           on pstd.profile_id = b.profile_id and pstd.btype_id = b.id and pstd.btype_info_type = 0
                 left join cf_labelfield_value v
                           on pstd.profile_id = v.profile_id and pstd.btype_mark_id = v.id and pstd.btype_info_type = 1
                 left join base_areatype a
                           on pstd.profile_id = a.profile_id and pstd.area_id = a.id and pstd.btype_info_type = 2
                 left join base_etype et on pstd.profile_id = et.profile_id and pstd.etype_id = et.id
                 left join base_dtype dt on pstd.profile_id = dt.profile_id and pstd.dtype_id = dt.id
                 left join base_otype ot on pstd.profile_id = ot.profile_id and pstd.otype_id = ot.id
        where pstd.profile_id = #{param.profileId}
          and pstd.sale_task_id = #{param.saleTaskId};
    </select>
    <select id="getSaleTaskPtypeInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        SELECT b.id,b.profile_id,b.ptype_id,b.unit as unit_id,b.sale_task_id,b.task_detail_id,b.sku_id
        , c.fullname
        , c.usercode
        , c.standard
        , c.ptype_type
        , c.ptype_area
        , c.memo
        , IFNULL(sku.propvalue_names
        , '') AS propValues
        , IFNULL(xcode.xcode
        , '') AS xcode
        , IF (c.propenabled=0
        , pic.pic_url
        , (CASE WHEN sku.pic_url='' THEN pic.pic_url ELSE sku.pic_url END)) AS picUrl
        , unit.unit_name
        , band.brand_name
        , barcode.fullbarcode AS barcode
        ,
        <include refid="com.wsgjp.ct.sale.biz.jarvis.mapper.PtypeCommonMapper.sideShow">
            <property name="ptype" value="c"/>
        </include>
        (case c.weight_unit when 1 then c.weight else c.weight/1000 end) AS weightShow
        , case when c.propenabled =0 then '' else '0' end propFormat
        , c.snenabled as snEnabled
        , c.batchenabled
        , IF (c.pcategory=1
        , 1
        , 0) AS pcategory
        , IF (c.pcategory=2
        , 1
        , 0) AS combo
        , c.typeid
        FROM pl_sale_task_detail_good b
        LEFT JOIN base_ptype c ON c.`id`=b.`ptype_id`
        AND c.`profile_id`=b.`profile_id`
        LEFT JOIN base_brandtype band ON band.profile_id=c.`profile_id`
        AND band.id=c.`brand_id`
        LEFT JOIN `base_ptype_sku` sku ON sku.profile_id=b.`profile_id`
        AND sku.id=b.sku_id
        LEFT JOIN base_ptype_xcode xcode ON xcode.profile_id=b.`profile_id`
        AND xcode.ptype_id=b.ptype_id
        AND xcode.sku_id=b.sku_id
        AND xcode.unit_id=b.unit
        AND xcode.defaulted=1
        LEFT JOIN base_ptype_fullbarcode barcode ON barcode.profile_id=b.profile_id
        AND barcode.ptype_id=b.ptype_id
        AND barcode.sku_id=b.sku_id
        AND barcode.unit_id=b.unit
        AND barcode.defaulted=1
        LEFT JOIN base_ptype_pic pic ON pic.profile_id=b.`profile_id`
        AND pic.ptype_id=b.ptype_id
        AND pic.rowindex=1
        LEFT JOIN base_ptype_unit unit ON unit.profile_id=b.`profile_id`
        AND unit.ptype_id=b.ptype_id
        AND unit.id=b.unit WHERE b.profile_id=#{profileId} and b.ptype_id>0
        and b.task_detail_id in(
        <foreach collection="taskDetailIds" item="taskDetailId" separator=",">
            #{taskDetailId}
        </foreach>
        )
        order by b.create_time, b.id;
    </select>
    <select id="getSaleTaskPtypeInfoForBrand"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        select id,profile_id,task_detail_id,brand_id from pl_sale_task_detail_good where profile_id=#{profileId} and
        brand_id>0 and task_detail_id in(
        <foreach collection="taskDetailIds" item="taskDetailId" separator=",">
            #{taskDetailId}
        </foreach>
        )
    </select>
    <select id="getSaleTaskPtypeInfoForClass"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        select g.id,g.profile_id,g.task_detail_id,g.ptype_class_id,g.ptype_class_id as ptype_id,p.fullname from
        pl_sale_task_detail_good g
        left join base_ptype p on g.ptype_class_id=p.id
        where g.profile_id=#{profileId} and g.ptype_class_id>0 and g.task_detail_id in(
        <foreach collection="taskDetailIds" item="taskDetailId" separator=",">
            #{taskDetailId}
        </foreach>
        )
    </select>
    <select id="getSaleTaskPtypeInfoForLabel"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        select id,profile_id,task_detail_id,ptype_mark_id from pl_sale_task_detail_good where profile_id=#{profileId}
        and ptype_mark_id>0 and task_detail_id in(
        <foreach collection="taskDetailIds" item="taskDetailId" separator=",">
            #{taskDetailId}
        </foreach>
        )
    </select>
    <select id="getSaleTaskPeriodIncomes"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncome">
        select id,profile_id,period_id,task_detail_id,total from pl_sale_task_period_income where
        profile_id=#{profileId} and task_detail_id in(
        <foreach collection="taskDetailIds" item="taskDetailId" separator=",">
            #{taskDetailId}
        </foreach>
        )
    </select>
    <select id="getSaleTaskFieldCollectionList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskFieldCollection">
        select id,profile_id,sale_task_id,task_field from pl_sale_task_filed_collection where profile_id=#{profileId}
        and sale_task_id in(
        <foreach collection="taskIds" item="taskId" separator=",">
            #{taskId}
        </foreach>
        )
    </select>
    <sql id="where">
        inc
        .
        profile_id
        =
        #{param.profileId}
        and
        inc
        .
        task_detail_id
        =
        #{param.id}
        and
        ac
        .
        bill_date
        between
        psp
        .
        period_start_time
        and
        psp
        .
        period_end_time
        and
        ac
        .
        redword
        =
        0
        and
        ac
        .
        redbill_state
        =
        0
        and
        ac
        .
        post_state
        =
        800
    </sql>
    <sql id="where2">
        <if test="param.btypeMarkId!=null and param.btypeMarkId!=0">
            and exists(select 1 from cf_data_label_btype bl where bl.profile_id=inc.profile_id and
            bl.resource_id=ac.btype_id and bl.labelfield_value_id=#{param.btypeMarkId})
        </if>
        <if test="param.btypeId!=null and param.btypeId!=0">
            and ac.btype_id = #{param.btypeId}
        </if>
        <if test="param.areaId!=null and param.areaId!=0">
            and b.areatype_id=#{param.areaId}
        </if>
        <if test="param.dtypeId!=null and param.dtypeId!=0">
            and ac.dtype_id=#{param.dtypeId}
        </if>
        <if test="param.etypeId!=null and param.etypeId!=0">
            and ac.etype_id=#{param.etypeId}
        </if>
        <if test="param.ktypeId!=null and param.ktypeId!=0">
            and ac.ktype_id=#{param.ktypeId}
        </if>
        <if test="param.otypeId!=null and param.otypeId!=0">
            and ac.otype_id=#{param.otypeId}
        </if>
        <if test="param.shopType!=null and param.shopType!=-1">
            and pe.eshop_type=#{param.shopType}
        </if>
    </sql>
    <sql id="publicWhere">
        <include refid="where"/>
        and ac.vchtype in (2000,2001,2002,2100,2101,2102,2200)
        <include refid="where2"/>
    </sql>
    <sql id="publicPtypeWhere">
        and adc.combo_detail_id = 0
        <if test="param.ptypeList!=null and param.ptypeList.size>0">
            and exists
            (select 1 from pl_sale_task_detail_good g
            where inc.profile_id=g.profile_id and inc.task_detail_id=g.task_detail_id
            and adc.ptype_id=g.ptype_id and adc.sku_id=g.sku_id and das.unit_id=g.unit
            and g.brand_id=0 and g.ptype_class_id=0 and g.ptype_mark_id=0
            )
        </if>
        <if test="(param.brandIds!=null and param.brandIds.size>0)">
            and p.brand_id in(
            <foreach collection="param.brandIds" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="(param.ptypeClassList!=null and param.ptypeClassList.size>0)">
            and exists(select 1 from base_ptype bp where bp.profile_id=inc.profile_id and p.partypeid = bp.typeid and bp.id in(
            <foreach collection="param.ptypeClassList" item="item" separator=",">
                #{item.ptypeClassId}
            </foreach>
            ))
        </if>
        <if test="param.ptypeMarkIds!=null and param.ptypeMarkIds.size>0">
            and exists(select 1 from cf_data_label_ptype bl where bl.profile_id=inc.profile_id and
            bl.resource_id=adc.ptype_id and bl.labelfield_value_id in(
            <foreach collection="param.ptypeMarkIds" item="item" separator=",">
                #{item}
            </foreach>
            ))
        </if>
    </sql>
    <sql id="publicComboWhere">
        and adc.combo_detail_id > 0
        <if test="param.ptypeList!=null and param.ptypeList.size>0">
            and exists
            (select 1 from pl_sale_task_detail_good g where
            inc.profile_id=g.profile_id and inc.task_detail_id=g.task_detail_id and g.ptype_id=abdc.combo_id and
            g.brand_id=0 and
            g.ptype_class_id=0 and g.ptype_mark_id=0 and g.sku_id=0)
        </if>
        <if test="param.ptypeMarkIds!=null and param.ptypeMarkIds.size>0">
            and exists(select 1 from cf_data_label_ptype bl where bl.profile_id=inc.profile_id and
            bl.resource_id=abdc.combo_id and bl.labelfield_value_id in(
            <foreach collection="param.ptypeMarkIds" item="item" separator=",">
                #{item}
            </foreach>
            ))
        </if>
    </sql>
    <sql id="publicCostTotal">
        (
        <include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
            <property name="bill_name" value="adc"/>
            <property name="ptype_name" value="p"/>
            <property name="cost_name" value="ap"/>
        </include>
        )
    </sql>
    <sql id="queryForNormal">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.task_detail_id,
        psp.period_start_time,
        psp.period_end_time,
        inc.total,
        adc.dised_total,
        adc.dised_taxed_total,
        -1*adc.order_fee_allot_total as order_fee_allot_total,
        adc.qty,
        adc.ptype_id,
        <include refid="publicCostTotal"></include>
        as cost_total
        from pl_sale_task_period_income inc
        inner join pl_sale_period psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and ac.vchcode=adc.vchcode and adc.deleted=0
        inner join acc_bill_detail_assinfo_sale das on adc.vchcode = das.vchcode and adc.detail_id = das.detail_id and
        inc.profile_id=das.profile_id
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhere"/>
        <include refid="publicPtypeWhere"/>
    </sql>
    <sql id="querForComboByCombo">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.task_detail_id,
        psp.period_start_time,
        psp.period_end_time,
        inc.total,
        sum(adc.dised_total) as dised_total,
        sum(adc.dised_taxed_total) as dised_taxed_total,
        -1*sum(adc.order_fee_allot_total) as order_fee_allot_total,
        abdc.qty,
        abdc.combo_id as ptype_id,
        sum(<include refid="publicCostTotal"></include>) cost_total
        from pl_sale_task_period_income inc
        left join pl_sale_period psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_combo abdc on abdc.profile_id = inc.profile_id and ac.vchcode=abdc.vchcode and abdc.deleted=0
        inner join base_ptype_combo bpc on abdc.combo_id = bpc.combo_id and inc.profile_id = bpc.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and abdc.vchcode = adc.vchcode and
        abdc.id = adc.combo_detail_id and adc.deleted=0
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhere"/>
        <include refid="publicComboWhere"/>
        and bpc.single_ptype = 0
        group by inc.profile_id,
        inc.period_id,
        inc.task_detail_id, abdc.id
    </sql>
    <sql id="queryForComboByDetail">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.task_detail_id,
        psp.period_start_time,
        psp.period_end_time,
        inc.total,
        adc.dised_total,
        adc.dised_taxed_total,
        -1*adc.order_fee_allot_total as order_fee_allot_total,
        adc.qty,
        abdc.combo_id as ptype_id,
        <include refid="publicCostTotal"></include>
        as cost_total
        from pl_sale_task_period_income inc
        left join pl_sale_period psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_combo abdc on abdc.profile_id = inc.profile_id and ac.vchcode=abdc.vchcode and abdc.deleted=0
        inner join base_ptype_combo bpc on abdc.combo_id = bpc.combo_id and inc.profile_id = bpc.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and adc.combo_detail_id = abdc.id
        and ac.vchcode=adc.vchcode and adc.deleted=0
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhere"/>
        <include refid="publicComboWhere"/>
        and bpc.single_ptype = 1
    </sql>
    <sql id="queryConcat">
        select a.id,
        a.profile_id,
        a.task_detail_id,
        a.total,
        <if test="saleTask.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeEnum@BY_AMOUNT">
            -sum(a.dised_total) as finishTotal,
        </if>
        <if test="saleTask.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeEnum@BY_DISED_TAXED_TOTAL">
            -sum(a.dised_taxed_total) as finishTotal,
        </if>
        <if test="saleTask.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeEnum@BY_ML">
            -sum(a.dised_total-cost_total-order_fee_allot_total) as finishTotal,
        </if>
        <if test="saleTask.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeEnum@BY_SALE_QTY">
            -sum(a.qty) as finishTotal,
        </if>
        <if test="saleTask.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeEnum@BY_SALE_KIND">
            count(distinct a.ptype_id) as finishTotal,
        </if>
        CONCAT_WS('----', CONCAT(left(a.period_start_time, 10), ' 00:00:00'), CONCAT(left(a.period_end_time, 10),
        '23:59:59')) as timeRange
        from(
        <include refid="queryForNormal"/>
        <if test="!(param.brandIds!=null and param.brandIds.size>0) and !(param.ptypeClassList!=null and param.ptypeClassList.size>0)">
            union all
            <include refid="querForComboByCombo"/>
            union all
            <include refid="queryForComboByDetail"/>
        </if>
        ) a
        group by period_id, task_detail_id
    </sql>
    <select id="getSaleTaskPeriodIncomDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncome">
        <include refid="queryConcat"></include>
    </select>
    <select id="getSaleTaskPeriodIncomDetailsDefault"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncome">
        select inc.id,
               inc.profile_id,
               inc.period_id,
               inc.task_detail_id,
               inc.total,
               0                                                                 as finishTotal,
               CONCAT_WS('----', CONCAT(left(psp.period_start_time,10),' 00:00:00'),
                         CONCAT(left(psp.period_end_time,10),'
        23:59:59')) as timeRange,
               psp.period_start_time                                                startTime,
               psp.period_end_time                                                  endTime
        from pl_sale_task_period_income inc
                 inner join pl_sale_period psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        where inc.profile_id = #{param.profileId}
          and inc.task_detail_id = #{param.id}
        order by psp.period_start_time
    </select>
    <select id="getSaleTaskById"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.PtypeSaleTaskNew">
        select *
        from pl_sale_task
        where profile_id = #{profileId}
          and id = #{saleTaskId}
    </select>
    <select id="getSaleTaskPeriodIncomDetailsForBtype"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncome">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.task_detail_id,
        inc.total,
        count(bb.id) finishTotal,
        CONCAT_WS('----', CONCAT(left(psp.period_start_time, 10), ' 00:00:00'), CONCAT(left(psp.period_end_time, 10),
        '23:59:59')) as timeRange
        from pl_sale_task_period_income inc
        left join pl_sale_period psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join base_btype bb on inc.profile_id=bb.profile_id
        inner join base_btype_bcategory bbb on bb.id=bbb.btype_id and bb.profile_id=bbb.profile_id
        inner join base_btype_extend bbe on bb.id=bbe.btype_id and inc.profile_id=bbe.profile_id
        <if test="param.dtypeId!=null and param.dtypeId!=0">
            left join base_etype be on inc.profile_id=be.profile_id and bbe.etype_id=be.id
        </if>
        where inc.profile_id=#{param.profileId} and inc.task_detail_id=#{param.id} and bb.deleted=0 and bb.stoped=0 and
        bb.classed=0 and bbb.bcategory=0
        and bb.create_time between psp.period_start_time and psp.period_end_time
        <if test="param.etypeId!=null and param.etypeId!=0">
            and bbe.etype_id=#{param.etypeId}
        </if>
        <if test="param.dtypeId!=null and param.dtypeId!=0">
            and be.dtype_id=#{param.dtypeId}
        </if>
        group by period_id, task_detail_id;
    </select>
    <select id="getSaleTaskPeriodIncomDetailsForReturnedMoney"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncome">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.task_detail_id,
        inc.total,
        sum(info.currency_bill_total-info.currency_order_preferential_allot_total) finishTotal,
        CONCAT_WS('----', CONCAT(left(psp.period_start_time, 10), ' 00:00:00'), CONCAT(left(psp.period_end_time, 10),
        '23:59:59')) as timeRange
        from pl_sale_task_period_income inc
        inner join pl_sale_period psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_assinfo info on inc.profile_id = info.profile_id and ac.vchcode=info.vchcode
        left join base_btype b on b.profile_id = inc.profile_id and b.id = ac.btype_id
        left join pl_eshop pe on ac.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        where
        <include refid="where"/>
        and ac.vchtype=4001
        <include refid="where2"/>
        group by period_id, task_detail_id;
    </select>
    <select id="checkExist"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.PtypeSaleTaskNew">
        select id
        from pl_sale_task
        where profile_id = #{profileId}
          and task_code = #{taskCode}
          and id!=#{id}
    </select>
    <select id="getSaleTaskDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.PtypeSaleTaskDetailNew">
        select pstd.id,pstd.profile_id
        ,pstd.dtype_id,d.fullname dtypeName
        ,pstd.etype_id,e.fullname etypeName
        ,pstd.btype_id,b.fullname btypeName
        ,pstd.btype_info_type
        ,pstd.ktype_id,k.fullname ktypeName
        ,pstd.otype_id,o.fullname otypeName
        ,pstd.shop_type
        ,pstd.area_id,a.fullname areaName
        ,pstd.detail_total,pstd.sale_task_id
        ,pstd.btype_mark_id,bl.labelfield_value btypeMarkName
        ,pstd.ptype_info_type,
        (case when pstd.btype_info_type=0 then concat('往来单位:',b.fullname)
        when pstd.btype_info_type=1 then concat('客户标签:',bl.labelfield_value)
        when pstd.btype_info_type=2 then concat('客户所属地区:',a.fullname) else '' end ) as btypeMessage
        from pl_sale_task_detail pstd
        left join base_dtype d on pstd.dtype_id=d.id and pstd.profile_id=d.profile_id
        left join base_etype e on pstd.etype_id=e.id and pstd.profile_id=e.profile_id
        left join base_btype b on pstd.btype_id=b.id and pstd.profile_id=b.profile_id
        left join base_ktype k on pstd.ktype_id=k.id and pstd.profile_id=k.profile_id
        left join base_otype o on pstd.otype_id=o.id and pstd.profile_id=o.profile_id
        left join base_areatype a on pstd.area_id=a.id and pstd.profile_id=a.profile_id
        left join cf_labelfield_value bl on pstd.btype_mark_id=bl.id and pstd.profile_id=bl.profile_id
        where pstd.profile_id=#{profileId} and pstd.sale_task_id=#{taskId}
        <if test="filter!=null">
            <if test="filter.etypeId!=null and filter.etypeId!=0">
                and pstd.etype_id = #{filter.etypeId}
            </if>
            <if test="filter.dtypeId!=null and filter.dtypeId!=0">
                and pstd.dtype_id = #{filter.dtypeId}
            </if>
            <if test="filter.otypeId!=null and filter.otypeId!=0">
                and pstd.otype_id = #{filter.otypeId}
            </if>
            <if test="filter.shopType!=-1">
                and pstd.shop_type = #{filter.shopType}
            </if>
            <if test="filter.taskTotalBegin!=null and filter.taskTotalBegin!=0">
                and pstd.detail_total&gt;=#{filter.taskTotalBegin}
            </if>
            <if test="filter.taskTotalEnd!=null and filter.taskTotalEnd!=0">
                and pstd.detail_total&lt;=#{filter.taskTotalEnd}
            </if>
        </if>
        <if test="param!=null">
            <if test="param.ptypeList!=null and param.ptypeList.size>0">
                and exists(select 1 from pl_sale_task_detail_good g where pstd.id=g.task_detail_id and
                pstd.profile_id=g.profile_id and (g.ptype_id,g.sku_id,g.unit) in
                <foreach collection="param.ptypeList" item="ptype" separator="," open="(" close=")">
                    (#{ptype.ptypeId},#{ptype.skuId},#{ptype.unitId})
                </foreach>
                )
            </if>
            <if test="param.brandIds!=null and param.brandIds.size>0">
                and exists(select 1 from pl_sale_task_detail_good g where pstd.id=g.task_detail_id and
                pstd.profile_id=g.profile_id and g.brand_id in(
                <foreach collection="param.brandIds" item="item" separator=",">
                    #{item}
                </foreach>
                ))
            </if>
            <if test="param.ptypeMarkIds!=null and param.ptypeMarkIds.size>0">
                and exists(select 1 from pl_sale_task_detail_good g where pstd.id=g.task_detail_id and
                pstd.profile_id=g.profile_id and g.ptype_mark_id in(
                <foreach collection="param.ptypeMarkIds" item="item" separator=",">
                    #{item}
                </foreach>
                ))
            </if>
            <if test="param.ptypeClassList!=null and param.ptypeClassList.size>0">
                and exists(select 1 from pl_sale_task_detail_good g where pstd.id=g.task_detail_id and
                pstd.profile_id=g.profile_id and g.ptype_class_id in(
                <foreach collection="param.ptypeClassList" item="item" separator=",">
                    #{item.ptypeId}
                </foreach>
                ))
            </if>
            <if test="param.btypeIds!=null and param.btypeIds.size>0">
                and pstd.btype_id in(
                <foreach collection="param.btypeIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="param.btypeMarkIds!=null and param.btypeMarkIds.size>0">
                and pstd.btype_mark_id in(
                <foreach collection="param.btypeMarkIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="param.areaIds!=null and param.areaIds.size>0">
                and pstd.area_id in(
                <foreach collection="param.areaIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
    </select>
    <insert id="addSaleTaskNew">
        insert into pl_sale_task
        (id,
         profile_id,
         task_name,
         task_code,
         period_group_id,
         period_group_name,
         task_total,
         creator_id,
         remark,
         task_type)
        values (#{param.id}, #{param.profileId}, #{param.taskName}, #{param.taskCode}, #{param.periodGroupId},
                #{param.periodGroupName}, #{param.taskTotal}, #{param.creatorId}, #{param.remark}, #{param.taskType})
    </insert>
    <insert id="addSaleTaskDetailNew">
        insert into pl_sale_task_detail(id,profile_id,dtype_id,etype_id,btype_id,
        btype_info_type,ktype_id,otype_id,shop_type,area_id,
        province,city,district,detail_total,sale_task_id,filter_str,btype_mark_id,ptype_info_type) values
        <foreach collection="params" item="item" separator=",">
            (#{item.id},#{item.profileId},#{item.dtypeId},#{item.etypeId},#{item.btypeId},
            #{item.btypeInfoType},#{item.ktypeId},#{item.otypeId},#{item.shopType},#{item.areaId},
            #{item.province},#{item.city},#{item.district},#{item.detailTotal},#{item.saleTaskId},#{item.filterStr},#{item.btypeMarkId},#{item.ptypeInfoType})
        </foreach>
    </insert>
    <insert id="addSaleTaskPtypeNew">
        insert into pl_sale_task_detail_good(id,profile_id,ptype_id,unit,detail_total,
        ocategory,task_detail_id,brand_id,ptype_class_id,sku_id,ptype_mark_id,sale_task_id) values
        <foreach collection="params" item="item" separator=",">
            (#{item.id},#{item.profileId},#{item.ptypeId},#{item.unitId},#{item.detailTotal},
            #{item.ocategory},#{item.taskDetailId},#{item.brandId},#{item.ptypeClassId},#{item.skuId},#{item.ptypeMarkId},#{item.saleTaskId})
        </foreach>
    </insert>
    <insert id="addSaleTaskFieldCollection">
        insert into pl_sale_task_filed_collection(id, profile_id, sale_task_id, task_field)
        values (#{id}, #{profileId}, #{saleTaskId}, #{taskField})
    </insert>
    <insert id="addSaleTaskPeriodIncomes">
        insert into pl_sale_task_period_income(id,profile_id,period_id,task_detail_id,total) values
        <foreach collection="params" item="item" separator=",">
            (#{item.id},#{item.profileId},#{item.periodId},#{item.taskDetailId},#{item.total})
        </foreach>
    </insert>
</mapper>