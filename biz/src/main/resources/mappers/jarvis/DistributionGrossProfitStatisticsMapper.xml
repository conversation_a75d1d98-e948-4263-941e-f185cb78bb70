<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.jarvis.mapper.DistributionGrossProfitStatisticsMapper">
    <sql id="fields">
       btype.fullname                                                                                  btypeFullName,
       tbwt.task_number,
       ptype.fullname                                                                                  ptypeFullName,
       ptype.usercode                                                                                  ptypeUserCode,
       ptype.standard,
       ptype.ptype_type,
       ifnull(bpu.unit_name, '')                                                                       unit_name,
       ifnull(brand.brand_name, '') as brandName,
       ifnull(xcode.xcode, '')                                                                     as xcode,
       btype.id as btypeId,
       ptype.id as ptypeId,
       tbdc.sku_id,
       tbda.unit_id,
       sum(tbda.unit_qty) * -1                                                                      as unit_qty,
       ptype.propenabled                                                                            as propFormat,
       ptype.snenabled                                                                              as snEnabled,
       ptype.batchenabled,
       IF(ptype.pcategory = 1, 1, 0)                                                                AS category,
       IF(ptype.pcategory = 2, 1, 0)                                                                AS combo,
       ptype.propenabled                                                                            as propEnabled,
       tbdc.gift,
       sum(ifnull(tbda.currency_dised_taxed_total,0)) * -1                                          as currency_dised_taxed_total,
       sum(ifnull(buyer.buyer_dised_taxed_total,0) - ifnull(tbda.currency_dised_taxed_total,0)) * -1 as 'distributionGrossProfit',
        ROUND(sum(ifnull(buyer.buyer_dised_taxed_total,0) - ifnull(tbda.currency_dised_taxed_total,0)) /
              sum(ifnull(buyer.buyer_dised_taxed_total,0)) *
       100 ,2)                                                                                          as 'distributionGrossProfitRate',
       sum(ifnull(buyer.buyer_dised_taxed_total,0))*-1                                                  as 'buyerDisedTaxedTotal',
       sum(ifnull(buyer.buyer_dised_taxed_total,0)) / sum(ifnull(tbda.unit_qty,0))                      as 'buyerDisedTaxedAveragePrice',


        sum(ifnull(tbda.currency_dised_taxed_total,0)*-1)-
        (sum(abs(<include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
        <property name="bill_name" value="tbdc"/>
        <property name="ptype_name" value="ptype"/>
        <property name="cost_name" value="cost"/>
        </include>))+sum(ifnull(tbda.currency_order_fee_allot_total,0))) as 'grossProfit',

        ROUND((sum(ifnull(tbda.currency_dised_taxed_total,0)*-1)-
        (sum(abs(<include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
        <property name="bill_name" value="tbdc"/>
        <property name="ptype_name" value="ptype"/>
        <property name="cost_name" value="cost"/>
        </include>))+sum(ifnull(tbda.currency_order_fee_allot_total,0))))
        /sum(ifnull(tbda.currency_dised_taxed_total,0)*-1)*100,2) as 'grossProfitRate',

        sum(abs(<include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
        <property name="bill_name" value="tbdc"/>
        <property name="ptype_name" value="ptype"/>
        <property name="cost_name" value="cost"/>
        </include>))+sum(ifnull(tbda.currency_order_fee_allot_total,0)) as 'saleCostTotal',

        sum(abs(<include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
        <property name="bill_name" value="tbdc"/>
        <property name="ptype_name" value="ptype"/>
        <property name="cost_name" value="cost"/>
        </include>)) as 'costTotal'
    </sql>

    <sql id="where">
        where tbwt.profile_id = #{param.profileId}
        and tbwt.deleted IN (0)
        and tbwt.business_type = 203
        and tbc.redword = 0
        <if test="param.queryTimeType == @com.wsgjp.ct.sale.biz.jarvis.state.QueryTimeTypeEnum@PAY_TIME ">
            and tbwt.pay_time between #{param.beginTime} and #{param.endTime}
        </if>

        <if test="param.queryTimeType == @com.wsgjp.ct.sale.biz.jarvis.state.QueryTimeTypeEnum@TRADE_CREATE_TIME">
            and tbwt.trade_create_time between #{param.beginTime} and #{param.endTime}
        </if>

        <if test="param.queryTimeType == @com.wsgjp.ct.sale.biz.jarvis.state.QueryTimeTypeEnum@DELIVER_TIME ">
            and tbwt.task_time between #{param.beginTime} and #{param.endTime}
        </if>

        <if test="param.queryTimeType == @com.wsgjp.ct.sale.biz.jarvis.state.QueryTimeTypeEnum@POST_TIME ">
            and tbdc.bill_date between #{param.beginTime} and #{param.endTime} and tbwt.full_link_status>=180
        </if>

        <if test="param.btypes!=null and param.btypes.size()>0">
            and tbwt.btype_id in
            <foreach collection="param.btypes" item="btype" open="(" close=")" separator=",">
                #{btype}
            </foreach>
        </if>
        <if test="param.eshops!=null and param.eshops.size()>0">
            and tbwt.otype_id in
            <foreach collection="param.eshops" item="eshop" open="(" close=")" separator=",">
                #{eshop}
            </foreach>
        </if>
        <if test="param.ktypeIds!=null and param.ktypeIds.size()>0">
            and tbwt.ktype_id in
            <foreach collection="param.ktypeIds" item="ktypeId" open="(" close=")" separator=",">
                #{ktypeId}
            </foreach>
        </if>
        <if test="param.etypeIds!=null and param.etypeIds.size()>0">
            and tbdc.etype_id in
            <foreach collection="param.etypeIds" item="etypeId" open="(" close=")" separator=",">
                #{etypeId}
            </foreach>
        </if>
        <if test="param.refundState !=null and param.refundState.size() > 0 ">
            and
            tbwt.refund_state in
            <foreach collection="param.refundState" close=")" open="(" separator="," item="refundState">
                #{refundState}
            </foreach>
        </if>
        <if test="param.tradeState !=null and param.tradeState.size() > 0 ">
            and tbwt.trade_state in
            <foreach collection="param.tradeState" close=")" open="(" separator="," item="tradeState">
                #{tradeState}
            </foreach>
        </if>
        <if test="param.fullLinkStatus != null and param.fullLinkStatus.size() > 0" >
            and tbwt.full_link_status in
            <foreach collection="param.fullLinkStatus" close=")" open="(" separator="," item="fullLinkStatus_item">
                #{fullLinkStatus_item}
            </foreach>
        </if>
    </sql>

    <sql id="hotTable">
        from td_bill_warehouse_task tbwt
        inner join td_bill_warehouse_task_detail tbwtd on tbwt.warehouse_task_id = tbwtd.warehouse_task_id and tbwt.profile_id = tbwtd.profile_id
        inner join td_bill_detail_core tbdc on tbwtd.detail_id = tbdc.detail_id and tbwt.profile_id = tbdc.profile_id
        left join td_bill_core tbc on tbdc.vchcode=tbc.vchcode and tbdc.profile_id=tbc.profile_id
        left join td_bill_detail_assinfo tbda on tbdc.detail_id = tbda.detail_id and tbwt.profile_id = tbda.profile_id and tbdc.vchcode = tbda.vchcode
        left join base_ptype_unit bpu on tbwt.profile_id=tbda.profile_id and tbda.unit_id = bpu.id and tbdc.ptype_id = bpu.ptype_id
        left join base_btype btype on tbdc.btype_id = btype.id and tbwt.profile_id = tbdc.profile_id
        left join base_ptype ptype on tbdc.ptype_id = ptype.id and tbwt.profile_id = ptype.profile_id
        left join base_brandtype brand on ptype.brand_id = brand.id and tbwt.profile_id = brand.profile_id
        left join base_ptype_xcode xcode on xcode.profile_id = tbwt.profile_id and xcode.ptype_id = tbdc.ptype_id and xcode.sku_id = tbdc.sku_id and tbda.unit_id = xcode.unit_id and xcode.defaulted = 1
        inner join td_bill_detail_distribution_buyer buyer on tbwtd.vchcode = buyer.vchcode and tbwtd.detail_id = buyer.detail_id and tbwt.profile_id = buyer.profile_id
        left join acc_periodcost cost on tbwt.profile_id = cost.profile_id and tbdc.cost_id = cost.id and tbdc.ptype_id = cost.ptype_id
    </sql>

    <sql id="coldTable">
        from acc_bill_warehouse_task tbwt
        inner join acc_bill_warehouse_task_detail tbwtd on tbwt.warehouse_task_id = tbwtd.warehouse_task_id and tbwt.profile_id = tbwtd.profile_id
        inner join acc_bill_detail_core_sale tbdc on tbwtd.detail_id = tbdc.detail_id and tbwt.profile_id = tbdc.profile_id
        left join acc_bill_core tbc on tbdc.vchcode=tbc.vchcode and tbdc.profile_id=tbc.profile_id
        left join acc_bill_detail_assinfo_sale tbda on tbdc.detail_id = tbda.detail_id and tbwt.profile_id = tbda.profile_id and tbdc.vchcode = tbda.vchcode
        left join base_ptype_unit bpu on tbwt.profile_id=tbda.profile_id and tbda.unit_id = bpu.id and tbdc.ptype_id = bpu.ptype_id
        left join base_btype btype on tbdc.btype_id = btype.id and tbwt.profile_id = tbdc.profile_id
        left join base_ptype ptype on tbdc.ptype_id = ptype.id and tbwt.profile_id = ptype.profile_id
        left join base_brandtype brand on ptype.brand_id = brand.id and tbwt.profile_id = brand.profile_id
        left join base_ptype_xcode xcode on xcode.profile_id = tbwt.profile_id and xcode.ptype_id = tbdc.ptype_id and xcode.sku_id = tbdc.sku_id and tbda.unit_id = xcode.unit_id and xcode.defaulted = 1
        inner join acc_bill_detail_distribution_buyer buyer on tbwtd.vchcode = buyer.vchcode and tbwtd.detail_id = buyer.detail_id and tbwt.profile_id = buyer.profile_id
        left join acc_periodcost cost on tbwt.profile_id = cost.profile_id and tbdc.cost_id = cost.id and tbdc.ptype_id = cost.ptype_id
    </sql>

    <select id="listBill" resultType="com.wsgjp.ct.sale.biz.jarvis.dto.DistributionGrossProfitStatisticsDTO">
        select
        <include refid="fields"/>
        <include refid="hotTable"/>
        <include refid="where"/>
        group by tbwt.btype_id,tbdc.ptype_id,tbdc.sku_id,tbda.unit_id
    </select>

    <select id="listPostedBill" resultType="com.wsgjp.ct.sale.biz.jarvis.dto.DistributionGrossProfitStatisticsDTO">
        select
        <include refid="fields"/>
        <include refid="coldTable"/>
        <include refid="where"/>
        group by tbwt.btype_id,tbdc.ptype_id,tbdc.sku_id,tbda.unit_id
    </select>

    <select id="listBillCount"
            resultType="com.wsgjp.ct.sale.biz.jarvis.dto.DistributionGrossProfitStatisticsDTO">
        select
        ifnull(sum(tbda.unit_qty) * -1,0) as unit_qty,
        sum(ifnull(tbda.currency_dised_taxed_total,0)) * -1                                          as currency_dised_taxed_total,
        sum(ifnull(buyer.buyer_dised_taxed_total,0) - ifnull(tbda.currency_dised_taxed_total,0)) * -1 as 'distributionGrossProfit',
        ROUND(sum(ifnull(buyer.buyer_dised_taxed_total,0) - ifnull(tbda.currency_dised_taxed_total,0)) /
        sum(ifnull(buyer.buyer_dised_taxed_total,0)) *
        100 ,2)                                                                                          as 'distributionGrossProfitRate',
        sum(ifnull(buyer.buyer_dised_taxed_total,0))*-1                                                  as 'buyerDisedTaxedTotal',
        sum(ifnull(buyer.buyer_dised_taxed_total,0)) / sum(ifnull(tbda.unit_qty,0))                      as 'buyerDisedTaxedAveragePrice',


        sum(ifnull(tbda.currency_dised_taxed_total,0)*-1)-
        (sum(abs(<include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
        <property name="bill_name" value="tbdc"/>
        <property name="ptype_name" value="ptype"/>
        <property name="cost_name" value="cost"/>
    </include>))+sum(ifnull(tbda.currency_order_fee_allot_total,0))) as 'grossProfit',

        ROUND((sum(ifnull(tbda.currency_dised_taxed_total,0)*-1)-
        (sum(abs(<include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
        <property name="bill_name" value="tbdc"/>
        <property name="ptype_name" value="ptype"/>
        <property name="cost_name" value="cost"/>
    </include>))+sum(ifnull(tbda.currency_order_fee_allot_total,0))))
        /sum(ifnull(tbda.currency_dised_taxed_total,0)*-1)*100,2) as 'grossProfitRate',

        sum(abs(<include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
        <property name="bill_name" value="tbdc"/>
        <property name="ptype_name" value="ptype"/>
        <property name="cost_name" value="cost"/>
    </include>))+sum(ifnull(tbda.currency_order_fee_allot_total,0)) as 'saleCostTotal',

        sum(abs(<include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
        <property name="bill_name" value="tbdc"/>
        <property name="ptype_name" value="ptype"/>
        <property name="cost_name" value="cost"/>
    </include>)) as 'costTotal'
        <if test="param.queryColdBill == true">
            <include refid="coldTable"/>
            <include refid="where"/>
        </if>
        <if test="param.queryColdBill == false">
            <include refid="hotTable"/>
            <include refid="where"/>
        </if>
    </select>
</mapper>