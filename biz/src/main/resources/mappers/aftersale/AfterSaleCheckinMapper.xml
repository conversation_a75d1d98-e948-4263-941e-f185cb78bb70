<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.aftersale.AfterSaleCheckinMapper">
    <select id="queryRefundReceiveCheckInDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        select
        d.id,
        d.vchcode,
        d.profile_id,
        d.ptype_id,
        d.sku_id,
        d.unit,
        d.qty,
        d.sub_qty,
        d.unit_qty,
        inout_detail.combo_inout_detail_id as combo_row_id,
        d.create_time,
        d.update_time,
        inout_detail.quality_state as goods_state,
        checkin.ktype_id,
        inout_detail.sub_unit_name as subUnit,
        inout_detail.sub_qty as subQty,
        bp.standard as ptypeStandard,
        bp.ptype_type,
        brand.brand_name,
        d.loss_vchcode,
        d.other_stock_in_vchcode,
        d.batchno as 'batchNo',
        d.produce_date,
        d.expire_date,
        d.apply_refund_total,
        d.apply_refund_taxed_total,
        d.apply_refund_tax_total,
        d.apply_refund_freight_fee,
        d.apply_refund_mall_fee,
        d.apply_refund_service_fee,
        d.dised_taxed_price,
        d.tax_rate,
        d.batch_price
        ,bp.pcategory,
        inout_detail.quality_state as oldGoodsState,
        bp.fullname as ptypeName,bp.ptype_type,bp.shortname
        as ptypeShortName,bp.usercode as ptypeCode,bp.standard as ptypeStandard,bp.memo as ptypeMemo,bp.protect_days,bp.protect_days_unit,bp.protect_days_view,
        bp.propenabled,bp.cost_mode,d.cost_period,
        bpx.xcode as xcode,bpf.fullbarcode as barcode,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name as unitName,unit.unit_rate as unitRate,
        price.preprice1, price.preprice2, price.preprice3, price.preprice4, price.preprice5,
        price.preprice6, price.preprice7, price.preprice8,price.preprice9,price.retail_price,price.preprice10,
        IF(IFNULL(bps.pic_url,'')='', pic.pic_url, bps.pic_url) as picUrl,
        `ktype`.fullname AS 'ktypeName',bp.snenabled,bp.batchenabled,unit.unit_name ASunitName,unit.unit_rate AS unitRate,
        bsunit.unit_name AS baseUnitName,
        bpcd.gifted as gift,
        d.inout_detail_id,
        d.inout_order_id,
        d.cost_period,
        d.cost_price,
        d.source_vchcode,
        d.source_detail_id,
        inout_detail.memo,
        d.purchase_total,d.purchase_price,checkin.checkin_number,
        d.apply_refund_unit_qty,
        d.combo_share_scale,
        checkin.checkin_number as checkinNumber
        from pl_eshop_refund_receive_checkin_detail d
        LEFT JOIN pl_eshop_refund_receive_checkin checkin on checkin.profile_id = d.profile_id and checkin.vchcode = d.vchcode
        LEFT JOIN td_bill_inout_detail inout_detail ON inout_detail.profile_id = d.profile_id AND inout_detail.inout_detail_id = d.inout_detail_id
        LEFT JOIN pl_eshop_refund_receive_checkin_detail_combo AS perrcdc ON perrcdc.profile_id=d.profile_id AND perrcdc.combo_row_id=inout_detail.combo_inout_detail_id
        LEFT JOIN base_ptype_combo_detail AS bpcd ON bpcd.profile_id=perrcdc.profile_id AND bpcd.combo_id= perrcdc.combo_id AND bpcd.sku_id=inout_detail.sku_id
        left join base_ptype bp on bp.profile_id=d.profile_id and inout_detail.ptype_id=bp.id
        left join base_brandtype brand on bp.brand_id = brand.id and bp.profile_id = brand.profile_id
        left join base_ptype_xcode bpx on bpx.profile_id=d.profile_id and bpx.sku_id=inout_detail.sku_id and bpx.unit_id=inout_detail.unit_id and
        bpx.ptype_id=inout_detail.ptype_id and bpx.defaulted=1
        left join base_ptype_sku bps on bps.profile_id=d.profile_id and bps.id=inout_detail.sku_id
        left join base_ptype_fullbarcode bpf on bpf.profile_id=d.profile_id and bpf.ptype_id=inout_detail.ptype_id and
        bpf.sku_id=inout_detail.sku_id and bpf.unit_id=inout_detail.unit_id AND bpf.defaulted=1
        left join base_ptype_unit unit on unit.profile_id=d.profile_id and unit.id=inout_detail.unit_id
        left join base_ptype_price price on price.profile_id=d.profile_id and price.unit_id=unit.id and price.sku_id = inout_detail.sku_id
        left join base_ptype_unit bsunit on bsunit.profile_id=d.profile_id and bsunit.ptype_id=inout_detail.ptype_id and
        bsunit.unit_code=1
        left join base_ptype_pic pic on pic.profile_id=d.profile_id and inout_detail.ptype_id=pic.ptype_id and pic.rowindex=1
        LEFT JOIN base_ktype ktype ON ktype.id=checkin.ktype_id AND ktype.profile_id=d.profile_id
        where d.profile_id=#{profileId}
        and d.vchcode in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
    </select>
    <select id="getSerialInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsSerialEntity">
        select * from
        pl_eshop_refund_receive_checkin_detail_serialno
        where profile_id=#{profileId}
        detail_id in
        <foreach collection="detailIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
    </select>
    <select id="queryRefundReceiveCheckInComboDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInCombo">
        select
        pl.combo_row_id,pl.vchcode,pl.profile_id,pl.combo_id,
        pl.qty,pl.create_time,pl.update_time,pl.goods_state,pl.ktype_id,pl.goods_state as 'oldGoodsState',
        pl.apply_refund_total,
        pl.apply_refund_taxed_total,
        pl.apply_refund_tax_total,
        pl.apply_refund_freight_fee,
        pl.apply_refund_mall_fee,
        pl.apply_refund_service_fee,
        pl.dised_taxed_price,
        ptype.fullname as ptypeName,ptype.shortname as ptypeShortName,ptype.id as ptypeId,'' as xcode,ptype.barcode,
        ptype.propenabled,
        pic.pic_url,ptype.usercode as ptypeCode,
        combo.need_print_name,checkin.checkin_number,
        pl.apply_refund_unit_qty
        from pl_eshop_refund_receive_checkin_detail_combo pl
        LEFT JOIN pl_eshop_refund_receive_checkin checkin on checkin.profile_id = pl.profile_id and checkin.vchcode = pl.vchcode
        left join base_ptype_combo combo on combo.combo_id = pl.combo_id and combo.profile_id = pl.profile_id
        left join base_ptype ptype on ptype.profile_id=pl.profile_id and pl.combo_id=ptype.id
        left join base_ptype_pic pic on pic.profile_id=pl.profile_id and pl.combo_id=pic.ptype_id and pic.rowindex=1
        where pl.profile_id=#{profileId}
        and pl.vchcode in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>

    </select>
    <select id="queryRefundReceiveCheckInVchcode" resultType="java.math.BigInteger">
        select ck.vchcode from pl_eshop_refund_receive_checkin ck
        join  pl_eshop_refund_checkin_relation rela on rel.profile_id=ck.profile_id and rel.refund_order_id=ck.vchcode
        where ck.profile_id=#{profileId}
        and rel.refund_order_id in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
        />
    </select>
</mapper>