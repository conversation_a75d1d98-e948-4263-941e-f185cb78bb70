<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.aftersale.AfterSaleFreightInterceptMapper">
    <select id="quereyAfterSaleFreightInterceptList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.freightintercept.AfterSaleFreightInterceptEntity">
        select tdf.freight_btype_id,
               bb.fullname as freightName,
               tdf.freight_billno as fregihtNo,
               r.trade_refund_order_number as refundNumber,
               e.fullname as eshopName,
               e.otype_id as otypeId,
               r.profile_id,
               peso.trade_order_id,
               tdf.warehouse_task_id,
               task.task_number as warehouseTaskNumber,
               task.full_link_status
               from pl_eshop_refund r
                   left join pl_eshop_refund_extend pere on pere.profile_id=r.profile_id and pere.refund_order_id=r.id
               left join td_bill_deliver tbc on tbc.profile_id=r.profile_id and tbc.order_id=r.eshop_order_id
               left join td_deliver_freight_info tdf on tdf.profile_id=tbc.profile_id and tdf.vchcode=tbc.vchcode
            left join td_bill_warehouse_task task on task.profile_id=tdf.profile_id and task.warehouse_task_id=tdf.warehouse_task_id
               left join base_btype bb on bb.id=tdf.freight_btype_id and bb.profile_id=tdf.profile_id
               left join pl_eshop e on e.profile_id=r.profile_id and e.otype_id=r.otype_id
               left join pl_eshop_sale_order peso on r.profile_id=peso.profile_id and r.eshop_order_id=peso.id
                left join pl_eshop_refund_freight_intercept rfi on rfi.profile_id=r.profile_id and rfi.refund_order_id=r.id and rfi.otype_id=r.otype_id
                and rfi.deliver_freight_info_id=tdf.id
     <include refid="interceptQueryWhere"></include>

    </select>
    <sql id="interceptQueryWhere">
        where r.profile_id=#{param.profileId} and peso.local_trade_state in(2,3)  and r.refund_type=1 and pere.platform_refund_type=0
        <if test="param.otypeIds != null and param.otypeIds.size() > 0">
            and r.otype_id in
            <foreach item="item" index="index" collection="param.otypeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.timeType == 0">
            and r.create_time between #{param.beginTime} and #{param.endTime}
        </if>
        <if test="param.timeType == 1">
            and tdf.deliver_time between #{param.beginTime} and #{param.endTime}
        </if>
        <if test="param.timeType == 2">
            and rfi.begin_intercept_time between #{param.beginTime} and #{param.endTime}
        </if>
        <if test="param.batchQueryKeyList!=null and param.batchQueryKeyList.size()>0">
           <if test="param.interceptBatchQueryType==0">
               and tdf.freight_billno in
               <foreach item="item" index="index" collection="param.batchQueryKeyList" open="(" separator="," close=")">
                   #{item}
               </foreach>
           </if>
            <if test="param.interceptBatchQueryType==1">
                and peso.trade_order_id in
                <foreach item="item" index="index" collection="param.batchQueryKeyList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.interceptBatchQueryType==2">
                and r.trade_refund_order_number in
                <foreach item="item" index="index" collection="param.batchQueryKeyList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </if>
        <if test="param.interceptStatusList!=null and param.interceptStatusList.size()>0">
            and rfi.freight_intercept_status in
            <foreach item="item" index="index" collection="param.interceptStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>