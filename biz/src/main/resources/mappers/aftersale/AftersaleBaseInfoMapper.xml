<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.aftersale.AftersaleBaseInfoMapper">
    <select id="getAtypeDeletedList" resultType="java.math.BigInteger">
        select id from base_atype where profile_id=#{profileId} and (deleted=1 or stoped =1);
    </select>
</mapper>