<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.aftersale.AfterSaleMapper">
    <update id="batchEditRefund"
            parameterType="com.wsgjp.ct.sale.biz.refund.refund.entity.request.UpdateRefundDutyRequest">
        update pl_eshop_refund
        <set>
            <if test="refundDutyIdsCheck">
                refund_duty_ids = #{refundDutyIds},
            </if>
            <if test="refundReasonCheck">
                refund_reason = #{reasonId},
            </if>
            <if test="refundRemarkCheck">
                refund_statement = #{remark},
            </if>
            <if test="edCommentCheck">
                memo = #{memo},
            </if>
            <if test="refundKtypeCheck">
                ktype_id = #{ktypeId},
            </if>
        </set>
        where profile_id = #{profileId} and id in
        <foreach item="id" collection="vchcodes" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="queryRefundApplyDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        select 0 AS selected, rd.*,
        bp.fullname as ptype_name,bp.ptype_type,bp.ptype_area,
        bp.shortname as ptypeShortName,
        bp.usercode as ptype_code,bp.pcategory as pcategory,
        /* bp.barcode,*/
        bp.ptype_width,bp.ptype_length,bp.ptype_height,bp.weight as
        ptype_weight,bp.length_unit,bp.weight_unit,bp.standard as ptype_standard,
        bpx.xcode as xcode,bpf.fullbarcode as barcode,bp.sub_unit,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name,unit.unit_rate,price.retail_price,price.min_sale_price,
        bsunit.unit_rate,bsunit.unit_name as base_unit_name,
        pic.pic_url,bp.protect_days, bp.cost_price,bp.snenabled,bp.snenabled as
        snenabledFromDb,bp.batchenabled,bp.propenabled,bp.protect_days_unit,bp.protect_days_view,
        bp.cost_mode,bp.sub_unit as subUnit,
        rd.cost_period,rd.cost_state,rd.cost_price,
        rd.sub_qty AS subQty,
        refund.confirm_state,refund.receive_state as receive_status,refund.deleted,refund.refund_type,
        ifnull(rd.refund_state,refund.refund_state) as refund_state,refund.trade_refund_order_number,
        refund.otype_id,
        ifnull(rd.platform_pic_url,'') as platform_pic_url,rd.batchNo,rd.batch_price as batchPrice,
        rrcd.goods_state,rd.actual_receipt_number as actualReceiptNumber,
        rrcd.vchcode as checkinVchcode,rrcd.id as checkinDetailId,
        rd.gift,refund.refund_phase as refundPhase,refund.refund_type as refundTypeEnum,
        rd.dised_taxed_total as disedTaxedTotal,rd.sku_id,rd.unit,
        rd.re_send_state,
        rd.memo, rd.order_detail_qty,
        peraddb.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        peraddb.buyer_dised_taxed_total as distributionDisedTaxedTotal,
        brand.brand_name
        ,ex.eshop_order_id
        ,ex.refund_save_principal_total,ex.refund_save_present_total,ex.refund_save_total,ex.refund_preference_total,ex.refund_preference_allot_total
        from pl_eshop_refund_apply_detail rd
        left join pl_eshop_refund_apply_detail_extend ex on ex.profile_id = rd.profile_id and ex.refund_detail_id = rd.id
        left join pl_eshop_refund_apply_detail_combo as rdc on rdc.profile_id=rd.profile_id and
        rd.combo_row_id=rdc.id
        join pl_eshop_refund refund on refund.id=rd.refund_order_id and refund.profile_id=rd.profile_id
        left join base_ptype bp on bp.profile_id=rd.profile_id and rd.ptype_id=bp.id
        left join base_ptype_xcode bpx on bpx.profile_id=rd.profile_id and bpx.sku_id=rd.sku_id and bpx.unit_id=rd.unit
        and bpx.defaulted=1
        left join base_ptype_sku bps on bps.profile_id=rd.profile_id and bps.id=rd.sku_id
        left join base_ptype_pic pic on pic.profile_id=rd.profile_id and rd.ptype_id=pic.ptype_id and pic.rowindex=1
        left join base_ptype_fullbarcode bpf on bpf.profile_id=rd.profile_id and bpf.ptype_id=rd.ptype_id and
        bpf.sku_id=rd.sku_id and bpf.unit_id=rd.unit and bpf.defaulted=1
        left join base_ptype_unit unit on unit.profile_id=rd.profile_id and unit.id=rd.unit
        left join base_ptype_price price on price.profile_id=rd.profile_id and price.unit_id=unit.id and
        unit.ptype_id=rd.ptype_id and price.sku_id = rd.sku_id
        left join base_ptype_unit bsunit on bsunit.profile_id=rd.profile_id and bsunit.ptype_id=rd.ptype_id and
        bsunit.unit_code=1
        left join pl_eshop_refund_receive_checkin_detail rrcd on rd.check_detail_id=rrcd.id and rd.profile_id =
        rrcd.profile_id
        left join pl_eshop_refund_apply_detail_distribution_buyer peraddb on peraddb.profile_id = rd.profile_id and
        peraddb.refund_order_detail_id = rd.id
        left join base_brandtype brand on bp.profile_id = brand.profile_id and bp.brand_id = brand.id
        where rd.profile_id=#{profileId}
        and rd.refund_order_id in
        <foreach collection="refundOrderIdList" separator="," open="(" close=")" item="detailId" index="0">
            #{detailId}
        </foreach>

    </select>
    <select id="queryRefundDetailList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail"></select>
    <select id="queryRefundDetailSerialInfos"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundDetailSerialNo">
        select
        id, profile_id, snno, sn1, sn2, sn3, sn_memo, create_time, update_time, refund_order_id,
        <if test="tableName == 'pl_eshop_refund_send_detail_serialno'">
            refund_order_send_detail_id as detailId
        </if>
        <if test="tableName == 'pl_eshop_refund_apply_detail_serialno'">
            refund_order_detail_id as detailId
        </if>
        from ${tableName}
        where profile_id = #{profileId}
        <if test="tableName == 'pl_eshop_refund_send_detail_serialno'">
            and refund_order_send_detail_id in
                <foreach collection="detailIds" separator="," open="(" close=")" item="detailId" index="0">
                    #{detailId}
                </foreach>
        </if>
        <if test="tableName == 'pl_eshop_refund_apply_detail_serialno'">
            and refund_order_detail_id  in
            <foreach collection="detailIds" separator="," open="(" close=")" item="detailId" index="0">
                #{detailId}
            </foreach>
        </if>
    </select>
    <select id="queryRefundComboDetailList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetailCombo">
        select rdc.*,
        ptype.fullname as ptype_name,ptype.id as ptypeId,ptype.usercode as ptype_code,
        ptype.barcode,ptype.sub_unit,ptype.weight_unit,ptype.weight as ptype_weight,ptype.propenabled,
        pic.pic_url,ptype.cost_price,rdc.price as unit_price,rdc.qty as unit_qty,
        rdc.actual_receipt_number as actualReceiptNumber,1 as unit_rate,bpcd.gifted as gift,rdc.order_detail_qty,
        distrbution_apply_combo.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        distrbution_apply_combo.buyer_dised_taxed_total as distributionDisedTaxedTotal
        from pl_eshop_refund_apply_detail_combo rdc
        left join base_ptype ptype on ptype.profile_id=rdc.profile_id and rdc.combo_id=ptype.id
        left join base_ptype_combo_detail bpcd on bpcd.profile_id=ptype.profile_id and ptype.id = bpcd.ptype_id
        left join base_ptype_pic pic on pic.profile_id=rdc.profile_id and rdc.combo_id=pic.ptype_id and pic.rowindex=1
        left join pl_eshop_refund_apply_detail_combo_distribution_buyer distrbution_apply_combo on
        distrbution_apply_combo.profile_id = rdc.profile_id and
        distrbution_apply_combo.refund_order_combo_detail_id = rdc.id and distrbution_apply_combo.refund_order_id =
        rdc.refund_order_id
        where rdc.profile_id=#{profileId}
        and rdc.refund_order_id in
        <foreach collection="refundIds" separator="," open="(" close=")" item="detailId" index="0">
            #{detailId}
        </foreach>
    </select>
    <select id="queryRefundFreights"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundFreight">
        SELECT frt.id,
               frt.profile_id,
               frt.refund_order_id,
               frt.freight_template_id,
               frt.freight_no,
               frt.create_time,
               frt.update_time,
               CASE IFNULL(frt.freight_btype_id, 0)
                   WHEN 0 THEN lg.freight_btype_id
                   ELSE frt.freight_btype_id END AS freight_btype_id,
               temp.template_name                AS freight_template_name,
               frt.freight_name,
               frt.freight_code,
               frt.freight_type,
               frt.freight_intercept_status
        FROM pl_eshop_refund_freight frt
                 LEFT JOIN td_template temp ON temp.profile_id = frt.profile_id AND temp.id = frt.freight_template_id
                 LEFT JOIN td_logistics_template lg
                           ON temp.`id` = lg.`template_id` AND temp.`profile_id` = lg.`profile_id`
                 LEFT JOIN base_btype btype ON btype.profile_id = frt.profile_id AND btype.id = lg.freight_btype_id
                 LEFT JOIN base_btype btype1 ON btype1.profile_id = frt.profile_id AND btype1.id = frt.freight_btype_id
        WHERE frt.profile_id = #{profileId}
          and frt.refund_order_id in
        <foreach collection="refundIds" separator="," open="(" close=")" item="detailId" index="0">
            #{detailId}
        </foreach>
    </select>
    <select id="queryRefundSendDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundSendDetail">
        SELECT
        detail.id, detail.profile_id, detail.ptype_id, detail.sku_id, detail.unit, detail.sub_unit, detail.qty,
        detail.unit_qty, detail.sub_qty, detail.goods_state, detail.create_time, detail.update_time, detail.batchno,
        detail.expire_date, detail.produce_date, detail.price, detail.total,
        detail.taxed_price, detail.taxed_total, detail.dised_taxed_price, detail.dised_taxed_total, detail.combo_row_id,
        ptype.tax_rate, detail.combo_share_scale, detail.cost_id, detail.refund_order_id, detail.batch_price,
        detail.platform_num_id, detail.platform_sku_id, detail.platform_full_name,
        detail.platform_properties_name, detail.platform_xcode, detail.mapping_state,
        ptype.pcategory,
        ptype.fullname AS ptype_name,ptype.ptype_type,ptype.ptype_area,ptype.shortname AS ptypeShortName,
        ptype.usercode AS ptype_code, ptype.ptype_width,ptype.ptype_length,ptype.ptype_height,
        ptype.weight AS ptype_weight,ptype.length_unit,ptype.weight_unit,ptype.propenabled,
        ptype.standard AS ptype_standard, xcode.xcode,fcode.fullbarcode AS barcode,
        sku.prop_name1,sku.propvalue_name1,sku.prop_id1,sku.propvalue_id1,
        sku.prop_name2,sku.propvalue_name2,sku.prop_id2,sku.propvalue_id2,
        sku.prop_name3,sku.propvalue_name3,sku.prop_id3,sku.propvalue_id3,
        sku.prop_name4,sku.propvalue_name4,sku.prop_id4,sku.propvalue_id4,
        sku.prop_name5,sku.propvalue_name5,sku.prop_id5,sku.propvalue_id5,
        sku.prop_name6,sku.propvalue_name6,sku.prop_id6,sku.propvalue_id6,
        unit.unit_name,unit.unit_rate,price.retail_price,price.min_sale_price,
        bsunit.unit_name AS
        base_unit_name,pic.pic_url,ptype.protect_days,ptype.protect_days_unit,ptype.protect_days_view,
        ptype.snenabled,ptype.batchenabled,ptype.cost_mode,ptype.sub_unit as subUnit,
        detail.gift,
        detail_distribution.buyer_dised_taxed_total as distributionDisedTaxedTotal,
        detail_distribution.buyer_dised_taxed_price as distributionDisedTaxedPrice,
        ptype_brand.brand_name
        from pl_eshop_refund_send_detail detail
        left join pl_eshop_refund_send_detail_distribution_buyer detail_distribution on detail_distribution.profile_id =
        detail.profile_id and
        detail_distribution.refund_order_detail_id = detail.id and detail_distribution.refund_order_id =
        detail.refund_order_id
        left join pl_eshop_refund_send_detail_combo as combo on combo.profile_id=detail.profile_id and
        detail.combo_row_id=combo.id
        <include refid="detailJoinFromOrder"/>
        WHERE detail.profile_id=#{profileId} AND detail.refund_order_id in
        <foreach collection="refundIds" separator="," open="(" close=")" item="detailId" index="0">
            #{detailId}
        </foreach>
    </select>
    <select id="queryRefundSendDetailCombos"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundSendDetailCombo">
        select combo.*,
               ptype.fullname                             as ptype_name,
               ptype.id                                   as ptypeId,
               ptype.usercode                             as ptype_code,
               ptype.barcode,
               ptype.weight_unit,
               ptype.weight                               as ptype_weight,
               pic.pic_url,
               ptype.cost_price,
               ptype.propenabled,
               combo.qty                                  as unit_qty,
               1                                          as unit_rate,
               true                                       as comboRow,
               combo_distribution.buyer_dised_taxed_total as distributionDisedTaxedTotal,
               combo_distribution.buyer_dised_taxed_price as distributionDisedTaxedPrice
        from pl_eshop_refund_send_detail_combo combo
                 left join pl_eshop_refund_send_detail_combo_distribution_buyer combo_distribution
                           on combo_distribution.profile_id = combo.profile_id and
                              combo_distribution.refund_order_id = combo.refund_order_id and
                              combo_distribution.refund_order_combo_detail_id = combo.id
                 left join base_ptype ptype on ptype.profile_id = combo.profile_id and combo.combo_id = ptype.id
                 left join base_ptype_pic pic
                           on pic.profile_id = combo.profile_id and combo.combo_id = pic.ptype_id and pic.rowindex = 1
        where combo.profile_id = #{profileId}
          AND combo.refund_order_id  in
        <foreach collection="refundIds" separator="," open="(" close=")" item="detailId" index="0">
            #{detailId}
        </foreach>
    </select>
    <sql id="refundListColumn">
        distinct refund.id,
        refund.trade_refund_order_id,
        refund.trade_refund_order_number,
        refund.profile_id,
        refund.otype_id,
        refund.buyer_id,
        refund.create_time as create_time,
        refund.cost_state,
        refund.etype_id,
        e.fullname as etype_name,
        refund.refund_create_time,
        refund.refund_modify_time,
        refund.trade_create_time,
        refund.trade_finish_time,
        refund.has_submit_to_wms,
        refund.trade_pay_time,
        refund.order_fee,
        refund.business_type,
        ba.id as atypeId,
        ba.fullname as atypeName,
        case
        when refund.refund_type = 0 and refund.refund_phase = 0 then 5
        else refund.refund_type
        end
        as refund_type,
        refund.create_type,
        refund.confirm_state,
        refund.confirm_time,
        refund.confirm_remark,
        refund.confirm_etype_id,
        refund.receive_state,
        refund.receive_buyer_id,
        refund.receive_remark,
        refund.refund_process_state,
        refund.refund_process_time,
        refund.receive_time,
        refund.pay_state,
        refund.pay_account,
        refund.pay_time,
        refund.pay_etype_id,
        refund.pay_number,
        refund.receive_account,
        refund.refund_apply_total,
        refund.refund_apply_taxed_total,
        refund.refund_apply_tax_total,
        refund.refund_apply_freight_fee,
        refund.refund_apply_mall_fee,
        refund.refund_apply_service_fee,
        refund.update_time,
        refund.ktype_id,
        refund.old_ktype_id,
        refund.refund_state,
        refund.refund_phase,
        refund.refund_statement,
        refund.trade_status,
        refund.deleted,
        refund.trade_order_id,
        refund.eshop_order_id,
        refund.no_detail,
        refund.bill_vchcode,
        refund.bill_poseted,
        refund.`refund_apply_taxed_total` as refundTotal,
        case
        when refund.btype_id = 0 or refund.btype_id is null
        then order.btype_id
        else refund.btype_id
        end
        as btypeId,
        refund.refund_type as 'refund_type_enum',
        IFNULL(reason.refund_reason, refund.refund_reason) as refund_reason,
        IFNULL(reason.id, refund.refund_reason) as reason_id,
        refund.has_edit,
        refund.mapping_state,
        case refund.bill_total
        when 0 then refund.refund_apply_taxed_total
        else refund.bill_total
        end
        as bill_total,
        case refund.bill_service_fee
        when 0 then refund.refund_apply_service_fee
        else refund.bill_service_fee
        end
        as bill_service_fee,
        refund.order_etype_id,
        e.fullname as orderEtypeName,
        ifnull(e.fullname, '') as order_etype_name,
        ifnull(receiveBuyer.ai, buyer.ai) as 'buyer.ai',
        ifnull(receiveBuyer.addri, buyer.addri) as 'buyer.addri',
        ifnull(receiveBuyer.ri, buyer.ri) as 'buyer.ri',
        buyer.di as 'buyer.di',
        ifnull(receiveBuyer.customer_receiver, buyer.customer_receiver) as 'buyer.customer_receiver',
        ifnull(receiveBuyer.customer_receiver_mobile, buyer.customer_receiver_mobile) as 'buyer.customer_receiver_mobile',
        ifnull(receiveBuyer.customer_receiver_country, buyer.customer_receiver_country) as 'buyer.customer_receiver_country',
        ifnull(receiveBuyer.customer_receiver_city, buyer.customer_receiver_city) as 'buyer.customer_receiver_city',
        ifnull(receiveBuyer.customer_receiver_province, buyer.customer_receiver_province) as 'buyer.customer_receiver_province',
        ifnull(receiveBuyer.customer_receiver_district, buyer.customer_receiver_district) as 'buyer.customer_receiver_district',
        ifnull(receiveBuyer.customer_receiver_town, buyer.customer_receiver_town) as 'buyer.customer_receiver_town',
        ifnull(receiveBuyer.customer_receiver_address, buyer.customer_receiver_address) as 'buyer.customer_receiver_address',
        ifnull(receiveBuyer.customer_receiver_full_address, buyer.customer_receiver_full_address) as 'buyer.customer_receiver_full_address',
        ifnull(receiveBuyer.customer_shop_account, buyer.customer_shop_account) as 'buyer.customer_shop_account',
        receiveBuyer.customer_shop_account as 'receiveBuyer.customer_shop_account',
        receiveBuyer.ai as 'receiveBuyer.ai',
        receiveBuyer.di as 'receiveBuyer.di',
        receiveBuyer.addri as 'receiveBuyer.addri',
        receiveBuyer.ri as 'receiveBuyer.ri',
        receiveBuyer.customer_receiver as 'receiveBuyer.customer_receiver',
        receiveBuyer.customer_receiver_mobile as 'receiveBuyer.customer_receiver_mobile',
        receiveBuyer.customer_receiver_country as 'receiveBuyer.customer_receiver_country',
        receiveBuyer.customer_receiver_city as 'receiveBuyer.customer_receiver_city',
        receiveBuyer.customer_receiver_province as 'receiveBuyer.customer_receiver_province',
        receiveBuyer.customer_receiver_district as 'receiveBuyer.customer_receiver_district',
        receiveBuyer.customer_receiver_town as 'receiveBuyer.customer_receiver_town',
        receiveBuyer.customer_receiver_address as 'receiveBuyer.customer_receiver_address',
        receiveBuyer.customer_receiver_full_address as 'receiveBuyer.customer_receiver_full_address',
        eshop.fullname as otype_name,
        eshop.fullname as 'eshopInfo.fullname',
        eshop.eshop_type as 'eshopInfo.eshopType',
        order.seller_memo as 'saleOrder.seller_memo',
        order.buyer_message as 'saleOrder.buyer_message',
        order.seller_flag as 'saleOrder.seller_flag',
        order.mapping_state as `saleOrder.mapping_state`,
        order.platform_store_id as 'saleOrder.platform_store_id',
        order.ktype_id as `saleOrder.ktype_id`,
        order.deliver_process_type as `saleOrder.deliver_process_type`,
        order.dised_taxed_total as `disedTaxedTotal`,
        refund.platform_parent_order_id,
        refund.eshop_order_id as eshopOrderId,
        ktype1.fullname as 'ktype_name',
        ktype2.fullname as 'oldKtypeName',
        freight.freight_name as 'checkinFreightName',
        freight.freight_no as 'checkinFreightBillNo',
        refund.memo as memo,
        freight.freight_name as refundCompany,
        freight.freight_no as refundCompanyNumber,
        freight.freight_btype_id as freightBtypeId,
        freight.freight_code as freightCode,
        has_commit_to_distributor,
        refund.platform_refund_state,
        refund.platform_confirm_state,
        refund.platform_change_state,
        refund.platform_return_state,
        refund.supplier_id,
        e.dtype_id,
        timing.id as 'refundTiming.id',
        timing.profile_id as 'refundTiming.profileId',
        timing.eshop_id as 'refundTiming.eshopId',
        timing.sys_promised_confirm_time as 'refundTiming.sysPromisedConfirmTime',
        timing.promised_confirm_time as 'refundTiming.promisedConfirmTime',
        timing.promised_agree_time as 'refundTiming.promisedAgreeTime',
        timing.promised_deliver_time as 'refundTiming.promisedDeliverTime',
        timing.promised_receive_time as 'refundTiming.promisedReceiveTime',
        timing.refund_id as 'refundTiming.refundId',
        refund.refund_duty_ids,
        e2.fullname as confirmEtypeName,
        refund.refund_finish_time,
        exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and profile_id = refund.profile_id) as related_checkin,
        refund_extend.platform_refund_version,refund_extend.platform_auto_intercept_agree,
        refund_extend.receive_etype_id,refund_extend.process_etype_id,
        refund_extend.order_detail_total,
        perdb.buyer_dised_taxed_total as distributionDisedTaxedTotal,
        refund_extend.return_sync_state,
        if(timing.promised_receive_time &lt; if( timing.promised_deliver_time &lt; if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)),timing.promised_deliver_time,if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time))),timing.promised_receive_time,        if( timing.promised_deliver_time &lt; if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)),timing.promised_deliver_time,if(timing.promised_agree_time &lt; if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time),timing.promised_agree_time,if(timing.sys_promised_confirm_time &lt; timing.promised_confirm_time,timing.sys_promised_confirm_time,timing.promised_confirm_time)))) as order_time_tag,
        reasonPlatform.refund_reason as platformRefundReasonStr,
        reasonPlatform.id as platformRefundReason,
        refund_extend.platform_refund_type as platformRefundType,
        refund_extend.order_type,
        if(ktype1.scategory = 2,true,false) as toWms,
        refund_extend.platform_sign_status,
        btype.fullname as 'btypeName',
        btype_supplier.fullname as 'supplierName',
        refund_extend.refund_order_detail_summary as refundOrderDetailSummaryId,
        big_data.big_data as refundOrderDetailSummaryGet,
        refund_extend.cycle,
        refund_extend.refund_save_total as 'refundSaveTotal',
        refund_extend.refund_save_present_total as 'refundSavePresentTotal',
        refund_extend.refund_save_principal_total as 'refundSavePrincipalTotal',
        refund_extend.refund_advance_total as 'refundAdvanceTotal'
        ,refund_extend.refund_national_subsidy_total
        ,refund_extend.refund_buyer_apply_total
        ,refund_extend.refund_platform_amount
        ,refund_extend.original_refund_platform_amount
        ,refund_extend.original_refund_national_subsidy_total,
        if(refund_extend.refund_pay_type=2,true,false) as 'advancePay',
        refund_extend.refund_pay_type,
        refund_extend.refund_advance_total
        ,refund_extend.system_refund_state,
        if(ktype1.scategory = 2,true,false) as 'wmsKtype'
    </sql>
    <select id="queryRefundList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        SELECT
        <include refid="refundListColumn"/>
        FROM pl_eshop_refund refund
        LEFT JOIN pl_eshop_sale_order on order.profile_id = refund.profile_id and order.id = refund.eshop_order_id
        LEFT JOIN pl_buyer buyer on buyer.profile_id = refund.profile_id and buyer.buyer_id = refund.buyer_id
        LEFT JOIN pl_buyer receiveBuyer on receiveBuyer.buyer_id = refund.receive_buyer_id and
        receiveBuyer.profile_id = refund.profile_id
        LEFT JOIN pl_eshop eshop on eshop.profile_id = refund.profile_id and eshop.otype_id = refund.otype_id
        LEFT JOIN `base_otype` o ON o.profile_id = refund.profile_id AND o.id = refund.otype_id
        LEFT JOIN pl_eshop_refund_config_reason reason on reason.profile_id = refund.profile_id and
        refund.refund_reason = reason.id
        LEFT JOIN base_etype e on e.profile_id = refund.profile_id and e.id = refund.etype_id
        LEFT JOIN base_etype e2 on e2.profile_id = refund.profile_id and e2.id = refund.confirm_etype_id
        LEFT JOIN base_atype ba on ba.profile_id = refund.profile_id and ba.id = o.atype_id
        LEFT JOIN pl_eshop_refund_freight freight on freight.profile_id = refund.profile_id and
        freight.refund_order_id = refund.id and freight.freight_type != 1
        LEFT JOIN base_ktype ktype1 on refund.profile_id = ktype1.profile_id and refund.ktype_id = ktype1.id
        LEFT JOIN base_ktype ktype2 on refund.profile_id = ktype2.profile_id and refund.old_ktype_id = ktype2.id
        LEFT JOIN pl_eshop_refund_timing timing on refund.profile_id = timing.profile_id and refund.id =
        timing.refund_id and refund.otype_id = timing.eshop_id
        LEFT JOIN pl_eshop_refund_extend refund_extend on refund.profile_id = refund_extend.profile_id and refund.id =
        refund_extend.refund_order_id
        LEFT JOIN pl_eshop_refund_config_reason reasonPlatform on reasonPlatform.profile_id = refund.profile_id and
        refund_extend.platform_refund_reason = reasonPlatform.id
        LEFT JOIN pl_eshop_refund_distribution_buyer perdb on perdb.profile_id = refund.profile_id and
        perdb.refund_order_id = refund.id
        left join base_btype btype on btype.profile_id = refund.profile_id and btype.id = refund.btype_id
        left join base_btype btype_supplier on btype_supplier.profile_id = refund.profile_id and btype_supplier.id = refund.supplier_id
        LEFT JOIN mark_data big_data on refund_extend.refund_order_detail_summary = big_data.id and
        refund_extend.profile_id = big_data.profile_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`refund`.profile_id and blsk.object_type=2 and
            `refund`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`refund`.profile_id and bls.object_type=3 and
            `refund`.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <include refid="refundListWhere"/>
    </select>
    <sql id="refundListWhere">
    WHERE refund.profile_id=#{profileId} AND (timing.id=0 or timing.id!=0 or timing.id is null or timing.id is not null) and refund.otype_id not in(select id from `base_otype` where
    profile_id=#{profileId} and ocategory=2 )
    <if test="agRefund == 1">
        AND refund_extend.ag_refund = 1 and refund.deleted = 0 and refund.refund_state not in (4,5,6)
    </if>
    <if test="unHandleRefund == 1">
        <if test="limitTime == 12">
            and NOW() > DATE_ADD(refund_create_time, interval 12 hour)
            and NOW() &lt; DATE_ADD(refund_create_time, interval 24 hour)
        </if>
        <if test="limitTime == 24">
            and NOW() > DATE_ADD(refund_create_time, interval 24 hour)
            and NOW() &lt; DATE_ADD(refund_create_time, interval 48 hour)
        </if>
        <if test="limitTime == 48">
            and NOW() > DATE_ADD(refund_create_time, interval 48 hour)
            and NOW() &lt; DATE_ADD(refund_create_time, interval 72 hour)
        </if>
        <if test="limitTime == 72">
            and NOW() > DATE_ADD(refund_create_time, interval 72 hour)
        </if>
        and refund.deleted = 0 and refund.refund_state not in (4,5,6) and refund.create_type = 1
    </if>
    <if test="specialType!=null">
        <if test="specialType == 0">
            and refund_extend.platform_refund_type = 5 and refund.refund_state = 1 and refund.deleted = 0
        </if>
        <if test="specialType == 1">
            and refund_extend.platform_refund_type = 0 and refund.refund_state = 1 and refund.deleted = 0
        </if>
        <if test="specialType == 2">
            and refund_extend.platform_refund_type = 1 and refund.refund_state = 1 and refund.deleted = 0
        </if>
        <if test="specialType == 3">
            and refund_extend.platform_refund_type = 1 and refund.refund_state = 2 and refund.deleted = 0
        </if>
        <if test="specialType == 4">
            and refund_extend.platform_refund_type = 1 and freight.freight_no !='' and freight.freight_name !='' and
            refund.deleted = 0 and refund.refund_state not in (4,5,6)
        </if>
        <if test="specialType == 100">
            and refund.refund_type in (0,1,2,3,4) and refund.confirm_state = 0 and refund.deleted = 0 and
            refund.refund_state not in (4,6)
        </if>
        <if test="specialType == 101">
            and refund.refund_type in (1,2) and refund.receive_state = 1 and refund.confirm_state = 1 and
            refund.deleted = 0 and refund.refund_state not in (4,6)
        </if>
        <if test="specialType == 102">
            and refund.refund_type in (0,1,6,7) and refund.pay_state = 1 and refund.confirm_state = 1 and
            refund.deleted = 0 and refund.refund_state not in (4,6)
        </if>
    </if>
    <if test="isSystemConfirm">
        and (refund.confirm_etype_id = 0 and refund.confirm_state in (1,2))
    </if>
    <if test="confirmEtypeIds!=null and confirmEtypeIds.size()>0">
        and refund.confirm_etype_id in
        <foreach collection="confirmEtypeIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    <if test="platformInterceptStatus!=null and platformInterceptStatus.size()>0">
        and refund.id in (select refund_order_id from pl_eshop_refund_freight where profile_id=#{profileId} and
        freight_intercept_status
        in
        <foreach collection="platformInterceptStatus" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </if>
    <if test="markCondition==0">
        <if test="(markType!=null and markType.size()>0) and (specialMarkType==null or specialMarkType.size()==0)">
            and (
            <foreach collection="markType" item="item" separator="or">
                <include refid="forMarkType"></include>
            </foreach>
            <if test="noMark == true">
                <include refid="notMark"/>
            </if>
            )
        </if>
        <if test="(markType==null or markType.size()==0) and (specialMarkType!=null and specialMarkType.size()>0)">
            AND
            (
            refund.id in (
            select mark.order_id from pl_eshop_order_mark mark where mark.profile_id = #{profileId} and
            mark.order_type
            = 2 AND mark.create_time &gt;=#{beginTime}
            AND mark.create_time &lt;=#{endTime}
            <if test="specialMarkType != null and specialMarkType.size() > 0">
                and mark.mark_code in
                <foreach item="type" collection="specialMarkType" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            )
            <if test="noMark == true">
                <include refid="notMark"/>
            </if>
            )
        </if>
        <if test="(markType!=null and markType.size()>0) and (specialMarkType!=null and specialMarkType.size()>0)">
            and (((
            <foreach collection="markType" item="item" separator="and">
                <include refid="forMarkType">
                </include>
            </foreach>
            )
            OR
            refund.id in (
            select mark.order_id from pl_eshop_order_mark mark where mark.profile_id = #{profileId} and
            mark.order_type
            = 2 AND mark.create_time &gt;=#{beginTime}
            AND mark.create_time &lt;=#{endTime}
            <if test="specialMarkType != null and specialMarkType.size() > 0">
                and mark.mark_code in
                <foreach item="type" collection="specialMarkType" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            ))
            <if test="noMark == true">
                <include refid="notMarkOnly"/>
            </if>
            )
        </if>
    </if>
    <if test="markCondition==1">
        <if test="(markType!=null and markType.size()>0) and (specialMarkType==null or specialMarkType.size()==0)">
            and !(
            <foreach collection="markType" item="item" separator="or">
                <include refid="forMarkType"></include>
            </foreach>
            <if test="noMark == true">
                <include refid="notMark"/>
            </if>
            )
        </if>
        <if test="(markType==null or markType.size()==0) and (specialMarkType!=null and specialMarkType.size()>0)">
            AND
            !(
            refund.id in (
            select mark.order_id from pl_eshop_order_mark mark where mark.profile_id = #{profileId} and
            mark.order_type
            = 2 AND mark.create_time &gt;=#{beginTime}
            AND mark.create_time &lt;=#{endTime}
            <if test="specialMarkType != null and specialMarkType.size() > 0">
                and mark.mark_code in
                <foreach item="type" collection="specialMarkType" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            )
            <if test="noMark == true">
                <include refid="notMark"/>
            </if>
            )
        </if>
        <if test="(markType!=null and markType.size()>0) and (specialMarkType!=null and specialMarkType.size()>0)">
            and !(((
            <foreach collection="markType" item="item" separator="or">
                <include refid="forMarkType">
                </include>
            </foreach>
            ) OR
            refund.id in (
            select mark.order_id from pl_eshop_order_mark mark where mark.profile_id = #{profileId} and
            mark.order_type
            = 2 AND mark.create_time &gt;=#{beginTime}
            AND mark.create_time &lt;=#{endTime}
            <if test="specialMarkType != null and specialMarkType.size() > 0">
                and mark.mark_code in
                <foreach item="type" collection="specialMarkType" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            ))
            <if test="noMark == true">
                <include refid="notMark"/>
            </if>
            )
        </if>
    </if>

    <if test="refundNoDetail != null">
        <if test="refundNoDetail.getCode() == 0">
            and refund.no_detail=0
        </if>
        <if test="refundNoDetail.getCode() == 2">
            and refund.no_detail>=0
        </if>
    </if>
    <if test="refundOrderId != null and refundOrderId != '' and refundOrderId > 0">
        AND refund.id=#{refundOrderId}
    </if>
    <if test="tradeOrderId != null and tradeOrderId != ''">
        AND refund.trade_order_id=#{tradeOrderId}
    </if>
    <if test="tradeIdList != null">
        AND refund.trade_order_id in
        <foreach collection="tradeIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </if>
    <if test="eshopOrderId != null and eshopOrderId!=0">
        AND refund.eshop_order_id = #{eshopOrderId}
    </if>
    <if test="relatedCheckin == 0">
        AND not exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and
        profile_id = refund.profile_id)
    </if>
    <if test="relatedCheckin == 1">
        AND exists (select * from pl_eshop_refund_checkin_relation where refund_order_id = refund.id and profile_id
        = refund.profile_id)
    </if>
    <if test="otypeId != null and otypeId > 0">
        AND refund.otype_id=#{otypeId}
    </if>
    <if test="tradeRefundOrderNumber != null and tradeRefundOrderNumber != ''">
        AND refund.trade_refund_order_number=#{tradeRefundOrderNumber}
    </if>
    <if test="refundIds != null and refundIds.size > 0">
        AND refund.id IN
        <foreach collection="refundIds" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </if>
    <if test="refundIdsForPass != null and refundIdsForPass.size > 0">
        AND refund.id IN
        <foreach collection="refundIdsForPass" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </if>
    <if test="refundIdsForPtype != null and refundIdsForPtype.size > 0">
        <choose>
            <when test="ptypeType == 0 or ptypeType == 1">
                AND refund.id IN
                <foreach collection="refundIdsForPtype" close=")" open="(" separator="," item="id">
                    #{id}
                </foreach>
            </when>
            <when test="ptypeType == 2">
                AND refund.id NOT IN
                <foreach collection="refundIdsForPtype" close=")" open="(" separator="," item="id">
                    #{id}
                </foreach>
            </when>
        </choose>
    </if>
    <if test="businessTypes != null and businessTypes.size > 0">
        AND refund.business_type IN
        <foreach collection="businessTypes" close=")" open="(" separator="," item="item">
            #{item}
        </foreach>
    </if>
    <if test="queryRefundOnlyOnSaleEnum != null and queryRefundOnlyOnSaleEnum.getCode() != 2">
        AND refund.refund_phase=#{queryRefundOnlyOnSaleEnum}
    </if>
    <if test="quickFilterType != null">
        <choose>
            <when test='quickFilterType == "0"'>
                AND extend.platform_refund_type=0
            </when>
            <when test='quickFilterType == "101"'>
                AND extend.platform_refund_type=0
                AND refund.confirm_state=0
            </when>
            <when test='quickFilterType == "102"'>
                AND refund.refund_type=0
                AND refund.pay_state=1
            </when>
            <when test='quickFilterType == "103"'>
                AND refund.refund_type=0
                AND refund.confirm_state=1
                AND refund.pay_state=2
            </when>
            <when test='quickFilterType == "1"'>
                AND refund.refund_type=1
            </when>
            <when test='quickFilterType == "111"'>
                AND refund.refund_type=1
                AND refund.confirm_state=0
            </when>
            <when test='quickFilterType == "112"'>
                AND refund.refund_type=1
                AND refund.receive_state=1
            </when>
            <when test='quickFilterType == "113"'>
                AND refund.refund_type=1
                AND refund.pay_state=1
            </when>
            <when test='quickFilterType == "114"'>
                AND refund.refund_type=1
                AND refund.confirm_state=1
                AND refund.receive_state=3
                AND
                refund.pay_state=2
            </when>

            <when test='quickFilterType == "2"'>
                AND refund.refund_type=2
            </when>
            <when test='quickFilterType == "121"'>
                AND refund.refund_type=2
                AND refund.confirm_state=0
            </when>
            <when test='quickFilterType == "122"'>
                AND refund.refund_type=2
                AND refund.receive_state=1
            </when>
            <when test='quickFilterType == "123"'>
                AND refund.refund_type=2
                AND refund.refund_process_state=0
            </when>
            <when test='quickFilterType == "124"'>
                AND refund.refund_type=2
                AND refund.confirm_state=1
                AND refund.receive_state=3
                AND
                refund.refund_process_state=1
            </when>
            <when test='quickFilterType == "3"'>
                AND refund.refund_type=3
            </when>
            <when test='quickFilterType == "131"'>
                AND refund.refund_type=3
                AND refund.confirm_state=0
            </when>
            <when test='quickFilterType == "132"'>
                AND refund.refund_type=3
                AND refund.refund_process_state=0
            </when>
            <when test='quickFilterType == "133"'>
                AND refund.refund_type=3
                AND refund.confirm_state=1
                AND refund.refund_process_state=1
            </when>
            <when test='quickFilterType == "4"'>
                AND refund.refund_type=4
            </when>
            <when test='quickFilterType == "141"'>
                AND refund.refund_type=4
                AND refund.confirm_state=0
            </when>
            <when test='quickFilterType == "142"'>
                AND refund.refund_type=4
                AND refund.refund_process_state=0
            </when>
            <when test='quickFilterType == "143"'>
                AND refund.refund_type=4
                AND refund.confirm_state=1
                AND refund.refund_process_state=1
            </when>
            <when test='quickFilterType == "5"'>
                AND refund.refund_phase=0
                AND refund.refund_type=5
            </when>
            <when test='quickFilterType == "6"'>
                AND refund.deleted=1
            </when>
            <when test='quickFilterType == "7"'>
                AND refund.business_type=2
            </when>
        </choose>
    </if>
    </sql>
    <sql id="forMarkType">
        <if test="item == 200073">
            (now() > timing.sys_promised_confirm_time and refund.confirm_state = 0 and refund.refund_type!=5
            and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND
            refund.refund_state != 6)
        </if>
        <if test="item == 200074">
            (now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type!=0 and
            refund.refund_type!=5
            and refund.refund_state != 0 AND refund.refund_state not in (0,4,5,6))
        </if>
        <if test="item == 200075">
            (now() > timing.promised_confirm_time AND refund.refund_type != 2 and refund.refund_type != 3 and
            refund.refund_type != 4 and refund.create_type = 1
            and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND
            refund.refund_state != 6)
        </if>
        <if test="item == 200076">
            (now() > timing.promised_deliver_time and refund.refund_process_state = 0
            and refund.refund_state != 0 AND refund.refund_state != 4 AND refund.refund_state != 5 AND
            refund.refund_state != 6)
        </if>
        <if test="item == 200077">
            (now() > timing.promised_agree_time AND refund.refund_type != 2 AND refund.refund_type!=0 and
            refund.refund_type!=5 and refund.refund_type!=3 and refund.refund_type!=4
            and
            refund.create_type = 1 and refund.refund_state = 1 and refund.refund_state != 0 AND refund.refund_state != 4
            AND refund.refund_state != 5 AND refund.refund_state != 6
            AND refund.platform_return_state = 0)
        </if>
    </sql>
    <sql id="notMark">
        OR
        (refund.id not in (select order_id
            from pl_eshop_order_mark
            where profile_id = #{profileId}
        and order_type = 2)
        AND
        !(
        (now() > timing.sys_promised_confirm_time AND refund.confirm_state = 0 AND refund.refund_type != 5 AND refund.refund_state not in (0,4,6,5))
        OR (now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type!=0 and
        refund.refund_type!=5
        and refund.refund_state != 0 AND refund.refund_state not in (0,4,5,6))
        OR (now() > timing.promised_confirm_time AND refund.platform_refund_state = 0 AND refund.refund_type != 2 AND refund.refund_type != 3 AND refund.refund_type != 4 AND refund.refund_state not in (0,4,6,5) AND refund.create_type = 1)
        OR (now() > timing.promised_deliver_time AND refund.refund_process_state = 0 AND refund.refund_state not in (0,4,6,5) AND refund.create_type = 1)
        OR (now() > timing.promised_agree_time AND refund.refund_state =1 AND refund.create_type = 1)
        ))
    </sql>
    <sql id="notMarkOnly">
        OR
        (refund.id not in (select order_id
            from pl_eshop_order_mark
            where profile_id = #{profileId}
        and order_type = 2)
        AND
        !(
        (now() > timing.sys_promised_confirm_time AND refund.confirm_state = 0 AND refund.refund_type != 5 AND refund.refund_state not in (0,4,6,5))
        OR (now() > timing.promised_receive_time and refund.receive_state = 1 AND refund.refund_type!=0 and
        refund.refund_type!=5
        and refund.refund_state != 0 AND refund.refund_state not in (0,4,5,6))
        OR (now() > timing.promised_confirm_time AND refund.platform_refund_state = 0 AND refund.refund_type != 2 AND refund.refund_type != 3 AND refund.refund_type != 4 AND refund.refund_state not in (0,4,6,5) AND refund.create_type = 1)
        OR (now() > timing.promised_deliver_time AND refund.refund_process_state = 0 AND refund.refund_state not in (0,4,6,5) AND refund.create_type = 1)
        OR (now() > timing.promised_agree_time AND refund.refund_state =1 AND refund.create_type = 1)
        ))
    </sql>
    <sql id="detailJoinFromOrder">
        LEFT JOIN base_ptype ptype
        ON ptype.profile_id=detail.profile_id AND detail.ptype_id=ptype.id
            left join base_brandtype ptype_brand on ptype.profile_id = ptype_brand.profile_id and ptype.brand_id = ptype_brand.id
            LEFT JOIN base_ptype_xcode xcode ON xcode.profile_id=detail.profile_id AND xcode.sku_id=detail.sku_id AND xcode.unit_id=detail.unit AND xcode.defaulted=1
            LEFT JOIN base_ptype_sku sku ON sku.profile_id=detail.profile_id AND sku.id=detail.sku_id
            LEFT JOIN base_ptype_pic pic ON pic.profile_id=detail.profile_id AND detail.ptype_id=pic.ptype_id AND pic.rowindex=1
            LEFT JOIN base_ptype_fullbarcode fcode ON fcode.profile_id=detail.profile_id AND fcode.ptype_id=detail.ptype_id AND
            fcode.sku_id=detail.sku_id AND fcode.unit_id=detail.unit and fcode.defaulted=1
            LEFT JOIN base_ptype_unit unit ON unit.profile_id=detail.profile_id AND unit.id=detail.unit
            LEFT JOIN base_ptype_price price ON unit.id=price.unit_id and unit.profile_id=price.profile_id and price.sku_id=sku.id
            LEFT JOIN base_ptype_unit bsunit ON bsunit.profile_id=detail.profile_id AND bsunit.ptype_id=detail.ptype_id AND bsunit.unit_code=1
    </sql>
</mapper>