<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.bill.mapper.ZyBillCoreMapper">
    <sql id="isNotOnlyVchcode">
        a
        .
        *
        ,
        case a.post_state when 0 then a.post_state else tba.audit_state
        end
        as auditState,
        b.pay_confirm_state,
        b.pay_state,
        b.product_account_id,
        b.refund_account_id,
        b.payment_date,
        b.custom_type as customType,
        b.ptype_qty as ptypeQty,
        b.bill_discount,
        b.currency_order_preferential_allot_total,
        CASE a.post_state
        WHEN 100 AND tba.audit_state &lt; 4 THEN 1
        WHEN 100 AND tba.audit_state = 4 THEN 4
        WHEN 500 THEN 10
        WHEN 550 THEN 6
        WHEN 600 THEN 7
        WHEN 650 THEN 5
        WHEN 700 THEN 8
        WHEN 800 THEN 8
        ELSE IFNULL(tba.audit_state, 0)
        END
        AS postAndauditState,
        b.ptype_unit_qty as ptypeunitQty,
        b.ptype_sub_qty as ptypeSubQty,
        c.fullname as kfullname,
        d.fullname as kfullname2,
        i.fullname as bfullname,
        o.fullname as ofullname,
        f.fullname as efullname,
        g.fullname as createEfullname,
        h.fullname as dfullname,
        j.fullname as postEfullname,
        j2.fullname as distributionEtypeName,
        j2.btype_id as distributionBtypeId,
        h2.fullname as distributionDtypeName,
                balancebtype.fullname as balanceFullname,
        p.customer_receiver,
        p.customer_id_card,
        p.customer_receiver_phone,
        p.customer_receiver_mobile,
        p.customer_receiver_zip_code,
        p.customer_email,
        p.customer_receiver_country,
        p.customer_receiver_province,
        p.customer_receiver_city,
        p.customer_receiver_district,
        p.customer_receiver_address,
        concat(p.customer_receiver_country, ifnull(p.customer_receiver_province, ''),
        ifnull(p.customer_receiver_city, ''), ifnull(p.customer_receiver_district, ''),
        ifnull(p.customer_receiver_address, '')) as customerReceiveFullAddress,
        (m.currency_balance_total - m.currency_balance_remain) as balance_settled,
        ifnull(bii.need_invoice, 0) as needInvoice,
        CASE a.vchtype
        WHEN 2100 THEN -b.currency_order_buyer_freight_fee
        ELSE b.currency_order_buyer_freight_fee
        END
        AS currency_order_buyer_freight_fee,
        IF(a.vchtype = 2000, IF(a.custom_refund_type = 0, null, a.custom_refund_type) ,a.custom_refund_type) AS custom_refund_type_ngp,
        IF((sum(distinct fib.invoice_total) = 0),0,IF((fib.should_invoice_total>sum(distinct fib.invoice_total)),1,IF(fib.should_invoice_total is null,null,2))) AS finInvoiceStatus,
        bii.invoice_user_type as invoiceUserType,
        bii.invoice_type as invoiceType,
        bii.invoice_title as invoiceTitle,
        bii.invoice_tax as invoiceTax,
                bii.submit as invoiceState,
        m.currency_balance_remain,
        m.balance_business_type,
        ifnull(pefcr.check_status, 0) as checkStatus,
        i.usercode as busercode,
        tba.audit_state as content_audit_state,
        sum(distinct fib.invoice_total) as invoiceTotal,
        (fib.should_invoice_total - sum(distinct fib.invoice_total)) as unInvoiceTotal,
        oc.account_name as cpaName,
        ifnull(tbr.source_vchcode, 0) as sourceVchcode,
        oc.first_buy_time as firstBuyTime,
        oc.trial_time as trialTime,
        oc.lost_time as lostTime,
        pt.fullname as cpaPtypeName,
               besu.fullname                              as saleUser,
               besvu.fullname                             as serviceUser,
            beru.fullname                             as renewalUser,
               betu.fullname                              as trainUser,
        ocs.full_name as opsSource,
        bet.fullname as operationalETypeName,
        bcg.fullname as levelGradeName,
        bbty.fullname as proxyName,


        (IF((pefcr.id is not null and pefcr.check_status = 3),4,IF(pefbr.id is null, 0, (IF((pefcr.check_status = 0 or
        pefcr.check_status is null), 1, IF(pefcr.flow_entry_state = 0, 2, 3)))))) as
        saleReconciliation
    </sql>
    <sql id="isOnlyVchcode">
        a
        .
        vchcode
        ,a.profile_id,a.bill_number
    </sql>

    <sql id="AllSales">
        and (a.create_type = 36 and ((a.business_type = 201 and
        (
        <if test="isEtypeLimited!=null and isEtypeLimited ==true">
            (bls.id is not null)or
        </if>
        a.etype_id = #{ownEtypeId} or
        <!--                如果是部门的管理员需要查询所在部门的所有单据-->
        <if test="isDTypeAdmin == true">
            a.dtype_id in (select bd.id from base_dtype bd where bd.etype_id = #{ownEtypeId} and bd.profile_id =
            #{profileId}) or
            a.dtype_id in(select bd.id from base_dtype bd where
            <choose>
                <when test="dTypeIds != null and dTypeIds.size() > 0">
                    <foreach item="item" index="index" collection="dTypeIds" open="" separator="or"
                             close="">
                        bd.partypeid like #{item}
                    </foreach>
                </when>
            </choose>
            ) or
        </if>
        (a.vchtype = 2000 and
        (b.product_account_id in (select ocrp.cpa_id
        from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
        ocrp.profile_id = #{profileId}
        <if test="ownEtypeId != null">
            and ocrp.etype_id = #{ownEtypeId}
        </if>)))
        or
        (a.vchtype=2100 and (b.refund_account_id in (select ocrp.cpa_id
        from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
        ocrp.profile_id = #{profileId}
        <if test="ownEtypeId != null">
            and ocrp.etype_id = #{ownEtypeId}
        </if>) ))
        or
        (a.vchtype=2200 and (b.refund_account_id in (select ocrp.cpa_id
        from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
        ocrp.profile_id = #{profileId}
        <if test="ownEtypeId != null">
            and ocrp.etype_id = #{ownEtypeId}
        </if>) ))
        ))
        )or(( a.commission_btype_id != 0 or a.business_type = 203)and

        <if test="isEtypeLimited!=null and isEtypeLimited ==true">
            (bls.id is not null)or
        </if>
        (
        <!--                如果是部门的管理员需要查询所在部门的所有单据-->
        <if test="isDTypeAdmin == true">
            a.dtype_id in (select bd.id from base_dtype bd where bd.etype_id = #{ownEtypeId} and bd.profile_id =
            #{profileId}) or
            a.dtype_id in(select bd.id from base_dtype bd where
            <choose>
                <when test="dTypeIds != null and dTypeIds.size() > 0">
                    <foreach item="item" index="index" collection="dTypeIds" open="" separator="or"
                             close="">
                        bd.partypeid like #{item}
                    </foreach>
                </when>
            </choose>
            ) or
        </if>
        ((select bbe.etype_id from base_btype_extend bbe where bbe.btype_id = a.btype_id and bbe.profile_id =
        #{profileId}) = #{ownEtypeId}
        or
        (select bbe.etype_id from base_btype_extend bbe where bbe.btype_id = a.commission_btype_id and
        bbe.profile_id = #{profileId}) = #{ownEtypeId})
        ))
        <if test="isAdmin != false">
            or ((a.commission_btype_id != 0 or a.business_type = 203) or a.business_type = 201)
        </if>
        )
    </sql>

    <sql id="directSales">
        and ((a.create_type = 36 and a.business_type = 201 and
        (
        <if test="isEtypeLimited!=null and isEtypeLimited ==true">
            (bls.id is not null)or
        </if>
        a.etype_id = #{ownEtypeId} or
        <!--                如果是部门的管理员需要查询所在部门的所有单据-->
        <if test="isDTypeAdmin == true">
            a.dtype_id in (select bd.id from base_dtype bd where bd.etype_id = #{ownEtypeId} and bd.profile_id =
            #{profileId}) or
            a.dtype_id in(select bd.id from base_dtype bd where
            <choose>
                <when test="dTypeIds != null and dTypeIds.size() > 0">
                    <foreach item="item" index="index" collection="dTypeIds" open="" separator="or"
                             close="">
                        bd.partypeid like #{item}
                    </foreach>
                </when>
            </choose>
            ) or
        </if>
        (a.vchtype = 2000 and
        (b.product_account_id in (select ocrp.cpa_id
        from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
        ocrp.profile_id = #{profileId}
        <if test="ownEtypeId != null">
            and ocrp.etype_id = #{ownEtypeId}
        </if>)))
        or
        (a.vchtype=2100 and (b.refund_account_id in (select ocrp.cpa_id
        from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
        ocrp.profile_id = #{profileId}
        <if test="ownEtypeId != null">
            and ocrp.etype_id = #{ownEtypeId}
        </if>) ))
        or
        (a.vchtype=2200 and (b.refund_account_id in (select ocrp.cpa_id
        from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
        ocrp.profile_id = #{profileId}
        <if test="ownEtypeId != null">
            and ocrp.etype_id = #{ownEtypeId}
        </if>) ))
        ))
        or
        ( a.create_type !=36
        <if test="isEtypeLimited!=null and isEtypeLimited ==true">
            and (bls.id is not null or a.etype_id = 0)
        </if>)
        <if test="isAdmin != false">
            or (a.create_type = 36 and a.business_type = 201 )
        </if>
        )
    </sql>
    <sql id="distributors">
        and ((a.create_type = 36 and( a.commission_btype_id != 0 or a.business_type = 203)and

        <if test="isEtypeLimited!=null and isEtypeLimited ==true">
            (bls.id is not null)or
        </if>
        (
        <!--                如果是部门的管理员需要查询所在部门的所有单据-->
        <if test="isDTypeAdmin == true">
            a.dtype_id in (select bd.id from base_dtype bd where bd.etype_id = #{ownEtypeId} and bd.profile_id =
            #{profileId}) or
            a.dtype_id in(select bd.id from base_dtype bd where
            <choose>
                <when test="dTypeIds != null and dTypeIds.size() > 0">
                    <foreach item="item" index="index" collection="dTypeIds" open="" separator="or"
                             close="">
                        bd.partypeid like #{item}
                    </foreach>
                </when>
            </choose>
            ) or
        </if>
        (
        (select bbe.etype_id from base_btype_extend bbe where bbe.btype_id = a.btype_id and bbe.profile_id =
        #{profileId}) = #{ownEtypeId}
        or
        (select bbe.etype_id from base_btype_extend bbe where bbe.btype_id = a.commission_btype_id and
        bbe.profile_id = #{profileId}) = #{ownEtypeId}
        )
        ))
        or
        ( a.create_type !=36
        <if test="isEtypeLimited!=null and isEtypeLimited ==true">
            and (bls.id is not null or a.etype_id = 0)
        </if>)
        <if test="isAdmin != false">
            or (a.create_type = 36 and ( a.commission_btype_id != 0 or a.business_type = 203))
        </if>
        )
    </sql>
    <sql id="FXS">
        and
        <if test="isAdmin == false or isAdmin == null">
            (a.create_type = 36 and
            (
            b.distribution_etype_id = #{ownEtypeId}
            or
            (a.vchtype = 2000 and
            (b.product_account_id in (select ocrp.cpa_id
            from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
            ocrp.profile_id = #{profileId}
            <if test="ownEtypeId != null">
                and ocrp.etype_id = #{ownEtypeId}
            </if>)))
            or
            (a.vchtype=2100 and (b.refund_account_id in (select ocrp.cpa_id
            from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
            ocrp.profile_id = #{profileId}
            <if test="ownEtypeId != null">
                and ocrp.etype_id = #{ownEtypeId}
            </if>) ))
            or
            (a.vchtype=2200 and (b.refund_account_id in (select ocrp.cpa_id
            from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
            ocrp.profile_id = #{profileId}
            <if test="ownEtypeId != null">
                and ocrp.etype_id = #{ownEtypeId}
            </if>) ))
            ))
        </if>
        <if test="isAdmin != false">
            (a.create_type = 36 and
            (
            b.distribution_etype_id = #{ownEtypeId}
            or
            a.btype_id = (select be.btype_id from base_etype be where be.id = #{ownEtypeId} and be.profile_id =
            #{profileId})
            or
            a.commission_btype_id =(select be.btype_id from base_etype be where be.id = #{ownEtypeId} and
            be.profile_id =
            #{profileId})
            or
            (a.vchtype = 2000 and
            (b.product_account_id in (select ocrp.cpa_id
            from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
            ocrp.profile_id = #{profileId}
            <if test="ownEtypeId != null">
                and ocrp.etype_id = #{ownEtypeId}
            </if>)))
            or
            (a.vchtype=2100 and (b.refund_account_id in (select ocrp.cpa_id
            from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
            ocrp.profile_id = #{profileId}
            <if test="ownEtypeId != null">
                and ocrp.etype_id = #{ownEtypeId}
            </if>) ))
            or
            (a.vchtype=2200 and (b.refund_account_id in (select ocrp.cpa_id
            from ops_cpa_recharge_protect ocrp where ocrp.protect_status != 2 and
            ocrp.profile_id = #{profileId}
            <if test="ownEtypeId != null">
                and ocrp.etype_id = #{ownEtypeId}
            </if>) ))
            ))
        </if>
    </sql>

    <resultMap id="BaseResultMap" type="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.TdBillCoreDAO">
        <id column="vchcode" property="vchcode" jdbcType="BIGINT"/>
        <result column="profile_id" property="profileId" jdbcType="BIGINT"/>
        <result column="vchtype" property="vchtype" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="BIGINT"/>
        <result column="process_type" property="processType" jdbcType="INTEGER"/>
        <result column="deliver_type" property="deliveryType" jdbcType="INTEGER"/>
        <result column="accounting_type" property="accountingType" jdbcType="INTEGER"/>
        <result column="bill_number" property="billNumber" jdbcType="VARCHAR"/>
        <result column="shopType" property="shopType" jdbcType="VARCHAR"/>
        <result column="bill_date" property="billDate" jdbcType="DATE"/>
        <result column="period" property="period" jdbcType="INTEGER"/>
        <result column="otype_id" property="otypeId" jdbcType="BIGINT"/>
        <result column="btype_id" property="btypeId" jdbcType="BIGINT"/>
        <result column="pay_btype_id" property="balanceBtypeId" jdbcType="BIGINT"/>
        <result column="ktype_id" property="ktypeId" jdbcType="BIGINT"/>
        <result column="ktype_id2" property="ktypeId2" jdbcType="BIGINT"/>
        <result column="etype_id" property="etypeId" jdbcType="BIGINT"/>
        <result column="dtype_id" property="dtypeId" jdbcType="BIGINT"/>
        <result column="buyer_id" property="buyerId" jdbcType="BIGINT"/>
        <result column="summary" property="summary" jdbcType="VARCHAR"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="create_type" property="createType" jdbcType="INTEGER"/>
        <result column="invoice_type" property="invoiceType" jdbcType="INTEGER"/>
        <result column="currency_id" property="currencyId" jdbcType="BIGINT"/>
        <result column="exchange_rate" property="exchangeRate" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="fee_total" property="feeTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="otherincome_total" property="otherIncomeTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="preferential_total" property="preferentialTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="bill_total" property="billTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_fee_total" property="currencyFeeTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_otherincome_total" property="currencyOtherIncomeTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_preferential_total" property="currencyPreferentialTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_bill_total" property="currencyBillTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_bill_total" property="advanceGiftBillTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="balance_settled" jdbcType="DECIMAL" property="balanceSettled"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_order_buyer_freight_fee" jdbcType="DECIMAL" property="currencyOrderBuyerFreightFee"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="post_state" property="postState" jdbcType="INTEGER"/>
        <result column="auditState" property="auditState" jdbcType="INTEGER"/>
        <result column="post_time" property="postTime" jdbcType="TIMESTAMP"/>
        <result column="post_etype_id" property="postEtypeId" jdbcType="BIGINT"/>
        <result column="create_etype_id" property="createEtypeId" jdbcType="BIGINT"/>
        <result column="distribution_etype_id" property="distributionEtypeId" jdbcType="BIGINT"/>
        <result column="distributionEtypeName" property="distributionEtypeName" jdbcType="VARCHAR"/>
        <result column="distributionBtypeId" property="distributionBtypeId" jdbcType="BIGINT"/>
        <result column="distributionDtypeName" property="distributionDtypeName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="ptypeunitQty" property="ptypeunitQty" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>

        <result column="payment_date" property="paymentDate" jdbcType="DATE"/>
        <!--        仓库一名称-->
        <result column="kfullname" property="kfullname" jdbcType="VARCHAR"/>
        <!--        仓库2名称-->
        <result column="kfullname2" property="kfullname2" jdbcType="VARCHAR"/>

        <!--  往来单位全名      -->
        <result column="bfullname" property="bfullname" jdbcType="VARCHAR"/>

        <!--   结算单位全名     -->
        <result column="balanceFullname" property="balanceFullname" jdbcType="VARCHAR"/>
        <!--   业务员全名     -->
        <result column="efullname" property="efullname" jdbcType="VARCHAR"/>
        <!--      部门全名  -->
        <result column="dfullname" property="dfullname" jdbcType="VARCHAR"/>
        <!--        -->
        <result column="createEfullname" property="createEfullname" jdbcType="VARCHAR"/>

        <result column="postEfullname" property="postEfullname" jdbcType="VARCHAR"/>
        <result column="customType" property="customType" jdbcType="INTEGER"/>
        <result column="custom_refund_type_ngp" property="customRefundType" jdbcType="INTEGER"/>
        <result column="customer_receiver" property="customerReceiver" jdbcType="VARCHAR"/>
        <result column="customer_receiver_phone" property="customerReceiverPhone" jdbcType="VARCHAR"/>
        <result column="customer_receiver_mobile" property="customerReceiverMobile" jdbcType="VARCHAR"/>
        <result column="customer_receiver_zip_code" property="customerReceiverZipCode" jdbcType="VARCHAR"/>
        <result column="customer_email" property="customerEmail" jdbcType="VARCHAR"/>
        <result column="customer_receiver_country" property="customerReceiverCountry" jdbcType="VARCHAR"/>
        <result column="customer_receiver_province" property="customerReceiverProvince" jdbcType="VARCHAR"/>
        <result column="customer_receiver_city" property="customerReceiverCity" jdbcType="VARCHAR"/>
        <result column="customer_receiver_district" property="customerReceiverDistrict" jdbcType="VARCHAR"/>
        <result column="customer_receiver_address" property="customerReceiverAddress" jdbcType="VARCHAR"/>
        <result column="customerReceiveFullAddress" property="customerReceiveFullAddress" jdbcType="VARCHAR"/>
        <result column="countQty" jdbcType="DECIMAL" property="countQty"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="balance_settled" jdbcType="DECIMAL" property="balanceSettled"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_balance_remain" jdbcType="DECIMAL" property="currencyBalanceRemain"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="countCostTotal" jdbcType="DECIMAL" property="countCostTotal"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="countCostQty" jdbcType="DECIMAL" property="countCostQty"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="ptypeQty" jdbcType="DECIMAL" property="ptypeQty"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="ptypeSubQty" jdbcType="DECIMAL" property="ptypeSubQty"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <!--        财务单据表头金额-->
        <result column="customFeeAccountTotal" jdbcType="DECIMAL" property="customFeeAccountTotal"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="customFeeSettleAccountTotal" jdbcType="DECIMAL" property="customFeeSettleAccountTotal"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="customFeeAccountName" property="customFeeAccountName" jdbcType="VARCHAR"/>
        <result column="customFeeSettleAccountName" property="customFeeSettleAccountName" jdbcType="VARCHAR"/>
        <result column="printCount" jdbcType="INTEGER" property="printCount"/>
        <result column="postAndauditState" property="postAndauditState" jdbcType="INTEGER"/>
        <result column="needInvoice" property="needInvoice" jdbcType="INTEGER"/>
        <result column="invoiceUserType" property="invoiceUserType" jdbcType="INTEGER"/>
        <result column="invoiceType" property="invoiceType" jdbcType="INTEGER"/>
        <result column="invoiceTitle" property="invoiceTitle" jdbcType="VARCHAR"/>
        <result column="invoiceTax" property="invoiceTax" jdbcType="VARCHAR"/>
        <result column="balance_business_type" property="balanceBusinessType" jdbcType="INTEGER"/>
        <result column="busercode" property="busercode" jdbcType="VARCHAR"/>
        <result column="bcategory" property="bcategory" jdbcType="INTEGER"/>
        <result column="freightBtypeId" property="freightBtypeId" jdbcType="INTEGER"/>
        <result column="freightBillNo" property="freightBillNo" jdbcType="VARCHAR"/>
        <result column="freightBtypeName" property="freightBtypeName" jdbcType="VARCHAR"/>
        <!--  销售区域,与btype相关   -->
        <result column="areaFullName" property="areaFullName" jdbcType="VARCHAR"/>
        <!--  零售pos单据查询相关   -->
        <result column="cashierName" property="cashierName" jdbcType="VARCHAR"/>
        <result column="product_account_id" property="productAccountId" jdbcType="BIGINT"/>
        <result column="refund_account_id" property="refundAccountId" jdbcType="BIGINT"/>
        <result column="commission_btype_id" property="commissionBtypeId" jdbcType="BIGINT"/>
        <result column="commissionBtypeName" property="commissionBtypeName" jdbcType="VARCHAR"/>
        <result column="currency_ptype_commission_total" property="currencyPtypeCommissionTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>

        <result column="cpaName" property="cpaName" jdbcType="VARCHAR"/>
        <result column="currency_order_preferential_allot_total" property="currencyOrderPreferentialAllotTotal"
                jdbcType="DECIMAL" typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="sourceVchcode" property="sourceVchcode" jdbcType="BIGINT"/>
        <result column="ofullname" property="ofullname" jdbcType="VARCHAR"/>
        <result column="custom_sale_stage" property="customSaleStage" jdbcType="INTEGER"/>
        <result column="pay_confirm_state" property="payConfirmState" jdbcType="INTEGER"/>
        <result column="pay_state" property="payState" jdbcType="INTEGER"/>
        <result column="saleReconciliation" property="saleReconciliation" jdbcType="INTEGER"/>
        <result column="bill_discount" property="billDiscount" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="dised_taxed_total" property="disedTaxedTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="tax_total" property="taxTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="content_audit_state" property="contentAuditState" jdbcType="INTEGER"/>
        <result column="checkStatus" property="checkStatus" jdbcType="INTEGER"/>
        <result column="pay_time_type" property="payTimeType" jdbcType="INTEGER"/>
        <result column="invoiceState" property="invoiceState" jdbcType="INTEGER"/>
        <result column="advanceTotal" property="advanceTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="advanceGiftTotal" property="advanceGiftTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="failure_info" property="failureInfo" jdbcType="VARCHAR"/>

        <result column="firstBuyTime" property="firstBuyTime" jdbcType="DATE"/>
        <result column="trialTime" property="trialTime" jdbcType="DATE"/>
        <result column="lostTime" property="lostTime" jdbcType="DATE"/>
        <result column="cpaPtypeName" property="cpaPtypeName" jdbcType="VARCHAR"/>
        <result column="saleUser" property="saleUser" jdbcType="VARCHAR"/>
        <result column="serviceUser" property="serviceUser" jdbcType="VARCHAR"/>
        <result column="renewalUser" property="renewalUser" jdbcType="VARCHAR"/>
        <result column="trainUser" property="trainUser" jdbcType="VARCHAR"/>
        <result column="opsSource" property="opsSource" jdbcType="VARCHAR"/>
        <result column="operationalETypeName" property="operationalETypeName" jdbcType="VARCHAR"/>
        <result column="levelGradeName" property="levelGradeName" jdbcType="VARCHAR"/>
        <result column="buyerDisedTaxedTotal" property="buyerDisedTaxedTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="costPrice" property="costPrice" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="performanceTotal" property="performanceTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="FXSIncome" property="fxsIncome" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <!--        <collection property="payMentlList"-->
        <!--                    ofType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.PayMentDTO">-->
        <!--            <id column="payId" property="id"/>-->
        <!--            <result column="payAtypeId" property="atypeId"/>-->
        <!--            <result column="payOutNo" property="outNo"/>-->
        <!--            <result column="paywayFullname" property="paywayFullname"/>-->
        <!--        </collection>-->
    </resultMap>

    <select id="getPayMentlList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.PayMentDTO">
        select tba2.id,tba2.atype_id as atypeId,tba2.pay_out_no as outNo,bpay.fullname as
        paywayFullname,tba2.vchcode,tba2.payway_id
        from
        td_bill_account tba2
        left join base_payways bpay on bpay.id = tba2.payway_id and bpay.profile_id = #{profileId}
        where tba2.profile_id = #{profileId}
        <if test="vchcodes != null and vchcodes.size() > 0">
            and tba2.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getPayMentAcclList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.PayMentDTO">
        select tba2.id,tba2.atype_id as atypeId,tba2.pay_out_no as outNo,bpay.fullname as
        paywayFullname,tba2.vchcode,tba2.payway_id
        from
        acc_bill_account tba2
        left join base_payways bpay on bpay.id = tba2.payway_id and bpay.profile_id = #{profileId}
        where tba2.profile_id = #{profileId}
        <if test="vchcodes != null and vchcodes.size() > 0">
            and tba2.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTdGoodsDetails" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsDetailDTO">
        select
        abdcs.ptype_commission_total,abdcs.cost_price,abdcs.qty,abdcs.inout_type,abdcs.dised_taxed_total as
        currencyDisedTaxedTotal,abdcs.vchcode,tbddb.buyer_dised_taxed_total,abdcs.detail_id
        from td_bill_detail_core abdcs
        left join td_bill_detail_distribution_buyer tbddb on tbddb.profile_id = #{profileId} and tbddb.vchcode =
        abdcs.vchcode and tbddb.detail_id = abdcs.detail_id
        where abdcs.profile_id = #{profileId}
        <if test="vchcodes != null and vchcodes.size() > 0">
            and abdcs.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="getBillCoreList" resultMap="BaseResultMap" parameterType="Map">
        select
        <choose>
            <when test="isOnlyVchcode != null">
                <include refid="isOnlyVchcode"/>
            </when>
            <when test="isOnlyVchcode == null and relationDeliverState != null and relationDeliverState == true">
                <include refid="isNotOnlyVchcode"/>
                , abds.process_state as processState
            </when>
            <otherwise>
                <include refid="isNotOnlyVchcode"/>
            </otherwise>
        </choose>
        ,b.freight_btype_id as
        freightBtypeId,
        concat("直营") as shopType,
        ba.fullname as areaFullName,
        b.freight_billno as freightBillNo,n.fullname as freightBtypeName
        ,b.currency_bill_total,b.distribution_etype_id,
        b.currency_ptype_commission_total,
        i1.fullname as commissionBtypeName
        from td_bill_core a
        left join td_bill_assinfo b on a.vchcode = b.vchcode and b.profile_id = #{profileId}
        left join td_deliver_freight_info tdfi on tdfi.vchcode = a.vchcode and tdfi.profile_id = #{profileId} and
        tdfi.original = 1
        left join base_ktype c on a.ktype_id = c.id and c.profile_id=#{profileId}
        left join base_ktype d on a.ktype_id2 = d.id and d.profile_id=#{profileId}
        left join base_btype i on a.btype_id = i.id and i.profile_id=#{profileId}
        left join base_btype i1 on a.commission_btype_id = i1.id and i1.profile_id=#{profileId}
        left join base_areatype ba on ba.id = i.areatype_id and ba.profile_id =#{profileId}
        left join base_etype f on a.etype_id = f.id and f.profile_id=#{profileId}
        left join base_ktype b2 on b2.id = a.ktype_id2 and b2.profile_id=#{profileId}
        left join base_etype g on a.create_etype_id = g.id and g.profile_id=#{profileId}
        left join base_etype j on a.post_etype_id = j.id and j.profile_id=#{profileId}
        left join base_etype j2 on b.distribution_etype_id = j2.id and j2.profile_id=#{profileId}
        left join base_dtype h2 on b.distribution_dtype_id = h2.id and h2.profile_id=#{profileId}
        left join base_dtype h on a.dtype_id = h.id and h.profile_id=#{profileId}
        left join base_otype o on o.id = a.otype_id and o.profile_id=#{profileId}
        left join pl_buyer p on a.buyer_id = p.buyer_id and p.profile_id=#{profileId}
        left join acc_bill_balance_info m on a.vchcode = m.vchcode and m.profile_id=#{profileId}
        and m.balance_business_type = 0
        left join base_btype n on b.freight_btype_id = n.id and n.profile_id=#{profileId}
        left join td_bill_audit tba on a.vchcode = tba.vchcode and tba.profile_id=#{profileId}
        and tba.obsolete_enabled = 0
        left join td_bill_invoice_info bii on bii.vchcode=a.vchcode and bii.profile_id=#{profileId}
        left join fin_invoice_bill fib on fib.vchcode=a.vchcode and fib.profile_id=#{profileId}
        left join td_bill_relation tbr on tbr.target_vchcode = a.vchcode and tbr.profile_id = #{profileId}
        left join td_bill_account tba2 on tba2.vchcode = a.vchcode and tba2.profile_id = #{profileId}
        left join base_payways bpay on bpay.id = tba2.payway_id and bpay.profile_id = #{profileId}
        left join base_btype balancebtype on a.pay_btype_id=balancebtype.id and
        balancebtype.profile_id=#{profileId}
        <include refid="zyJoin"/>


        <if test="isEtypeLimited != null and isEtypeLimited == true">
            left join base_limit_scope bls on bls.profile_id=#{profileId} and bls.object_type=1 and
            a.etype_id=bls.object_id and bls.etype_id=#{employeeId}
        </if>
        <if test="isEtypeLimited!=null and isEtypeLimited == true">
            left join base_limit_scope blsc on blsc.profile_id=#{profileId} and blsc.object_type=1 and
            b.driver_id=blsc.object_id and blsc.etype_id=#{employeeId}
        </if>
        <if test="isOtypeLimited!=null and isOtypeLimited == true">
            left join base_limit_scope blb on blb.profile_id=#{profileId} and blb.object_type=3 and
            a.otype_id=blb.object_id and blb.etype_id=#{employeeId}
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            left join base_limit_scope bbs on bbs.profile_id=#{profileId} and bbs.object_type=4 and
            a.btype_id=bbs.object_id and bbs.etype_id=#{employeeId}
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            left join base_limit_scope bbs1 on bbs1.profile_id=#{profileId} and bbs1.object_type=4 and
            tdfi.freight_btype_id=bbs1.object_id and bbs1.etype_id=#{employeeId}
        </if>
        <if test="isKtypeLimited!=null and isKtypeLimited == true">
            left join base_limit_scope blk on blk.profile_id=#{profileId} and blk.object_type=2 and
            a.ktype_id=blk.object_id and blk.etype_id=#{employeeId}
            left join base_limit_scope blk2 on blk2.profile_id=#{profileId} and blk2.object_type=2 and
            a.ktype_id2=blk2.object_id and blk2.etype_id=#{employeeId}
        </if>
        <if test="relationDeliverState != null and relationDeliverState == true">
            left join td_bill_deliver_state abds
            on abds.vchcode = a.vchcode and abds.profile_id=#{profileId}
        </if>
        where a.profile_id =#{profileId} and a.order_sale_mode = 6 and a.deleted = 0
        <if test="payState != -1">
            and b.pay_state = #{payState}
        </if>
        <if test="createEtypeId != null">
            and a.create_etype_id like concat('%',#{createEtypeId},'%')
        </if>
        <if test="postTimeBeginTime != null and postTimeEndTime != null">
            and a.post_time between #{postTimeBeginTime} and #{postTimeEndTime}
        </if>
        <if test="relationDeliverState != null and relationDeliverState == true">
            AND a.deleted = 0
        </if>
        <choose>
            <when test="vchcodeList != null and vchcodeList.size() > 0">
                AND a.vchcode in
                <foreach item="vchcodeItem" index="index" collection="vchcodeList" open="(" separator="," close=")">
                    #{vchcodeItem}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="vchtypes != null and vchtypes.size() > 0">
                AND a.vchtype in
                <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        <choose>
            <when test="businessTypeList != null and businessTypeList.size() > 0">
                AND a.business_type in
                <foreach item="items" index="index" collection="businessTypeList" open="(" separator="," close=")">
                    #{items}
                </foreach>
            </when>
        </choose>

        <if test="billNumber != null">
            and a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
        </if>
        <if test="otypeIds != null and otypeIds.size() > 0">
            AND a.otype_id in
            <foreach item="item" index="index" collection="otypeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="commissionBtypeIds != null and commissionBtypeIds.size() > 0">
            AND a.commission_btype_id in
            <foreach item="item" index="index" collection="commissionBtypeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="btypeids != null and btypeids.size() > 0">
            AND a.btype_id in
            <foreach item="item" index="index" collection="btypeids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customSaleStages != null and customSaleStages.size() > 0">
            AND a.custom_sale_stage in
            <foreach item="item" index="index" collection="customSaleStages" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="billNumbers != null and billNumbers.size() > 0">
            and (
            <foreach collection="billNumbers" separator="or" item="billNumber">
                a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
            </foreach>
            )
        </if>
        <if test="btypeId != null">
            and a.btype_id =#{btypeId}
        </if>
        <if test="otypeId != null">
            and a.otype_id =#{otypeId}
        </if>
        <choose>
            <when test="relationDeliverState != null and relationDeliverState == true and number != null">
                and a.bill_number = #{number,jdbcType=VARCHAR}
            </when>
            <when test="number != null">
                and a.bill_number like concat('%',#{number,jdbcType=VARCHAR},'%')
            </when>
        </choose>
        <if test="etypeId != null and etypeId != 0">
            and a.etype_id =#{etypeId}
        </if>
        <if test="paymentType != null and paymentType == 1">
            and m.currency_balance_remain &lt;&gt; 0
        </if>
        <choose>
            <when test="checkStatus != -1">
                and (IF((pefcr.id is not null and pefcr.check_status = 3),4,IF(pefbr.id is null, 0,
                (IF((pefcr.check_status = 0 or
                pefcr.check_status is null), 1, IF(pefcr.flow_entry_state = 0, 2, 3))))))
                = #{checkStatus}
            </when>
        </choose>
        <choose>
            <when test="refundType != -1">
                and a.custom_refund_type = #{refundType} and (a.vchtype != 2000 or (a.vchtype = 2000 and a.custom_refund_type != 0))
            </when>
        </choose>
        <choose>
            <when test="invoiceType != null and invoiceType == 1">
                and bii.need_invoice=1
            </when>
            <when test="invoiceType != null and invoiceType == 2">
                and (bii.id is null or bii.need_invoice = 0)
            </when>
        </choose>
        <choose>
            <when test="invoiceStatusType != -1">
                <if test="invoiceStatusType == 0">
                    and (bii.id is null or bii.submit = #{invoiceStatusType})
                </if>
                <if test="invoiceStatusType != null and invoiceStatusType != 0">
                    and bii.submit = #{invoiceStatusType}
                </if>
            </when>
        </choose>

        <if test="skuId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM td_bill_detail_core a WHERE a.profile_id =#{profileId}
            AND a.sku_id =#{skuId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <if test="comboId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM td_bill_detail_combo a WHERE a.profile_id =#{profileId}
            AND a.combo_id =#{comboId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <choose>
            <when test="saleOrbuy != null">
                <if test="ktypeId != null">
                    and (a.ktype_id =#{ktypeId} or a.ktype_id2 =#{ktypeId})
                </if>
            </when>
            <when test="ktypeIdList != null and ktypeIdList.size() > 0">
                AND (a.ktype_id in
                <foreach item="item" index="index" collection="ktypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
                a.ktype_id2 in
                <foreach item="item" index="index" collection="ktypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </when>
            <otherwise>
                <if test="ktypeId != null">
                    and a.ktype_id=#{ktypeId}
                </if>
                <if test="ktypeId2 != null">
                    and (a.ktype_id2=#{ktypeId2})
                </if>
                <if test="ktypeId3 != null">
                    AND a.ktype_id2=#{ktypeId3}
                </if>
            </otherwise>
        </choose>
        <if test="customTypeList != null and customTypeList.size() > 0">
            AND b.custom_type IN
            <foreach item="item1" index="index1" collection="customTypeList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <if test="completedStateList != null and completedStateList.size() > 0">
            AND
            <foreach item="item2" index="index2" collection="completedStateList" open="(" separator="OR" close=")">
                <if test="item2 == 0">
                    b.ptype_completed_qty = 0
                </if>
                <if test="item2 == 1">
                    (b.ptype_completed_qty != 0 AND b.ptype_qty > b.ptype_completed_qty)
                </if>
                <if test="item2 == 2">
                    (b.ptype_completed_qty != 0 AND b.ptype_qty = b.ptype_completed_qty)
                </if>
            </foreach>
        </if>
        <if test="ptypeId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM td_bill_detail_core a WHERE a.profile_id =#{profileId}
            AND a.ptype_id =#{ptypeId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>

        <choose>
            <!--            直营-->
            <when test="saleOrderType == 0">
                <include refid="directSales"/>
            </when>
            <!--            渠道门店单据查询-->
            <when test="saleOrderType == 1">
                <include refid="distributors"/>
            </when>
            <!--            分销商销售单据查询-->
            <when test="saleOrderType == 2">
                <include refid="FXS"/>
            </when>
            <!--            全部单据查询-->
            <when test="saleOrderType == 3">
                <if test="isFxs == 0">
                    <include refid="AllSales"/>
                </if>
                <if test="isFxs == 1">
                    <include refid="FXS"/>
                </if>
            </when>
        </choose>


        <choose>
            <when test="postStateList != null">
                and ((
                <foreach collection="postStateList" index="index" item="item" open="(" close=")" separator="or">
                    <choose>
                        <when test="item == 0">
                            a.post_state = 0
                        </when>
                        <when test="item == 4">
                            a.post_state = 100 and tba.audit_state = 4
                        </when>
                        <when test="item == 10">
                            a.post_state = 500
                        </when>
                        <when test="item == 5">
                            a.post_state = 650
                        </when>
                        <when test="item == 6">
                            a.post_state = 550
                        </when>
                        <when test="item == 7">
                            a.post_state = 600
                        </when>
                        <when test="item == 8">
                            a.post_state = 700
                        </when>
                        <when test="item == 5">
                            a.post_state = 650
                        </when>
                    </choose>
                </foreach>
                <!--                    and (tba.audit_state = 0 or tba.audit_state = 4 or tba.audit_state is null)) or ((a.post_state <![CDATA[<=]]> 500) and-->
                <!--                tba.audit_state in-->
                <!--                <foreach item="item" index="index" collection="auditList" open="(" separator="," close=")">-->
                <!--                    #{item}-->
                <!--                </foreach>-->
                ))
            </when>
            <otherwise>
                <if test="postStateList != null">
                    and a.post_state = 0 and (tba.audit_state = 0 or tba.audit_state is null)
                </if>
                <if test="auditList != null">
                    and tba.audit_state in
                    <foreach item="item" index="index" collection="auditList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>

        <if test="beginDate != null and endDate != null">
            and a.bill_date between #{beginDate} and #{endDate}
        </if>
        <if test="MinTotal != null">
            and a.currency_bill_total <![CDATA[ >= ]]> #{MinTotal}
        </if>
        <if test="MaxTotal != null">
            and a.currency_bill_total <![CDATA[ <= ]]> #{MaxTotal}
        </if>
        <if test="customerReceiverProvince != null">
            and p.customer_receiver_province =#{customerReceiverProvince}
        </if>
        <if test="customerReceiverCity != null">
            and p.customer_receiver_city =#{customerReceiverCity}
        </if>
        <if test="customerReceiverDistrict != null">
            and p.customer_receiver_district =#{customerReceiverDistrict}
        </if>
        <if test="processType != null">
            and a.process_type = #{processType}
        </if>
        <if test="showBackBill != null  and showBackBill == true">
            and (a.process_type!=1 or (a.process_type=1 and (a.create_type=4 or a.create_type=5)))
        </if>

        <if test="isOtypeLimited!=null and isOtypeLimited ==true">
            and (blb.id is not null or a.otype_id=0)
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            and (bbs.id is not null or a.btype_id=0)
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            and (bbs1.id is not null or tdfi.freight_btype_id is null or tdfi.freight_btype_id = 0)
        </if>
        <if test="isKtypeLimited!=null and isKtypeLimited ==true">
            AND IF((a.vchtype = 3000),
            ( blk.object_id IS NOT NULL OR
            blk2.object_id IS NOT NULL
            ),
            (
            (blk.id IS NOT NULL OR a.ktype_id=0)
            AND (blk2.id IS NOT NULL OR a.ktype_id2=0))
            )
        </if>
        <if test="freightBillNo != null and freightBillNo != ''">
            and b.freight_billno like concat('%',#{freightBillNo,jdbcType=VARCHAR},'%')
        </if>
        <if test="freightBtypeId != null and freightBtypeId != 0">
            and b.freight_btype_id = #{freightBtypeId}
        </if>
        <include refid="query-filter">
            <property name="_list" value="gridFilter"/>
        </include>
        group by a.vchcode
        <choose>
            <when test="orderColumn != null and orderColumn != ''">
                order by ${orderColumn}
            </when>
            <otherwise>
                order by a.bill_date DESC,a.update_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="getBillCoreListBySale" resultMap="BaseResultMap" parameterType="Map">
        select
        <choose>
            <when test="isOnlyVchcode != null">
                <include refid="isOnlyVchcode"/>
            </when>
            <when test="isOnlyVchcode == null and relationDeliverState != null and relationDeliverState == true">
                <include refid="isNotOnlyVchcode"/>
                , abds.process_state as processState
            </when>
            <otherwise>
                <include refid="isNotOnlyVchcode"/>
            </otherwise>
        </choose>
        ,
        b.freight_btype_id as freightBtypeId,
        concat('直营') as shopType,
        ba.fullname as areaFullName,
        b.freight_billno as freightBillNo,
        n.fullname as freightBtypeName,
        b.currency_bill_total,b.distribution_etype_id,
        b.currency_ptype_commission_total,
        i1.fullname as commissionBtypeName
        from acc_bill_core a
        left join acc_bill_assinfo b on a.vchcode = b.vchcode and b.profile_id = #{profileId}
        LEFT JOIN td_bill_audit tba ON b.vchcode = tba.vchcode AND tba.profile_id =#{profileId} AND tba.obsolete_enabled
        = 0
        left join acc_deliver_freight_info adfi on adfi.vchcode = a.vchcode and adfi.profile_id = #{profileId} and
        adfi.original = 1
        left join base_ktype c on a.ktype_id = c.id and c.profile_id=#{profileId}
        left join base_ktype d on a.ktype_id2 = d.id and d.profile_id=#{profileId}
        left join base_btype i on a.btype_id = i.id and i.profile_id=#{profileId}
        left join base_btype i1 on a.commission_btype_id = i1.id and i1.profile_id=#{profileId}
        left join base_areatype ba on ba.id = i.areatype_id and ba.profile_id =#{profileId}
        left join base_etype f on a.etype_id = f.id and f.profile_id=#{profileId}
        left join base_etype g on a.create_etype_id = g.id and g.profile_id=#{profileId}
        left join base_etype j on a.post_etype_id = j.id and j.profile_id=#{profileId}
        left join base_etype j2 on b.distribution_etype_id = j2.id and j2.profile_id=#{profileId}
        left join base_dtype h2 on b.distribution_dtype_id = h2.id and h2.profile_id=#{profileId}
        left join base_dtype h on a.dtype_id = h.id and h.profile_id=#{profileId}
        left join base_otype o on o.id = a.otype_id and o.profile_id=#{profileId}
        left join pl_buyer p on a.buyer_id = p.buyer_id and p.profile_id=#{profileId}
        left join acc_bill_balance_info m on a.vchcode = m.vchcode and m.profile_id=#{profileId} and
        m.balance_business_type = 0
        left join base_btype n on b.freight_btype_id = n.id and n.profile_id=#{profileId}
        left join acc_bill_invoice_info bii on bii.vchcode=a.vchcode and bii.profile_id=#{profileId}
        left join fin_invoice_bill fib on fib.vchcode=a.vchcode and fib.profile_id=#{profileId} and fib.status = 0
        left join td_bill_relation tbr on tbr.target_vchcode = a.vchcode and tbr.profile_id = #{profileId}
        left join acc_bill_account tba2 on tba2.vchcode = a.vchcode and tba2.profile_id = #{profileId}
        left join base_payways bpay on bpay.id = tba2.payway_id and bpay.profile_id = #{profileId}
        left join base_btype balancebtype on a.pay_btype_id=balancebtype.id and
        balancebtype.profile_id=#{profileId}
        <include refid="zyJoin"/>

        <if test="isEtypeLimited!=null and isEtypeLimited == true">
            left join base_limit_scope bls on bls.profile_id=#{profileId} and bls.object_type=1 and
            a.etype_id=bls.object_id and bls.etype_id=#{employeeId}
        </if>
        <if test="isOtypeLimited!=null and isOtypeLimited ==true">
            left join base_limit_scope blb on blb.profile_id=#{profileId} and blb.object_type=3 and
            a.otype_id=blb.object_id and blb.etype_id=#{employeeId}
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            left join base_limit_scope bbs on bbs.profile_id=#{profileId} and bbs.object_type=4 and
            a.btype_id=bbs.object_id and bbs.etype_id=#{employeeId}
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            left join base_limit_scope bbs1 on bbs1.profile_id=#{profileId} and bbs1.object_type=4 and
            adfi.freight_btype_id=bbs1.object_id and bbs1.etype_id=#{employeeId}
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            left join base_limit_scope bbs2 on bbs2.profile_id=#{profileId} and bbs2.object_type=4 and
            a.commission_btype_id=bbs2.object_id and bbs2.etype_id=#{employeeId}
        </if>
        <if test="isKtypeLimited!=null and isKtypeLimited == true">
            left join base_limit_scope blk on blk.profile_id=#{profileId} and blk.object_type=2 and
            a.ktype_id=blk.object_id and blk.etype_id=#{employeeId}
        </if>
        <if test="relationDeliverState != null and relationDeliverState == true">
            left join acc_bill_deliver_state abds
            on abds.vchcode = a.vchcode and abds.profile_id=#{profileId}
        </if>
        <!--        <if test="isKtypeLimited!=null and isKtypeLimited">-->
        <!--            inner join base_limit_scope blb on blb.profileid=#{profileId} and object_type=3 and a.btype_id=blb.object_id and blb=#{employeeId}-->
        <!--        </if>-->
        where a.profile_id =#{profileId} and a.order_sale_mode = 6
        AND a.post_state=800 and a.deleted = 0
        <if test="payState != -1">
            and b.pay_state = #{payState}
        </if>

        <choose>
            <!--            直营-->
            <when test="saleOrderType == 0">
                <include refid="directSales"/>
            </when>
            <!--            渠道门店单据查询-->
            <when test="saleOrderType == 1">
                <include refid="distributors"/>
            </when>
            <!--            分销商销售单据查询-->
            <when test="saleOrderType == 2">
                <include refid="FXS"/>
            </when>
            <!--            全部单据查询-->
            <when test="saleOrderType == 3">
                <if test="isFxs == 0">
                    <include refid="AllSales"/>
                </if>
                <if test="isFxs == 1">
                    <include refid="FXS"/>
                </if>
            </when>
        </choose>

        <if test="createEtypeId != null">
            and a.create_etype_id like concat('%',#{createEtypeId},'%')
        </if>
        <if test="postTimeBeginTime != null and postTimeEndTime != null">
            and a.post_time between #{postTimeBeginTime} and #{postTimeEndTime}
        </if>
        <choose>
            <when test="vchcodeList != null and vchcodeList.size() > 0">
                AND a.vchcode in
                <foreach item="vchcodeItem" index="index" collection="vchcodeList" open="(" separator="," close=")">
                    #{vchcodeItem}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="vchtypes != null and vchtypes.size() > 0">
                AND a.vchtype in
                <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="businessTypeList != null and businessTypeList.size() > 0">
                AND a.business_type in
                <foreach item="items" index="index" collection="businessTypeList" open="(" separator="," close=")">
                    #{items}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="relationDeliverState != null and relationDeliverState == true and number != null">
                and a.bill_number = #{number,jdbcType=VARCHAR}
            </when>
            <when test="number != null">
                and a.bill_number like concat('%',#{number,jdbcType=VARCHAR},'%')
            </when>
            <when test="billNumbers != null and billNumbers.size() > 0">
                and (
                <foreach collection="billNumbers" separator="or" item="billNumber">
                    a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
                </foreach>
                )
            </when>
        </choose>
        <if test="otypeIds != null and otypeIds.size() > 0">
            AND a.otype_id in
            <foreach item="item" index="index" collection="otypeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="commissionBtypeIds != null and commissionBtypeIds.size() > 0">
            AND a.commission_btype_id in
            <foreach item="item" index="index" collection="commissionBtypeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="btypeids != null and btypeids.size() > 0">
            AND a.btype_id in
            <foreach item="item" index="index" collection="btypeids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customSaleStages != null and customSaleStages.size() > 0">
            AND a.custom_sale_stage in
            <foreach item="item" index="index" collection="customSaleStages" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


        <if test="btypeId != null">
            and a.btype_id =#{btypeId}
        </if>
        <if test="etypeId != null and etypeId != 0">
            and a.etype_id =#{etypeId}
        </if>
        <if test="billNumber != null">
            and a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
        </if>
        <if test="billNumbers != null and billNumbers.size() > 0">
            and (
            <foreach collection="billNumbers" separator="or" item="billNumber">
                a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
            </foreach>
            )
        </if>
        <if test="otypeId != null">
            and a.otype_id =#{otypeId}
        </if>

        <if test="paymentType != null and paymentType == 1">
            and m.currency_balance_remain &lt;&gt; 0
        </if>

        <choose>
            <when test="checkStatus != -1">
                and (IF((pefcr.id is not null and pefcr.check_status = 3),4,IF(pefbr.id is null, 0,
                (IF((pefcr.check_status = 0 or
                pefcr.check_status is null), 1, IF(pefcr.flow_entry_state = 0, 2, 3))))))
                = #{checkStatus}
            </when>
        </choose>
        <choose>
            <when test="refundType != -1">
                and a.custom_refund_type = #{refundType} and (a.vchtype != 2000 or (a.vchtype = 2000 and a.custom_refund_type != 0))
            </when>
        </choose>
        <choose>
            <when test="invoiceStatusType != -1">
                <if test="invoiceStatusType == 0">
                    and (bii.id is null or bii.submit = #{invoiceStatusType})
                </if>
                <if test="invoiceStatusType != null and invoiceStatusType != 0">
                    and bii.submit = #{invoiceStatusType}
                </if>
            </when>
        </choose>
        <choose>
            <when test="invoiceType != null and invoiceType == 1">
                and bii.need_invoice=1
            </when>
            <when test="invoiceType != null and invoiceType == 2">
                and (bii.id is null or bii.need_invoice = 0)
            </when>
        </choose>
        <choose>
            <when test="saleOrbuy != null">
                <if test="ktypeId != null">
                    and (a.ktype_id =#{ktypeId} or a.ktype_id2 =#{ktypeId})
                </if>
            </when>
            <when test="ktypeIdList != null and ktypeIdList.size() > 0">
                AND (a.ktype_id in
                <foreach item="item" index="index" collection="ktypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
                a.ktype_id2 in
                <foreach item="item" index="index" collection="ktypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </when>
            <otherwise>
                <if test="ktypeId != null">
                    and a.ktype_id =#{ktypeId}
                </if>
                <if test="ktypeId2 != null">
                    and a.ktype_id2 =#{ktypeId2}
                </if>
            </otherwise>
        </choose>
        <if test="skuId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM acc_bill_detail_core_sale a WHERE a.profile_id =#{profileId}
            AND a.sku_id =#{skuId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <if test="comboId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM acc_bill_detail_combo a WHERE a.profile_id =#{profileId}
            AND a.combo_id =#{comboId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <if test="ptypeId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM acc_bill_detail_core_sale a WHERE a.profile_id =#{profileId}
            AND a.ptype_id =#{ptypeId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <if test="beginDate != null and endDate != null">
            and a.bill_date between #{beginDate} and #{endDate}
        </if>
        <if test="MinTotal != null">
            and a.bill_total <![CDATA[ >= ]]> #{MinTotal}
        </if>
        <if test="MaxTotal != null">
            and a.bill_total <![CDATA[ <= ]]> #{MaxTotal}
        </if>
        <if test="processType != null">
            and a.process_type = #{processType}
        </if>
        <if test="customerReceiverProvince != null">
            and p.customer_receiver_province =#{customerReceiverProvince}
        </if>
        <if test="customerReceiverCity != null">
            and p.customer_receiver_city =#{customerReceiverCity}
        </if>
        <if test="customerReceiverDistrict != null">
            and p.customer_receiver_district =#{customerReceiverDistrict}
        </if>
        <if test="showBackBill != null and showBackBill == true">
            and (a.process_type!=1 or (a.process_type=1 and (a.create_type=4 or a.create_type=5)))
        </if>
        <if test="showCompletedBill != null">
            <choose>
                <when test="showCompletedBill == true">
                    and b.ptype_completed_qty >= b.ptype_qty
                </when>
                <otherwise>
                    and b.ptype_qty > b.ptype_completed_qty
                </otherwise>
            </choose>
        </if>

        <if test="isOtypeLimited!=null and isOtypeLimited == true">
            and (blb.id is not null or a.otype_id=0)
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            and (bbs.id is not null or a.btype_id=0)
            and (bbs2.id is not null or a.commission_btype_id=0)
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            and (bbs1.id is not null or adfi.freight_btype_id=0 or adfi.freight_btype_id is null )
        </if>
        <if test="isKtypeLimited!=null and isKtypeLimited ==true">
            and (blk.id is not null or a.ktype_id=0)
        </if>
        <if test="freightBillNo != null and freightBillNo != ''">
            and b.freight_billno like concat('%',#{freightBillNo,jdbcType=VARCHAR},'%')
        </if>
        <if test="freightBtypeId != null and freightBtypeId != 0">
            and b.freight_btype_id = #{freightBtypeId}
        </if>

        <include refid="query-filter">
            <property name="_list" value="gridFilter"/>
        </include>

        group by a.vchcode
        <choose>
            <when test="orderColumn != null and orderColumn != ''">
                order by ${orderColumn}
            </when>
            <otherwise>
                ORDER BY a.bill_date DESC,a.update_time DESC
            </otherwise>
        </choose>
    </select>

    <sql id="query-filter">
        <if test="${_list} != null">
            <trim prefix=" and (" suffix=")">
                <foreach collection="${_list}" item="item" separator=" and ">
                    <choose>
                        <when test="item.magicQuery != null and item.magicQuery.size() != 0">
                            <trim prefix="(" suffix=")">
                                <foreach collection="item.magicQuery" item="magic_item" separator="or">
                                    ${magic_item.dataField} like concat('%',#{magic_item.value},'%')
                                </foreach>
                            </trim>
                        </when>
                        <when test="item.type == 0">
                            ${item.dataField} like concat('%',#{item.value},'%')
                        </when>
                        <when test="item.type == 1">
                            <trim prefixOverrides="and" prefix="(" suffix=")">
                                <if test="item.value1 != null">
                                    ${item.dataField} >= #{item.value1}
                                </if>
                                <if test="item.value2 != null">
                                    and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                                </if>
                            </trim>
                        </when>
                        <when test="item.type == 2">
                            ${item.dataField} = #{item.value}
                        </when>
                        <when test="item.type == 3">
                            ${item.dataField}
                        </when>
                        <when test="item.type == 4">
                            <trim prefixOverrides="and" prefix="(" suffix=")">
                                <if test="item.value1 != null">
                                    ${item.dataField} >= #{item.value1}
                                </if>
                                <if test="item.value2 != null">
                                    and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                                </if>
                            </trim>
                            <if test="item.value1 != null and item.value1 == 0">
                                or ${item.dataField} is null
                            </if>
                        </when>
                        <when test="item.type == 5">
                            tba2.pay_out_no like concat('%',#{item.value},'%') or bpay.fullname like
                            concat('%',#{item.value},'%')
                        </when>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>

    <sql id="zyJoin">
        left join pl_eshop_flow_bill_relation pefbr on pefbr.vchcode = a.vchcode and pefbr.profile_id = #{profileId}
        left join pl_eshop_payment_flow pepf on pepf.id = pefbr.flow_id and pepf.profile_id = #{profileId}
        left join pl_eshop_finance_check_result pefcr on pefcr.vchcode = a.vchcode and pefcr.profile_id = #{profileId}
        left join ops_cpa oc on oc.profile_id = #{profileId} and oc.id =(CASE b.product_account_id
        WHEN 0 THEN b.refund_account_id ELSE b.product_account_id END )

        left join base_ptype pt on pt.profile_id = #{profileId} and pt.id = oc.ptype_id
        left join ops_customer_source ocs on ocs.profile_id = #{profileId} and oc.otype_id = ocs.id
        left join base_etype bet on bet.profile_id = #{profileId} and bet.id = ocs.operational_etype_id
        left join base_customer_grade bcg on bcg.profile_id = #{profileId} and bcg.id = oc.customer_value_level
        <choose>
            <!--            直营 和 渠道-->
            <when test="saleOrderType == 0 or saleOrderType == 1">
                left join ops_cpa_belong ocb on ocb.profile_id = #{profileId} and ocb.cpa_id = (CASE
                b.product_account_id
                WHEN 0 THEN b.refund_account_id ELSE b.product_account_id END ) and
                ocb.proxy_id = 0
            </when>
            <when test="saleOrderType == 2">
                left join ops_cpa_belong ocb on ocb.profile_id = #{profileId} and ocb.cpa_id = (CASE
                b.product_account_id
                WHEN 0 THEN b.refund_account_id ELSE b.product_account_id END ) and
                ocb.proxy_id != 0
            </when>
            <!--            其他查询分销商-->
            <otherwise>
                left join ops_cpa_belong ocb on ocb.profile_id = #{profileId} and ocb.cpa_id = (CASE
                b.product_account_id
                WHEN 0 THEN b.refund_account_id ELSE b.product_account_id END )
            </otherwise>
        </choose>

        left join base_etype besu on besu.profile_id = #{profileId} and besu.id = ocb.sale_user
        left join base_etype besvu on besvu.profile_id = #{profileId} and besvu.id = ocb.service_user
        left join base_etype beru on beru.profile_id = #{profileId} and beru.id = ocb.renewal_user
        left join base_etype betu on betu.profile_id = #{profileId} and betu.id = ocb.train_user


        left join ops_cpa_belong ocb2 on ocb2.profile_id = #{profileId} and ocb2.cpa_id = (CASE b.product_account_id
        WHEN 0 THEN b.refund_account_id ELSE b.product_account_id END )
        and ocb2.belong_lower_proxy = 0
        left join base_btype bbty on bbty.profile_id = #{profileId} and bbty.id = ocb2.proxy_id
    </sql>

    <select id="getTdSumData" resultMap="BaseResultMap">
        select
               abdcs.vchcode,
               0-sum(ifnull(tbddb.buyer_dised_taxed_total, 0)) as buyerDisedTaxedTotal,
               0-sum(abdcs.cost_price * abdcs.qty) as costPrice,
               0-sum(abdcs.dised_taxed_total - abdcs.ptype_commission_total - (abdcs.cost_price * abdcs.qty)) as performanceTotal,
               (0-sum(ifnull(tbddb.buyer_dised_taxed_total, 0)) + sum(abdcs.dised_taxed_total - abdcs.ptype_commission_total - (abdcs.cost_price * abdcs.qty)) + sum(abdcs.cost_price * abdcs.qty)) as FXSIncome
        from td_bill_detail_core abdcs
        left join td_bill_detail_distribution_buyer tbddb on tbddb.profile_id = #{profileId} and abdcs.detail_id =
            tbddb.detail_id
        where abdcs.profile_id = #{profileId} and abdcs.vchcode in
        <foreach item="item" index="index" collection="vchcodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by abdcs.vchcode
    </select>

    <select id="getAccSumData" resultMap="BaseResultMap">
        select
        abdcs.vchcode,
        0-sum(ifnull(tbddb.buyer_dised_taxed_total, 0)) as buyerDisedTaxedTotal,
        0-sum(abdcs.cost_price * abdcs.qty) as costPrice,
        0-sum(abdcs.dised_taxed_total - abdcs.ptype_commission_total - (abdcs.cost_price * abdcs.qty)) as performanceTotal,
        (0-sum(ifnull(tbddb.buyer_dised_taxed_total, 0)) + sum(abdcs.dised_taxed_total - abdcs.ptype_commission_total - (abdcs.cost_price * abdcs.qty)) + sum(abdcs.cost_price * abdcs.qty)) as FXSIncome
        from acc_bill_detail_core_sale abdcs
        left join acc_bill_detail_distribution_buyer tbddb on tbddb.profile_id = #{profileId} and abdcs.detail_id =
        tbddb.detail_id
        where abdcs.profile_id = #{profileId} and abdcs.vchcode in
        <foreach item="item" index="index" collection="vchcodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by abdcs.vchcode
    </select>
</mapper>
