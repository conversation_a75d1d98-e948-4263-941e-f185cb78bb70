<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.PosPtypeMapper">

    <select id="getPtypeList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.Ptype">
        select bp.*, pic.pic_url
        from base_ptype bp
        left join base_ptype_pic pic
        on pic.profile_id = #{profileId} and pic.ptype_id = bp.id and pic.rowindex = 1
        where bp.profile_id = #{profileId}
        and bp.stoped = 0
        and bp.deleted = 0
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and bp.id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getPtypeList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from base_ptype
        where profile_id = #{profileId}
          and stoped = 0
          and deleted = 0
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and bp.id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getPriceList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.Price">
        select *
        from
        <choose>
            <when test="type == 1">
                pub_bill_price
            </when>
            <otherwise>
                base_ptype_price
            </otherwise>
        </choose>
        where profile_id = #{profileId}
        <if test="type == 1">
            and create_type = 4
        </if>
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getPriceList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from
        <choose>
            <when test="type == 1">
                pub_bill_price
            </when>
            <otherwise>
                base_ptype_price
            </otherwise>
        </choose>
        where profile_id = #{profileId}
        <if test="type == 1">
            and create_type = 4
        </if>
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getPtypeLimitList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.PtypeLimit">
        select *
        from base_ptype_limit_scope
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getPtypeLimitList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from base_ptype_limit_scope
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>


    <select id="getStockList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.Stock">
        select agd.id,
               agd.profile_id,
               agd.ktype_id,
               agd.sku_id,
               agd.ptype_id,
               (IFNULL(agd.qty, 0) - IFNULL(srqs.qty, 0)) as qty
        from acc_goodsstock_detail agd
                 left join stock_record_qty_sale srqs on srqs.profile_id = #{profileId}
            and srqs.ptype_id = agd.ptype_id and srqs.sku_id = agd.sku_id and srqs.ktype_id = agd.ktype_id
        where agd.profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and agd.ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getStockList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from acc_goodsstock_detail
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getSkuList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.Sku">
        select *
        from base_ptype_sku
        where profile_id = #{profileId}
          and stoped = 0
          and deleted = 0
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getSkuList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from base_ptype_sku
        where profile_id = #{profileId}
          and stoped = 0
          and deleted = 0
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getUnitList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.Unit">
        select *
        from base_ptype_unit
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getUnitList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from base_ptype_unit
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getComboDetailList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.ComboDetail">
        select *
        from base_ptype_combo_detail
        where profile_id = #{profileId}
          and stoped = 0
        <if test="comboIds!=null and comboIds.size()>0">
            and combo_id in
            <foreach collection="comboIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getComboDetailList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from base_ptype_combo_detail
        where profile_id = #{profileId}
          and stoped = 0
        <if test="comboIds!=null and comboIds.size()>0">
            and combo_id in
            <foreach collection="comboIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getFullBarcodeList"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.FullBarcode">
        select *
        from base_ptype_fullbarcode
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getFullBarcodeList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from base_ptype_fullbarcode
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getXcodeList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.PtypeXcode">
        select *
        from base_ptype_xcode
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getXcodeList_COUNT" resultType="java.lang.Integer">
        select count(0)
        from base_ptype_xcode
        where profile_id = #{profileId}
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" open="(" item="id" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getPriceById" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.Price">
        select *
        from
        <choose>
            <when test="type == 1">
                pub_bill_price
            </when>
            <otherwise>
                base_ptype_price
            </otherwise>
        </choose>
        where profile_id = #{profileId}
        <if test="type == 1">
            and xtype_id = #{otypeId}
            and create_type = 4
        </if>
        and sku_id = #{skuId}
        and unit_id = #{unitId}
        and ptype_id = #{ptypeId}
    </select>

    <select id="getPtypeLabels" resultType="com.wsgjp.ct.sale.biz.jarvis.entity.baseinfo.PtypeLabel">
        select id, labelfield_value as fullname, enabled
        from cf_labelfield_value
        where profile_id = #{profileId}
            and enabled = 1
          and labelfield_id in
              (select id from cf_labelfield_list where profile_id = #{profileId} and labelfield_name = '商品标签')
    </select>

    <select id="getDataLabelPtypeList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.local.DataLabelPtype">
        SELECT
            cdlp.id,
            cdlp.profile_id,
            cdlp.resource_id,
            cdlp.scene_field_id,
            cdlp.labelfield_value_id
        FROM cf_data_label_ptype cdlp
        WHERE cdlp.profile_id = #{profileId}
        <if test="resourceId != null">
            AND cdlp.resource_id = #{resourceId}
        </if>
        ORDER BY cdlp.id DESC
    </select>

    <select id="getDataLabelPtypeList_COUNT" resultType="java.lang.Integer">
        SELECT count(0)
        FROM cf_data_label_ptype cdlp
        WHERE cdlp.profile_id = #{profileId}
        <if test="resourceId != null">
            AND cdlp.resource_id = #{resourceId}
        </if>
    </select>

</mapper>