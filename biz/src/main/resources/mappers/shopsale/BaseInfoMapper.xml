<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.BaseInfoMapper">
    <select id="getBtypeByName" resultType="com.wsgjp.ct.baseinfo.core.dao.entity.Btype">
        SELECT b.id
             , b.profile_id
             , b.typeid
             , b.partypeid
             , b.fullname
             , b.usercode
             , b.stoped
             , b.deleted
             , b.create_time
             , b.update_time
             , b.shortname
             , b.postcode
             , b.tel
             , b.person
             , b.tax_number
             , b.bank
             , b.bank_account
             , b.memo
             , b.ar_warnup
             , b.warnup_enabled
             , b.fax
             , b.rowindex
             , b.namepy
             , b.email
             , b.phone
             , b.areatype_id
             , b.sysrow
             , b.freighted
             , b.currency_id
             , b.tax
             , b.sale_discount
             , b.buy_discount
             , b.payment_days
             , b.freight_alias
             , b.classed
             , b.register_addr
             , b.payment_type
             , b.stop_time
             , b.create_etype_id
             , b.modified_etype_id
             , b.acc_type
             , b.distribution_etype_id
             , b.pay_btype_id
             , bbe.price_level
        from base_btype b
                 left join base_btype_extend bbe on bbe.profile_id = b.profile_id and bbe.btype_id = b.id
        where b.fullname = #{btypeName}
          and b.profile_id = #{profileId}
          and b.deleted = 0
          and b.stoped = 0
          and b.classed = 0
    </select>

    <select id="getKtypeByName" resultType="com.wsgjp.ct.baseinfo.core.dao.entity.Ktype">
        SELECT id,
               profile_id,
               typeid,
               partypeid,
               usercode,
               fullname,
               shortname,
               namepy,
               scategory,
               classed,
               stoped,
               deleted,
               rowindex,
               memo,
               sysrow,
               create_time,
               update_time,
               btype_id,
               blong_type,
               etype_id
        from base_ktype
        where fullname = #{ktypeName}
          and profile_id = #{profileId}
          and deleted = 0
          and stoped = 0
          and classed = 0
    </select>


    <delete id="deleteOtypes">
        update base_otype set deleted=1 where profile_id=#{profileId} and
        id in
        <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getPtypeTypeIds" resultType="Map">
        select id, typeid from base_ptype bp
        <where>
            profile_id=#{profileId} and
            id in
            <foreach collection="ptypeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <delete id="deleteOtype">
        update base_otype
        set deleted=1
        where profile_id = #{profileId}
          and id = #{otypeId}
    </delete>

    <select id="listOtypes" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseOtype">
        select id, fullname, 0 as eshop_type, ocategory, 1 as enabled,stoped
        from base_otype
        <where>
            profile_id = #{params.profileId}
            <if test="params.stoped!=null and params.stoped >-1">
                and stoped=#{params.stoped}
            </if>
            <if test="params.storeType!=null and params.storeType >-1">
                and store_type=#{params.storeType}
            </if>
            and deleted = 0
        </where>
    </select>

    <select id="listBtypes" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseOtype">
        select bb.id, bb.fullname, 0 as eshop_type, 1 as enabled,bb.stoped
        from base_btype bb left join base_btype_bcategory bbb on bbb.profile_id = #{params.profileId} and bbb.btype_id =
        bb.id
        <where>
            bb.profile_id = #{params.profileId}
            <if test="params.stoped!=null and params.stoped >-1">
                and bb.stoped=#{params.stoped}
            </if>
            and bb.deleted = 0 and bbb.bcategory != 2 and bb.classed = 0
        </where>
    </select>


    <select id="getEtypeById" resultType="com.wsgjp.ct.sale.biz.shopsale.model.vo.baseinfo.Etype">
        SELECT id,
               profile_id,
               typeid,
               partypeid,
               usercode,
               fullname,
               shortname,
               namepy,
               classed,
               stoped,
               deleted,
               rowindex,
               create_time,
               update_time,
               tel,
               mobile,
               address,
               memo,
               birthday,
               email,
               login_user,
               dtype_id,
               sysid,
               etype_limited,
               ktype_limited,
               otype_limited,
               btype_limited,
               atype_limited,
               ptype_limited,
               btype_id,
               work_status
        from base_etype
        where profile_id = #{profileId}
          and id = #{etypeId}
        limit 1
    </select>

    <select id="getBtypeById" resultType="com.wsgjp.ct.baseinfo.core.dao.entity.Btype">
        SELECT id,
               profile_id,
               typeid,
               partypeid,
               fullname,
               usercode,
               stoped,
               deleted,
               create_time,
               update_time,
               shortname,
               postcode,
               tel,
               person,
               tax_number,
               bank,
               bank_account,
               memo,
               ar_warnup,
               warnup_enabled,
               fax,
               rowindex,
               namepy,
               email,
               phone,
               areatype_id,
               sysrow,
               freighted,
               currency_id,
               tax,
               sale_discount,
               buy_discount,
               payment_days,
               freight_alias,
               classed,
               register_addr,
               payment_type,
               stop_time,
               create_etype_id,
               modified_etype_id,
               acc_type,
               distribution_etype_id,
               pay_btype_id
        from base_btype
        where id = #{btypeId}
    </select>

    <select id="getKtypeById" resultType="com.wsgjp.ct.baseinfo.core.dao.entity.Ktype">
        SELECT id,
               profile_id,
               typeid,
               partypeid,
               usercode,
               fullname,
               shortname,
               namepy,
               scategory,
               classed,
               stoped,
               deleted,
               rowindex,
               memo,
               sysrow,
               create_time,
               update_time,
               btype_id,
               blong_type,
               etype_id
        from base_ktype
        where id = #{ktypeId}
    </select>

    <select id="getAtypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Atype">
        SELECT id,
               profile_id,
               typeid,
               partypeid,
               classed,
               fullname,
               shortname,
               namepy,
               usercode,
               memo,
               sysrow,
               deleted,
               rowindex,
               currency_id,
               stoped,
               create_time,
               update_time,
               tax_number,
               tax_rate,
               invoice_fullname
        from base_atype
        where id = #{atypeId}
    </select>

    <select id="getStoreMoneyAtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Atype">
        select id,
               profile_id,
               typeid,
               partypeid,
               classed,
               fullname,
               shortname,
               namepy,
               usercode,
               memo
        from base_atype
        where deleted = 0
          and profile_id = #{profileId}
          and typeid = '000020000500002'
    </select>

    <select id="selectBaseInfo" resultType="java.util.HashMap">
        select fullname,(deleted+0) as del,usercode from ${tableName}
        where id=#{id}
        <if test="stoped != null">
            and stoped =#{stoped}
        </if>
    </select>

    <select id="baseInfoLimit" resultType="java.math.BigInteger">
        select count(id)
        from base_limit_scope
        where profile_id = #{profileId}
          and etype_id = #{etypeId}
          and object_type = #{objectType}
          and object_id = #{objectId}
    </select>

    <select id="selectComboIsDelete" resultType="java.math.BigInteger">
        select combo_id
        from base_ptype_combo
        where combo_id = #{id}
    </select>

    <select id="listStopSkuId" resultType="java.math.BigInteger">
        select id from base_ptype_sku
        where profile_id=#{profileId} and stoped=1
        <if test="skuIds.size() > 0">
            and id in
            <foreach collection="skuIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getComboSkuId" resultType="java.math.BigInteger">
        select sku_id
        from base_ptype_combo_detail
        where profile_id = #{profileId}
          and combo_id = #{ptypeId}
    </select>
    <select id="getKtypeList" resultType="com.wsgjp.ct.baseinfo.core.dao.entity.Ktype">
        SELECT id,
               profile_id,
               typeid,
               partypeid,
               usercode,
               fullname,
               shortname,
               namepy,
               scategory,
               classed,
               stoped,
               deleted,
               rowindex,
               memo,
               sysrow,
               create_time,
               update_time,
               btype_id,
               blong_type,
               etype_id
        from base_ktype
        where profile_id = #{profileId}
          and deleted = 0
          and stoped = 0
    </select>
    <select id="getBasePaywayList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StorePayway">
        SELECT p.id as paywayId,p.profile_id,p.usercode,p.fullname as
        paywayName,p.memo,p.stoped,p.payway_type,p.atype_id,
        a.fullname as atypeFullname,a.usercode as ausercode
        FROM base_payways p
        LEFT JOIN base_atype a ON p.atype_id =a.id
        <where>
            p.profile_id= #{profileId,jdbcType=BIGINT} and p.deleted=0 and p.stoped=0
            <if test="params.filterkey != null and params.filterkey != ''">
                AND (p.fullname like CONCAT('%',#{params.filterkey},'%') or p.usercode like
                CONCAT('%',#{params.filterkey},'%'))
            </if>
            <if test="params.paywayTypeList != null and params.paywayTypeList.size() > 0">
                and p.payway_type in
                <foreach collection="params.paywayTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.excludePaywayType != null and params.excludePaywayType.size() > 0">
                and (p.payway_type is null or p.payway_type not in
                <foreach collection="params.excludePaywayType" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>)
            </if>
        </where>
    </select>

    <!--根据职员etypeId获取部门dtypeId-->
    <select id="selectDtypeIdByEtypeId" resultType="java.math.BigInteger">
        select dtype_id
        from base_etype
        where profile_id = #{profileId,jdbcType=BIGINT}
          and id = #{etypeId,jdbcType=BIGINT}
    </select>

    <!--    查询职员是否是部门管理员-->
    <select id="isDTypeAdmin" resultType="java.lang.Boolean">
        select count(*) > 0
        from base_dtype bd
        where bd.etype_id = #{etypeId,jdbcType=BIGINT}
          and bd.profile_id = #{profileId,jdbcType=BIGINT};
    </select>

    <select id="getDtypeId" resultType="java.lang.String">
        select concat(bd.typeid, '%')
        from base_dtype bd
        where bd.etype_id = #{etypeId,jdbcType=BIGINT}
          and bd.profile_id = #{profileId,jdbcType=BIGINT};
    </select>

    <select id="queryBasePaywaysByName" resultType="java.math.BigInteger">
        select id
        from base_payways
        where profile_id = #{profileId,jdbcType=BIGINT}
          and fullname = #{paymentName}
    </select>

    <!--查询门店以及其关联的仓库-->
    <select id="selectOtypeWithKtypeInfo" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.store.OtypeWithKtypeDTO">
        select bo.id as otype_id, bo.fullname as ofullname, ktype_id, bk.fullname as kfullname
        from base_otype bo
                 inner join base_ktype bk on bk.profile_id = #{profileId}
            and bo.ktype_id = bk.id and bk.stoped = 0 and bk.deleted = 0
        where bo.profile_id = #{profileId}
          and bo.stoped = 0
          and bo.deleted = 0
          and bo.ocategory = 4
    </select>
    <!--    查询会员储值密码-->
    <select id="getVipPassword" resultType="string">
        select svc.password
        from ss_vip_card svc
        where svc.profile_id = #{profileId}
          and svc.id = #{vipCardId}
    </select>
    <!--    更新会员储值密码-->
    <update id="updateVipPassword">
        update ss_vip_card
        set password = #{password}
        where profile_id = #{profileId}
          and id = #{vipCardId}
    </update>

    <select id="selectAtypeByTypeId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Atype">
        select *
        from base_atype
        where profile_id = #{profileId}
          and typeid = #{typeId}
          and classed = 0
          and deleted = 0
          and stoped = 0
    </select>

    <update id="updateCheckModel">
        update td_stock_check
        set etype_id = #{etypeId}
        where profile_id = #{profileId}
          and id = #{checkId}
    </update>

    <select id="getOtypeNameList" resultType="java.lang.String">
        select fullname from base_otype where profile_id = #{profileId} and id in
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getEtypeNameById" resultType="java.lang.String">
        select fullname
        from base_etype
        where id = #{etypeId}
          and deleted = 0
    </select>
    <select id="getEtypeList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.vo.baseinfo.Etype">
        select
        be.id,
        be.fullname,
        be.usercode,
        bd.fullname as dtypeName,
        be2.fullname as dEtypeName
        from
        base_etype be
        left join base_dtype bd on
        be.dtype_id = bd.id
        and bd.profile_id = #{profileId,jdbcType=BIGINT}
        left join base_etype be2 on
        be2.id = bd.etype_id
        and be2.profile_id = #{profileId,jdbcType=BIGINT}
        <where>
            be.profile_id= #{profileId,jdbcType=BIGINT} and be.deleted = 0
            and be.stoped = 0
            <if test="params.filterkey != null and params.filterkey != ''">
                AND (be.fullname like CONCAT('%',#{params.filterkey},'%') or be.usercode like
                CONCAT('%',#{params.filterkey},'%') or bd.fullname like CONCAT('%',#{params.filterkey},'%') )
            </if>
        </where>
        group by
        be.id
    </select>
</mapper>
