<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.BaseShopSaleAtypePayMapper">

    <insert id="addBaseAtypePay" >
        insert into base_atype_pay (id, profile_id, atype_id, paytype, content, remark, payway_id)
            values (#{id}, #{profileId}, #{atypeId}, #{paytype}, #{content}, #{remark}, #{paywayId})
    </insert>

    <update id="updateBaseAtypePay">
        update base_atype_pay
        <set>
            <if test="id != null">
                id = #{id},
            </if>
            <if test="profileId != null">
                profile_id = #{profileId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="atypeId != null">
                atype_id = #{atypeId},
            </if>
            <if test="paytype != null">
                payType = #{paytype},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="paywayId != null">
                payway_id = #{paywayId},
            </if>
        </set>
        where profile_id = #{profileId} and id = #{id}
    </update>

    <delete id="deleteBaseAtypePay">
        delete from base_atype_pay where profile_id = #{profileId} and id = #{id}
    </delete>

    <select id="getAtypeById" resultType="com.wsgjp.ct.sale.biz.jarvis.dto.BaseAtypeDAO">
        select id, profile_id, partypeid, deleted from base_atype where profile_id = #{profileId} and id = #{id}
    </select>

    <select id="getAtypePayByPaywayId" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseAtypePay">
        select bap.id, bap.profile_id, bap.create_time, bap.update_time, bap.atype_id, bp.payway_type, bap.paytype, bap.content, bap.remark, bap.payway_id, bp.product
        from base_atype_pay bap
        left join base_payways bp on bap.payway_id = bp.id and bp.profile_id = #{profileId}
        where bap.profile_id = #{profileId}
        and bap.payway_id = #{paywayId}
        <if test="payType != null">
            and bap.paytype = #{payType}
        </if>
    </select>

    <select id="getPaywayById" resultType="java.math.BigInteger">
        select id from base_payways where profile_id = #{profileId} and id = #{id}
    </select>

    <select id="getDevices" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseAtypePayDevice">
        select id, profile_id, create_time, update_time, atype_pay_id, device_type, device_id, device_sn
        from base_atype_pay_device where profile_id = #{profileId} and atype_pay_id = #{atypePayId}
    </select>

    <insert id="insertDevices">
        insert into base_atype_pay_device (id, profile_id, atype_pay_id, device_type, device_id, device_sn)
        values
        <foreach collection="list" item="device" separator=",">
            (#{device.id}, #{device.profileId}, #{device.atypePayId}, #{device.deviceType}, #{device.deviceId}, #{device.deviceSn})
        </foreach>
    </insert>

    <delete id="deleteDevices">
        delete from base_atype_pay_device where profile_id = #{profileId} and atype_pay_id = #{atypePayId}
    </delete>

    <select id="getPlatformPayTypeByPaywayId" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseAtypePay">
        select payway_type, product from base_payways where profile_id = #{profileId} and id = #{paywayId}
    </select>

    <select id="getAtypePay" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseAtypePay">
        select bap.id, bap.profile_id, bap.create_time, bap.update_time, bap.atype_id, bap.paytype, bap.content, bap.remark, bap.payway_id
        from base_atype_pay bap
        where bap.profile_id = #{profileId}
        <if test="payType != null">
            and bap.paytype = #{payType}
        </if>
    </select>
</mapper>