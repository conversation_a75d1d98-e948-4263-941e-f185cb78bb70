<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.PromotionOtypeMapper">
    <insert id="saveBatch">
        insert into ss_promotion_filter
        (id,promotion_id,filter_id,profile_id)
        values
        <foreach collection="otypeIds" item="item" separator=",">
            ( #{item.id},
            #{id},
            #{item.pid},
            #{profileId})
        </foreach>
    </insert>

    <select id="getPromotionId" resultType="java.math.BigInteger">
        select
        spf.promotion_id
        from
        ss_promotion_filter spf
        where
        spf.profile_id=#{profileId}
        and
        spf.filter_id
        in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and
        spf.deleted=0
    </select>
</mapper>