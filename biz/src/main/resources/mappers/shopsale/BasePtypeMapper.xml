<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.BasePtypeMapper">
    <sql id="selectPtypeList_Where">
        bp.profile_id = #{profileId}
        <!--仅查询启用商品-->
        and bp.stoped = 0
        and bp.deleted = 0
        and bp.classed = 0
        <!--库存查询时排除套餐-->
        <if test="stockSelect!=null and stockSelect==true">
            and bp.pcategory != 2
        </if>
        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-where-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit_pcategory" value="bp.pcategory"/>
            </include>
        </if>
        <if test="typeId!=null and typeId!=''">
            and bp.typeid like concat(#{typeId}, '%')
        </if>
        <!--仅查询启用sku和套餐,套餐sku为空-->
        and (bp.pcategory = 2 or bps.stoped = 0)
        <if test="filterValue!=null and filterValue!=''">
            <trim prefix="and (" suffixOverrides="or" suffix=")">
                <choose>
                    <when test="fullBarCode!=true">
                        <!--商品名称-->
                        bp.fullname like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                        <!--商品编码-->
                        bp.usercode like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                        <!--商品拼音码-->
                        bp.namepy like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                        <!--商品码，主要是用于套餐时，套餐的fullbarcode取得就是商品表的barcode,对于普通商品，存的是商品码-->
                        (bp.barcode like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') and bp.pcategory = 2) or
                        <!--商品条码-->
                        bpf.fullbarcode in
                        (select bpf.fullbarcode from base_ptype_fullbarcode bpf where bpf.profile_id = #{profileId}
                        and bpf.ptype_id = bp.id and bpf.sku_id = bps.id and bpf.unit_id = bpu.id and bpf.fullbarcode
                        like concat('%',#{filterValue,jdbcType=VARCHAR}, '%')) or
                    </when>
                    <otherwise>
                        <!--扫码搜索时精确匹配-->
                        (bp.barcode = #{filterValue,jdbcType=VARCHAR} and bp.pcategory = 2)
                        or bpf.fullbarcode = #{filterValue,jdbcType=VARCHAR}
                        <!--条码称时，需要额外匹配条码称拆出来的商品条码-->
                        <if test="skuBarCode!=null and skuBarCode!=''">
                            or bpx.xcode = #{skuBarCode,jdbcType=VARCHAR}
                        </if>
                    </otherwise>
                </choose>


                <!--商品条码-->
                <!--                bpf.fullbarcode in (select bpf.fullbarcode from base_ptype_fullbarcode bpf where bpf.profile_id =-->
                <!--                #{profileId}-->
                <!--                and bpf.ptype_id = bp.id and bpf.sku_id = bps.id and bpf.unit_id = bpu.id and bpf.fullbarcode like-->
                <!--                concat('%',-->
                <!--                #{filterValue,jdbcType=VARCHAR}, '%')) or-->
                <!--                &lt;!&ndash;往来单位商品编码&ndash;&gt;-->
                <!--                bpus.usercode in (select bpus.usercode from base_ptype_usercodes bpus where bpus.profile_id =-->
                <!--                #{profileId}-->
                <!--                and bpus.ptype_id = bp.id and bpus.btype_id=#{btypeId,jdbcType=BIGINT} and bpus.usercode like-->
                <!--                concat('%',-->
                <!--                #{filterValue,jdbcType=VARCHAR}, '%')) or-->
                <!--                &lt;!&ndash;商家编码&ndash;&gt;-->
                <!--                bpx.xcode in (select bpx.xcode from base_ptype_xcode bpx where bpx.profile_id = #{profileId}-->
                <!--                and bpx.ptype_id = bp.id and bpx.sku_id = bps.id and bpx.unit_id = bpu.id and bpx.xcode like concat('%',-->
                <!--                #{filterValue,jdbcType=VARCHAR}, '%')) or-->
            </trim>
        </if>
    </sql>

    <select id="getStockQtyAndSaleQty" resultType="java.util.HashMap">
        select IFNULL(agd.qty, 0) as stockQty, IFNULL(agd.qty, 0) - IFNULL(srqs.qty, 0) as saleQty,agd.sku_id as skuId
        from acc_goodsstock_detail agd
        left join stock_record_qty_sale srqs
        on srqs.profile_id = #{profiledId}
        and srqs.sku_id = agd.sku_id
        and srqs.ktype_id = agd.ktype_id
        and srqs.ptype_id = agd.ptype_id
        where
        agd.sku_id
        in
        <foreach collection="dto" index="index" item="item" open="(" separator="," close=")">
            #{item.skuId}
        </foreach>
        and agd.ktype_id = #{ktypeId}
        and agd.profile_id=#{profiledId}
    </select>

    <select id="getGoodsDtails" resultType="java.lang.String">
        select
        concat_ws('_',ptype.fullname,if(sku.propvalue_names='',null,sku.propvalue_names),
        if(unit.unit_name='',null,unit.unit_name))
        from
        base_ptype ptype
        left join
        base_ptype_sku as sku
        on
        sku.profile_id=ptype.profile_id and sku.ptype_id=ptype.id and sku.deleted=0
        left join
        base_ptype_unit as unit
        on
        unit.profile_id=ptype.profile_id and unit.ptype_id=ptype.id
        where
        ptype.id
        in
        <foreach collection="dto" index="index" item="item" open="(" separator="," close=")">
            #{item.goodsId}
        </foreach>
        and
        sku.id
        in
        <foreach collection="dto" index="index" item="item" open="(" separator="," close=")">
            #{item.skuId}
        </foreach>
        and
        unit.id
        in
        <foreach collection="dto" index="index" item="item" open="(" separator="," close=")">
            #{item.unitId}
        </foreach>
        and
        ptype.profile_id=#{profiledId}
        and
        ptype.deleted=0
    </select>


    <select id="getInStockSn" resultType="java.lang.String">
        select snno from acc_inventory_serialno
        where ktype_id = #{ktypeId}
        and profile_id = #{profileId}
        and snno in
        <foreach collection="sns" index="index" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
    </select>


    <insert id="insetPriceToOtypePriceList">
        insert into pub_bill_price (profile_id, id, ptype_id, unit_id, sale_price, create_type, sku_id,
                                    xtype_id, sale_otype_vip_price)
        values (#{profileId}, #{id}, #{ptypeId}, #{unitId}, #{salePrice}, 4, #{skuId}, #{xtypeId},
                #{vipPrice})
    </insert>

    <select id="getPriceFromOtypePriceList" resultType="java.util.HashMap">
        SELECT id, profile_id, ptype_id, unit_id, buy_discount, sale_discount, buy_price, sale_price, last_sale_price,
        last_sale_discount, last_sale_time, last_buy_price, last_buy_discount, last_buy_time, create_time, update_time,
        create_type, sku_id, xtype_id, sale_otype_vip_price
        from pub_bill_price pbp
        where profile_id = #{profileId}
        and xtype_id = #{otypeId}
        and create_type = 4
        and (ptype_id,unit_id,sku_id) in
        <foreach collection="goods" index="index" item="item" open="(" separator="," close=")">
            (#{item.ptypeId},#{item.unitId},#{item.skuId})
        </foreach>
    </select>

    <!--查询商品是否开启sku定价管理-->
    <select id="selectPtypeSkuPriceEnabled"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePriceRequestDTO$Ptype">
        SELECT id as ptype_id,sku_price
        FROM base_ptype
        where profile_id = #{profileId} and id in
        <foreach collection="ptypeIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--查询商品价格-->
    <select id="getPtypePrice" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePriceDTO">
        SELECT
        <trim suffixOverrides=",">
            temp.*,
            <if test="request.searchRetailPrice!=null and request.searchRetailPrice == true">
                IFNULL(bpp.retail_price,0) as retail_price,
            </if>
            <if test="request.otypeId!=null">
                IFNULL(pbp.sale_price,0) as otype_price,
                IFNULL(pbp.sale_otype_vip_price,0) as vip_price,
            </if>
        </trim>
        FROM
        <!--创建临时表，包含ptypeId,skuId,unitId,skuPrice,小表驱动大表-->
        <foreach collection="request.goodsList" index="index" item="item" open="(" separator="UNION ALL" close=")">
            SELECT
            #{index} as primaryId,
            #{item.ptypeId} as ptype_id,
            #{item.unitId} as unit_id,
            #{item.skuPrice} as sku_price,
            <choose>
                <when test="item.skuPrice!=null and item.skuPrice==1">
                    #{item.skuId} as sku_id
                </when>
                <otherwise>
                    0 as sku_id
                </otherwise>
            </choose>
        </foreach>
        temp
        <if test="request.searchRetailPrice!=null and request.searchRetailPrice == true">
            LEFT JOIN base_ptype_price bpp
            ON bpp.profile_id = #{profileId}
            AND bpp.sku_id = temp.sku_id
            AND bpp.unit_id = temp.unit_id
            AND bpp.ptype_id = temp.ptype_id
        </if>
        <if test="request.otypeId!=null">
            LEFT JOIN pub_bill_price pbp
            ON pbp.profile_id = #{profileId}
            AND pbp.xtype_id = #{request.otypeId}
            AND pbp.create_type = 4
            AND pbp.ptype_id = temp.ptype_id
            AND pbp.unit_id = temp.unit_id
            AND pbp.sku_id = temp.sku_id
        </if>
        <!--当价格本中没有价格时，不会返回结果，导致前端更改单位或者sku，不会更改价格，应该改为0-->
        <!--        WHERE-->
        <!--        <trim suffixOverrides="OR">-->
        <!--            <if test="request.searchRetailPrice!=null and request.searchRetailPrice == true">-->
        <!--                bpp.id IS NOT NULL OR-->
        <!--            </if>-->
        <!--            <if test="request.otypeId!=null">-->
        <!--                pbp.id IS NOT NULL-->
        <!--            </if>-->
        <!--        </trim>-->
        GROUP BY temp.primaryId
    </select>

    <select id="getPtypeIdAndSkuIdByPtypeId" resultType="com.wsgjp.ct.sale.biz.bill.model.dto.ZyPtypeResult">
        select bp.id         as ptypeId,
               bp.pcategory,
               bp.standard,
               bp.ptype_type as ptypeType,
               bps.id        as skuId,
               bpu.id        as unitId
        from base_ptype bp
                 left join base_ptype_sku bps on bp.id = bps.ptype_id and bps.profile_id = #{profileId}
                 left join base_ptype_unit bpu on bp.id = bpu.ptype_id and bpu.profile_id = #{profileId}
        where bp.id = #{ptypeId}
          and bp.profile_id = #{profileId}
        order by bpu.create_time
    </select>

    <!--当sku价格管理关闭后，base_ptype_price这张表只关联ptypeId和unitId，而skuId为0-->
    <!--商品pcategory，1=》实物商品，2=》虚拟商品 3=》套餐，根据值可以判断是否是套餐，无需查询套餐表-->
    <!--对于套餐，套餐价格被同时存在套餐表的total字段，以及商品价格表的retail_price字段,另外若需要取其他售价，例如预设售价，则必须使用价格表，所以无需查询套餐表-->
    <!--对于价格表中的套餐，其skuId为0。与其类似，当商品未开启sku价格管理，即所有sku都是一个价格，此时对应价格表中skuId也是0。这两种情况下，其sku价格管理字段sku_price均等于0-->
    <select id="selectPtypeList" resultMap="ptypeResponseMap">
        select
        <!--套餐的skuId为空，会导致concat函数生成null-->
        concat(bp.id,ifnull(bps.id,0),ifnull(bpu.id,0)) as primaryId,
        bp.id, bp.profile_id, bp.typeid, bp.partypeid, bp.usercode, bp.fullname, bp.shortname, bp.namepy, bp.classed,
        bp.stoped, bp.deleted, bp.rowindex, bp.barcode, bp.standard, bp.ptype_type, bp.ptype_area, bp.memo,
        bp.create_type,
        bp.cost_mode, bp.pcategory, bp.tax_number, bp.tax_rate, bp.cost_price, bp.brand_id, bp.ktype_limit,
        bp.snenabled, bp.propenabled,
        bp.batchenabled, bp.protect_days, bp.protect_days_unit, bp.protect_days_view, bp.protect_warndays,
        bp.protect_warndays_unit,
        bp.weight, bp.weight_unit,
        bp.retail_default_unit, bp.sale_default_unit, bp.buy_default_unit, bp.stock_default_unit, bp.ptype_length,
        bp.ptype_width,
        bp.ptype_height, bp.length_unit, bp.create_time, bp.update_time, bp.sku_price, bp.propvalues_descart_count,
        bp.fullbarcode_rule_id,
        bp.sub_unit, bp.share_type, bp.invoice_fullname, bp.preparation_type, bp.industry_category, bp.audit_state,
        bp.buy_days,
        <!--套餐并没有关联base_ptype_fullbarcode这张表，套餐条码直接取商品表的barcode字段-->
        if(bp.pcategory != 2,bpf.fullbarcode,bp.barcode) as fullbarcode,
        pic.pic_url,
        1 as count,
        <!--套餐-->
        if(bp.pcategory = 2,bp.id,null) as combo_id,IF(bp.pcategory = 2,1,0) as comboRow,
        <!--优先取门店价格本的价格(不为零不为空)，再取零售价-->
        IF(IFNULL(pbp.sale_price,0) <![CDATA[>]]> 0,pbp.sale_price,IFNULL(bpp.retail_price,0)) as currencyPrice,
        <!--店铺价格本会员价-->
        pbp.sale_otype_vip_price,
        <!--sku信息-->
        bps.id as sku_id, bps.prop_id1, bps.prop_name1, bps.propvalue_id1, bps.propvalue_name1, bps.prop_id2,
        bps.prop_name2,bps.propvalue_id2, bps.propvalue_name2,bps.prop_id3, bps.prop_name3, bps.propvalue_id3,
        bps.propvalue_name3,bps.prop_id4, bps.prop_name4, bps.propvalue_id4, bps.propvalue_name4, bps.prop_id5,
        bps.prop_name5,bps.propvalue_id5, bps.propvalue_name5, bps.prop_id6, bps.prop_name6, bps.propvalue_id6,
        bps.propvalue_name6, bps.pic_url as sku_pic_url,bps.memo as sku_memo, bps.prop_names, bps.propvalue_names,
        bps.cost_price as sku_cost_price,bpx.xcode,
        <!--unit信息-->
        bpu.id as unit_id,bpu.unit_code,bpu.unit_name, bpu.unit_rate,bpu.barcode as unit_barcode,bpu.ptype_weight
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            ,(IFNULL(agd.qty,0) - IFNULL(srqs.qty,0)) as stockQty,IFNULL(agd.qty,0) as qty
        </if>
        from base_ptype bp
        <!--sku-->
        left join base_ptype_sku bps on bps.profile_id = #{profileId}
        and bps.ptype_id = bp.id and bps.stoped = 0 and bps.deleted = 0
        <!--单位-->
        left join base_ptype_unit bpu on bpu.profile_id = #{profileId} and bpu.ptype_id = bp.id
        <!--商品条码-->
        left join base_ptype_fullbarcode bpf on bpf.profile_id = #{profileId}
        and bpf.ptype_id = bp.id and bpf.sku_id = bps.id and bpf.unit_id = bpu.id
        <!--                &lt;!&ndash;往来单位商品编码表&ndash;&gt;-->
        <!--                left join base_ptype_usercodes bpus on bpus.profile_id = #{profileId}-->
        <!--                and bpus.ptype_id = bp.id and bpus.btype_id=#{btypeId,jdbcType=BIGINT}-->
        <!--                &lt;!&ndash;商家编码表，商家编码&ndash;&gt;-->
        left join base_ptype_xcode bpx on bpx.profile_id = #{profileId}
        and bpx.ptype_id = bp.id and bpx.sku_id = bps.id and bpx.unit_id = bpu.id
        <!--图片，只取一张-->
        left join base_ptype_pic pic on pic.profile_id = #{profileId} and pic.ptype_id = bp.id and pic.rowindex = 1
        <!--商品价格表-->
        left join base_ptype_price bpp on bpp.profile_id = #{profileId}
        and bpp.ptype_id = bp.id and bpp.unit_id = bpu.id
        <!--套餐和未开启sku价格管理的情况下，价格表中skuId为0-->
        and bpp.sku_id = if(bp.sku_price = 1,bps.id,0)
        <!--门店价格表-->
        left join pub_bill_price pbp on pbp.profile_id = #{profileId} and pbp.xtype_id = #{otypeId}
        and pbp.create_type = 4 and pbp.ptype_id = bp.id and pbp.unit_id = bpu.id
        <!--套餐和未开启sku价格管理的情况下，价格表中skuId为0-->
        and pbp.sku_id = if(bp.sku_price = 1,bps.id,0)
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            left join acc_goodsstock_detail agd on agd.profile_id = #{profileId}
            and agd.sku_id =bps.id and agd.ktype_id = #{ktypeId}
            left join stock_record_qty_sale srqs on srqs.profile_id = #{profileId}
            and srqs.sku_id = bps.id and srqs.ktype_id = #{ktypeId}
        </if>

        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit-alias-profileid" value="bp.profile_id"/>
            </include>
        </if>

        <!--这里可以用这个表来判断当前操作员是否有权限查询这个商品-->
        <!-- left join base_ptype_limit_scope _blsPtype on bp.id = _blsPtype.ptype_id
        and _blsPtype.profile_id=#{profileId} and _blsPtype.etype_id=#{employeeId} -->
        where
        <include refid="selectPtypeList_Where"/>
        group by primaryId
    </select>
    <!--套餐的skuId为空，会导致concat函数生成null-->

    <select id="selectPtypeList_COUNT" resultType="Long">
        SELECT count(0)
        FROM ( select
        <!--套餐的skuId为空，会导致concat函数生成null-->
        concat(bp.id,ifnull(bps.id,0),ifnull(bpu.id,0)) as primaryId
        from base_ptype bp
        <!--sku-->
        left join base_ptype_sku bps on bps.profile_id = #{profileId}
        and bps.ptype_id = bp.id and bps.stoped = 0 and bps.deleted = 0
        <!--单位-->
        left join base_ptype_unit bpu on bpu.profile_id = #{profileId} and bpu.ptype_id = bp.id
        <!--商品条码-->
        left join base_ptype_fullbarcode bpf on bpf.profile_id = #{profileId}
        and bpf.ptype_id = bp.id and bpf.sku_id = bps.id and bpf.unit_id = bpu.id
        left join base_ptype_xcode bpx on bpx.profile_id = #{profileId}
        and bpx.ptype_id = bp.id and bpx.sku_id = bps.id and bpx.unit_id = bpu.id
        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit-alias-profileid" value="bp.profile_id"/>
            </include>
        </if>
        where
        <include refid="selectPtypeList_Where"/>

        group by primaryId
        ) table_count;
    </select>


    <!--查询套餐明细-->
    <select id="selectComboDetails" resultMap="ptypeResponseMap">
        select
        <!--将套餐明细行id作为主键，之前是ptype，sku，unit的id组合，后面发现套餐内允许商品重复（即这三个id相同的商品重复出现）-->
        distinct(bpcd.id) as primaryId,
        bp.id, bp.profile_id, bp.typeid, bp.partypeid, bp.usercode, bp.fullname, bp.shortname, bp.namepy, bp.classed,
        bp.stoped, bp.deleted, bp.rowindex, bp.barcode, bp.standard, bp.ptype_type, bp.ptype_area, bp.memo,
        bp.create_type,
        bp.cost_mode, bp.pcategory, bp.tax_number, bp.tax_rate, bp.cost_price, bp.brand_id, bp.ktype_limit,
        bp.snenabled, bp.propenabled,
        bp.batchenabled, bp.protect_days, bp.protect_days_unit, bp.protect_days_view, bp.protect_warndays,
        bp.protect_warndays_unit,
        bp.weight, bp.weight_unit,
        bp.retail_default_unit, bp.sale_default_unit, bp.buy_default_unit, bp.stock_default_unit, bp.ptype_length,
        bp.ptype_width,
        bp.ptype_height, bp.length_unit, bp.create_time, bp.update_time, bp.sku_price, bp.propvalues_descart_count,
        bp.fullbarcode_rule_id,
        bp.sub_unit, bp.share_type, bp.invoice_fullname, bp.preparation_type, bp.industry_category, bp.audit_state,
        bp.buy_days,
        bpf.fullbarcode,
        pic.pic_url,
        <!--套餐-->
        bpcd.combo_id,
        0 as comboRow,
        bpcd.gifted,
        <!--单价-->
        IFNULL(bpcd.price,0) as currencyPrice,
        bpcd.total,
        <!--数量-->
        bpcd.qty as count,
        <!--套餐明细行分摊比例-->
        bpcd.scale,
        <!--sku信息-->
        bps.id as sku_id, bps.prop_id1, bps.prop_name1, bps.propvalue_id1, bps.propvalue_name1, bps.prop_id2,
        bps.prop_name2,bps.propvalue_id2, bps.propvalue_name2,bps.prop_id3, bps.prop_name3, bps.propvalue_id3,
        bps.propvalue_name3,bps.prop_id4, bps.prop_name4, bps.propvalue_id4, bps.propvalue_name4, bps.prop_id5,
        bps.prop_name5,bps.propvalue_id5, bps.propvalue_name5, bps.prop_id6, bps.prop_name6, bps.propvalue_id6,
        bps.propvalue_name6, bps.pic_url as sku_pic_url,bps.memo as sku_memo, bps.prop_names, bps.propvalue_names,
        bps.cost_price as sku_cost_price,
        <!--unit信息-->
        bpu.id as unit_id,bpu.unit_code,bpu.unit_name, bpu.unit_rate,bpu.barcode as unit_barcode
        from base_ptype_combo_detail bpcd
        left join base_ptype bp on bp.id = bpcd.ptype_id and bp.profile_id =
        #{profileId}
        left join base_ptype_sku bps on bps.ptype_id = bp.id and bps.id = bpcd.sku_id and bps.profile_id = #{profileId}
        and bps.stoped = 0 and
        bps.deleted = 0
        left join base_ptype_unit bpu on bpu.ptype_id = bp.id and bpu.id = bpcd.unit_id and bpu.profile_id =
        #{profileId}
        left join base_ptype_fullbarcode bpf on bpf.ptype_id = bpcd.ptype_id and bpf.sku_id = bpcd.sku_id and
        bpf.unit_id = bpcd.unit_id and bpf.profile_id = #{profileId} and bpf.defaulted = 1
        left join base_ptype_pic pic on pic.ptype_id = bp.id and pic.rowindex=1 and pic.profile_id =#{profileId}
        where bpcd.profile_id = #{profileId}
        and bp.stoped = 0 and bp.deleted = 0
        and bpcd.stoped = 0 and bpcd.combo_id in
        <foreach collection="comboIds" open="(" close=")" item="comboId" separator=",">
            #{comboId}
        </foreach>
    </select>

    <!--根据序列号精确查询商品-->
    <select id="selectPtypeBySerialNo" resultMap="ptypeResponseMap">
        select
        bp.id as primaryId,
        bp.id, bp.profile_id, bp.typeid, bp.partypeid, bp.usercode, bp.fullname, bp.shortname, bp.namepy, bp.classed,
        bp.stoped, bp.deleted, bp.rowindex, bp.barcode, bp.standard, bp.ptype_type, bp.ptype_area, bp.memo,
        bp.create_type,
        bp.cost_mode, bp.pcategory, bp.tax_number, bp.tax_rate, bp.cost_price, bp.brand_id, bp.ktype_limit,
        bp.snenabled, bp.propenabled,
        bp.batchenabled, bp.protect_days, bp.protect_days_unit,bp.protect_days_view, bp.protect_warndays,
        bp.protect_warndays_unit,
        bp.weight, bp.weight_unit,
        bp.retail_default_unit, bp.sale_default_unit, bp.buy_default_unit, bp.stock_default_unit, bp.ptype_length,
        bp.ptype_width,
        bp.ptype_height, bp.length_unit, bp.create_time, bp.update_time, bp.sku_price, bp.propvalues_descart_count,
        bp.fullbarcode_rule_id,
        bp.sub_unit, bp.share_type, bp.invoice_fullname, bp.preparation_type, bp.industry_category, bp.audit_state,
        bp.buy_days,
        <!--店铺价格本会员价-->
        pbp.sale_otype_vip_price,

        bpf.fullbarcode,
        pic.pic_url,
        1 as count,
        0 as comboRow,
        <!--优先取门店价格本的价格(不为零不为空)，再取零售价-->
        IF(IFNULL(pbp.sale_price,0) <![CDATA[>]]> 0,pbp.sale_price,IFNULL(bpp.retail_price,0)) as currencyPrice,
        <!--sku信息-->
        bps.id as sku_id, bps.prop_id1, bps.prop_name1, bps.propvalue_id1, bps.propvalue_name1, bps.prop_id2,
        bps.prop_name2,bps.propvalue_id2, bps.propvalue_name2,bps.prop_id3, bps.prop_name3, bps.propvalue_id3,
        bps.propvalue_name3,bps.prop_id4, bps.prop_name4, bps.propvalue_id4, bps.propvalue_name4, bps.prop_id5,
        bps.prop_name5,bps.propvalue_id5, bps.propvalue_name5, bps.prop_id6, bps.prop_name6, bps.propvalue_id6,
        bps.propvalue_name6, bps.pic_url as sku_pic_url,bps.memo as sku_memo, bps.prop_names, bps.propvalue_names,
        bps.cost_price as sku_cost_price,
        <!--unit信息-->
        bpu.id as unit_id,bpu.unit_code,bpu.unit_name, bpu.unit_rate,bpu.barcode as unit_barcode,
        <!--成本 costId-->
        ap.id as cost_id,
        <!--序列号-->
        ais.id as serial_id,ais.snno,1 as isInTheStock, #根据序列号查出来的肯定在库
        <!--批次-->
        aib.ktype_id,aib.batchno,aib.produce_date,aib.expire_date,aib.batch_price
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            ,(IFNULL(agd.qty,0) - IFNULL(srqs.qty,0)) as stockQty
        </if>
        from acc_inventory_serialno ais
        <!--sku-->
        left join `base_ptype_sku` bps on bps.id = ais.sku_id and bps.profile_id = #{profileId,jdbcType=BIGINT}
        and bps.ptype_id = ais.ptype_id and bps.stoped = 0 and bps.deleted = 0
        <!--商品表-->
        left join `base_ptype` bp on bp.profile_id = #{profileId,jdbcType=BIGINT} and bp.id = ais.ptype_id
        <!--单位表，只有商品的基本单位才有序列号-->
        left join `base_ptype_unit` bpu on bpu.profile_id = #{profileId,jdbcType=BIGINT}
        and bpu.ptype_id = bp.id and bpu.unit_code = 1
        <!--条码-->
        left join `base_ptype_fullbarcode` bpf on bpf.profile_id = #{profileId,jdbcType=BIGINT}
        and bpf.ptype_id = bp.id and bpf.sku_id = bps.id and bpf.unit_id = bpu.id and bpf.defaulted = 1
        <!--图片，只取一张-->
        left join `base_ptype_pic` pic on pic.profile_id = #{profileId,jdbcType=BIGINT}
        and pic.ptype_id = bp.id and pic.rowindex = 1
        <!--商品价格表-->
        left join `base_ptype_price` bpp on bpp.profile_id = #{profileId,jdbcType=BIGINT}
        and bpp.ptype_id = bp.id and bpp.unit_id = bpu.id
        <!--未开启sku价格管理的情况下，价格表中skuId为0-->
        and bpp.sku_id = if(bp.sku_price = 1,bps.id,0)
        <!--门店价格表-->
        left join `pub_bill_price` pbp on pbp.profile_id = #{profileId,jdbcType=BIGINT}
        and pbp.xtype_id = #{otypeId,jdbcType=BIGINT} and pbp.create_type = 4
        and pbp.ptype_id = bp.id and pbp.unit_id = bpu.id
        <!--套餐和未开启sku价格管理的情况下，价格表中skuId为0-->
        and pbp.sku_id = if(bp.sku_price = 1,bps.id,0)
        <!--批次-->
        left join `acc_inventory_batch` aib on aib.profile_id = #{profileId,jdbcType=BIGINT} and aib.id = ais.batch_id
        <!--全月商品成本表（和批次关联）-->
        left join `acc_periodcost` ap on ap.profile_id =#{profileId,jdbcType=BIGINT}
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            left join acc_goodsstock_detail agd on agd.profile_id = #{profileId}
            and agd.sku_id =bps.id and agd.ktype_id = #{ktypeId}
            left join stock_record_qty_sale srqs on srqs.profile_id = #{profileId}
            and srqs.sku_id = bps.id and srqs.ktype_id = #{ktypeId}
        </if>
        and ap.ptype_id = aib.ptype_id and ap.sku_id = aib.sku_id and ap.batchno = aib.batchno
        and ap.produce_date <![CDATA[<=>]]> aib.produce_date
        and ap.expire_date <![CDATA[<=>]]> aib.expire_date
        and ap.price = aib.batch_price and ap.ktype_id = aib.ktype_id
        <include refid="limit-ptype">
            <property name="limit-alias-id" value="bp.id"/>
            <property name="limit-alias-profileid" value="bp.profile_id"/>
        </include>
        <!--这里可以用这个表来判断当前操作员是否有权限查询这个商品-->
        <!-- left join base_ptype_limit_scope _blsPtype on bp.id = _blsPtype.ptype_id
        and _blsPtype.profile_id=#{profileId} and _blsPtype.etype_id=#{employeeId} -->
        <!--可以通过这边表来判断操作员是否有这个仓库的权限-->
        <!-- INNER JOIN base_limit_scope bls on bls.profile_id=#{profileId} and bls.object_type=2 and
        bls.etype_id=#{employeeId} and sn.ktype_id=bls.object_id -->
        where ais.profile_id = #{profileId,jdbcType=BIGINT}
        and ais.snno = #{serialNo,jdbcType=VARCHAR}
        and ais.ktype_id = #{ktypeId,jdbcType=BIGINT}
        and bp.stoped = 0
        and bp.deleted = 0
        and bp.classed = 0
        <!--排除套餐商品-->
        and bp.pcategory IN(0,1)
        <include refid="limit-where-ptype">
            <property name="limit-alias-id" value="bp.id"/>
            <property name="limit_pcategory" value="bp.pcategory"/>
        </include>
        group by ais.snno
    </select>

    <resultMap id="ptypeResponseMap" type="com.wsgjp.ct.sale.biz.shopsale.model.vo.bill.PtypeResponse">
        <id column="primaryId"/>
        <!-- 商品信息-->
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="profile_id" property="profileId" jdbcType="BIGINT"/>
        <result column="typeid" property="typeid" jdbcType="VARCHAR"/>
        <result column="partypeid" property="partypeid" jdbcType="VARCHAR"/>
        <result column="usercode" property="usercode" jdbcType="VARCHAR"/>
        <result column="fullname" property="fullname" jdbcType="VARCHAR"/>
        <result column="shortname" property="shortname" jdbcType="VARCHAR"/>
        <result column="namepy" property="namepy" jdbcType="VARCHAR"/>
        <result column="classed" property="classed" jdbcType="TINYINT"/>
        <result column="stoped" property="stoped" jdbcType="TINYINT"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
        <result column="rowindex" property="rowindex" jdbcType="BIGINT"/>
        <result column="barcode" property="barcode" jdbcType="VARCHAR"/>
        <result column="standard" property="standard" jdbcType="VARCHAR"/>
        <result column="ptype_type" property="ptypeType" jdbcType="VARCHAR"/>
        <result column="ptype_area" property="ptypeArea" jdbcType="VARCHAR"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="create_type" property="createType" jdbcType="INTEGER"/>
        <result column="cost_mode" property="costMode" jdbcType="TINYINT"/>
        <result column="pcategory" property="pcategory" jdbcType="TINYINT"/>
        <result column="text_number" property="taxNumber" jdbcType="VARCHAR"/>
        <result column="tax_rate" property="taxRate" jdbcType="DECIMAL"/>
        <result column="cost_price" property="costPrice" jdbcType="DECIMAL"/>
        <result column="brand_id" property="brandId" jdbcType="BIGINT"/>
        <result column="ktype_limit" property="ktypeLimit" jdbcType="TINYINT"/>
        <result column="snenabled" property="snenabled" jdbcType="INTEGER"/>
        <result column="propenabled" property="propenabled" jdbcType="TINYINT"/>
        <result column="batchenabled" property="batchenabled" jdbcType="TINYINT"/>
        <result column="protect_days" property="protectDays" jdbcType="INTEGER"/>
        <result column="protect_days_unit" property="protectDaysUnit" jdbcType="TINYINT"/>
        <result column="protect_days_view" property="protectDaysView" jdbcType="INTEGER"/>
        <result column="protect_warndays" property="protectWarndays" jdbcType="INTEGER"/>
        <result column="protect_warndays_unit" property="protectWarndaysUnit" jdbcType="TINYINT"/>
        <result column="weight" property="weight" jdbcType="DECIMAL"/>
        <result column="weight_unit" property="weightUnit" jdbcType="TINYINT"/>
        <result column="retail_default_unit" property="retailDefaultUnit" jdbcType="BIGINT"/>
        <result column="sale_default_unit" property="saleDefaultUnit" jdbcType="BIGINT"/>
        <result column="buy_default_unit" property="buyDefaultUnit" jdbcType="BIGINT"/>
        <result column="stock_default_unit" property="stockDefaultUnit" jdbcType="BIGINT"/>
        <result column="ptype_length" property="ptypeLength" jdbcType="DECIMAL"/>
        <result column="ptype_width" property="ptypeWidth" jdbcType="DECIMAL"/>
        <result column="ptype_height" property="ptypeHeight" jdbcType="DECIMAL"/>
        <result column="length_unit" property="lengthUnit" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="sku_price" property="skuPrice" jdbcType="TINYINT"/>
        <result column="propvalues_descart_count" property="propvaluesDescartCount" jdbcType="INTEGER"/>
        <result column="fullbarcode_rule_id" property="fullbarcodeRuleId" jdbcType="BIGINT"/>
        <!--条码-->
        <result column="fullbarcode" property="fullbarcode" jdbcType="VARCHAR"/>
        <!--图片-->
        <result column="pic_url" property="picUrl" jdbcType="VARCHAR"/>
        <!--价格-->
        <result column="currencyPrice" property="currencyPrice"/>
        <result column="total" property="total"/>
        <result column="sale_otype_vip_price" property="saleOtypeVipPrice"/>
        <!--套餐明细行分摊比例-->
        <result column="scale" property="scale"/>
        <!--数量-->
        <result column="count" property="count"/>
        <result column="qty" property="qty"/>

        <!--套餐-->
        <result column="combo_id" property="comboId"/>
        <result column="comboRow" property="comboRow"/>
        <result column="gifted" property="gift"/>
        <!--成本id-->
        <result column="cost_id" property="costId" jdbcType="BIGINT"/>
        <!--批次-->
        <result column="batchno" property="batchNo"/>
        <result column="produce_date" property="produceDate"/>
        <result column="expire_date" property="expireDate"/>
        <result column="batch_price" property="batchPrice"/>
        <!--库存-->
        <result column="stockQty" property="stockQty"/>
        <result column="xcode" property="xcode"/>


        <!--sku-->
        <association property="sku" javaType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PtypeSku">
            <result column="sku_id" property="id" jdbcType="BIGINT"/>
            <result column="prop_id1" property="propId1" jdbcType="BIGINT"/>
            <result column="prop_name1" property="propName1" jdbcType="VARCHAR"/>
            <result column="propvalue_id1" property="propvalueId1" jdbcType="BIGINT"/>
            <result column="propvalue_name1" property="propvalueName1" jdbcType="VARCHAR"/>
            <result column="prop_id2" property="propId2" jdbcType="BIGINT"/>
            <result column="prop_name2" property="propName2" jdbcType="VARCHAR"/>
            <result column="propvalue_id2" property="propvalueId2" jdbcType="BIGINT"/>
            <result column="propvalue_name2" property="propvalueName2" jdbcType="VARCHAR"/>
            <result column="prop_id3" property="propId3" jdbcType="BIGINT"/>
            <result column="prop_name3" property="propName3" jdbcType="VARCHAR"/>
            <result column="propvalue_id3" property="propvalueId3" jdbcType="BIGINT"/>
            <result column="propvalue_name3" property="propvalueName3" jdbcType="VARCHAR"/>
            <result column="prop_id4" property="propId4" jdbcType="BIGINT"/>
            <result column="prop_name4" property="propName4" jdbcType="VARCHAR"/>
            <result column="propvalue_id4" property="propvalueId4" jdbcType="BIGINT"/>
            <result column="propvalue_name4" property="propvalueName4" jdbcType="VARCHAR"/>
            <result column="prop_id5" property="propId5" jdbcType="BIGINT"/>
            <result column="prop_name5" property="propName5" jdbcType="VARCHAR"/>
            <result column="propvalue_id5" property="propvalueId5" jdbcType="BIGINT"/>
            <result column="propvalue_name5" property="propvalueName5" jdbcType="VARCHAR"/>
            <result column="prop_id6" property="propId6" jdbcType="BIGINT"/>
            <result column="prop_name6" property="propName6" jdbcType="VARCHAR"/>
            <result column="propvalue_id6" property="propvalueId6" jdbcType="BIGINT"/>
            <result column="propvalue_name6" property="propvalueName6" jdbcType="VARCHAR"/>
            <result column="sku_pic_url" property="picUrl" jdbcType="VARCHAR"/>
            <result column="sku_memo" property="memo" jdbcType="VARCHAR"/>
            <result column="prop_names" property="propNames" jdbcType="VARCHAR"/>
            <result column="propvalue_names" property="propvalueNames" jdbcType="VARCHAR"/>
            <result column="costPrice" property="sku_cost_price"/>
            <result column="rowindex1" property="rowindex1" jdbcType="INTEGER"/>
            <result column="rowindex2" property="rowindex2" jdbcType="INTEGER"/>
            <result column="rowindex3" property="rowindex3" jdbcType="INTEGER"/>
            <result column="rowindex4" property="rowindex4" jdbcType="INTEGER"/>
            <result column="rowindex5" property="rowindex5" jdbcType="INTEGER"/>
            <result column="rowindex6" property="rowindex6" jdbcType="INTEGER"/>
        </association>
        <!--unit-->
        <association property="unit" javaType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PtypeUnitPrice">
            <result column="unit_id" property="id" jdbcType="BIGINT"/>
            <result column="unit_code" property="unitCode" jdbcType="INTEGER"/>
            <result column="unit_name" property="unitName" jdbcType="VARCHAR"/>
            <result column="unit_rate" property="unitRate" jdbcType="DECIMAL"/>
            <result column="unit_barcode" property="barcode" jdbcType="VARCHAR"/>
            <result column="ptype_weight" property="ptypeWeight"/>
        </association>
        <!--序列号-->
        <collection property="serialNoList" ofType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.SerialNoDTO">
            <id column="serial_id" property="id"/>
            <result column="snno" property="snno"/>
            <result column="batchno" property="batchNo"/>
            <result column="produce_date" property="produceDate"/>
            <result column="expire_date" property="expireDate"/>
            <result column="batch_price" property="batchPrice"/>
            <result column="ktype_id" property="ktypeId"/>
            <result column="isInTheStock" property="isInTheStock"/>
        </collection>
    </resultMap>

    <sql id="limit-ptype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ptype')">
            left join base_ptype_limit_scope _blsPtype on ${limit-alias-id} = _blsPtype.ptype_id and
            ${limit-alias-profileid} =
            _blsPtype.profile_id and _blsPtype.etype_id =
            ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>

    <sql id="limit-where-ptype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ptype')">
            and (_blsPtype.ptype_id is not null or ${limit-alias-id} =0 or ${limit_pcategory} = 2)
        </if>
    </sql>

    <select id="getPtypeAllRootType" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PtypeType">
        select typeid, fullname, partypeid
        from base_ptype
        where profile_id = #{profileId}
          and stoped = 0
          and deleted = 0
          and classed = 1
          and CHAR_LENGTH(typeid) <![CDATA[<=]]> 10
        ORDER BY rowindex desc, id ASC
    </select>

    <!--仅查询商品和套餐，不带sku和unit-->
    <select id="selectOnlyPtypeList" resultMap="ptypeResponseMap">
        select
        bp.id as primaryId,
        <!--商品表-->
        bp.id, bp.profile_id, bp.typeid, bp.partypeid, bp.usercode, bp.fullname, bp.shortname, bp.namepy, bp.classed,
        bp.stoped, bp.deleted, bp.rowindex, bp.barcode, bp.standard, bp.ptype_type, bp.ptype_area, bp.memo,
        bp.create_type,
        bp.cost_mode, bp.pcategory, bp.tax_number, bp.tax_rate, bp.cost_price, bp.brand_id, bp.ktype_limit,
        bp.snenabled, bp.propenabled,
        bp.batchenabled, bp.protect_days, bp.protect_days_unit, bp.protect_days_view, bp.protect_warndays,
        bp.protect_warndays_unit,
        bp.weight, bp.weight_unit,
        bp.retail_default_unit, bp.sale_default_unit, bp.buy_default_unit, bp.stock_default_unit, bp.ptype_length,
        bp.ptype_width,
        bp.ptype_height, bp.length_unit, bp.create_time, bp.update_time, bp.sku_price, bp.propvalues_descart_count,
        bp.fullbarcode_rule_id,
        bp.sub_unit, bp.share_type, bp.invoice_fullname, bp.preparation_type, bp.industry_category, bp.audit_state,
        bp.buy_days,
        <!--图片-->
        pic.pic_url,
        1 as count,
        <!--套餐-->
        if(bp.pcategory = 2,bp.id,null) as combo_id,IF(bp.pcategory = 2,1,0) as comboRow
        from base_ptype bp
        <!--图片，只取一张-->
        left join base_ptype_pic pic on pic.profile_id = #{profileId} and pic.ptype_id = bp.id and pic.rowindex = 1
        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit-alias-profileid" value="bp.profile_id"/>
            </include>
        </if>
        where bp.profile_id = #{profileId}
        <!--仅查询启用商品-->
        and bp.stoped = 0
        and bp.deleted = 0
        and bp.classed = 0
        <if test="typeId!=null and typeId!=''">
            and bp.typeid like concat(#{typeId}, '%')
        </if>
        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-where-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit_pcategory" value="bp.pcategory"/>
            </include>
        </if>
        <if test="filterValue!=null and filterValue!=''">
            <trim prefix="and (" suffixOverrides="or" suffix=")">
                <!--商品名称-->
                bp.fullname like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                <!--商品编码-->
                bp.usercode like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                <!--商品码，主要是用于套餐时，套餐的fullbarcode取得就是商品表的barcode,对于普通商品，存的是商品码-->
                bp.barcode like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                <!--商品拼音码-->
                bp.namepy like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
            </trim>
        </if>
    </select>

    <select id="selectOnlyPtypeList_COUNT" resultType="Long">
        SELECT count(0)
        FROM (select
        bp.id as primaryId
        from base_ptype bp
        <!--        &lt;!&ndash;图片，只取一张&ndash;&gt;-->
        <!--        left join base_ptype_pic pic on pic.profile_id = #{profileId} and pic.ptype_id = bp.id and pic.rowindex = 1-->
        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit-alias-profileid" value="bp.profile_id"/>
            </include>
        </if>
        where bp.profile_id = #{profileId}
        <!--仅查询启用商品-->
        and bp.stoped = 0
        and bp.deleted = 0
        and bp.classed = 0
        <if test="typeId!=null and typeId!=''">
            and bp.typeid like concat(#{typeId}, '%')
        </if>
        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-where-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit_pcategory" value="bp.pcategory"/>
            </include>
        </if>
        <if test="filterValue!=null and filterValue!=''">
            <trim prefix="and (" suffixOverrides="or" suffix=")">
                <!--商品名称-->
                bp.fullname like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                <!--商品编码-->
                bp.usercode like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                <!--商品码，主要是用于套餐时，套餐的fullbarcode取得就是商品表的barcode,对于普通商品，存的是商品码-->
                bp.barcode like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
                <!--商品拼音码-->
                bp.namepy like concat('%',#{filterValue,jdbcType=VARCHAR}, '%') or
            </trim>
        </if>
        ) table_count
    </select>


    <select id="selectPtypeByIds" resultMap="ptypeResponseMap">
        select
        <!--套餐的skuId为空，会导致concat函数生成null-->
        concat(bp.id,ifnull(bps.id,0),ifnull(bpu.id,0)) as primaryId,
        bp.id,
        <if test="hasPtypeInfo!=null and hasPtypeInfo==true">
            bp.profile_id, bp.typeid, bp.partypeid, bp.usercode, bp.fullname, bp.shortname, bp.namepy, bp.classed,
            bp.stoped, bp.deleted, bp.rowindex, bp.barcode, bp.standard, bp.ptype_type, bp.ptype_area, bp.memo,
            bp.create_type,
            bp.cost_mode, bp.pcategory, bp.tax_number, bp.tax_rate, bp.cost_price, bp.brand_id, bp.ktype_limit,
            bp.snenabled, bp.propenabled,
            bp.batchenabled, bp.protect_days, bp.protect_days_unit, bp.protect_days_view, bp.protect_warndays,
            bp.protect_warndays_unit,
            bp.weight, bp.weight_unit,
            bp.retail_default_unit, bp.sale_default_unit, bp.buy_default_unit, bp.stock_default_unit, bp.ptype_length,
            bp.ptype_width,
            bp.ptype_height, bp.length_unit, bp.create_time, bp.update_time, bp.sku_price, bp.propvalues_descart_count,
            bp.fullbarcode_rule_id,
            bp.sub_unit, bp.share_type, bp.invoice_fullname, bp.preparation_type, bp.industry_category, bp.audit_state,
            bp.buy_days,
            <!--图片-->
            pic.pic_url,
            <!--套餐-->
            if(bp.pcategory = 2,bp.id,null) as combo_id,IF(bp.pcategory = 2,1,0) as comboRow,
        </if>

        <!--套餐并没有关联base_ptype_fullbarcode这张表，套餐条码直接取商品表的barcode字段-->
        if(bp.pcategory != 2,bpf.fullbarcode,bp.barcode) as fullbarcode,
        1 as count,
        <!--优先取门店价格本的价格(不为零不为空)，再取零售价-->
        IF(IFNULL(pbp.sale_price,0) <![CDATA[>]]> 0,pbp.sale_price,IFNULL(bpp.retail_price,0)) as currencyPrice,
        <!--店铺价格本会员价-->
        pbp.sale_otype_vip_price,
        <!--sku信息-->
        bps.id as sku_id, bps.prop_id1, bps.prop_name1, bps.propvalue_id1, bps.propvalue_name1, bps.prop_id2,
        bps.prop_name2,bps.propvalue_id2, bps.propvalue_name2,bps.prop_id3, bps.prop_name3, bps.propvalue_id3,
        bps.propvalue_name3,bps.prop_id4, bps.prop_name4, bps.propvalue_id4, bps.propvalue_name4, bps.prop_id5,
        bps.prop_name5,bps.propvalue_id5, bps.propvalue_name5, bps.prop_id6, bps.prop_name6, bps.propvalue_id6,
        bps.propvalue_name6, bps.pic_url as sku_pic_url,bps.memo as sku_memo, bps.prop_names, bps.propvalue_names,
        bps.cost_price as sku_cost_price,
        <!--属性值排序信息-->
        bpv1.rowindex as rowindex1, bpv2.rowindex as rowindex2, bpv3.rowindex as rowindex3,
        bpv4.rowindex as rowindex4, bpv5.rowindex as rowindex5, bpv6.rowindex as rowindex6,
        <!--unit信息-->
        bpu.id as unit_id,bpu.unit_code,bpu.unit_name, bpu.unit_rate,bpu.barcode as unit_barcode
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            ,(IFNULL(agd.qty,0) - IFNULL(srqs.qty,0)) as stockQty
            ,IFNULL(agd.qty,0) as qty
        </if>
        from base_ptype bp
        <!--sku-->
        left join base_ptype_sku bps on bps.profile_id = #{profileId}
        and bps.ptype_id = bp.id and bps.stoped = 0 and bps.deleted = 0
        <!--单位-->
        left join base_ptype_unit bpu on bpu.profile_id = #{profileId} and bpu.ptype_id = bp.id
        <!--商品条码-->
        left join base_ptype_fullbarcode bpf on bpf.profile_id = #{profileId}
        and bpf.ptype_id = bp.id and bpf.sku_id = bps.id and bpf.unit_id = bpu.id
        <if test="hasPtypeInfo==true">
            <!--图片，只取一张-->
            left join base_ptype_pic pic on pic.profile_id = #{profileId} and pic.ptype_id = bp.id and pic.rowindex = 1
        </if>
        <!--商品价格表-->
        left join base_ptype_price bpp on bpp.profile_id = #{profileId}
        and bpp.ptype_id = bp.id and bpp.unit_id = bpu.id
        <!--套餐和未开启sku价格管理的情况下，价格表中skuId为0-->
        and bpp.sku_id = if(bp.sku_price = 1,bps.id,0)
        <!--门店价格表-->
        left join pub_bill_price pbp on pbp.profile_id = #{profileId} and pbp.xtype_id = #{otypeId}
        and pbp.create_type = 4 and pbp.ptype_id = bp.id and pbp.unit_id = bpu.id
        <!--套餐和未开启sku价格管理的情况下，价格表中skuId为0-->
        and pbp.sku_id = if(bp.sku_price = 1,bps.id,0)
        <!--属性值排序表关联-->
        left join base_propvalue bpv1 on bpv1.profile_id = #{profileId}
        and bpv1.id = bps.propvalue_id1 and bpv1.prop_id = bps.prop_id1 and bps.propvalue_id1 > 0
        left join base_propvalue bpv2 on bpv2.profile_id = #{profileId}
        and bpv2.id = bps.propvalue_id2 and bpv2.prop_id = bps.prop_id2 and bps.propvalue_id2 > 0
        left join base_propvalue bpv3 on bpv3.profile_id = #{profileId}
        and bpv3.id = bps.propvalue_id3 and bpv3.prop_id = bps.prop_id3 and bps.propvalue_id3 > 0
        left join base_propvalue bpv4 on bpv4.profile_id = #{profileId}
        and bpv4.id = bps.propvalue_id4 and bpv4.prop_id = bps.prop_id4 and bps.propvalue_id4 > 0
        left join base_propvalue bpv5 on bpv5.profile_id = #{profileId}
        and bpv5.id = bps.propvalue_id5 and bpv5.prop_id = bps.prop_id5 and bps.propvalue_id5 > 0
        left join base_propvalue bpv6 on bpv6.profile_id = #{profileId}
        and bpv6.id = bps.propvalue_id6 and bpv6.prop_id = bps.prop_id6 and bps.propvalue_id6 > 0
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            left join acc_goodsstock_detail agd on agd.profile_id = #{profileId}
            and agd.sku_id =bps.id and agd.ktype_id = #{ktypeId}
            left join stock_record_qty_sale srqs on srqs.profile_id = #{profileId}
            and srqs.sku_id = bps.id and srqs.ktype_id = #{ktypeId}
        </if>

        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit-alias-profileid" value="bp.profile_id"/>
            </include>
        </if>

        <!--这里可以用这个表来判断当前操作员是否有权限查询这个商品-->
        <!--left join base_ptype_limit_scope _blsPtype on bp.id = _blsPtype.ptype_id
        and _blsPtype.profile_id=#{profileId} and _blsPtype.etype_id=#{employeeId} -->
        where bp.profile_id = #{profileId}
        <!--仅查询启用商品-->
        and bp.stoped = 0
        and bp.deleted = 0
        and bp.classed = 0

        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-where-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit_pcategory" value="bp.pcategory"/>
            </include>
        </if>
        <!--仅查询启用sku和套餐,套餐sku为空-->
        and (bp.pcategory = 2 or bps.stoped = 0)
        <if test="ptypeIds!=null and ptypeIds.size()>0 ">
            and bp.id in
            <foreach collection="ptypeIds" close=")" open="(" separator="," item="ptypeId">
                #{ptypeId}
            </foreach>
        </if>
        <if test="skuIds!=null and skuIds.size()>0 ">
            and bps.id in
            <foreach collection="skuIds" close=")" open="(" separator="," item="skuId">
                #{skuId}
            </foreach>
        </if>
        <if test="unitIds!=null and unitIds.size()>0 ">
            and bpu.id in
            <foreach collection="unitIds" close=")" open="(" separator="," item="unitId">
                #{unitId}
            </foreach>
        </if>
        group by primaryId
    </select>

    <select id="selectPtypeUnitWithBarcodeList"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PtypeUnitPrice">
        SELECT bpu.id, bpu.ptype_id, bpu.unit_code, bpu.unit_name, bpu.unit_rate, bpf.fullbarcode as barcode
        FROM base_ptype_unit bpu
                 LEFT JOIN base_ptype_fullbarcode bpf
                           on bpf.profile_id = #{profileId} and bpf.sku_id = #{skuId} and bpf.unit_id = bpu.id
        WHERE bpu.profile_id = #{profileId}
          AND bpu.ptype_id = #{ptypeId}
    </select>

    <select id="selectPtypeBarcodeBySkuUnit" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypeUnitSkuPo">
        select
        bpf.fullbarcode as skuFullBarcode,
        (select bpx2.xcode
        from base_ptype_xcode bpx2
        where bpx2.profile_id = #{profileId}
        and bpx2.ptype_id = bpf.ptype_id
        and bpx2.sku_id = bpf.sku_id
        and bpx2.unit_id = bpf.unit_id
        order by bpx2.defaulted desc
        limit 1) as skuXcode,
        bpf.ptype_id,
        bpf.unit_id,
        bpf.sku_id
        from base_ptype_fullbarcode bpf
        where bpf.profile_id = #{profileId}
        and exists (
        select 1
        from base_ptype_fullbarcode bpf2
        where bpf2.profile_id = #{profileId}
        and bpf2.ptype_id = bpf.ptype_id
        and bpf2.sku_id = bpf.sku_id
        and bpf2.unit_id = bpf.unit_id
        order by bpf2.defaulted desc
        limit 1
        )
        and bpf.ptype_id in
        <foreach collection="ptypeUnitSkuPo" close=")" open="(" separator="," item="ptypeUnitSku">
            #{ptypeUnitSku.ptype.id}
        </foreach>
        and bpf.sku_id in
        <foreach collection="ptypeUnitSkuPo" close=")" open="(" separator="," item="ptypeUnitSku">
            #{ptypeUnitSku.sku.id}
        </foreach>
        and bpf.unit_id in
        <foreach collection="ptypeUnitSkuPo" close=")" open="(" separator="," item="ptypeUnitSku">
            #{ptypeUnitSku.unit.id}
        </foreach>
        group by bpf.ptype_id, bpf.sku_id, bpf.unit_id
    </select>


    <select id="getPubBillPrice" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PtypePrice">
        select
        id,
        profile_id,
        ptype_id,
        sku_id,
        unit_id,
        sale_price
        from
        pub_bill_price
        where
        profile_id = #{profileId}
        and xtype_id = #{otypeId}
        and create_type = 4
        and (ptype_id,sku_id,unit_id) in
        <foreach collection="list" item="ptype" separator="," open="(" close=")">
            <if test="ptype.skuPrice == 0">
                (#{ptype.ptypeId},0,#{ptype.unitId})
            </if>
            <if test="ptype.skuPrice == 1">
                (#{ptype.ptypeId},#{ptype.skuId},#{ptype.unitId})
            </if>
        </foreach>
    </select>

    <update id="updateOtypePrice">
        UPDATE pub_bill_price
        SET sale_price = #{price}
        WHERE profile_id = #{profileId}
          AND xtype_id = #{otypeId}
          AND create_type = 4
          AND ptype_id = #{ptypeId}
          AND unit_id = #{unitId}
          AND sku_id = #{skuId}
    </update>

    <!--    <select id="getClassList" resultMap="BasicInfoClassResultMap">-->
    <!--        <if test="cateNameList != null and cateNameList.size() > 0">-->
    <!--            <foreach collection="cateNameList" item="item" index="index" open="" close="" separator=" union ">-->
    <!--                select *-->
    <!--                FROM base_ptype-->
    <!--                WHERE profile_id = #{profileId}-->
    <!--                AND deleted = 0-->
    <!--                AND classed = 1-->
    <!--                and pcategory != 2-->
    <!--                and fullname like concat(#{item}, '%')-->
    <!--                ORDER BY rowindex desc, id ASC limit 1;-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--    </select>-->

    <select id="getClassByName" resultMap="BasicInfoClassResultMap">
        select *
        FROM base_ptype
        WHERE profile_id = #{profileId}
          AND deleted = 0
          AND classed = 1
          and pcategory != 2
          and fullname like concat(#{cateName}, '%')
        ORDER BY rowindex
                desc, id ASC
        limit 1;
    </select>

    <resultMap type="com.wsgjp.ct.baseinfo.core.dal.po.BasicInfoClassPo" id="BasicInfoClassResultMap">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="profile_id" jdbcType="BIGINT" property="profileId"/>
        <result column="fullname" jdbcType="VARCHAR" property="fullname"/>
        <result column="typeid" jdbcType="VARCHAR" property="typeid"/>
        <result column="partypeid" jdbcType="VARCHAR" property="partypeid"/>
        <result column="rowindex" jdbcType="BIGINT" property="rowindex"/>
    </resultMap>

    <!-- 根据过滤条件查询商品基本信息 -->
    <select id="filterPtypeInfo" resultMap="ptypeResponseMap">
        select
        bp.id as id,
        bps.id as sku_id,
        bpu.id as unit_id
        from base_ptype bp
        <!-- sku -->
        left join base_ptype_sku bps on bps.profile_id = #{profileId}
        and bps.ptype_id = bp.id and bps.stoped = 0 and bps.deleted = 0
        <!-- 单位 -->
        left join base_ptype_unit bpu on bpu.profile_id = #{profileId} and bpu.ptype_id = bp.id
        <!-- 商品条码 -->
        left join base_ptype_fullbarcode bpf on bpf.profile_id = #{profileId}
        and bpf.ptype_id = bp.id and bpf.sku_id = bps.id and bpf.unit_id = bpu.id
        <!-- 商品编码 -->
        left join base_ptype_xcode bpx on bpx.profile_id = #{profileId}
        and bpx.ptype_id = bp.id and bpx.sku_id = bps.id and bpx.unit_id = bpu.id
        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit-alias-profileid" value="bp.profile_id"/>
            </include>
        </if>
        where bp.profile_id = #{profileId}
        <!--仅查询启用商品-->
        and bp.stoped = 0
        and bp.deleted = 0
        and bp.classed = 0
        <if test="typeId!=null and typeId!=''">
            and bp.typeid like concat(#{typeId}, '%')
        </if>
        <!--仅查询启用sku和套餐,套餐sku为空-->
        and (bp.pcategory = 2 or bps.stoped = 0)

        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-where-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit_pcategory" value="bp.pcategory"/>
            </include>
        </if>
        and (
        <if test="filterValue!=null and filterValue!=''">
            <!--扫码搜索时精确匹配-->
            (bp.barcode = #{filterValue,jdbcType=VARCHAR} and bp.pcategory = 2)
            or bpf.fullbarcode = #{filterValue,jdbcType=VARCHAR}
            <!--条码称时，需要额外匹配条码称拆出来的商品条码-->
            <if test="skuBarCode!=null and skuBarCode!=''">
                or bpx.xcode = #{skuBarCode,jdbcType=VARCHAR}
            </if>
        </if>
        )
        group by bp.id
    </select>

    <select id="selectPtypeByBarcode" resultMap="ptypeResponseMap">
        select temp.*,
        <!--套餐并没有关联base_ptype_fullbarcode这张表，套餐条码直接取商品表的barcode字段-->
        if(temp.pcategory != 2, temp.fullbarcode, temp.barcode) as fullbarcode,
        pic.pic_url,
        1 as count,
        <!--优先取门店价格本的价格(不为零不为空)，再取零售价-->
        IF(IFNULL(pbp.sale_price, 0) <![CDATA[>]]> 0, pbp.sale_price, IFNULL(bpp.retail_price, 0)) as currencyPrice,
        <!--店铺价格本会员价-->
        pbp.sale_otype_vip_price,

        <!--unit信息-->
        bpu.id as unit_id, bpu.unit_code, bpu.unit_name, bpu.unit_rate, bpu.barcode as unit_barcode, bpu.ptype_weight
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            ,(IFNULL(agd.qty, 0) - IFNULL(srqs.qty, 0)) as stockQty, IFNULL(agd.qty, 0) as qty
        </if>
        from (
        SELECT
        <!--套餐的skuId为空，会导致concat函数生成null-->
        concat(bp.id, ifnull(if(bpf.id IS NOT NULL, bpf.sku_id, bpx.sku_id), 0), ifnull(if(bpf.id IS NOT
        NULL, bpf.unit_id, bpx.unit_id), 0)) as primaryId,
        bp.id, bp.profile_id, bp.typeid, bp.partypeid, bp.usercode, bp.fullname, bp.shortname, bp.namepy, bp.classed,
        bp.stoped, bp.deleted, bp.rowindex, bp.barcode, bp.standard, bp.ptype_type, bp.ptype_area, bp.memo,
        bp.create_type,
        bp.cost_mode, bp.pcategory, bp.tax_number, bp.tax_rate, bp.cost_price, bp.brand_id, bp.ktype_limit,
        bp.snenabled, bp.propenabled,
        bp.batchenabled, bp.protect_days, bp.protect_days_unit, bp.protect_days_view, bp.protect_warndays,
        bp.protect_warndays_unit,
        bp.weight, bp.weight_unit,
        bp.retail_default_unit, bp.sale_default_unit, bp.buy_default_unit, bp.stock_default_unit, bp.ptype_length,
        bp.ptype_width,
        bp.ptype_height, bp.length_unit, bp.create_time, bp.update_time, bp.sku_price, bp.propvalues_descart_count,
        bp.fullbarcode_rule_id,
        bp.sub_unit, bp.share_type, bp.invoice_fullname, bp.preparation_type, bp.industry_category, bp.audit_state,
        bp.buy_days,
        <!--sku信息-->
        bps.id as sku_id, bps.prop_id1, bps.prop_name1, bps.propvalue_id1, bps.propvalue_name1, bps.prop_id2,
        bps.prop_name2,bps.propvalue_id2, bps.propvalue_name2,bps.prop_id3, bps.prop_name3, bps.propvalue_id3,
        bps.propvalue_name3,bps.prop_id4, bps.prop_name4, bps.propvalue_id4, bps.propvalue_name4, bps.prop_id5,
        bps.prop_name5,bps.propvalue_id5, bps.propvalue_name5, bps.prop_id6, bps.prop_name6, bps.propvalue_id6,
        bps.propvalue_name6, bps.pic_url as sku_pic_url,bps.memo as sku_memo, bps.prop_names, bps.propvalue_names,
        bps.cost_price as sku_cost_price, bpx.xcode,
        <!--套餐并没有关联base_ptype_fullbarcode这张表，套餐条码直接取商品表的barcode字段-->
        if(bp.pcategory != 2, bpf.fullbarcode, bp.barcode) AS fullbarcode,
        if(bpf.id IS NOT NULL, bpf.sku_id, bpx.sku_id) AS final_sku_id,
        if(bpf.id IS NOT NULL, bpf.unit_id, bpx.unit_id) AS final_unit_id,
        1 AS count,
        <!--套餐-->
        if(bp.pcategory = 2, bp.id, null) AS combo_id, IF(bp.pcategory = 2, 1, 0) AS comboRow

        FROM base_ptype bp
        <!-- sku -->
        left join base_ptype_sku bps on bps.profile_id = #{profileId}
        and bps.ptype_id = bp.id and bps.deleted = 0
        LEFT JOIN base_ptype_fullbarcode bpf ON bpf.profile_id = #{profileId}
        AND bpf.ptype_id = bp.id
        LEFT JOIN base_ptype_xcode bpx ON bpx.profile_id = #{profileId}
        AND bpx.ptype_id = bp.id

        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit-alias-profileid" value="bp.profile_id"/>
            </include>
        </if>
        where
        <include refid="selectPtypeList_Where_New"/>

        <if test="filterValue!=null and filterValue!=''">
            <trim prefix="and (" suffixOverrides="or" suffix=")">
                <!--扫码搜索时精确匹配-->
                (bp.barcode = #{filterValue,jdbcType=VARCHAR} and bp.pcategory = 2)
                or bpf.fullbarcode = #{filterValue,jdbcType=VARCHAR}
                <!--条码称时，需要额外匹配条码称拆出来的商品条码-->
                <if test="skuBarCode!=null and skuBarCode!=''">
                    or bpx.xcode = #{skuBarCode,jdbcType=VARCHAR}
                </if>
            </trim>
        </if>
        group by primaryId
        ) temp
        left join base_ptype_unit bpu on bpu.profile_id = #{profileId} and bpu.ptype_id = temp.id
        and bpu.id = temp.final_unit_id
        <!--图片，只取一张-->
        left join base_ptype_pic pic on pic.profile_id = #{profileId} and pic.ptype_id = temp.id and pic.rowindex = 1
        <!--商品价格表-->
        left join base_ptype_price bpp on bpp.profile_id = #{profileId}
        and bpp.ptype_id = temp.id and bpp.unit_id = bpu.id
        <!--套餐和未开启sku价格管理的情况下，价格表中skuId为0-->
        and bpp.sku_id = if(temp.sku_price = 1, temp.final_sku_id, 0)
        <!--门店价格表-->
        left join pub_bill_price pbp on pbp.profile_id = #{profileId} and pbp.xtype_id = #{otypeId}
        and pbp.create_type = 4 and pbp.ptype_id = temp.id and pbp.unit_id = bpu.id
        <!--套餐和未开启sku价格管理的情况下，价格表中skuId为0-->
        and pbp.sku_id = if(temp.sku_price = 1, temp.final_sku_id, 0)
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            left join acc_goodsstock_detail agd on agd.profile_id = #{profileId}
            and agd.sku_id = temp.final_sku_id and agd.ktype_id = #{ktypeId}
            left join stock_record_qty_sale srqs on srqs.profile_id = #{profileId}
            and srqs.sku_id = temp.final_sku_id and srqs.ktype_id = #{ktypeId}
        </if>
    </select>

    <sql id="selectPtypeList_Where_New">
        bp.profile_id = #{profileId}
        <!--仅查询启用商品-->
        and bp.stoped = 0
        and bp.deleted = 0
        and bp.classed = 0
        <!--库存查询时排除套餐-->
        <if test="stockSelect!=null and stockSelect==true">
            and bp.pcategory != 2
        </if>
        <if test="ptypeLimit!=null and ptypeLimit==true">
            <include refid="limit-where-ptype">
                <property name="limit-alias-id" value="bp.id"/>
                <property name="limit_pcategory" value="bp.pcategory"/>
            </include>
        </if>
        <if test="typeId!=null and typeId!=''">
            and bp.typeid like concat(#{typeId}, '%')
        </if>
        <!--仅查询启用sku和套餐,套餐sku为空-->
        and (bp.pcategory = 2 or bps.stoped = 0)
    </sql>

    <!-- 根据商品ID列表查询商品标签ID -->
    <select id="getPtypeLabelIds" resultType="java.util.HashMap">
        SELECT
            cdlp.resource_id as ptypeId,
            GROUP_CONCAT(cdlp.labelfield_value_id) as labelIds
        FROM cf_data_label_ptype cdlp
        WHERE cdlp.profile_id = #{profileId}
        <if test="ptypeIds != null and ptypeIds.size() > 0">
            AND cdlp.resource_id IN
            <foreach collection="ptypeIds" item="ptypeId" open="(" separator="," close=")">
                #{ptypeId}
            </foreach>
        </if>
        GROUP BY cdlp.resource_id
    </select>

</mapper>
