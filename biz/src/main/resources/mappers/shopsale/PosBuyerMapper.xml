<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.PosBuyerMapper">

    <select id="getBuyer" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.base.PosBuyer">
        select buyer_id, customer_receiver_phone, di, pi from pl_buyer
        where profile_id = #{profileId} and buyer_id in
        <foreach collection="buyerIds" item="buyerId" open="(" close=")" separator=",">
            #{buyerId}
        </foreach>
    </select>
</mapper>