<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.StoreMapper">
    <select id="getStoreList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreInfo">
        SELECT bo.id, bo.profile_id, bo.usercode, bo.fullname, bo.ocategory, bo.store_type, bo.business_type,
        bo.ktype_id,
        bo.btype_id, bo.check_account_type, bo.currency_id, bo.atype_id, bo.memo, bo.deleted, bo.stoped,
        bo.deliver_duration,
        bo.order_index, bo.create_time, bo.update_time, bo.deliver_id, bo.typeid, bo.partypeid, bo.classed, bo.rowindex,
        bo.independent_check ,k.fullname as ktype_name ,b.fullname as btype_name,s.otype_id as otypeId,be.fullname
        as
        shop_owner,s.open_time ,s.close_time ,s.owner_id,
        bd.cellphone ,bd.province ,bd.city ,bd.district ,bd.addr_lat ,bd.addr_lng
        ,bd.address,concat(ifnull(bd.province,''),ifnull(bd.city ,''),ifnull(bd.district,''),ifnull(bd.address,''))as
        full_address,s.allow_pos_login
        FROM base_otype bo
        LEFT JOIN base_ktype k
        ON bo.profile_id=k.profile_id and bo.ktype_id=k.id
        left join base_deliveryinfo bd on bd.id = bo.deliver_id
        LEFT JOIN base_btype b
        ON b.profile_id=bo.profile_id and b.id=bo.btype_id
        JOIN ss_store s
        ON s.profile_id=bo.profile_id and s.otype_id=bo.id
        LEFT JOIN base_etype be
        ON s.profile_id=be.profile_id and be.id =s.owner_id
        <if test="param.isOtypeLimited!=null and param.isOtypeLimited ==true">
            left join base_limit_scope blb on blb.profile_id=#{param.profileId} and blb.object_type=3 and
            bo.id=blb.object_id and blb.etype_id=#{param.employeeId}
        </if>
        <where>
            bo.profile_id=#{param.profileId} and bo.deleted=0 and bo.classed = 0 and ( bo.ocategory=
            #{param.ocategory} or (bo.business_type=#{param.businessType} and bo.store_type=#{param.storeType}))

            <if test="param.partypeid!=null and param.partypeid!=''">
                and bo.partypeid like CONCAT(#{param.partypeid},'%')
            </if>
            <if test="param.stoped !=null and param.stoped > -1">
                and bo.stoped =#{param.stoped}
            </if>
            <if test="param.usercode !=null and param.usercode!=''">
                AND bo.usercode like CONCAT('%',#{param.usercode},'%')
            </if>
            <if test="param.fullname!=null and param.fullname!=''">
                AND bo.fullname like CONCAT('%',#{param.fullname},'%')
            </if>
            <if test="param.shopOwner !=null and param.shopOwner!=''">
                AND be.fullname like CONCAT('%',#{param.shopOwner},'%')
            </if>
            <if test="param.btypeName !=null and param.btypeName!=''">
                AND b.fullname like CONCAT('%',#{param.btypeName},'%')
            </if>
            <if test="param.ktypeName !=null and param.ktypeName!=''">
                AND k.fullname like CONCAT('%',#{param.ktypeName},'%')
            </if>
            <if test="param.memo !=null and param.memo!=''">
                AND bo.memo like CONCAT('%',#{param.memo},'%')
            </if>
            <if test="param.isOtypeLimited!=null and param.isOtypeLimited == true">
                and (blb.id is not null)
            </if>
        </where>
        group by id
        ORDER BY create_time desc
    </select>

    <select id="getStoreNameList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreInfo">
        SELECT bo.profile_id, bo.usercode, bo.fullname, bo.ocategory, bo.store_type, bo.business_type, bo.ktype_id,
        bo.btype_id, bo.check_account_type, bo.currency_id, bo.atype_id, bo.memo, bo.deleted, bo.stoped,
        bo.deliver_duration,
        bo.order_index, bo.create_time, bo.update_time, bo.deliver_id, bo.typeid, bo.partypeid, bo.classed, bo.rowindex,
        bo.independent_check
        ,bo.id as otypeId from base_otype bo
        left join ss_store ss on ss.otype_id = bo.id and ss.profile_id =#{profileId}
        left join ss_store_etype sse on bo.id = sse.otype_id and ss.profile_id =#{profileId}
        <!--        <if test="etypeId!=0 and null!= etypeId">-->
        <!--            left join base_limit_scope blb on blb.profile_id=#{profileId} and blb.object_type=3 and-->
        <!--            blb.object_id = sse.otype_id and blb.etype_id=#{etypeId}-->
        <!--        </if>-->

        <where>
            bo.profile_id =#{profileId} and bo.stoped =0 and bo.deleted =0 and bo.classed = 0 and ss.allow_pos_login=1
            and
            (bo.ocategory = 4
            or (bo.business_type = 0
            and bo.store_type = 1))
            <if test="etypeId!=0 and null!= etypeId">
                and exists (select 1 from base_limit_scope blb where blb.profile_id = #{profileId} and blb.object_type =
                3
                and sse.otype_id = blb.object_id and blb.etype_id = #{etypeId})
            </if>
        </where>
        group by bo.id
    </select>
    <select id="getStore" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreInfo">
        SELECT
        bo.id, bo.profile_id, bo.usercode, bo.fullname, bo.ocategory, bo.store_type, bo.business_type,
        bo.ktype_id,s.min_discount,
        s.open_store_value_password,s.password_type,
        s.cross_cash_bill,
        bo.btype_id, bo.check_account_type, bo.currency_id, bo.atype_id, bo.memo, bo.deleted, bo.stoped,
        bo.deliver_duration,
        bo.order_index, bo.create_time, bo.update_time, bo.deliver_id, bo.typeid, bo.partypeid, bo.classed, bo.rowindex,
        bo.independent_check,
        ifnull(bo2.fullname,'') as par_full_name ,k.fullname as ktype_name ,b.fullname as btype_name,s.otype_id as
        otype_id,be.fullname as shop_owner,
        s.open_time ,s.close_time ,s.owner_id, bd.cellphone ,bd.province ,bd.city,bd.secret_id
        ,bd.district
        ,bd.addr_lat ,bd.addr_lng
        ,bd.address,s.allow_pos_login,s.shop_logo_url,s.shop_scan_url,s.shop_scan_memo,s.shop_notice
        FROM base_otype bo
        JOIN ss_store s on s.profile_id=bo.profile_id and s.otype_id=bo.id
        left join base_otype bo2 on bo.partypeid = bo2.typeid and bo2.profile_id =#{profileId} and bo2.typeid !="00000"
        left join base_deliveryinfo bd on bd.id =bo.deliver_id
        left join base_ktype k on bo.profile_id=k.profile_id and bo.ktype_id=k.id
        left join base_btype b on b.profile_id=bo.profile_id and b.id=bo.btype_id
        left JOIN base_etype be on s.profile_id=be.profile_id and be.id =s.owner_id
        <where>
            bo.profile_id=#{profileId} and bo.deleted=0 and bo.id=#{otypeId}
            <if test="allowPosLogin != null">
                and s.allow_pos_login =#{allowPosLogin}
            </if>
        </where>
    </select>
    <select id="getFirstDirectStore" resultType="com.wsgjp.ct.baseinfo.core.dao.entity.Otype">
        SELECT profile_id,
               id,
               usercode,
               fullname,
               ocategory,
               store_type,
               business_type,
               ktype_id,
               btype_id,
               check_account_type,
               currency_id,
               atype_id,
               memo,
               deleted,
               stoped,
               deliver_duration,
               order_index,
               create_time,
               update_time,
               deliver_id,
               typeid,
               partypeid,
               classed,
               rowindex,
               independent_check
        FROM base_otype
        where deleted = 0
          and (ocategory = 4 or (business_type = 0
            and store_type = 1))
          and profile_id = #{profileId}
        limit 0,1
    </select>


    <select id="getCashierCountByName" resultType="boolean">
        select count(1) > 0 from ss_cashier sc

        <where>
            deleted =0 and profile_id =#{profileId} and otype_id =#{otypeId} and fullname =#{fullname} and id != #{id}
        </where>
    </select>

    <select id="checkStoreUsed" resultType="boolean">
        select count(1) > 0 from ss_promotion_filter sc
        left join ss_promotion sp on sp.profile_id =sc.profile_id and sp.id = sc.promotion_id
        <where>
            sp.deleted=0 and sc.profile_id =#{profileId} and sc.filter_id in
            <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="checkStoreUsedCashBox" resultType="boolean">
        select count(1) > 0 from ss_cashbox_payment scp
        <where>
            scp.profile_id =#{profileId} and scp.deleted=0 and scp.otype_id in
            <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="checkStoreUsedShiftChanges" resultType="boolean">
        select count(1) > 0 from ss_shift_changes_record sscr
        <where>
            sscr.profile_id =#{profileId} and sscr.otype_id in
            <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="checkStoreUsedCardTemplate" resultType="boolean">
        select count(1) > 0 from ss_card_template_filter scto
        <where>
            scto.profile_id =#{profileId} and scto.filter_type = 0 and scto.filter_id in
            <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="getCashierCountByUserCode" resultType="boolean">
        select count(1) > 0 from ss_cashier sc

        <where>
            deleted =0 and profile_id =#{profileId} and otype_id =#{otypeId} and usercode =#{usercode} and id != #{id}
        </where>
    </select>


    <!--    <select id="checkKtypeIn" resultType="boolean">-->
    <!--        SELECT count(1) > 0-->
    <!--        from base_ktype-->
    <!--        where profile_id = #{profileId}-->
    <!--          and id = #{ktypeId}-->
    <!--          and deleted = 0-->
    <!--          and stoped = 0-->
    <!--    </select>-->

    <select id="checkDuplicateOtype" resultType="java.lang.Boolean">
        select count(0) > 0
        from base_otype
        where profile_id = #{pageInfo.profileId}
        <if test="pageInfo.otypeId != null">
            and id != #{pageInfo.otypeId}
        </if>
        and fullname = #{pageInfo.fullname}
        and deleted = 0
        and classed = 0
    </select>

    <select id="checkDuplicateOtypeUserCode" resultType="java.lang.Boolean">
        select count(0) > 0
        from base_otype
        where profile_id = #{pageInfo.profileId}
        <if test="pageInfo.otypeId != null">
            and id != #{pageInfo.otypeId}
        </if>
        and usercode = #{pageInfo.usercode}
        and deleted = 0
        and classed = 0
    </select>

    <insert id="insertStore">
        INSERT INTO ss_store(otype_id, profile_id, open_time, close_time,
                             owner_id, shop_logo_url, shop_scan_url, shop_scan_memo, shop_notice, min_discount)
        VALUES (#{otypeId}, #{profileId}, #{openTime}, #{closeTime}, #{ownerId}, #{shopLogoUrl}, #{shopScanUrl},
                #{shopScanMemo}, #{shopNotice}, #{minDiscount});
    </insert>


    <update id="updateStore">
        UPDATE ss_store
        <trim prefix="set" suffixOverrides=",">
            owner_id = #{ownerId}, open_time = #{openTime},
            close_time = #{closeTime},
            owner_id=#{ownerId},
            shop_logo_url = #{shopLogoUrl},
            shop_scan_url = #{shopScanUrl},
            shop_scan_memo = #{shopScanMemo},
            shop_notice= #{shopNotice},
            min_discount = #{minDiscount},
        </trim>
        WHERE otype_id = #{otypeId} and profile_id = #{profileId}
    </update>

    <update id="deleteStore" parameterType="java.math.BigInteger">
        UPDATE ss_store
        SET deleted=1
        WHERE otype_id = #{id}
          and profile_id = #{profileId};
    </update>

    <delete id="deleteStores">
        update ss_store set deleted=1 where profile_id=#{profileId} and
        otype_id in
        <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="setStoreSetting">
        UPDATE ss_store
        SET cross_cash_bill           = #{param.crossCashBill},
            open_store_value_password = #{param.openStoreValuePassword},
            password_type             = #{param.passwordType}
        WHERE otype_id = #{param.otypeId}
          and profile_id = #{profileId}
    </update>

    <select id="getStoreSetting" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreInfo">
        SELECT *
        from ss_store
        WHERE otype_id = #{otypeId}
          and profile_id = #{profileId}
    </select>

    <update id="stopStore">
        UPDATE ss_store
        SET stoped = #{stoped}
        WHERE otype_id = #{otypeId}
          and profile_id = #{profileId}
    </update>

    <update id="stopStores">
        UPDATE ss_store
        SET stoped = #{params.stoped}
        where profile_id=#{profileId} and
        otype_id in
        <foreach collection="params.ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="changeAllowPosLogin">
        UPDATE ss_store
        SET allow_pos_login = #{allowPosLogin}
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
    </update>
    <select id="getStoreCashier" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreCashier">
        SELECT id, usercode, fullname, stoped, deleted, create_time, update_time, profile_id, otype_id
        from ss_cashier
        <where>
            deleted=0
            <if test="null != params.otypeId and params.otypeId != 0">
                and otype_id = #{params.otypeId}
            </if>
            <if test="null != params.profileId">
                and profile_id=#{params.profileId}
            </if>
            <if test="null != params.stoped">
                and stoped=#{params.stoped}
            </if>
        </where>
    </select>

    <update id="stopStoreCashier">
        UPDATE ss_cashier
        SET stoped = #{stoped}
        WHERE id = #{id}
          and profile_id = #{profileId}
    </update>

    <update id="deleteStoreCashier" parameterType="java.math.BigInteger">
        UPDATE ss_cashier
        SET deleted=1
        <where>
            id = #{id} and profile_id=#{profileId}
        </where>
    </update>

    <update id="deleteStoreCashierByOtypeId" parameterType="java.math.BigInteger">
        delete from ss_cashier
        <where>
            otype_id = #{otypeId} and profile_id=#{profileId}
        </where>
    </update>
    <update id="deleteStoreCashiers" parameterType="java.math.BigInteger">
        UPDATE ss_cashier
        SET deleted=1 where profile_id=#{profileId} and otype_id in
        <foreach item="otypeid" collection="otypeIds" open="(" separator="," close=")">
            ${otypeid}
        </foreach>
    </update>

    <insert id="insertStoreCashier">
        INSERT INTO ss_cashier (id, usercode, fullname, otype_id, profile_id)
        VALUES (#{id}, #{usercode},
                #{fullname}, #{otypeId}, #{profileId});
    </insert>


    <insert id="insertStoreCashiers">
        INSERT INTO ss_cashier (id, usercode, fullname, otype_id, profile_id)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.id}, #{item.usercode}, #{item.fullname}, #{item.otypeId}, #{item.profileId})
        </foreach>
    </insert>
    <update id="updateStoreCashier">
        UPDATE ss_cashier
        set usercode=#{usercode},
        fullname=#{fullname}
        <where>
            id = #{id} and profile_id=#{profileId} ;
        </where>
    </update>

    <insert id="insertStoreEtypes">
        INSERT INTO ss_store_etype(ID, profile_id, otype_id, etype_id)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.id,jdbcType=BIGINT},
            #{item.profileId,jdbcType=BIGINT},
            #{item.otypeId,jdbcType=BIGINT},
            #{item.etypeId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertStorePayments">
        INSERT INTO ss_store_payway (ID, profile_id, otype_id, payway_id)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.id,jdbcType=BIGINT},
            #{item.profileId,jdbcType=BIGINT},
            #{item.otypeId,jdbcType=BIGINT},
            #{item.paywayId,jdbcType=BIGINT})
        </foreach>
    </insert>


    <delete id="deleteStoreEtype">
        UPDATE ss_store_etype
        SET deleted=1
        <where>
            profile_id=#{profileId} and id =#{id}
        </where>
    </delete>
    <delete id="deleteStoreEtypeByOtypeId">
        UPDATE ss_store_etype
        SET deleted=1
        <where>
            profile_id=#{profileId} and otype_id =#{otypeId}
        </where>
    </delete>
    <delete id="deleteStorePayments">
        DELETE FROM ss_store_payway where profile_id=#{profileId} and otype_id in
        <foreach item="otypeid" collection="otypeIds" open="(" separator="," close=")">
            ${otypeid}
        </foreach>
    </delete>

    <update id="deleteStoreEtypes" parameterType="java.math.BigInteger">
        UPDATE ss_store_etype
        SET deleted=1 where otype_id in
        <foreach item="otypeid" collection="otypeIds" open="(" separator="," close=")">
            ${otypeid}
        </foreach>
    </update>
    <update id="logicDeleteStorePayments">
        UPDATE ss_store_payway
        SET deleted=1 where otype_id in
        <foreach item="otypeid" collection="otypeIds" open="(" separator="," close=")">
            ${otypeid}
        </foreach>
    </update>

    <select id="getStoreEtypeList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreEtype">
        select ss.id, ss.otype_id as otypeId, ss.etype_id as etypeId,e.fullname as etypeName,ss.profile_id as
        profileId,e.usercode,e.stoped
        from ss_store_etype ss
        left join base_etype e on e.profile_id = ss.profile_id and e.id = ss.etype_id
        <where>
            ss.deleted=0 and ss.profile_id=#{profileId} and ss.otype_id=#{otypeId} and e.deleted=0 and e.stoped=0
        </where>
    </select>

    <select id="getStoreEtypeListOther" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreEtype">
        select ss.id, ss.otype_id as otypeId, ss.etype_id as etypeId,e.fullname as etypeName,ss.profile_id as
        profileId,e.usercode,e.stoped
        from ss_store_etype ss
        left join base_etype e on e.profile_id = ss.profile_id and e.id = ss.etype_id
        <where>
            ss.deleted=0 and e.deleted = 0 and ss.etype_id is not null
            AND ss.profile_id=#{profileId}
            <if test="null != otypeId and otypeId != 0">
                and ss.otype_id=#{otypeId}
            </if>
            group by ss.etype_id
        </where>
    </select>
    <select id="getSysData" resultType="java.lang.String">
        select sub_value from sys_data
        <where>
            profile_id = #{profileId}
            and
            sub_name =#{subName}
        </where>
    </select>
    <select id="getStorePaymentList"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StorePayway">
        select ssp.id, ssp.payway_id, ssp.otype_id, bp.fullname as paywayName ,bp.atype_id ,ba.fullname as
        atype_fullname,bp.product,bp.payway_type,bp.platform_paytype,ssp.profile_id
        from ss_store_payway ssp
        left join base_payways bp on bp.id = ssp.payway_id and bp.profile_id=#{profileId}
        left join base_atype ba on bp.atype_id =ba.id and ba.profile_id=#{profileId} and ba.deleted=0
        <where>
            ssp.deleted =0 and bp.deleted =0 and bp.stoped=0 and ssp.profile_id=#{profileId} and ssp.otype_id=#{otypeId}
            and (bp.payway_type is null or bp.payway_type not in (8,9))
        </where>
    </select>
    <select id="getStoreCashierListByOtypeIds"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreCashier">
        SELECT id, usercode, fullname, stoped, deleted, create_time, update_time, profile_id, otype_id
        FROM ss_cashier
        <where>
            deleted=0
            <if test="null != profileId">
                and profile_id=#{profileId}
            </if>
            and otype_id in
            <foreach item="otypeid" collection="otypeIds" open="(" separator="," close=")">
                ${otypeid}
            </foreach>
        </where>
    </select>
    <select id="getStoreEtypeListByOtypeIds"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StoreEtype">
        select ss.id, ss.otype_id as otypeId, ss.etype_id as etypeId,e.fullname as etypeName,ss.profile_id as
        profileId,e.usercode,e.stoped
        from ss_store_etype ss
        left join base_etype e on e.profile_id = ss.profile_id and e.id = ss.etype_id
        <where>
            ss.deleted=0 and ss.profile_id=#{profileId} and e.deleted=0 and e.stoped=0 and otype_id in
            <foreach item="otypeid" collection="otypeIds" open="(" separator="," close=")">
                ${otypeid}
            </foreach>
        </where>
    </select>
    <select id="getStorePaymentListByOtypeIds"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StorePayway">
        select ssp.id, ssp.payway_id, ssp.otype_id, bp.fullname as paywayName
        from ss_store_payway ssp
        left join base_payways bp on bp.id = ssp.payway_id and bp.profile_id=#{profileId}
        <where>
            ssp.deleted=0 and ssp.profile_id=#{profileId} and bp.deleted=0
            and (bp.platform_paytype is null or bp.platform_paytype not in (3,5))
            and otype_id in
            <foreach item="otypeid" collection="otypeIds" open="(" separator="," close=")">
                ${otypeid}
            </foreach>
        </where>
    </select>
    <select id="getStoreBarcodeScaleConfig"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BarcodeScaleConfig">
        select
        id,
        profile_id,
        otype_id,
        output_format_type,
        mark_digit,
        total_digit,
        weight_digit,
        use_sku_code,
        deleted,
        create_time,
        update_time
        from
        ss_barcode_scale_config
        <where>
            deleted = 0
            and profile_id =#{profileId} and otype_id=#{otypeId}
        </where>
    </select>
    <select id="getAllowPosLoginCount" resultType="java.lang.Integer">
        select count(0)
        from
        ss_store
        <where>
            deleted =0 and allow_pos_login =1
            and profile_id =#{profileId}
            <if test="null != otypeId and otypeId != 0">
                and otype_id !=#{otypeId}
            </if>
        </where>
    </select>
    <select id="getPrintConfigList"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.PrintConfigInfo">
        select spc.id,spc.profile_id,spc.otype_id,spc.cashier_id,spc.sync_other_template,
        spc.print_type,spc.field_key,spc.selected,spc.custom_field_name,spc.print_location,spc.content_config from
        ss_print_config spc
        <where>
            spc.deleted=0 and spc.profile_id=#{profileId} and spc.cashier_id =#{cashierId}
        </where>
    </select>


    <sql id="filter">
        <trim prefix=" and (" suffix=")">
            <foreach collection="param.filter" item="item" separator="and">
                <choose>
                    <when test="item.type==0">
                        ${item.dataField} like concat('%',#{item.value},'%')
                    </when>
                    <when test="item.type == 1">
                        <trim prefixOverrides="and">
                            <if test="item.value1 != null ">
                                ${item.dataField} >= #{item.value1}
                            </if>
                            <if test="item.value2 != null ">
                                and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                            </if>
                        </trim>
                    </when>
                    <when test="item.type == 2">
                        ${item.dataField} = #{item.value}
                    </when>
                </choose>
            </foreach>
        </trim>
    </sql>

    <delete id="deletePrintConfig">
        DELETE
        FROM ss_print_config
        where profile_id = #{profileId}
          and cashier_id = #{cashierId}
    </delete>

    <insert id="batchInsertPrintConfig">
        INSERT INTO ss_print_config (id,
        profile_id,
        otype_id,
        cashier_id,
        print_type,
        field_key,
        selected,
        custom_field_name,
        print_location,
        content_config,
        sync_other_template)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.id}, #{item.profileId}, #{item.otypeId}, #{item.cashierId}, #{item.printType}, #{item.fieldKey},
            #{item.selected}, #{item.customFieldName}, #{item.printLocation}, #{item.contentConfig},
            #{item.syncOtherTemplate})
        </foreach>
    </insert>
</mapper>