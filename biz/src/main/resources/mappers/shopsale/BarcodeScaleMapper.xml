<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.BarcodeScaleMapper">
    <select id="getBarcodeScaleConfig"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BarcodeScaleConfig">
        select id,
               profile_id,
               otype_id,
               output_format_type,
               mark_digit,
               total_digit,
               weight_digit,
               use_sku_code,
               deleted
        from ss_barcode_scale_config
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
    </select>

    <delete id="deleteBarcodeScaleConfig">
        delete
        from ss_barcode_scale_config
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
    </delete>

    <insert id="insertBarcodeScaleConfig">
        insert into ss_barcode_scale_config (id, profile_id, otype_id, output_format_type, mark_digit
        <if test="totalDigit != null">
            , total_digit
        </if>
        <if test="weightDigit != null">
            , weight_digit
        </if>
        , use_sku_code)
        values (#{id}, #{profileId}, #{otypeId}, #{outputFormatType}, #{markDigit}
        <if test="totalDigit != null">
            , #{totalDigit}
        </if>
        <if test="weightDigit != null">
            , #{weightDigit}
        </if>
        , #{useSkuCode})
    </insert>

    <delete id="deleteBarcodeScalePtype">
        delete
        from ss_barcode_scale_ptype
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
    </delete>

    <insert id="insertBarcodeScalePtypeList">
        insert into ss_barcode_scale_ptype (id, profile_id, otype_id, ptype_id, sku_id, unit_id, plu_code, weight_mode,
        valid_date, tare_weight)
        values
        <foreach collection="list" item="ptype" separator=",">
            (#{ptype.id}, #{ptype.profileId}, #{ptype.otypeId}, #{ptype.ptypeId}, #{ptype.skuId}, #{ptype.unitId},
            #{ptype.pluCode}, #{ptype.weightMode}, #{ptype.validDate}, #{ptype.tareWeight})
        </foreach>
    </insert>

    <select id="getBarcodeScalePtype" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BarcodeScalePtype">
        select a.id,
               a.profile_id,
               a.otype_id,
               a.ptype_id,
               a.sku_id,
               a.unit_id,
               a.plu_code,
               a.weight_mode,
               a.valid_date,
               a.tare_weight,
               bp.fullname         as ptypeName,
               bps.propvalue_names as propertyName,
               bpu.unit_name,
               bpx.xcode,
               bpp.retail_price
        from ss_barcode_scale_ptype a
                 LEFT JOIN base_ptype bp
                           ON bp.id = a.ptype_id and bp.profile_id = #{profileId}
                 LEFT JOIN base_ptype_unit bpu
                           ON bpu.id = a.unit_id and bpu.profile_id = #{profileId}
                 LEFT JOIN base_ptype_sku bps
                           ON bps.id = a.sku_id and bps.profile_id = #{profileId}
                 left join base_ptype_xcode bpx
                           on bpx.ptype_id = a.ptype_id and bpx.sku_id = a.sku_id and bpx.unit_id = a.unit_id and
                              bpx.profile_id = #{profileId}
                 LEFT JOIN base_ptype_price bpp
                           on bpp.profile_id = #{profileId}
                               and bpp.ptype_id = a.ptype_id and bpp.unit_id = a.unit_id
                               and (if(bp.sku_price = 1, a.sku_id = bpp.sku_id, a.ptype_id = bpp.ptype_id))
        where a.profile_id = #{profileId}
          and a.otype_id = #{otypeId}
    </select>

    <select id="selectPtypeFromImport"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BarcodeScalePtypeImport">
        select bp.fullname as fullname,bpx.xcode,bp.id as ptypeId,bps.propvalue_names as propValues,bpu.unit_name as unitName,
        IF(bp.sku_price=0,IFNULL(upp.retail_price,0),IFNULL(ups.retail_price,0)) AS retailPrice, bp.sku_price,
        bps.id as skuId,bpu.id as unitId
        from base_ptype bp
        left join base_ptype_sku bps on bps.profile_id = #{profileId} and bps.ptype_id = bp.id
        left join base_ptype_unit bpu on bpu.profile_id = #{profileId} and bpu.ptype_id = bp.id
        left join base_ptype_price upp on upp.profile_id = #{profileId} and upp.ptype_id = bp.id and upp.unit_id =
        bpu.id and upp.sku_id = 0
        left join base_ptype_price ups on ups.profile_id = #{profileId} and ups.ptype_id = bp.id and ups.unit_id =
        bpu.id and ups.sku_id = bps.id
        left join base_ptype_xcode bpx on bpx.profile_id = #{profileId} and bpx.ptype_id = bp.id and bpx.unit_id =
        bpu.id and bpx.sku_id = bps.id
        where bp.profile_id = #{profileId} and bp.deleted = 0 and bp.stoped != 1 and bps.stoped != 1 and bp.pcategory !=
        2 and bps.id IS NOT NULL
        and bp.fullname = #{importPtype.fullname}
        <if test="importPtype.propValues != null">
            and bps.propvalue_names = #{importPtype.propValues}
        </if>
        <if test="importPtype.unitName != null">
            and bpu.unit_name = #{importPtype.unitName}
        </if>
        group by bp.id,bps.id,bpu.id
    </select>
</mapper>