<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.CashBoxPaymentMapper">
    <insert id="insert">
        INSERT INTO ss_cashbox_payment(id, amount, payment_type, etype_id, otype_id, profile_id, cashier_id)
        VALUES (#{id}, #{amount}, #{paymentType}, #{etypeId}, #{otypeId}, #{profileId}, #{cashierId});
    </insert>

    <select id="selectByTimeRange" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.cashbox.CashBoxPaymentDTO">
        SELECT scp.id, scp.amount, scp.payment_type, scp.etype_id, scp.otype_id, scp.create_time, scp.update_time,
        scp.deleted, scp.profile_id, scp.cashier_id,
        o.fullname as storeName,sc.fullname as cashierName,e.fullname as etypeName
        FROM ss_cashbox_payment scp
        left join base_otype o on o.id = scp.otype_id and o.deleted=0
        left join ss_cashier sc on sc.id = scp.cashier_id
        left join base_etype e on e.id = scp.etype_id
        WHERE
        scp.profile_id = #{payment.queryParams.profileId}
        <if test="payment.queryParams.startTime!=null and payment.queryParams.endTime !=null">
            AND scp.create_time between #{payment.queryParams.startTime} AND #{payment.queryParams.endTime}
        </if>
        <if test="payment.queryParams.paymentType!=null">
            <if test="payment.queryParams.paymentType ==0 ">
                AND payment_type in (0,2)
            </if>
            <if test="payment.queryParams.paymentType ==1 ">
                AND payment_type in (1,3)
            </if>
        </if>
        <if test="payment.queryParams.cashierId!=null">
            AND cashier_id = #{payment.queryParams.cashierId}
        </if>
        <if test="payment.queryParams.etypeId!=null">
            AND etype_id = #{payment.queryParams.etypeId}
        </if>

        <if test="payment.queryParams.partypeid!=null and payment.queryParams.partypeid!=''">
            and o.partypeid like CONCAT(#{payment.queryParams.partypeid},'%')
        </if>

        <if test="payment.queryParams.storeName!=null and payment.queryParams.storeName!=''">
            and o.fullname like CONCAT('%', #{payment.queryParams.storeName},'%')
        </if>

        <if test="payment.queryParams.cashierName!=null and payment.queryParams.cashierName!=''">
            and sc.fullname like CONCAT('%', #{payment.queryParams.cashierName},'%')
        </if>

        <if test="payment.queryParams.etypeName!=null and payment.queryParams.etypeName!=''">
            and e.fullname like CONCAT('%', #{payment.queryParams.etypeName},'%')
        </if>

        <if test="payment.queryParams.amount!=null and payment.queryParams.amount!=''">
            and scp.amount = #{payment.queryParams.amount}
        </if>

        <if test="payment.queryParams.otypeIds!=null and payment.queryParams.otypeIds.size>0">
            and scp.otype_id in
            <foreach collection="payment.queryParams.otypeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="payment.queryParams.etypeIds!=null and payment.queryParams.etypeIds.size>0">
            and scp.etype_id in
            <foreach collection="payment.queryParams.etypeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="payment.queryParams.cashierIds!=null and payment.queryParams.cashierIds.size>0">
            and sc.id in
            <foreach collection="payment.queryParams.cashierIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="payment.queryParams.orderField!=null and payment.queryParams.orderField!=''">
            ORDER BY ${payment.queryParams.orderField}
        </if>
        <if test="payment.queryParams.orderField==null or payment.queryParams.orderField ==''">
            ORDER BY scp.create_time desc
        </if>

        <if test="payment.pageSize>0">
            limit #{payment.pageIndex},#{payment.pageSize}
        </if>
    </select>

    <select id="getCashBoxPaymentRecordCount" resultType="java.lang.Integer">
        SELECT count(scp.id)
        FROM ss_cashbox_payment scp
        left join base_otype o on o.id = scp.otype_id and o.deleted=0
        left join ss_cashier sc on sc.id = scp.cashier_id
        left join base_etype e on e.id = scp.etype_id
        WHERE
        scp.profile_id = #{payment.queryParams.profileId}
        <if test="payment.queryParams.startTime!=null and payment.queryParams.endTime !=null">
            AND scp.create_time between #{payment.queryParams.startTime} AND #{payment.queryParams.endTime}
        </if>
        <!--        <if test="payment.queryParams.paymentType!=null">-->
        <!--            AND payment_type = #{payment.queryParams.paymentType}-->
        <!--        </if>-->

        <if test="payment.queryParams.paymentType!=null">
            <if test="payment.queryParams.paymentType ==0 ">
                AND payment_type in (0,2)
            </if>
            <if test="payment.queryParams.paymentType ==1 ">
                AND payment_type in (1,3)
            </if>
        </if>

        <if test="payment.queryParams.etypeId!=null">
            AND etype_id = #{payment.queryParams.etypeId}
        </if>
        <if test="payment.queryParams.cashierId!=null">
            AND cashier_id = #{payment.queryParams.cashierId}
        </if>

        <if test="payment.queryParams.partypeid!=null and payment.queryParams.partypeid!=''">
            and o.partypeid like CONCAT(#{payment.queryParams.partypeid},'%')
        </if>

        <if test="payment.queryParams.storeName!=null and payment.queryParams.storeName!=''">
            and o.fullname like CONCAT('%', #{payment.queryParams.storeName},'%')
        </if>

        <if test="payment.queryParams.cashierName!=null and payment.queryParams.cashierName!=''">
            and sc.fullname like CONCAT('%', #{payment.queryParams.cashierName},'%')
        </if>
        <if test="payment.queryParams.etypeName!=null and payment.queryParams.etypeName!=''">
            and e.fullname like CONCAT('%', #{payment.queryParams.etypeName},'%')
        </if>

        <if test="payment.queryParams.amount!=null and payment.queryParams.amount!=''">
            and scp.amount = #{payment.queryParams.amount}
        </if>

        <if test="payment.queryParams.otypeIds!=null and payment.queryParams.otypeIds.size>0">
            and scp.otype_id in
            <foreach collection="payment.queryParams.otypeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="payment.queryParams.etypeIds!=null and payment.queryParams.etypeIds.size>0">
            and scp.etype_id in
            <foreach collection="payment.queryParams.etypeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="payment.queryParams.cashierIds!=null and payment.queryParams.cashierIds.size>0">
            and sc.id in
            <foreach collection="payment.queryParams.cashierIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="payment.queryParams.orderField!=null and payment.queryParams.orderField!=''">
            ORDER BY ${payment.queryParams.orderField}
        </if>
        <if test="payment.queryParams.orderField==null or payment.queryParams.orderField ==''">
            ORDER BY scp.create_time desc
        </if>
    </select>

    <select id="getCashBoxRecordListCount"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.cashbox.CashBoxPaymentDTO">
        select SUM(amount) as amountTotal FROM ss_cashbox_payment scp
        left join base_otype o on o.id = scp.otype_id and o.deleted=0
        left join ss_cashier sc on sc.id = scp.cashier_id
        left join base_etype e on e.id = scp.etype_id
        WHERE
        <if test="profileId!=null">
            scp.profile_id = #{profileId}
            <if test="startTime!=null and endTime !=null">
                AND scp.create_time between #{startTime} AND #{endTime}
            </if>
            <!--            <if test="paymentType!=null">-->
            <!--                AND payment_type = #{paymentType}-->
            <!--            </if>-->

            <if test="paymentType!=null">
                <if test="paymentType ==0 ">
                    AND payment_type in (0,2)
                </if>
                <if test="paymentType ==1 ">
                    AND payment_type in (1,3)
                </if>
            </if>

            <if test="etypeId!=null">
                AND etype_id = #{etypeId}
            </if>
            <if test="cashierId!=null">
                AND cashier_id = #{cashierId}
            </if>

            <if test="partypeid!=null and partypeid!=''">
                and o.partypeid like CONCAT(#{partypeid},'%')
            </if>

            <if test="storeName!=null and storeName!=''">
                and o.fullname like CONCAT('%', #{storeName},'%')
            </if>

            <if test="cashierName!=null and cashierName!=''">
                and sc.fullname like CONCAT('%', #{cashierName},'%')
            </if>
            <if test="etypeName!=null and etypeName!=''">
                and e.fullname like CONCAT('%', #{etypeName},'%')
            </if>

            <if test="amount!=null and amount!=''">
                and scp.amount = #{amount}
            </if>

            <if test="otypeIds!=null and otypeIds.size>0">
                and scp.otype_id in
                <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="etypeIds!=null and etypeIds.size>0">
                and scp.etype_id in
                <foreach collection="etypeIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="cashierIds!=null and cashierIds.size>0">
                and sc.id in
                <foreach collection="cashierIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="orderField!=null and orderField!=''">
                ORDER BY ${orderField}
            </if>
            <if test="orderField==null or orderField ==''">
                ORDER BY scp.create_time desc
            </if>
        </if>
    </select>
</mapper>