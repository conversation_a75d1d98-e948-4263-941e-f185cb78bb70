<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.ShopSaleBillMapper">
    <select id="getVchcodeListBySourceVchcode" resultType="java.math.BigInteger">
        select distinct t.target_vchcode
        from td_bill_relation  t
                 left join td_bill_assinfo tba
                            on tba.profile_id = #{profileId} and tba.vchcode = t.target_vchcode
                 left join acc_bill_core tbc
                           on tbc.profile_id = #{profileId} and tbc.vchcode = t.target_vchcode
        where t.source_vchcode = #{sourceVchcode}
          and t.profile_id = #{profileId}
          and tba.pay_state = 1
          and tbc.deleted = 0
        union
        select distinct t.target_vchcode
        from td_bill_relation  t
                 left join acc_bill_assinfo tba
                            on tba.profile_id = #{profileId} and tba.vchcode = t.target_vchcode
                 left join acc_bill_core tbc
                            on tbc.profile_id = #{profileId} and tbc.vchcode = t.target_vchcode
        where t.source_vchcode = #{sourceVchcode}
          and t.profile_id = #{profileId}
          and tba.pay_state = 1
          and tbc.deleted = 0
    </select>

    <select id="getGoodBillByVchcodeList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsBillDTO">
        select
        a.vchcode,a.bill_number as
        number,a.profile_id,a.bill_date,a.btype_id,a.vchtype,tba.currency_order_preferential_allot_total,a.bill_date as
        date,tba.share_type,
        a.etype_id,a.dtype_id,a.summary,a.memo,tba.currency_bill_total,a.create_etype_id,a.post_etype_id,a.ktype_id,a.ktype_id2,
        b.fullname as kfullname,c.fullname as kfullname2,d.fullname as bfullname,a.post_state,
        f.fullname as efullname,g.fullname as createEfullname,h.fullname as dfullname,
        tba.payment_date,
        a.advance_total as currencyAdvanceTotal,
        p.customer_receiver,
        p.customer_id_card,p.customer_receiver_phone,p.customer_receiver_mobile,p.customer_receiver_zip_code,p.customer_email,
        p.customer_receiver_country,p.customer_receiver_province,p.customer_receiver_city,p.customer_receiver_district,p.customer_receiver_address
        as customerReceiverAddress,
        p.customer_receiver_full_address as customerReceiveFullAddress,b.scategory

        from td_bill_core a
        left join td_deliver_freight_info tdfi on tdfi.vchcode = a.vchcode and tdfi.profile_id = #{profileId}
        left join td_bill_assinfo tba on a.vchcode = tba.vchcode and tba.profile_id = #{profileId}
        left join pl_buyer p on a.buyer_id = p.buyer_id and p.profile_id=#{profileId}
        left join base_ktype b on a.ktype_id = b.id and b.profile_id=#{profileId}
        left join base_ktype c on a.ktype_id2 = c.id and c.profile_id=#{profileId}
        left join base_btype d on a.btype_id = d.id and d.profile_id=#{profileId}
        left join base_etype f on a.etype_id = f.id and f.profile_id=#{profileId}
        left join base_etype g on a.create_etype_id = g.id and g.profile_id=#{profileId}
        left join base_dtype h on a.dtype_id = h.id and h.profile_id=#{profileId}
        where a.profile_id=#{profileId}
        <choose>
            <when test="vchcodes!=null and vchcodes.size()>0">
                AND a.vchcode in
                <foreach item="item" index="index" collection="vchcodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
    </select>

    <insert id="doSave">
        insert into pub_bill_batch_save (`id`, `profile_id`, `employee_id`, `create_time`, `filter_count`, `sel_count`,
                                         `suc_count`, `err_count`, `create_type`)
        values (#{id}, #{profileId}, #{employeeId}, #{createTime}, #{filterCount}, #{selCount}, #{sucCount},
                #{errCount}, #{createType})
    </insert>
    <insert id="batchSave">
        INSERT INTO `pub_bill_batch_save_detail` (`id`,`profile_id`,`vchcode`,`vchtype`,`state`,`err_type`,`err_detail`,
        `batch_id`,`create_time`,`update_time`,`bill_number`,`bill_date`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.profileId},#{item.vchcode},#{item.vchtype},#{item.state},#{item.errType},#{item.errDetail},
            #{item.batchId},#{item.createTime},#{item.updateTime},#{item.billNumber},#{item.billDate})
        </foreach>
    </insert>
    <select id="getListByBatchSaveId"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PubBillBatchSaveDetailDAO">
        SELECT id,
               profile_id,
               vchcode,
               vchtype,
               state,
               err_type,
               err_detail,
               batch_id,
               create_time,
               update_time,
               bill_date,
               bill_number
        FROM pub_bill_batch_save_detail
        WHERE profile_id = #{profileId}
          AND batch_id = #{batchId}
    </select>
    <select id="getById" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PubBillBatchSaveDTO">
        SELECT a.id,
               a.profile_id,
               a.employee_id,
               a.create_time,
               a.filter_count,
               a.sel_count,
               a.suc_count,
               a.err_count,
               a.create_type,
               b.fullname as efullname
        from pub_bill_batch_save a
                 LEFT JOIN base_etype b on a.employee_id = b.id and b.profile_id = #{profileId}
        where a.profile_id = #{profileId}
          and a.id = #{batchId}
    </select>

    <update id="updateDetailEntity">
        update pub_bill_batch_save_detail
        set state      = #{state},
            err_detail =#{errDetail},
            err_type=#{errType}
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateEntity">
        update pub_bill_batch_save
        set suc_count = #{sucCount},
            err_count =#{errCount}
        where profile_id = #{profileId}
          and id = #{id}
    </update>
</mapper>