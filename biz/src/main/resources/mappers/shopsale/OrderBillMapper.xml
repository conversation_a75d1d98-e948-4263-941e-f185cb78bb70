<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.OrderBillMapper">
    <select id="getPerferentialBill" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.BillPreferentialDTO">
        SELECT spb.preferential_total, spb.preferential_type, spb.vchcode
        from ss_preferential_bill spb
        where spb.profile_id = #{profileId}
        and spb.deleted = 0
        and spb.vchcode in
        <foreach item="vchcode" index="index" collection="vchcodes" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
    </select>

    <sql id="isNotOnlyVchcode">
        a
        .
        vchcode
        ,
        a.profile_id,
        a.vchtype,
        a.business_type,
        a.process_type,
        a.bill_number,
        a.bill_date,
        a.otype_id,
        a.btype_id,
        a.ktype_id,
        a.ktype_id2,
        a.etype_id,
        a.dtype_id,
        a.summary,
        a.memo,
        a.create_type,
        a.bill_total,
        a.tax_total,
        a.dised_taxed_total,
        a.post_state,
        a.post_time,
        a.create_etype_id,
        a.client_type,
        a.inout_state,
        a.pay_btype_id,
        a.vip_card_id,
        case a.post_state when 0 then a.post_state else tba.audit_state
        end
        as auditState,
        b.pay_confirm_state,
        b.pay_state,
        b.payment_date,
        b.custom_type as customType,
        b.ptype_qty as ptypeQty,
        b.bill_discount,
        b.currency_order_preferential_allot_total,
        b.currency_give_preferential_total,
        CASE a.post_state
        WHEN 100 AND tba.audit_state &lt; 4 THEN 1
        WHEN 100 AND tba.audit_state = 4 THEN 4
        WHEN 500 THEN 10
        WHEN 550 THEN 6
        WHEN 600 THEN 7
        WHEN 650 THEN 5
        WHEN 700 THEN 8
        WHEN 800 THEN 8
        ELSE IFNULL(tba.audit_state, 0)
        END
        AS postAndauditState,
        b.ptype_unit_qty as ptypeunitQty,
        b.ptype_sub_qty as ptypeSubQty,
        c.fullname as kfullname,
        d.fullname as kfullname2,
        i.fullname as bfullname,
        o.fullname as ofullname,
        f.fullname as efullname,
        g.fullname as createEfullname,
        h.fullname as dfullname,
        se.fullname as storeManagerName,
        balancebtype.fullname as balanceFullname,
        (m.currency_balance_total - m.currency_balance_remain) as balance_settled,
        m.currency_balance_remain,
        m.balance_business_type,
        ifnull(print.print_count, 0) print_count,
        i.usercode as busercode,
        tba.audit_state as content_audit_state,
        bc.fullname as vipName,
        bd.buyer_id,
        bd.customer_receiver_phone as vipPhone,
        IFNULL(ABS(aba.currency_total), 0) as advanceTotal
    </sql>

    <resultMap id="BaseResultMap" type="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.TdBillCoreDAO">
        <id column="vchcode" property="vchcode" jdbcType="BIGINT"/>
        <result column="profile_id" property="profileId" jdbcType="BIGINT"/>
        <result column="vchtype" property="vchtype" jdbcType="INTEGER"/>
        <result column="business_type" property="businessType" jdbcType="BIGINT"/>
        <result column="process_type" property="processType" jdbcType="INTEGER"/>
        <result column="deliver_type" property="deliveryType" jdbcType="INTEGER"/>
        <result column="accounting_type" property="accountingType" jdbcType="INTEGER"/>
        <result column="bill_number" property="billNumber" jdbcType="VARCHAR"/>
        <result column="shopType" property="shopType" jdbcType="VARCHAR"/>
        <result column="bill_date" property="billDate" jdbcType="DATE"/>
        <result column="period" property="period" jdbcType="INTEGER"/>
        <result column="otype_id" property="otypeId" jdbcType="BIGINT"/>
        <result column="btype_id" property="btypeId" jdbcType="BIGINT"/>
        <result column="pay_btype_id" property="balanceBtypeId" jdbcType="BIGINT"/>
        <result column="ktype_id" property="ktypeId" jdbcType="BIGINT"/>
        <result column="ktype_id2" property="ktypeId2" jdbcType="BIGINT"/>
        <result column="etype_id" property="etypeId" jdbcType="BIGINT"/>
        <result column="dtype_id" property="dtypeId" jdbcType="BIGINT"/>
        <result column="buyer_id" property="buyerId" jdbcType="BIGINT"/>
        <result column="summary" property="summary" jdbcType="VARCHAR"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="create_type" property="createType" jdbcType="INTEGER"/>
        <result column="currency_id" property="currencyId" jdbcType="BIGINT"/>
        <result column="exchange_rate" property="exchangeRate" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="fee_total" property="feeTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="otherincome_total" property="otherIncomeTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="preferential_total" property="preferentialTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="bill_total" property="billTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_fee_total" property="currencyFeeTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_otherincome_total" property="currencyOtherIncomeTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_preferential_total" property="currencyPreferentialTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_bill_total" property="currencyBillTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_bill_total" property="advanceGiftBillTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="balance_settled" jdbcType="DECIMAL" property="balanceSettled"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_order_buyer_freight_fee" jdbcType="DECIMAL" property="currencyOrderBuyerFreightFee"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="post_state" property="postState" jdbcType="INTEGER"/>
        <result column="auditState" property="auditState" jdbcType="INTEGER"/>
        <result column="post_time" property="postTime" jdbcType="TIMESTAMP"/>
        <result column="create_etype_id" property="createEtypeId" jdbcType="BIGINT"/>
        <result column="distribution_etype_id" property="distributionEtypeId" jdbcType="BIGINT"/>
        <result column="distributionEtypeName" property="distributionEtypeName" jdbcType="VARCHAR"/>
        <result column="distributionBtypeId" property="distributionBtypeId" jdbcType="BIGINT"/>
        <result column="distributionDtypeName" property="distributionDtypeName" jdbcType="VARCHAR"/>
        <result column="storeManagerName" property="storeManagerName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="ptypeunitQty" property="ptypeunitQty" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>

        <result column="payment_date" property="paymentDate" jdbcType="DATE"/>
        <result column="print_count" property="printCount" jdbcType="INTEGER"/>
        <!--        仓库一名称-->
        <result column="kfullname" property="kfullname" jdbcType="VARCHAR"/>
        <!--        仓库2名称-->
        <result column="kfullname2" property="kfullname2" jdbcType="VARCHAR"/>

        <!--  往来单位全名      -->
        <result column="bfullname" property="bfullname" jdbcType="VARCHAR"/>

        <!--   结算单位全名     -->
        <result column="balanceFullname" property="balanceFullname" jdbcType="VARCHAR"/>
        <!--   业务员全名     -->
        <result column="efullname" property="efullname" jdbcType="VARCHAR"/>
        <!--      部门全名  -->
        <result column="dfullname" property="dfullname" jdbcType="VARCHAR"/>
        <!--        -->
        <result column="createEfullname" property="createEfullname" jdbcType="VARCHAR"/>

        <result column="customType" property="customType" jdbcType="INTEGER"/>
        <result column="customer_receiver" property="customerReceiver" jdbcType="VARCHAR"/>
        <result column="customer_receiver_phone" property="customerReceiverPhone" jdbcType="VARCHAR"/>
        <result column="customer_receiver_mobile" property="customerReceiverMobile" jdbcType="VARCHAR"/>
        <result column="customer_receiver_zip_code" property="customerReceiverZipCode" jdbcType="VARCHAR"/>
        <result column="customer_email" property="customerEmail" jdbcType="VARCHAR"/>
        <result column="customer_receiver_country" property="customerReceiverCountry" jdbcType="VARCHAR"/>
        <result column="customer_receiver_province" property="customerReceiverProvince" jdbcType="VARCHAR"/>
        <result column="customer_receiver_city" property="customerReceiverCity" jdbcType="VARCHAR"/>
        <result column="customer_receiver_district" property="customerReceiverDistrict" jdbcType="VARCHAR"/>
        <result column="customer_receiver_address" property="customerReceiverAddress" jdbcType="VARCHAR"/>
        <result column="customerReceiveFullAddress" property="customerReceiveFullAddress" jdbcType="VARCHAR"/>
        <result column="countQty" jdbcType="DECIMAL" property="countQty"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="balance_settled" jdbcType="DECIMAL" property="balanceSettled"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_balance_remain" jdbcType="DECIMAL" property="currencyBalanceRemain"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="countCostTotal" jdbcType="DECIMAL" property="countCostTotal"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="countCostQty" jdbcType="DECIMAL" property="countCostQty"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="ptypeQty" jdbcType="DECIMAL" property="ptypeQty"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="ptypeSubQty" jdbcType="DECIMAL" property="ptypeSubQty"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <!--        财务单据表头金额-->
        <result column="customFeeAccountTotal" jdbcType="DECIMAL" property="customFeeAccountTotal"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="customFeeSettleAccountTotal" jdbcType="DECIMAL" property="customFeeSettleAccountTotal"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="customFeeAccountName" property="customFeeAccountName" jdbcType="VARCHAR"/>
        <result column="customFeeSettleAccountName" property="customFeeSettleAccountName" jdbcType="VARCHAR"/>
        <result column="printCount" jdbcType="INTEGER" property="printCount"/>
        <result column="postAndauditState" property="postAndauditState" jdbcType="INTEGER"/>
        <result column="busercode" property="busercode" jdbcType="VARCHAR"/>
        <result column="bcategory" property="bcategory" jdbcType="INTEGER"/>
        <result column="freightBtypeId" property="freightBtypeId" jdbcType="INTEGER"/>
        <result column="freightBillNo" property="freightBillNo" jdbcType="VARCHAR"/>
        <result column="freightBtypeName" property="freightBtypeName" jdbcType="VARCHAR"/>
        <!--  销售区域,与btype相关   -->
        <result column="areaFullName" property="areaFullName" jdbcType="VARCHAR"/>
        <!--  零售pos单据查询相关   -->
        <result column="vipName" property="vipName" jdbcType="VARCHAR"/>
        <result column="vipPhone" property="vipPhone" jdbcType="VARCHAR"/>
        <result column="cashierName" property="cashierName" jdbcType="VARCHAR"/>
        <result column="commission_btype_id" property="commissionBtypeId" jdbcType="BIGINT"/>

        <result column="currency_order_preferential_allot_total" property="currencyOrderPreferentialAllotTotal"
                jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="sourceVchcode" property="sourceVchcode" jdbcType="BIGINT"/>
        <result column="ofullname" property="ofullname" jdbcType="VARCHAR"/>
        <result column="pay_confirm_state" property="payConfirmState" jdbcType="INTEGER"/>
        <result column="pay_state" property="payState" jdbcType="INTEGER"/>
        <result column="bill_discount" property="billDiscount" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="dised_taxed_total" property="disedTaxedTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="tax_total" property="taxTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="content_audit_state" property="contentAuditState" jdbcType="INTEGER"/>
        <result column="checkStatus" property="checkStatus" jdbcType="INTEGER"/>
        <result column="pay_time_type" property="payTimeType" jdbcType="INTEGER"/>
        <result column="invoiceState" property="invoiceState" jdbcType="INTEGER"/>
        <result column="advanceTotal" property="advanceTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="advanceGiftTotal" property="advanceGiftTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_give_preferential_total" property="currencyGivePreferentialTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="failure_info" property="failureInfo" jdbcType="VARCHAR"/>
        <result column="currency_ptype_preferential_total" property="currencyPtypePreferentialTotal"
                jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <collection property="mark"
                    ofType="com.wsgjp.ct.common.enums.core.entity.DeliverNewMark">
            <id column="markId" property="id"/>
            <result column="mark_target" property="markTarget"/>
            <result column="show_type" property="showType"/>
            <result column="order_type" property="orderType"/>
            <result column="mark_code" property="markCode"/>
            <result column="mark_use_type" property="markUseType"/>
            <result column="markCreateType" property="createType"/>
        </collection>
        <!--        <collection property="payMentlList"-->
        <!--                    ofType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.PayMentDTO">-->
        <!--            <id column="payId" property="id"/>-->
        <!--            <result column="payAtypeId" property="atypeId"/>-->
        <!--            <result column="payOutNo" property="outNo"/>-->
        <!--            <result column="paywayFullname" property="paywayFullname"/>-->
        <!--        </collection>-->
    </resultMap>

    <select id="getPayMentlList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.PayMentDTO">
        select tba2.id,tba2.atype_id as atypeId,COALESCE(NULLIF(TRIM(sopr.pay_out_no), ''), tba2.pay_out_no) AS outNo,
        tba2.currency_total as currencyAtypeTotal,
        bpay.fullname as paywayFullname,bpay.payway_type,tba2.vchcode,tba2.payway_id
        from
        td_bill_account tba2
        left join base_payways bpay on bpay.id = tba2.payway_id and bpay.profile_id = #{profileId}
        left join ss_online_pay_record sopr on sopr.vchcode = tba2.vchcode and sopr.payway_id = tba2.payway_id and sopr.deleted = 0 and sopr.profile_id = #{profileId}
        where tba2.profile_id = #{profileId}
        <if test="vchcodes != null and vchcodes.size() > 0">
            and tba2.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getPayMentAcclList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.PayMentDTO">
        select tba2.id,tba2.atype_id as atypeId, COALESCE(NULLIF(TRIM(sopr.pay_out_no), ''), tba2.pay_out_no) AS outNo,
        tba2.currency_total as currencyAtypeTotal,
        bpay.fullname as paywayFullname,bpay.payway_type,tba2.vchcode,tba2.payway_id
        from
        acc_bill_account tba2
        left join base_payways bpay on bpay.id = tba2.payway_id and bpay.profile_id = #{profileId}
        left join ss_online_pay_record sopr on sopr.vchcode = tba2.vchcode and sopr.payway_id = tba2.payway_id and sopr.deleted = 0 and sopr.profile_id = #{profileId}
        where tba2.profile_id = #{profileId}
        <if test="vchcodes != null and vchcodes.size() > 0">
            and tba2.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <resultMap id="BaseDetailMap" type="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsDetailDTO">
        <!--    主表的字段集合  c -->
        <id column="detail_id" property="detailId" jdbcType="BIGINT"/>
        <result column="stockQty" property="stockQty" jdbcType="DECIMAL"/>
        <result column="vchcode" property="vchcode" jdbcType="BIGINT"/>
        <result column="vchtype" property="vchtype" jdbcType="INTEGER"/>
        <result column="profile_id" property="profileId" jdbcType="BIGINT"/>
        <result column="ktype_id" property="ktypeId" jdbcType="BIGINT"/>
        <result column="ptype_id" property="ptypeId" jdbcType="BIGINT"/>
        <result column="ptypeClass" property="ptypeClass" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="shortname" property="shortname" jdbcType="VARCHAR"/>
        <result column="ptype_area" property="ptypeArea" jdbcType="VARCHAR"/>
        <result column="sku_pic_url" property="skuPicUrl" jdbcType="VARCHAR"/>
        <result column="cost_price" property="costPrice" jdbcType="DECIMAL"/>
        <result column="cost_total" property="costTotal" jdbcType="DECIMAL"/>
        <result column="tax_rate" property="taxRate" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="inout_type" property="inoutType" jdbcType="TINYINT"/>
        <result column="source_vchtype" property="sourceVchtype" jdbcType="BIGINT"/>
        <result column="source_vchcode" property="sourceVchcode" jdbcType="BIGINT"/>
        <result column="source_business_type" property="sourceBusinessType" jdbcType="BIGINT"/>
        <result column="source_detail_id" property="sourceDetailId" jdbcType="BIGINT"/>
        <result column="source_bill_number" property="sourceBillNumber" jdbcType="VARCHAR"/>
        <result column="combo_detail_id" property="comboDetailId" jdbcType="BIGINT"/>
        <result column="qty" property="qty" jdbcType="DECIMAL"/>
        <result column="request_qty" property="requestQty" jdbcType="DECIMAL"/>
        <result column="request_sub_qty" property="requestSubQty" jdbcType="DECIMAL"/>
        <result column="request_unit_qty" property="requestUnitQty" jdbcType="DECIMAL"/>
        <result column="completed_qty" property="completedQty" jdbcType="DECIMAL"/>
        <result column="row_index" property="rowIndex" jdbcType="INTEGER"/>
        <result column="row_index" property="__rowIndex" jdbcType="INTEGER"/>
        <result column="ptype_preferential_total" property="currencyPtypePreferentialTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <!--   扩展表字段 tbde-->
        <result column="wmsRealityQty" property="wmsRealityQty" jdbcType="DECIMAL"/>
        <result column="custom_buy_type" property="customBuyType" jdbcType="BIGINT"/>
        <result column="give_vip_score" property="giveVipScore" jdbcType="TINYINT"/>
        <result column="typeid" property="typeId" jdbcType="VARCHAR"/>

        <!--   商品表字段  pt-->
        <result column="fullname" property="pFullName" jdbcType="VARCHAR"/>
        <result column="usercode" property="pUserCode" jdbcType="VARCHAR"/>
        <result column="snenabled" property="snenabled" jdbcType="TINYINT"/>
        <result column="pcategory" property="pcategory" jdbcType="TINYINT"/>
        <result column="propenabled" property="propenabled" jdbcType="BIT"/>
        <result column="batchenabled" property="batchenabled" jdbcType="BIT"/>
        <result column="gift" property="gift" jdbcType="TINYINT"/>

        <!--info  i表集合-->
        <result column="unit_qty" property="unitQty" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="sub_qty" property="subQty" jdbcType="DECIMAL"/>
        <result column="completed_sub_qty" property="completedSubQty" jdbcType="DECIMAL"/>
        <result column="currency_price" property="currencyPrice" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_total" property="currencyTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_dised_price" property="currencyDisedPrice" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_dised_total" property="currencyDisedTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="discount" property="discount" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_dised_taxed_price" property="currencyDisedTaxedPrice" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_dised_taxed_total" property="currencyDisedTaxedTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_tax_total" property="currencyTaxTotal" jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="currency_completed_preferential_share" property="currencyCompletedPreferentialShare"
                jdbcType="DECIMAL"/>
        <result column="refund_qty" property="refundQty" jdbcType="DECIMAL"/>
        <result column="refund_sub_qty" property="refundSubQty" jdbcType="DECIMAL"/>
        <result column="refund_order_preferential_allot_total" property="refundOrderPreferentialAllotTotal"
                jdbcType="DECIMAL"/>
        <result column="ptype_commission_rate" property="ptypeCommissionRate" jdbcType="DECIMAL"/>
        <result column="currency_order_preferential_allot_total" property="currencyOrderPreferentialAllotTotal"
                jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>

        <result column="batchno" property="batchNo" jdbcType="VARCHAR"/>
        <result column="sub_unit_name" property="subUnitName" jdbcType="VARCHAR"/>
        <result column="produce_date" property="produceDate" jdbcType="DATE"/>
        <result column="expire_date" property="expireDate" jdbcType="DATE"/>
        <result column="batch_price" property="batchPrice" jdbcType="DECIMAL"/>
        <result column="unit_rate" property="unitRate" jdbcType="DECIMAL"/>
        <result column="unit_id" property="unitId" jdbcType="BIGINT"/>
        <result column="unit_name" property="unitName" jdbcType="VARCHAR"/>
        <result column="kfullname" property="kfullname" jdbcType="VARCHAR"/>
        <result column="scategory" property="scategory" jdbcType="INTEGER"/>
        <result column="stock_type" property="stockType" jdbcType="INTEGER"/>
        <result column="stock_state" property="stockState" jdbcType="INTEGER"/>
        <result column="protect_days" property="protectDays" jdbcType="INTEGER"/>
        <result column="standard" property="standard" jdbcType="VARCHAR"/>
        <result column="ptype_type" property="ptypetype" jdbcType="VARCHAR"/>
        <result column="brand_name" property="brandName" jdbcType="VARCHAR"/>
        <result column="fullbarcode" property="fullbarcode" jdbcType="VARCHAR"/>
        <result column="xcode" property="xcode" jdbcType="VARCHAR"/>
        <result column="pic_url" property="picUrl" jdbcType="VARCHAR"/>
        <result column="objectKtypeId" property="objectKtypeId" jdbcType="BIGINT"/>
        <result column="ptype_length" property="ptypeLength" jdbcType="DECIMAL"/>
        <result column="ptype_width" property="ptypeWidth" jdbcType="DECIMAL"/>
        <result column="ptype_height" property="ptypeHeight" jdbcType="DECIMAL"/>
        <result column="weight" property="weight" jdbcType="DECIMAL"/>
        <result column="length_unit" property="lengthUnit" jdbcType="DECIMAL"/>
        <result column="weight_unit" property="weightUnit" jdbcType="DECIMAL"/>
        <result column="currencyOrderFeeAllotTotal" property="currencyOrderFeeAllotTotal" jdbcType="DECIMAL"/>
        <result column="custom_field01" property="customField01" jdbcType="VARCHAR"/>
        <result column="custom_field02" property="customField02" jdbcType="VARCHAR"/>
        <result column="custom_field03" property="customField03" jdbcType="VARCHAR"/>
        <result column="custom_field04" property="customField04" jdbcType="VARCHAR"/>
        <result column="custom_field05" property="customField05" jdbcType="VARCHAR"/>
        <result column="custom_field06" property="customField06" jdbcType="DECIMAL"/>
        <result column="custom_field07" property="customField07" jdbcType="DECIMAL"/>
        <result column="custom_field08" property="customField08" jdbcType="DECIMAL"/>
        <result column="custom_field09" property="customField09" jdbcType="DATE"/>
        <result column="custom_field10" property="customField10" jdbcType="DATE"/>
        <result column="custom_field11" property="customField11" jdbcType="DATE"/>
        <result column="custom_field12" property="customField12" jdbcType="DECIMAL"/>
        <result column="custom_field13" property="customField13" jdbcType="DECIMAL"/>
        <result column="orderUnitId" property="orderUnitId" jdbcType="BIGINT"/>
        <result column="orderUnitRate" property="orderUnitRate" jdbcType="DECIMAL"/>
        <result column="orderUnitQty" property="orderUnitQty" jdbcType="DECIMAL"/>
        <result column="orderCompletedQty" property="orderCompletedQty" jdbcType="DECIMAL"/>
        <result column="currencyBuyerTotal" property="currencyBuyerTotal" jdbcType="DECIMAL"/>
        <result column="ktype_point_id" property="ktypePointId" jdbcType="BIGINT"/>
        <result column="ktype_point_type" property="ktypePointType" jdbcType="TINYINT"/>
        <result column="car_id" property="carId" jdbcType="BIGINT"/>
        <result column="car_number" property="carNumber" jdbcType="VARCHAR"/>
        <result column="drfullname" property="drfullname" jdbcType="VARCHAR"/>
        <result column="buyer_dised_taxed_price" property="buyerDisedTaxedPrice" jdbcType="DECIMAL"/>
        <result column="buyer_dised_taxed_total" property="buyerDisedTaxedTotal" jdbcType="DECIMAL"/>

        <result column="productAccountName" property="productAccountName" jdbcType="VARCHAR"/>
        <result column="currency_give_preferential_total" property="currencyGivePreferentialTotal"
                jdbcType="DECIMAL"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.BillBigDecimalTypeHandler"/>
        <result column="give_preferential_total" property="givePreferentialTotal"
                jdbcType="DECIMAL"/>
        <result column="ptype_standard" property="ptypeStandard" jdbcType="VARCHAR"/>
        <result column="zyPtypeType" property="ptypeType" jdbcType="VARCHAR"/>

        <!--   套餐明细表 tbdc-->
        <result column="combo_id" property="comboId" jdbcType="BIGINT"/>
    </resultMap>

    <select id="getTdGoodsDetails" resultMap="BaseDetailMap" >
        select distinct
        tbde.custom_buy_type,
        tbde.give_vip_score,
        c.detail_id, c.vchcode, c.profile_id, c.vchtype,
        c.ktype_id,c.ptype_id,c.sku_id,c.sku_name,c.inout_type,bpp.pic_url,c.combo_detail_id,c.gift,c.completed_qty,c.qty,c.row_index,i.request_qty,i.request_sub_qty,i.request_unit_qty,
        c.cost_price,c.cost_total,tbdb.batch_price,c.tax_rate,c.memo,c.source_vchtype,c.source_vchcode,c.source_business_type,c.source_detail_id, c.ptype_preferential_total,
        c.source_number as source_bill_number,pt.usercode,pt.fullname,pt.barcode,i.unit_qty,
        i.currency_price,i.currency_total,i.currency_dised_price,i.currency_dised_total,i.discount,i.currency_order_preferential_allot_total,
        i.currency_completed_preferential_share, i.currency_tax_total as currency_tax_total,
        c.refund_qty,c.refund_sub_qty,i.refund_order_preferential_allot_total,c.ptype_commission_total,
        i.currency_dised_taxed_price,i.currency_dised_taxed_total,tbdb.batchno,tbdb.produce_date,tbdb.expire_date,i.unit_rate,i.unit_id,bpu.unit_name,i.sub_unit_name,c.sub_qty,
        c.completed_sub_qty,
        bs.fullname as kfullname,bs.fullname as kfullname,bs.scategory,0 as stock_type,0 as stock_state,
        pt.protect_days,pt.propenabled, pt.snenabled,pt.batchenabled,pt.standard,pt.ptype_type,pt1.fullname as
        ptypeClass,bb.brand_name, c.sku_barcode fullbarcode,i.combo_share_scale AS comboShareScale,
        i.currency_order_fee_allot_total as currencyOrderFeeAllotTotal ,pt.pcategory,
        pt.ptype_length,pt.ptype_width,pt.ptype_height,pt.weight,pt.length_unit,pt.weight_unit,sku.pic_url as
        sku_pic_url,pt.sku_price,pt.cost_mode,c.cost_id,
        IFNULL(SUM(abid.qty),0) as
        inOutQty,pt.shortname,pt.ptype_area,i.service_start_time,i.service_end_time,i.service_expire_time,i.product_account_id,i.ptype_commission_rate,
        i.currency_give_preferential_total,c.give_preferential_total,
        CASE (IFNULL(tba.share_type,0) + IFNULL(tba.freight_share_type,0)) WHEN 0 THEN 0 ELSE 3 END AS
        isShare,c.ktype_point_id,c.ktype_point_type,
        tbddb.buyer_dised_taxed_price,tbddb.buyer_dised_taxed_total,tbde.ptype_standard,tbde.ptype_type as zyPtypeType,
        tbdc.combo_id, pt.typeid
        from td_bill_detail_core c
        LEFT JOIN td_bill_detail_extend tbde on tbde.vchcode = c.vchcode and tbde.detail_id = c.detail_id and
        tbde.profile_id = #{profileId}
        left join td_bill_detail_batch tbdb on tbdb.detail_id = c.detail_id and tbdb.vchcode = c.vchcode
        and tbdb.profile_id=#{profileId}
        left join td_bill_detail_combo tbdc on tbdc.vchcode = c.vchcode and tbdc.id = c.combo_detail_id
        and tbdc.profile_id = #{profileId}
        left join td_bill_detail_distribution_buyer tbddb on tbddb.detail_id = c.detail_id and tbddb.vchcode = c.vchcode
        and tbddb.profile_id=#{profileId}
        LEFT JOIN td_bill_assinfo tba
        on tba.vchcode = c.vchcode and tba.profile_id = #{profileId}
        left join td_bill_detail_assinfo i on c.detail_id = i.detail_id and c.vchcode = i.vchcode and i.profile_id =
        #{profileId}
        left join base_ptype pt on c.ptype_id = pt.id and pt.profile_id = #{profileId}
        left join base_ptype pt1 on pt1.typeid = left(pt.typeid,5) and pt1.profile_id = #{profileId}
        left join base_ptype_pic bpp on pt.id = bpp.ptype_id and bpp.profile_id = #{profileId} AND bpp.rowindex = 1
        LEFT JOIN base_ptype_sku sku ON c.sku_id = sku.id AND c.ptype_id = sku.ptype_id AND sku.profile_id=#{profileId}
        left join base_brandtype bb on pt.brand_id = bb.id and bb.profile_id = #{profileId}
        LEFT JOIN base_ktype bs on c.ktype_id = bs.id and bs.profile_id = #{profileId}
        LEFT join base_ptype_unit bpu on i.unit_id = bpu.id and bpu.profile_id = #{profileId}
        LEFT JOIN td_bill_inout_detail abid ON c.vchcode = abid.vchcode AND c.detail_id = abid.detail_id AND
        abid.ktype_point_type = 0 AND abid.inout_detail_type != 9 AND abid.profile_id=#{profileId}
        where c.profile_id = #{profileId} and c.combo_detail_id = 0
        <if test="vchcodes != null and vchcodes.size() > 0">
            and c.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        group by c.detail_id
        order by c.detail_id, c.row_index
    </select>

    <select id="getAccGoodsDetails" resultMap="BaseDetailMap">
        select distinct
        tbde.custom_buy_type,
        tbde.give_vip_score,
        c.detail_id, c.vchcode, c.profile_id, c.vchtype,
        c.ktype_id,c.ptype_id,c.sku_id,c.sku_name,c.inout_type,bpp.pic_url,c.combo_detail_id,c.gift,c.completed_qty,c.qty,c.row_index,i.request_qty,i.request_sub_qty,i.request_unit_qty,
        c.cost_price,c.cost_total,tbdb.batch_price,c.tax_rate,c.memo,c.source_vchtype,c.source_vchcode,c.source_business_type,c.source_detail_id, c.ptype_preferential_total,
        c.source_number as source_bill_number,pt.usercode,pt.fullname,pt.barcode,i.unit_qty,
        i.currency_price,i.currency_total,i.currency_dised_price,i.currency_dised_total,i.discount,i.currency_order_preferential_allot_total,
        i.currency_completed_preferential_share, i.currency_tax_total as currency_tax_total,
        c.refund_qty,c.refund_sub_qty,i.refund_order_preferential_allot_total,c.ptype_commission_total,
        i.currency_dised_taxed_price,i.currency_dised_taxed_total,tbdb.batchno,tbdb.produce_date,tbdb.expire_date,i.unit_rate,i.unit_id,bpu.unit_name,i.sub_unit_name,c.sub_qty,
        c.completed_sub_qty,
        bs.fullname as kfullname,bs.fullname as kfullname,bs.scategory,0 as stock_type,0 as stock_state,
        pt.protect_days,pt.propenabled, pt.snenabled,pt.batchenabled,pt.standard,pt.ptype_type,pt1.fullname as
        ptypeClass,bb.brand_name, c.sku_barcode fullbarcode,i.combo_share_scale AS comboShareScale,
        i.currency_order_fee_allot_total as currencyOrderFeeAllotTotal ,pt.pcategory,
        pt.ptype_length,pt.ptype_width,pt.ptype_height,pt.weight,pt.length_unit,pt.weight_unit,sku.pic_url as
        sku_pic_url,pt.sku_price,pt.cost_mode,c.cost_id,
        IFNULL(SUM(abid.qty),0) as
        inOutQty,pt.shortname,pt.ptype_area,i.service_start_time,i.service_end_time,i.service_expire_time,i.product_account_id,i.ptype_commission_rate,
        i.currency_give_preferential_total,c.give_preferential_total,
        c.ktype_point_id,c.ktype_point_type,
        tbddb.buyer_dised_taxed_price,tbddb.buyer_dised_taxed_total,tbde.ptype_standard,tbde.ptype_type as zyPtypeType,
        tbdc.combo_id, pt.typeid
        from acc_bill_detail_core_sale c
        LEFT JOIN acc_bill_detail_extend tbde on tbde.vchcode = c.vchcode and tbde.detail_id = c.detail_id and
        tbde.profile_id = #{profileId}
        left join acc_bill_detail_batch tbdb on tbdb.detail_id = c.detail_id and tbdb.vchcode = c.vchcode
        and tbdb.profile_id=#{profileId}
        left join acc_bill_detail_combo tbdc on tbdc.vchcode = c.vchcode and tbdc.id = c.combo_detail_id
        and tbdc.profile_id = #{profileId}
        left join acc_bill_detail_distribution_buyer tbddb on tbddb.detail_id = c.detail_id and tbddb.vchcode = c.vchcode
        and tbddb.profile_id=#{profileId}
        left join acc_bill_detail_assinfo_sale i on c.detail_id = i.detail_id and c.vchcode = i.vchcode and i.profile_id =
        #{profileId}
        left join base_ptype pt on c.ptype_id = pt.id and pt.profile_id = #{profileId}
        left join base_ptype pt1 on pt1.typeid = left(pt.typeid,5) and pt1.profile_id = #{profileId}
        left join base_ptype_pic bpp on pt.id = bpp.ptype_id and bpp.profile_id = #{profileId} AND bpp.rowindex = 1
        LEFT JOIN base_ptype_sku sku ON c.sku_id = sku.id AND c.ptype_id = sku.ptype_id AND sku.profile_id=#{profileId}
        left join base_brandtype bb on pt.brand_id = bb.id and bb.profile_id = #{profileId}
        LEFT JOIN base_ktype bs on c.ktype_id = bs.id and bs.profile_id = #{profileId}
        LEFT join base_ptype_unit bpu on i.unit_id = bpu.id and bpu.profile_id = #{profileId}
        LEFT JOIN acc_bill_inout_detail abid ON c.vchcode = abid.vchcode AND c.detail_id = abid.detail_id AND
        abid.ktype_point_type = 0 AND abid.inout_detail_type != 9 AND abid.profile_id=#{profileId}
        where c.profile_id = #{profileId} and c.combo_detail_id = 0
        <if test="vchcodes != null and vchcodes.size() > 0">
            and c.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        group by c.detail_id
        order by c.detail_id, c.row_index
    </select>


    <select id="getBillCoreList" resultMap="BaseResultMap" parameterType="Map">
        select
        <include refid="isNotOnlyVchcode"/>
        ,b.freight_btype_id as freightBtypeId,
        concat("直营") as shopType,
        spb.preferential_total as advanceGiftTotal,
        b.freight_billno as freightBillNo,
        b.currency_bill_total,b.distribution_etype_id,
        b.currency_ptype_preferential_total,
        tba2.atype_id as payAtypeId,tba2.pay_out_no as payOutNo,bpay.fullname as
        paywayFullname,tba2.id as payId, adm.id as markId, adm.mark_target, adm.show_type, adm.order_type,
        adm.mark_code, adm.mark_use_type,
        adm.create_type as markCreateType
        from td_bill_core a
        left join td_bill_assinfo b on a.vchcode = b.vchcode and b.profile_id = #{profileId}
        Left Join base_atype bat on bat.deleted = 0 and bat.typeid = '***************' and bat.profile_id = #{profileId}
        Left Join td_bill_account aba on aba.atype_id = bat.id and aba.vchcode = a.vchcode and aba.profile_id =
        #{profileId}
        Left Join ss_vip_card svc on svc.id = a.vip_card_id and svc.profile_id = #{profileId}
        left join base_customer bc on bc.id = svc.customer_id and bc.profile_id = #{profileId}
        left join base_btype_deliveryinfo bbd on bbd.deliverytype = 3 and bbd.btype_id = svc.customer_id and
        bbd.profile_id = #{profileId}
        left join pl_buyer bd on bd.buyer_id = bbd.delivery_id and bd.profile_id = #{profileId}
        LEFT JOIN pub_bill_print print ON a.vchcode = print.vchcode AND print.profile_id = #{profileId}
        left join base_ktype c on a.ktype_id = c.id and c.profile_id=#{profileId}
        left join base_ktype d on a.ktype_id2 = d.id and d.profile_id=#{profileId}
        left join base_btype i on a.btype_id = i.id and i.profile_id=#{profileId}
        left join base_etype f on a.etype_id = f.id and f.profile_id=#{profileId}
        left join base_etype g on a.create_etype_id = g.id and g.profile_id=#{profileId}
        left join base_etype se on b.store_manager_id = se.id and se.profile_id=#{profileId}
        left join base_dtype h on f.dtype_id = h.id and h.profile_id=#{profileId}
        left join base_otype o on o.id = a.otype_id and o.profile_id=#{profileId}
        left join acc_bill_balance_info m on a.vchcode = m.vchcode and m.profile_id=#{profileId}
        and m.balance_business_type = 0
        left join td_bill_audit tba on a.vchcode = tba.vchcode and tba.profile_id=#{profileId}
        and tba.obsolete_enabled = 0
        left join ss_preferential_bill spb on spb.vchcode = a.vchcode and spb.profile_id = #{profileId} and
        spb.preferential_type = 6
        left join td_bill_account tba2 on tba2.vchcode = a.vchcode and tba2.profile_id = #{profileId}
        left join base_payways bpay on bpay.id = tba2.payway_id and bpay.profile_id = #{profileId}
        left join base_btype balancebtype on a.pay_btype_id=balancebtype.id and
        balancebtype.profile_id=#{profileId}
        left join td_deliver_mark adm on adm.profile_id = #{profileId} and adm.vchcode = a.vchcode


        <if test="isEtypeLimited != null and isEtypeLimited == true">
            left join base_limit_scope bls on bls.profile_id=#{profileId} and bls.object_type=1 and
            a.etype_id=bls.object_id and bls.etype_id=#{employeeId}
        </if>
        <if test="isOtypeLimited!=null and isOtypeLimited == true">
            left join base_limit_scope blb on blb.profile_id=#{profileId} and blb.object_type=3 and
            a.otype_id=blb.object_id and blb.etype_id=#{employeeId}
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            left join base_limit_scope bbs on bbs.profile_id=#{profileId} and bbs.object_type=4 and
            a.btype_id=bbs.object_id and bbs.etype_id=#{employeeId}
        </if>
        <if test="isKtypeLimited!=null and isKtypeLimited == true">
            left join base_limit_scope blk on blk.profile_id=#{profileId} and blk.object_type=2 and
            a.ktype_id=blk.object_id and blk.etype_id=#{employeeId}
            left join base_limit_scope blk2 on blk2.profile_id=#{profileId} and blk2.object_type=2 and
            a.ktype_id2=blk2.object_id and blk2.etype_id=#{employeeId}
        </if>
        where a.profile_id =#{profileId} and a.order_sale_mode = 6 and a.deleted = 0 and a.process_type = 3
        <if test="payState != -1">
            and b.pay_state = #{payState}
        </if>
        <if test="createEtypeId != null">
            and a.create_etype_id like concat('%',#{createEtypeId},'%')
        </if>
        <choose>
            <when test="vchtypes != null and vchtypes.size() > 0">
                AND a.vchtype in
                <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        <choose>
            <when test="businessTypeList != null and businessTypeList.size() > 0">
                AND a.business_type in
                <foreach item="items" index="index" collection="businessTypeList" open="(" separator="," close=")">
                    #{items}
                </foreach>
            </when>
        </choose>

        <if test="billNumber != null">
            and a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
        </if>
        <if test="vipName != null">
            and bc.fullname like concat('%',#{vipName,jdbcType=VARCHAR},'%')
        </if>
        <if test="vipPhone != null">
            and (substring(bd.customer_receiver_phone, -4) = #{vipPhone,jdbcType=VARCHAR}
            <if test="encryptPhone != null and encryptPhone != ''">
                or bd.pi = #{encryptPhone,jdbcType=VARCHAR}
            </if>
            or bd.customer_receiver_phone = #{vipPhone,jdbcType=VARCHAR})
        </if>
        <if test="otypeIds != null and otypeIds.size() > 0">
            AND a.otype_id in
            <foreach item="item" index="index" collection="otypeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="btypeids != null and btypeids.size() > 0">
            AND a.btype_id in
            <foreach item="item" index="index" collection="btypeids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="paywayIds != null and paywayIds.size() > 0">
            AND tba2.payway_id in
            <foreach item="item" index="index" collection="paywayIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="billNumbers != null and billNumbers.size() > 0">
            and (
            <foreach collection="billNumbers" separator="or" item="billNumber">
                a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
            </foreach>
            )
        </if>
        <if test="btypeId != null">
            and a.btype_id =#{btypeId}
        </if>
        <if test="otypeId != null">
            and a.otype_id =#{otypeId}
        </if>
        <choose>
            <when test="number != null">
                and a.bill_number like concat('%',#{number,jdbcType=VARCHAR},'%')
            </when>
        </choose>
        <if test="etypeId != null and etypeId != 0">
            and a.etype_id =#{etypeId}
        </if>
        <if test="skuId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM td_bill_detail_core a WHERE a.profile_id =#{profileId}
            AND a.sku_id =#{skuId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <if test="comboId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM td_bill_detail_combo a WHERE a.profile_id =#{profileId}
            AND a.combo_id =#{comboId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <choose>
            <when test="saleOrbuy != null">
                <if test="ktypeId != null">
                    and (a.ktype_id =#{ktypeId} or a.ktype_id2 =#{ktypeId})
                </if>
            </when>
            <when test="ktypeIdList != null and ktypeIdList.size() > 0">
                AND (a.ktype_id in
                <foreach item="item" index="index" collection="ktypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
                a.ktype_id2 in
                <foreach item="item" index="index" collection="ktypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </when>
            <otherwise>
                <if test="ktypeId != null">
                    and a.ktype_id=#{ktypeId}
                </if>
                <if test="ktypeId2 != null">
                    and (a.ktype_id2=#{ktypeId2})
                </if>
                <if test="ktypeId3 != null">
                    AND a.ktype_id2=#{ktypeId3}
                </if>
            </otherwise>
        </choose>
        <if test="customTypeList != null and customTypeList.size() > 0">
            AND b.custom_type IN
            <foreach item="item1" index="index1" collection="customTypeList" open="(" separator="," close=")">
                #{item1}
            </foreach>
        </if>
        <if test="completedStateList != null and completedStateList.size() > 0">
            AND
            <foreach item="item2" index="index2" collection="completedStateList" open="(" separator="OR" close=")">
                <if test="item2 == 0">
                    b.ptype_completed_qty = 0
                </if>
                <if test="item2 == 1">
                    (b.ptype_completed_qty != 0 AND b.ptype_qty > b.ptype_completed_qty)
                </if>
                <if test="item2 == 2">
                    (b.ptype_completed_qty != 0 AND b.ptype_qty = b.ptype_completed_qty)
                </if>
            </foreach>
        </if>
        <if test="ptypeId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM td_bill_detail_core a WHERE a.profile_id =#{profileId}
            AND a.ptype_id =#{ptypeId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>


        <choose>
            <when test="postStateList != null">
                and ((
                <foreach collection="postStateList" index="index" item="item" open="(" close=")" separator="or">
                    <choose>
                        <when test="item == 0">
                            a.post_state = 0
                        </when>
                        <when test="item == 4">
                            a.post_state = 100 and tba.audit_state = 4
                        </when>
                        <when test="item == 10">
                            a.post_state = 500
                        </when>
                        <when test="item == 5">
                            a.post_state = 650
                        </when>
                        <when test="item == 6">
                            a.post_state = 550
                        </when>
                        <when test="item == 7">
                            a.post_state = 600
                        </when>
                        <when test="item == 8">
                            a.post_state = 700
                        </when>
                        <when test="item == 5">
                            a.post_state = 650
                        </when>
                    </choose>
                </foreach>
                <!--                    and (tba.audit_state = 0 or tba.audit_state = 4 or tba.audit_state is null)) or ((a.post_state <![CDATA[<=]]> 500) and-->
                <!--                tba.audit_state in-->
                <!--                <foreach item="item" index="index" collection="auditList" open="(" separator="," close=")">-->
                <!--                    #{item}-->
                <!--                </foreach>-->
                ))
            </when>
            <otherwise>
                <if test="postStateList != null">
                    and a.post_state = 0 and (tba.audit_state = 0 or tba.audit_state is null)
                </if>
                <if test="auditList != null">
                    and tba.audit_state in
                    <foreach item="item" index="index" collection="auditList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>

        <if test="beginDate != null and endDate != null">
            and a.bill_date between #{beginDate} and #{endDate}
        </if>
        <if test="MinTotal != null">
            and a.currency_bill_total <![CDATA[ >= ]]> #{MinTotal}
        </if>
        <if test="MaxTotal != null">
            and a.currency_bill_total <![CDATA[ <= ]]> #{MaxTotal}
        </if>
        <if test="processType != null">
            and a.process_type = #{processType}
        </if>
        <if test="showBackBill != null  and showBackBill == true">
            and (a.process_type!=1 or (a.process_type=1 and (a.create_type=4 or a.create_type=5)))
        </if>

        <if test="isEtypeLimited != null and isEtypeLimited == true">
            and (bls.id is not null or a.etype_id = 0)
        </if>
        <if test="isOtypeLimited!=null and isOtypeLimited ==true">
            and (blb.id is not null or a.otype_id=0)
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            and (bbs.id is not null or a.btype_id=0)
        </if>
        <if test="isKtypeLimited!=null and isKtypeLimited ==true">
            AND IF((a.vchtype = 3000),
            ( blk.object_id IS NOT NULL OR
            blk2.object_id IS NOT NULL
            ),
            (
            (blk.id IS NOT NULL OR a.ktype_id=0)
            AND (blk2.id IS NOT NULL OR a.ktype_id2=0))
            )
        </if>
        <if test="freightBillNo != null and freightBillNo != ''">
            and b.freight_billno like concat('%',#{freightBillNo,jdbcType=VARCHAR},'%')
        </if>
        <if test="freightBtypeId != null and freightBtypeId != 0">
            and b.freight_btype_id = #{freightBtypeId}
        </if>
        <include refid="query-filter">
            <property name="_list" value="gridFilter"/>
        </include>
        group by a.vchcode
        <choose>
            <when test="orderColumn != null and orderColumn != ''">
                order by ${orderColumn}
            </when>
            <otherwise>
                order by a.bill_date DESC,a.update_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="getBillCoreListBySale" resultMap="BaseResultMap" parameterType="Map">
        select
        <include refid="isNotOnlyVchcode"/>
        ,b.freight_btype_id as freightBtypeId,
        concat('直营') as shopType,
        spb.preferential_total as advanceGiftTotal,
        b.freight_billno as freightBillNo,
        b.currency_bill_total,
        b.currency_ptype_preferential_total,
        tba2.atype_id as payAtypeId,tba2.pay_out_no as payOutNo,bpay.fullname as
        paywayFullname,tba2.id as payId, adm.id as markId, adm.mark_target, adm.show_type, adm.order_type,
        adm.mark_code, adm.mark_use_type,
        adm.create_type as markCreateType
        from acc_bill_core a
        Left Join base_atype bat on bat.deleted = 0 and bat.typeid = '***************' and bat.profile_id = #{profileId}
        Left Join acc_bill_account aba on aba.atype_id = bat.id and aba.vchcode = a.vchcode and aba.profile_id =
        #{profileId}
        Left Join ss_vip_card svc on svc.id = a.vip_card_id and svc.profile_id = #{profileId}
        left join base_customer bc on bc.id = svc.customer_id and bc.profile_id = #{profileId}
        left join base_btype_deliveryinfo bbd on bbd.deliverytype = 3 and bbd.btype_id = bc.id and bbd.profile_id =
        #{profileId}
        left join pl_buyer bd on bd.buyer_id = bbd.delivery_id and bd.profile_id = #{profileId}
        left join acc_bill_assinfo b on a.vchcode = b.vchcode and b.profile_id = #{profileId}
        LEFT JOIN td_bill_audit tba ON b.vchcode = tba.vchcode AND tba.profile_id =#{profileId} AND tba.obsolete_enabled
        = 0
        left join pub_bill_print print on a.vchcode = print.vchcode and print.profile_id = #{profileId}
        left join acc_deliver_freight_info adfi on adfi.vchcode = a.vchcode and adfi.profile_id = #{profileId} and
        adfi.original = 1
        left join base_ktype c on a.ktype_id = c.id and c.profile_id=#{profileId}
        left join base_ktype d on a.ktype_id2 = d.id and d.profile_id=#{profileId}
        left join base_btype i on a.btype_id = i.id and i.profile_id=#{profileId}
        left join base_etype f on a.etype_id = f.id and f.profile_id=#{profileId}
        left join base_etype g on a.create_etype_id = g.id and g.profile_id=#{profileId}
        left join base_etype se on b.store_manager_id = se.id and se.profile_id=#{profileId}
        left join base_dtype h on f.dtype_id = h.id and h.profile_id=#{profileId}
        left join base_otype o on o.id = a.otype_id and o.profile_id=#{profileId}
        left join acc_bill_balance_info m on a.vchcode = m.vchcode and m.profile_id=#{profileId} and
        m.balance_business_type = 0
        left join ss_preferential_bill spb on spb.vchcode = a.vchcode and spb.profile_id = #{profileId} and
        spb.preferential_type = 6
        left join acc_bill_account tba2 on tba2.vchcode = a.vchcode and tba2.profile_id = #{profileId}
        left join base_payways bpay on bpay.id = tba2.payway_id and bpay.profile_id = #{profileId}
        left join base_btype balancebtype on a.pay_btype_id=balancebtype.id and
        balancebtype.profile_id=#{profileId}
        left join acc_deliver_mark adm on adm.profile_id = #{profileId} and adm.vchcode = a.vchcode

        <if test="isEtypeLimited!=null and isEtypeLimited == true">
            left join base_limit_scope bls on bls.profile_id=#{profileId} and bls.object_type=1 and
            a.etype_id=bls.object_id and bls.etype_id=#{employeeId}
        </if>
        <if test="isOtypeLimited!=null and isOtypeLimited ==true">
            left join base_limit_scope blb on blb.profile_id=#{profileId} and blb.object_type=3 and
            a.otype_id=blb.object_id and blb.etype_id=#{employeeId}
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            left join base_limit_scope bbs on bbs.profile_id=#{profileId} and bbs.object_type=4 and
            a.btype_id=bbs.object_id and bbs.etype_id=#{employeeId}
        </if>
        <if test="isKtypeLimited!=null and isKtypeLimited == true">
            left join base_limit_scope blk on blk.profile_id=#{profileId} and blk.object_type=2 and
            a.ktype_id=blk.object_id and blk.etype_id=#{employeeId}
            left join base_limit_scope blk2 on blk2.profile_id=#{profileId} and blk2.object_type=2 and
            a.ktype_id2=blk2.object_id and blk2.etype_id=#{employeeId}
        </if>
        where a.profile_id =#{profileId} AND a.post_state=800 and a.order_sale_mode = 6 and a.process_type = 3
        and a.deleted = 0
        <if test="payState != -1">
            and b.pay_state = #{payState}
        </if>

        <if test="createEtypeId != null">
            and a.create_etype_id like concat('%',#{createEtypeId},'%')
        </if>
        <choose>
            <when test="vchtypes != null and vchtypes.size() > 0">
                AND a.vchtype in
                <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="businessTypeList != null and businessTypeList.size() > 0">
                AND a.business_type in
                <foreach item="items" index="index" collection="businessTypeList" open="(" separator="," close=")">
                    #{items}
                </foreach>
            </when>
        </choose>
        <choose>
            <when test="number != null">
                and a.bill_number like concat('%',#{number,jdbcType=VARCHAR},'%')
            </when>
        </choose>
        <if test="otypeIds != null and otypeIds.size() > 0">
            AND a.otype_id in
            <foreach item="item" index="index" collection="otypeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="btypeids != null and btypeids.size() > 0">
            AND a.btype_id in
            <foreach item="item" index="index" collection="btypeids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="paywayIds != null and paywayIds.size() > 0">
            AND tba2.payway_id in
            <foreach item="item" index="index" collection="paywayIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


        <if test="btypeId != null">
            and a.btype_id =#{btypeId}
        </if>
        <if test="etypeId != null and etypeId != 0">
            and a.etype_id =#{etypeId}
        </if>
        <if test="billNumber != null">
            and a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
        </if>
        <if test="billNumbers != null and billNumbers.size() > 0">
            and (
            <foreach collection="billNumbers" separator="or" item="billNumber">
                a.bill_number like concat('%',#{billNumber,jdbcType=VARCHAR},'%')
            </foreach>
            )
        </if>
        <if test="vipName != null">
            and bc.fullname like concat('%',#{vipName,jdbcType=VARCHAR},'%')
        </if>
        <if test="vipPhone != null">
            and (substring(bd.customer_receiver_phone, -4) = #{vipPhone,jdbcType=VARCHAR}
            <if test="encryptPhone != null and encryptPhone != ''">
                or bd.pi = #{encryptPhone,jdbcType=VARCHAR}
            </if>
            or bd.customer_receiver_phone = #{vipPhone,jdbcType=VARCHAR})
        </if>
        <if test="otypeId != null">
            and a.otype_id =#{otypeId}
        </if>
        <choose>
            <when test="saleOrbuy != null">
                <if test="ktypeId != null">
                    and (a.ktype_id =#{ktypeId} or a.ktype_id2 =#{ktypeId})
                </if>
            </when>
            <when test="ktypeIdList != null and ktypeIdList.size() > 0">
                AND (a.ktype_id in
                <foreach item="item" index="index" collection="ktypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
                a.ktype_id2 in
                <foreach item="item" index="index" collection="ktypeIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </when>
            <otherwise>
                <if test="ktypeId != null">
                    and a.ktype_id =#{ktypeId}
                </if>
                <if test="ktypeId2 != null">
                    and a.ktype_id2 =#{ktypeId2}
                </if>
            </otherwise>
        </choose>
        <if test="skuId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM acc_bill_detail_core_sale a WHERE a.profile_id =#{profileId}
            AND a.sku_id =#{skuId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <if test="comboId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM acc_bill_detail_combo a WHERE a.profile_id =#{profileId}
            AND a.combo_id =#{comboId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <if test="ptypeId != null">
            and a.vchcode in (
            SELECT a.vchcode FROM acc_bill_detail_core_sale a WHERE a.profile_id =#{profileId}
            AND a.ptype_id =#{ptypeId}
            <choose>
                <when test="vchtypes != null and vchtypes.size() > 0">
                    AND a.vchtype in
                    <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
            </choose>
            <if test="beginDate != null and endDate != null">
                and a.bill_date between #{beginDate} and #{endDate}
            </if>
            GROUP BY a.vchcode)
        </if>
        <if test="beginDate != null and endDate != null">
            and a.bill_date between #{beginDate} and #{endDate}
        </if>

        <if test="isEtypeLimited != null and isEtypeLimited == true">
            and (bls.id is not null or a.etype_id = 0)
        </if>
        <if test="isOtypeLimited!=null and isOtypeLimited == true">
            and (blb.id is not null or a.otype_id=0)
        </if>
        <if test="isBtypeLimited!=null and isBtypeLimited == true">
            and (bbs.id is not null or a.btype_id=0)
        </if>
        <if test="isKtypeLimited!=null and isKtypeLimited ==true">
            AND IF((a.vchtype = 3000),
            ( blk.object_id IS NOT NULL OR
            blk2.object_id IS NOT NULL
            ),
            (
            (blk.id IS NOT NULL OR a.ktype_id=0)
            AND (blk2.id IS NOT NULL OR a.ktype_id2=0))
            )
        </if>

        <include refid="query-filter">
            <property name="_list" value="gridFilter"/>
        </include>

        group by a.vchcode
        <choose>
            <when test="orderColumn != null and orderColumn != ''">
                order by ${orderColumn}
            </when>
            <otherwise>
                ORDER BY a.bill_date DESC,a.update_time DESC
            </otherwise>
        </choose>
    </select>

    <sql id="query-filter">
        <if test="${_list} != null">
            <trim prefix=" and (" suffix=")">
                <foreach collection="${_list}" item="item" separator=" and ">
                    <choose>
                        <when test="item.magicQuery != null and item.magicQuery.size() != 0">
                            <trim prefix="(" suffix=")">
                                <foreach collection="item.magicQuery" item="magic_item" separator="or">
                                    ${magic_item.dataField} like concat('%',#{magic_item.value},'%')
                                </foreach>
                            </trim>
                        </when>
                        <when test="item.type == 0">
                            ${item.dataField} like concat('%',#{item.value},'%')
                        </when>
                        <when test="item.type == 1">
                            <trim prefixOverrides="and" prefix="(" suffix=")">
                                <if test="item.value1 != null">
                                    ${item.dataField} >= #{item.value1}
                                </if>
                                <if test="item.value2 != null">
                                    and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                                </if>
                            </trim>
                        </when>
                        <when test="item.type == 2">
                            ${item.dataField} = #{item.value}
                        </when>
                        <when test="item.type == 3">
                            ${item.dataField}
                        </when>
                        <when test="item.type == 4">
                            <trim prefixOverrides="and" prefix="(" suffix=")">
                                <if test="item.value1 != null">
                                    ${item.dataField} >= #{item.value1}
                                </if>
                                <if test="item.value2 != null">
                                    and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                                </if>
                            </trim>
                            <if test="item.value1 != null and item.value1 == 0">
                                or ${item.dataField} is null
                            </if>
                        </when>
                        <when test="item.type == 5">
                            tba2.pay_out_no like concat('%',#{item.value},'%') or bpay.fullname like
                            concat('%',#{item.value},'%')
                        </when>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>

    <resultMap id="billCoreALLDTO" type="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.BillCoreALLDTO">
        <id column="vchcode" property="vchcode" jdbcType="BIGINT"/>
        <result column="billDate" property="billDate" jdbcType="DATE"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="BIGINT"/>
        <result column="billTotal" property="billTotal" jdbcType="DECIMAL"/>
        <result column="bill_number" property="billNumber" jdbcType="VARCHAR"/>
        <result column="vchtype" property="vchtype" jdbcType="INTEGER"/>
        <result column="create_etype_id" property="createEtypeId" jdbcType="BIGINT"/>
        <result column="createEfullname" property="createEfullname" jdbcType="VARCHAR"/>
        <result column="post_state" property="postState" jdbcType="INTEGER"/>
        <result column="ktype_id" property="ktypeId" jdbcType="BIGINT"/>
        <result column="ktype_id2" property="ktypeId2" jdbcType="BIGINT"/>
        <result column="vip_name" property="vipName" jdbcType="VARCHAR"/>
        <result column="vip_phone" property="vipPhone" jdbcType="VARCHAR"/>
        <result column="buyer_id" property="buyerId" jdbcType="BIGINT"/>
        <collection property="mark"
                    ofType="com.wsgjp.ct.common.enums.core.entity.DeliverNewMark">
            <id column="markId" property="id"/>
            <result column="mark_target" property="markTarget"/>
            <result column="show_type" property="showType"/>
            <result column="order_type" property="orderType"/>
            <result column="mark_code" property="markCode"/>
            <result column="mark_use_type" property="markUseType"/>
            <result column="create_type" property="createType"/>
        </collection>
    </resultMap>

    <select id="getAllBillList" resultMap="billCoreALLDTO">
        select distinct abc.memo, abc.vchcode, abc.bill_date as billDate, abc.business_type,
        (abc.bill_total + IFNULL(spb.preferential_total, 0)) as billTotal,
        abc.bill_number, abc.vchtype, abc.create_etype_id, be.fullname as createEfullname,
        abc.post_state,abc.ktype_id,abc.ktype_id2,bc.fullname as vip_name,bd.customer_receiver_phone as
        vip_phone,bd.buyer_id,
        adm.id as markId, adm.mark_target, adm.show_type, adm.order_type, adm.mark_code, adm.mark_use_type,
        adm.create_type
        from acc_bill_core abc
        left join acc_bill_assinfo aba2 on aba2.vchcode = abc.vchcode and aba2.profile_id = #{profileId}
        LEFT JOIN base_etype be on be.id = abc.create_etype_id and be.profile_id = #{profileId}
        LEFT JOIN ss_preferential_bill spb on spb.profile_id = #{profileId} and spb.vchcode = abc.vchcode and
        spb.preferential_type = 6
        Left Join ss_vip_card svc on svc.profile_id = #{profileId} and svc.id = abc.vip_card_id
        left join base_customer bc on bc.profile_id = #{profileId} and bc.id = svc.customer_id
        left join base_btype_deliveryinfo bbd on bbd.profile_id = #{profileId} and bbd.deliverytype = 3 and bbd.btype_id
        = bc.id
        left join pl_buyer bd on bd.profile_id = #{profileId} and bd.buyer_id = bbd.delivery_id
        left join acc_bill_account aba on aba.profile_id = #{profileId} and aba.vchcode = abc.vchcode
        left join acc_bill_detail_core_sale abdcs on abdcs.profile_id = #{profileId} and abdcs.vchcode = abc.vchcode
        left join acc_bill_detail_combo abdc on abdc.profile_id = #{profileId} and abdc.vchcode = abc.vchcode
        <if test=" unitId != null and unitId != ''">
            left join acc_bill_detail_assinfo_sale abdas on abdas.profile_id = #{profileId} and abdas.vchcode =
            abc.vchcode and abdas.detail_id =abdcs.detail_id
        </if>

        left join acc_deliver_mark adm on adm.profile_id = #{profileId} and adm.vchcode = abc.vchcode
        where abc.profile_id = #{profileId} and abc.deleted = 0
        <if test="payState != null and payState != ''">
            AND aba2.pay_state = #{payState}
        </if>
        <if test=" goodsId != null and goodsId != ''">
            AND (abdcs.sku_id = #{goodsId} or abdc.combo_id = #{goodsId})
        </if>
        <if test=" unitId != null and unitId != ''">
            AND (abdas.unit_id = #{unitId})
        </if>

        <if test="otypeId != null and otypeId!=''">
            AND abc.otype_id = #{otypeId}
            <if test="cashierId != null and cashierId != ''">
                AND svb.cashier_id = #{cashierId}
            </if>
        </if>
        AND abc.bill_date between #{startTime} AND #{endTime}
        <if test="orderSaleModeList != null and orderSaleModeList.size()>0">
            and abc.order_sale_mode in
            <foreach collection="orderSaleModeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and abc.process_type = 3 and abc.business_type = 201
        </if>
        <if test="postStateList != null and postStateList.size()>0">
            and abc.post_state in
            <foreach collection="postStateList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="billNumber != null and billNumber!='' ">
            AND abc.bill_number like concat('%',#{billNumber},'%')
        </if>
        <if test="phone != null and phone != ''">
            and (bd.customer_receiver_phone = #{phone}
            <if test="encryptPhone != null and encryptPhone != ''">
                or bd.pi = #{encryptPhone}
            </if>
            or substring(bd.customer_receiver_phone, -4) = #{phone})
        </if>
        <if test="payOutNo != null and payOutNo != ''">
            AND aba.pay_out_no like concat('%',#{payOutNo},'%')
        </if>
        <choose>
            <when test="vchtypes != null and vchtypes.size() > 0">
                AND abc.vchtype in
                <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND abc.vchtype in (2000,2100)
            </otherwise>
        </choose>
        <if test="ktypeId != null and ktypeId !=''">
            and abc.ktype_id = #{ktypeId}
        </if>
        <if test="ktypeId2 != null and ktypeId2 !=''">
            and abc.ktype_id2 = #{ktypeId2}
        </if>
        <if test="excludeBackCompletedBill == true">
            and aba2.ptype_completed_qty &lt; aba2.ptype_qty
        </if>
        UNION
        SELECT distinct tbc.memo, tbc.vchcode, tbc.bill_date as billDate, tbc.business_type,
        (tbc.bill_total + IFNULL(spb.preferential_total, 0)) as billTotal,
        tbc.bill_number, tbc.vchtype, tbc.create_etype_id, be.fullname as createEfullname,
        tbc.post_state,tbc.ktype_id,tbc.ktype_id2,bc.fullname as vip_name,bd.customer_receiver_phone as
        vip_phone,bd.buyer_id,
        tdm.id as markId, tdm.mark_target, tdm.show_type, tdm.order_type, tdm.mark_code, tdm.mark_use_type,
        tdm.create_type
        from td_bill_core tbc
        left join td_bill_assinfo aba2 on aba2.vchcode = tbc.vchcode and aba2.profile_id = #{profileId}
        LEFT JOIN base_etype be on be.id = tbc.create_etype_id and be.profile_id = #{profileId}
        LEFT JOIN ss_preferential_bill spb on spb.profile_id = #{profileId} and spb.vchcode = tbc.vchcode and
        spb.preferential_type = 6
        Left Join ss_vip_card svc on svc.profile_id = #{profileId} and svc.id = tbc.vip_card_id
        left join base_customer bc on bc.profile_id = #{profileId} and bc.id = svc.customer_id
        left join base_btype_deliveryinfo bbd on bbd.profile_id = #{profileId} and bbd.deliverytype = 3 and bbd.btype_id
        = bc.id
        left join pl_buyer bd on bd.profile_id = #{profileId} and bd.buyer_id = bbd.delivery_id
        left join td_bill_account aba on aba.profile_id = #{profileId} and aba.vchcode = tbc.vchcode
        left join td_bill_detail_core tbdc on tbdc.profile_id = #{profileId} and tbdc.vchcode = tbc.vchcode
        left join td_bill_detail_combo tbdcc on tbdcc.profile_id = #{profileId} and tbdcc.vchcode = tbc.vchcode
        <if test=" unitId != null and unitId != ''">
            left join td_bill_detail_assinfo abdas on abdas.profile_id = #{profileId} and abdas.vchcode =
            tbc.vchcode and abdas.detail_id =tbdc.detail_id
        </if>

        left join td_deliver_mark tdm on tdm.profile_id = #{profileId} and tdm.vchcode = tbc.vchcode
        where tbc.profile_id = #{profileId} and tbc.deleted = 0
        <if test="payState != null and payState != ''">
            AND aba2.pay_state = #{payState}
        </if>
        <if test=" goodsId != null and goodsId != ''">
            AND (tbdc.sku_id = #{goodsId} or tbdcc.combo_id = #{goodsId})
        </if>
        <if test=" unitId != null and unitId != ''">
            AND (abdas.unit_id = #{unitId})
        </if>
        <if test="otypeId != null and otypeId!=''">
            AND tbc.otype_id = #{otypeId}
            <if test="cashierId != null and cashierId != ''">
                AND svb.cashier_id = #{cashierId}
            </if>
        </if>
        AND tbc.bill_date between #{startTime} AND #{endTime}
        <if test="orderSaleModeList != null and orderSaleModeList.size()>0">
            and tbc.order_sale_mode in
            <foreach collection="orderSaleModeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and tbc.process_type = 3 and tbc.business_type = 201
        </if>
        <if test="postStateList != null and postStateList.size()>0">
            and tbc.post_state in
            <foreach collection="postStateList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="billNumber != null and billNumber!='' ">
            AND tbc.bill_number like concat('%',#{billNumber},'%')
        </if>
        <if test="phone != null and phone != ''">
            and (bd.pi = #{phone}
            or substring(bd.customer_receiver_phone, -4) = #{phone})
        </if>
        <if test="payOutNo != null and payOutNo != ''">
            AND aba.pay_out_no like concat('%',#{payOutNo},'%')
        </if>
        <choose>
            <when test="vchtypes != null and vchtypes.size() > 0">
                AND tbc.vchtype in
                <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND tbc.vchtype in (2000,2100)
            </otherwise>
        </choose>
        <if test="ktypeId != null and ktypeId !=''">
            and tbc.ktype_id = #{ktypeId}
        </if>
        <if test="ktypeId2 != null and ktypeId2 !=''">
            and tbc.ktype_id2 = #{ktypeId2}
        </if>
        <if test="excludeBackCompletedBill == true">
            and aba2.ptype_completed_qty &lt; aba2.ptype_qty
        </if>
        order by billDate desc
    </select>

    <!-- 获取总记录数 -->
    <select id="getAllBillList_COUNT" resultType="java.lang.Integer">
        select count(1) from (
        select abc.vchcode
        from acc_bill_core abc
        left join acc_bill_assinfo aba2 on aba2.vchcode = abc.vchcode and aba2.profile_id = #{profileId}
        LEFT JOIN ss_preferential_bill spb on spb.profile_id = #{profileId} and spb.vchcode = abc.vchcode and
        spb.preferential_type = 6
        Left Join ss_vip_card svc on svc.profile_id = #{profileId} and svc.id = abc.vip_card_id
        left join base_customer bc on bc.profile_id = #{profileId} and bc.id = svc.customer_id
        left join base_btype_deliveryinfo bbd on bbd.profile_id = #{profileId} and bbd.deliverytype = 3 and bbd.btype_id
        = bc.id
        left join pl_buyer bd on bd.profile_id = #{profileId} and bd.buyer_id = bbd.delivery_id
        left join acc_bill_account aba on aba.profile_id = #{profileId} and aba.vchcode = abc.vchcode
        left join acc_bill_detail_core_sale abdcs on abdcs.profile_id = #{profileId} and abdcs.vchcode = abc.vchcode
        left join acc_bill_detail_combo abdc on abdc.profile_id = #{profileId} and abdc.vchcode = abc.vchcode
        left join ss_vip_bill svb on svb.profile_id = #{profileId} and svb.vchcode = abc.vchcode
        where abc.profile_id = #{profileId} and abc.deleted = 0
        <if test="payState != null and payState != ''">
            AND aba2.pay_state = #{payState}
        </if>
        <if test=" goodsId != null and goodsId != ''">
            AND (abdcs.sku_id = #{goodsId} or abdc.combo_id = #{goodsId})
        </if>
        <if test="otypeId != null and otypeId!=''">
            AND abc.otype_id = #{otypeId}
            <if test="cashierId != null and cashierId != ''">
                AND svb.cashier_id = #{cashierId}
            </if>
        </if>
        AND abc.bill_date between #{startTime} AND #{endTime}
        <if test="orderSaleModeList != null and orderSaleModeList.size()>0">
            and abc.order_sale_mode in
            <foreach collection="orderSaleModeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and abc.process_type = 3 and abc.business_type = 201
        </if>
        <if test="postStateList != null and postStateList.size()>0">
            and abc.post_state in
            <foreach collection="postStateList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="billNumber != null and billNumber!='' ">
            AND abc.bill_number like concat('%',#{billNumber},'%')
        </if>
        <if test="phone != null and phone != ''">
            and (bd.customer_receiver_phone = #{phone}
            <if test="encryptPhone != null and encryptPhone != ''">
                or bd.pi = #{encryptPhone}
            </if>
            or substring(bd.customer_receiver_phone, -4) = #{phone})
        </if>
        <if test="payOutNo != null and payOutNo != ''">
            AND aba.pay_out_no like concat('%',#{payOutNo},'%')
        </if>
        <choose>
            <when test="vchtypes != null and vchtypes.size() > 0">
                AND abc.vchtype in
                <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND abc.vchtype in (2000,2100)
            </otherwise>
        </choose>
        <if test="ktypeId != null and ktypeId !=''">
            and abc.ktype_id = #{ktypeId}
        </if>
        <if test="ktypeId2 != null and ktypeId2 !=''">
            and abc.ktype_id2 = #{ktypeId2}
        </if>
        <if test="excludeBackCompletedBill == true">
            and aba2.ptype_completed_qty &lt; aba2.ptype_qty
        </if>

        UNION

        SELECT tbc.vchcode
        from td_bill_core tbc
        left join td_bill_assinfo aba2 on aba2.vchcode = tbc.vchcode and aba2.profile_id = #{profileId}
        LEFT JOIN ss_preferential_bill spb on spb.profile_id = #{profileId} and spb.vchcode = tbc.vchcode and
        spb.preferential_type = 6
        Left Join ss_vip_card svc on svc.profile_id = #{profileId} and svc.id = tbc.vip_card_id
        left join base_customer bc on bc.profile_id = #{profileId} and bc.id = svc.customer_id
        left join base_btype_deliveryinfo bbd on bbd.profile_id = #{profileId} and bbd.deliverytype = 3 and bbd.btype_id
        = bc.id
        left join pl_buyer bd on bd.profile_id = #{profileId} and bd.buyer_id = bbd.delivery_id
        left join td_bill_account aba on aba.profile_id = #{profileId} and aba.vchcode = tbc.vchcode
        left join td_bill_detail_core tbdc on tbdc.profile_id = #{profileId} and tbdc.vchcode = tbc.vchcode
        left join td_bill_detail_combo tbdcc on tbdcc.profile_id = #{profileId} and tbdcc.vchcode = tbc.vchcode
        left join ss_vip_bill svb on svb.profile_id = #{profileId} and svb.vchcode = tbc.vchcode
        where tbc.profile_id = #{profileId} and tbc.deleted = 0
        <if test="payState != null and payState != ''">
            AND aba2.pay_state = #{payState}
        </if>
        <if test=" goodsId != null and goodsId != ''">
            AND (tbdc.sku_id = #{goodsId} or tbdcc.combo_id = #{goodsId})
        </if>
        <if test="otypeId != null and otypeId!=''">
            AND tbc.otype_id = #{otypeId}
            <if test="cashierId != null and cashierId != ''">
                AND svb.cashier_id = #{cashierId}
            </if>
        </if>
        AND tbc.bill_date between #{startTime} AND #{endTime}
        <if test="orderSaleModeList != null and orderSaleModeList.size()>0">
            and tbc.order_sale_mode in
            <foreach collection="orderSaleModeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and tbc.process_type = 3 and tbc.business_type = 201
        </if>
        <if test="postStateList != null and postStateList.size()>0">
            and tbc.post_state in
            <foreach collection="postStateList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="billNumber != null and billNumber!='' ">
            AND tbc.bill_number like concat('%',#{billNumber},'%')
        </if>
        <if test="phone != null and phone != ''">
            and (bd.pi = #{phone}
            or substring(bd.customer_receiver_phone, -4) = #{phone})
        </if>
        <if test="payOutNo != null and payOutNo != ''">
            AND aba.pay_out_no like concat('%',#{payOutNo},'%')
        </if>
        <choose>
            <when test="vchtypes != null and vchtypes.size() > 0">
                AND tbc.vchtype in
                <foreach item="item" index="index" collection="vchtypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND tbc.vchtype in (2000,2100)
            </otherwise>
        </choose>
        <if test="ktypeId != null and ktypeId !=''">
            and tbc.ktype_id = #{ktypeId}
        </if>
        <if test="ktypeId2 != null and ktypeId2 !=''">
            and tbc.ktype_id2 = #{ktypeId2}
        </if>
        <if test="excludeBackCompletedBill == true">
            and aba2.ptype_completed_qty &lt; aba2.ptype_qty
        </if>
        ) t
    </select>

    <!--调拨订单查询-->
    <select id="getTransferOrderList"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.TransferOrderDTO">
        select distinct(toc.vchcode) as vchcodeDistinct,toc.*,
        tbr.target_vchcode as relation_vchcode,
        tbc.post_state as relation_state
        from td_orderbill_core toc
        left join td_bill_relation tbr on tbr.profile_id = #{profileId} and tbr.source_vchcode = toc.vchcode
        left join td_bill_core tbc on tbc.profile_id = #{profileId} and tbc.vchcode = tbr.target_vchcode
        where toc.profile_id = #{profileId}
        and toc.deleted = 0
        and ifnull(toc.orderover_state,2) != 2
        <!--单据类型为调拨订单-->
        and toc.vchtype = 9005
        <if test="startTime !=null and endTime!=null">
            and toc.bill_date between #{startTime} and #{endTime}
        </if>
        <if test="billNumber != null and billNumber!='' ">
            and toc.bill_number like concat('%',#{billNumber},'%')
        </if>
        <if test="ktypeId != null and ktypeId !=''">
            <!--发货仓库不为空，说明是发货方，发货方无需查询状态为草稿的调拨订单，（无法进行操作）-->
            and toc.ktype_id = #{ktypeId} and toc.post_state in (500,600)
            <!--在pos新增调拨订单时，可以选择向总部要货，也就是说，并不知道发货仓库。理论上这个订单的发货仓库id应该为空，
                但是目前新增订单必须填收发货仓库，所以只能收发货仓库都填本地的仓库。对于这种特殊的单据，是不能发货的。
                所以当作为发货方角色查询单据时，这种单子要排除
            -->
            and toc.ktype_id != toc.ktype_id2
        </if>
        <if test="ktypeId2 != null and ktypeId2 !=''">
            and toc.ktype_id2 = #{ktypeId2}
        </if>
        <!--这里 单据已确认=500|600 ，未确认=草稿=0 -->
        <if test="postStateList!=null and postStateList.size()>0">
            and toc.post_state in
            <foreach collection="postStateList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--根据关联的调拨单以及调拨单的发货状态来获取调拨订单的发货状态，
            若发货：则有调拨单， 且调拨单已发货
            若未发货：1.未关联调拨单
                    2.或有调拨单，但是调拨单为草稿
            deliveryState: 0 未发货 1 已发货 其他情况 全部
        -->
        <if test="deliveryState==0 or deliveryState==1">
            <!-- and #{deliveryState} = if(toc.orderover_state = 1,1,if(tbc.post_state = 0,0,1)) -->
            and (toc.orderover_state = 1 or ifnull(tbc.post_state,0) &gt; 0) = #{deliveryState}
        </if>
        order by toc.bill_date desc
    </select>
    <select id="getVchcodeListByBillNumber" resultType="com.wsgjp.ct.sale.biz.record.dto.request.DeleteBillRequest">
        select vchcode, vchtype, bill_date, business_type, post_state as billPostState
        from acc_bill_core abc
        where profile_id = #{profileId}
          and deleted = 0
          and bill_number = #{billNumber}
        union
        select vchcode, vchtype, bill_date, business_type, post_state
        from td_bill_core tbc
        where profile_id = #{profileId}
          and deleted = 0
          and bill_number = #{billNumber}
    </select>

    <select id="getRelationVchcodeList" resultType="com.wsgjp.ct.sale.biz.record.dto.request.DeleteBillRequest">
        select abc.vchcode, abc.vchtype, abc.bill_date, abc.business_type, abc.post_state as billPostState
        from td_bill_relation tbr
        join acc_bill_core abc on abc.vchcode =tbr .target_vchcode
        where tbr.profile_id = #{profileId} and tbr.source_vchcode in
        <foreach collection="vchcodes" item="item" open="(" separator="," close=")">
            #{item.vchcode}
        </foreach>
        union
        select tbc.vchcode, tbc.vchtype, tbc.bill_date, tbc.business_type, tbc.post_state as billPostState
        from td_bill_relation tbr
        join td_bill_core tbc on tbc.vchcode =tbr .target_vchcode
        where tbr.profile_id = #{profileId} and tbr.source_vchcode in
        <foreach collection="vchcodes" item="item" open="(" separator="," close=")">
            #{item.vchcode}
        </foreach>
    </select>

    <select id="getAccBillDetailComboList" resultType="com.wsgjp.ct.sale.biz.bill.model.dao.BillComboDAO">
        SELECT c.id,
               c.profile_id,
               c.vchcode,
               c.combo_id,
               c.inout_type,
               pt.usercode,
               pt.fullname                                      pfullname,
               pt.shortname,
               pt.barcode,
               pt.barcode                                       fullbarcode,
               pt.weight,
               pt.weight_unit                              as   weightUnit,
               pt.sku_price                                as   skuPrice,
               pt.pcategory,
               pt.standard,
               pt.ptype_type,
               abs(c.qty)                                       qty,
               abs(asi.currency_price)                          currency_price,
               abs(asi.currency_total)                          currency_total,
               c.gift,
               abs(asi.currency_dised_price)                    currency_dised_price,
               abs(asi.currency_dised_total)                    currency_dised_total
                ,
               abs(asi.currency_tax_total)                      currency_tax_total,
               abs(asi.currency_dised_taxed_price)              currency_dised_taxed_price,
               abs(asi.currency_dised_taxed_total)              currency_dised_taxed_total,
               abs(c.order_preferential_allot_total)            order_preferential_allot_total,
               abs(asi.currency_order_preferential_allot_total) currency_order_preferential_allot_total,
               abs(asi.discount)                                discount,
               abs(c.tax_rate)                                  tax_rate,
               abs(asi.currency_ptype_preferential_total)      currency_ptype_preferential_total,
               abs(asi.currency_give_preferential_total)      currency_give_preferential_total,
               c.source_vchtype,
               c.source_vchcode,
               c.source_detail_id,
               pic.pic_url                                      picUrl,
               u.id                                        as   unitId,
               u.unit_name,
               c.memo
        FROM `acc_bill_detail_combo` c
                 LEFT JOIN acc_bill_detail_combo_assinfo asi
                           ON c.id = asi.combo_detail_id AND asi.profile_id = #{profileId}
                 LEFT JOIN base_ptype pt ON c.combo_id = pt.id AND pt.profile_id = #{profileId}
                 LEFT JOIN base_ptype_xcode bpx ON c.combo_id = bpx.ptype_id AND bpx.profile_id = #{profileId}
                 LEFT JOIN base_ptype_pic pic
                           on c.combo_id = pic.ptype_id and pic.profile_id = #{profileId} AND pic.rowindex = 1
                 LEFT JOIN base_ptype_unit u ON u.ptype_id = pt.id AND u.unit_code = 1 AND u.profile_id = #{profileId}
        WHERE c.profile_id = #{profileId}
          and c.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="getTdBillDetailComboList" resultType="com.wsgjp.ct.sale.biz.bill.model.dao.BillComboDAO">
        SELECT c.id,
               c.profile_id,
               c.vchcode,
               c.combo_id,
               c.inout_type,
               pt.usercode,
               pt.fullname                                    pfullname,
               pt.shortname,
               pt.barcode,
               pt.barcode                                     fullbarcode,
               pt.weight,
               pt.weight_unit                              as weightUnit,
               pt.sku_price                                as skuPrice,
               pt.pcategory,
               pt.standard,
               pt.ptype_type,
               abs(c.qty)                                     qty,
               abs(i.currency_price)                          currency_price,
               abs(i.currency_total)                          currency_total,
               c.gift,
               abs(i.currency_dised_price)                    currency_dised_price,
               abs(i.currency_dised_total)                    currency_dised_total
                ,
               abs(i.currency_tax_total)                      currency_tax_total,
               abs(i.currency_dised_taxed_price)              currency_dised_taxed_price,
               abs(i.currency_dised_taxed_total)              currency_dised_taxed_total,
               abs(i.currency_order_preferential_allot_total) currency_order_preferential_allot_total,
               abs(i.discount)                                discount,
               abs(c.tax_rate)                                tax_rate,
               abs(i.currency_ptype_preferential_total)       currency_ptype_preferential_total,
               abs(i.currency_give_preferential_total)      currency_give_preferential_total,
               c.source_vchtype,
               c.source_vchcode,
               c.source_detail_id,
               pic.pic_url                                    picUrl,
               u.id                                        as unitId,
               u.unit_name,
               c.memo
        FROM `td_bill_detail_combo` c
                 LEFT join td_bill_detail_combo_assinfo i on c.id = i.combo_detail_id and i.profile_id = #{profileId}
                 LEFT JOIN base_ptype pt ON c.combo_id = pt.id AND pt.profile_id = #{profileId}
                 LEFT JOIN base_ptype_unit u ON u.ptype_id = pt.id AND u.unit_code = 1 AND u.profile_id = #{profileId}
                 LEFT JOIN base_ptype_xcode bpx ON c.combo_id = bpx.ptype_id AND bpx.profile_id = #{profileId}
                 LEFT JOIN base_ptype_pic pic
                           on c.combo_id = pic.ptype_id and pic.profile_id = #{profileId} AND pic.rowindex = 1
        WHERE c.profile_id = #{profileId}
          and c.vchcode in
            <foreach item="item" index="index" collection="vchcodes" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
</mapper>
