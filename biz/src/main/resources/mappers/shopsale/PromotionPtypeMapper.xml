<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.PromotionPtypeMapper">
    <insert id="saveScorePromotionOtype">
        insert into ss_promotion_ptype
        (id,ptype_group,promotion_id,sku_id,preferential,profile_id,unit_id,pid,ptype_id)
        values
        <foreach collection="ptypeList" item="item" separator=",">
            ( #{item.id},
            <choose>
                <when test="item.combo != null and  item.combo == true">
                    2,
                </when>
                <otherwise>
                    #{ptypeGroup},
                </otherwise>
            </choose>
            #{id},
            <choose>
                <when test="item.skuId == null">
                    0,
                </when>
                <otherwise>
                    #{item.skuId},
                </otherwise>
            </choose>
            #{item.preferential},
            #{profileId},
            <choose>
                <when test="item.unitId == null">
                    0,
                </when>
                <otherwise>
                    #{item.unitId},
                </otherwise>
            </choose>
            #{item.pid},
            #{item.ptypeId}
            )
        </foreach>
    </insert>
    <insert id="saveScorePromotionCard">
        insert into ss_promotion_ptype
        (id,ptype_group,promotion_id,preferential,profile_id,pid)
        values
        <foreach collection="ptypeList" item="item" separator=",">
            ( #{item.id},
            #{ptypeGroup},
            #{id},
            #{item.preferential},
            #{profileId},
            #{item.pid})
        </foreach>
    </insert>
    <insert id="saveScorePromotionAmount">
        insert into ss_promotion_ptype
        (id,ptype_group,promotion_id,preferential,profile_id,price)
        values
        <foreach collection="ptypeList" item="item" separator=",">
            ( #{item.id},
            #{ptypeGroup},
            #{id},
            #{item.preferential},
            #{profileId},
            #{item.price})
        </foreach>
    </insert>

    <update id="deletedByPtypeId">
        update
        ss_promotion_ptype
        set deleted =1
        where
        profile_id=#{profileId}
        and
        pid
        in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and
        promotion_id
        in
        <foreach collection="promotionIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="deletedByCardId">
        update
        ss_promotion_ptype
        set deleted =1
        where
        profile_id=#{profileId}
        and
        pid
        in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and
        promotion_id
        in
        <foreach collection="promotionIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectRightsCard" resultType="java.util.Map">
        select sct.fullname as cardName,sp.fullname as promotionName
        from ss_promotion_ptype spp
        left join ss_card_template sct on sct.id = spp.pid
        left join ss_promotion sp on sp.id = spp.promotion_id
        where spp.profile_id = #{profileId}
        and spp.pid in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item.id}
        </foreach>
        and spp.deleted = 0
        and sct.deleted = 0
        and sp.deleted = 0
        and sp.stoped = 0
        group by pid
    </select>

    <select id="selectPtypeFromImport"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionImportPtype">
        select bp.fullname as fullname,bpx.xcode,IFNULL(bpf.fullbarcode,bp.barcode) as fullbarcode, bp.id as
        ptypeId,bpu.unit_name as
        unitName,bp.usercode as usercode,bp.batchenabled,bps.id as skuId,bpu.id as unitId,if( bp.pcategory
        =2,true,false) as ptypeCombo
        <if test="importPtype.propValues != null  || importPtype.fullbarcode != null ||  importPtype.xcode != null">
            ,bps.propvalue_names as propValues,bps.id as
            skuId,IF(bp.sku_price=0,IFNULL(upp.retail_price,0),IFNULL(ups.retail_price,0)) AS
            price
        </if>
        <if test="importPtype.propValues == null">
            ,IF(bp.sku_price=0,IFNULL(upp.retail_price,0),0) AS
            price
        </if>
        from base_ptype bp
        left join base_ptype_sku bps on bps.profile_id = #{profileId} and bps.ptype_id = bp.id
        left join base_ptype_unit bpu on bpu.profile_id = #{profileId} and bpu.ptype_id = bp.id
        left join base_ptype_price upp on upp.profile_id = #{profileId} and upp.ptype_id = bp.id and upp.unit_id =
        bpu.id and upp.sku_id = 0
        left join base_ptype_price ups on ups.profile_id = #{profileId} and ups.ptype_id = bp.id and ups.unit_id =
        bpu.id and ups.sku_id = bps.id
        left join base_ptype_xcode bpx on bpx.profile_id = #{profileId} and bpx.ptype_id = bp.id and bpx.unit_id =
        bpu.id and bpx.sku_id = bps.id and bpx.defaulted = 1
        left join base_ptype_fullbarcode bpf on bpf.profile_id = #{profileId} and bpf.ptype_id = bp.id and bpf.unit_id =
        bpu.id and bpf.sku_id = bps.id and bpf.defaulted = 1
        where bp.profile_id = #{profileId} and bp.deleted = 0 and bp.stoped != 1 and bps.stoped != 1 and bps.id IS NOT
        NULL

        <if test="importPtype.fullname != null">
            and bp.fullname = #{importPtype.fullname}
        </if>

        <if test="importPtype.propValues != null">
            and bps.propvalue_names = #{importPtype.propValues}
        </if>
        <if test="importPtype.unitName != null">
            and bpu.unit_name = #{importPtype.unitName}
        </if>
        <if test="importPtype.usercode != null">
            and bp.usercode = #{importPtype.usercode}
        </if>
        <if test="importPtype.fullbarcode != null">
            and (bpf.fullbarcode = #{importPtype.fullbarcode} or bp.barcode = #{importPtype.fullbarcode})
        </if>
        <if test="importPtype.xcode != null">
            and bpx.xcode = #{importPtype.xcode}
        </if>
        group by bp.id,bpu.id
    </select>
</mapper>