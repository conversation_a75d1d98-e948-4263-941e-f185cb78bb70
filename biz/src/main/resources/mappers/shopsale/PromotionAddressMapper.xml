<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.PromotionAddressMapper">
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, profile_id, promotion_id, province, city, district, total_condition, deleted, create_time, update_time
    </sql>
    
    <!-- 批量插入包邮促销地址 -->
    <insert id="insertPromotionAddresses" parameterType="java.util.List">
        INSERT INTO ss_promotion_address (
            id, profile_id, promotion_id, province, city, district, total_condition, deleted
        ) VALUES 
        <foreach collection="addressList" item="item" separator=",">
            (
                #{item.id}, #{item.profileId}, #{item.promotionId}, #{item.province}, 
                #{item.city}, #{item.district}, #{item.totalCondition}, 0
            )
        </foreach>
    </insert>
    
    <!-- 根据促销ID删除包邮促销地址 -->
    <delete id="deletePromotionAddressesByPromotionId">
        UPDATE ss_promotion_address 
        SET deleted = 1
        WHERE profile_id = #{profileId} AND promotion_id = #{promotionId}
    </delete>
    
    <!-- 根据促销ID查询包邮促销地址 -->
    <select id="selectPromotionAddressesByPromotionId" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionAddress">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            ss_promotion_address
        WHERE 
            profile_id = #{profileId} AND promotion_id = #{promotionId} AND deleted = 0
    </select>
    
    <!-- 根据促销ID列表查询包邮促销地址 -->
    <select id="selectPromotionAddressesByPromotionIds" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionAddress">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            ss_promotion_address
        WHERE 
            profile_id = #{profileId} AND deleted = 0
        <if test="promotionIds != null and promotionIds.size() > 0">
            AND promotion_id IN
            <foreach collection="promotionIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    
</mapper> 